package errorkit

/**
 * Code is internal global error code using for all mono repo services of installment
 * These error codes also include the domain or the common error type,
 * so maybe we not need to define more domain field or error type field in a struct ErrorInfo
 * Example CALL_UM_FAILED, CALL_CIMB_FAILED, CALL_ACCOUNT_FAILED, CALL_ONBOARDING_FAILED is defining the main error code
 * when we call external service and has an error, we can propagate this error code to the client by setting in PartnerCode
 */
type Code string

const (
	// Common
	CodeUnknown                     Code = "UNKNOWN"
	CodeApplication                 Code = "APPLICATION"
	CodeBadRequest                  Code = "BAD_REQUEST"
	CodeInvalidArgument             Code = "INVALID_ARGUMENT"
	CodeConversionError             Code = "CONVERSION_ERROR"
	CodeRepositoryError             Code = "REPOSITORY_ERROR"
	CodeConditionFailed             Code = "CONDITION_FAILED"
	CodeInternalError               Code = "INTERNAL_ERROR"
	CodeCallUMFailed                Code = "CALL_UM_FAILED"
	CodeDataNotFound                Code = "DATA_NOT_FOUND"
	CodeCallCIMBFailed              Code = "CALL_CIMB_FAILED"
	CodeCallRiskFailed              Code = "CALL_RISK__FAILED"
	CodeCallPartnerFailed           Code = "CALL_PARTNER_FAILED"
	CodeCallAccountFailed           Code = "CALL_ACCOUNT_FAILED"
	CodeCallWhitelistFailed         Code = "CALL_WHITELIST_FAILED"
	CodeCallOnboardingFailed        Code = "CALL_ONBOARDING_FAILED"
	CodeResourceBusy                Code = "RESOURCE_BUSY"
	CodeResourceUnderMaintenance    Code = "RESOURCE_UNDER_MAINTENANCE"
	CodeResourceLockedForProcessing Code = "RESOURCE_LOCKED_FOR_PROCESSING"
	CodeRateLimitExceeded           Code = "RATE_LIMIT_EXCEEDED"

	// Internal Onboarding
	CodeUserNotFound                    Code = "USER_NOT_FOUND"
	CodeUserFraud                       Code = "USER_HAS_FRAUD"
	CodeProfileProblems                 Code = "USER_PROFILE_PROBLEMS"
	CodeUserNonWhitelist                Code = "USER_NON_WHITELIST"
	CodePartnerNotSupported             Code = "PARTNER_NOT_SUPPORTED"
	CodeGetKycProfileFailed             Code = "GET_KYC_PROFILE_FAILED"
	CodeImageDataInvalid                Code = "IMAGE_DATA_INVALID"
	CodeImageDataCorrupted              Code = "IMAGE_DATA_CORRUPTED"
	CodeFaceChallengeInvalid            Code = "FACE_CHALLENGE_INVALID"
	CodeImageFormatNotSupported         Code = "IMAGE_FORMAT_NOT_SUPPORTED"
	CodeContractHasBeenSigned           Code = "CONTRACT_HAS_BEEN_SIGNED"
	CodeContractTransactionExpired      Code = "CONTRACT_TRANSACTION_EXPIRED"
	CodeOnboardingNotFound              Code = "ONBOARDING_NOT_FOUND"
	CodeOnboardingCompleted             Code = "ONBOARDING_COMPLETED"
	CodeOnboardingListInvalid           Code = "ONBOARDING_LIST_INVALID"
	CodeOnboardingNotRejectedYet        Code = "ONBOARDING_NOT_REJECTED_YET"
	CodeOnboardingNotAllowToResubmit    Code = "ONBOARDING_NOT_ALLOW_TO_RESUBMIT"
	CodeOnboardingStepInvalidForExec    Code = "ONBOARDING_STEP_INVALID_FOR_EXECUTION"
	CodeOnboardingNotEligibleToRegister Code = "ONBOARDING_INFO_NOT_ELIGIBLE_TO_REGISTER"

	// Internal Account
	CodeAccountNotFound    Code = "ACCOUNT_NOT_FOUND"
	CodeAccountNotActive   Code = "ACCOUNT_NOT_ACTIVE"
	CodeAccountDataInvalid Code = "ACCOUNT_DATA_INVALID"

	// Internal Installment
	CodeEmptyPlanKey         Code = "EMPTY_PLAN_KEY"
	CodeInvalidPlanKey       Code = "INVALID_PLAN_KEY"
	CodePlanOptsNotFound     Code = "PLAN_OPTS_NOT_FOUND"
	CodePlanDataIsEmpty      Code = "PLAN_DATA_EMPTY"
	CodePlanDataInvalid      Code = "PLAN_DATA_INVALID"
	CodePlanDataNotFound     Code = "PLAN_DATA_NOT_FOUND"
	CodeOrderInfoMismatch    Code = "ORDER_INFO_MISMATCH"
	CodePlanSelectedNotMatch Code = "PLAN_SELECTED_NOT_MATCH"
	CodeInstallmentNotFound  Code = "INSTALLMENT_NOT_FOUND"
	CodeInstallmentDataEmpty Code = "INSTALLMENT_DATA_EMPTY"

	// Internal Statement
	CodeStatementEmptyData Code = "STATEMENT_EMPTY_DATA"
	CodeStatementNotFound  Code = "STATEMENT_NOT_FOUND"
)

func (c Code) String() string {
	return string(c)
}
