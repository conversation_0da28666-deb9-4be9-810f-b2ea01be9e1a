package errorkit

type Kind string

const (
	TypeUnexpected   Kind = "unexpected"
	TypeBadRequest   Kind = "bad_request"
	TypeInvalidArg   Kind = "invalid_argument"
	TypeValidation   Kind = "validation"
	TypeConversion   Kind = "conversion"
	TypeUnauthorized Kind = "unauthorized"
	TypeForbidden    Kind = "forbidden"
	TypeNotFound     Kind = "not_found"
	TypePrecondition Kind = "precondition"
	TypeRepository   Kind = "repository"
	TypeRemoteCall   Kind = "remote_call"
	TypeConflict     Kind = "conflict"
	TypeRateLimit    Kind = "rate_limit"
	TypeTimeout      Kind = "timeout"
)

type Error struct {
	Err  error          `json:"-"` // Don't serialize the cause
	Type Kind           `json:"type"`
	Code string         `json:"code"`
	Msg  string         `json:"msg"`
	Meta map[string]any `json:"meta,omitempty"`
}

type RepositoryError struct {
	Error
	Entity string `json:"entity"`
}

type PartnerCError struct {
	Error
	Partner string `json:"partner"`
	SysCode string `json:"sys_code"`
}

type ExternalError struct {
	Error
	SubCode string `json:"sub_code"`
}
