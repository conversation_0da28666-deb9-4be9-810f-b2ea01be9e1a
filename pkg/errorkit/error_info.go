package errorkit

import (
	"errors"
	"fmt"

	"github.com/spf13/cast"
)

// ErrorInfo Common struct error
type ErrorInfo struct {
	// Error is a root cause of the error, can be nil
	Cause error
	// Kind type of error, using to mapping error status in grpc/http transport
	Kind Kind `json:"kind"`
	// Code is a code of error, it presents the business judgment error code
	// If not define it will be mapped by Kind
	Code     Code           `json:"code"`
	Message  Message        `json:"message,omitempty"`
	Metadata map[string]any `json:"metadata,omitempty"`
}

func New(kind Kind, code Code, message Message) *ErrorInfo {
	return &ErrorInfo{
		Kind:    kind,
		Code:    code,
		Message: message,
	}
}

func NewError(code Code, message Message) *ErrorInfo {
	return &ErrorInfo{
		Code:    code,
		Message: message,
	}
}

func NewWithCode(code Code) *ErrorInfo {
	return &ErrorInfo{
		Code: code,
	}
}

func NewWithCause(cause error, code Code, message Message) *ErrorInfo {
	return &ErrorInfo{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

func (e *ErrorInfo) Is(err error) bool {
	var target *ErrorInfo
	if errors.As(err, &target) {
		return e.Code == target.Code
	}
	return false
}

func (e *ErrorInfo) As(err any) bool {
	t, ok := err.(*ErrorInfo)
	if !ok {
		return false
	}
	*t = *e
	return true
}

func (e *ErrorInfo) Unwrap() error {
	return e.Cause
}

func (e *ErrorInfo) Error() string {
	return fmt.Sprintf("code: %s, message: %s", e.Code, e.Message)
}

func (e *ErrorInfo) ErrorMessage() string {
	return e.Message.String()
}

func (e *ErrorInfo) WithCause(err error) *ErrorInfo {
	e.Cause = err
	return e
}

func (e *ErrorInfo) WithKind(kind Kind) *ErrorInfo {
	e.Kind = kind
	return e
}

func (e *ErrorInfo) UnwrapAll() []*ErrorInfo {
	result := make([]*ErrorInfo, 0)
	cause := e.Cause

	for cause != nil {
		var err *ErrorInfo
		if !errors.As(cause, &err) {
			break
		}
		cause = err.Cause
		result = append(result, err)
	}

	return result
}

// WithMetadata will overwrite the existing metadata
func (e *ErrorInfo) WithMetadata(metadata map[string]any) *ErrorInfo {
	e.Metadata = metadata
	return e
}

func (e *ErrorInfo) WithMetadataField(key MetadataField, value any) *ErrorInfo {
	if e.Metadata == nil {
		e.Metadata = make(map[string]any)
	}
	if value == "" {
		return e
	}

	e.Metadata[key.String()] = value
	return e
}

func (e *ErrorInfo) GetCode() Code {
	if e == nil {
		return ""
	}
	return e.Code
}

func (e *ErrorInfo) GetMetadataField(key MetadataField) any {
	return e.Metadata[key.String()]
}

func (e *ErrorInfo) ResetMetadata() *ErrorInfo {
	e.Metadata = make(map[string]any)
	return e
}

func (e *ErrorInfo) WithMessage(message Message) *ErrorInfo {
	e.Message = message
	return e
}

func (e *ErrorInfo) MetadataToMapString() map[string]string {
	result := make(map[string]string)
	for k, v := range e.Metadata {
		result[k] = cast.ToString(v)
	}
	return result
}

func IsErrorCode(err error, code Code) bool {
	var e *ErrorInfo
	if errors.As(err, &e) {
		return e.Code == code
	}
	return false
}

/********** Message **********/

type Message string

func (m Message) String() string {
	return string(m)
}

/********** Domain **********/

type Domain string

func (d Domain) String() string {
	return string(d)
}

/********** Metadata Field **********/

type MetadataField string

func (f MetadataField) String() string {
	return string(f)
}

func GetErrorKind(err error) Kind {
	var e *ErrorInfo
	if errors.As(err, &e) {
		return e.Kind
	}
	return ""
}
