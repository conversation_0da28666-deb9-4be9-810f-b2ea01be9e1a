package ratelimit

import (
	"context"
	"errors"
	"slices"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
)

type SlidingWindowConfig struct {
	RedisCli    redis.UniversalClient
	StoreType   StoreType
	WindowSize  time.Duration
	MaxRequests int
}

func (s SlidingWindowConfig) Validate() error {
	if s.WindowSize == 0 {
		return errors.New("window size is required")
	}
	if s.MaxRequests == 0 {
		return errors.New("max requests is required")
	}

	storeTypeSupport := []StoreType{RedisStore, MemoryStore}
	if !slices.Contains(storeTypeSupport, s.StoreType) {
		return errors.New("store type is not supported")
	}
	if s.StoreType == RedisStore && s.RedisCli == nil {
		return errors.New("redis client is required for redis store")
	}
	return nil
}

type SlidingWindowRedis struct {
	client      redis.UniversalClient
	windowSize  time.Duration
	maxRequests int
}

type SlidingWindowMemory struct {
	mutex       sync.RWMutex
	windowSize  time.Duration
	maxRequests int
	timestamps  map[string][]time.Time
}

func NewSlidingWindow(config *SlidingWindowConfig) (Limiter, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	switch config.StoreType {
	case RedisStore:
		return newRedisSlidingWindow(config), nil
	case MemoryStore:
		fallthrough
	default:
		return newMemorySlidingWindow(config), nil
	}
}

func newRedisSlidingWindow(config *SlidingWindowConfig) Limiter {
	return &SlidingWindowRedis{
		client:      config.RedisCli,
		windowSize:  config.WindowSize,
		maxRequests: config.MaxRequests,
	}
}

func (s *SlidingWindowRedis) Allow(ctx context.Context, key string) (bool, error) {
	timeNow := time.Now()
	timeStart := timeNow.Add(-s.windowSize)

	handlePipe := func(pipe redis.Pipeliner) error {
		pipe.ZAdd(ctx, key, &redis.Z{
			Score:  float64(timeNow.UnixMilli()),
			Member: timeNow.UnixMilli(),
		})
		pipe.ZRemRangeByScore(ctx, key, "0", cast.ToString(timeStart.UnixMilli()-1))
		pipe.Expire(ctx, key, s.windowSize*2)
		return nil
	}

	handleTx := func(tx *redis.Tx) error {
		// Get all timestamps
		timeNowStr := cast.ToString(timeNow.UnixMilli())
		timeStartStr := cast.ToString(timeStart.UnixMilli())
		count, err := tx.ZCount(ctx, key, timeStartStr, timeNowStr).Result()
		if err != nil {
			return err
		}

		if count >= int64(s.maxRequests) {
			return ErrRateLimitExceeded
		}

		if _, err = tx.TxPipelined(ctx, handlePipe); err != nil {
			return err
		}
		return nil
	}

	err := s.client.Watch(ctx, handleTx, key)
	if errors.Is(err, redis.TxFailedErr) {
		return false, err
	}
	if errors.Is(err, ErrRateLimitExceeded) {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	return true, nil
}

func newMemorySlidingWindow(config *SlidingWindowConfig) Limiter {
	return &SlidingWindowMemory{
		mutex:       sync.RWMutex{},
		windowSize:  config.WindowSize,
		maxRequests: config.MaxRequests,
		timestamps:  make(map[string][]time.Time),
	}
}

func (s *SlidingWindowMemory) Allow(ctx context.Context, key string) (bool, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	timeNow := time.Now()
	windowStart := timeNow.Add(-s.windowSize)

	// Initialize if key not present
	if _, exists := s.timestamps[key]; !exists {
		s.timestamps[key] = []time.Time{}
	}

	// Filter out timestamps outside the window
	curTimestamps := s.timestamps[key]
	newTimestamps := make([]time.Time, 0)
	for _, timestamp := range curTimestamps {
		if timestamp.After(windowStart) {
			newTimestamps = append(newTimestamps, timestamp)
		}
	}

	// Update the timestamps slice
	s.timestamps[key] = newTimestamps

	// Check if the number of requests exceeds the limit
	if len(newTimestamps)+1 > s.maxRequests {
		return false, nil
	}

	// Allow request
	s.timestamps[key] = append(s.timestamps[key], timeNow)
	return true, nil
}
