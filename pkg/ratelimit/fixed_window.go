package ratelimit

import (
	"context"
	"fmt"
	"slices"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
)

type fixedWindowData struct {
	Time  time.Time `json:"time" redis:"time"`
	Count int       `json:"count" redis:"count"`
}

type FixedWindowConfig struct {
	RedisCli    redis.UniversalClient
	StoreType   StoreType
	WindowSize  time.Duration
	MaxRequests int
}

func (s FixedWindowConfig) Validate() error {
	if s.WindowSize == 0 {
		return errors.New("window size is required")
	}
	if s.MaxRequests == 0 {
		return errors.New("max requests is required")
	}

	storeTypeSupport := []StoreType{RedisStore, MemoryStore}
	if !slices.Contains(storeTypeSupport, s.StoreType) {
		return errors.New("store type is not supported")
	}
	if s.StoreType == RedisStore && s.RedisCli == nil {
		return errors.New("redis client is required for redis store")
	}
	return nil
}

func NewFixedWindow(config *FixedWindowConfig) (Limiter, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	switch config.StoreType {
	case RedisStore:
		return newRedisFixedWindow(config), nil
	case MemoryStore:
		fallthrough
	default:
		return newMemoryFixedWindow(config), nil
	}
}

func newRedisFixedWindow(config *FixedWindowConfig) Limiter {
	return &FixedWindowRedis{
		client:      config.RedisCli,
		windowSize:  config.WindowSize,
		maxRequests: config.MaxRequests,
	}
}

type FixedWindowRedis struct {
	client      redis.UniversalClient
	windowSize  time.Duration
	maxRequests int
}

func (f FixedWindowRedis) Allow(ctx context.Context, key string) (bool, error) {
	timeNow := time.Now()
	timeWindow := timeNow.Truncate(f.windowSize)
	windowKey := fmt.Sprintf("%s:%d", key, timeWindow.Unix())

	count, err := f.client.Incr(ctx, windowKey).Result()
	if err != nil {
		return false, err
	}

	if count != 1 {
		return count <= int64(f.maxRequests), nil
	}

	// Set the expiration to the window size
	_, err = f.client.Expire(ctx, windowKey, f.windowSize).Result()
	if err != nil {
		return false, err
	}
	return true, nil
}

type FixedWindowMemory struct {
	mutex       sync.RWMutex
	windowSize  time.Duration
	maxRequests int
	windows     map[string]*fixedWindowData
}

func newMemoryFixedWindow(config *FixedWindowConfig) Limiter {
	return &FixedWindowMemory{
		windowSize:  config.WindowSize,
		maxRequests: config.MaxRequests,
		windows:     make(map[string]*fixedWindowData),
	}
}

func (f *FixedWindowMemory) Allow(_ context.Context, key string) (bool, error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	timeNow := time.Now()
	timeWindow := timeNow.Truncate(f.windowSize)

	data, exists := f.windows[key]
	if !exists || data.Time != timeWindow {
		// Start a new window
		f.windows[key] = &fixedWindowData{
			Time:  timeWindow,
			Count: 1,
		}
		return true, nil
	}

	if data.Count < f.maxRequests {
		data.Count++
		return true, nil
	}
	return false, nil
}
