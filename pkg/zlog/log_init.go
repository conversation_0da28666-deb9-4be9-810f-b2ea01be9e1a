package zlog

import (
	"os"
	"time"

	kzrLog "github.com/go-kratos/kratos/contrib/log/zerolog/v2"
	kraLog "github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	zrLog "github.com/rs/zerolog"
	zrLogErr "github.com/rs/zerolog/pkgerrors"
)

type LoggerInfo struct {
	SvcID      string
	SvcVer     string
	SvcName    string
	Level      string
	Encoding   string
	StackTrace bool
}

// MustNewLogger creates a new logger with zerolog.
func MustNewLogger(loggerConf *LoggerInfo) kraLog.Logger {
	zrLog.TimeFieldFormat = time.RFC3339
	zrLog.ErrorStackMarshaler = zrLogErr.MarshalStack

	logLevel, err := zrLog.ParseLevel(loggerConf.Level)
	if err != nil {
		logLevel = zrLog.InfoLevel
	}

	zrLogger := zrLog.New(os.Stdout).With().Timestamp().Logger().Level(logLevel)
	appLogger := kraLog.With(
		kzrLog.NewLogger(&zrLogger),
		"service.id", loggerConf.SvcID,
		"service.name", loggerConf.SvcName,
		"caller", kraLog.DefaultCaller,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)

	kraLog.SetLogger(appLogger)

	return appLogger
}
