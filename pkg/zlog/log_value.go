package zlog

type LogField struct {
	Key   string
	Value interface{}
}

func LogValue(key string, value interface{}) LogField {
	return LogField{
		Key:   key,
		Value: value,
	}
}
func LogMsg(value string) LogField {
	return LogField{
		Key:   "msg",
		Value: value,
	}
}
func LogError(value error) LogField {
	return LogField{
		Key:   "error",
		Value: value,
	}
}

func Log<PERSON>ombine(list ...LogField) (keyvals []interface{}) {
	maps := make(map[string]interface{})
	for _, logField := range list {
		maps[logField.Key] = logField.Value
	}
	for k, v := range maps {
		keyvals = append(keyvals, k, v)
	}

	return keyvals
}
