package metrics

import (
	"time"

	"gitlab.zalopay.vn/fin/platform/common/metrics"
)

type ClientNoop struct {
}

var _ metrics.Client = (*ClientNoop)(nil)

func (m ClientNoop) Gauge(metricName string, value float64, tags ...metrics.Tag) {}

func (m ClientNoop) GaugeInc(metricName string, tags ...metrics.Tag) {}

func (m ClientNoop) GaugeDec(metricName string, tags ...metrics.Tag) {}

func (m ClientNoop) CountInc(metricName string, tags ...metrics.Tag) {}

func (m ClientNoop) Count(metricName string, amount int64, tags ...metrics.Tag) {}

func (m ClientNoop) Histogram(metricName string, value float64, tags ...metrics.Tag) {}

func (m ClientNoop) Duration(metricName string, start time.Time, tags ...metrics.Tag) {}
