package metrics

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/prometheus/client_golang/prometheus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/metric"
)

const (
	MetricAccountMeter    = "fin.installment.account"
	MetricOnboardingMeter = "fin.installment.onboarding"
	MetricManagementMeter = "fin.installment.management"
)

const (
	MetricModuleHttpServer = "http_server"
	MetricModuleGrpcServer = "grpc_server"
	MetricModuleGrpcClient = "grpc_client"
	MetricModuleRepository = "repository"

	MetricNameDurationSecond   = "duration_seconds"
	MetricNameRequestCounter   = "requests_code_total"
	MetricNameRequestHistogram = "requests"
)

type Options struct {
	Name      string
	Module    string
	Namespace string
}

type RequestMetrics struct {
	HandledCounter   *prometheus.CounterVec
	HandledHistogram *prometheus.HistogramVec
}

type OtelRequestMetrics struct {
	HandledCounter   metric.Int64Counter
	HandledHistogram metric.Float64Histogram
}

type RepositoryMetrics struct {
	HandledHistogram *prometheus.HistogramVec
}

func NewOtelRequestMetrics(opts Options) *OtelRequestMetrics {
	meter := otel.GetMeterProvider().Meter(opts.Namespace)
	counterName := getMetricName(opts.Module, MetricNameRequestCounter)
	counterMetric, _ := meter.Int64Counter(
		counterName,
		metric.WithUnit("{call}"),
		metric.WithDescription("The total number of processed requests"),
	)
	histogramName := getMetricName(opts.Module, MetricNameRequestHistogram)
	histogramMetric, _ := meter.Float64Histogram(
		histogramName,
		metric.WithUnit("s"),
		metric.WithDescription("requests duration(sec)."),
		metric.WithExplicitBucketBoundaries(prometheus.DefBuckets...),
	)
	return &OtelRequestMetrics{
		HandledCounter:   counterMetric,
		HandledHistogram: histogramMetric,
	}
}

func NewRequestMetrics(opts Options) *RequestMetrics {
	result := &RequestMetrics{
		HandledCounter: prometheus.NewCounterVec(prometheus.CounterOpts{
			Namespace: opts.Namespace,
			Subsystem: opts.Module,
			Name:      "requests_total",
			Help:      "The total number of processed requests",
		}, []string{"kind", "operation", "code", "reason"}),
		HandledHistogram: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Namespace: opts.Namespace,
			Subsystem: opts.Module,
			Name:      "duration_seconds",
			Help:      "requests duration(sec).",
			Buckets:   prometheus.DefBuckets,
		}, []string{"kind", "operation"}),
	}

	if err := prometheus.Register(result.HandledCounter); err != nil {
		log.Warnf("register metrics failed: %v", err)
	}
	if err := prometheus.Register(result.HandledHistogram); err != nil {
		log.Warnf("register metrics failed: %v", err)
	}
	return result
}

func NewRepositoryMetrics(opts Options) *RepositoryMetrics {
	result := &RepositoryMetrics{
		HandledHistogram: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Namespace: opts.Namespace,
			Subsystem: opts.Module,
			Name:      MetricNameDurationSecond,
			Help:      "requests duration(sec).",
			Buckets:   prometheus.DefBuckets,
		}, []string{"repo", "operation"}),
	}

	if err := prometheus.Register(result.HandledHistogram); err != nil {
		log.Warnf("register metrics failed: %v", err)
	}
	return result
}

func (m *RepositoryMetrics) MonitoredTime(repo string) func(operation string) {
	start := time.Now()
	return func(operation string) {
		if m.HandledHistogram == nil {
			return
		}
		duration := time.Since(start).Seconds()
		m.HandledHistogram.WithLabelValues(repo, operation).Observe(duration)
	}
}

func getMetricName(module string, name string) string {
	return fmt.Sprintf("%s_%s", module, name)
}
