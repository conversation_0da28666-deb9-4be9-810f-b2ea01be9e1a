package partner

import (
	"slices"
	"strings"
)

type PartnerCode string

const (
	PartnerCIMB PartnerCode = "CIMB"
)

func (s PartnerCode) String() string {
	return string(s)
}

func (s PartnerCode) Lowercase() string {
	return strings.ToLower(s.String())
}

func (s PartnerCode) IsValid() bool {
	arr := []PartnerCode{PartnerCIMB}
	return slices.Contains(arr, s)
}

func CodeFromString(str string) PartnerCode {
	mapping := map[string]PartnerCode{
		PartnerCIMB.Lowercase(): PartnerCIMB,
	}
	value, ok := mapping[strings.ToLower(str)]
	if !ok {
		return PartnerCode("")
	}
	return value
}

func ListFromStrings(data []string) []PartnerCode {
	var result []PartnerCode
	for _, v := range data {
		result = append(result, CodeFromString(v))
	}
	return result
}

func ListToStrings(data []PartnerCode) []string {
	var result []string
	for _, v := range data {
		result = append(result, v.String())
	}
	return result
}
