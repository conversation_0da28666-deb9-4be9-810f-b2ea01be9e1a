package mocking

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/platform/common/redis"
)

var ProviderSet = wire.NewSet(NewMocking)

const (
	stmtDateKey        = "fin:installment:mocking:statement_date"
	stmtDateOverdueKey = "fin:installment:mocking:statement_date_overdue"
)

type Mocking struct {
	logger     *log.Helper
	redisCache redis.CacheNoCaller
}

func NewMocking(cache redis.CacheNoCaller, kLogger log.Logger) *Mocking {
	return &Mocking{
		logger:     log.NewHelper(log.With(kLogger, "module", "mocking")),
		redisCache: cache,
	}
}

func (m *Mocking) SetStatementDate(ctx context.Context, date time.Time) error {
	return m.redisCache.Set(ctx, stmtDateKey, date.Format("2006-01-02"), time.Minute*30)
}

func (m *Mocking) GetStatementDate() (time.Time, error) {
	var stmtDate string
	if err := m.redisCache.Get(context.Background(), stmtDateKey, &stmtDate); err != nil {
		return time.Time{}, err
	}
	return time.Parse("2006-01-02", stmtDate)
}

func (m *Mocking) SetStatementDateOverdue(ctx context.Context, date time.Time) error {
	return m.redisCache.Set(ctx, stmtDateOverdueKey, date.Format("2006-01-02"), time.Minute*30)
}

func (m *Mocking) GetStatementDateOverdue() (time.Time, error) {
	var stmtDate string
	if err := m.redisCache.Get(context.Background(), stmtDateOverdueKey, &stmtDate); err != nil {
		return time.Time{}, err
	}
	return time.Parse("2006-01-02", stmtDate)
}
