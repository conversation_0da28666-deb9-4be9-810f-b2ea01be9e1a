package maintenance

type State struct {
	All        bool `json:"all" redis:"all"`
	Onboarding bool `json:"onboarding" redis:"onboarding"`
	Purchase   bool `json:"purchase" redis:"purchase"`
	Repayment  bool `json:"repayment" redis:"repayment"`
}

type Feat string

const (
	FeatAll        Feat = "all"
	FeatOnboarding Feat = "onboarding"
	FeatPurchase   Feat = "purchase"
	FeatRepayment  Feat = "repayment"
)

func (f Feat) String() string {
	return string(f)
}

type Element struct {
	Operation string
	Features  []Feat
}
