// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance (interfaces: Handler)
//
// Generated by this command:
//
//	mockgen --destination=./mocks/mock.go --package=maintenance_mocks . Handler
//

// Package maintenance_mocks is a generated GoMock package.
package maintenance_mocks

import (
	context "context"
	reflect "reflect"

	maintenance "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockHandler is a mock of Handler interface.
type MockHandler struct {
	ctrl     *gomock.Controller
	recorder *MockHandlerMockRecorder
	isgomock struct{}
}

// MockHandlerMockRecorder is the mock recorder for MockHandler.
type MockHandlerMockRecorder struct {
	mock *MockHandler
}

// NewMockHandler creates a new mock instance.
func NewMockHandler(ctrl *gomock.Controller) *MockHandler {
	mock := &MockHandler{ctrl: ctrl}
	mock.recorder = &MockHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHandler) EXPECT() *MockHandlerMockRecorder {
	return m.recorder
}

// CheckFeaturesMaintenance mocks base method.
func (m *MockHandler) CheckFeaturesMaintenance(ctx context.Context, partner partner.PartnerCode, feats []maintenance.Feat) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFeaturesMaintenance", ctx, partner, feats)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFeaturesMaintenance indicates an expected call of CheckFeaturesMaintenance.
func (mr *MockHandlerMockRecorder) CheckFeaturesMaintenance(ctx, partner, feats any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFeaturesMaintenance", reflect.TypeOf((*MockHandler)(nil).CheckFeaturesMaintenance), ctx, partner, feats)
}

// GetMaintenanceState mocks base method.
func (m *MockHandler) GetMaintenanceState(ctx context.Context, partner partner.PartnerCode) (maintenance.State, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaintenanceState", ctx, partner)
	ret0, _ := ret[0].(maintenance.State)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaintenanceState indicates an expected call of GetMaintenanceState.
func (mr *MockHandlerMockRecorder) GetMaintenanceState(ctx, partner any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaintenanceState", reflect.TypeOf((*MockHandler)(nil).GetMaintenanceState), ctx, partner)
}

// SetMaintenanceState mocks base method.
func (m *MockHandler) SetMaintenanceState(ctx context.Context, partner partner.PartnerCode, mState maintenance.State) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMaintenanceState", ctx, partner, mState)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMaintenanceState indicates an expected call of SetMaintenanceState.
func (mr *MockHandlerMockRecorder) SetMaintenanceState(ctx, partner, mState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMaintenanceState", reflect.TypeOf((*MockHandler)(nil).SetMaintenanceState), ctx, partner, mState)
}
