package codec

import (
	"encoding/json"
	"testing"
)

func TestNestedArrayRedaction(t *testing.T) {
	tests := []struct {
		name           string
		input          interface{}
		redactElements []string
		expected       map[string]interface{}
	}{
		{
			name: "Simple field redaction",
			input: map[string]interface{}{
				"name":     "<PERSON>",
				"password": "secret123",
				"email":    "<EMAIL>",
			},
			redactElements: []string{"password"},
			expected: map[string]interface{}{
				"name":  "<PERSON>",
				"email": "<EMAIL>",
			},
		},
		{
			name: "Single level array redaction",
			input: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name":     "<PERSON>",
						"password": "secret123",
					},
					map[string]interface{}{
						"name":     "<PERSON>",
						"password": "password456",
					},
				},
			},
			redactElements: []string{"users.*.password"},
			expected: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name": "<PERSON>",
					},
					map[string]interface{}{
						"name": "<PERSON>",
					},
				},
			},
		},
		{
			name: "Nested array redaction",
			input: map[string]interface{}{
				"departments": []interface{}{
					map[string]interface{}{
						"name": "Engineering",
						"teams": []interface{}{
							map[string]interface{}{
								"name": "Frontend",
								"members": []interface{}{
									map[string]interface{}{
										"name":     "John",
										"password": "secret123",
										"email":    "<EMAIL>",
									},
									map[string]interface{}{
										"name":     "Jane",
										"password": "password456",
										"email":    "<EMAIL>",
									},
								},
							},
							map[string]interface{}{
								"name": "Backend",
								"members": []interface{}{
									map[string]interface{}{
										"name":     "Bob",
										"password": "bobpass",
										"email":    "<EMAIL>",
									},
								},
							},
						},
					},
				},
			},
			redactElements: []string{"departments.*.teams.*.members.*.password"},
			expected: map[string]interface{}{
				"departments": []interface{}{
					map[string]interface{}{
						"name": "Engineering",
						"teams": []interface{}{
							map[string]interface{}{
								"name": "Frontend",
								"members": []interface{}{
									map[string]interface{}{
										"name":  "John",
										"email": "<EMAIL>",
									},
									map[string]interface{}{
										"name":  "Jane",
										"email": "<EMAIL>",
									},
								},
							},
							map[string]interface{}{
								"name": "Backend",
								"members": []interface{}{
									map[string]interface{}{
										"name":  "Bob",
										"email": "<EMAIL>",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "Multiple redaction paths",
			input: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name":     "John",
						"password": "secret123",
						"cards": []interface{}{
							map[string]interface{}{
								"number": "1234-5678-9012-3456",
								"cvv":    "123",
							},
						},
					},
				},
			},
			redactElements: []string{
				"users.*.password",
				"users.*.cards.*.cvv",
			},
			expected: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name": "John",
						"cards": []interface{}{
							map[string]interface{}{
								"number": "1234-5678-9012-3456",
							},
						},
					},
				},
			},
		},
		{
			name: "Redact entire array",
			input: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name":     "John",
						"password": "secret123",
					},
					map[string]interface{}{
						"name":     "Jane",
						"password": "password456",
					},
				},
				"apiKeys": []interface{}{
					"key1", "key2", "key3",
				},
			},
			redactElements: []string{"apiKeys"},
			expected: map[string]interface{}{
				"users": []interface{}{
					map[string]interface{}{
						"name":     "John",
						"password": "secret123",
					},
					map[string]interface{}{
						"name":     "Jane",
						"password": "password456",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			codec := NewBaseLogCodec(tt.redactElements)

			// Marshal the input
			result, err := codec.Marshal(tt.input)
			if err != nil {
				t.Fatalf("Failed to marshal: %v", err)
			}

			// Unmarshal the result to compare with expected
			var actual map[string]interface{}
			if err := json.Unmarshal(result, &actual); err != nil {
				t.Fatalf("Failed to unmarshal result: %v", err)
			}

			// Compare with expected
			if !compareJSON(actual, tt.expected) {
				expectedJSON, _ := json.MarshalIndent(tt.expected, "", "  ")
				actualJSON, _ := json.MarshalIndent(actual, "", "  ")
				t.Errorf("Expected:\n%s\n\nGot:\n%s", expectedJSON, actualJSON)
			}
		})
	}
}

// Helper function to compare JSON objects
func compareJSON(a, b map[string]interface{}) bool {
	if len(a) != len(b) {
		return false
	}

	for k, v1 := range a {
		v2, ok := b[k]
		if !ok {
			return false
		}

		switch val1 := v1.(type) {
		case map[string]interface{}:
			val2, ok := v2.(map[string]interface{})
			if !ok || !compareJSON(val1, val2) {
				return false
			}
		case []interface{}:
			val2, ok := v2.([]interface{})
			if !ok || len(val1) != len(val2) {
				return false
			}
			for i, item1 := range val1 {
				item2 := val2[i]

				map1, isMap1 := item1.(map[string]interface{})
				map2, isMap2 := item2.(map[string]interface{})

				if isMap1 && isMap2 {
					if !compareJSON(map1, map2) {
						return false
					}
				} else if item1 != item2 {
					return false
				}
			}
		default:
			if v1 != v2 {
				return false
			}
		}
	}

	return true
}

func TestRequestCodecWithMethod(t *testing.T) {
	methods := []string{"GetUser", "UpdateUser"}
	redactElements := []string{"password", "users.*.password"}

	codec := NewRequestLogCodec(methods, redactElements)

	input := map[string]interface{}{
		"name":     "John",
		"password": "secret123",
		"users": []interface{}{
			map[string]interface{}{
				"name":     "Jane",
				"password": "password456",
			},
		},
	}

	// Test with method that should apply redaction
	result, err := codec.MarshalWithMethod("GetUser", input)
	if err != nil {
		t.Fatalf("Failed to marshal with method: %v", err)
	}

	var redacted map[string]interface{}
	if err := json.Unmarshal(result, &redacted); err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Password should be redacted
	if _, ok := redacted["password"]; ok {
		t.Error("Password should be redacted but was not")
	}

	// Test with method that should not apply redaction
	result, err = codec.MarshalWithMethod("ListUsers", input)
	if err != nil {
		t.Fatalf("Failed to marshal with method: %v", err)
	}

	var notRedacted map[string]interface{}
	if err := json.Unmarshal(result, &notRedacted); err != nil {
		t.Fatalf("Failed to unmarshal result: %v", err)
	}

	// Password should not be redacted
	if _, ok := notRedacted["password"]; !ok {
		t.Error("Password should not be redacted but was")
	}
}
