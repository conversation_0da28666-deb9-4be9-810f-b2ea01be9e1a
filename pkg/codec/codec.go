package codec

import (
	"bytes"
	"encoding/json"
	"strconv"
	"strings"

	"github.com/Jeffail/gabs/v2"
	jsoniter "github.com/json-iterator/go"
)

type LogCodec interface {
	Marshal(v interface{}) ([]byte, error)
	Unmarshal(v []byte, i interface{}) error
	MarshalWith<PERSON>ethod(method string, v interface{}) ([]byte, error)
}

func NewRequestLogCodec(methods []string, redactElements []string) LogCodec {
	appliedMethods := make(map[string]struct{})
	for _, method := range methods {
		appliedMethods[method] = struct{}{}
	}
	return &RequestCodec{
		appliedMethods: appliedMethods,
		redactElements: redactElements,
	}
}

type RequestCodec struct {
	appliedMethods map[string]struct{}
	redactElements []string
}

/**
 * Marshal will redact the elements in the redactElements list
 * and return the JSON byte array. Supports nested arrays at any level.
 */
func (c *RequestCodec) Marshal(v interface{}) ([]byte, error) {
	dataBin, err := jsoniter.Marshal(v)
	if err != nil {
		return nil, err
	}

	reader := bytes.NewReader(dataBin)
	decoder := json.NewDecoder(reader)
	decoder.UseNumber() // Use Number to preserve number types

	jc, err := gabs.ParseJSONDecoder(decoder)
	if err != nil {
		return nil, err
	}

	// Process each redact element path
	for _, path := range c.redactElements {
		redactPath(jc, path)
	}

	return jc.MarshalJSON()
}

func (c *RequestCodec) MarshalWithMethod(m string, v interface{}) ([]byte, error) {
	rawJSON, err := jsoniter.Marshal(v)
	if err != nil {
		return nil, err
	}
	if _, ok := c.appliedMethods[m]; !ok {
		return rawJSON, nil
	}
	return c.Marshal(v)
}

func (c *RequestCodec) Unmarshal(v []byte, i interface{}) error {
	return jsoniter.Unmarshal(v, i)
}

type BaseCodec struct {
	redactElements []string
}

func NewBaseLogCodec(redactElements []string) LogCodec {
	return &BaseCodec{redactElements: redactElements}
}

// Marshal implements LogCodec.
func (b *BaseCodec) Marshal(v interface{}) ([]byte, error) {
	dataBin, err := jsoniter.Marshal(v)
	if err != nil {
		return nil, err
	}

	reader := bytes.NewReader(dataBin)
	decoder := json.NewDecoder(reader)
	decoder.UseNumber() // Use Number to preserve number types

	jc, err := gabs.ParseJSONDecoder(decoder)
	if err != nil {
		return nil, err
	}

	// Process each redact element path
	for _, path := range b.redactElements {
		redactPath(jc, path)
	}

	return jc.MarshalJSON()
}

// MarshalWithMethod implements LogCodec.
func (b *BaseCodec) MarshalWithMethod(method string, v interface{}) ([]byte, error) {
	return b.Marshal(v)
}

// Unmarshal implements LogCodec.
func (b *BaseCodec) Unmarshal(v []byte, i interface{}) error {
	return jsoniter.Unmarshal(v, i)
}

// redactPath handles the redaction of fields based on the path pattern
// It supports nested arrays with wildcards at any level
func redactPath(container *gabs.Container, path string) {
	// Split the path into segments
	segments := strings.Split(path, ".")

	// Handle direct path with no wildcards
	if !strings.Contains(path, "*") {
		container.DeleteP(path)
		return
	}

	// Find the first wildcard segment
	wildcardIndex := -1
	for i, segment := range segments {
		if segment == "*" {
			wildcardIndex = i
			break
		}
	}

	// If no wildcard found, delete the path directly
	if wildcardIndex == -1 {
		container.DeleteP(path)
		return
	}

	// Get the parent path before the wildcard
	parentPath := strings.Join(segments[:wildcardIndex], ".")

	// Get the parent container
	parentContainer := container
	if parentPath != "" {
		parentContainer = container.Path(parentPath)
		// If parent doesn't exist, nothing to redact
		if parentContainer == nil || parentContainer.Data() == nil {
			return
		}
	}

	// Get all children of the parent
	children := parentContainer.Children()
	if len(children) == 0 {
		return
	}

	// For each child, handle the remaining path
	for i := range children {
		// Replace the wildcard with the current index
		remainingSegments := make([]string, len(segments))
		copy(remainingSegments, segments)
		remainingSegments[wildcardIndex] = strconv.Itoa(i)

		// Create the new path with the current index
		newPath := strings.Join(remainingSegments, ".")

		// If there are more wildcards in the remaining path, recurse
		remainingEles := remainingSegments[wildcardIndex+1:]
		remainingPath := strings.Join(remainingEles, ".")
		if strings.Contains(remainingPath, "*") {
			redactPath(container, newPath)
			continue
		}

		container.DeleteP(newPath)
	}
}
