package bootstrap

import (
	"context"
	"crypto/tls"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/pkg/errors"
	"google.golang.org/grpc/metadata"

	kgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"go.opentelemetry.io/otel"
	"google.golang.org/grpc"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	imetrics "gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/logging"
)

var (
	clientMetrics = imetrics.NewOtelRequestMetrics(imetrics.Options{
		Module: imetrics.MetricModuleGrpcClient,
	})
)

type GrpcConfig struct {
	Address string
	Timeout time.Duration
	Secured bool
	Logger  log.Logger
	Codec   codec.LogCodec
}

func InitGrpcConn(grpcConf *GrpcConfig, grpcOpts ...grpc.DialOption) (*grpc.ClientConn, error) {
	if grpcConf == nil {
		return nil, errors.New("grpc config is nil")
	}
	if grpcConf.Timeout == 0 {
		grpcConf.Timeout = 10 * time.Second
	}

	if grpcConf.Logger == nil {
		grpcConf.Logger = log.GetLogger()
	}

	ctx, cancel := context.WithTimeout(context.Background(), grpcConf.Timeout)
	defer cancel()

	var opts []kgrpc.ClientOption
	opts = append(opts, kgrpc.WithEndpoint(grpcConf.Address))
	opts = append(opts, kgrpc.WithTimeout(grpcConf.Timeout))
	opts = append(opts, kgrpc.WithHealthCheck(false))
	opts = append(opts, kgrpc.WithOptions(grpc.WithDisableServiceConfig()))

	opts = append(opts, kgrpc.WithMiddleware(
		metrics.Client(
			metrics.WithRequests(clientMetrics.HandledCounter),
			metrics.WithSeconds(clientMetrics.HandledHistogram),
		),
		tracing.Client(
			tracing.WithTracerProvider(otel.GetTracerProvider()),
			tracing.WithPropagator(otel.GetTextMapPropagator()),
		),
		logging.Client(grpcConf.Logger, grpcConf.Codec),
	))

	if !grpcConf.Secured {
		opts = append(opts, kgrpc.WithOptions(grpcOpts...))
		return kgrpc.DialInsecure(ctx, opts...)
	}

	opts = append(opts, kgrpc.WithTLSConfig(&tls.Config{
		InsecureSkipVerify: false,
		MinVersion:         tls.VersionTLS12,
	}))

	opts = append(opts, kgrpc.WithOptions(grpcOpts...))

	return kgrpc.Dial(ctx, opts...)
}

func WithCliCredsInterceptor(clientID, clientKey string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		ctx = metadata.AppendToOutgoingContext(ctx,
			"client-id", clientID,
			"client-key", clientKey,
		)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func WithCliTraceFromCtxInterceptor(getFunc func(ctx context.Context) string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		ctx = metadata.AppendToOutgoingContext(ctx,
			"trace_id", getFunc(ctx),
		)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func WithMetadataAppendInterceptor(kv ...string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		if len(kv)%2 != 0 {
			return errors.New("metadata key value must be even")
		}
		ctx = metadata.AppendToOutgoingContext(ctx, kv...)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}
