package zutils

import (
	"encoding/base64"
	"encoding/json"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"strings"
)

func Base64DataToImage(base64Str string, mime string) (image.Image, error) {
	base64Prefix := "data:" + mime + ";base64,"
	base64Data := base64Str[len(base64Prefix):]
	dataReader := strings.NewReader(base64Data)
	base64Decoder := base64.NewDecoder(base64.StdEncoding, dataReader)
	imageDecoded, _, err := image.Decode(base64Decoder)
	if err != nil {
		return nil, err
	}
	return imageDecoded, nil
}

func Base64DataToBinary(base64Str string, mime string) ([]byte, error) {
	base64Prefix := "data:" + mime + ";base64,"
	base64Data := base64Str[len(base64Prefix):]
	binaryData, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return nil, err
	}
	return binaryData, nil
}

func Base64DataToStruct(base64Str string, v any) error {
	binaryData, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return err
	}
	err = json.Unmarshal(binaryData, v)
	if err != nil {
		return err
	}
	return nil
}
