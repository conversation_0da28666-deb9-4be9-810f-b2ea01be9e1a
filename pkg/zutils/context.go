package zutils

import (
	"context"

	"github.com/go-kratos/kratos/v2/middleware/tracing"
)

func TraceIDFromCtx(ctx context.Context) string {
	traceIDValFromTracing := tracing.TraceID()(ctx)
	traceIDStrFromTracing, ok := traceIDValFromTracing.(string)
	if ok && traceIDStrFromTracing != "" {
		return traceIDStrFromTracing
	}

	traceIDValFromCtx := ctx.Value("trace.id")
	traceIDStrFromCtx, ok := traceIDValFromCtx.(string)
	if ok && traceIDStrFromCtx != "" {
		return traceIDStrFromCtx
	}

	return ""
}
