package zutils

import "slices"

const (
	GenderUnknown = "UNKNOWN"
	GenderMale    = "MALE"
	GenderFemale  = "FEMALE"
)

func GenderConvertToString(gender int32) string {
	switch gender {
	case 1:
		return GenderMale
	case 2:
		return GenderFemale
	default:
		return GenderUnknown
	}
}

func GenderConvertToInt(str string) int32 {
	switch str {
	case GenderMale:
		return 1
	case GenderFemale:
		return 2
	default:
		return 0
	}
}

func GenderNfcConvertToInt(str string) int32 {
	switch str {
	case "Nam":
		return 1
	case "Nữ":
		return 2
	default:
		return 0
	}
}

func IsGenderStringValid(gender string) bool {
	validGender := []string{GenderMale, GenderFemale}
	return slices.Contains(validGender, gender)
}
