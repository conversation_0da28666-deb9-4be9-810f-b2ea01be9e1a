package zutils

import (
	"database/sql"
	"strings"
	"time"
)

func FormatDBSource(source string) string {
	return strings.NewReplacer("\n", "", " ", "").Replace(source)
}

func NewNullString(s string) sql.NullString {
	return sql.NullString{String: s, Valid: true}
}

func NewNullTime(t time.Time) sql.NullTime {
	if t.<PERSON>() {
		return sql.NullTime{Valid: false}
	}
	return sql.NullTime{Valid: true, Time: t}
}

func NewNullInt64(i int64) sql.NullInt64 {
	return sql.NullInt64{Int64: i, Valid: true}
}

func NewNullInt32(i int32) sql.NullInt32 {
	return sql.NullInt32{Int32: i, Valid: true}
}

func NewNullFloat64(f float64) sql.NullFloat64 {
	return sql.NullFloat64{Float64: f, Valid: true}
}

func NewInvalidNullString(s string) sql.NullString {
	return sql.NullString{String: s, Valid: false}
}
