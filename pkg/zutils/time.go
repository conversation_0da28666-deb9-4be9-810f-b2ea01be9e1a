package zutils

import (
	"time"
)

const (
	LayoutSlashDDMMYYYY = "02/01/2006"
	LayoutDashYYYYMMDD  = "2006-01-02"
	LayoutFullWithDash  = "2006-01-02T15:04:05-07"
	LayoutYYMMDD        = "060102"
	LayoutYYYYMM        = "200601"
)

func TimeConvertIntToString(msec int64) string {
	if msec == 0 {
		return ""
	}
	return time.UnixMilli(msec * 1000).Format(LayoutDashYYYYMMDD)
}

func TimeConvertStringToInt64(str string) int64 {
	t, err := time.Parse(LayoutYYMMDD, str)
	if err != nil {
		return 0
	}
	return t.UnixMilli()
}

func TruncateDate(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}
