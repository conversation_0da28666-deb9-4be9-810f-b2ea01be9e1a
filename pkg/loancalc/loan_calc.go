package loancalc

import (
	"errors"

	"github.com/shopspring/decimal"
)

type LoanCalculator interface {
	CalculateEMIAmount(principal int64, tenure int32, partner string) (int64, error)
}

type loanCalculator struct{}

func NewLoanCalculator() LoanCalculator {
	return &loanCalculator{}
}

// CalculateEmiAmount implements LoanCalculator.
func (l *loanCalculator) CalculateEMIAmount(principal int64, tenure int32, partner string) (int64, error) {
	interestRate := l.getHardAnnualRate(tenure)
	emiAmount := l.calcAvgEmiAmount(principal, tenure, interestRate)
	if emiAmount <= 0 {
		return 0, errors.New("invalid EMI amount")
	}
	return emiAmount, nil
}

func (l *loanCalculator) getHardAnnualRate(tenure int32) float64 {
	switch tenure {
	case 3:
		return 0.54
	case 6:
		return 0.6024
	case 9:
		return 0.6181
	case 12:
		return 0.6205
	default:
		return 0.54
	}
}

/**
 * CalcAvgPlanEmiAmount using cimb recipe provided to calculate the average emi amount
 * TODO,IMPORTANT: Maybe we should move this func to a usecase package for reusable and easy managed by partner
 * Repice: ROUNDUP_PMT(P,r,n,d) = [(P*r)/(1-(1+r)^(-n)) * 10^d] * 10^(-d)
 * P: Principal amount
 * r: Interest rate
 * n: Tenure
 * d: Decimal places (2)
 */
func (l *loanCalculator) calcAvgEmiAmount(principal int64, tenure int32, interestRate float64) int64 {
	// Convert to decimal
	ratioDec := decimal.NewFromInt(1)
	constDec := decimal.NewFromInt(10)
	tenureDec := decimal.NewFromInt32(tenure)
	yearMonthDec := decimal.NewFromInt(12)
	tenureDecNeg := tenureDec.Neg()
	principalDec := decimal.NewFromInt(principal)
	iRatePerYearDec := decimal.NewFromFloat(interestRate)
	iRatePerMonthDec := iRatePerYearDec.Div(yearMonthDec)
	decimalPlacesDec := decimal.NewFromInt(2)
	decimalPlacesNegDec := decimalPlacesDec.Neg()

	// Calculate emi amount
	exp1Dec := principalDec.Mul(iRatePerMonthDec)
	exp2Dec := ratioDec.Sub((ratioDec.Add(iRatePerMonthDec)).Pow(tenureDecNeg))
	exp3Dec := constDec.Pow(decimalPlacesDec)
	exp4Dec := constDec.Pow(decimalPlacesNegDec)
	emiDec := exp1Dec.Div(exp2Dec).Mul(exp3Dec).Mul(exp4Dec)

	return emiDec.IntPart()
}
