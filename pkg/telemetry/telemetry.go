package telemetry

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/contrib/propagators/b3"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
)

type TracingInfo struct {
	SvcVer      string
	SvcName     string
	Environment string
	AgentHost   string
	AgentPort   int32
}

func newResource(info *TracingInfo) (*resource.Resource, error) {
	return resource.Merge(resource.Default(),
		resource.NewWithAttributes(semconv.SchemaURL,
			semconv.ServiceName(info.SvcName),
			semconv.ServiceVersion(info.SvcVer),
			semconv.DeploymentEnvironment(info.Environment),
		))
}

func newPropagator() propagation.TextMapPropagator {
	return propagation.NewCompositeTextMapPropagator(
		// At ZaloPay we use B3 format to propagation Context
		b3.New(b3.WithInjectEncoding(b3.B3MultipleHeader|b3.B3SingleHeader)),
		propagation.TraceContext{},
		propagation.Baggage{},
	)
}

func newMeterProvider(info *TracingInfo) (*metric.MeterProvider, error) {
	metricExporter, err := prometheus.New()
	if err != nil {
		return nil, err
	}
	meterProvider := metric.NewMeterProvider(
		metric.WithReader(metricExporter),
		metric.WithResource(resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(info.SvcName),
			semconv.ServiceVersion(info.SvcVer),
		)),
	)
	return meterProvider, nil
}

func newOtlpGRPC(info *TracingInfo) (trace.SpanExporter, error) {
	endpoint := fmt.Sprintf("%s:%d", info.AgentHost, info.AgentPort)
	return otlptracegrpc.New(
		context.Background(),
		otlptracegrpc.WithEndpoint(endpoint),
		otlptracegrpc.WithInsecure(),
	)
}

func newTraceProvider(res *resource.Resource, info *TracingInfo) (*trace.TracerProvider, error) {
	var traceExporter trace.SpanExporter
	traceExporter, err := newOtlpGRPC(info)

	if err != nil {
		return nil, err
	}

	traceProvider := trace.NewTracerProvider(
		// Set the sampling rate based on the parent span to 100%
		trace.WithSampler(trace.ParentBased(trace.TraceIDRatioBased(1.0))),
		//trace.WithSyncer(traceExporter),
		// Always be sure to batch in production.
		trace.WithBatcher(traceExporter,
			// Default is 5s. Set to 1s for demonstrative purposes.
			trace.WithBatchTimeout(time.Second),
		),
		// Record information about this application in a Resource.
		trace.WithResource(res),
	)
	return traceProvider, nil
}

func MustInitTracer(info *TracingInfo) error {
	// Set up resource.
	res, err := newResource(info)
	if err != nil {
		return err
	}

	// Set up propagator.
	propagator := newPropagator()
	// Register our propagator globally.
	otel.SetTextMapPropagator(propagator)

	// Set up trace provider.
	tp, err := newTraceProvider(res, info)
	if err != nil {
		return err
	}

	mp, err := newMeterProvider(info)
	if err != nil {
		return err
	}

	// Set the wrapperTracerProvider as the global OpenTelemetry
	// TracerProvider so instrumentation will use it by default.
	otel.SetTracerProvider(tp)
	otel.SetMeterProvider(mp)

	return nil
}
