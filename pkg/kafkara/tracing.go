package kafkara

import (
	"context"
	"encoding/base64"

	"github.com/segmentio/kafka-go"
	"go.opentelemetry.io/contrib/propagators/b3"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
	kafka_client "zalopay.io/zgo/kafka-client"
)

// ExtractB3FromHeaders extracts B3 tracing headers from Kafka message headers and returns a context with tracing information.
// If no B3 headers found, creates a new trace context.
func ExtractB3FromHeaders(headers []kafka.Header) context.Context {
	// Create a carrier to store the B3 format headers
	carrier := make(map[string]string)
	ctx := context.Background()

	// B3 headers can be in single or multi-header format
	// See: https://github.com/openzipkin/b3-propagation
	for _, header := range headers {
		// Kafka header values are Base64 encoded, decode them
		decodedValue, err := base64.StdEncoding.DecodeString(string(header.Value))
		if err != nil {
			// If decoding fails, use the original value as fallback
			decodedValue = header.Value
		}

		switch header.Key {
		case "b3", // Single header format
			"x-b3-traceid",      // Multi-header format - trace ID
			"x-b3-spanid",       // Multi-header format - span ID
			"x-b3-parentspanid", // Multi-header format - parent span ID
			"x-b3-sampled",      // Multi-header format - sampling decision
			"x-b3-flags":        // Multi-header format - debug flag
			carrier[header.Key] = string(decodedValue)
		}
	}

	// Create a B3 propagator
	b3Propagator := b3.New(b3.WithInjectEncoding(b3.B3MultipleHeader | b3.B3SingleHeader))

	// If B3 headers found, extract the trace context
	if len(carrier) > 0 {
		return b3Propagator.Extract(ctx, propagation.MapCarrier(carrier))
	}

	// If no B3 headers found, create a new trace context
	tracer := otel.GetTracerProvider().Tracer("kafka-consumer")
	ctx, span := tracer.Start(ctx, "kafka-message-processing")
	defer span.End()

	return ctx
}

// InjectB3ToKafkaHeaders injects tracing information from context into Kafka message headers.
// It returns a slice of Kafka headers containing B3 propagation format.
// This can be used when producing Kafka messages to ensure trace context propagation across services.
func InjectB3ToKafkaHeaders(ctx context.Context) []kafka.Header {
	// Create a B3 propagator with multiple and single header support
	b3Propagator := b3.New(b3.WithInjectEncoding(b3.B3MultipleHeader))

	// Create a carrier to store the B3 format headers
	carrier := propagation.MapCarrier{}

	// Inject the current trace context into the carrier
	b3Propagator.Inject(ctx, carrier)

	// No trace context in the provided context, return empty headers
	if len(carrier) == 0 {
		return []kafka.Header{}
	}

	// Convert the carrier to Kafka headers
	headers := make([]kafka.Header, 0, len(carrier))
	for key, value := range carrier {
		// Encode header values as Base64
		encodedValue := base64.StdEncoding.EncodeToString([]byte(value))
		headers = append(headers, kafka.Header{
			Key:   key,
			Value: []byte(encodedValue),
		})
	}

	return headers
}

// AppendB3HeadersToKafkaMessage appends tracing headers to an existing Kafka message.
// This is a helper function when you already have a Kafka message and want to add tracing.
func AppendB3HeadersToKafkaMessage(ctx context.Context, message kafka.Message) kafka.Message {
	// Get B3 headers from context
	tracingHeaders := InjectB3ToKafkaHeaders(ctx)

	// Append the tracing headers to the existing headers
	message.Headers = append(message.Headers, tracingHeaders...)

	return message
}

// ConvertSegmentioHeadersToClientHeaders converts segmentio kafka.Header to kafka_client.Header
// This is necessary when using headers with zalopay.io/zgo/kafka-client library
func ConvertSegmentioHeadersToClientHeaders(headers []kafka.Header) []kafka_client.Header {
	clientHeaders := make([]kafka_client.Header, len(headers))
	for i, header := range headers {
		clientHeaders[i] = kafka_client.Header{
			Key:   header.Key,
			Value: header.Value,
		}
	}
	return clientHeaders
}

// InjectB3ToClientKafkaHeaders injects tracing information from context and returns kafka_client.Header slice
// This is a convenience function that combines InjectB3ToKafkaHeaders and ConvertSegmentioHeadersToClientHeaders
func InjectB3ToClientKafkaHeaders(ctx context.Context) []kafka_client.Header {
	segmentioHeaders := InjectB3ToKafkaHeaders(ctx)
	return ConvertSegmentioHeadersToClientHeaders(segmentioHeaders)
}
