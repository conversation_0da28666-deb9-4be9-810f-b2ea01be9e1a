package keygen

import (
	"fmt"
	"strings"
)

//go:generate mockgen -source=rediskey.go -destination=mock/rediskey_mock.go -package=mock
type RedisKeyGenerator interface {
	Generate(name string) (string, error)
}

type redisKeyTemplate struct {
	env     string
	service string
	prefix  string
}

const (
	separator = ":"
	namespace = "fin:installment"
)

func NewRedisKeyGenerator(env, service string) (RedisKeyGenerator, error) {
	keyParts := []string{namespace, env, service}
	keyPrefix := strings.Join(keyParts, separator)
	return redisKeyTemplate{env: env, service: service, prefix: keyPrefix}, nil
}

func (rkt redisKeyTemplate) Generate(name string) (string, error) {
	if name == "" {
		return "", fmt.Errorf("name cannot be empty")
	}
	return fmt.Sprintf("%s:%s", rkt.prefix, name), nil
}
