// Code generated by MockGen. DO NOT EDIT.
// Source: rediskey.go
//
// Generated by this command:
//
//	mockgen -source=rediskey.go -destination=mock/rediskey_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRedisKeyGenerator is a mock of RedisKeyGenerator interface.
type MockRedisKeyGenerator struct {
	ctrl     *gomock.Controller
	recorder *MockRedisKeyGeneratorMockRecorder
	isgomock struct{}
}

// MockRedisKeyGeneratorMockRecorder is the mock recorder for MockRedisKeyGenerator.
type MockRedisKeyGeneratorMockRecorder struct {
	mock *MockRedisKeyGenerator
}

// NewMockRedisKeyGenerator creates a new mock instance.
func NewMockRedisKeyGenerator(ctrl *gomock.Controller) *MockRedisKeyGenerator {
	mock := &MockRedisKeyGenerator{ctrl: ctrl}
	mock.recorder = &MockRedisKeyGeneratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedisKeyGenerator) EXPECT() *MockRedisKeyGeneratorMockRecorder {
	return m.recorder
}

// Generate mocks base method.
func (m *MockRedisKeyGenerator) Generate(name string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Generate", name)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Generate indicates an expected call of Generate.
func (mr *MockRedisKeyGeneratorMockRecorder) Generate(name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Generate", reflect.TypeOf((*MockRedisKeyGenerator)(nil).Generate), name)
}
