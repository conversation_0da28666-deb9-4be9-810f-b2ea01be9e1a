package parallel

import (
	"context"
	"fmt"

	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
)

type ExecFunc func(ctx context.Context) (any, error)

type Result struct {
	Result any
	Error  error
}

type Results []Result

// All returns a slice of all results from the slice of Result.
func (results Results) All() []interface{} {
	all := make([]interface{}, len(results))
	for i, result := range results {
		all[i] = result.Result
	}
	return all
}

// Errors returns a slice of all errors from the slice of Result.
func (results Results) Errors() []error {
	var errs []error
	for _, result := range results {
		if result.Error != nil {
			errs = append(errs, result.Error)
		}
	}
	return errs
}

// FirstError returns the first error encountered in the slice of Result.
func (results Results) FirstError() error {
	for _, result := range results {
		if result.Error != nil {
			return result.Error
		}
	}
	return nil
}

// HasError returns true if any of the Result in the slice has an error.
func (results Results) HasError() bool {
	for _, result := range results {
		if result.Error != nil {
			return true
		}
	}
	return false
}

// String returns a string representation of the slice of Result.
func (results Results) String() string {
	var str string
	for i, result := range results {
		str += fmt.Sprintf("Result %d: %v, Error: %v\n", i, result.Result, result.Error)
	}
	return str
}

// Validate checks if any of the Result in the slice has an error. If so, it returns the first error.
func (results Results) Validate() error {
	for _, result := range results {
		if result.Error != nil {
			return errors.New("one or more promises have failed")
		}
	}
	return nil
}

// Get returns the Result at the specified index from the slice of Result.
// If the index is out of bounds, it returns an error.
func (results Results) Get(index int) (Result, error) {
	if index < 0 || index >= len(results) {
		return Result{}, fmt.Errorf("index out of bounds: %d", index)
	}
	return results[index], nil
}

// CastAt returns the Result at the specified index from the slice of Result and tries to cast it to the specified interface.
// If the index is out of bounds, it returns an error.
// If the Result cannot be cast to the specified interface, it also returns an error.
func (results Results) CastAt(index int, v interface{}) error {
	if index < 0 || index >= len(results) {
		return fmt.Errorf("index out of bounds: %d", index)
	}
	result := results[index]
	if result.Error != nil {
		return result.Error
	}

	if err := mapstructure.Decode(result.Result, v); err != nil {
		return fmt.Errorf("error decoding result: %w", err)
	}

	return nil
}
