package parallel

import (
	"context"
	"sync"
	"time"
)

// parallel execution of functions
// PromiseAll mimics the behavior of Promise.all in JavaScript.
// It takes a slice of functions that return a result and an error, and executes them concurrently.
// It returns a slice of PromiseResult, each containing the result or error of a function.
// It also takes a timeout duration. If the functions do not complete within the timeout, the context is cancelled.
func PromiseAll(ctx context.Context, funcs []ExecFunc,
	timeout time.Duration) (Results, error) {
	results := make([]Result, len(funcs))
	var wg sync.WaitGroup
	var once sync.Once

	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	for i, fn := range funcs {
		wg.Add(1)
		go func() {
			defer wg.Done()
			res, err := fn(ctx)
			if err != nil {
				// If an error is encountered, cancel all goroutines
				once.Do(cancel)
			}
			results[i] = Result{Result: res, Error: err}
		}()
	}

	wg.Wait()

	// Check if there was an error and return it
	for _, result := range results {
		if result.Error != nil {
			return nil, result.Error
		}
	}

	return results, nil
}
