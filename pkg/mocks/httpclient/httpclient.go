// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocks (interfaces: HttpClient)
//
// Generated by this command:
//
//	mockgen -destination=./httpclient/httpclient.go -package=httpclientmocks . HttpClient
//

// Package httpclientmocks is a generated GoMock package.
package httpclientmocks

import (
	context "context"
	reflect "reflect"

	httpclient "gitlab.zalopay.vn/fin/platform/common/httpclient"
	gomock "go.uber.org/mock/gomock"
)

// MockHttpClient is a mock of HttpClient interface.
type MockHttpClient struct {
	ctrl     *gomock.Controller
	recorder *MockHttpClientMockRecorder
	isgomock struct{}
}

// MockHttpClientMockRecorder is the mock recorder for MockHttpClient.
type MockHttpClientMockRecorder struct {
	mock *MockHttpClient
}

// NewMockHttpClient creates a new mock instance.
func NewMockHttpClient(ctrl *gomock.Controller) *MockHttpClient {
	mock := &MockHttpClient{ctrl: ctrl}
	mock.recorder = &MockHttpClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHttpClient) EXPECT() *MockHttpClientMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockHttpClient) Get(ctx context.Context, uri string, result, errorDetail any, funcName string, opts ...httpclient.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, result, errorDetail, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockHttpClientMockRecorder) Get(ctx, uri, result, errorDetail, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, result, errorDetail, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockHttpClient)(nil).Get), varargs...)
}

// Post mocks base method.
func (m *MockHttpClient) Post(ctx context.Context, uri string, body, result, errorDetail any, funcName string, opts ...httpclient.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, body, result, errorDetail, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Post", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Post indicates an expected call of Post.
func (mr *MockHttpClientMockRecorder) Post(ctx, uri, body, result, errorDetail, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, body, result, errorDetail, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Post", reflect.TypeOf((*MockHttpClient)(nil).Post), varargs...)
}

// Put mocks base method.
func (m *MockHttpClient) Put(ctx context.Context, uri string, body, result, errorDetail any, funcName string, opts ...httpclient.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, body, result, errorDetail, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Put", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Put indicates an expected call of Put.
func (mr *MockHttpClientMockRecorder) Put(ctx, uri, body, result, errorDetail, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, body, result, errorDetail, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockHttpClient)(nil).Put), varargs...)
}

// RawGet mocks base method.
func (m *MockHttpClient) RawGet(ctx context.Context, uri, funcName string, opts ...httpclient.Option) (int, *httpclient.RawResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RawGet", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*httpclient.RawResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RawGet indicates an expected call of RawGet.
func (mr *MockHttpClientMockRecorder) RawGet(ctx, uri, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawGet", reflect.TypeOf((*MockHttpClient)(nil).RawGet), varargs...)
}

// RawPatch mocks base method.
func (m *MockHttpClient) RawPatch(ctx context.Context, uri string, body any, funcName string, opts ...httpclient.Option) (int, *httpclient.RawResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, body, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RawPatch", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*httpclient.RawResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RawPatch indicates an expected call of RawPatch.
func (mr *MockHttpClientMockRecorder) RawPatch(ctx, uri, body, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, body, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawPatch", reflect.TypeOf((*MockHttpClient)(nil).RawPatch), varargs...)
}

// RawPost mocks base method.
func (m *MockHttpClient) RawPost(ctx context.Context, uri string, body any, funcName string, opts ...httpclient.Option) (int, *httpclient.RawResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, body, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RawPost", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*httpclient.RawResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RawPost indicates an expected call of RawPost.
func (mr *MockHttpClientMockRecorder) RawPost(ctx, uri, body, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, body, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawPost", reflect.TypeOf((*MockHttpClient)(nil).RawPost), varargs...)
}

// RawPut mocks base method.
func (m *MockHttpClient) RawPut(ctx context.Context, uri string, body any, funcName string, opts ...httpclient.Option) (int, *httpclient.RawResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, uri, body, funcName}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RawPut", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*httpclient.RawResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RawPut indicates an expected call of RawPut.
func (mr *MockHttpClientMockRecorder) RawPut(ctx, uri, body, funcName any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, uri, body, funcName}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RawPut", reflect.TypeOf((*MockHttpClient)(nil).RawPut), varargs...)
}
