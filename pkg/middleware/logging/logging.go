package logging

import (
	"context"
	"fmt"
	"time"

	json "github.com/json-iterator/go"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
)

/*
* Logging middleware is a middleware that logs the request and response.
* Notice: This middleware was cloned from the official Kratos repository and custom something to fit the project.
 */

// Redacter defines how to log an object
type Redacter interface {
	Redact() string
}

type LogCodec interface {
	codec.LogCodec
}

// Server is an server logging middleware.
func Server(logger log.Logger, codec LogCodec) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)
			startTime := time.Now()
			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			level, stack := extractError(err)
			log.NewHelper(log.WithContext(ctx, logger)).Log(level,
				"component", kind,
				"operation", operation,
				"request", extractArgs(applyCodec(operation, req, codec)),
				"response", extractArgs(applyCodec(operation, reply, codec)),
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

// Client is a client logging middleware.
func Client(logger log.Logger, codec LogCodec) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)
			startTime := time.Now()
			if info, ok := transport.FromClientContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			level, stack := extractError(err)
			log.NewHelper(log.WithContext(ctx, logger)).Log(level,
				"kind", "client",
				"component", kind,
				"operation", operation,
				"request", extractArgs(applyCodec(operation, req, codec)),
				"response", extractArgs(applyCodec(operation, reply, codec)),
				"code", code,
				"reason", reason,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

func applyCodec(method string, value interface{}, codec LogCodec) interface{} {
	if codec == nil {
		return value
	}
	data, err := codec.MarshalWithMethod(method, value)
	if err != nil {
		return value
	}
	var result any
	if err = codec.Unmarshal(data, &result); err != nil {
		return value
	}
	return result
}

// extractArgs returns the string of the req
func extractArgs(value interface{}) string {
	if redacter, ok := value.(Redacter); ok {
		return redacter.Redact()
	}
	if stringer, ok := value.(fmt.Stringer); ok {
		return stringer.String()
	}
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Sprintf("%+v", value)
	}
	return string(data)
}

// extractError returns the string of the error
func extractError(err error) (log.Level, string) {
	if err != nil {
		return log.LevelInfo, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}
