# Technical Context

## Technologies Used

- **Primary Language**: Go (Golang)
- **API Protocols**: gRPC, RESTful HTTP
- **API Definition**: OpenAPI/Swagger, Protocol Buffers (Protobuf)
- **Database**: MySQL with JSON column support for flexible event payloads
- **Caching**: Redis for high-frequency data access
- **Message Brokers**: Kafka for event-driven communication between services
- **Storage**: S3 (for file storage)
- **Workflow Engine**: Temporal for orchestrating complex business processes
- **Containerization**: Docker
- **Observability**: OpenTelemetry tracing, Prometheus metrics

## Development Environment

- **OS**: macOS (development), Linux (production)
- **IDE**: Compatible with any Go-supporting IDE
- **Configuration Management**: YAML-based configuration with environment-specific files
- **Local Environment**:
  - Local MySQL database
  - Local Redis instance
  - Local Kafka broker
  - Connection to QC environments for external services
- **Version Control**: Git
- **Code Organization**:
  - Domain-driven design with clean architecture
  - Separation of concerns between domains (account, management, onboarding)

## Dependencies

- **Go Modules**:

  - github.com/go-kratos/kratos/v2: Go microservice framework
  - zalopay.io/zgo/kafka-client: Kafka client library
  - go.temporal.io: Temporal workflow engine client
  - github.com/go-sql-driver/mysql: MySQL driver
  - github.com/lib/pq: PostgreSQL driver (for certain components)
  - github.com/golang/protobuf: Protocol buffer support
  - github.com/grpc-ecosystem/grpc-gateway: RESTful API gateway for gRPC
  - Other standard Go libraries and utilities

- **External Services**:
  - ZaloPay Session Service
  - User Profile Service
  - CIMB Connector Service
  - eKYC Center
  - Face Authentication Service
  - Risk System
  - Credit Score Service
  - AB Platform
  - Paylater Product Service (new integration)

## Build Process

- **Build Tool**: Go build, Make
- **Makefile**: Used for automating build tasks
- **Docker**: Used for containerization
- **Build Artifacts**: Go binaries
- **Dependency Management**: Go modules

## Deployment Strategy

- **Containerization**: Docker containers
- **Environments**:
  - Local (local.yaml)
  - Development (dev.yaml)
  - QC (qc.yaml)
  - Staging (stg.yaml)
  - Production (prod.yaml)
- **Configuration Management**: Environment-specific YAML files
- **Service Discovery**: DNS-based service discovery
- **Feature Flags**: Configuration-based for gradual rollout

## Testing Framework

- **Unit Testing**: Go's native testing package
- **Mock Generation**: Mockgen for interface mocking
- **Test Automation**: Test files structured alongside production code
- **Integration Testing**: Tests that validate cross-service communication
- **Performance Testing**: Load testing for event processing pipeline

## Infrastructure

- **Service Architecture**: Microservices architecture
- **Domains**:
  - Account Service (account management, balance tracking)
  - Onboarding Service (user onboarding, KYC, contract signing)
  - Management Service (installment plan management, statements)
- **Data Storage**:
  - MySQL databases (separate for each domain)
  - Redis for caching
  - S3 for file storage
- **Message Broker**: Kafka for event-driven communication
  - Topics configured for different event types
  - Consumer groups for scalable processing
  - Partitioning for parallel processing
- **Workflow Engine**: Temporal for orchestrating long-running processes

## Integration Points

- **Internal Services**:

  - Account Service
  - Onboarding Service
  - Management Service
  - These services communicate via gRPC

- **External Services**:

  - CIMB Banking Connector
  - User Profile Service
  - eKYC services for identity verification
  - Face Authentication Service
  - Risk System for fraud detection
  - AB Platform for A/B testing
  - Various ZaloPay internal services
  - Paylater Product Service (new integration)

- **Event-based Communication**:
  - Kafka topics for various event types:
    - CRM events
    - Payment updates
    - Refund settlements
    - Onboarding events
    - Paylater product events (new integration)

## Monitoring and Observability

- **Logging**: Structured JSON logging
- **Tracing**: OpenTelemetry integration
  - Service names configured for each domain
  - Agent configuration for collecting telemetry data
  - Span context propagation across service boundaries
- **Metrics**: Prometheus metrics for system health monitoring
  - Custom metrics for event processing pipeline
  - Latency and throughput measurements
  - Error rate tracking
- **Health Checks**: Built-in for HTTP and gRPC servers
- **Error Handling**: Standardized error format with error codes and metadata
- **Alerting**: Configurable alerts for critical system conditions

## Technical Constraints

- **Security**:
  - SSL/TLS for secure communications
  - Client ID/Key authentication for service-to-service communication
  - Encryption for sensitive data
  - Proper access control for event processing
- **Maintenance Mode**:

  - Support for maintenance mode to gracefully handle downtime
  - Whitelist configurations for testing features

- **Performance**:
  - Connection pooling for databases
  - Redis caching for frequently accessed data
  - Configured timeouts for external service calls
  - Batch processing for event handling
  - Optimized database queries for JSON columns

## Development Workflows

- **Code Structure**:

  - Clean Architecture pattern:
    - Model: Domain models
    - Repository: Data access layer
    - Service: Controller layer should handle small business logic
    - Server: API endpoints server setup includes grpc server, http server, kafka consumer instance, etc...
    - Adapters: External service integrations
    - Usecase: Application use cases, main business handle in here. Include the global DTO dir

- **Event Processing**:

  - Kafka consumers for processing events
  - Event-driven architecture for asynchronous operations
  - Idempotent processing to handle duplicates
  - Ordered processing based on event timestamps

- **Workflow Orchestration**:

  - Temporal workflows for long-running processes
  - Configurable retry policies and timeouts
  - Saga pattern for distributed transactions
  - Signal-based user interaction for pausing and resuming workflows
  - Activity-based step execution with independent retry policies
  - State tracking in database for workflow progress

- **Feature Deployment**:

  - A/B testing integration
  - Feature flags through whitelist configurations
  - Gradual rollout strategy for new features

- **Background Jobs**:
  - Scheduled tasks for data synchronization
  - Batch processing for statements and other data
  - Monitoring jobs for system health

## Event-Driven Architecture

- **Event Structure**:

  - Standardized event format defined in proto files
  - Event metadata for tracking and debugging
  - Typed payloads for different event types
  - Version field for schema evolution

- **Event Processing**:

  - Event validation against schema
  - Storage in dedicated event tables
  - Processing by specialized consumers
  - Error handling with retry mechanisms
  - Dead letter queues for unprocessable events

- **Event Storage**:

  - Long-term storage for audit and compliance
  - Indexing for efficient querying
  - Archiving strategy for historical events

- **Event Consistency**:
  - At-least-once delivery semantics
  - Idempotent processing for safety
  - Transactional boundaries for state changes
  - Conflict resolution strategies
