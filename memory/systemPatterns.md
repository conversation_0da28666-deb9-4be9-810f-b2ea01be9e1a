# System Patterns

## Architecture Overview

The Installment Service follows a domain-driven design approach with clear separation of concerns across multiple layers. The system is organized into core domains (account, management, onboarding) with each domain having its own set of adapters, models, repositories, and services.

## Project Structure

The project follows a standardized structure with clearly defined responsibilities:

```
/                                  -- Root of the project; contains global config files, entry point (main.go), and build scripts.
├── api/                           -- API definitions and generated code.
│   ├── account_service/           -- Protobuf and API files for the Account service.
│   │   ├── external/
│   │   │   └── v1/               -- Externally exposed API definitions for account payment.
│   │   └── v1/                  -- Internal API definitions for account operations.
│   ├── external_services/         -- API definitions for external integrations.
│   │   ├── ab_platform/          -- AB platform integration APIs.
│   │   ├── ekyc/                 -- Electronic Know Your Customer integration.
│   │   ├── payment/              -- Payment service integration.
│   │   ├── user_profile/         -- User profile service integration.
│   │   └── zp_base/              -- ZaloPay base services integration.
│   ├── management_service/        -- Definitions for management-related APIs.
│   │   ├── installment/          -- Installment-specific API definitions.
│   │   ├── statement/            -- Statement-specific API definitions.
│   │   └── v1/                   -- Core management service API definitions.
│   └── onboarding_service/        -- API definitions for onboarding processes.
│       └── v1/                   -- Onboarding service API versions.
├── config/                        -- Environment and configuration files (YAML, proto, etc.).
├── internal/                      -- Core application logic divided by domain.
│   ├── account/                   -- Account domain contains business logic and integrations.
│   │   ├── adapters/              -- External RPC integrations and third-party interaction layer.
│   │   ├── cmd/                   -- Service entry points and CLI commands.
│   │   ├── mocks/                 -- Mock implementations for testing account functionality.
│   │   ├── model/                 -- Domain entities and business models.
│   │   ├── repo/                  -- Persistence layer for account data access.
│   │   ├── server/                -- Transport layer: gRPC, HTTP, Kafka servers for account.
│   │   ├── service/               -- Application layer to orchestrate interactions and business flows.
│   │   ├── usecase/               -- Business logic and orchestrated flows (account creation, syncing, etc.).
│   │   └── utils/                 -- Helper functions and utilities.
│   ├── management/                -- Management domain handles statements, fees, installments, and monitoring.
│   ├── onboarding/                -- Onboarding domain handles new account onboarding, verification, and profile creation.
│   └── zombie/                    -- Ancillary legacy or background services (e.g., maintenance or cleanup tasks).
├── memory-bank/                   -- Detailed documentation files tracking project context and progress.
├── pkg/                           -- Shared packages that can be reused across domains.
│   ├── bootstrap/               -- gRPC or other transport connection bootstrapping utilities.
│   ├── codec/                   -- Custom codec implementations.
│   ├── errorkit/                -- Custom error definitions and handling helpers.
│   ├── errorkts/                -- Integration with error handling from kratos.
│   ├── kafkara/                 -- Kafka client utilities and tracing support.
│   ├── keygen/                  -- Key generation utilities, including mocks.
│   ├── maintenance/             -- Maintenance routines and models.
│   ├── metrics/                 -- Metrics and monitoring support (OpenTelemetry, Prometheus).
│   ├── middleware/              -- Middleware components for authentication, logging, and maintenance.
│   ├── mocking/                 -- Mock generation utilities.
│   ├── mocks/                   -- Mock implementations for shared packages.
│   ├── parallel/                -- Utilities for parallel processing.
│   ├── partner/                 -- Partner integration utilities.
│   ├── ratelimit/               -- Rate limiting implementations.
│   ├── telemetry/               -- Telemetry and observability utilities.
│   ├── uberfx/                  -- Uber FX integration helpers.
│   ├── zlog/                    -- Logging utilities and initialization.
│   └── zutils/                  -- General utility functions for various operations.
├── resources/                     -- Static and resource files.
├── third_party/                   -- Externally maintained code (shared proto, libraries, etc.).
├── tools/                         -- Scripts and utilities (e.g., mock generation, linting).
└── vendor/                        -- Vendored Go module dependencies.
```

### Key Structural Patterns

1. **Domain-driven organization**: Core business logic is separated by domain (`account`, `management`, `onboarding`) with each domain having a consistent internal structure.
2. **Layered architecture**: Each domain follows a clear layering of responsibilities:
   - `adapters`: External service, third parties integrations
   - `model`: Domain entities and data structures
   - `repo`: Data persistence layer
   - `server`: Transport layer (HTTP/gRPC)
   - `service`: Application services
   - `usecase`: Business logic implementations
   - `usecase/interface`: Interface define for DI
3. **Shared utilities**: Common functionality across domains is placed in the `pkg` directory to promote reuse and maintain DRY principles.
4. **Clear API definitions**: Protobuf API definitions are organized by service and version.
5. **Resource isolation**: Configuration, migrations, and static resources are kept separate from code.

## Design Patterns

### Event-Driven Integration

The combined onboarding feature implements an event-driven architecture pattern where:

1. **Event Producer**: The paylater product service produces onboarding events
2. **Event Consumer**: The installment service consumes and processes these events
3. **Event Store**: The `onboard_events` table serves as a persistent event store
4. **Event Processor**: Business logic that combines data from multiple event sources

### Workflow Pattern with User Interaction

The automatic onboarding flow implements a workflow pattern with support for user interaction:

1. **Workflow Engine**: Uses Temporal for workflow orchestration
2. **Signal-based Interaction**: Pauses workflow when user input is needed and continues automatically after receiving signals
3. **State Management**: Tracks interaction state in the database with interact_required and interact_reason fields
4. **Activity-based Steps**: Breaks down the onboarding process into discrete activities that can be retried independently

### Event Schema

Events follow a structured format defined in `binding_event.proto`:

- Core event metadata (event_id, version, timestamp)
- Source information (from_source with ProductCode enum, partner_code with PartnerCode enum)
- Structured payload with domain-specific data

### Repository Pattern

Data access follows the repository pattern:

- Repository interfaces defined in usecase/interface package
- Concrete implementations in the repo package
- Strong separation between data access and business logic
- Transaction management at the repository level

### Adapter Pattern

External integrations use the adapter pattern:

- Adapters encapsulate third-party service interactions
- Domain models are transformed to/from external service models
- Error handling and retries managed at adapter level

## Component Relationships

The system follows a clear component hierarchy:

1. **API Layer**: Defines service interfaces and data contracts
2. **Server Layer**: Implements API endpoints and connection handling
3. **Usecase Layer**: Implements business logic and orchestrates operations
4. **Repository Layer**: Handles data access and persistence
5. **Adapter Layer**: Integrates with external services and dependencies

Each domain (account, management, onboarding) maintains this layered approach for better separation of concerns.

## Data Flow

### Combined Onboarding Data Flow

1. Paylater service completes a step in its onboarding process
2. Paylater service publishes an onboarding event with relevant user data
3. Installment service receives and validates the event
4. Event is persisted to the `onboard_events` table
5. Onboarding service processes the event and extracts relevant data
6. Data is merged with existing onboarding information if any
7. Updated combined onboarding status is made available via API

### Event Processing Flow

1. Event listener receives event from message broker
2. Event is validated against schema defined in binding_event.proto
3. Repository layer stores event in onboard_events table
4. Service layer determines if event requires processing
5. For actionable events, relevant data is extracted and processed
6. Any state changes are persisted to appropriate tables
7. Notifications or callbacks may be triggered based on event type

## Critical Implementation Paths

### Onboarding Event Processing

1. **Event Reception**: Events from paylater received via Kafka or similar message broker
2. **Event Validation**: Validate event structure and content
3. **Event Storage**: Persist raw event to onboard_events table
4. **Event Processing**: Extract and process relevant information
5. **Data Merging**: Combine data with existing user onboarding information
6. **Status Update**: Update user's combined onboarding status

### Data Consistency Management

1. **Event Idempotency**: Use event_id and from_source to ensure events are processed exactly once
2. **Event Ordering**: Use event_time to process events in correct sequence
3. **Conflict Resolution**: Apply business rules to resolve conflicts between data sources
4. **Transaction Boundaries**: Ensure ACID properties for critical operations

## API Structure

The API structure follows a service-oriented approach with:

1. Core services:

   - Account Service: Managing account operations and data
   - Management Service: Handling installments, statements, and fees
   - Onboarding Service: Managing the user onboarding process

2. API versioning:

   - Each service maintains versioned APIs (v1)
   - External vs. internal API separation for appropriate access control

3. External service integrations:
   - EKYC (Electronic Know Your Customer)
   - Payment services
   - User profile services
   - AB platform
   - ZaloPay base services

## Error Handling Strategy

### Event Processing Errors

1. **Validation Errors**: Events that fail validation are logged and potentially sent to a dead letter queue
2. **Processing Errors**: Errors during processing are caught, logged, and may trigger retries
3. **Data Inconsistency Errors**: Conflicting data triggers alerts and may require manual intervention
4. **System Errors**: Infrastructure or dependency failures follow standard retry policies

## State Management

### Event-Based State

1. **Event Sourcing**: System state can be recreated from the sequence of events
2. **Materialized Views**: Current state is stored in optimized tables for query performance
3. **State Transitions**: Clearly defined state machine for onboarding process with transitions triggered by events

## Security Patterns

### Data Protection

1. **Sensitive Data Handling**: PII in events is encrypted or tokenized
2. **Access Control**: Event processors have minimal required permissions
3. **Audit Trail**: All event processing actions are logged for auditability

## Performance Considerations

### Event Processing Performance

1. **Batch Processing**: Group events for efficient processing where possible
2. **Indexing Strategy**: Optimize database indices for event queries
3. **Asynchronous Processing**: Non-critical processing done asynchronously
4. **Caching**: Cache frequently accessed reference data
5. **Query Optimization**: JSON column queries are optimized with specific path expressions

## Protobuf Message Design Patterns

The project follows several best practices for Protocol Buffer message design:

### Composition Over Duplication

Messages are designed to favor composition over duplication:

1. **Component Messages**: Common field groups are extracted into reusable component messages

   - `InstallmentPeriod`: Groups tenure, paid_tenure, start_date, end_date
   - `InstallmentAmounts`: Groups financial fields like principal_amount, outstanding_principal_amount, etc.

2. **Base and Detail Messages**: Messages are structured in layers

   - `InstallmentBase`: Contains core identification and reference fields
   - Component messages: Contain grouped related fields
   - Detail messages: Compose base and component messages for specific API needs

3. **Field Reuse Strategy**: For critical high-access fields (like tenure, disburse_amount):
   - Primary definition lives in the appropriate component message
   - May be duplicated in base messages when direct access is critical
   - Duplicated fields are marked as deprecated to encourage use of components

### Message Evolution

1. **Backward Compatibility**:

   - Field numbers are never reused
   - New fields are added as optional
   - Deprecated fields are marked but maintained for compatibility

2. **Field Numbering**:

   - Sequential numbering for base fields
   - Reserve space for future fields in each logical group
   - Reserved numbers for removed fields

3. **Naming Conventions**:
   - Consistent terminology across messages (e.g., outstanding_principal_amount vs remaining_principal_amount)
   - Clear distinction between similar concepts through consistent prefixes
   - json_name tags used consistently for API stability

### API Response Design

1. **Targeted Response Messages**:

   - Each API endpoint has a specific response message
   - Responses compose the appropriate base and component messages
   - List responses include pagination metadata

2. **Data Optimization**:
   - List APIs return minimal required fields
   - Detail APIs provide comprehensive information
   - Optional fields used for data that is expensive to compute
