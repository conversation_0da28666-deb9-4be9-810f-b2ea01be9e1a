# Project Progress

## Completed Work

- Created database schema for `onboard_events` table to store external product onboarding events
  - Designed with flexibility to handle varying event formats from multiple product services
  - Implemented using a combination of structured columns for consistent data and JSON for variable payloads
- Defined optimal indices for efficient querying with composite keys (zalopay_id, partner_code, from_source)
  - Enables fast lookups by user ID and product source
  - Supports efficient filtering by partner code for reporting
- Set up JSON columns for flexible payload data storage from different event sources
  - Allows for evolution of event schemas without requiring database migrations
  - Maintains backward compatibility while enabling new event formats
- Created binding_event.proto with clear enum definitions and field descriptions
  - Defined ProductCode enum with values including PAYLATER and INSTALLMENT
  - Added PartnerCode enum for consistent partner identification
  - Included detailed comments for all fields to ensure clear understanding across teams
- Optimized database schema based on query patterns and access requirements
  - Added created_at, updated_at timestamps for audit purposes
  - Included event_time field to properly sequence events from different sources
  - Designed schema to support idempotent event processing
- Implemented automatic onboarding flow for installment service
  - Added functionality to consume binding events from other lending products (paylater, cashloan)
  - Integrated support for user interaction when needed during the automatic flow
  - Added database schema changes for interact_required and interact_reason fields
- Refactored workflow implementation to follow clean architecture principles
  - Moved workflow code from internal/onboarding/workflow/ to internal/onboarding/service/workflow/
  - Renamed automatic_onboarding_usecase.go to automatic_onboarding.go for consistency
  - Removed redundant files like entity/binding.go and da/binding.go
- Integrated automatic onboarding into the existing onboarding service
  - Added automatic onboarding endpoints to the existing onboarding service proto
  - Implemented the endpoints in the onboarding service
  - Avoided creating a separate automatic onboarding service
- Fixed interface issues in UserProfileService
  - Restored missing methods that were defined in the mock implementation
  - Added GetKycNfcData, GetKycNfcStatus, GetKycProgStatus, ResetKycNfcData, and VerifyKycNfcBCA methods
- Reorganized Proto Messages for ListClientInstallments API
  - Refactored InstallmentData into reusable components (InstallmentPeriod and InstallmentAmounts)
  - Applied composition pattern by using InstallmentBase, InstallmentPeriod, and InstallmentAmounts in ListInstallmentItem
  - Ensured proper naming consistency for amounts (using outstanding_principal_amount)
  - Improved field organization for better reusability across different API endpoints
  - Fixed field number inconsistency in InstallmentAmounts message

## Work in Progress

- Implementing repository methods for onboard_events CRUD operations
  - Creating interfaces in usecase/interface package
  - Building implementation in repo package with proper transaction handling
  - Developing query methods with optimized SQL for common access patterns
  - Adding support for batch operations to improve performance
- Developing event listener adapter for paylater service integration
  - Setting up Kafka consumer configuration
  - Implementing message deserialization and validation
  - Building retry logic for failed message processing
  - Creating monitoring hooks for observability
- Building service layer logic to process and merge onboarding data
  - Defining rules for combining data from multiple sources
  - Implementing conflict resolution strategies
  - Creating state management for the combined onboarding process
  - Building transformation logic to convert events to domain models
- Creating proto message definitions for cross-service communication
  - Defining message structures for paylater event data
  - Adding validation rules for required fields
  - Implementing versioning strategy for backward compatibility
  - Including clear documentation for all message fields

## Pending Work

- Creating unit and integration tests for the combined onboarding feature
  - Unit tests for repository layer methods
  - Integration tests for event processing flow
  - Mock implementations for external dependencies
  - Performance tests for event processing under load
- Adding API endpoints or updating existing ones to expose combined onboarding status
  - New endpoint for retrieving combined status
  - Updates to existing endpoints to include cross-product information
  - Documentation for API changes
  - Client SDK updates if needed
- Implementing error handling and retry mechanisms for failed event processing
  - Dead letter queue for unprocessable events
  - Monitoring and alerting for processing failures
  - Automatic retry policies for transient errors
  - Manual intervention workflow for persistent failures
- Setting up monitoring and alerting for event processing pipeline
  - Metrics for event processing latency and throughput
  - Alerts for processing backlogs or failures
  - Dashboards for monitoring system health
  - Logging for debugging and auditing
- Coordinating with paylater team on event schema and delivery mechanism
  - Finalizing event format and required fields
  - Agreeing on delivery guarantees and retry policies
  - Establishing schema evolution process
  - Defining SLAs for event delivery and processing
- Developing conflict resolution strategies for data from multiple sources
  - Business rules for handling conflicting information
  - Timestamp-based resolution for simultaneous updates
  - Prioritization framework for different data sources
  - Escalation path for unresolvable conflicts

## Known Issues

- Need to determine proper data reconciliation strategy when events contain conflicting information
  - Events from different sources may contain contradictory data
  - Temporal ordering may not be sufficient for all conflict types
  - Some conflicts may require business-specific resolution rules
- Potential performance impact of JSON field queries needs to be evaluated
  - Complex JSON path queries may be less efficient than structured columns
  - Need to benchmark query performance with realistic data volumes
  - May require denormalization of frequently accessed JSON fields
- Race conditions possible when processing events from multiple sources
  - Events may arrive out of order
  - Need to implement proper sequencing based on event timestamps
  - Concurrent updates to same user record require careful transaction management
- Backward compatibility with existing onboarding flows must be maintained
  - New event processing must not disrupt existing functionality
  - UI components may need updates to display combined status
  - Database schema changes must preserve existing query patterns

## Deployment Plan

- Deploy database migration for onboard_events table
  - Run migration during low-traffic period
  - Verify indices are created correctly
  - Confirm no impact to existing tables
  - Set up monitoring for new table growth and query performance
- Release event listener adapter with feature flag
  - Deploy with feature disabled in production
  - Enable for internal testing
  - Gradually roll out to percentage of traffic
  - Monitor for errors or performance issues
- Roll out combined onboarding logic incrementally
  - Start with read-only integration
  - Add write capabilities with careful validation
  - Enable cross-product data sharing with proper permissions
  - Roll back plan ready in case of issues
- Monitor event processing performance and adjust as needed
  - Track processing latency and throughput
  - Monitor database query performance
  - Adjust batch sizes or concurrency as needed
  - Scale infrastructure if required
- Implement proper logging and observability
  - Set up structured logging for all event processing
  - Create dashboards for monitoring system health
  - Establish alerts for processing failures or backlogs
  - Document troubleshooting procedures

## Dependencies

- Paylater service team needs to finalize event structure and delivery mechanism
  - Agreement on event schema and versioning
  - Confirmation of delivery guarantees
  - Testing of end-to-end flow
  - Documentation of event semantics
- Event bus or message queue infrastructure for reliable event delivery
  - Kafka cluster with sufficient capacity
  - Proper topic configuration for retention and partitioning
  - Monitoring and alerting for message delivery
  - Disaster recovery procedures
- Database with JSON support for flexible payload storage
  - MySQL with proper JSON column support
  - Optimized for JSON path queries
  - Sufficient storage capacity for event volume
  - Backup and recovery procedures
- Consistent event schema and versioning approach across teams
  - Shared understanding of event semantics
  - Process for schema evolution
  - Backward compatibility requirements
  - Documentation of field meanings and valid values
