# Product Context

## Problem Statement

The installment service needs to support a combined onboarding flow that integrates with multiple product services, including the newly added paylater product service.

## User Needs

- Users want a seamless onboarding experience across multiple financial products
- Users expect to provide their information only once for multiple financial products
- Users need clear visibility into their onboarding status for each product

## Solution Overview

The installment service acts as a central onboarding hub, collecting and processing user data for multiple financial products through:

- Direct user input via our onboarding flow
- Event-based integration with other product services like paylater
- Unified data storage and status tracking across products

## User Experience Goals

- Minimize duplicate data entry across product onboarding
- Provide clear feedback on onboarding progress across multiple products
- Streamline verification steps by sharing verified information between products

## Business Value

- Reduced user friction leads to higher conversion rates
- Shared verification processes reduce operational costs
- Cross-selling opportunities increase through integrated onboarding
- Consolidated user data creates better risk assessment

## Market Context

- Competitive financial service platforms offer unified onboarding experiences
- Users expect minimal friction when using multiple products from the same provider
- Regulatory requirements necessitate proper user verification across all products

## Key Features

- Combined onboarding data management
- Event-based onboarding integration with other products
- Unified verification status tracking
- Cross-product data sharing with proper permissions
- Automatic onboarding with user interaction support
- Workflow-based onboarding process that can pause and resume

## User Personas

- New users looking to access multiple financial products
- Existing users expanding their product portfolio
- Internal operations team managing user onboarding

## User Journeys

- User starts onboarding for installment service and is offered additional product options
- User completes onboarding for one product and is pre-approved for others
- External product events trigger updates to user's onboarding status in the installment service
- User receives notification that interaction is needed for automatic onboarding
- User provides updated information or documents to continue automatic onboarding
- System automatically continues processing after user interaction
