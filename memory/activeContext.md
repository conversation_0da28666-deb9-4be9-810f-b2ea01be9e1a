# Active Context

## Current Work Focus

- Implementing combined onboarding feature to listen for and process paylater product service events
- Developing repository methods for the onboard_events table
- Building event listener adapter for paylater service integration
- Implementing service layer logic to process and merge onboarding data
- Creating proper error handling for the event processing pipeline

## Recent Changes

- Created new SQL table `onboard_events` to store event data from external product services
- Optimized database indices with composite keys (zalopay_id, partner_code, from_source)
- Defined ProductCode and PartnerCode enums in the binding_event.proto file
- Added detailed field descriptions to the binding_event.proto file
- Implemented schema with JSON fields to handle flexible payload data coming from different sources
- Started implementing repository methods for CRUD operations on the onboard_events table
- Implemented automatic onboarding flow for installment service
- Added database schema changes for interact_required and interact_reason fields
- Refactored workflow implementation to follow clean architecture principles
- Moved workflow code from internal/onboarding/workflow/ to internal/onboarding/service/workflow/
- Integrated automatic onboarding into the existing onboarding service
- Fixed interface issues in UserProfileService by restoring missing methods
- Decided to create onboarding records with 'draft' status in binding event handler
- Designed workflow to activate records from 'draft' to 'active' in first activity
- Implemented error handling to ensure onboarding records exist even when validation fails
- Extended ProfileUsecase with RegisterAutomaticOnboarding method for user interactions

## Next Steps

- Complete repository layer implementation for onboard_events table
- Finalize adapter implementation to listen for paylater service events
- Implement service layer logic for merging onboarding data from multiple sources
- Add unit and integration tests for the combined onboarding feature
- Update API endpoints to expose combined onboarding status
- Develop conflict resolution strategies for handling data from multiple sources
- Implement 'draft' status for automatic onboarding records
- Create ActivateOnboardingActivity as first activity in workflow
- Extend ProfileUsecase with RegisterAutomaticOnboarding method
- Update RegisterClientOnboarding to detect and route based on onboarding mode
- Implement wait-list feature using the same binding event infrastructure

## Active Decisions

- Using a dedicated table (onboard_events) rather than extending the existing bindings table
- Storing nested payload data in JSON columns for flexibility across different event sources
- Creating composite indices to optimize queries by user ID and partner information
- Using enums for ProductCode and PartnerCode to ensure consistent values across the system
- Implementing event idempotency using event_id and from_source to prevent duplicate processing
- Using event_time for proper event ordering and processing sequence
- Creating onboarding records in binding event handler rather than in workflow
- Adding 'draft' status for newly created automatic onboarding records
- Activating records in first workflow activity or when user interacts
- Reusing existing usecase methods where possible for automatic onboarding
- Extending ProfileUsecase instead of creating separate automatic onboarding endpoints

## Current Challenges

- Ensuring data consistency across multiple event sources
- Handling race conditions when processing events from different product services
- Determining the correct sequence and priority of events from different sources
- Maintaining backward compatibility with existing onboarding flows
- Optimizing JSON field queries for performance
- Implementing proper error handling and retry mechanisms
- Handling validation failures in automatic onboarding without user intervention
- Ensuring onboarding records exist in database even when validation checks fail
- Managing workflow state for automatic onboarding with user interactions
- Implementing wait-list feature that triggers automatic onboarding when approved

## Important Patterns and Preferences

- Following repository pattern for data access with clear separation from business logic
- Using JSON for flexible payload storage while maintaining strongly typed processing in Go
- Implementing proper error handling and transaction management
- Following existing onboarding domain patterns for consistency
- Using event-driven architecture for loose coupling between product services
- Implementing asynchronous processing for non-critical operations

## Recent Learnings

- JSON columns in MySQL provide good flexibility for handling variable event payloads
- Composite indices are important for query performance on tables with multiple filtering criteria
- Event-based integration allows for looser coupling between product services
- Enums in protobuf provide type safety and clear documentation for inter-service communication
- Proper event ordering is critical for maintaining data consistency
- Transaction boundaries need careful planning in event-driven systems

## Current Priorities

- Complete the onboard_events table implementation and data model
- Develop the event listening mechanism for paylater events
- Implement the business logic to combine onboarding data from multiple sources
- Add tests to verify combined onboarding functionality
- Establish monitoring and alerting for the event processing pipeline
- Document the combined onboarding approach for the team

## Questions and Uncertainties

- What is the expected volume of events from paylater service?
- Are there specific fields from paylater events that require special handling?
- What is the error handling strategy for failed event processing?
- How to handle conflicting information from different event sources?
- What performance metrics should be monitored for the event processing pipeline?
- How to ensure backward compatibility with existing onboarding flows?

## Team Context

- Coordination required with paylater team to align on event format and delivery mechanism
- Need to document the new combined onboarding approach for the team
- Working with infrastructure team to ensure proper event delivery infrastructure
- Collaborating with QA team to develop comprehensive test scenarios
