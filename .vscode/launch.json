{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch onboarding",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}",
      "args": ["onboarding", "--config", "./config/local.yaml"]
    },
    {
      "name": "Launch onboarding worker",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}",
      "args": ["onboarding", "worker", "--config", "./config/local.yaml"]
    },
    {
      "name": "Launch account",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}",
      "args": ["account", "--config", "./config/local.yaml"]
    },
    {
      "name": "Launch management",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}",
      "args": ["management", "--config", "./config/local.yaml"]
    }
  ]
}
