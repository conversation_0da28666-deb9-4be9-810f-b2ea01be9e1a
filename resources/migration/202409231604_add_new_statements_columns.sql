alter table statements
add column penalty_amount bigint not null default 0
comment 'Accumulated penalty amount' after incurred_date;

alter table statements
add column outstanding_balance bigint generated always
as (GREATEST(0, outstanding_amount + penalty_amount - outstanding_repaid)) stored
comment 'Outstanding balance calculated from penalty, repaid, and outstanding amount' after outstanding_repaid;

alter table statements
add column metadata json not null default (_utf8mb4'{}') after outstanding_balance;