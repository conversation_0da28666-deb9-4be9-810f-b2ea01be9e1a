CREATE TABLE `bindings`
(
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `zalopay_id` BIGINT NOT NULL,
    `account_id` BIGINT,
    `partner_code` VARCHAR(50) NOT NULL,
    `full_name` VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    `phone_number` VA<PERSON>HAR(20) NOT NULL,
    `id_number` VARCHAR(64) NOT NULL,
    `id_issued_date` VARCHAR(15),
    `id_issued_location` VARCHAR(255),
    `ic_images` JSON NOT NULL,
    `permanent_address` VARCHAR(1024),
    `temp_residence_address` VARCHAR(1024),
    `birthday` VARCHAR(15),
    `gender` VARCHAR(10),
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `current_step` VARCHAR(32) NOT NULL,
    `reject_code` VARCHAR(64) NOT NULL,
    `extra_profile` JSON NOT NULL DEFAULT '{}',
    `partner_data` JSON NOT NULL DEFAULT '{}',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zalopay_id` (`zalopay_id`),
    KEY `idx_zalopay_id_partner_code_status` (`zalopay_id`, `partner_code`, `status`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

-- name: CreateOnboarding :execresult
INSERT INTO bindings (
  zalopay_id,
  partner_code, full_name,
  phone_number, id_number,
  id_issued_date, id_issued_location,
  ic_images, permanent_address,
  temp_residence_address, birthday, gender,
  status, current_step, reject_code)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: UpdateBasicOnboarding :exec
UPDATE bindings
SET `full_name` = ?,
    `phone_number` = ?,
    `id_number` = ?,
    `gender` = ?,
    `current_step` = ?,
    `reject_code` = ?
WHERE id = ?;

-- name: ListOnboardingByZalopayID :many
SELECT *
FROM bindings
WHERE zalopay_id = ?;

-- name: GetActiveOnboarding :one
SELECT *
FROM bindings
WHERE zalopay_id = ? AND partner_code = ? AND status = 'active';

-- name: ListActiveOnboarding :many
SELECT *
FROM bindings
WHERE zalopay_id = ? AND status = 'active';

-- name: UpdateOnboarding :exec
UPDATE bindings
SET `full_name` = ?,
    `phone_number` = ?,
    `id_number` = ?,
    `id_issued_date` = ?,
    `id_issued_location` = ?,
    `ic_images` = ?,
    `permanent_address` = ?,
    `temp_residence_address` = ?,
    `birthday` = ?,
    `gender` = ?,
    `current_step` = ?,
    `reject_code` = ?,
    `extra_profile` = ?,
    `partner_data` = ?,
    `updated_at`=CURRENT_TIMESTAMP
WHERE id = ?;

-- name: UpdateStep :exec
UPDATE bindings
SET `current_step` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: UpdateStepAndRejectCode :exec
UPDATE bindings
SET `current_step` = ?,
    `reject_code` = ?
WHERE id = ?;

-- name: UpdateStepAndExtraProfile :exec
UPDATE bindings
SET `current_step` = ?,
    `extra_profile` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: GetOnboardingByIDAndZalopayID :one
SELECT *
FROM bindings
WHERE id = ? AND zalopay_id = ?;

-- name: GetOnboardingByIDAndZalopayIDForUpdate :one
SELECT *
FROM bindings
WHERE id = ? AND zalopay_id = ? FOR UPDATE;

-- name: ListActiveOnboardingByZalopayIDs :many
SELECT *
FROM bindings
WHERE zalopay_id IN (sqlc.slice('zalopay_ids')) and status = 'active';

-- name: UpdatePartnerData :exec
UPDATE bindings
SET `partner_data` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: UpdateExtraProfile :exec
UPDATE bindings
SET `extra_profile` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: UpdateApprovalData :exec
UPDATE bindings
SET `current_step` = ?,
    `partner_data` = ?,
    `reject_code` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: UpdateStepAndPartnerData :exec
UPDATE bindings
SET `current_step` = ?,
    `partner_data` = ?
WHERE id = ?;

-- name: UpdateAccountID :exec
UPDATE bindings
SET `account_id` = ?
WHERE id = ? AND zalopay_id = ?;

-- name: UpdateStatus :exec
UPDATE bindings
SET `status` = ?
WHERE id = ? AND zalopay_id = ?;