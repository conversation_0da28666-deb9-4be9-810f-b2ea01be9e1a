CREATE TABLE `accounts` (
   `id`                        BIGINT NOT NULL AUTO_INCREMENT,
   `zalopay_id`                BIGINT NOT NULL COMMENT 'ZaloPay user unique identifier',
   `partner_code`              VARCHAR(50) NOT NULL COMMENT 'Partner code in installment system',
   `partner_account_name`      VARCHAR(255) NOT NULL COMMENT 'Installment partner account name',
   `partner_account_number`    VARCHAR(255) NOT NULL COMMENT 'Installment partner account identifier',
   `installment_limit`         BIGINT NOT NULL COMMENT 'The maximum installment account balance approved by the partner',
   `installment_balance`       BIGINT NOT NULL COMMENT 'Installment account available balance',
   `repayment_balance`         BIGINT COMMENT 'Installment account repayment balance, optional by partner',
   `status`                    ENUM ('active', 'inactive', 'closed', 'blocked') NOT NULL COMMENT 'Installment account status',
   `expiry_date`               DATETIME,
   `origin`                    VARCHAR(50) NOT NULL COMMENT 'Source that installment account was created from',
   `created_at`                DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at`                DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_account` (`zalopay_id`, `partner_code`, `partner_account_number`),
   KEY `idx_zalopay_id_partner_id_status` (`zalopay_id`, `partner_code`, `status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: CreateAccount :execresult
INSERT INTO accounts (
    id, zalopay_id, partner_code,
    partner_account_name, partner_account_number,
    installment_limit, installment_balance,
    status, expiry_date, origin
) VALUES (uuid_short(), ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetAccountByID :one
SELECT * FROM accounts
WHERE id = ?;

-- name: GetAccountByIDForUpdate :one
SELECT * FROM accounts
WHERE id = ? FOR UPDATE;

-- name: GetAccountByIDAndZalopayID :one
SELECT * FROM accounts
WHERE zalopay_id = ? AND id = ?;

-- name: GetActiveAccountByPartnerCode :one
SELECT * FROM accounts
WHERE zalopay_id = ?
  AND partner_code = ?
  AND status = 'active';

-- name: ListAccountByPartnerCode :many
SELECT * FROM accounts
WHERE partner_code = ?
  AND id > sqlc.arg(id_gt)
LIMIT ?;

-- name: GetLatestAccountByPartnerCode :one
SELECT * FROM accounts
WHERE zalopay_id = ?
  AND partner_code = ?
ORDER BY id DESC LIMIT 1;

-- name: ListAccountForStmtSync :many
SELECT * FROM accounts
WHERE id > sqlc.arg(id_gt)
  AND partner_code IN (sqlc.slice('partner_codes'))
  AND status IN ('active', 'blocked')
  AND created_at <= sqlc.arg(created_at_lte)
LIMIT ?;

-- name: ListAccountByZalopayIDs :many
SELECT * FROM accounts
WHERE zalopay_id IN (sqlc.slice('zalopay_ids'));

-- name: UpdateAccountBalance :exec
UPDATE accounts
SET installment_limit = ?,
    installment_balance = ?,
    repayment_balance = ?
WHERE id = ?;

-- name: UpdateAccountBalanceAndStatus :exec
UPDATE accounts
SET status = ?,
    installment_limit = ?,
    installment_balance = ?,
    repayment_balance = ?
WHERE id = ?;

-- name: ListDebtAccountByPartnerCode :many
SELECT * FROM accounts
WHERE partner_code = ?
  AND installment_balance < installment_limit
  AND id > sqlc.arg(id_gt)
LIMIT ?;