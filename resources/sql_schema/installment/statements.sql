CREATE TABLE `statements` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'The statement id',
  `period` INT NOT NULL COMMENT 'Statement period format [YYYYMM] example 202101',
  `zalopay_id` BIGINT NOT NULL COMMENT 'Zalopay user id',
  `account_id` BIGINT NOT NULL COMMENT 'User account id',
  `partner_code` VARCHAR(255) NOT NULL COMMENT 'The partner code',
  `due_date` DATE NOT NULL COMMENT 'The repayment grace end date',
  `incurred_date` DATE NOT NULL COMMENT 'Statement incurred date',
  `penalty_amount` BIGINT NOT NULL DEFAULT 0 COMMENT 'Accumulated penalty amount',
  `outstanding_amount` BIGINT NOT NULL COMMENT 'Statement outstanding amount',
  `outstanding_repaid` BIGINT NOT NULL COMMENT 'Statement outstanding repaid',
  `outstanding_balance` BIGINT GENERATED ALWAYS AS (GREATEST(0, outstanding_amount + penalty_amount - outstanding_repaid))
      NOT NULL COMMENT 'Outstanding balance calculated from penalty, repaid, and outstanding amount',
  `metadata` JSON NOT NULL DEFAULT '{}' COMMENT 'The statement metadata',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE uk_account_id_period_stmt_date (`account_id`, `period`, `incurred_date`),
  INDEX `idx_zalopay_account_period` (`zalopay_id`, `account_id`, `period`),
  INDEX `idx_zalopay_account_incurred_date` (`zalopay_id`, `account_id`, `incurred_date`),
  INDEX `idx_partner_incurred_id` (`partner_code`, `incurred_date`, `id`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

-- name: UpsertStatement :execresult
INSERT INTO statements (
    period, zalopay_id, account_id,
    partner_code, due_date, incurred_date,
    outstanding_amount, outstanding_repaid
) VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    outstanding_amount = VALUES(outstanding_amount),
    outstanding_repaid = VALUES(outstanding_repaid);

-- name: GetStatementByID :one
SELECT * FROM statements
WHERE id = ?;

-- name: GetUserStatementByIncurredDate :one
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date = DATE(sqlc.arg(incurred_date));

-- name: ListUserStatementByPeriod :many
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?;

-- name: GetUserLatestStatement :one
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
ORDER BY period DESC LIMIT 1;

-- name: GetUserLatestStatementByPeriod :one
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?
ORDER BY period DESC LIMIT 1;

-- name: GetUserStatementByPeriodAndIncurredDate :one
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?
  AND incurred_date = ?;

-- name: ListUserStatementByIncurredDateRange :many
SELECT * FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date >= DATE(sqlc.arg(from_incurred_date))
  AND incurred_date <= DATE(sqlc.arg(to_incurred_date));

-- name: UpdateStatementOutstandingByIncurredDate :exec
UPDATE statements
SET outstanding_amount = ?,
    outstanding_repaid = ?
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date = DATE(sqlc.arg(incurred_date));

-- name: UpdateStatementOutstandingAndPenaltyByID :exec
UPDATE statements
set penalty_amount = ?,
    outstanding_amount = ?,
    outstanding_repaid = ?,
    metadata = ?
WHERE id = ?;

-- name: GetStatementDueDateByIncurredDateAndPartner :one
SELECT due_date FROM statements
WHERE partner_code = ? AND incurred_date = ?;

-- name: ListStatementByIncurredDateAndPartner :many
SELECT * FROM statements
WHERE partner_code = ?
  AND incurred_date = DATE(sqlc.arg(incurred_date))
  AND id > ? LIMIT ?;

-- name: ListOutstandingStatementByIncurredDateAndPartner :many
SELECT * FROM statements
WHERE partner_code = ?
  AND incurred_date = DATE(sqlc.arg(incurred_date))
  AND outstanding_balance > 0
  AND id > ? LIMIT ?;





