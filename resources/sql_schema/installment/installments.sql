CREATE TABLE `installments` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'The incremental id',
    `tenure` INT NOT NULL COMMENT 'Number of the installment',
    `status` ENUM ('init', 'open', 'closed') NOT NULL DEFAULT 'init' COMMENT 'The status of the installment package',
    `zalopay_id` BIGINT NOT NULL COMMENT 'User zalopay id',
    `account_id` BIGINT NOT NULL COMMENT 'User account id',
    `zp_trans_id` BIGINT NOT NULL COMMENT 'Ref to the zalopay transaction id',
    `partner_code` VARCHAR(50) NOT NULL COMMENT 'Partner code',
    `partner_inst_id` VARCHAR(255) COMMENT 'Ref to the partner installment id',
    `paid_tenure` INT COMMENT 'The number of paid tenure',
    `emi_amount` BIGINT COMMENT 'The equated monthly installment amount (provisional or actual)',
    `interest_rate` FLOAT COMMENT 'The proportion of an installment that is charged (provisional or actual)',
    `interest_amount` BIGINT COMMENT 'The interest amount calculated by annual interest rate (provisional or actual)',
    `disburse_amount` BIGINT COMMENT 'The approved disbursed loan amount',
    `total_fee_amount` BIGINT COMMENT 'Total fees of the installment',
    `total_due_amount` BIGINT COMMENT 'Total amount including disbursed amount, interest amount, and fees (provisional)',
    `refund_info` JSON NULL COMMENT 'The object include some refund information',
    `transaction_info` JSON DEFAULT '{}' COMMENT 'The object include some transaction information',
    `outstanding_info` JSON DEFAULT '{}' COMMENT 'The object include some outstanding information',
    `fee_details` JSON DEFAULT '{}' COMMENT 'The object or array object include all fee information',
    `early_discharge` JSON DEFAULT '{}' COMMENT 'The object include some early discharge information',
    `discharge_status` VARCHAR(30) NOT NULL DEFAULT 'pending' COMMENT 'The latest snapshot discharge state',
    `start_date` DATETIME COMMENT 'Installment start DATE',
    `end_date` DATETIME COMMENT 'Installment end DATE',
    `curr_due_date` DATE COMMENT 'Curr repayment due date',
    `next_due_date` DATE COMMENT 'Next repayment due date',
    `days_past_due` INT NOT NULL DEFAULT 0 COMMENT 'The number of days past due',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE uk_zp_trans_id_partner_inst_id (`zp_trans_id`, `partner_inst_id`),
    INDEX idx_partner_inst_id (`partner_inst_id`),
    INDEX idx_zalopay_id_zp_trans_id (`zalopay_id`, `zp_trans_id`),
    INDEX idx_zalopay_id_account_id (`zalopay_id`, `account_id`, `zp_trans_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: InsertInstallment :execresult
INSERT INTO installments (
    `tenure`, `status`, `zalopay_id`, `account_id`,
    `zp_trans_id`, `partner_code`, `partner_inst_id`,
    `emi_amount`, `disburse_amount`, `interest_rate`,
    `interest_amount`,`total_fee_amount`, `total_due_amount`,
    `transaction_info`, `outstanding_info`, `fee_details`,
    `paid_tenure`, `start_date`, `end_date`, `curr_due_date`,
    `next_due_date`, `days_past_due`, `early_discharge`
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetInstallmentByID :one
SELECT * FROM installments
WHERE `id` = ?;

-- name: GetInstallmentByIDForUpdate :one
SELECT * FROM installments
WHERE `id` = ? FOR UPDATE;

-- name: GetInstallmentByZPTransID :one
SELECT * FROM installments
WHERE `zp_trans_id` = ?;

-- name: GetInstallmentByZalopayIDAndZPTransID :one
SELECT * FROM installments
WHERE zalopay_id = ? AND `zp_trans_id` = ?;

-- name: ListInstallmentByPartnerRefIDs :many
SELECT * FROM installments
WHERE `partner_inst_id` IS NOT NULL
AND `partner_inst_id` IN (sqlc.slice('partner_inst_ids'));

-- name: ListOpenInstallmentByZalopayIDAndAcctID :many
SELECT * FROM installments
WHERE `zalopay_id` = ? AND `account_id` = ? AND `status` = 'open';

-- name: ListInstallmentPeriodicSync :many
SELECT * FROM installments
WHERE `status` IN ('init', 'open') AND `created_at` <= ? LIMIT ? OFFSET ?;

-- name: UpdateInstallment :exec
UPDATE installments
SET `status` = ?, `paid_tenure` = ?, `emi_amount` = ?, `interest_rate` = ?, `interest_amount` = ?,
    `disburse_amount` = ?, `total_fee_amount` = ?, `total_due_amount` = ?, `transaction_info` = ?,
    `outstanding_info` = ?, `fee_details` = ?, `start_date` = ?, `end_date` = ?, `curr_due_date` = ?,
    `next_due_date` = ?, `days_past_due` = ?, `early_discharge` = ?, `discharge_status` = ?
WHERE `id` = ?;

-- name: UpdateInstallmentAfterSync :exec
-- Force updated_at to NOW() because we want to track the last sync time instead data not changed
UPDATE installments
SET `status` = ?, `paid_tenure` = ?, `outstanding_info` = ?,
    `start_date` = ?, `end_date` = ?, `curr_due_date` = ?,
    `next_due_date` = ?, `days_past_due` = ?, `early_discharge` = ?,
    `discharge_status` = ?, `updated_at` = CURRENT_TIMESTAMP
WHERE `id` = ?;

-- name: UpdateEarlyDischargeInfo :exec
UPDATE installments
SET early_discharge = ?,
    discharge_status = ?
WHERE `id` = ?;

-- name: UpdateInstallmentTransaction :exec
UPDATE installments
SET `transaction_info` = ?
WHERE `id` = ?;

-- name: UpdateInstallmentRefund :exec
UPDATE installments
SET `refund_info` = ?
WHERE `id` = ?;

-- name: UpdateDischargeStatus :exec
UPDATE installments
SET `discharge_status` = ?
WHERE `id` = ?;