CREATE TABLE `scheduled_repayments` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'The repayment id',
    `seq_no` INT NOT NULL COMMENT 'Installment sequence number',
    `inst_id` BIGINT NOT NULL COMMENT 'Ref to the installment id',
    `partner_inst_id` VARCHAR(255) NOT NULL COMMENT 'Ref to the partner installment id',
    `status` ENUM ('init', 'due', 'paid') NOT NULL DEFAULT 'init' COMMENT 'Status of this period repayment',
    `due_date` DATE COMMENT 'Due date of this installment period',
    `grace_due_date` DATE COMMENT 'Grace due date of this installment period',
    `emi_amount` BIGINT COMMENT 'Period emi amount',
    `interest_amount` BIGINT COMMENT 'Period interest amount',
    `principal_amount` BIGINT COMMENT 'Period outstanding principal amount',
    `penalty_details` JSON COMMENT 'Penalty info include total, paid, outstanding',
    `outstanding_details` JSON COMMENT 'Outstanding info include principal, interest, total',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE uk_installment_id_seq_no (`inst_id`, `seq_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

-- name: InsertRepaymentSchedule :exec
INSERT INTO scheduled_repayments (
    `seq_no`, `inst_id`, `partner_inst_id`, `status`, `due_date`, `grace_due_date`,
    `emi_amount`, `interest_amount`, `principal_amount`,
    `penalty_details`, `outstanding_details`
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetRepaymentScheduleByInstID :many
SELECT * FROM scheduled_repayments
WHERE `inst_id` = ?;

-- name: ListRepaymentScheduleByInstIDs :many
SELECT * FROM scheduled_repayments
WHERE `inst_id` IN (sqlc.slice('inst_ids'));

-- name: UpdateRepaymentScheduleAfterSync :exec
-- Force updated_at to NOW() because we want to track the last sync time instead data not changed
UPDATE scheduled_repayments
SET `status` = ?, `due_date` = ?, `grace_due_date` = ?,
    `penalty_details` = ?, `outstanding_details` = ?,
    `updated_at` = CURRENT_TIMESTAMP
WHERE `inst_id` = ? AND `seq_no` = ?;
