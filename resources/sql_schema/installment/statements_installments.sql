CREATE TABLE `statements_installments` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'The statement installment id',
  `statement_id` BIGINT NOT NULL COMMENT 'The statement id',
  `statement_date` DATE NOT NULL COMMENT 'Statement date',
  `zalopay_id` BIGINT NOT NULL COMMENT 'Zalopay user id',
  `account_id` BIGINT NOT NULL COMMENT 'User account id',
  `partner_code` VARCHAR(255) NOT NULL COMMENT 'The partner code',
  `partner_inst_id` VARCHAR(255) NOT NULL COMMENT 'Reference installment id of partner',
  `due_date` DATE COMMENT 'The installment due date',
  `installment_amount` BIGINT NOT NULL COMMENT 'The installment amount',
  `outstanding_details` JSON DEFAULT '{}' COMMENT 'The installment outstanding info',
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON>EY (`id`),
  <PERSON><PERSON><PERSON><PERSON> KEY (`statement_id`, `partner_inst_id`),
  <PERSON><PERSON><PERSON> `idx_zalopay_account_statement_id` (`zalopay_id`, `account_id`, `statement_id`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

/**
* Note: Currently we still store some field like zalopay_id, account_id, partner_code in this table
* to make it easier to query and ensure the data is belong to user account.
* Maybe we must take care of the consistency and normalization in the future.
*/

-- name: UpsertStmtInstallment :exec
INSERT INTO statements_installments (
    `statement_id`, `statement_date`, `partner_inst_id`,
    `zalopay_id`, `account_id`, `partner_code`,
    `due_date`, `installment_amount`, `outstanding_details`
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    `due_date` = VALUES(due_date),
    `installment_amount` = VALUES(installment_amount),
    `outstanding_details` = VALUES(outstanding_details);

-- name: ListStmtInstallmentByStatementId :many
SELECT * FROM statements_installments
WHERE `statement_id` = ?;

-- name: ListStmtInstallmentByClientAndStmtDate :many
SELECT * FROM statements_installments
WHERE `zalopay_id` = ? AND `account_id` = ? AND `statement_date` = ?;

-- name: ListStmtInstallmentByUserAndStmtId :many
SELECT * FROM statements_installments
WHERE `zalopay_id` = ? AND `account_id` = ? AND `statement_id` = ?;

-- name: ListStmtInstallmentByStmtId :many
SELECT * FROM statements_installments
WHERE `statement_id` = ?;