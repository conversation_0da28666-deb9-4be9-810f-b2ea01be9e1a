CREATE TABLE `statements_syncer` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'The statement syncer id',
    `statement_date` DATE COMMENT 'Statement incurred date',
    `statement_period` INT NOT NULL COMMENT 'Statement period format [YYYYMM] example 202101',
    `partner_code` VARCHAR(50) NOT NULL COMMENT 'The partner code of the statement',
    `batch_no` INT COMMENT 'The batch number of the sync process, optional',
    `status` ENUM ('PENDING', 'RUNNING', 'FAILED', 'SUCCESS') NOT NULL DEFAULT 'PENDING' COMMENT 'The status of the sync process',
    `exec_id` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'The execution id of the sync process',
    `sync_stats` JSON NOT NULL DEFAULT '{}' COMMENT 'The sync statistics of the statement',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON>AR<PERSON> KEY (`id`),
    <PERSON>IQUE KEY `uk_period_partner_exec` (`statement_period`, `partner_code`, `exec_id`)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8mb4
COLLATE = utf8mb4_unicode_ci;

-- name: InsertSyncer :execresult
INSERT INTO statements_syncer (
    `statement_period`, `statement_date`, `partner_code`, `batch_no`, `status`, `exec_id`, `sync_stats`
) VALUES (?, ?, ?, ?, ?, ?, ?);

-- name: UpsertSyncer :execresult
INSERT INTO statements_syncer (
   `statement_period`, `statement_date`, `partner_code`, batch_no, `status`, `exec_id`, `sync_stats`
) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    `status` = VALUES(`status`),
    `sync_stats` = VALUES(`sync_stats`);

-- name: UpdateSyncerStatus :exec
UPDATE statements_syncer SET
     `status` = ?
WHERE `id` = ? OR `exec_id` = ?;

-- name: UpdateSyncerStatusAndStats :exec
UPDATE statements_syncer SET
    `status` = ?,
    `sync_stats` = ?
WHERE `id` = ? OR `exec_id` = ?;

-- name: GetSyncerBatchByPeriodAndPartner :many
SELECT * FROM statements_syncer
WHERE `batch_no` IS NOT NULL
  AND `partner_code` = ?
  AND `statement_period` = ?
  AND `statement_date` = ?;