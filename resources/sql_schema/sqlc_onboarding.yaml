version: "2"
sql:
  - engine: "mysql"
    queries: "onboarding"
    schema: "onboarding"
    gen:
      go:
        package: "da"
        out: "../../internal/onboarding/repo/da"
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: true
        emit_json_tags: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: true
        output_files_suffix: _generated
        output_db_file_name: db_generated
        overrides:
          - column: "partners.code"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner.PartnerCode"
            nullable: false
#          - column: "bindings.current_step"
#            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/entity.BindingStep"
#            nullable: false
          - column: "bindings.ic_images"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/entity.ICImages"
            nullable: true
