version: "2"
sql:
  - engine: "mysql"
    queries: "installment"
    schema: "installment"
    gen:
      go:
        package: "da"
        out: "../../internal/management/repo/da"
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: true
        emit_json_tags: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: true
        output_files_suffix: _generated
        output_db_file_name: db_generated
        overrides:
          - column: "statements.metadata"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.StatementMetadata"
            nullable: false
          - column: "statements_installments.outstanding_details"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.StatementInstallmentOuts"
            nullable: false
          - column: "installments.fee_details"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.JsonRawMessage"
            nullable: true
          - column: "installments.outstanding_info"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.JsonRawMessage"
            nullable: true
          - column: "installments.refund_info"
            go_type: 
              import: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
              type: "InstallmentRefund"
              pointer: true
            nullable: true
          - column: "installments.early_discharge"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.JsonRawMessage"
            nullable: true
          - column: "installments.provisional_info"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.InstallmentProvisional"
            nullable: false
          - column: "installments.transaction_info"
            go_type: "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity.InstallmentTransaction"
            nullable: false

