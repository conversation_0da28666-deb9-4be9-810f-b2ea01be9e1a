version: "2"
sql:
  - engine: "mysql"
    queries: "account"
    schema: "account"
    gen:
      go:
        package: "da"
        out: "../../internal/account/repo/da"
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: true
        emit_json_tags: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: true
        output_files_suffix: _generated
        output_db_file_name: db_generated
        overrides: