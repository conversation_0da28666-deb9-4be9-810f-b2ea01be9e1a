# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /account/v1/account:
        get:
            tags:
                - Account
            operationId: Account_GetClientAccount
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
                - name: partner_code
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/account_service.v1.GetClientAccountResponse'
    /account/v1/content/proposed-services:
        get:
            tags:
                - Account
            operationId: Account_ListClientProposedService
            parameters:
                - name: partnerCode
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/account_service.v1.ListClientProposedServiceResponse'
    /installment/v1/early-discharge:
        get:
            tags:
                - Installment
            operationId: Installment_GetClientEarlyDischarge
            parameters:
                - name: zpTransId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.installment.v1.GetClientEarlyDischargeResponse'
    /installment/v1/info:
        get:
            tags:
                - Installment
            description: Installment
            operationId: Installment_GetClientInstallment
            parameters:
                - name: zp_trans_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.installment.v1.GetClientInstallmentResponse'
    /installment/v1/plans/acceptance:
        post:
            tags:
                - Installment
            operationId: Installment_AcceptClientPlan
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/management_service.installment.v1.AcceptClientPlanRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.installment.v1.AcceptClientPlanResponse'
    /installment/v1/plans/detail:
        get:
            tags:
                - Installment
            operationId: Installment_GetClientPlanDetail
            parameters:
                - name: plan_key
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.installment.v1.GetClientPlanDetailResponse'
    /installment/v1/plans/eligible:
        get:
            tags:
                - Installment
            description: PLan
            operationId: Installment_ListClientEligiblePlans
            parameters:
                - name: partner_code
                  in: query
                  schema:
                    type: string
                - name: app_id
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: app_trans_id
                  in: query
                  schema:
                    type: string
                - name: charge_amount
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.installment.v1.ListClientEligiblePlansResponse'
    /onboarding/v1/application:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_SubmitClientApplication
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.SubmitClientApplicationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.SubmitClientApplicationResponse'
    /onboarding/v1/contract:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientContract
            parameters:
                - name: onboarding_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientContractResponse'
    /onboarding/v1/face-challenge:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_SubmitClientFaceChallenge
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.SubmitClientFaceChallengeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.SubmitClientFaceChallengeResponse'
    /onboarding/v1/link-account:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_LinkingClientAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.LinkingClientAccountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.LinkingClientAccountResponse'
    /onboarding/v1/onboardings:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_ListClientOnboarding
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.ListClientOnboardingResponse'
    /onboarding/v1/onboardings/{onboarding_id}:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientOnboarding
            parameters:
                - name: onboarding_id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientOnboardingResponse'
    /onboarding/v1/register:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_RegisterClientOnboarding
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.RegisterClientOnboardingRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.RegisterClientOnboardingResponse'
    /onboarding/v1/reinitiate:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_ReinitClientOnboarding
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.ReinitClientOnboardingRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.ReinitClientOnboardingResponse'
    /onboarding/v1/rejection:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientRejection
            parameters:
                - name: onboarding_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientRejectionResponse'
    /onboarding/v1/request-otp:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_RequestClientOTP
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.RequestClientOTPRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.RequestClientOTPResponse'
    /onboarding/v1/resources:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientResources
            parameters:
                - name: resource_types
                  in: query
                  schema:
                    type: array
                    items:
                        enum:
                            - NATURE_OF_BUSINESS
                            - OCCUPATION
                            - JOB_TITLE
                            - CITY
                            - DISTRICT
                            - WARD
                            - GENDER
                            - SOURCE_OF_FUND
                            - MARITAL_STATUS
                            - EMPLOYMENT_STATUS
                            - GENERAL_TERM_AND_CONDITION
                            - COMPANY_TYPE
                            - LOAN_TERM_AND_CONDITION
                            - OPENING_CASA_PURPOSE
                            - NAPAS_BANK
                            - REFERENCE_CONTACT_TYPE
                            - CUSTOMER_TYPE
                            - EDUCATION
                            - LOAN_CANCEL_REASON
                            - INCOME
                            - FUND_PURPOSE
                        type: string
                        format: enum
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientResourcesResponse'
    /onboarding/v1/users/ekyc-nfc/reset:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_ResetClientEkycNfc
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.ResetClientEkycNfcRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.ResetClientEkycNfcResponse'
    /onboarding/v1/users/ekyc-profile:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientEkycProfile
            parameters:
                - name: need_verify_profile
                  in: query
                  schema:
                    type: boolean
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientEkycProfileResponse'
    /onboarding/v1/users/permissions:
        get:
            tags:
                - Onboarding
            operationId: Onboarding_GetClientPermissions
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.GetClientPermissionsResponse'
    /onboarding/v1/verify-otp:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_VerifyClientOTP
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.VerifyClientOTPRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.VerifyClientOTPResponse'
    /onboarding/v1/wet-sign:
        post:
            tags:
                - Onboarding
            operationId: Onboarding_SubmitClientWetSign
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/onboarding_service.v1.SubmitClientWetSignRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/onboarding_service.v1.SubmitClientWetSignResponse'
    /statement/v1/info:
        get:
            tags:
                - Statement
            operationId: Statement_GetClientStatement
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
                - name: statement_id
                  in: query
                  schema:
                    type: string
                - name: incurred_date
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.statement.v1.GetClientStatementResponse'
    /statement/v1/installments:
        get:
            tags:
                - Statement
            description: ListClientInstallments returns a list of installments for a given statement.
            operationId: Statement_ListClientInstallments
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
                - name: statement_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.statement.v1.ListClientInstallmentsResponse'
    /statement/v1/latest:
        get:
            tags:
                - Statement
            operationId: Statement_GetClientLatestStatement
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.statement.v1.GetClientLatestStatementResponse'
    /statement/v1/list:
        get:
            tags:
                - Statement
            operationId: Statement_ListClientStatements
            parameters:
                - name: account_id
                  in: query
                  schema:
                    type: string
                - name: from_time
                  in: query
                  schema:
                    type: string
                    format: date-time
                - name: to_time
                  in: query
                  schema:
                    type: string
                    format: date-time
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/management_service.statement.v1.ListClientStatementsResponse'
components:
    schemas:
        account_service.v1.ContentImage:
            type: object
            properties:
                icon_url:
                    type: string
                cover_url:
                    type: string
                thumbnail_url:
                    type: string
        account_service.v1.ContentInteraction:
            type: object
            properties:
                zpa_url:
                    type: string
                    description: string type = 1;
                zpi_url:
                    type: string
        account_service.v1.GetClientAccountResponse:
            type: object
            properties:
                account:
                    $ref: '#/components/schemas/account_service.v1.UserAccount'
        account_service.v1.InstallmentTerm:
            type: object
            properties:
                fee_explanation:
                    type: string
                    description: Installment fee explanation
                stmt_due_date_text:
                    type: string
                    description: Statement due date description
                stmt_incur_date_text:
                    type: string
                    description: Statement incurred date description
        account_service.v1.ListClientProposedServiceResponse:
            type: object
            properties:
                services:
                    type: array
                    items:
                        $ref: '#/components/schemas/account_service.v1.ServiceProposed'
        account_service.v1.ServiceProposed:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                content_image:
                    $ref: '#/components/schemas/account_service.v1.ContentImage'
                interaction:
                    $ref: '#/components/schemas/account_service.v1.ContentInteraction'
        account_service.v1.UserAccount:
            type: object
            properties:
                account_id:
                    type: string
                status:
                    enum:
                        - UNSPECIFIED
                        - ACTIVE
                        - INACTIVE
                        - BLOCKED
                        - CLOSED
                    type: string
                    format: enum
                source:
                    type: string
                partner_code:
                    type: string
                partner_account_name:
                    type: string
                partner_account_number:
                    type: string
                installment_limit:
                    type: string
                installment_balance:
                    type: string
                installment_term:
                    $ref: '#/components/schemas/account_service.v1.InstallmentTerm'
                created_at:
                    type: string
                    format: date-time
                repayment_balance:
                    type: string
        google.protobuf.Any:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        management_service.installment.v1.AcceptClientPlanRequest:
            type: object
            properties:
                plan_key:
                    type: string
                app_id:
                    type: integer
                    format: int32
                app_trans_id:
                    type: string
        management_service.installment.v1.AcceptClientPlanResponse:
            type: object
            properties:
                plan_key:
                    type: string
        management_service.installment.v1.BasePlan:
            type: object
            properties:
                plan_key:
                    type: string
                    description: Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
                tenor_number:
                    type: string
                    description: Tenor unit is the unit of the tenor, it can be day, month, year
                emi_amount:
                    type: string
                    description: Emi amount is the amount of each installment
                total_amount:
                    type: string
                    description: Total amount is the total amount of the plan
                principal_amount:
                    type: string
                    description: Principal amount is the principal amount of the plan
                status:
                    enum:
                        - PLAN_STATUS_UNSPECIFIED
                        - PLAN_STATUS_ACTIVE
                        - PLAN_STATUS_INACTIVE
                        - PLAN_STATUS_INVALID
                    type: string
                    description: Plan status
                    format: enum
        management_service.installment.v1.CostInfo:
            type: object
            properties:
                total_cost_amount:
                    type: string
                    description: Total cost amount is the total amount of the plan
                total_fee_amount:
                    type: string
                    description: Total fee amount is the total amount of all fees
                platform_fee_amount:
                    type: string
                    description: Platform fee amount is the fee amount of the platform
                conversion_fee_amount:
                    type: string
                    description: Conversion fee amount is the fee amount of the conversion
                interest_amount:
                    type: string
                    description: Interest amount is the interest amount of the plan
                list_fee_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.installment.v1.Fee'
                    description: List of fee info
                fee_explanation:
                    type: string
                    description: Fee explanation
        management_service.installment.v1.EarlyDischarge:
            type: object
            properties:
                info:
                    $ref: '#/components/schemas/management_service.installment.v1.EarlyDischargeBase'
                detail:
                    $ref: '#/components/schemas/management_service.installment.v1.EarlyDischargeDetail'
        management_service.installment.v1.EarlyDischargeBase:
            type: object
            properties:
                kind:
                    enum:
                        - EARLY_DISCHARGE_KIND_UNSPECIFIED
                        - EARLY_DISCHARGE_KIND_NORMAL
                        - EARLY_DISCHARGE_KIND_REFUND
                    type: string
                    format: enum
                status:
                    enum:
                        - EARLY_DISCHARGE_STATUS_UNSPECIFIED
                        - EARLY_DISCHARGE_STATUS_ELIGIBLE
                        - EARLY_DISCHARGE_STATUS_INELIGIBLE
                        - EARLY_DISCHARGE_STATUS_PROCESSING
                        - EARLY_DISCHARGE_STATUS_CLOSED
                    type: string
                    format: enum
                allowed:
                    type: boolean
                in_session:
                    type: boolean
                session_info:
                    $ref: '#/components/schemas/management_service.installment.v1.Session'
        management_service.installment.v1.EarlyDischargeDetail:
            type: object
            properties:
                total_discharge_amount:
                    type: string
                early_discharge_amount:
                    type: string
                early_discharge_fee:
                    type: string
                total_outstanding_amount:
                    type: string
                outstanding_principal_amount:
                    type: string
                outstanding_interest_amount:
                    type: string
                outstanding_penalty_amount:
                    type: string
        management_service.installment.v1.Fee:
            type: object
            properties:
                feeType:
                    enum:
                        - FEE_TYPE_INVALID
                        - FEE_TYPE_CONVERSION
                        - FEE_TYPE_PLATFORM
                    type: string
                    format: enum
                feeAmount:
                    type: string
                message:
                    type: string
                messageEn:
                    type: string
        management_service.installment.v1.GetClientEarlyDischargeResponse:
            type: object
            properties:
                early_discharge:
                    $ref: '#/components/schemas/management_service.installment.v1.EarlyDischarge'
        management_service.installment.v1.GetClientInstallmentResponse:
            type: object
            properties:
                installment:
                    $ref: '#/components/schemas/management_service.installment.v1.InstallmentData'
                early_discharge:
                    $ref: '#/components/schemas/management_service.installment.v1.EarlyDischarge'
                repayment_schedules:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.installment.v1.RepaymentSchedule'
        management_service.installment.v1.GetClientPlanDetailResponse:
            type: object
            properties:
                plan_info:
                    allOf:
                        - $ref: '#/components/schemas/management_service.installment.v1.BasePlan'
                    description: Plan info includes general information of the plan
                cost_info:
                    allOf:
                        - $ref: '#/components/schemas/management_service.installment.v1.CostInfo'
                    description: Cost info of the plan includes total of each fee amount and fee info url
                plan_detail_url:
                    type: string
                    description: Plan detail url
        management_service.installment.v1.InstallmentData:
            type: object
            properties:
                id:
                    type: string
                tenure:
                    type: integer
                    format: int32
                status:
                    enum:
                        - INSTALLMENT_STATUS_UNSPECIFIED
                        - INSTALLMENT_STATUS_INIT
                        - INSTALLMENT_STATUS_OPEN
                        - INSTALLMENT_STATUS_CLOSED
                    type: string
                    format: enum
                principal_amount:
                    type: string
                interest_amount:
                    type: string
                penalty_amount:
                    type: string
                total_amount_due:
                    type: string
                total_paid_amount:
                    type: string
                total_remaining_amount:
                    type: string
        management_service.installment.v1.ListClientEligiblePlansResponse:
            type: object
            properties:
                plan_options:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.installment.v1.ListClientEligiblePlansResponse_Plan'
                    description: List of plans
                plan_selected:
                    type: string
                    description: Plan key selected
        management_service.installment.v1.ListClientEligiblePlansResponse_Plan:
            type: object
            properties:
                plan_key:
                    type: string
                    description: Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
                plan_info:
                    allOf:
                        - $ref: '#/components/schemas/management_service.installment.v1.BasePlan'
                    description: Plan info includes general information of the plan
                cost_info:
                    allOf:
                        - $ref: '#/components/schemas/management_service.installment.v1.CostInfo'
                    description: Cost info of the plan includes total of each fee amount and fee info url
                is_popular:
                    type: boolean
                    description: Is highlight plan
                plan_detail_url:
                    type: string
                    description: Plan detail url
                scheduled_repayments:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.installment.v1.PlanRepaymentSchedule'
                    description: List of scheduled repayment
        management_service.installment.v1.PlanRepaymentSchedule:
            type: object
            properties:
                amount:
                    type: string
                due_date:
                    type: string
                installment_number:
                    type: integer
                    format: int32
        management_service.installment.v1.RepaymentSchedule:
            type: object
            properties:
                seq_no:
                    type: integer
                    format: int32
                status:
                    enum:
                        - REPAY_STATUS_UNSPECIFIED
                        - REPAY_STATUS_PENDING
                        - REPAY_STATUS_DUE
                        - REPAY_STATUS_PAID
                        - REPAY_STATUS_OVERDUE
                    type: string
                    format: enum
                due_date:
                    type: string
                due_amount:
                    type: string
                penalty_amount:
                    type: string
                total_due_amount:
                    type: string
                total_paid_amount:
                    type: string
                total_remaining_amount:
                    type: string
        management_service.installment.v1.Session:
            type: object
            properties:
                start_time:
                    type: string
                    description: Start time in 24-hour format "HH:MM"
                end_time:
                    type: string
                    description: End time in 24-hour format "HH:MM"
            description: Session defines the operational time window when early discharge is available
        management_service.statement.v1.GetClientLatestStatementResponse:
            type: object
            properties:
                statement:
                    $ref: '#/components/schemas/management_service.statement.v1.StatementData'
        management_service.statement.v1.GetClientStatementResponse:
            type: object
            properties:
                statement:
                    $ref: '#/components/schemas/management_service.statement.v1.StatementData'
        management_service.statement.v1.ListClientInstallmentsResponse:
            type: object
            properties:
                installments:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.statement.v1.StatementInstallment'
        management_service.statement.v1.ListClientStatementsResponse:
            type: object
            properties:
                statements:
                    type: array
                    items:
                        $ref: '#/components/schemas/management_service.statement.v1.StatementItem'
        management_service.statement.v1.StatementData:
            type: object
            properties:
                id:
                    type: string
                    description: Unique identifier for the statement.
                account_id:
                    type: string
                    description: Identifier for the user account.
                due_date:
                    type: string
                    description: The repayment grace end date.
                incurred_date:
                    type: string
                    description: The date the statement was incurred.
                paid_status:
                    enum:
                        - PAID_STATUS_UNSPECIFIED
                        - PAID_STATUS_PAID
                        - PAID_STATUS_UNPAID
                        - PAID_STATUS_PARTIALLY_PAID
                    type: string
                    description: The paid status of the statement.
                    format: enum
                outstanding_amount:
                    type: string
                    description: Snapshot of the outstanding amount at the statement date.
                outstanding_repaid:
                    type: string
                    description: Amount that has been repaid towards the outstanding amount.
                installment_fee_amount:
                    type: string
                    description: Snapshot of the total fees of the installment at the statement date.
                installment_fee_repaid:
                    type: string
                    description: Total fees repaid.
                total_due_amount:
                    type: string
                    description: The total amount due, combining outstanding amount and installment fee amount.
                total_due_repaid:
                    type: string
                    description: The total amount repaid, combining outstanding repaid and installment fee repaid.
                total_due_remaining:
                    type: string
                    description: The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
                penalty_amount:
                    type: string
                    description: The penalty amount when the statement is overdue.
            description: StatementData represents a statement detail for a given account.
        management_service.statement.v1.StatementInstallment:
            type: object
            properties:
                id:
                    type: string
                statement_id:
                    type: string
                zp_trans_id:
                    type: string
                installment_id:
                    type: string
                transaction_desc:
                    type: string
                outstanding_amount:
                    type: string
                outstanding_details:
                    $ref: '#/components/schemas/management_service.statement.v1.StatementInstallment_OutstandingDetails'
        management_service.statement.v1.StatementInstallment_OutstandingDetails:
            type: object
            properties:
                total_due_amount:
                    type: string
                total_overdue_amount:
                    type: string
                total_penalty_amount:
                    type: string
        management_service.statement.v1.StatementItem:
            type: object
            properties:
                id:
                    type: string
                    description: Unique identifier for the statement.
                account_id:
                    type: string
                    description: Identifier for the user account.
                due_date:
                    type: string
                    description: The repayment grace end date.
                incurred_date:
                    type: string
                    description: The date the statement was incurred.
                paid_status:
                    enum:
                        - PAID_STATUS_UNSPECIFIED
                        - PAID_STATUS_PAID
                        - PAID_STATUS_UNPAID
                        - PAID_STATUS_PARTIALLY_PAID
                    type: string
                    description: The paid status of the statement.
                    format: enum
                total_due_amount:
                    type: string
                    description: The total amount due, combining outstanding amount and installment fee amount.
                total_due_repaid:
                    type: string
                    description: The total amount repaid, combining outstanding repaid and installment fee repaid.
                total_due_remaining:
                    type: string
                    description: The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
            description: StatementItem represents item in statement list, it have less info than statement data.
        onboarding_service.v1.Action:
            type: object
            properties:
                code:
                    type: string
                title:
                    type: string
                variant:
                    type: string
                zpa_action_url:
                    type: string
                zpi_action_url:
                    type: string
                metadata:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/google.protobuf.Any'
        onboarding_service.v1.BindInfo:
            type: object
            properties:
                onboarding_id:
                    type: string
                    description: Binding onboarding id
                account_id:
                    type: string
                    description: Binding account id
                partner_code:
                    type: string
                    description: Binding partner code
        onboarding_service.v1.CIMBResource:
            type: object
            properties:
                type:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.CIMBResourceData'
        onboarding_service.v1.CIMBResourceData:
            type: object
            properties:
                code:
                    type: string
                vietnamese:
                    type: string
                    description: Data description in vietnamese
                english:
                    type: string
                    description: Data description in english
        onboarding_service.v1.Content:
            type: object
            properties:
                title:
                    type: string
                message:
                    type: string
        onboarding_service.v1.GetClientContractResponse:
            type: object
            properties:
                unsigned_contract_url:
                    type: string
                signed_contract_url:
                    type: string
        onboarding_service.v1.GetClientEkycProfileResponse:
            type: object
            properties:
                profile_info:
                    $ref: '#/components/schemas/onboarding_service.v1.UserProfile'
                profile_issues:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.ProfileIssue'
        onboarding_service.v1.GetClientOnboardingResponse:
            type: object
            properties:
                status:
                    type: string
                partner_code:
                    type: string
                current_step:
                    type: string
                full_name:
                    type: string
                gender:
                    type: string
                phone_number:
                    type: string
                id_number:
                    type: string
                id_issue_date:
                    type: string
                id_issue_place:
                    type: string
                date_of_birth:
                    type: string
                permanent_address:
                    type: string
                temp_residence_address:
                    type: string
        onboarding_service.v1.GetClientPermissionsResponse:
            type: object
            properties:
                is_whitelisted:
                    type: boolean
                bind_status:
                    enum:
                        - BIND_STATUS_UNKNOWN
                        - BIND_STATUS_UNBOUND
                        - BIND_STATUS_BOUND
                        - BIND_STATUS_ONBOARDING
                    type: string
                    format: enum
                kyc_status:
                    $ref: '#/components/schemas/onboarding_service.v1.KycStatusData'
                risk_info:
                    $ref: '#/components/schemas/onboarding_service.v1.RiskInfo'
                bind_info:
                    allOf:
                        - $ref: '#/components/schemas/onboarding_service.v1.BindInfo'
                    description: |-
                        *
                         Binding info include some general info about user account and onboarding
                         If user not onboarded or not bind account, this field will be empty
        onboarding_service.v1.GetClientRejectionResponse:
            type: object
            properties:
                code:
                    type: string
                content:
                    $ref: '#/components/schemas/onboarding_service.v1.Content'
                actions:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.Action'
        onboarding_service.v1.GetClientResourcesResponse:
            type: object
            properties:
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.CIMBResource'
        onboarding_service.v1.KycStatusData:
            type: object
            properties:
                status:
                    enum:
                        - KYC_STATUS_INVALID
                        - KYC_STATUS_PROCESSING
                        - KYC_STATUS_APPROVE
                        - KYC_STATUS_REJECT
                        - KYC_STATUS_BYPASS
                        - KYC_STATUS_UNKNOWN
                    type: string
                    format: enum
                notice:
                    $ref: '#/components/schemas/onboarding_service.v1.Notice'
        onboarding_service.v1.LinkingClientAccountRequest:
            type: object
            properties:
                onboarding_id:
                    type: string
        onboarding_service.v1.LinkingClientAccountResponse:
            type: object
            properties: {}
        onboarding_service.v1.ListClientOnboardingResponse:
            type: object
            properties:
                onboardings:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.ListClientOnboardingResponse_Onboarding'
        onboarding_service.v1.ListClientOnboardingResponse_Onboarding:
            type: object
            properties:
                id:
                    type: string
                partner_code:
                    type: string
                status:
                    type: string
                current_step:
                    type: string
                created_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time
                all_step:
                    type: array
                    items:
                        type: string
                next_step:
                    type: string
        onboarding_service.v1.Notice:
            type: object
            properties:
                content:
                    $ref: '#/components/schemas/onboarding_service.v1.Content'
                actions:
                    type: array
                    items:
                        $ref: '#/components/schemas/onboarding_service.v1.Action'
        onboarding_service.v1.ProfileIssue:
            type: object
            properties:
                code:
                    type: string
                notice:
                    $ref: '#/components/schemas/onboarding_service.v1.Notice'
        onboarding_service.v1.RegisterClientOnboardingRequest:
            type: object
            properties:
                partner_code:
                    type: string
        onboarding_service.v1.RegisterClientOnboardingResponse:
            type: object
            properties:
                onboarding_id:
                    type: string
        onboarding_service.v1.ReinitClientOnboardingRequest:
            type: object
            properties:
                onboarding_id:
                    type: string
        onboarding_service.v1.ReinitClientOnboardingResponse:
            type: object
            properties: {}
        onboarding_service.v1.RequestClientOTPRequest:
            type: object
            properties:
                otp_type:
                    enum:
                        - CONTRACT_SIGNING
                        - LINK_TYPE_3
                    type: string
                    format: enum
                onboarding_id:
                    type: string
                partner_code:
                    type: string
        onboarding_service.v1.RequestClientOTPResponse:
            type: object
            properties:
                wait_time:
                    type: string
                resend_time:
                    type: string
                status:
                    type: boolean
                error_message:
                    type: string
                phone_number:
                    type: string
                otp_request_id:
                    type: string
                is_send_sms:
                    type: boolean
        onboarding_service.v1.ResetClientEkycNfcRequest:
            type: object
            properties: {}
        onboarding_service.v1.ResetClientEkycNfcResponse:
            type: object
            properties: {}
        onboarding_service.v1.RiskInfo:
            type: object
            properties:
                code:
                    type: string
                level:
                    type: string
                notice:
                    $ref: '#/components/schemas/onboarding_service.v1.Notice'
        onboarding_service.v1.SubmitClientApplicationRequest:
            type: object
            properties:
                onboarding_id:
                    type: string
                job_title:
                    type: string
                occupation:
                    type: string
                living_city:
                    type: string
                monthly_income:
                    type: string
                temp_residence_address:
                    type: string
                education:
                    type: string
                source_of_fund:
                    type: string
                employment_status:
                    type: string
        onboarding_service.v1.SubmitClientApplicationResponse:
            type: object
            properties:
                application_rejected:
                    type: boolean
                require_link_account:
                    type: boolean
        onboarding_service.v1.SubmitClientFaceChallengeRequest:
            type: object
            properties:
                onboarding_id:
                    type: string
                face_request_id:
                    type: string
        onboarding_service.v1.SubmitClientFaceChallengeResponse:
            type: object
            properties:
                onboarding_id:
                    type: string
        onboarding_service.v1.SubmitClientWetSignRequest:
            type: object
            properties:
                onboarding_id:
                    type: string
                sign_image_uri:
                    $ref: '#/components/schemas/onboarding_service.v1.SubmitClientWetSignRequest_SignImageUri'
                sign_image_data:
                    $ref: '#/components/schemas/onboarding_service.v1.SubmitClientWetSignRequest_SignImageData'
        onboarding_service.v1.SubmitClientWetSignRequest_SignImageData:
            type: object
            properties:
                image_data:
                    type: string
                image_type:
                    type: string
        onboarding_service.v1.SubmitClientWetSignRequest_SignImageUri:
            type: object
            properties:
                image_url:
                    type: string
                image_type:
                    type: string
        onboarding_service.v1.SubmitClientWetSignResponse:
            type: object
            properties:
                onboarding_id:
                    type: string
        onboarding_service.v1.UserProfile:
            type: object
            properties:
                kyc_level:
                    type: integer
                    description: User KYC level in ZaloPay system
                    format: int32
                profile_level:
                    type: integer
                    description: User profile level in ZaloPay system
                    format: int32
                full_name:
                    type: string
                    description: User registed fullname
                gender:
                    enum:
                        - UNKNOWN
                        - MALE
                        - FEMALE
                    type: string
                    description: User registed gender
                    format: enum
                phone_number:
                    type: string
                    description: User registed phone
                permanent_address:
                    type: string
                    description: User registed address
                id_type:
                    type: integer
                    description: 'User registed id type | 1: CMND, 2: passport, 3: CCCD, 4: CMSQ, 5: CCCD gắn chip'
                    format: int32
                id_number:
                    type: string
                    description: User registed id number
                email:
                    type: string
                    description: User registed email
                date_of_birth:
                    type: string
                    description: User registed date of birth
                id_issue_place:
                    type: string
                    description: Id issued location
                id_issue_date:
                    type: string
                    description: Id issued date
            description: UserProfile is present zalopay user kyc profile information, not onboarding profile
        onboarding_service.v1.VerifyClientOTPRequest:
            type: object
            properties:
                otp_code:
                    type: string
                otp_type:
                    enum:
                        - CONTRACT_SIGNING
                        - LINK_TYPE_3
                    type: string
                    format: enum
                onboarding_id:
                    type: string
                partner_code:
                    type: string
        onboarding_service.v1.VerifyClientOTPResponse:
            type: object
            properties:
                status:
                    type: boolean
tags:
    - name: Account
    - name: Installment
    - name: Onboarding
    - name: Statement
