# Installment Service

- Account Service
- Onboarding Service
- Management Service
  - Installment Service
  - Statement Service
 
## Generate API
```
# Download and update dependencies
make init
# Generate API files
make api
```
## Generate Wire
```
 make gen-wire
```

## Note

- All the http api don't follow the RESTful style, so we decide to build api for easy to use first and consistency.
- All the http api only serve for a front-end client need to add the context Client into the grpc method pattern
<Verb>Client<Noun>