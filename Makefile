GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)

ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git))))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find api -name *.proto")
else
	CONFIG_PROTO_FILES=$(shell find config -name *.proto)
	INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
	API_ACCOUNT_PROTO_FILES=$(shell find api/account_service -name *.proto)
	API_ONBOARDING_PROTO_FILES=$(shell find api/onboarding_service -name *.proto)
	API_EXTERNAL_PROTO_FILES=$(shell find api/external_services -name *.proto)
	API_MANAGEMENT_PROTO_FILES=$(shell find api/management_service -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-errors/v2@latest
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/sorcererxw/protoc-gen-go-grpc-mock@latest

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./config \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./config \
	       $(CONFIG_PROTO_FILES)

.PHONY: api
# generate api proto
api-internal:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       --go-grpc-mock_out=paths=source_relative:./api \
	       --openapi_out=enum_type=string,fq_schema_naming=true,default_response=false:. \
	       --validate_out=paths=source_relative,lang=go:./api \
	       $(API_PROTO_FILES) $(API_ACCOUNT_PROTO_FILES) \
	       $(API_ONBOARDING_PROTO_FILES) $(API_MANAGEMENT_PROTO_FILES)
api-external:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
		   --go-grpc-mock_out=paths=source_relative:./api \
	       $(API_EXTERNAL_PROTO_FILES)

.PHONY: errors
# generate error proto
errors:
	protoc --proto_path=./api \
           --proto_path=./third_party \
		   --go_out=paths=source_relative:./api \
           --go-errors_out=paths=source_relative:./api \
           $(API_ONBOARDING_PROTO_FILES)

.PHONY: build
# build
build:
	go mod tidy && go mod vendor && go build  -o ./app

.PHONY: generate
# generate
generate:
	go mod tidy && go mod vendor
	go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: mockgen
# generate
mockgen:
	bash tools/mockgen.sh

.PHONY: gen-wire
# generate wire
gen-wire:
	cd internal/zombie/cmd && wire gen
	cd internal/account/cmd/account && wire gen
	cd internal/account/cmd/worker && wire gen
	cd internal/onboarding/cmd/worker && wire gen
	cd internal/onboarding/cmd/onboarding && wire gen
	cd internal/management/cmd/worker && wire gen
	cd internal/management/cmd/management && wire gen

.PHONY: gen-sqlc
gen-sqlc:
	cd resources/sql_schema && sqlc generate -f sqlc_account.yaml
	cd resources/sql_schema && sqlc generate -f sqlc_onboarding.yaml
	cd resources/sql_schema && sqlc generate -f sqlc_installment.yaml

.PHONY: all
# generate all
all:
	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
