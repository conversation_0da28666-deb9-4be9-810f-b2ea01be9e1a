package main

import (
	"os"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cobra"

	accountcmd "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/cmd"
	managementcmd "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/cmd"
	onboardingcmd "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd"
	zombiecmd "gitlab.zalopay.vn/fin/installment/installment-service/internal/zombie/cmd"
)

var rootCmd = cobra.Command{
	Use:              "installment-service",
	Short:            "Installment Service",
	RunE:             runCmd,
	TraverseChildren: true,
}

func init() {
	rootCmd.PersistentFlags().String("config", "/apps/config/config.yaml", "config file (default is empty)")
}

func main() {
	if err := loadTZ(); err != nil {
		log.Warn("Load timezone failed: ", err)
	}

	if err := Execute(); err != nil {
		log.Fatal("Execute program failed: ", err)
	}
}

func loadTZ() error {
	tz := os.Getenv("TZ")
	if tz == "" {
		return nil
	}
	loc, err := time.LoadLocation(tz)
	if err != nil {
		return err
	}
	time.Local = loc
	return nil
}

func runCmd(cmd *cobra.Command, args []string) error {
	// Make default run account service when no command is provided
	defaultCmd := zombiecmd.NewCmd()
	return defaultCmd.RunE(cmd, args)
}

func Execute() error {
	rootCmd.AddCommand(accountcmd.NewCmd())
	rootCmd.AddCommand(onboardingcmd.NewCmd())
	rootCmd.AddCommand(managementcmd.NewCmd())

	if err := rootCmd.Execute(); err != nil {
		return err
	}

	return nil
}
