---
trigger: model_decision
description: When working on the all task related to unit tests, code coverage
---

# Test rules

- Always using the test suite approach when writting tests on golang project
- Please find all the existing mocks for testing about dependencies and apply it mocks before generate it by your self.
- Please use package `go.uber.org/mock` instead of `github.com/golang/mock`(deprecated)
- If you need to generate mock, please analayze and thinking about the exsting interface and using mockgen tool to generate mock
- Ensure code coverage always greater than 90%
