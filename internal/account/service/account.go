package service

import (
	"context"

	"gitlab.zalopay.vn/fin/asset-management/financial-profile/api/genproto"
	fsProfile "gitlab.zalopay.vn/fin/asset-management/financial-profile/api/genproto/adapter"
	"google.golang.org/protobuf/types/known/timestamppb"

	v1Ext "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/external/v1"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/account"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/content"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/sof"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type AccountService struct {
	v1.UnimplementedAccountServer
	v1Ext.UnimplementedPaymentAccountServer
	fsProfile.UnimplementedProfileAdapterServiceServer
	rlogger   log.Logger
	logger    *log.Helper
	sofUc     *sof.Usecase
	accountUc *account.Usecase
	contentUc *content.Usecase
}

// NewAccountService new an account service.
func NewAccountService(
	sofUc *sof.Usecase,
	accountUc *account.Usecase,
	contentUc *content.Usecase,
	logger log.Logger) *AccountService {
	return &AccountService{
		sofUc:     sofUc,
		accountUc: accountUc,
		contentUc: contentUc,
		rlogger:   logger,
		logger:    log.NewHelper(log.With(logger, "service", "account")),
	}
}

func (s *AccountService) CreateAccount(ctx context.Context,
	req *v1.CreateAccountRequest) (*v1.CreateAccountResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}
	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodePartnerNotSupported.String(), "invalid partner code")
	}

	params := &dto.CreateAccountParams{
		ZalopayID:    req.GetZalopayId(),
		OnboardingID: req.GetOnboardingId(),
		PartnerCode:  partnerCode,
	}
	userAcc, err := s.accountUc.CreateAccount(ctx, params)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.CreateAccountResponse{
		AccountId: userAcc.ID,
	}, nil
}

func (s *AccountService) GetAccount(ctx context.Context,
	req *v1.GetAccountRequest) (*v1.GetAccountResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	if req.GetQueryBy() == nil {
		defaultPartner := partner.PartnerCIMB.String()
		req.QueryBy = &v1.GetAccountRequest_PartnerCode{PartnerCode: defaultPartner}
	}

	var err error
	var userAccount *model.Account
	switch req.GetQueryBy().(type) {
	case *v1.GetAccountRequest_AccountId:
		userAccount, err = s.accountUc.GetAccountByID(ctx, req.GetZalopayId(), req.GetAccountId())
	case *v1.GetAccountRequest_PartnerCode:
		userAccount, err = s.accountUc.GetAccountByPartner(ctx, req.GetZalopayId(), partner.PartnerCode(req.GetPartnerCode()))
	}

	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.GetAccountResponse{
		Account: convertToUserAccount(userAccount),
	}, nil
}

func (s *AccountService) GetClientAccount(ctx context.Context,
	req *v1.GetClientAccountRequest) (*v1.GetClientAccountResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	if req.GetQueryBy() == nil {
		defaultPartner := partner.PartnerCIMB.String()
		req.QueryBy = &v1.GetClientAccountRequest_PartnerCode{PartnerCode: defaultPartner}
	}

	var err error
	var userAccount *model.Account
	switch req.GetQueryBy().(type) {
	case *v1.GetClientAccountRequest_AccountId:
		userAccount, err = s.accountUc.GetAccountByID(ctx, zalopayID, req.GetAccountId())
	case *v1.GetClientAccountRequest_PartnerCode:
		userAccount, err = s.accountUc.GetAccountByPartner(ctx, zalopayID, partner.PartnerCode(req.GetPartnerCode()))
	}

	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.GetClientAccountResponse{
		Account: convertToUserAccount(userAccount),
	}, nil
}

func (s *AccountService) ListClientProposedService(ctx context.Context,
	req *v1.ListClientProposedServiceRequest) (*v1.ListClientProposedServiceResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.GetContentRequest{PartnerCode: req.GetPartnerCode()}
	result, err := s.contentUc.GetProposedServices(ctx, params)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	resp := &v1.ListClientProposedServiceResponse{
		Services: convertToProposeServiceList(result.Services),
	}
	return resp, nil
}

func (s *AccountService) GetAccountForPayment(ctx context.Context, req *v1.GetAccountForPaymentRequest) (*v1.GetAccountForPaymentResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodePartnerNotSupported.String(), "invalid partner code")
	}

	userAccount, err := s.accountUc.GetAccountByPartner(ctx, req.GetZalopayId(), partnerCode)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.GetAccountForPaymentResponse{
		Account: convertToUserAccount(userAccount),
	}, nil
}

func (s *AccountService) ListAccountForStmtSync(ctx context.Context, req *v1.ListAccountForStmtSyncRequest) (*v1.ListAccountForStmtSyncResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &model.AccountListStmtSyncParams{
		Limit:         req.GetItemsLimit(),
		AccountIDGt:   req.GetAccountIdFrom(),
		StatementDate: req.GetStatementDate().AsTime(),
		PartnerCodes:  partner.ListFromStrings(req.GetPartnerCodes()),
	}
	userAccounts, err := s.accountUc.ListAccountForStmtSync(ctx, params)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.ListAccountForStmtSyncResponse{
		Accounts: convertToUserAccountsForStmt(userAccounts),
	}, nil
}

func (s *AccountService) GetBasicAccount(ctx context.Context,
	req *v1Ext.GetBasicAccountRequest) (*v1Ext.GetBasicAccountResponse, error) {
	if req.GetPartnerCode() == "" {
		req.PartnerCode = partner.PartnerCIMB.String()
	}
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	resp, err := s.sofUc.GetAccountForValidity(ctx, dto.GetAccountForValidityParams{
		ZalopayID:   req.GetZalopayId(),
		PartnerCode: partnerCode,
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get basic account: %v", err)
		return nil, convertToTransportError(err)
	}

	accResp := resp.Account
	accInfo := &v1Ext.AccountInfo{
		ZalopayId:   req.GetZalopayId(),
		PartnerCode: req.GetPartnerCode(),
		Status:      convertToSofStatus(resp.SofStatus),
	}
	if accResp != nil {
		accInfo.AccountId = accResp.ID
		accInfo.Balance = accResp.InstallmentBalance
		accInfo.TotalLimit = accResp.InstallmentLimit
	}

	return &v1Ext.GetBasicAccountResponse{
		Account: accInfo,
	}, nil
}

func (s *AccountService) GetPaymentAccount(ctx context.Context,
	req *v1Ext.GetPaymentAccountRequest) (*v1Ext.GetPaymentAccountResponse, error) {
	if req.GetPartnerCode() == "" {
		req.PartnerCode = partner.PartnerCIMB.String()
	}
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	resp, err := s.sofUc.GetAccountForPayment(ctx, dto.GetAccountForPaymentParams{
		ZalopayID:   req.GetZalopayId(),
		PartnerCode: partnerCode,
		OrderReqInfo: model.OrderRequest{
			MNO:          req.GetOrderInfo().GetMno(),
			AppID:        req.GetOrderInfo().GetAppId(),
			AppTransID:   req.GetOrderInfo().GetAppTransId(),
			ServiceType:  req.GetOrderInfo().GetServiceType(),
			OrderSource:  req.GetOrderInfo().GetOrderSource(),
			ChargeAmount: req.GetOrderInfo().GetChargeAmount(),
			Description:  req.GetOrderInfo().GetDescription(),
			MerchantName: req.GetOrderInfo().GetMerchantName(),
			EmbedData:    req.GetOrderInfo().GetEmbedData(),
		},
		ClientReqInfo: model.ClientRequest{
			DeviceID:    req.GetClientInfo().GetDeviceId(),
			UserIP:      req.GetClientInfo().GetUserIp(),
			UserLevel:   req.GetClientInfo().GetUserLevel(),
			OSPlatform:  req.GetClientInfo().GetOsPlatform(),
			OSVersion:   req.GetClientInfo().GetOsVersion(),
			AppVersion:  req.GetClientInfo().GetAppVersion(),
			DeviceModel: req.GetClientInfo().GetDeviceModel(),
			ExUserInfo:  req.GetClientInfo().GetExUserInfo(),
		},
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get payment account: %v", err)
		return nil, convertToTransportError(err)
	}

	accResp := resp.Account
	accInfo := &v1Ext.AccountInfo{
		ZalopayId:   req.GetZalopayId(),
		PartnerCode: req.GetPartnerCode(),
		Status:      convertToSofStatus(resp.SofStatus),
	}
	if accResp != nil {
		accInfo.AccountId = accResp.ID
		accInfo.Balance = accResp.InstallmentBalance
	}

	return &v1Ext.GetPaymentAccountResponse{
		Account: accInfo,
		Action:  convertToSofAction(resp.Action),
		Message: convertSofMessage(resp.Message),
	}, nil
}

func (s *AccountService) CheckFullAccount(ctx context.Context,
	req *v1Ext.CheckFullAccountRequest) (*v1Ext.CheckFullAccountResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	resp, err := s.accountUc.GetAccountComplete(ctx, &dto.GetAccountCompleteParams{
		ZalopayID:   req.GetZalopayId(),
		PartnerCode: partnerCode,
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get account complete: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1Ext.CheckFullAccountResponse{
		Account: convertToPayAccount(resp.Account),
		Outstanding: &v1Ext.OutstandingInfo{
			TotalOutstanding: resp.Outstanding.TotalAmount,
			TotalDueAmount:   resp.Outstanding.DueAmount,
			TotalDueRepaid:   resp.Outstanding.DueRepaid,
			TotalDuePenalty:  resp.Outstanding.DuePenalty,
			DueCreatedAt:     timestamppb.New(resp.Outstanding.DueCreatedAt),
			DueUpdatedAt:     timestamppb.New(resp.Outstanding.DueUpdatedAt),
		},
	}, nil
}

func (s *AccountService) ListAdapterProfiles(ctx context.Context,
	req *fsProfile.ListAdapterProfilesRequest) (*fsProfile.ListAdapterProfilesResponse, error) {
	if len(req.GetUserIds()) == 0 {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "empty user ids")
	}
	if len(req.GetUserIds()) > 50 {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "number of users exceed limit")
	}

	var zalopayIDs []int64
	for _, uid := range req.GetUserIds() {
		zalopayIDs = append(zalopayIDs, cast.ToInt64(uid))
	}

	acctBindings, err := s.accountUc.ListAccountBindings(ctx, zalopayIDs)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	acctProfiles := make(map[string]*genproto.Profile, len(acctBindings))
	for _, v := range acctBindings {
		userIDKey := cast.ToString(v.ZalopayID)
		acctProfiles[userIDKey] = convertBindingToAdapterProfile(v)
	}

	return &fsProfile.ListAdapterProfilesResponse{
		Profiles: acctProfiles,
	}, nil
}
