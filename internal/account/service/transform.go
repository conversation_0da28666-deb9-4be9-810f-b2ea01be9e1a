package service

import (
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"
	profileProto "gitlab.zalopay.vn/fin/asset-management/financial-profile/api/genproto"
	"google.golang.org/protobuf/types/known/timestamppb"

	v1Ext "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/external/v1"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkts"
)

func convertToAccountStatus(status model.AccountStatus) v1.Status {
	var mapping = map[model.AccountStatus]v1.Status{
		model.StatusUnknown:  v1.Status_UNSPECIFIED,
		model.StatusActive:   v1.Status_ACTIVE,
		model.StatusInactive: v1.Status_INACTIVE,
		model.StatusBlocked:  v1.Status_BLOCKED,
		model.StatusClosed:   v1.Status_CLOSED,
	}
	result, ok := mapping[status]
	if !ok {
		return v1.Status_UNSPECIFIED
	}
	return result
}

func convertToAccountBalance(acc *model.Account) *dto.AccountBalanceResult {
	return &dto.AccountBalanceResult{
		AccountID:          acc.ID,
		ZalopayID:          acc.ZalopayID,
		PartnerCode:        acc.PartnerCode,
		AccountStatus:      acc.Status,
		InstallmentLimit:   acc.InstallmentLimit,
		InstallmentBalance: acc.InstallmentBalance,
	}
}

func convertToProposeServiceList(services []*model.ServiceContent) []*v1.ServiceProposed {
	result := make([]*v1.ServiceProposed, 0, len(services))

	for _, service := range services {
		result = append(result, &v1.ServiceProposed{
			Code: service.Code,
			Name: service.Name,
			ContentImage: &v1.ContentImage{
				IconUrl:      service.ContentImage.IconUrl,
				CoverUrl:     service.ContentImage.IconUrl,
				ThumbnailUrl: service.ContentImage.IconUrl,
			},
			Interaction: &v1.ContentInteraction{
				ZpaUrl: service.Interaction.ZPAUrl,
				ZpiUrl: service.Interaction.ZPIUrl,
			},
		})
	}
	return result
}

func convertToSofAction(action *model.Action) *v1Ext.Action {
	if action == nil {
		return nil
	}

	var actTypeMap = map[model.CTAType]v1Ext.ActionType{
		model.CTATypePlanSelection: v1Ext.ActionType_ACTION_TYPE_PLAN_SELECTION,
	}
	actionType, found := actTypeMap[action.Type]
	if !found {
		actionType = v1Ext.ActionType_ACTION_TYPE_UNSPECIFIED
	}

	return &v1Ext.Action{
		Type:   actionType,
		Text:   action.Title,
		ZpiUrl: action.ZpiUrl,
		ZpaUrl: action.ZpaUrl,
	}
}

func convertToSofStatus(status model.SoFStatus) v1Ext.PaymentAccountStatus {
	statusMap := map[model.SoFStatus]v1Ext.PaymentAccountStatus{
		model.SofUnknown:       v1Ext.PaymentAccountStatus_ACCOUNT_UNSPECIFIED,
		model.SofActive:        v1Ext.PaymentAccountStatus_ACCOUNT_ACTIVE,
		model.SofInactive:      v1Ext.PaymentAccountStatus_ACCOUNT_INACTIVE,
		model.SofNotRegister:   v1Ext.PaymentAccountStatus_ACCOUNT_NOT_REGISTER,
		model.SofInOnboarding:  v1Ext.PaymentAccountStatus_ACCOUNT_IN_ONBOARDING,
		model.SofMaintenance:   v1Ext.PaymentAccountStatus_ACCOUNT_MAINTENANCE,
		model.SofRiskRule:      v1Ext.PaymentAccountStatus_ACCOUNT_RISK_RULE,
		model.SofTempLocked:    v1Ext.PaymentAccountStatus_ACCOUNT_TEMPORARY_LOCKED,
		model.SofNotApplicable: v1Ext.PaymentAccountStatus_ACCOUNT_NOT_APPLICABLE,
	}
	result, found := statusMap[status]
	if !found {
		return v1Ext.PaymentAccountStatus_ACCOUNT_UNSPECIFIED
	}
	return result
}

func convertSofMessage(msg *model.Message) *v1Ext.Message {
	if msg == nil {
		return nil
	}
	return &v1Ext.Message{
		Text:  msg.Text,
		Color: msg.Color,
	}
}

func convertToPayAccount(account *model.Account) *v1Ext.AccountInfo {
	if account == nil {
		return nil
	}
	return &v1Ext.AccountInfo{
		ZalopayId:            account.ZalopayID,
		AccountId:            account.ID,
		PartnerCode:          account.PartnerCode.String(),
		Status:               convertToPayStatus(account.Status),
		Balance:              account.InstallmentBalance,
		TotalLimit:           account.InstallmentLimit,
		PartnerAccountName:   &account.PartnerAccountName,
		PartnerAccountNumber: &account.PartnerAccountNumber,
		CreatedAt:            timestamppb.New(account.CreatedAt),
		UpdatedAt:            timestamppb.New(account.UpdatedAt),
	}
}

func convertToPayStatus(status model.AccountStatus) v1Ext.PaymentAccountStatus {
	statusMap := map[model.AccountStatus]v1Ext.PaymentAccountStatus{
		model.StatusUnknown:  v1Ext.PaymentAccountStatus_ACCOUNT_UNSPECIFIED,
		model.StatusActive:   v1Ext.PaymentAccountStatus_ACCOUNT_ACTIVE,
		model.StatusInactive: v1Ext.PaymentAccountStatus_ACCOUNT_INACTIVE,
		model.StatusBlocked:  v1Ext.PaymentAccountStatus_ACCOUNT_TEMPORARY_LOCKED,
		model.StatusClosed:   v1Ext.PaymentAccountStatus_ACCOUNT_INACTIVE,
	}
	result, found := statusMap[status]
	if !found {
		return v1Ext.PaymentAccountStatus_ACCOUNT_UNSPECIFIED
	}
	return result

}

func convertToUserAccount(account *model.Account) *v1.UserAccount {
	if account == nil {
		return nil
	}
	return &v1.UserAccount{
		AccountId:            account.ID,
		Source:               account.Origin.String(),
		PartnerCode:          account.PartnerCode.String(),
		PartnerAccountName:   account.PartnerAccountName,
		PartnerAccountNumber: account.PartnerAccountNumber,
		InstallmentLimit:     account.InstallmentLimit,
		RepaymentBalance:     account.RepaymentBalance,
		InstallmentBalance:   account.InstallmentBalance,
		InstallmentTerm:      convertToInstallmentTerm(account),
		Status:               convertToAccountStatus(account.Status),
		CreatedAt:            timestamppb.New(account.CreatedAt),
	}
}

func convertToUserAccountsForStmt(accounts []*model.Account) []*v1.StmtAccount {
	result := make([]*v1.StmtAccount, 0, len(accounts))
	for _, account := range accounts {
		result = append(result, convertToUserAccountForStmt(account))
	}
	return result
}

func convertToUserAccountForStmt(account *model.Account) *v1.StmtAccount {
	if account == nil {
		return nil
	}
	return &v1.StmtAccount{
		ZalopayId:            account.ZalopayID,
		AccountId:            account.ID,
		PartnerCode:          account.PartnerCode.String(),
		PartnerAccountName:   account.PartnerAccountName,
		PartnerAccountNumber: account.PartnerAccountNumber,
		Status:               convertToAccountStatus(account.Status),
	}
}

func convertToInstallmentTerm(account *model.Account) *v1.InstallmentTerm {
	instTerm := account.BuildInstallmentTerm()
	return &v1.InstallmentTerm{
		FeeExplanation:    instTerm.FeeExplanationText,
		StmtDueDateText:   instTerm.StatementDueDateText,
		StmtIncurDateText: instTerm.StatementIncurredText,
	}
}

func convertBindingToAdapterProfile(data *dto.AccountBindingItem) *profileProto.Profile {
	userAccount := data.Account
	userOnboard := data.Onboarding
	zalopayIDStr := cast.ToString(data.ZalopayID)
	profileAddInfo := map[string]string{}
	profileResult := &profileProto.Profile{
		UserId:        zalopayIDStr,
		AccountStatus: profileProto.AccountStatus_UNBINDING,
		ExtraInfo:     &profileProto.ExtraInfo{},
	}

	if userAccount == nil && userOnboard == nil {
		return profileResult
	}
	if userOnboard != nil {
		profileResult.FullName = userOnboard.FullName
		profileResult.PhoneNumber = userOnboard.PhoneNumber
		profileResult.NationalId = userOnboard.IdentityNumber
		profileResult.ExtraInfo.OriginalStatus = userOnboard.CurrentStep
		profileResult.ExtraInfo.OriginalStatusMsg = getProfileStatusMsg(userOnboard)
		profileAddInfo["partner_code"] = userOnboard.PartnerCode.String()
		profileAddInfo["od_status"] = userOnboard.CIMBPartnerData.ODStatus
		profileAddInfo["casa_status"] = userOnboard.CIMBPartnerData.CASAStatus
		profileAddInfo["signing_status"] = userOnboard.CIMBPartnerData.SignStatus
		profileAddInfo["error_detail"] = userOnboard.CIMBPartnerData.ErrorDetail
		profileAddInfo["manual_approval_reason"] = userOnboard.CIMBPartnerData.ManualApprovalReason
	}
	if userAccount != nil {
		profileResult.AccountId = userAccount.ID
		profileResult.ExtraInfo.PartnerAccountId = userAccount.PartnerAccountNumber
		profileResult.ExtraInfo.ApprovalDateTime = userAccount.CreatedAt.Format(time.RFC3339)
		profileAddInfo["partner_code"] = userAccount.PartnerCode.String()
		profileAddInfo["account_total_limit"] = cast.ToString(userAccount.InstallmentLimit)
		profileAddInfo["account_available_balance"] = cast.ToString(userAccount.InstallmentBalance)
	}

	profileResult.AccountStatus = mappingProfileStatus(userAccount, userOnboard)
	profileResult.ExtraInfo.AdditionalInfo = profileAddInfo
	return profileResult
}

func getProfileStatusMsg(onboarding *model.OnboardingData) string {
	if onboarding.CIMBPartnerData.ErrorDetail != "" {
		return onboarding.CIMBPartnerData.ErrorDetail
	}
	return onboarding.RejectCode
}

func mappingProfileStatus(account *model.Account, onboarding *model.OnboardingData) profileProto.AccountStatus {
	if account == nil && onboarding == nil {
		return profileProto.AccountStatus_UNBINDING
	}

	switch {
	case account == nil:
		break
	case account.Status == model.StatusActive:
		return profileProto.AccountStatus_ACTIVE
	case account.Status == model.StatusBlocked:
		return profileProto.AccountStatus_LOCKED
	case account.Status == model.StatusInactive:
		return profileProto.AccountStatus_INACTIVE
	case account.Status == model.StatusClosed:
		return profileProto.AccountStatus_UNBINDING
	}

	switch {
	case onboarding == nil:
		break
	case onboarding.OnboardingStatusFlag.IsUnregister:
		return profileProto.AccountStatus_UNBINDING
	case onboarding.OnboardingStatusFlag.IsOnboarding:
		return profileProto.AccountStatus_PROCESSING
	case onboarding.OnboardingStatusFlag.IsRejected:
		return profileProto.AccountStatus_REJECTED
	}
	return profileProto.AccountStatus_ACCOUNT_STATUS_UNSPECIFIED
}

func convertToTransportError(err error) *errors.Error {
	var errApp *errors.Error
	var errInfo *errorkit.ErrorInfo
	if errors.As(err, &errApp) {
		return errApp
	}
	if errors.As(err, &errInfo) {
		return convertErrorInfoToTransportError(errInfo)
	}
	return errors.InternalServer("UNKNOWN_ERROR", err.Error())
}

func convertErrorInfoToTransportError(err *errorkit.ErrorInfo) *errors.Error {
	var errorCodeMapping = map[errorkit.Kind]errorkts.ErrorFunc{
		errorkit.TypeUnexpected:   errors.InternalServer,
		errorkit.TypeBadRequest:   errors.BadRequest,
		errorkit.TypeInvalidArg:   errors.BadRequest,
		errorkit.TypeNotFound:     errors.NotFound,
		errorkit.TypeConversion:   errors.InternalServer,
		errorkit.TypeRemoteCall:   errors.InternalServer,
		errorkit.TypeRepository:   errors.InternalServer,
		errorkit.TypeConflict:     errors.Conflict,
		errorkit.TypeUnauthorized: errors.Unauthorized,
		errorkit.TypeForbidden:    errors.Forbidden,
		errorkit.TypeValidation:   errorkts.UnprocessableEntity,
		errorkit.TypePrecondition: errorkts.PreconditionsFailed,
	}
	utilFunc, found := errorCodeMapping[err.Kind]
	if !found {
		utilFunc = errors.InternalServer
	}

	metadata := cast.ToStringMapString(err.Metadata)
	return utilFunc(err.Code.String(), err.Error()).WithMetadata(metadata)
}
