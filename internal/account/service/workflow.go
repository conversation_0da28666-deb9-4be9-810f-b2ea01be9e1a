package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	tLog "go.temporal.io/sdk/log"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	ErrTypePollingBalanceDone    = "POLLING_BALANCE_DONE"
	ErrTypePollingBalanceSuccess = "POLLING_BALANCE_SUCCESS"
)

func (s *AccountService) SyncAccountBalanceWorkflow(ctx workflow.Context,
	workflowData *dto.SyncAccountBalanceParams) (*dto.AccountBalanceResult, error) {
	logger := workflow.GetLogger(ctx)
	logger = tLog.With(logger,
		"zalopay_id", workflowData.ZalopayID,
		"account_id", workflowData.AccountID)

	logger.Info("Start process sync account balance workflow", "data", workflowData)

	opts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Second * 30,
		ScheduleToCloseTimeout: time.Minute * 5,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 5,
			BackoffCoefficient: 2,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, opts)

	var acc *dto.AccountBalanceResult
	if err := workflow.ExecuteActivity(ctx, s.SyncAccountBalanceActivity, workflowData).Get(ctx, &acc); err != nil {
		logger.Error("Sync account balance activity failed", "error", err)
		return nil, err
	}

	logger.Info("Sync account balance workflow completed", "account", acc)

	return acc, nil
}

func (s *AccountService) PollingAccountBalanceWorkflow(
	ctx workflow.Context, workflowData *dto.SyncAccountBalanceParams) error {
	logger := tLog.With(
		workflow.GetLogger(ctx),
		"zalopay_id", workflowData.ZalopayID,
		"account_id", workflowData.AccountID,
	)

	logger.Info("Start process polling account balance workflow", "data", workflowData)

	/**
	 * Sleep 5s to wait for the account balance to be updated
	 */
	if err := workflow.Sleep(ctx, time.Second*5); err != nil {
		return err
	}

	actAcc := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval: time.Second * 5,
			MaximumInterval: time.Second * 5,
			MaximumAttempts: 2,
		},
	}
	ctxAcc := workflow.WithActivityOptions(ctx, actAcc)
	var currAcc *dto.AccountBalanceResult
	err := workflow.
		ExecuteActivity(ctxAcc, s.GetCurrentAccountInfoActivity, workflowData.ZalopayID, workflowData.AccountID).
		Get(ctxAcc, &currAcc)
	if err != nil {
		logger.Error("Get current account info activity failed", "error", err)
	}

	logger.Info("Current account info", "account", currAcc)

	actPoll := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: time.Minute * 25,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 5,
			MaximumInterval:    time.Second * 30,
			BackoffCoefficient: 1.5,
		},
	}
	ctxPoll := workflow.WithActivityOptions(ctx, actPoll)
	err = workflow.ExecuteActivity(ctxPoll, s.PollingAccountBalanceActivity, workflowData).Get(ctxPoll, nil)
	if err != nil {
		logger.Error("Polling account balance activity failed", "error", err)
		var appErr *temporal.ApplicationError
		if !errors.As(err, &appErr) {
			return err
		}
		if appErr.Type() == ErrTypePollingBalanceSuccess {
			return nil
		}
	}

	logger.Info("Polling account balance workflow completed")
	return nil
}

func (s *AccountService) SyncCIMBAccountBalancesWorkflow(ctx workflow.Context) error {
	logger := tLog.With(workflow.GetLogger(ctx))

	logger.Info("Start process sync CIMB full accounts balances workflow")

	actOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 24,
		RetryPolicy:            &temporal.RetryPolicy{MaximumAttempts: 1},
	}
	syncCtx := workflow.WithActivityOptions(ctx, actOpts)
	syncParams := &dto.SyncDebtAccountBalancesParams{PartnerCode: partner.PartnerCIMB}
	if err := workflow.ExecuteActivity(syncCtx, s.SyncAccountBalancesActivity, syncParams).Get(syncCtx, nil); err != nil {
		logger.Error("Sync full account balances activity failed", "error", err)
		return err
	}

	logger.Info("Sync CIMB full account balances workflow completed")
	return nil
}

// Only used in CIMB Debt Accounts
func (s *AccountService) SyncCIMBDebtAccountBalancesWorkflow(ctx workflow.Context) error {
	logger := tLog.With(workflow.GetLogger(ctx))

	logger.Info("Start process sync CIMB debt account balances workflow")

	actOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 24,
		RetryPolicy:            &temporal.RetryPolicy{MaximumAttempts: 1},
	}
	syncCtx := workflow.WithActivityOptions(ctx, actOpts)
	syncParams := &dto.SyncDebtAccountBalancesParams{PartnerCode: partner.PartnerCIMB}
	if err := workflow.ExecuteActivity(syncCtx, s.SyncDebtAccountBalancesActivity, syncParams).Get(syncCtx, nil); err != nil {
		logger.Error("Sync debt account balances activity failed", "error", err)
		return err
	}

	logger.Info("Sync CIMB debt account balances workflow completed")
	return nil
}

func (s *AccountService) SyncAccountBalanceActivity(ctx context.Context,
	workflowData *dto.SyncAccountBalanceParams) (*dto.AccountBalanceResult, error) {
	logger := s.loggerWithActCtx(ctx)

	acc, err := s.accountUc.SyncAccountBalance(ctx, workflowData)
	if err != nil {
		logger.Errorf("failed to sync account balance, err=%v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync account balance", "", err)
	}

	result := convertToAccountBalance(acc)
	logger.Infow("msg", "Sync account balance activity success", "account", result)
	return result, nil
}

func (s *AccountService) GetCurrentAccountInfoActivity(
	ctx context.Context, zalopayID, accountID int64) (*dto.AccountBalanceResult, error) {
	logger := s.loggerWithActCtx(ctx)

	acc, err := s.accountUc.GetAccountByID(ctx, zalopayID, accountID)
	if err != nil {
		logger.Errorf("failed to get account, err=%v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get account", "", err)
	}

	result := convertToAccountBalance(acc)
	logger.Infow("msg", "Get current account info activity success", "account", result)
	return result, nil
}

func (s *AccountService) PollingAccountBalanceActivity(
	ctx context.Context, workflowData *dto.SyncAccountBalanceParams) error {
	gfCtx := context.WithoutCancel(ctx)
	logger := s.loggerWithActCtx(ctx)

	for {
		select {
		case <-ctx.Done():
			var result *dto.AccountBalanceResult
			if err := activity.GetHeartbeatDetails(gfCtx, &result); err != nil {
				logger.Errorf("failed to get heartbeat details, err=%v", err)
				return temporal.NewApplicationErrorWithCause("failed to get heartbeat details", "", err)
			}
			logger.Infow("Polling account balance activity done or timeout")
			return temporal.NewApplicationErrorWithCause("polling account balance activity canceled", ErrTypePollingBalanceDone, ctx.Err(), result)
		default:
			acc, err := s.accountUc.SyncAccountBalance(ctx, workflowData)
			if err != nil {
				logger.Errorf("failed to polling account balance, err=%v", err)
				return temporal.NewApplicationErrorWithCause("failed to polling account balance", "", err)
			}
			activity.RecordHeartbeat(ctx, acc)
			result := convertToAccountBalance(acc)
			logger.Infow("msg", "Polling account balance activity success", "account", result)
			return temporal.NewApplicationError("polling account balance activity success but continue process", ErrTypePollingBalanceSuccess, result)
		}
	}
}

func (s *AccountService) SyncAccountBalancesActivity(ctx context.Context, params *dto.SyncAccountBalancesParams) (*dto.AccountsSyncStats, error) {
	logger := s.loggerWithActCtx(ctx)

	logger.Info("Start process sync account balances activity", "partner_code", params.PartnerCode)

	data, err := s.accountUc.SyncAccountBalancesByPartner(ctx, params.PartnerCode)
	if err != nil {
		logger.Errorf("failed to sync account balances, err=%v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync account balances", "", err)
	}

	logger.Infow("msg", "Sync account balances activity success", "stats", data)
	return data, nil
}

func (s *AccountService) SyncDebtAccountBalancesActivity(ctx context.Context, params *dto.SyncDebtAccountBalancesParams) (*dto.AccountsSyncStats, error) {
	logger := s.loggerWithActCtx(ctx)

	logger.Info("Start process sync debt account balances activity", "partner_code", params.PartnerCode)

	data, err := s.accountUc.SyncDebtAccountBalancesByPartner(ctx, params.PartnerCode)
	if err != nil {
		logger.Errorf("failed to sync debt account balances, err=%v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync debt account balances", "", err)
	}

	logger.Infow("msg", "Sync debt account balances activity success", "stats", data)
	return data, nil
}

func (s *AccountService) loggerWithActCtx(ctx context.Context) *log.Helper {
	actInfo := activity.GetInfo(ctx)
	logging := log.With(s.rlogger,
		"service", "account",
		"activity_id", actInfo.ActivityID,
		"workflow_id", actInfo.WorkflowExecution.ID,
		"workflow_run_id", actInfo.WorkflowExecution.RunID,
	)
	return log.NewHelper(logging).WithContext(ctx)
}
