package model

type CTAType int

const (
	CTATypeDeepLink = iota + 1
	CTATypePlanSelection
)

type Action struct {
	Type   CTAType
	Title  string
	ZpiUrl string
	ZpaUrl string
}

type Message struct {
	Text  string `json:"text"`
	Color string `json:"color"`
}

type CTATitle string

const (
	CTARegister   CTATitle = "Đăng ký"
	CTADetail     CTATitle = "Chi tiết"
	CTAChangePlan CTATitle = "Đổi gói"
	CTASelectPlan CTATitle = "Chọn gói"
	CTAUnlock     CTATitle = "Mở khóa"
)

func (t CTATitle) String() string {
	return string(t)

}

type MessageText string

const (
	MessageAccountNotRegister   MessageText = "Chưa đăng ký"
	MessageOnboardingIsRejected MessageText = "Chưa được duyệt"
	MessageOnboardingInProgress MessageText = "Đang chờ duyệt"
	MessageAccountHitRiskRule   MessageText = "Vượt hạn mức sử dụng cho dịch vụ này"
	MessageNotEnoughLimit       MessageText = "Không đủ hạn mức"
	MessageAccountLocked        MessageText = "Tài khoản tạm khóa"
	MessageNotApplicable        MessageText = "Không áp dụng"
	MessageNotApplicableOrder   MessageText = "Không áp dụng cho đơn hàng"
	MessageMinEmiAmount         MessageText = "Chỉ từ %s/tháng"
)

func (t MessageText) String() string {
	return string(t)
}
