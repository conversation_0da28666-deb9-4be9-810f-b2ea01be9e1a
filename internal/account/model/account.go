package model

import (
	"slices"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	feeExplanation   = "3%/tháng + 4.000đ"
	statementDueText = "Ngày 15 hàng tháng"
	statementIncText = "Ngày 10 hàng tháng"
)

type Account struct {
	ID                   int64
	ZalopayID            int64
	PartnerCode          partner.PartnerCode
	Origin               AccountSource
	Status               AccountStatus
	InstallmentLimit     int64
	InstallmentBalance   int64
	RepaymentBalance     *int64
	PartnerAccountName   string
	PartnerAccountNumber string
	CreatedAt            time.Time
	UpdatedAt            time.Time
}

func (a *Account) GetID() int64 {
	if a == nil {
		return -1
	}
	return a.ID
}

func (a *Account) GetStatus() AccountStatus {
	if a == nil {
		return StatusUnknown
	}
	return a.Status
}

func (a *Account) CanUsable() bool {
	validStatus := []AccountStatus{StatusActive, StatusBlocked}
	return slices.Contains(validStatus, a.GetStatus())
}

func (a *Account) SetBalances(totalLimit, availableBalance int64, repaymentBalance *int64) {
	a.SetMainBalances(totalLimit, availableBalance)
	a.SetRepaymentBalance(repaymentBalance)
}

func (a *Account) SetMainBalances(limit, balance int64) {
	a.InstallmentLimit = limit
	a.InstallmentBalance = balance
}

// SetRepaymentBalance set RepaymentBalance for account if balance exists
func (a *Account) SetRepaymentBalance(balance *int64) {
	if balance == nil {
		return
	}
	a.RepaymentBalance = balance
}

func (a *Account) SetStatus(status AccountStatus) {
	a.Status = status
}

func (a *Account) GetInstallmentBalance() int64 {
	if a == nil {
		return 0
	}
	return a.InstallmentBalance
}

func (a *Account) GetInstallmentLimit() int64 {
	if a == nil {
		return 0
	}
	return a.InstallmentLimit
}

func (a *Account) GetInstallmentOutstanding() int64 {
	if a == nil {
		return 0
	}
	return a.InstallmentLimit - a.InstallmentBalance
}

func (a *Account) BuildInstallmentTerm() InstallmentTerm {
	if a == nil {
		return InstallmentTerm{}
	}
	return InstallmentTerm{
		FeeExplanationText:    feeExplanation,
		StatementDueDateText:  statementDueText,
		StatementIncurredText: statementIncText,
	}
}

type AccountCreateParams struct {
	ZalopayID            int64
	PartnerCode          partner.PartnerCode
	Origin               AccountSource
	Status               AccountStatus
	ExpiryDate           time.Time
	InstallmentLimit     int64
	InstallmentBalance   int64
	PartnerAccountName   string
	PartnerAccountNumber string
}

type AccountListParams struct {
	Filters    AccountFilter
	Pagination Pagination
}

type AccountListingBase struct {
	Limit       int32
	AccountIDGt int64
	PartnerCode partner.PartnerCode
	// Optional
	PartnerCodes []partner.PartnerCode
}

func (a *AccountListingBase) GetLimit() int32 {
	if a == nil {
		return 0
	}
	return a.Limit
}

func (a *AccountListingBase) GetAccountIDNext() int64 {
	if a == nil {
		return 0
	}
	return a.AccountIDGt
}

type AccountListByPartnerParams struct {
	AccountListingBase
}

type AccountDebtListParams struct {
	AccountListingBase
}

type AccountListStmtSyncParams struct {
	Limit         int32
	AccountIDGt   int64
	StatementDate time.Time
	PartnerCodes  []partner.PartnerCode
}

func (a *AccountListStmtSyncParams) GetLimit() int32 {
	if a == nil {
		return 0
	}
	return a.Limit
}

func (a *AccountListStmtSyncParams) GetAccountIDNext() int64 {
	if a == nil {
		return 0
	}
	return a.AccountIDGt
}

func (a *AccountListStmtSyncParams) GetPartnerCodesStr() []string {
	partnerCodes := make([]string, 0, len(a.PartnerCodes))
	for _, code := range a.PartnerCodes {
		partnerCodes = append(partnerCodes, code.String())
	}
	return partnerCodes
}

func (a *AccountListStmtSyncParams) GetStatementTime() time.Time {
	if a == nil {
		return time.Time{}
	}
	return a.StatementDate
}

type AccountSource string

const (
	SourceUnknown     AccountSource = "unknown"
	SourceInstallment AccountSource = "installment"
)

func (a AccountSource) String() string {
	return string(a)
}

func AccountSourceFromString(str string) AccountSource {
	mapping := map[string]AccountSource{
		string(SourceUnknown):     SourceUnknown,
		string(SourceInstallment): SourceInstallment,
	}
	value, ok := mapping[str]
	if !ok {
		return SourceUnknown
	}
	return value
}

type AccountStatus string

const (
	StatusUnknown  AccountStatus = "unknown"
	StatusActive   AccountStatus = "active"
	StatusInactive AccountStatus = "inactive"
	StatusClosed   AccountStatus = "closed"
	StatusBlocked  AccountStatus = "blocked"
)

func (a AccountStatus) String() string {
	return string(a)
}

func AccountStatusFromString(str string) AccountStatus {
	mapping := map[string]AccountStatus{
		string(StatusUnknown):  StatusUnknown,
		string(StatusActive):   StatusActive,
		string(StatusInactive): StatusInactive,
		string(StatusClosed):   StatusActive,
		string(StatusBlocked):  StatusBlocked,
	}
	value, ok := mapping[str]
	if !ok {
		return StatusUnknown
	}
	return value
}

type AccountFilter struct {
	AccountID    int64
	ZalopayID    int64
	CreatedAt    *TimeRangeFilter
	PartnerCodes []partner.PartnerCode
}

type TimeRangeFilter struct {
	From time.Time
	To   time.Time
}

type InstallmentTerm struct {
	FeeExplanationText    string
	StatementDueDateText  string
	StatementIncurredText string
}
