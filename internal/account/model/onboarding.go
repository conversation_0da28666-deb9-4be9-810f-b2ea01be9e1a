package model

import "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"

type OnboardingData struct {
	ZalopayID       int64
	OnboardingID    int64
	PartnerCode     partner.PartnerCode
	CurrentStep     string
	RejectCode      string
	FullName        string
	PhoneNumber     string
	IdentityNumber  string
	CIMBPartnerData CIMBOnboarding
	OnboardingStatusFlag
}

type OnboardingProfile struct {
	FullName string
	Phone    string
	IDNumber string
}

type OnboardingStatus struct {
	CurrentStep string
	PartnerCode partner.PartnerCode
	OnboardingStatusFlag
}

type OnboardingStatusFlag struct {
	IsOnboarding bool
	IsUnregister bool
	IsRejected   bool
}

type CIMBOnboarding struct {
	ODStatus             string
	CASAStatus           string
	SignStatus           string
	ErrorDetail          string
	ManualApprovalReason string
}
