package model

import "reflect"

type SoFStatus string

const (
	SofUnknown        SoFStatus = "unknown"
	SofNotRegister    SoFStatus = "not_register"
	SofInOnboarding   SoFStatus = "in_onboarding"
	SofMaintenance    SoFStatus = "maintenance"
	SofRiskRule       SoFStatus = "risk_rule"
	SofNotApplicable  SoFStatus = "not_applicable"
	SofNotEnoughLimit SoFStatus = "not_enough_limit"
	SofTempLocked     SoFStatus = "temp_locked"
	SofActive         SoFStatus = "active"
	SofInactive       SoFStatus = "inactive"
)

type OrderRequest struct {
	AppID        int32  `json:"app_id"`
	AppTransID   string `json:"app_trans_id"`
	ChargeAmount int64  `json:"charge_amount"`
	MerchantName string `json:"merchant_name,omitempty"`
	ServiceType  string `json:"service_type,omitempty"`
	Description  string `json:"description,omitempty"`
	OrderSource  int32  `json:"order_source,omitempty"`
	EmbedData    string `json:"embed_data,omitempty"`
	MNO          string `json:"mno,omitempty"`
}

func (o OrderRequest) IsEmpty() bool {
	return reflect.DeepEqual(o, OrderRequest{})
}

type ClientRequest struct {
	DeviceID    string `json:"device_id"`
	UserIP      string `json:"user_ip"`
	UserLevel   string `json:"user_level"`
	OSPlatform  string `json:"os_platform"`
	OSVersion   string `json:"os_version"`
	AppVersion  string `json:"app_version"`
	DeviceModel string `json:"device_model"`
	ExUserInfo  string `json:"ex_user_info"`
}

type OrderEmbedData struct {
	Installment OrderInstData `json:"installment"`
}

// OrderInstData is installment info of order
type OrderInstData struct {
	Eligible bool `json:"eligible"`
}
