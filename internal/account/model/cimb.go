package model

import (
	"time"
)

type PartnerAccount struct {
	AccountInfo    *PartnerAccountInfo
	AccountBalance *PartnerAccountBalance
}

type PartnerAccountInfo struct {
	AccountName   string
	AccountID     string
	AccountType   string
	AccountNumber string

	/**
	 * Expiry date of overdraft limit, depend on partner
	 * With CIMB: it will be set from OverdraftExpiryDate in PartnerAccountBalance
	 * Ensure check this value in this struct before using
	 */
	ExpiryDate time.Time
}

type PartnerAccountBalance struct {
	TotalLimit             int64
	OverdraftLimit         int64
	AvailableBalance       int64
	RepaymentBalance       int64
	CASABalanceStatus      string
	OverdraftExpiryDate    time.Time
	OverdraftBalanceStatus PartnerODBalanceStatus
}

type PartnerODBalanceStatus string

const (
	PartnerODBalanceStatusNormal  PartnerODBalanceStatus = "NORMAL"
	PartnerODBalanceStatusBlocked PartnerODBalanceStatus = "BLOCKED"
)

func (p PartnerODBalanceStatus) String() string {
	return string(p)
}

func (p PartnerODBalanceStatus) ToAccountStatus() AccountStatus {
	switch p {
	case PartnerODBalanceStatusNormal:
		return StatusActive
	case PartnerODBalanceStatusBlocked:
		return StatusBlocked
	default:
		return StatusUnknown
	}
}
