package model

type Pagination struct {
	Offset *int      `json:"page"`
	Limit  int       `json:"limit"`
	Cursor *string   `json:"cursor"`
	Direct Direction `json:"direct"`
}

type PaginationResult struct {
	HasNext    bool   `json:"has_next"`
	HasPrev    bool   `json:"has_prev"`
	NextCursor string `json:"next_cursor"`
	PrevCursor string `json:"prev_cursor"`
}

type Direction int32

const (
	CursorDirectionNext Direction = iota
	CursorDirectionPrev
)
