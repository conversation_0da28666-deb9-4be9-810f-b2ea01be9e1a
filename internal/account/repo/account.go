package repo

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"

	"github.com/go-kratos/kratos/v2/log"
)

type accountRepo struct {
	repos  *Repository
	logger *log.Helper
}

// NewAccountRepo .
func NewAccountRepo(repos *Repository, kLogger log.Logger) _interface.AccountRepo {
	return &accountRepo{
		repos:  repos,
		logger: log.NewHelper(kLogger),
	}
}

func (a *accountRepo) CreateAccount(ctx context.Context, params *model.AccountCreateParams) (int64, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("CreateAccount")

	createParams := &da.CreateAccountParams{
		ZalopayID:            params.ZalopayID,
		PartnerCode:          params.PartnerCode.String(),
		PartnerAccountName:   params.PartnerAccountName,
		PartnerAccountNumber: params.PartnerAccountNumber,
		InstallmentLimit:     params.InstallmentLimit,
		InstallmentBalance:   params.InstallmentBalance,
		Status:               da.AccountsStatus(params.Status),
		ExpiryDate:           zutils.NewNullTime(params.ExpiryDate),
		Origin:               params.Origin.String(),
	}
	result, err := a.repos.Queries(ctx).CreateAccount(ctx, createParams)
	if err != nil {
		a.logger.WithContext(ctx).Errorf("CreateAccount failed: %v", err)
		return -1, errors.Wrap(err, "CreateAccount into db failed")
	}

	accountID, err := result.LastInsertId()
	if err != nil {
		a.logger.WithContext(ctx).Errorf("Get last insert id failed: %v", err)
		return -1, errors.Wrap(err, "Get last insert id failed")
	}
	return accountID, nil
}

func (a *accountRepo) UpdateAccountBalance(ctx context.Context, params *model.Account) error {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("UpdateAccountBalance")

	updateParams := &da.UpdateAccountBalanceAndStatusParams{
		ID:                 params.ID,
		Status:             fromAccountStatus(params.Status),
		InstallmentLimit:   params.InstallmentLimit,
		InstallmentBalance: params.InstallmentBalance,
		RepaymentBalance:   fromRepaymentBalance(params.RepaymentBalance),
	}
	if err := a.repos.Queries(ctx).UpdateAccountBalanceAndStatus(ctx, updateParams); err != nil {
		a.logger.WithContext(ctx).Errorf("UpdateAccountBalance failed: %v", err)
		return errors.Wrap(err, "UpdateAccountBalance into db failed")
	}
	return nil
}

func (a *accountRepo) GetAccountByID(ctx context.Context, accountID int64) (*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("GetAccountByID")

	result, err := a.repos.Queries(ctx).GetAccountByID(ctx, accountID)
	if err != nil {
		a.logger.WithContext(ctx).Errorf("GetAccountByID failed: %v", err)
		return nil, errors.Wrapf(err, "GetAccountByID failed, accID=%d", accountID)
	}
	return toAccountDomain(result), nil
}

func (a *accountRepo) GetAccountByIDForUpdate(ctx context.Context, accountID int64) (*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("GetAccountByIDForUpdate")

	result, err := a.repos.Queries(ctx).GetAccountByIDForUpdate(ctx, accountID)
	if err != nil {
		a.logger.WithContext(ctx).Errorf("GetAccountByIDForUpdate failed: %v", err)
		return nil, errors.Wrapf(err, "GetAccountByIDForUpdate failed, accID=%d", accountID)
	}
	return toAccountDomain(result), nil
}

func (a *accountRepo) GetAccountByIDAndZalopayID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("GetAccountByIDForUpdate")

	result, err := a.repos.Queries(ctx).GetAccountByIDAndZalopayID(ctx, &da.GetAccountByIDAndZalopayIDParams{
		ID:        accountID,
		ZalopayID: zalopayID,
	})
	if err != nil {
		a.logger.WithContext(ctx).Errorf("GetAccountByIDAndZalopayID failed: %v", err)
		return nil, errors.Wrapf(err, "GetAccountByIDAndZalopayID failed, accID=%d, zalopayID=%d", accountID, zalopayID)
	}
	return toAccountDomain(result), nil
}

func (a *accountRepo) GetActiveAccountByPartner(ctx context.Context,
	zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("GetActiveAccountByPartner")

	result, err := a.repos.Queries(ctx).GetActiveAccountByPartnerCode(ctx,
		&da.GetActiveAccountByPartnerCodeParams{
			ZalopayID:   zalopayID,
			PartnerCode: partnerCode.String(),
		})
	if err != nil {
		a.logger.WithContext(ctx).Errorf("CreateAccount failed: %v", err)
		return nil, errors.Wrap(err, "CreateAccount into db failed")
	}
	return toAccountDomain(result), nil
}

func (a *accountRepo) GetLatestAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("GetLatestAccountByPartner")

	account, err := a.repos.Queries(ctx).GetLatestAccountByPartnerCode(ctx, &da.GetLatestAccountByPartnerCodeParams{
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode.String(),
	})
	if errors.Is(err, sql.ErrNoRows) {
		a.logger.WithContext(ctx).Warnf("account not found, zalopayID=%d, partnerCode=%s", zalopayID, partnerCode)
		return nil, model.ErrAccountNotFound
	}
	if err != nil {
		a.logger.WithContext(ctx).Errorf("get latest account by partner fail: %v", err)
		return nil, errors.Wrap(err, "get latest account by partner fail")
	}
	return toAccountDomain(account), nil
}

func (a *accountRepo) ListAccountByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("ListAccountByUserIDs")

	accounts, err := a.repos.Queries(ctx).ListAccountByZalopayIDs(ctx, zalopayIDs)
	if err != nil {
		a.logger.WithContext(ctx).Errorf("query list account by zalopayIDs from db failed: %v", err)
		return nil, errors.Wrap(err, "query list account by zalopayIDs from db")
	}

	result := make([]*model.Account, 0, len(accounts))
	for _, account := range accounts {
		result = append(result, toAccountDomain(account))
	}
	return result, nil
}

func (a *accountRepo) ListAccountForStmtSync(ctx context.Context,
	params *model.AccountListStmtSyncParams) ([]*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("ListAccountForStmtSync")

	// Get all accounts that have been created before the statement time
	// If the statement time is not provided, use the current time mean get all accounts
	createdAtLte := params.GetStatementTime()
	if createdAtLte.IsZero() {
		createdAtLte = time.Now()
	}

	acts, err := a.repos.Queries(ctx).ListAccountForStmtSync(ctx, &da.ListAccountForStmtSyncParams{
		Limit:        params.GetLimit(),
		IDGt:         params.GetAccountIDNext(),
		CreatedAtLte: createdAtLte,
		PartnerCodes: params.GetPartnerCodesStr(),
	})
	if err != nil {
		a.logger.WithContext(ctx).Errorf("ListAccountForStmtSync failed: %v", err)
		return nil, errors.Wrap(err, "ListAccountForStmtSync failed")
	}
	accounts := make([]*model.Account, 0, len(acts))
	for _, act := range acts {
		accounts = append(accounts, toAccountDomain(act))
	}
	if len(accounts) > int(params.GetLimit()) {
		return accounts[:len(accounts)-1], nil
	}
	return accounts, nil
}

func (a *accountRepo) ListAccountByPartner(ctx context.Context, params *model.AccountListByPartnerParams) ([]*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("ListAccountByPartner")

	accounts, err := a.repos.Queries(ctx).ListAccountByPartnerCode(ctx, &da.ListAccountByPartnerCodeParams{
		IDGt:        params.GetAccountIDNext(),
		Limit:       params.GetLimit(),
		PartnerCode: params.PartnerCode.String(),
	})
	if err != nil {
		a.logger.WithContext(ctx).Errorf("ListAccountByPartnerCode failed: %v", err)
		return nil, errors.Wrap(err, "ListAccountByPartnerCode failed")
	}

	result := make([]*model.Account, 0, len(accounts))
	for _, account := range accounts {
		result = append(result, toAccountDomain(account))
	}
	return result, nil
}

func (a *accountRepo) ListDebtAccountByPartner(ctx context.Context, params *model.AccountDebtListParams) ([]*model.Account, error) {
	defer a.repos.metrics.MonitoredTime(accountRepoName)("ListDebtAccountByPartner")

	if !params.PartnerCode.IsValid() {
		return nil, errors.Errorf("invalid partner code, partnerCode=%s", params.PartnerCode)
	}

	accounts, err := a.repos.Queries(ctx).ListDebtAccountByPartnerCode(ctx, &da.ListDebtAccountByPartnerCodeParams{
		IDGt:        params.GetAccountIDNext(),
		Limit:       params.GetLimit(),
		PartnerCode: params.PartnerCode.String(),
	})
	if err != nil {
		a.logger.WithContext(ctx).Errorf("ListDebtAccountByPartner failed: %v", err)
		return nil, errors.Wrap(err, "ListDebtAccountByPartner failed")
	}

	result := make([]*model.Account, 0, len(accounts))
	for _, account := range accounts {
		result = append(result, toAccountDomain(account))
	}
	return result, nil
}

func toAccountDomain(result *da.Accounts) *model.Account {
	return &model.Account{
		ID:                   result.ID,
		ZalopayID:            result.ZalopayID,
		Status:               toAccountStatus(result.Status),
		Origin:               model.AccountSourceFromString(result.Origin),
		PartnerCode:          partner.CodeFromString(result.PartnerCode),
		InstallmentLimit:     result.InstallmentLimit,
		InstallmentBalance:   result.InstallmentBalance,
		RepaymentBalance:     toRepaymentBalance(result.RepaymentBalance),
		PartnerAccountName:   result.PartnerAccountName,
		PartnerAccountNumber: result.PartnerAccountNumber,
		CreatedAt:            result.CreatedAt,
		UpdatedAt:            result.UpdatedAt,
	}
}

func fromAccountStatus(status model.AccountStatus) da.AccountsStatus {
	switch status {
	case model.StatusActive:
		return da.AccountsStatusActive
	case model.StatusInactive:
		return da.AccountsStatusInactive
	case model.StatusBlocked:
		return da.AccountsStatusBlocked
	case model.StatusClosed:
		return da.AccountsStatusClosed
	default:
		return da.AccountsStatus(status)
	}
}

func toAccountStatus(status da.AccountsStatus) model.AccountStatus {
	switch status {
	case da.AccountsStatusActive:
		return model.StatusActive
	case da.AccountsStatusInactive:
		return model.StatusInactive
	case da.AccountsStatusBlocked:
		return model.StatusBlocked
	case da.AccountsStatusClosed:
		return model.StatusClosed
	default:
		return model.AccountStatus(status)
	}
}

func fromRepaymentBalance(balance *int64) sql.NullInt64 {
	if balance == nil {
		return sql.NullInt64{}
	}
	return sql.NullInt64{Int64: *balance, Valid: true}
}

func toRepaymentBalance(balance sql.NullInt64) *int64 {
	if !balance.Valid {
		return nil
	}
	return &balance.Int64
}
