package repo

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
)

type contentRepo struct {
	repos   *Repository
	logger  *log.Helper
	svcRes  entity.ServiceResources
	svcPath string
}

func NewContentRepo(conf *config.Account, repos *Repository, kLogger log.Logger) _interface.ContentRepo {
	svcPath := conf.GetResources().GetServiceFilePath()
	svcRes := LoadResources(svcPath, entity.ServiceResources{})
	logger := log.NewHelper(log.With(kLogger, "module", "content-repo"))
	return &contentRepo{
		repos:   repos,
		logger:  logger,
		svcRes:  svcRes,
		svcPath: svcPath,
	}
}

func (f *contentRepo) initServiceResources() {
	f.svcRes = LoadResources(f.svcPath, entity.ServiceResources{})
}

func (f *contentRepo) GetProposedServices(_ context.Context) *model.ServicesProposed {
	defer f.repos.metrics.MonitoredTime(contentRepoName)("GetProposedServices")

	if f.svcRes.IsEmpty() {
		f.initServiceResources()
	}

	serviceRes := f.svcRes.Services
	serviceList := make([]*model.ServiceContent, 0, len(serviceRes))

	for _, service := range serviceRes {
		if !service.Enable {
			continue
		}
		serviceList = append(serviceList, &model.ServiceContent{
			Code: service.Code,
			Name: service.Name,
			ContentImage: model.ContentImage{
				IconUrl:      service.Image.IconUrl,
				CoverUrl:     service.Image.CoverUrl,
				ThumbnailUrl: service.Image.ThumbnailUrl,
			},
			Interaction: model.InteractionGuide{
				ZPAUrl: service.Interaction.ZPAUrl,
				ZPIUrl: service.Interaction.ZPIUrl,
			},
		})

	}

	return &model.ServicesProposed{
		Services: serviceList,
	}
}
