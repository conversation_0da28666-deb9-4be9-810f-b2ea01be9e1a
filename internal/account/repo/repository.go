package repo

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	jsoniter "github.com/json-iterator/go"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo/entity"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
)

var ProviderSet = wire.NewSet(
	NewRepository,
	NewAccountRepo,
	NewContentRepo,
)

type Repository struct {
	sqlDB   *sql.DB
	logger  *log.Helper
	metrics *metrics.RepositoryMetrics
}

type ctxTxKey struct{}

const (
	accountRepoName = "account"
	contentRepoName = "content"
)

func NewRepository(sqlDB *sql.DB, kLogger log.Logger) *Repository {
	repos := &Repository{sqlDB: sqlDB}
	repos.logger = log.NewHelper(log.With(kLogger, "module", "repository"))
	repos.metrics = metrics.NewRepositoryMetrics(metrics.Options{Module: metrics.MetricModuleRepository})
	return repos
}

func LoadResources[T entity.ServiceResources](path string, res T) T {
	filePath := filepath.Clean(path)
	fileObj, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
	if err != nil {
		fmt.Println("Error opening file: ", err)
		return res
	}
	defer fileObj.Close()

	fileBytes, err := os.ReadFile(fileObj.Name())
	if err != nil {
		fmt.Println("Error reading file: ", err)
		return res
	}

	err = jsoniter.Unmarshal(fileBytes, &res)
	if err != nil {
		return res
	}
	return res
}

func (r *Repository) WithTx(ctx context.Context, exec func(ctx context.Context) error) error {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return err
	}
	defer func() { _ = tx.Rollback() }()

	err = exec(context.WithValue(ctx, ctxTxKey{}, da.New(tx)))
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (r *Repository) Queries(ctx context.Context) *da.Queries {
	tx, ok := ctx.Value(ctxTxKey{}).(*da.Queries)
	if ok {
		return tx
	}
	return da.New(r.sqlDB)
}
