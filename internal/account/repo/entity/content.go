package entity

type ServiceData struct {
	Code        string           `json:"code"`
	Name        string           `json:"name"`
	Image       ContentImage     `json:"image"`
	Enable      bool             `json:"enable"`
	Interaction InteractionGuide `json:"interaction"`
}

type ServiceCategory struct{}

type ContentImage struct {
	IconUrl      string `json:"icon_url"`
	CoverUrl     string `json:"cover_url"`
	ThumbnailUrl string `json:"thumbnail_url"`
}

type InteractionGuide struct {
	ZPAUrl string `json:"zpa_url"`
	ZPIUrl string `json:"zpi_url"`
}

type ServiceResources struct {
	Categories []ServiceCategory `json:"categories"`
	Services   []ServiceData     `json:"services"`
}

func (s ServiceResources) IsEmpty() bool {
	return len(s.Services) == 0 || len(s.Categories) == 0
}
