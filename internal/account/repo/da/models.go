// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"time"
)

type AccountsStatus string

const (
	AccountsStatusActive   AccountsStatus = "active"
	AccountsStatusInactive AccountsStatus = "inactive"
	AccountsStatusClosed   AccountsStatus = "closed"
	AccountsStatusBlocked  AccountsStatus = "blocked"
)

func (e *AccountsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = AccountsStatus(s)
	case string:
		*e = AccountsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for AccountsStatus: %T", src)
	}
	return nil
}

type NullAccountsStatus struct {
	AccountsStatus AccountsStatus `json:"accounts_status"`
	Valid          bool           `json:"valid"` // Valid is true if AccountsStatus is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullAccountsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.AccountsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.AccountsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullAccountsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.AccountsStatus), nil
}

type Accounts struct {
	ID int64 `json:"id"`
	// ZaloPay user unique identifier
	ZalopayID int64 `json:"zalopay_id"`
	// Partner code in installment system
	PartnerCode string `json:"partner_code"`
	// Installment partner account name
	PartnerAccountName string `json:"partner_account_name"`
	// Installment partner account identifier
	PartnerAccountNumber string `json:"partner_account_number"`
	// The maximum installment account balance approved by the partner
	InstallmentLimit int64 `json:"installment_limit"`
	// Installment account available balance
	InstallmentBalance int64 `json:"installment_balance"`
	// Installment account repayment balance, optional by partner
	RepaymentBalance sql.NullInt64 `json:"repayment_balance"`
	// Installment account status
	Status     AccountsStatus `json:"status"`
	ExpiryDate sql.NullTime   `json:"expiry_date"`
	// Source that installment account was created from
	Origin    string    `json:"origin"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
