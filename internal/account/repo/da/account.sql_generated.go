// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: account.sql

package da

import (
	"context"
	"database/sql"
	"strings"
	"time"
)

const createAccount = `-- name: CreateAccount :execresult
INSERT INTO accounts (
    id, zalopay_id, partner_code,
    partner_account_name, partner_account_number,
    installment_limit, installment_balance,
    status, expiry_date, origin
) VALUES (uuid_short(), ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateAccountParams struct {
	ZalopayID            int64          `json:"zalopay_id"`
	PartnerCode          string         `json:"partner_code"`
	PartnerAccountName   string         `json:"partner_account_name"`
	PartnerAccountNumber string         `json:"partner_account_number"`
	InstallmentLimit     int64          `json:"installment_limit"`
	InstallmentBalance   int64          `json:"installment_balance"`
	Status               AccountsStatus `json:"status"`
	ExpiryDate           sql.NullTime   `json:"expiry_date"`
	Origin               string         `json:"origin"`
}

func (q *Queries) CreateAccount(ctx context.Context, arg *CreateAccountParams) (sql.Result, error) {
	return q.exec(ctx, q.createAccountStmt, createAccount,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.PartnerAccountName,
		arg.PartnerAccountNumber,
		arg.InstallmentLimit,
		arg.InstallmentBalance,
		arg.Status,
		arg.ExpiryDate,
		arg.Origin,
	)
}

const getAccountByID = `-- name: GetAccountByID :one
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE id = ?
`

func (q *Queries) GetAccountByID(ctx context.Context, id int64) (*Accounts, error) {
	row := q.queryRow(ctx, q.getAccountByIDStmt, getAccountByID, id)
	var i Accounts
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.PartnerAccountName,
		&i.PartnerAccountNumber,
		&i.InstallmentLimit,
		&i.InstallmentBalance,
		&i.RepaymentBalance,
		&i.Status,
		&i.ExpiryDate,
		&i.Origin,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getAccountByIDAndZalopayID = `-- name: GetAccountByIDAndZalopayID :one
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE zalopay_id = ? AND id = ?
`

type GetAccountByIDAndZalopayIDParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	ID        int64 `json:"id"`
}

func (q *Queries) GetAccountByIDAndZalopayID(ctx context.Context, arg *GetAccountByIDAndZalopayIDParams) (*Accounts, error) {
	row := q.queryRow(ctx, q.getAccountByIDAndZalopayIDStmt, getAccountByIDAndZalopayID, arg.ZalopayID, arg.ID)
	var i Accounts
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.PartnerAccountName,
		&i.PartnerAccountNumber,
		&i.InstallmentLimit,
		&i.InstallmentBalance,
		&i.RepaymentBalance,
		&i.Status,
		&i.ExpiryDate,
		&i.Origin,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getAccountByIDForUpdate = `-- name: GetAccountByIDForUpdate :one
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE id = ? FOR UPDATE
`

func (q *Queries) GetAccountByIDForUpdate(ctx context.Context, id int64) (*Accounts, error) {
	row := q.queryRow(ctx, q.getAccountByIDForUpdateStmt, getAccountByIDForUpdate, id)
	var i Accounts
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.PartnerAccountName,
		&i.PartnerAccountNumber,
		&i.InstallmentLimit,
		&i.InstallmentBalance,
		&i.RepaymentBalance,
		&i.Status,
		&i.ExpiryDate,
		&i.Origin,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getActiveAccountByPartnerCode = `-- name: GetActiveAccountByPartnerCode :one
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE zalopay_id = ?
  AND partner_code = ?
  AND status = 'active'
`

type GetActiveAccountByPartnerCodeParams struct {
	ZalopayID   int64  `json:"zalopay_id"`
	PartnerCode string `json:"partner_code"`
}

func (q *Queries) GetActiveAccountByPartnerCode(ctx context.Context, arg *GetActiveAccountByPartnerCodeParams) (*Accounts, error) {
	row := q.queryRow(ctx, q.getActiveAccountByPartnerCodeStmt, getActiveAccountByPartnerCode, arg.ZalopayID, arg.PartnerCode)
	var i Accounts
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.PartnerAccountName,
		&i.PartnerAccountNumber,
		&i.InstallmentLimit,
		&i.InstallmentBalance,
		&i.RepaymentBalance,
		&i.Status,
		&i.ExpiryDate,
		&i.Origin,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getLatestAccountByPartnerCode = `-- name: GetLatestAccountByPartnerCode :one
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE zalopay_id = ?
  AND partner_code = ?
ORDER BY id DESC LIMIT 1
`

type GetLatestAccountByPartnerCodeParams struct {
	ZalopayID   int64  `json:"zalopay_id"`
	PartnerCode string `json:"partner_code"`
}

func (q *Queries) GetLatestAccountByPartnerCode(ctx context.Context, arg *GetLatestAccountByPartnerCodeParams) (*Accounts, error) {
	row := q.queryRow(ctx, q.getLatestAccountByPartnerCodeStmt, getLatestAccountByPartnerCode, arg.ZalopayID, arg.PartnerCode)
	var i Accounts
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.PartnerCode,
		&i.PartnerAccountName,
		&i.PartnerAccountNumber,
		&i.InstallmentLimit,
		&i.InstallmentBalance,
		&i.RepaymentBalance,
		&i.Status,
		&i.ExpiryDate,
		&i.Origin,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const listAccountByPartnerCode = `-- name: ListAccountByPartnerCode :many
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE partner_code = ?
  AND id > ?
LIMIT ?
`

type ListAccountByPartnerCodeParams struct {
	PartnerCode string `json:"partner_code"`
	IDGt        int64  `json:"id_gt"`
	Limit       int32  `json:"limit"`
}

func (q *Queries) ListAccountByPartnerCode(ctx context.Context, arg *ListAccountByPartnerCodeParams) ([]*Accounts, error) {
	rows, err := q.query(ctx, q.listAccountByPartnerCodeStmt, listAccountByPartnerCode, arg.PartnerCode, arg.IDGt, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Accounts
	for rows.Next() {
		var i Accounts
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.PartnerCode,
			&i.PartnerAccountName,
			&i.PartnerAccountNumber,
			&i.InstallmentLimit,
			&i.InstallmentBalance,
			&i.RepaymentBalance,
			&i.Status,
			&i.ExpiryDate,
			&i.Origin,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAccountByZalopayIDs = `-- name: ListAccountByZalopayIDs :many
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE zalopay_id IN (/*SLICE:zalopay_ids*/?)
`

func (q *Queries) ListAccountByZalopayIDs(ctx context.Context, zalopayIds []int64) ([]*Accounts, error) {
	query := listAccountByZalopayIDs
	var queryParams []interface{}
	if len(zalopayIds) > 0 {
		for _, v := range zalopayIds {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:zalopay_ids*/?", strings.Repeat(",?", len(zalopayIds))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:zalopay_ids*/?", "NULL", 1)
	}
	rows, err := q.query(ctx, nil, query, queryParams...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Accounts
	for rows.Next() {
		var i Accounts
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.PartnerCode,
			&i.PartnerAccountName,
			&i.PartnerAccountNumber,
			&i.InstallmentLimit,
			&i.InstallmentBalance,
			&i.RepaymentBalance,
			&i.Status,
			&i.ExpiryDate,
			&i.Origin,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAccountForStmtSync = `-- name: ListAccountForStmtSync :many
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE id > ?
  AND partner_code IN (/*SLICE:partner_codes*/?)
  AND status IN ('active', 'blocked')
  AND created_at <= ?
LIMIT ?
`

type ListAccountForStmtSyncParams struct {
	IDGt         int64     `json:"id_gt"`
	PartnerCodes []string  `json:"partner_codes"`
	CreatedAtLte time.Time `json:"created_at_lte"`
	Limit        int32     `json:"limit"`
}

func (q *Queries) ListAccountForStmtSync(ctx context.Context, arg *ListAccountForStmtSyncParams) ([]*Accounts, error) {
	query := listAccountForStmtSync
	var queryParams []interface{}
	queryParams = append(queryParams, arg.IDGt)
	if len(arg.PartnerCodes) > 0 {
		for _, v := range arg.PartnerCodes {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:partner_codes*/?", strings.Repeat(",?", len(arg.PartnerCodes))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:partner_codes*/?", "NULL", 1)
	}
	queryParams = append(queryParams, arg.CreatedAtLte)
	queryParams = append(queryParams, arg.Limit)
	rows, err := q.query(ctx, nil, query, queryParams...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Accounts
	for rows.Next() {
		var i Accounts
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.PartnerCode,
			&i.PartnerAccountName,
			&i.PartnerAccountNumber,
			&i.InstallmentLimit,
			&i.InstallmentBalance,
			&i.RepaymentBalance,
			&i.Status,
			&i.ExpiryDate,
			&i.Origin,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listDebtAccountByPartnerCode = `-- name: ListDebtAccountByPartnerCode :many
SELECT id, zalopay_id, partner_code, partner_account_name, partner_account_number, installment_limit, installment_balance, repayment_balance, status, expiry_date, origin, created_at, updated_at FROM accounts
WHERE partner_code = ?
  AND installment_balance < installment_limit
  AND id > ?
LIMIT ?
`

type ListDebtAccountByPartnerCodeParams struct {
	PartnerCode string `json:"partner_code"`
	IDGt        int64  `json:"id_gt"`
	Limit       int32  `json:"limit"`
}

func (q *Queries) ListDebtAccountByPartnerCode(ctx context.Context, arg *ListDebtAccountByPartnerCodeParams) ([]*Accounts, error) {
	rows, err := q.query(ctx, q.listDebtAccountByPartnerCodeStmt, listDebtAccountByPartnerCode, arg.PartnerCode, arg.IDGt, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Accounts
	for rows.Next() {
		var i Accounts
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.PartnerCode,
			&i.PartnerAccountName,
			&i.PartnerAccountNumber,
			&i.InstallmentLimit,
			&i.InstallmentBalance,
			&i.RepaymentBalance,
			&i.Status,
			&i.ExpiryDate,
			&i.Origin,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAccountBalance = `-- name: UpdateAccountBalance :exec
UPDATE accounts
SET installment_limit = ?,
    installment_balance = ?,
    repayment_balance = ?
WHERE id = ?
`

type UpdateAccountBalanceParams struct {
	InstallmentLimit   int64         `json:"installment_limit"`
	InstallmentBalance int64         `json:"installment_balance"`
	RepaymentBalance   sql.NullInt64 `json:"repayment_balance"`
	ID                 int64         `json:"id"`
}

func (q *Queries) UpdateAccountBalance(ctx context.Context, arg *UpdateAccountBalanceParams) error {
	_, err := q.exec(ctx, q.updateAccountBalanceStmt, updateAccountBalance,
		arg.InstallmentLimit,
		arg.InstallmentBalance,
		arg.RepaymentBalance,
		arg.ID,
	)
	return err
}

const updateAccountBalanceAndStatus = `-- name: UpdateAccountBalanceAndStatus :exec
UPDATE accounts
SET status = ?,
    installment_limit = ?,
    installment_balance = ?,
    repayment_balance = ?
WHERE id = ?
`

type UpdateAccountBalanceAndStatusParams struct {
	Status             AccountsStatus `json:"status"`
	InstallmentLimit   int64          `json:"installment_limit"`
	InstallmentBalance int64          `json:"installment_balance"`
	RepaymentBalance   sql.NullInt64  `json:"repayment_balance"`
	ID                 int64          `json:"id"`
}

func (q *Queries) UpdateAccountBalanceAndStatus(ctx context.Context, arg *UpdateAccountBalanceAndStatusParams) error {
	_, err := q.exec(ctx, q.updateAccountBalanceAndStatusStmt, updateAccountBalanceAndStatus,
		arg.Status,
		arg.InstallmentLimit,
		arg.InstallmentBalance,
		arg.RepaymentBalance,
		arg.ID,
	)
	return err
}
