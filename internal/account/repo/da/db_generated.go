// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.createAccountStmt, err = db.PrepareContext(ctx, createAccount); err != nil {
		return nil, fmt.Errorf("error preparing query CreateAccount: %w", err)
	}
	if q.getAccountByIDStmt, err = db.PrepareContext(ctx, getAccountByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountByID: %w", err)
	}
	if q.getAccountByIDAndZalopayIDStmt, err = db.PrepareContext(ctx, getAccountByIDAndZalopayID); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountByIDAndZalopayID: %w", err)
	}
	if q.getAccountByIDForUpdateStmt, err = db.PrepareContext(ctx, getAccountByIDForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountByIDForUpdate: %w", err)
	}
	if q.getActiveAccountByPartnerCodeStmt, err = db.PrepareContext(ctx, getActiveAccountByPartnerCode); err != nil {
		return nil, fmt.Errorf("error preparing query GetActiveAccountByPartnerCode: %w", err)
	}
	if q.getLatestAccountByPartnerCodeStmt, err = db.PrepareContext(ctx, getLatestAccountByPartnerCode); err != nil {
		return nil, fmt.Errorf("error preparing query GetLatestAccountByPartnerCode: %w", err)
	}
	if q.listAccountByPartnerCodeStmt, err = db.PrepareContext(ctx, listAccountByPartnerCode); err != nil {
		return nil, fmt.Errorf("error preparing query ListAccountByPartnerCode: %w", err)
	}
	if q.listAccountByZalopayIDsStmt, err = db.PrepareContext(ctx, listAccountByZalopayIDs); err != nil {
		return nil, fmt.Errorf("error preparing query ListAccountByZalopayIDs: %w", err)
	}
	if q.listAccountForStmtSyncStmt, err = db.PrepareContext(ctx, listAccountForStmtSync); err != nil {
		return nil, fmt.Errorf("error preparing query ListAccountForStmtSync: %w", err)
	}
	if q.listDebtAccountByPartnerCodeStmt, err = db.PrepareContext(ctx, listDebtAccountByPartnerCode); err != nil {
		return nil, fmt.Errorf("error preparing query ListDebtAccountByPartnerCode: %w", err)
	}
	if q.updateAccountBalanceStmt, err = db.PrepareContext(ctx, updateAccountBalance); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateAccountBalance: %w", err)
	}
	if q.updateAccountBalanceAndStatusStmt, err = db.PrepareContext(ctx, updateAccountBalanceAndStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateAccountBalanceAndStatus: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.createAccountStmt != nil {
		if cerr := q.createAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createAccountStmt: %w", cerr)
		}
	}
	if q.getAccountByIDStmt != nil {
		if cerr := q.getAccountByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountByIDStmt: %w", cerr)
		}
	}
	if q.getAccountByIDAndZalopayIDStmt != nil {
		if cerr := q.getAccountByIDAndZalopayIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountByIDAndZalopayIDStmt: %w", cerr)
		}
	}
	if q.getAccountByIDForUpdateStmt != nil {
		if cerr := q.getAccountByIDForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountByIDForUpdateStmt: %w", cerr)
		}
	}
	if q.getActiveAccountByPartnerCodeStmt != nil {
		if cerr := q.getActiveAccountByPartnerCodeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getActiveAccountByPartnerCodeStmt: %w", cerr)
		}
	}
	if q.getLatestAccountByPartnerCodeStmt != nil {
		if cerr := q.getLatestAccountByPartnerCodeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getLatestAccountByPartnerCodeStmt: %w", cerr)
		}
	}
	if q.listAccountByPartnerCodeStmt != nil {
		if cerr := q.listAccountByPartnerCodeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listAccountByPartnerCodeStmt: %w", cerr)
		}
	}
	if q.listAccountByZalopayIDsStmt != nil {
		if cerr := q.listAccountByZalopayIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listAccountByZalopayIDsStmt: %w", cerr)
		}
	}
	if q.listAccountForStmtSyncStmt != nil {
		if cerr := q.listAccountForStmtSyncStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listAccountForStmtSyncStmt: %w", cerr)
		}
	}
	if q.listDebtAccountByPartnerCodeStmt != nil {
		if cerr := q.listDebtAccountByPartnerCodeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listDebtAccountByPartnerCodeStmt: %w", cerr)
		}
	}
	if q.updateAccountBalanceStmt != nil {
		if cerr := q.updateAccountBalanceStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateAccountBalanceStmt: %w", cerr)
		}
	}
	if q.updateAccountBalanceAndStatusStmt != nil {
		if cerr := q.updateAccountBalanceAndStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateAccountBalanceAndStatusStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                                DBTX
	tx                                *sql.Tx
	createAccountStmt                 *sql.Stmt
	getAccountByIDStmt                *sql.Stmt
	getAccountByIDAndZalopayIDStmt    *sql.Stmt
	getAccountByIDForUpdateStmt       *sql.Stmt
	getActiveAccountByPartnerCodeStmt *sql.Stmt
	getLatestAccountByPartnerCodeStmt *sql.Stmt
	listAccountByPartnerCodeStmt      *sql.Stmt
	listAccountByZalopayIDsStmt       *sql.Stmt
	listAccountForStmtSyncStmt        *sql.Stmt
	listDebtAccountByPartnerCodeStmt  *sql.Stmt
	updateAccountBalanceStmt          *sql.Stmt
	updateAccountBalanceAndStatusStmt *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                                tx,
		tx:                                tx,
		createAccountStmt:                 q.createAccountStmt,
		getAccountByIDStmt:                q.getAccountByIDStmt,
		getAccountByIDAndZalopayIDStmt:    q.getAccountByIDAndZalopayIDStmt,
		getAccountByIDForUpdateStmt:       q.getAccountByIDForUpdateStmt,
		getActiveAccountByPartnerCodeStmt: q.getActiveAccountByPartnerCodeStmt,
		getLatestAccountByPartnerCodeStmt: q.getLatestAccountByPartnerCodeStmt,
		listAccountByPartnerCodeStmt:      q.listAccountByPartnerCodeStmt,
		listAccountByZalopayIDsStmt:       q.listAccountByZalopayIDsStmt,
		listAccountForStmtSyncStmt:        q.listAccountForStmtSyncStmt,
		listDebtAccountByPartnerCodeStmt:  q.listDebtAccountByPartnerCodeStmt,
		updateAccountBalanceStmt:          q.updateAccountBalanceStmt,
		updateAccountBalanceAndStatusStmt: q.updateAccountBalanceAndStatusStmt,
	}
}
