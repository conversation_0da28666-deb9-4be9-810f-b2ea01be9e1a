// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
)

type Querier interface {
	CreateAccount(ctx context.Context, arg *CreateAccountParams) (sql.Result, error)
	GetAccountByID(ctx context.Context, id int64) (*Accounts, error)
	GetAccountByIDAndZalopayID(ctx context.Context, arg *GetAccountByIDAndZalopayIDParams) (*Accounts, error)
	GetAccountByIDForUpdate(ctx context.Context, id int64) (*Accounts, error)
	GetActiveAccountByPartnerCode(ctx context.Context, arg *GetActiveAccountByPartnerCodeParams) (*Accounts, error)
	GetLatestAccountByPartnerCode(ctx context.Context, arg *GetLatestAccountByPartnerCodeParams) (*Accounts, error)
	ListAccountByPartnerCode(ctx context.Context, arg *ListAccountByPartnerCodeParams) ([]*Accounts, error)
	ListAccountByZalopayIDs(ctx context.Context, zalopayIds []int64) ([]*Accounts, error)
	ListAccountForStmtSync(ctx context.Context, arg *ListAccountForStmtSyncParams) ([]*Accounts, error)
	ListDebtAccountByPartnerCode(ctx context.Context, arg *ListDebtAccountByPartnerCodeParams) ([]*Accounts, error)
	UpdateAccountBalance(ctx context.Context, arg *UpdateAccountBalanceParams) error
	UpdateAccountBalanceAndStatus(ctx context.Context, arg *UpdateAccountBalanceAndStatusParams) error
}

var _ Querier = (*Queries)(nil)
