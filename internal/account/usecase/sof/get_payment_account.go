package sof

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

const (
	fixedMinEmiAmount = 150_000
)

var (
	otaAppIDs = []int32{69, 606, 612, 677, 678, 2643}
	// instValidatingAppIDs is the list of appIDs that need to validate the installment info in order request
	instValidatingAppIDs = append([]int32{}, otaAppIDs...)
)

func (s Usecase) GetAccountForPayment(ctx context.Context,
	req dto.GetAccountForPaymentParams) (*dto.GetAccountForPaymentResult, error) {
	logger := s.logger.WithContext(ctx)

	instEligible, err := s.isInstallmentApplicableForOrder(req.OrderReqInfo)
	if err != nil {
		logger.Errorf("validate installment applicable for this order has error, %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeConditionFailed, "validate order installment info failed").
			WithCause(err).WithKind(errorkit.TypePrecondition)
	}
	if !instEligible {
		return s.buildAccountNotApplyOrderResponse(), nil
	}

	account, err := s.accRepo.GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode)
	if errors.Is(err, model.ErrAccountNotFound) {
		logger.Warnw("msg", "account not found", "zalopayID", req.ZalopayID, "partnerCode", req.PartnerCode)
		return s.handleAccountInvalidResponse(ctx, req)
	}
	if err != nil {
		logger.Errorf("get account by partner failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get account by partner failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infow(
		"msg", "get account by partner success",
		"account_id", account.ID,
		"zalopay_id", account.ZalopayID,
		"partner_code", account.PartnerCode,
		"available_balance", account.InstallmentBalance,
	)

	fCtx, fCancel := context.WithTimeout(ctx, time.Second)
	defer fCancel()

	fraudInfo, err := s.fraudSvc.CheckFraud(fCtx, &dto.UserFraudCheckParams{
		ZalopayID:          req.ZalopayID,
		PartnerCode:        req.PartnerCode,
		OrderInfo:          req.OrderReqInfo,
		ClientInfo:         req.ClientReqInfo,
		RequestTime:        time.Now().UnixMilli(),
		AccountLimit:       account.GetInstallmentLimit(),
		AccountBalance:     account.GetInstallmentBalance(),
		AccountOutstanding: account.GetInstallmentOutstanding(),
	})
	if err != nil && !errors.Is(err, model.ErrTimeout) {
		logger.Errorf("check fraud has error, %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallRiskFailed, "call check fraud failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if fraudInfo != nil && fraudInfo.HasFraud {
		logger.Warnw("msg", "account has fraud", "fraudInfo", fraudInfo)
		return s.buildAccountFraudResponse(fraudInfo), nil
	}

	if account.Status == model.StatusActive {
		return s.buildAccountActiveResponse(account), nil
	}
	if account.Status == model.StatusBlocked {
		return s.buildAccountBlockedResponse(account), nil
	}

	return &dto.GetAccountForPaymentResult{
		Account:   account,
		SofStatus: model.SofInactive,
	}, nil
}

func (s Usecase) isInstallmentApplicableForOrder(order model.OrderRequest) (bool, error) {
	if !slices.Contains(instValidatingAppIDs, order.AppID) {
		return true, nil
	}
	if order.IsEmpty() || order.EmbedData == "" {
		return false, nil
	}

	var orderEmbed model.OrderEmbedData
	if err := json.Unmarshal([]byte(order.EmbedData), &orderEmbed); err != nil {
		return false, errors.Errorf("unmarshal order embed data failed: %v", err)
	}
	if !orderEmbed.Installment.Eligible {
		return false, nil
	}
	return true, nil
}

func (s Usecase) buildAccountActiveResponse(account *model.Account) *dto.GetAccountForPaymentResult {
	// Default case
	emiAmount := utils.FormatCurrencyKMB(fixedMinEmiAmount)

	result := &dto.GetAccountForPaymentResult{
		Account:   account,
		SofStatus: model.SofActive,
		Action:    &model.Action{Type: model.CTATypePlanSelection, Title: model.CTASelectPlan.String()},
		Message:   &model.Message{Text: fmt.Sprintf(model.MessageMinEmiAmount.String(), emiAmount)},
	}

	return result
}

func (s Usecase) buildAccountBlockedResponse(account *model.Account) *dto.GetAccountForPaymentResult {
	return &dto.GetAccountForPaymentResult{
		Account:   account,
		SofStatus: model.SofInactive,
		Message:   &model.Message{Text: model.MessageAccountLocked.String()},
	}
}

func (s Usecase) buildAccountFraudResponse(fraudInfo *dto.UserFraudCheckResult) *dto.GetAccountForPaymentResult {
	message := model.MessageAccountHitRiskRule.String()
	if fraudInfo.InfoMessage != "" {
		message = fraudInfo.InfoMessage
	}
	return &dto.GetAccountForPaymentResult{
		SofStatus: model.SofRiskRule,
		Message:   &model.Message{Text: message},
	}
}

func (s Usecase) buildAccountNotApplyOrderResponse() *dto.GetAccountForPaymentResult {
	return &dto.GetAccountForPaymentResult{
		SofStatus: model.SofNotApplicable,
		Message:   &model.Message{Text: model.MessageNotApplicableOrder.String()},
	}
}

func (s Usecase) handleAccountInvalidResponse(ctx context.Context,
	req dto.GetAccountForPaymentParams) (*dto.GetAccountForPaymentResult, error) {
	logger := s.logger.WithContext(ctx)
	onboarding, err := s.obdSvc.QueryOnboardingStatus(ctx, req.ZalopayID, req.PartnerCode.String())
	if err != nil {
		logger.Errorf("query onboarding status failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallOnboardingFailed, "query onboarding status failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	result := &dto.GetAccountForPaymentResult{
		SofStatus: model.SofInactive,
	}

	appLink := s.deeplinks.GetApplicationLink()
	appLinkUtm := s.deeplinks.GetApplicationLinkUtm("cashier")

	switch {
	case onboarding.IsUnregister:
		result.SofStatus = model.SofNotRegister
		result.Message = &model.Message{
			Text: model.MessageAccountNotRegister.String(),
		}
		result.Action = &model.Action{
			Type:   model.CTATypeDeepLink,
			Title:  model.CTARegister.String(),
			ZpiUrl: appLink.ZPIUrl,
			ZpaUrl: appLink.ZPAUrl,
		}
	case onboarding.IsOnboarding:
		result.SofStatus = model.SofInOnboarding
		result.Message = &model.Message{
			Text: model.MessageOnboardingInProgress.String(),
		}
		result.Action = &model.Action{
			Type:   model.CTATypeDeepLink,
			Title:  model.CTADetail.String(),
			ZpiUrl: appLink.ZPIUrl,
			ZpaUrl: appLink.ZPAUrl,
		}
	case onboarding.IsRejected:
		result.SofStatus = model.SofInOnboarding
		result.Message = &model.Message{
			Text: model.MessageOnboardingIsRejected.String(),
		}
		result.Action = &model.Action{
			Type:   model.CTATypeDeepLink,
			Title:  model.CTADetail.String(),
			ZpiUrl: appLinkUtm.ZPIUrl,
			ZpaUrl: appLinkUtm.ZPAUrl,
		}
	}

	return result, nil
}
