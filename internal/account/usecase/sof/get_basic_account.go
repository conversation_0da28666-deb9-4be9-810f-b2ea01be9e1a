package sof

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s Usecase) GetAccountForValidity(ctx context.Context,
	req dto.GetAccountForValidityParams) (*dto.GetAccountForValidityResult, error) {
	logger := s.logger.WithContext(ctx)

	account, err := s.accRepo.GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode)
	if errors.Is(err, model.ErrAccountNotFound) {
		logger.Warnw("msg", "account not found", "zalopayID", req.ZalopayID, "partnerCode", req.PartnerCode)
		return nil, errorkit.
			NewError(errorkit.CodeAccountNotFound, "account not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("get account by partner failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get account by partner failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infow(
		"msg", "get account by partner success",
		"account_id", account.ID,
		"zalopay_id", account.ZalopayID,
		"partner_code", account.PartnerCode,
		"available_balance", account.InstallmentBalance,
	)

	if account.Status != model.StatusActive {
		logger.Warnw("msg", "account is not active", "account_status", account.Status)
	}

	return &dto.GetAccountForValidityResult{
		Account:   account,
		SofStatus: fromAccountToSofStatus(account.Status),
	}, nil
}

func fromAccountToSofStatus(status model.AccountStatus) model.SoFStatus {
	switch status {
	case model.StatusActive:
		return model.SofActive
	default:
		return model.SofInactive
	}
}
