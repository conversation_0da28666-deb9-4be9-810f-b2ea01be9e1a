package sof

import (
	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/utils"
)

type Usecase struct {
	accRepo   _interface.AccountRepo
	obdSvc    _interface.OnboardingService
	fraudSvc  _interface.FraudService
	deeplinks utils.DeepLinkCraft
	kLogger   log.Logger
	logger    *log.Helper
}

func NewUsecase(
	accRepo _interface.AccountRepo,
	obdSvc _interface.OnboardingService,
	fraudSvc _interface.FraudService,
	deeplinks utils.DeepLinkCraft,
	kLogger log.Logger) *Usecase {
	logging := log.With(kLogger, "usecase", "sof")
	logger := log.NewHelper(logging)
	return &Usecase{
		obdSvc:    obdSvc,
		fraudSvc:  fraudSvc,
		accRepo:   accRepo,
		deeplinks: deeplinks,
		kLogger:   kLogger,
		logger:    logger,
	}
}
