package sof

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/mocks/repository"
	utils_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/utils/mocks"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

// SOFTestSuite defines the test suite for SOF package
type SOFTestSuite struct {
	suite.Suite
	uc            *Usecase
	ctrl          *gomock.Controller
	accountRepo   *repository_mocks.MockAccountRepo
	contentRepo   *repository_mocks.MockContentRepo
	cimbService   *adapter_mocks.MockCIMBService
	onboardingSvc *adapter_mocks.MockOnboardingService
	fraudService  *adapter_mocks.MockFraudService
	deeplinkCraft *utils_mocks.MockDeepLinkCraft
}

// SetupTest runs before each test
func (s *SOFTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.accountRepo = repository_mocks.NewMockAccountRepo(s.ctrl)
	s.contentRepo = repository_mocks.NewMockContentRepo(s.ctrl)
	s.cimbService = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.onboardingSvc = adapter_mocks.NewMockOnboardingService(s.ctrl)
	s.fraudService = adapter_mocks.NewMockFraudService(s.ctrl)
	s.deeplinkCraft = utils_mocks.NewMockDeepLinkCraft(s.ctrl)
	s.uc = NewUsecase(s.accountRepo, s.onboardingSvc, s.fraudService, s.deeplinkCraft, log.GetLogger())

	s.deeplinkCraft.EXPECT().GetApplicationLink().Return(model.DeepLinkData{
		ZPIUrl:    "https://example.com",
		ZPAUrl:    "https://example.com",
		CommonUrl: "https://example.com",
	}).AnyTimes()
	s.deeplinkCraft.EXPECT().GetApplicationLinkUtm("cashier").Return(model.DeepLinkData{
		ZPIUrl: "https://example.com",
		ZPAUrl: "https://example.com",
	}).AnyTimes()
}

// TearDownTest runs after each test
func (s *SOFTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestSOFTestSuite runs the test suite
func TestSOFTestSuite(t *testing.T) {
	suite.Run(t, new(SOFTestSuite))
}

func (s *SOFTestSuite) TestGetAccountForPayment() {
	ctx := context.Background()
	req := dto.GetAccountForPaymentParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
		OrderReqInfo: model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":true}}`,
		},
	}

	s.Run("success - account active", func() {
		account := &model.Account{
			ID:                 1,
			ZalopayID:          123,
			PartnerCode:        "CIMB",
			Status:             model.StatusActive,
			InstallmentLimit:   1000000,
			InstallmentBalance: 1000000,
		}

		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(&dto.UserFraudCheckResult{HasFraud: false}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(account, result.Account)
		s.Equal(model.SofActive, result.SofStatus)
		s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
	})

	s.Run("success - account blocked", func() {
		account := &model.Account{
			ID:        1,
			ZalopayID: 123,
			Status:    model.StatusBlocked,
		}

		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(&dto.UserFraudCheckResult{HasFraud: false}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofInactive, result.SofStatus)
		s.Equal(model.MessageAccountLocked.String(), result.Message.Text)
	})

	s.Run("success - account blocked but check risk timeout", func() {
		account := &model.Account{
			ID:        1,
			ZalopayID: 123,
			Status:    model.StatusBlocked,
		}

		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(nil, model.ErrTimeout)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofInactive, result.SofStatus)
		s.Equal(model.MessageAccountLocked.String(), result.Message.Text)
	})

	s.Run("success - account has fraud with default message", func() {
		account := &model.Account{
			ID:        1,
			ZalopayID: 123,
		}

		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(&dto.UserFraudCheckResult{HasFraud: true}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofRiskRule, result.SofStatus)
		s.Equal(model.MessageAccountHitRiskRule.String(), result.Message.Text)
	})

	s.Run("success - account has fraud with custom message", func() {
		account := &model.Account{
			ID:        1,
			ZalopayID: 123,
		}

		customMessage := "You have exceeded your credit limit"
		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(&dto.UserFraudCheckResult{
				HasFraud:    true,
				InfoMessage: customMessage,
			}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofRiskRule, result.SofStatus)
		s.Equal(customMessage, result.Message.Text)
	})

	s.Run("success - account not found, user unregistered", func() {
		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(nil, model.ErrAccountNotFound)

		s.onboardingSvc.EXPECT().QueryOnboardingStatus(gomock.Any(), req.ZalopayID, req.PartnerCode.String()).
			Return(&model.OnboardingStatus{OnboardingStatusFlag: model.OnboardingStatusFlag{
				IsUnregister: true,
			}}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofNotRegister, result.SofStatus)
		s.Equal(model.MessageAccountNotRegister.String(), result.Message.Text)
	})

	s.Run("success - account not found, user in onboarding", func() {
		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(nil, model.ErrAccountNotFound)

		s.onboardingSvc.EXPECT().QueryOnboardingStatus(gomock.Any(), req.ZalopayID, req.PartnerCode.String()).
			Return(&model.OnboardingStatus{OnboardingStatusFlag: model.OnboardingStatusFlag{
				IsOnboarding: true,
			}}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofInOnboarding, result.SofStatus)
		s.Equal(model.MessageOnboardingInProgress.String(), result.Message.Text)
	})

	s.Run("success - account not found, user rejected", func() {
		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(nil, model.ErrAccountNotFound)

		s.onboardingSvc.EXPECT().QueryOnboardingStatus(gomock.Any(), req.ZalopayID, req.PartnerCode.String()).
			Return(&model.OnboardingStatus{OnboardingStatusFlag: model.OnboardingStatusFlag{
				IsRejected: true,
			}}, nil)

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofInOnboarding, result.SofStatus)
		s.Equal(model.MessageOnboardingIsRejected.String(), result.Message.Text)
	})

	s.Run("error - get account failed", func() {
		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(nil, errors.New("db error"))

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.Error(err)
		s.Nil(result)
	})

	s.Run("error - check fraud failed", func() {
		account := &model.Account{
			ID:        1,
			ZalopayID: 123,
		}

		s.accountRepo.EXPECT().GetLatestAccountByPartner(gomock.Any(), req.ZalopayID, req.PartnerCode).
			Return(account, nil)

		s.fraudService.EXPECT().CheckFraud(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("fraud service error"))

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.Error(err)
		s.Nil(result)
	})

	s.Run("success - not applicable for order", func() {
		req := dto.GetAccountForPaymentParams{
			ZalopayID:   123,
			PartnerCode: "CIMB",
			OrderReqInfo: model.OrderRequest{
				AppID:     69,
				EmbedData: `{"installment":{"eligible":false}}`,
			},
		}

		result, err := s.uc.GetAccountForPayment(ctx, req)

		s.NoError(err)
		s.Equal(model.SofNotApplicable, result.SofStatus)
		s.Equal(model.MessageNotApplicableOrder.String(), result.Message.Text)
	})
}

func (s *SOFTestSuite) TestIsInstallmentApplicableForOrder() {
	s.Run("success - app not in validation list", func() {
		order := model.OrderRequest{
			AppID: 1,
		}

		result, err := s.uc.isInstallmentApplicableForOrder(order)

		s.NoError(err)
		s.True(result)
	})

	s.Run("success - app in validation list, installment eligible", func() {
		order := model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":true}}`,
		}

		result, err := s.uc.isInstallmentApplicableForOrder(order)

		s.NoError(err)
		s.True(result)
	})

	s.Run("success - app in validation list, installment not eligible", func() {
		order := model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":false}}`,
		}

		result, err := s.uc.isInstallmentApplicableForOrder(order)

		s.NoError(err)
		s.False(result)
	})

	s.Run("error - invalid embed data", func() {
		order := model.OrderRequest{
			AppID:     69,
			EmbedData: `invalid json`,
		}

		result, err := s.uc.isInstallmentApplicableForOrder(order)

		s.Error(err)
		s.False(result)
	})
}

func (s *SOFTestSuite) TestGetAccountForValidity_Success() {
	ctx := context.Background()
	req := dto.GetAccountForValidityParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
		OrderReqInfo: model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":true}}`,
		},
	}

	account := &model.Account{
		ID:                 1,
		ZalopayID:          123,
		PartnerCode:        "CIMB",
		Status:             model.StatusActive,
		InstallmentLimit:   1000000,
		InstallmentBalance: 1000000,
	}

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode).
		Return(account, nil)

	result, err := s.uc.GetAccountForValidity(ctx, req)

	s.NoError(err)
	s.Equal(account, result.Account)
	s.Equal(model.SofActive, result.SofStatus)
}

func (s *SOFTestSuite) TestGetAccountForValidity_AccountNotFound() {
	ctx := context.Background()
	req := dto.GetAccountForValidityParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
		OrderReqInfo: model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":true}}`,
		},
	}

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode).
		Return(nil, model.ErrAccountNotFound)

	result, err := s.uc.GetAccountForValidity(ctx, req)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeNotFound, errorkit.GetErrorKind(err))
}

func (s *SOFTestSuite) TestGetAccountForValidity_AccountNotActive() {
	ctx := context.Background()
	req := dto.GetAccountForValidityParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
	}

	account := &model.Account{
		ID:        1,
		ZalopayID: 123,
		Status:    model.StatusBlocked,
	}

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode).
		Return(account, nil)

	result, err := s.uc.GetAccountForValidity(ctx, req)

	s.Nil(err)
	s.Equal(model.SofInactive, result.SofStatus)
}

func (s *SOFTestSuite) TestGetAccountForValidity_RepositoryError() {
	ctx := context.Background()
	req := dto.GetAccountForValidityParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
		OrderReqInfo: model.OrderRequest{
			AppID:     69,
			EmbedData: `{"installment":{"eligible":true}}`,
		},
	}

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, req.ZalopayID, req.PartnerCode).
		Return(nil, errors.New("db error"))

	result, err := s.uc.GetAccountForValidity(ctx, req)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *SOFTestSuite) TestGetAccountForPayment_ParseEmbedDataError() {
	ctx := context.Background()
	req := dto.GetAccountForPaymentParams{
		ZalopayID:   123,
		PartnerCode: "CIMB",
		OrderReqInfo: model.OrderRequest{
			AppID:     69,
			EmbedData: `invalid json`,
		},
	}

	result, err := s.uc.GetAccountForPayment(ctx, req)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypePrecondition, errorkit.GetErrorKind(err))
}
