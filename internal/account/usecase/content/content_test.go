package content

import (
	"testing"

	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/mocks/repository"
)

type ContentTestSuite struct {
	suite.Suite
	uc          *Usecase
	ctrl        *gomock.Controller
	contentRepo *repository_mocks.MockContentRepo
}

// SetupTest runs before each test
func (s *ContentTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.contentRepo = repository_mocks.NewMockContentRepo(s.ctrl)
	s.uc = NewUsecase(s.contentRepo, log.GetLogger())
}

// TearDownTest runs after each test
func (s *ContentTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestContentTestSuite runs the test suite
func TestContentTestSuite(t *testing.T) {
	suite.Run(t, new(ContentTestSuite))
}

func (s *ContentTestSuite) TestGetProposedServices() {
	ctx := context.Background()
	req := &dto.GetContentRequest{}

	s.Run("success", func() {
		expectedContent := &model.ServicesProposed{
			Services: []*model.ServiceContent{
				{
					Code: "service_code",
					Name: "service_name",
					ContentImage: model.ContentImage{
						IconUrl:      "icon-url",
						CoverUrl:     "cover-url",
						ThumbnailUrl: "thumbnail-url",
					},
				},
			},
		}

		s.contentRepo.EXPECT().GetProposedServices(ctx).Return(expectedContent)

		result, err := s.uc.GetProposedServices(ctx, req)

		s.NoError(err)
		s.Equal(expectedContent.Services, result.Services)
	})
}
