package content

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
)

// Usecase is an Account usecase.
type Usecase struct {
	contentRepo _interface.ContentRepo
	kLogger     log.Logger
	logger      *log.Helper
}

// NewUsecase new an Account usecase.
func NewUsecase(
	contentRepo _interface.ContentRepo,
	logger log.Logger) *Usecase {
	return &Usecase{
		kLogger:     logger,
		logger:      log.<PERSON><PERSON>per(logger),
		contentRepo: contentRepo,
	}
}

// GetProposedServices get proposed services.
func (uc *Usecase) GetProposedServices(ctx context.Context, _ *dto.GetContentRequest) (*dto.GetContentResponse, error) {
	result := uc.contentRepo.GetProposedServices(ctx)
	return (*dto.GetContentResponse)(result), nil
}
