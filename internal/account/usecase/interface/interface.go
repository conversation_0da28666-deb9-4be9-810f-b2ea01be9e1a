package _interface

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

//go:generate mockgen --destination=../mocks/repository/account.go --package=repository_mocks . AccountRepo
type AccountRepo interface {
	CreateAccount(ctx context.Context, params *model.AccountCreateParams) (accountID int64, err error)
	UpdateAccountBalance(ctx context.Context, params *model.Account) error
	GetAccountByID(ctx context.Context, accountID int64) (*model.Account, error)
	GetAccountByIDForUpdate(ctx context.Context, accountID int64) (*model.Account, error)
	GetAccountByIDAndZalopayID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error)
	GetActiveAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error)
	GetLatestAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error)
	ListAccountByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Account, error)
	ListAccountByPartner(ctx context.Context, params *model.AccountListByPartnerParams) ([]*model.Account, error)
	ListAccountForStmtSync(ctx context.Context, params *model.AccountListStmtSyncParams) ([]*model.Account, error)
	ListDebtAccountByPartner(ctx context.Context, params *model.AccountDebtListParams) ([]*model.Account, error)
}

//go:generate mockgen --destination=../mocks/repository/content.go --package=repository_mocks . ContentRepo
type ContentRepo interface {
	GetProposedServices(ctx context.Context) *model.ServicesProposed
}

//go:generate mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
type CIMBService interface {
	QueryAccountInfo(ctx context.Context, zalopayID int64) (*model.PartnerAccountInfo, error)
	QueryAccountBalance(ctx context.Context, zalopayID int64, accountNumber string) (*model.PartnerAccountBalance, error)
	QueryFullAccountData(ctx context.Context, zalopayID int64) (*model.PartnerAccount, error)
}

//go:generate mockgen --destination=../mocks/adapter/onboarding_service.go --package=adapter_mocks . OnboardingService
type OnboardingService interface {
	QueryOnboardingStatus(ctx context.Context, zalopayID int64, partnerCode string) (*model.OnboardingStatus, error)
	ListOnboardingByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.OnboardingData, error)
}

//go:generate mockgen --destination=../mocks/adapter/outstanding_service.go --package=adapter_mocks . OutstandingService
type OutstandingService interface {
	GetOutstandingInfo(ctx context.Context, zalopayID int64, accountID int64) (*model.Outstanding, error)
}

//go:generate mockgen --destination=../mocks/adapter/fraud_service.go --package=adapter_mocks . FraudService
type FraudService interface {
	CheckFraud(ctx context.Context, data *dto.UserFraudCheckParams) (*dto.UserFraudCheckResult, error)
}

//go:generate mockgen --destination=../mocks/adapter/crm_event_publisher.go --package=adapter_mocks . CRMEventPublisher
type CRMEventPublisher interface {
	PublishAccountCreatedEvent(ctx context.Context, event *dto.AccountCreatedEvent) error
}

//go:generate mockgen --destination=../mocks/adapter/dist_lock.go --package=adapter_mocks . DistributedLock
type DistributedLock interface {
	// Acquire attempts to acquire the lock for the specified resource.
	// It returns an error if the lock could not be acquired.
	Acquire(ctx context.Context, resource string, ttl time.Duration) error

	// Release releases the lock for the specified resource.
	// It returns an error if the lock could not be released.
	Release(ctx context.Context, resource string) error

	// IsLocked checks if the specified resource is currently locked.
	// It returns a boolean indicating the lock status and an error if the status could not be determined.
	IsLocked(ctx context.Context, resource string) (bool, error)

	AcquireBalanceSyncing(ctx context.Context, resource string) error
}
