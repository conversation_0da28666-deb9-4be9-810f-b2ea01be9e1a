package dto

import (
	"fmt"
	"time"

	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type CreateAccountParams struct {
	ZalopayID    int64
	OnboardingID int64
	PartnerCode  partner.PartnerCode
}

type GetAccountForValidityParams struct {
	ZalopayID    int64
	PartnerCode  partner.PartnerCode
	OrderReqInfo model.OrderRequest
}

type GetAccountForValidityResult struct {
	Account   *model.Account
	SofStatus model.SoFStatus
}

type GetAccountForPaymentParams struct {
	ZalopayID     int64
	PartnerCode   partner.PartnerCode
	OrderReqInfo  model.OrderRequest
	ClientReqInfo model.ClientRequest
}

type GetAccountForPaymentResult struct {
	Account   *model.Account
	Action    *model.Action
	Message   *model.Message
	SofStatus model.SoFStatus
}

type GetAccountCompleteParams struct {
	ZalopayID   int64
	PartnerCode partner.PartnerCode
}

type GetAccountCompleteResult struct {
	Account     *model.Account
	Outstanding *model.Outstanding
}

type SyncAccountBalanceParams struct {
	ZalopayID int64          `json:"zalopay_id"`
	AccountID int64          `json:"account_id"`
	Polling   bool           `json:"polling"`
	EventData map[string]any `json:"event_data"`
}

type SyncAccountBalancesParams struct {
	PartnerCode partner.PartnerCode `json:"partner_code"`
}

type SyncDebtAccountBalancesParams struct {
	PartnerCode partner.PartnerCode `json:"partner_code"`
}

type AccountsSyncStats struct {
	TotalItems   int `json:"total_items"`
	TotalSuccess int `json:"total_success"`
	TotalFailed  int `json:"total_failed"`
}

// AccountBalanceResult simplify the model.Account to be used in the service layer
// and hide sensitive information like PartnerAccountName, PartnerAccountNumber
type AccountBalanceResult struct {
	ZalopayID          int64
	AccountID          int64
	PartnerCode        partner.PartnerCode
	AccountStatus      model.AccountStatus
	InstallmentLimit   int64
	InstallmentBalance int64
}

type AccountBindingItem struct {
	ZalopayID  int64
	Account    *model.Account
	Onboarding *model.OnboardingData
}

type AccountBindingResult []*AccountBindingItem

type AccountCreatedEvent struct {
	Version   string                `json:"version"` // version is use to manage schema evolution
	EventID   string                `json:"event_id"`
	EventType string                `json:"event_type"` // event_type is use to identify the event if using same topic/channel
	EventTime int64                 `json:"event_time"`
	ZalopayID string                `json:"zalopay_id"`
	Payload   AccountCreatedPayload `json:"payload"`
}

// PayloadData represents the payload structure for AccountCreatedEvent
type AccountCreatedPayload struct {
	AccountID            int64  `json:"account_id"`
	OnboardingID         int64  `json:"onboarding_id"`
	PartnerCode          string `json:"partner_code"`
	AccountCreatedAt     string `json:"account_created_at"`
	InstallmentLimit     int64  `json:"installment_limit"`
	InstallmentBalance   int64  `json:"installment_balance"`
	PartnerAccountName   string `json:"partner_account_name"`
	PartnerAccountNumber string `json:"partner_account_number"`
}

func (e *AccountCreatedEvent) FromOnboardAndAccount(onboardID int64, accountInfo *model.Account) *AccountCreatedEvent {
	eventID := fmt.Sprintf("%d-%d", accountInfo.ID, onboardID)
	eventType := "AccountCreated"
	eventTime := time.Now().UnixMilli()
	eventPayload := AccountCreatedPayload{
		OnboardingID:         onboardID,
		AccountID:            accountInfo.ID,
		PartnerCode:          accountInfo.PartnerCode.String(),
		AccountCreatedAt:     accountInfo.CreatedAt.Format(time.RFC3339),
		InstallmentLimit:     accountInfo.InstallmentLimit,
		InstallmentBalance:   accountInfo.InstallmentBalance,
		PartnerAccountName:   accountInfo.PartnerAccountName,
		PartnerAccountNumber: accountInfo.PartnerAccountNumber,
	}
	return &AccountCreatedEvent{
		Version:   "1.0",
		EventID:   eventID,
		EventType: eventType,
		EventTime: eventTime,
		ZalopayID: cast.ToString(accountInfo.ZalopayID),
		Payload:   eventPayload,
	}
}
