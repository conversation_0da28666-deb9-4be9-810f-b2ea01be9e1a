package dto

import (
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type UserFraudCheckParams struct {
	ZalopayID          int64
	RequestTime        int64
	PartnerCode        partner.PartnerCode
	OrderInfo          model.OrderRequest
	ClientInfo         model.ClientRequest
	AccountLimit       int64
	AccountBalance     int64
	AccountOutstanding int64
}

type UserFraudCheckResult struct {
	HasFraud    bool
	RiskCode    int32
	InfoCode    int32
	ActionCode  int32
	InfoMessage string
}
