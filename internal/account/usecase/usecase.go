package usecase

import (
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/account"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/content"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/sof"
)

var ProviderSet = wire.NewSet(
	sof.NewUsecase,
	account.NewUsecase,
	content.NewUsecase,
)
