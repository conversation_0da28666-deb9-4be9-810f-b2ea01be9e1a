// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: DistributedLock)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/dist_lock.go --package=adapter_mocks . DistributedLock
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockDistributedLock is a mock of DistributedLock interface.
type MockDistributedLock struct {
	ctrl     *gomock.Controller
	recorder *MockDistributedLockMockRecorder
	isgomock struct{}
}

// MockDistributedLockMockRecorder is the mock recorder for MockDistributedLock.
type MockDistributedLockMockRecorder struct {
	mock *MockDistributedLock
}

// NewMockDistributedLock creates a new mock instance.
func NewMockDistributedLock(ctrl *gomock.Controller) *MockDistributedLock {
	mock := &MockDistributedLock{ctrl: ctrl}
	mock.recorder = &MockDistributedLockMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributedLock) EXPECT() *MockDistributedLockMockRecorder {
	return m.recorder
}

// Acquire mocks base method.
func (m *MockDistributedLock) Acquire(ctx context.Context, resource string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Acquire", ctx, resource, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// Acquire indicates an expected call of Acquire.
func (mr *MockDistributedLockMockRecorder) Acquire(ctx, resource, ttl any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Acquire", reflect.TypeOf((*MockDistributedLock)(nil).Acquire), ctx, resource, ttl)
}

// AcquireBalanceSyncing mocks base method.
func (m *MockDistributedLock) AcquireBalanceSyncing(ctx context.Context, resource string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireBalanceSyncing", ctx, resource)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireBalanceSyncing indicates an expected call of AcquireBalanceSyncing.
func (mr *MockDistributedLockMockRecorder) AcquireBalanceSyncing(ctx, resource any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireBalanceSyncing", reflect.TypeOf((*MockDistributedLock)(nil).AcquireBalanceSyncing), ctx, resource)
}

// IsLocked mocks base method.
func (m *MockDistributedLock) IsLocked(ctx context.Context, resource string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsLocked", ctx, resource)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsLocked indicates an expected call of IsLocked.
func (mr *MockDistributedLockMockRecorder) IsLocked(ctx, resource any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsLocked", reflect.TypeOf((*MockDistributedLock)(nil).IsLocked), ctx, resource)
}

// Release mocks base method.
func (m *MockDistributedLock) Release(ctx context.Context, resource string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", ctx, resource)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockDistributedLockMockRecorder) Release(ctx, resource any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockDistributedLock)(nil).Release), ctx, resource)
}
