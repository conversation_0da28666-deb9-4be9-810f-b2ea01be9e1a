// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: OutstandingService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/outstanding_service.go --package=adapter_mocks . OutstandingService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	gomock "go.uber.org/mock/gomock"
)

// MockOutstandingService is a mock of OutstandingService interface.
type MockOutstandingService struct {
	ctrl     *gomock.Controller
	recorder *MockOutstandingServiceMockRecorder
	isgomock struct{}
}

// MockOutstandingServiceMockRecorder is the mock recorder for MockOutstandingService.
type MockOutstandingServiceMockRecorder struct {
	mock *MockOutstandingService
}

// NewMockOutstandingService creates a new mock instance.
func NewMockOutstandingService(ctrl *gomock.Controller) *MockOutstandingService {
	mock := &MockOutstandingService{ctrl: ctrl}
	mock.recorder = &MockOutstandingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOutstandingService) EXPECT() *MockOutstandingServiceMockRecorder {
	return m.recorder
}

// GetOutstandingInfo mocks base method.
func (m *MockOutstandingService) GetOutstandingInfo(ctx context.Context, zalopayID, accountID int64) (*model.Outstanding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOutstandingInfo", ctx, zalopayID, accountID)
	ret0, _ := ret[0].(*model.Outstanding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOutstandingInfo indicates an expected call of GetOutstandingInfo.
func (mr *MockOutstandingServiceMockRecorder) GetOutstandingInfo(ctx, zalopayID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOutstandingInfo", reflect.TypeOf((*MockOutstandingService)(nil).GetOutstandingInfo), ctx, zalopayID, accountID)
}
