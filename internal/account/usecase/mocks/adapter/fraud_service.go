// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: FraudService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/fraud_service.go --package=adapter_mocks . FraudService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockFraudService is a mock of FraudService interface.
type MockFraudService struct {
	ctrl     *gomock.Controller
	recorder *MockFraudServiceMockRecorder
	isgomock struct{}
}

// MockFraudServiceMockRecorder is the mock recorder for MockFraudService.
type MockFraudServiceMockRecorder struct {
	mock *MockFraudService
}

// NewMockFraudService creates a new mock instance.
func NewMockFraudService(ctrl *gomock.Controller) *MockFraudService {
	mock := &MockFraudService{ctrl: ctrl}
	mock.recorder = &MockFraudServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFraudService) EXPECT() *MockFraudServiceMockRecorder {
	return m.recorder
}

// CheckFraud mocks base method.
func (m *MockFraudService) CheckFraud(ctx context.Context, data *dto.UserFraudCheckParams) (*dto.UserFraudCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFraud", ctx, data)
	ret0, _ := ret[0].(*dto.UserFraudCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFraud indicates an expected call of CheckFraud.
func (mr *MockFraudServiceMockRecorder) CheckFraud(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFraud", reflect.TypeOf((*MockFraudService)(nil).CheckFraud), ctx, data)
}
