// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: CRMEventPublisher)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/crm_event_publisher.go --package=adapter_mocks . CRMEventPublisher
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockCRMEventPublisher is a mock of CRMEventPublisher interface.
type MockCRMEventPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockCRMEventPublisherMockRecorder
	isgomock struct{}
}

// MockCRMEventPublisherMockRecorder is the mock recorder for MockCRMEventPublisher.
type MockCRMEventPublisherMockRecorder struct {
	mock *MockCRMEventPublisher
}

// NewMockCRMEventPublisher creates a new mock instance.
func NewMockCRMEventPublisher(ctrl *gomock.Controller) *MockCRMEventPublisher {
	mock := &MockCRMEventPublisher{ctrl: ctrl}
	mock.recorder = &MockCRMEventPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCRMEventPublisher) EXPECT() *MockCRMEventPublisherMockRecorder {
	return m.recorder
}

// PublishAccountCreatedEvent mocks base method.
func (m *MockCRMEventPublisher) PublishAccountCreatedEvent(ctx context.Context, event *dto.AccountCreatedEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishAccountCreatedEvent", ctx, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishAccountCreatedEvent indicates an expected call of PublishAccountCreatedEvent.
func (mr *MockCRMEventPublisherMockRecorder) PublishAccountCreatedEvent(ctx, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishAccountCreatedEvent", reflect.TypeOf((*MockCRMEventPublisher)(nil).PublishAccountCreatedEvent), ctx, event)
}
