// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: CIMBService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	gomock "go.uber.org/mock/gomock"
)

// MockCIMBService is a mock of CIMBService interface.
type MockCIMBService struct {
	ctrl     *gomock.Controller
	recorder *MockCIMBServiceMockRecorder
	isgomock struct{}
}

// MockCIMBServiceMockRecorder is the mock recorder for MockCIMBService.
type MockCIMBServiceMockRecorder struct {
	mock *MockCIMBService
}

// NewMockCIMBService creates a new mock instance.
func NewMockCIMBService(ctrl *gomock.Controller) *MockCIMBService {
	mock := &MockCIMBService{ctrl: ctrl}
	mock.recorder = &MockCIMBServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCIMBService) EXPECT() *MockCIMBServiceMockRecorder {
	return m.recorder
}

// QueryAccountBalance mocks base method.
func (m *MockCIMBService) QueryAccountBalance(ctx context.Context, zalopayID int64, accountNumber string) (*model.PartnerAccountBalance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAccountBalance", ctx, zalopayID, accountNumber)
	ret0, _ := ret[0].(*model.PartnerAccountBalance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAccountBalance indicates an expected call of QueryAccountBalance.
func (mr *MockCIMBServiceMockRecorder) QueryAccountBalance(ctx, zalopayID, accountNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAccountBalance", reflect.TypeOf((*MockCIMBService)(nil).QueryAccountBalance), ctx, zalopayID, accountNumber)
}

// QueryAccountInfo mocks base method.
func (m *MockCIMBService) QueryAccountInfo(ctx context.Context, zalopayID int64) (*model.PartnerAccountInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAccountInfo", ctx, zalopayID)
	ret0, _ := ret[0].(*model.PartnerAccountInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAccountInfo indicates an expected call of QueryAccountInfo.
func (mr *MockCIMBServiceMockRecorder) QueryAccountInfo(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAccountInfo", reflect.TypeOf((*MockCIMBService)(nil).QueryAccountInfo), ctx, zalopayID)
}

// QueryFullAccountData mocks base method.
func (m *MockCIMBService) QueryFullAccountData(ctx context.Context, zalopayID int64) (*model.PartnerAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryFullAccountData", ctx, zalopayID)
	ret0, _ := ret[0].(*model.PartnerAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryFullAccountData indicates an expected call of QueryFullAccountData.
func (mr *MockCIMBServiceMockRecorder) QueryFullAccountData(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryFullAccountData", reflect.TypeOf((*MockCIMBService)(nil).QueryFullAccountData), ctx, zalopayID)
}
