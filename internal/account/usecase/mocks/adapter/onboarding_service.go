// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: OnboardingService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/onboarding_service.go --package=adapter_mocks . OnboardingService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	gomock "go.uber.org/mock/gomock"
)

// MockOnboardingService is a mock of OnboardingService interface.
type MockOnboardingService struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingServiceMockRecorder
	isgomock struct{}
}

// MockOnboardingServiceMockRecorder is the mock recorder for MockOnboardingService.
type MockOnboardingServiceMockRecorder struct {
	mock *MockOnboardingService
}

// NewMockOnboardingService creates a new mock instance.
func NewMockOnboardingService(ctrl *gomock.Controller) *MockOnboardingService {
	mock := &MockOnboardingService{ctrl: ctrl}
	mock.recorder = &MockOnboardingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingService) EXPECT() *MockOnboardingServiceMockRecorder {
	return m.recorder
}

// ListOnboardingByUserIDs mocks base method.
func (m *MockOnboardingService) ListOnboardingByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.OnboardingData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnboardingByUserIDs", ctx, zalopayIDs)
	ret0, _ := ret[0].([]*model.OnboardingData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnboardingByUserIDs indicates an expected call of ListOnboardingByUserIDs.
func (mr *MockOnboardingServiceMockRecorder) ListOnboardingByUserIDs(ctx, zalopayIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnboardingByUserIDs", reflect.TypeOf((*MockOnboardingService)(nil).ListOnboardingByUserIDs), ctx, zalopayIDs)
}

// QueryOnboardingStatus mocks base method.
func (m *MockOnboardingService) QueryOnboardingStatus(ctx context.Context, zalopayID int64, partnerCode string) (*model.OnboardingStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryOnboardingStatus", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(*model.OnboardingStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOnboardingStatus indicates an expected call of QueryOnboardingStatus.
func (mr *MockOnboardingServiceMockRecorder) QueryOnboardingStatus(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOnboardingStatus", reflect.TypeOf((*MockOnboardingService)(nil).QueryOnboardingStatus), ctx, zalopayID, partnerCode)
}
