// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: ContentRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/content.go --package=repository_mocks . ContentRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	gomock "go.uber.org/mock/gomock"
)

// MockContentRepo is a mock of ContentRepo interface.
type MockContentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockContentRepoMockRecorder
	isgomock struct{}
}

// MockContentRepoMockRecorder is the mock recorder for MockContentRepo.
type MockContentRepoMockRecorder struct {
	mock *MockContentRepo
}

// NewMockContentRepo creates a new mock instance.
func NewMockContentRepo(ctrl *gomock.Controller) *MockContentRepo {
	mock := &MockContentRepo{ctrl: ctrl}
	mock.recorder = &MockContentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContentRepo) EXPECT() *MockContentRepoMockRecorder {
	return m.recorder
}

// GetProposedServices mocks base method.
func (m *MockContentRepo) GetProposedServices(ctx context.Context) *model.ServicesProposed {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProposedServices", ctx)
	ret0, _ := ret[0].(*model.ServicesProposed)
	return ret0
}

// GetProposedServices indicates an expected call of GetProposedServices.
func (mr *MockContentRepoMockRecorder) GetProposedServices(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposedServices", reflect.TypeOf((*MockContentRepo)(nil).GetProposedServices), ctx)
}
