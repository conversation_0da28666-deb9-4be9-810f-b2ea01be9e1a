// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface (interfaces: AccountRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/account.go --package=repository_mocks . AccountRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockAccountRepo is a mock of AccountRepo interface.
type MockAccountRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAccountRepoMockRecorder
	isgomock struct{}
}

// MockAccountRepoMockRecorder is the mock recorder for MockAccountRepo.
type MockAccountRepoMockRecorder struct {
	mock *MockAccountRepo
}

// NewMockAccountRepo creates a new mock instance.
func NewMockAccountRepo(ctrl *gomock.Controller) *MockAccountRepo {
	mock := &MockAccountRepo{ctrl: ctrl}
	mock.recorder = &MockAccountRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountRepo) EXPECT() *MockAccountRepoMockRecorder {
	return m.recorder
}

// CreateAccount mocks base method.
func (m *MockAccountRepo) CreateAccount(ctx context.Context, params *model.AccountCreateParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAccount", ctx, params)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAccount indicates an expected call of CreateAccount.
func (mr *MockAccountRepoMockRecorder) CreateAccount(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccount", reflect.TypeOf((*MockAccountRepo)(nil).CreateAccount), ctx, params)
}

// GetAccountByID mocks base method.
func (m *MockAccountRepo) GetAccountByID(ctx context.Context, accountID int64) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByID", ctx, accountID)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByID indicates an expected call of GetAccountByID.
func (mr *MockAccountRepoMockRecorder) GetAccountByID(ctx, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByID", reflect.TypeOf((*MockAccountRepo)(nil).GetAccountByID), ctx, accountID)
}

// GetAccountByIDAndZalopayID mocks base method.
func (m *MockAccountRepo) GetAccountByIDAndZalopayID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByIDAndZalopayID", ctx, zalopayID, accountID)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByIDAndZalopayID indicates an expected call of GetAccountByIDAndZalopayID.
func (mr *MockAccountRepoMockRecorder) GetAccountByIDAndZalopayID(ctx, zalopayID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByIDAndZalopayID", reflect.TypeOf((*MockAccountRepo)(nil).GetAccountByIDAndZalopayID), ctx, zalopayID, accountID)
}

// GetAccountByIDForUpdate mocks base method.
func (m *MockAccountRepo) GetAccountByIDForUpdate(ctx context.Context, accountID int64) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByIDForUpdate", ctx, accountID)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByIDForUpdate indicates an expected call of GetAccountByIDForUpdate.
func (mr *MockAccountRepoMockRecorder) GetAccountByIDForUpdate(ctx, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByIDForUpdate", reflect.TypeOf((*MockAccountRepo)(nil).GetAccountByIDForUpdate), ctx, accountID)
}

// GetActiveAccountByPartner mocks base method.
func (m *MockAccountRepo) GetActiveAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveAccountByPartner", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveAccountByPartner indicates an expected call of GetActiveAccountByPartner.
func (mr *MockAccountRepoMockRecorder) GetActiveAccountByPartner(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveAccountByPartner", reflect.TypeOf((*MockAccountRepo)(nil).GetActiveAccountByPartner), ctx, zalopayID, partnerCode)
}

// GetLatestAccountByPartner mocks base method.
func (m *MockAccountRepo) GetLatestAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestAccountByPartner", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestAccountByPartner indicates an expected call of GetLatestAccountByPartner.
func (mr *MockAccountRepoMockRecorder) GetLatestAccountByPartner(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestAccountByPartner", reflect.TypeOf((*MockAccountRepo)(nil).GetLatestAccountByPartner), ctx, zalopayID, partnerCode)
}

// ListAccountByPartner mocks base method.
func (m *MockAccountRepo) ListAccountByPartner(ctx context.Context, params *model.AccountListByPartnerParams) ([]*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountByPartner", ctx, params)
	ret0, _ := ret[0].([]*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountByPartner indicates an expected call of ListAccountByPartner.
func (mr *MockAccountRepoMockRecorder) ListAccountByPartner(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountByPartner", reflect.TypeOf((*MockAccountRepo)(nil).ListAccountByPartner), ctx, params)
}

// ListAccountByUserIDs mocks base method.
func (m *MockAccountRepo) ListAccountByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountByUserIDs", ctx, zalopayIDs)
	ret0, _ := ret[0].([]*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountByUserIDs indicates an expected call of ListAccountByUserIDs.
func (mr *MockAccountRepoMockRecorder) ListAccountByUserIDs(ctx, zalopayIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountByUserIDs", reflect.TypeOf((*MockAccountRepo)(nil).ListAccountByUserIDs), ctx, zalopayIDs)
}

// ListAccountForStmtSync mocks base method.
func (m *MockAccountRepo) ListAccountForStmtSync(ctx context.Context, params *model.AccountListStmtSyncParams) ([]*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountForStmtSync", ctx, params)
	ret0, _ := ret[0].([]*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountForStmtSync indicates an expected call of ListAccountForStmtSync.
func (mr *MockAccountRepoMockRecorder) ListAccountForStmtSync(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountForStmtSync", reflect.TypeOf((*MockAccountRepo)(nil).ListAccountForStmtSync), ctx, params)
}

// ListDebtAccountByPartner mocks base method.
func (m *MockAccountRepo) ListDebtAccountByPartner(ctx context.Context, params *model.AccountDebtListParams) ([]*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDebtAccountByPartner", ctx, params)
	ret0, _ := ret[0].([]*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDebtAccountByPartner indicates an expected call of ListDebtAccountByPartner.
func (mr *MockAccountRepoMockRecorder) ListDebtAccountByPartner(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDebtAccountByPartner", reflect.TypeOf((*MockAccountRepo)(nil).ListDebtAccountByPartner), ctx, params)
}

// UpdateAccountBalance mocks base method.
func (m *MockAccountRepo) UpdateAccountBalance(ctx context.Context, params *model.Account) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountBalance", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountBalance indicates an expected call of UpdateAccountBalance.
func (mr *MockAccountRepoMockRecorder) UpdateAccountBalance(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountBalance", reflect.TypeOf((*MockAccountRepo)(nil).UpdateAccountBalance), ctx, params)
}
