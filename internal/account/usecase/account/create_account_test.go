package account

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

func (s *AccountTestSuite) TestCreateAccount_Success() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	accountInfo := &model.PartnerAccountInfo{
		AccountName:   "Test Account",
		AccountNumber: "*********",
		ExpiryDate:    time.Now().Add(24 * time.Hour),
	}
	accountBalance := &model.PartnerAccountBalance{
		OverdraftLimit:         1000000,
		AvailableBalance:       1000000,
		OverdraftBalanceStatus: model.PartnerODBalanceStatusNormal,
	}
	partnerAccount := &model.PartnerAccount{
		AccountInfo:    accountInfo,
		AccountBalance: accountBalance,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(nil, nil)

	s.cimbService.EXPECT().QueryFullAccountData(ctx, params.ZalopayID).
		Return(partnerAccount, nil)

	s.accountRepo.EXPECT().CreateAccount(ctx, gomock.Any()).
		Return(int64(789), nil)

		// Add expectations for the goroutine calls
	s.accountRepo.EXPECT().
		GetAccountByID(gomock.Any(), int64(789)).
		Return(&model.Account{
			ID:        789,
			ZalopayID: params.ZalopayID,
			Status:    model.StatusActive,
		}, nil)

	s.crmEvt.EXPECT().
		PublishAccountCreatedEvent(gomock.Any(), gomock.Any()).
		Return(nil)

	result, err := s.uc.CreateAccount(ctx, params)

	time.Sleep(time.Second)

	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(789), result.ID)
	s.Equal(params.ZalopayID, result.ZalopayID)
}

func (s *AccountTestSuite) TestCreateAccount_AccountAlreadyExists() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	existingAccount := &model.Account{
		ID:        789,
		ZalopayID: params.ZalopayID,
		Status:    model.StatusActive,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(existingAccount, nil)

	result, err := s.uc.CreateAccount(ctx, params)

	s.NoError(err)
	s.Equal(existingAccount, result)
}

func (s *AccountTestSuite) TestCreateAccount_QueryPartnerAccountFailed() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(nil, nil)

	s.cimbService.EXPECT().QueryFullAccountData(ctx, params.ZalopayID).
		Return(nil, errors.New("connection error"))

	result, err := s.uc.CreateAccount(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRemoteCall, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestCreateAccount_PartnerAccountNotFound() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(nil, nil)

	s.cimbService.EXPECT().QueryFullAccountData(ctx, params.ZalopayID).
		Return(nil, nil)

	result, err := s.uc.CreateAccount(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeNotFound, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestCreateAccount_InvalidPartnerAccountData() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(nil, nil)

	s.cimbService.EXPECT().QueryFullAccountData(ctx, params.ZalopayID).
		Return(&model.PartnerAccount{}, nil)

	result, err := s.uc.CreateAccount(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeValidation, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestCreateAccount_CreateAccountFailed() {
	ctx := context.Background()
	params := &dto.CreateAccountParams{
		ZalopayID:    123,
		PartnerCode:  "CIMB",
		OnboardingID: 456,
	}

	accountInfo := &model.PartnerAccountInfo{
		AccountName:   "Test Account",
		AccountNumber: "*********",
		ExpiryDate:    time.Now().Add(24 * time.Hour),
	}
	accountBalance := &model.PartnerAccountBalance{
		OverdraftLimit:         1000000,
		AvailableBalance:       1000000,
		OverdraftBalanceStatus: model.PartnerODBalanceStatusNormal,
	}
	partnerAccount := &model.PartnerAccount{
		AccountInfo:    accountInfo,
		AccountBalance: accountBalance,
	}

	s.accountRepo.EXPECT().GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode).
		Return(nil, nil)

	s.cimbService.EXPECT().QueryFullAccountData(ctx, params.ZalopayID).
		Return(partnerAccount, nil)

	s.accountRepo.EXPECT().CreateAccount(ctx, gomock.Any()).
		Return(int64(0), errors.New("db error"))

	result, err := s.uc.CreateAccount(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestPublishAccountCreatedEvent_Success() {
	ctx := context.Background()
	onboardID := int64(456)
	accountID := int64(789)

	account := &model.Account{
		ID:        accountID,
		ZalopayID: 123,
		Status:    model.StatusActive,
	}

	s.accountRepo.EXPECT().GetAccountByID(context.WithoutCancel(ctx), accountID).
		Return(account, nil)

	s.crmEvt.EXPECT().PublishAccountCreatedEvent(context.WithoutCancel(ctx), gomock.Any()).
		Return(nil)

	err := s.uc.PublishAccountCreatedEvent(ctx, onboardID, accountID)

	s.NoError(err)
}

func (s *AccountTestSuite) TestPublishAccountCreatedEvent_GetAccountFailed() {
	ctx := context.Background()
	onboardID := int64(456)
	accountID := int64(789)

	s.accountRepo.EXPECT().GetAccountByID(context.WithoutCancel(ctx), accountID).
		Return(nil, errors.New("db error"))

	err := s.uc.PublishAccountCreatedEvent(ctx, onboardID, accountID)

	s.Error(err)
}

func (s *AccountTestSuite) TestPublishAccountCreatedEvent_PublishEventFailed() {
	ctx := context.Background()
	onboardID := int64(456)
	accountID := int64(789)

	account := &model.Account{
		ID:        accountID,
		ZalopayID: 123,
		Status:    model.StatusActive,
	}

	s.accountRepo.EXPECT().GetAccountByID(context.WithoutCancel(ctx), accountID).
		Return(account, nil)

	s.crmEvt.EXPECT().PublishAccountCreatedEvent(context.WithoutCancel(ctx), gomock.Any()).
		Return(errors.New("publish error")).Times(6)

	err := s.uc.PublishAccountCreatedEvent(ctx, onboardID, accountID)

	s.Error(err)
}
