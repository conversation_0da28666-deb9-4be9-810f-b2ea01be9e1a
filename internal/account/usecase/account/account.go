package account

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"

	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
)

const (
	basAccBatchSize       = 256
	debtAccBatchSize      = 256
	workerAccBalanceSync  = 10
	channelAccBalanceSize = 10
)

// Usecase is an Account usecase.
type Usecase struct {
	logger      *log.Helper
	kLogger     log.Logger
	sleep       func(d time.Duration) // easy to mock in test
	accRepo     _interface.AccountRepo
	cimbConn    _interface.CIMBService
	distLock    _interface.DistributedLock
	outsSvc     _interface.OutstandingService
	obdSvc      _interface.OnboardingService
	crmEvt      _interface.CRMEventPublisher
	redisKeyGen keygen.RedisKeyGenerator
}

// NewUsecase new an Account usecase.
func NewUsecase(
	accRepo _interface.AccountRepo,
	cimbConn _interface.CIMBService,
	outsSvc _interface.OutstandingService,
	obdSvc _interface.OnboardingService,
	crmEvt _interface.CRMEventPublisher,
	distLock _interface.DistributedLock,
	redisKeyGen keygen.RedisKeyGenerator,
	logger log.Logger) *Usecase {
	return &Usecase{
		accRepo:     accRepo,
		cimbConn:    cimbConn,
		redisKeyGen: redisKeyGen,
		distLock:    distLock,
		outsSvc:     outsSvc,
		obdSvc:      obdSvc,
		crmEvt:      crmEvt,
		kLogger:     logger,
		sleep:       time.Sleep,
		logger:      log.NewHelper(logger),
	}
}
