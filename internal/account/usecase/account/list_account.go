package account

import (
	"context"
	"slices"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (uc *Usecase) ListAccountForStmtSync(ctx context.Context,
	params *model.AccountListStmtSyncParams) ([]*model.Account, error) {
	accounts, err := uc.accRepo.ListAccountForStmtSync(ctx, params)
	if err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "list account for stmt sync failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return accounts, nil
}

func (uc *Usecase) BuildAccountsByPartnerCh(ctx context.Context, partnerCode partner.PartnerCode) (<-chan *model.Account, error) {
	logger := uc.logger.WithContext(ctx)
	result := make(chan *model.Account, basAccBatchSize)

	go func() {
		lastID := int64(0)
		defer close(result)
		for {
			params := &model.AccountListByPartnerParams{
				AccountListingBase: model.AccountListingBase{
					Limit:       basAccBatchSize,
					AccountIDGt: lastID,
					PartnerCode: partnerCode,
				},
			}
			accts, err := uc.accRepo.ListAccountByPartner(ctx, params)
			if err != nil {
				logger.Errorf("list account by partner failed: %v", err)
				return
			}
			if len(accts) == 0 {
				logger.Infof("no more accounts by partner %s", partnerCode)
				break
			}
			for _, acct := range accts {
				result <- acct
			}
			lastID = accts[len(accts)-1].ID
		}
	}()

	return result, nil
}

func (uc *Usecase) BuildDebtAccountsByPartnerCh(ctx context.Context, partnerCode partner.PartnerCode) (<-chan *model.Account, error) {
	logger := uc.logger.WithContext(ctx)
	result := make(chan *model.Account, debtAccBatchSize)

	go func() {
		lastID := int64(0)
		defer close(result)
		for {
			params := &model.AccountDebtListParams{
				AccountListingBase: model.AccountListingBase{
					Limit:       debtAccBatchSize,
					AccountIDGt: lastID,
					PartnerCode: partnerCode,
				},
			}
			accts, err := uc.accRepo.ListDebtAccountByPartner(ctx, params)
			if err != nil {
				logger.Errorf("list debt account by partner failed: %v", err)
				return
			}
			if len(accts) == 0 {
				logger.Infof("no more debt account by partner %s", partnerCode)
				break
			}
			for _, acct := range accts {
				result <- acct
			}
			lastID = accts[len(accts)-1].ID
		}
	}()

	return result, nil
}

func (uc *Usecase) ListAccountBindings(ctx context.Context, zalopayIDs []int64) (dto.AccountBindingResult, error) {
	zalopayIDs = dedupZalopayIDs(zalopayIDs)
	userBindings := initUserBindings(zalopayIDs)

	userOnboards, err := uc.obdSvc.ListOnboardingByUserIDs(ctx, zalopayIDs)
	if err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeCallOnboardingFailed, "call get list onboarding by user ids failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	userOnboardsMap := zutils.KeyBy(userOnboards, func(onboard *model.OnboardingData) int64 { return onboard.ZalopayID })

	userAccounts, err := uc.accRepo.ListAccountByUserIDs(ctx, zalopayIDs)
	if err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "list account by user ids failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	userAccountsMap := zutils.KeyBy(userAccounts, func(account *model.Account) int64 { return account.ZalopayID })

	for key, value := range userBindings {
		value.ZalopayID = key
		value.Account = userAccountsMap[key]
		value.Onboarding = userOnboardsMap[key]
	}

	return zutils.Values(userBindings), nil
}

func initUserBindings(zalopayIDs []int64) map[int64]*dto.AccountBindingItem {
	userBindings := make(map[int64]*dto.AccountBindingItem)
	for _, zalopayID := range zalopayIDs {
		userBindings[zalopayID] = &dto.AccountBindingItem{}
	}
	return userBindings
}

func dedupZalopayIDs(zalopayIDs []int64) []int64 {
	uniqueZalopayIDs := make(map[int64]struct{})
	for _, zalopayID := range zalopayIDs {
		uniqueZalopayIDs[zalopayID] = struct{}{}
	}

	result := make([]int64, 0, len(uniqueZalopayIDs))
	for zalopayID := range uniqueZalopayIDs {
		result = append(result, zalopayID)
	}
	slices.Sort(result)
	return result
}
