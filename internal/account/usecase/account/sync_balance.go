package account

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/platform/common/retry"
)

const (
	syncBalanceLockKeyPattern = "balance:syncing:%d:%d"
)

func (uc *Usecase) SyncAccountBalance(ctx context.Context,
	params *dto.SyncAccountBalanceParams) (*model.Account, error) {
	lockKey := fmt.Sprintf(syncBalanceLockKeyPattern, params.ZalopayID, params.AccountID)
	lockKey, err := uc.redisKeyGen.Generate(lockKey)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("failed to generate lock key, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	if err = uc.distLock.AcquireBalanceSyncing(ctx, lockKey); err != nil {
		uc.logger.WithContext(ctx).Errorf("failed to acquire lock, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to acquire lock").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	defer uc.distLock.Release(ctx, lockKey)

	acc, err := uc.accRepo.GetAccountByIDAndZalopayID(ctx, params.ZalopayID, params.AccountID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("failed to get account, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "account not found").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	accBalance, err := uc.cimbConn.QueryAccountBalance(ctx, acc.ZalopayID, acc.PartnerAccountNumber)
	if err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to query account balance").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	acc.SetStatus(accBalance.OverdraftBalanceStatus.ToAccountStatus())
	acc.SetBalances(accBalance.OverdraftLimit, accBalance.AvailableBalance, &accBalance.RepaymentBalance)

	if err = uc.accRepo.UpdateAccountBalance(ctx, acc); err != nil {
		uc.logger.WithContext(ctx).Errorf("failed to update account balance, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "update account balance failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return acc, nil
}

func (uc *Usecase) SyncAccountBalancesByPartner(ctx context.Context, partnerCode partner.PartnerCode) (*dto.AccountsSyncStats, error) {
	logger := uc.logger.WithContext(ctx)

	listAccCh, err := uc.BuildAccountsByPartnerCh(ctx, partnerCode)
	if err != nil {
		logger.WithContext(ctx).Errorf("failed to build accounts channel, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to build accounts channel").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if listAccCh == nil {
		logger.Warn("no account channel for sync")
		return &dto.AccountsSyncStats{}, nil
	}

	succCount := atomic.Int64{}
	failCount := atomic.Int64{}
	syncResCh := make(chan any, workerAccBalanceSync)
	waitGroup := sync.WaitGroup{}
	waitGroup.Add(workerAccBalanceSync)

	for i := 0; i < workerAccBalanceSync; i++ {
		go func() {
			defer waitGroup.Done()
			uc.workerSyncAccountBalances(ctx, listAccCh, syncResCh)
		}()
	}

	go func() {
		waitGroup.Wait()
		close(syncResCh)
	}()

	for result := range syncResCh {
		if _, ok := result.(error); ok {
			failCount.Add(1)
			continue
		}
		succCount.Add(1)
	}

	logger.Infow(
		"msg", "sync accounts balance by partner completed",
		"partner", partnerCode, "total_success", succCount.Load(),
		"total_fail", failCount.Load(),
	)

	return &dto.AccountsSyncStats{
		TotalItems:   cast.ToInt(succCount.Load() + failCount.Load()),
		TotalSuccess: cast.ToInt(succCount.Load()),
		TotalFailed:  cast.ToInt(failCount.Load()),
	}, nil
}

func (uc *Usecase) SyncDebtAccountBalancesByPartner(ctx context.Context, partnerCode partner.PartnerCode) (*dto.AccountsSyncStats, error) {
	logger := uc.logger.WithContext(ctx)

	listAccCh, err := uc.BuildDebtAccountsByPartnerCh(ctx, partnerCode)
	if err != nil {
		logger.WithContext(ctx).Errorf("failed to build debt accounts channel, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to build debt accounts channel").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if listAccCh == nil {
		logger.Warn("no debt account channel for sync")
		return &dto.AccountsSyncStats{}, nil
	}

	succCount := atomic.Int64{}
	failCount := atomic.Int64{}
	syncResCh := make(chan any, workerAccBalanceSync)
	waitGroup := sync.WaitGroup{}
	waitGroup.Add(workerAccBalanceSync)

	for i := 0; i < workerAccBalanceSync; i++ {
		go func() {
			defer waitGroup.Done()
			uc.workerSyncAccountBalances(ctx, listAccCh, syncResCh)
		}()
	}

	go func() {
		waitGroup.Wait()
		close(syncResCh)
	}()

	for result := range syncResCh {
		if _, ok := result.(error); ok {
			failCount.Add(1)
			continue
		}
		succCount.Add(1)
	}

	logger.Infow(
		"msg", "sync debt accounts balance by partner completed",
		"partner", partnerCode, "total_success", succCount.Load(),
		"total_fail", failCount.Load(),
	)

	return &dto.AccountsSyncStats{
		TotalItems:   cast.ToInt(succCount.Load() + failCount.Load()),
		TotalSuccess: cast.ToInt(succCount.Load()),
		TotalFailed:  cast.ToInt(failCount.Load()),
	}, nil
}

func (uc *Usecase) workerSyncAccountBalances(
	ctx context.Context,
	listAccCh <-chan *model.Account,
	syncResCh chan any,
) {
	logger := uc.logger.WithContext(ctx)
	retryer := retry.NewRetry(3, time.Second*500, nil)

	for acc := range listAccCh {
		if !acc.CanUsable() {
			syncResCh <- acc
			continue
		}
		err := retryer.Execute(ctx, func() error {
			data, sErr := uc.SyncAccountBalance(ctx, &dto.SyncAccountBalanceParams{
				AccountID: acc.ID,
				ZalopayID: acc.ZalopayID,
			})
			if sErr != nil {
				return sErr
			}
			acc = data
			return nil
		})
		if err != nil {
			logger.Errorw(
				"msg", "failed to sync account balance",
				"account_id", acc.ID, "zalopay_id", acc.ZalopayID,
				"partner_code", acc.PartnerCode, "error", err,
			)
			syncResCh <- err
			continue
		}
		logger.Infow(
			"msg", "sync account balance success",
			"account_id", acc.ID, "zalopay_id", acc.ZalopayID,
			"balance", acc.InstallmentBalance, "partner_code", acc.PartnerCode,
		)
		syncResCh <- acc
	}
}
