package account

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (uc *Usecase) CreateAccount(ctx context.Context, params *dto.CreateAccountParams) (*model.Account, error) {
	if userAcc, _ := uc.accRepo.GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode); userAcc != nil {
		return userAcc, nil
	}

	logger := uc.logger.WithContext(ctx)
	logger.Infow("msg", "begin create account", "params", params)

	// Query full account data from partner, include basic info and balance
	userPartnerAcc, err := uc.cimbConn.QueryFullAccountData(ctx, params.ZalopayID)
	if err != nil {
		logger.Errorf("query partner account info failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "query partner account info failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if userPartnerAcc == nil {
		logger.Errorf("partner account info not found")
		return nil, errorkit.
			NewError(errorkit.CodeAccountNotFound, "partner account info not found").
			WithKind(errorkit.TypeNotFound)
	}
	if userPartnerAcc.AccountInfo == nil || userPartnerAcc.AccountBalance == nil {
		logger.Errorf("partner account info invalid")
		logger.Warnw("msg", "partner account info",
			"account_info_valid", userPartnerAcc.AccountInfo != nil,
			"account_balance_valid", userPartnerAcc.AccountBalance != nil,
		)
		return nil, errorkit.
			NewError(errorkit.CodeAccountDataInvalid, "partner account info invalid").
			WithKind(errorkit.TypeValidation)
	}

	onboardID := params.OnboardingID
	accountInfo := userPartnerAcc.AccountInfo
	accountBalance := userPartnerAcc.AccountBalance
	accountStatus := accountBalance.OverdraftBalanceStatus.ToAccountStatus()

	// Create an account
	userAccCreate := &model.AccountCreateParams{
		ZalopayID:            params.ZalopayID,
		PartnerCode:          params.PartnerCode,
		Origin:               model.SourceInstallment,
		Status:               accountStatus,
		ExpiryDate:           accountInfo.ExpiryDate,
		PartnerAccountName:   accountInfo.AccountName,
		PartnerAccountNumber: accountInfo.AccountNumber,
		InstallmentLimit:     accountBalance.OverdraftLimit,
		InstallmentBalance:   accountBalance.AvailableBalance,
	}
	accountID, err := uc.accRepo.CreateAccount(ctx, userAccCreate)
	if err != nil {
		logger.Errorf("create account failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "create account failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infow(
		"msg", "create account success",
		"account_id", accountID,
		"zalopay_id", params.ZalopayID,
		"onboarding_id", params.OnboardingID,
		"partner_code", params.PartnerCode,
		"account_balance", accountBalance,
	)

	go uc.PublishAccountCreatedEvent(ctx, onboardID, accountID)

	return &model.Account{
		ID:        accountID,
		ZalopayID: params.ZalopayID,
	}, nil
}

func (uc *Usecase) PublishAccountCreatedEvent(ctx context.Context, onboardID, accountID int64) error {
	ctx = context.WithoutCancel(ctx)
	logger := uc.logger.WithContext(ctx)
	retryer := retry.NewRetry(5, time.Second*3, nil)
	delayTime := time.Second * 5

	// Delay 5s to ensure onboarding service has already updated account_id
	if uc.sleep != nil {
		uc.sleep(delayTime)
	} else {
		time.Sleep(delayTime)
	}

	logger.Infow("msg", "publish account created event", "account_id", accountID)

	account, err := uc.accRepo.GetAccountByID(ctx, accountID)
	if err != nil {
		logger.Errorf("get account by id failed: %v", err)
		return errors.Wrapf(err, "get account by id failed, id=%v", accountID)
	}

	// Publish account created event
	err = retryer.Execute(ctx, func() error {
		params := &dto.AccountCreatedEvent{}
		params = params.FromOnboardAndAccount(onboardID, account)
		if err := uc.crmEvt.PublishAccountCreatedEvent(ctx, params); err != nil {
			logger.Errorf("publish account created event failed: %v", err)
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("publish account created event failed: %v", err)
		return errors.Wrapf(err, "publish account created event failed, account_id=%v", accountID)
	}

	logger.Infow(
		"msg", "publish account created event success",
		"onboard_id", onboardID, "account_id", accountID,
	)
	return nil
}
