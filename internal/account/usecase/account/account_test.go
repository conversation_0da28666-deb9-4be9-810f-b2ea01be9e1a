package account

import (
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/mocks/repository"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
)

type AccountTestSuite struct {
	suite.Suite
	uc             *Usecase
	ctrl           *gomock.Controller
	accountRepo    *repository_mocks.MockAccountRepo
	cimbService    *adapter_mocks.MockCIMBService
	onboardingSvc  *adapter_mocks.MockOnboardingService
	outstandingSvc *adapter_mocks.MockOutstandingService
	distLock       *adapter_mocks.MockDistributedLock
	keyGen         *mock.MockRedisKeyGenerator
	crmEvt         *adapter_mocks.MockCRMEventPublisher
}

// SetupTest runs before each test
func (s *AccountTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.accountRepo = repository_mocks.NewMockAccountRepo(s.ctrl)
	s.cimbService = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.onboardingSvc = adapter_mocks.NewMockOnboardingService(s.ctrl)
	s.outstandingSvc = adapter_mocks.NewMockOutstandingService(s.ctrl)
	s.crmEvt = adapter_mocks.NewMockCRMEventPublisher(s.ctrl)
	s.distLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.keyGen = mock.NewMockRedisKeyGenerator(s.ctrl)
	s.uc = NewUsecase(
		s.accountRepo, s.cimbService, s.outstandingSvc,
		s.onboardingSvc, s.crmEvt, s.distLock, s.keyGen, log.GetLogger())
	s.uc.sleep = func(d time.Duration) { time.Sleep(0) }
}

// TearDownTest runs after each test
func (s *AccountTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestAccountTestSuite runs the test suite
func TestAccountTestSuite(t *testing.T) {
	suite.Run(t, new(AccountTestSuite))
}
