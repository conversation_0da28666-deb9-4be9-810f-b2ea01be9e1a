package account

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"go.uber.org/mock/gomock"
)

func (s *AccountTestSuite) TestSyncAccountBalance_Success() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	account := &model.Account{
		ID:                   456,
		ZalopayID:            123,
		Status:               model.StatusActive,
		PartnerCode:          partner.PartnerCIMB,
		PartnerAccountNumber: "**********",
	}

	accBalance := &model.PartnerAccountBalance{
		TotalLimit:             1000000,
		OverdraftLimit:         1000000,
		AvailableBalance:       500000,
		OverdraftBalanceStatus: model.PartnerODBalanceStatusNormal,
	}

	s.keyGen.EXPECT().Generate(gomock.Any()).Return("123", nil)
	s.distLock.EXPECT().AcquireBalanceSyncing(gomock.Any(), "123").Return(nil)
	s.distLock.EXPECT().Release(gomock.Any(), "123").Return(nil)

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, params.ZalopayID, params.AccountID).
		Return(account, nil)

	s.cimbService.EXPECT().QueryAccountBalance(ctx, account.ZalopayID, account.PartnerAccountNumber).
		Return(accBalance, nil)

	s.accountRepo.EXPECT().UpdateAccountBalance(ctx, account).Return(nil)

	result, err := s.uc.SyncAccountBalance(ctx, params)

	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.StatusActive, result.Status)
	s.Equal(accBalance.TotalLimit, result.InstallmentLimit)
	s.Equal(accBalance.AvailableBalance, result.InstallmentBalance)
}

func (s *AccountTestSuite) TestSyncAccountBalance_GetAccountFailed() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	s.keyGen.EXPECT().Generate(gomock.Any()).Return("123", nil)
	s.distLock.EXPECT().AcquireBalanceSyncing(gomock.Any(), "123").Return(nil)
	s.distLock.EXPECT().Release(gomock.Any(), "123").Return(nil)

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, params.ZalopayID, params.AccountID).
		Return(nil, errors.New("db error"))

	result, err := s.uc.SyncAccountBalance(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestSyncAccountBalance_QueryBalanceFailed() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	account := &model.Account{
		ID:                   456,
		ZalopayID:            123,
		Status:               model.StatusActive,
		PartnerCode:          partner.PartnerCIMB,
		PartnerAccountNumber: "**********",
	}

	s.keyGen.EXPECT().Generate(gomock.Any()).Return("123", nil)
	s.distLock.EXPECT().AcquireBalanceSyncing(gomock.Any(), "123").Return(nil)
	s.distLock.EXPECT().Release(gomock.Any(), "123").Return(nil)

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, params.ZalopayID, params.AccountID).
		Return(account, nil)

	s.cimbService.EXPECT().QueryAccountBalance(ctx, account.ZalopayID, account.PartnerAccountNumber).
		Return(nil, errors.New("service error"))

	result, err := s.uc.SyncAccountBalance(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRemoteCall, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestSyncAccountBalance_UpdateBalanceFailed() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	account := &model.Account{
		ID:                   456,
		ZalopayID:            123,
		Status:               model.StatusActive,
		PartnerCode:          partner.PartnerCIMB,
		PartnerAccountNumber: "**********",
	}

	accBalance := &model.PartnerAccountBalance{
		TotalLimit:             1000000,
		AvailableBalance:       500000,
		OverdraftBalanceStatus: model.PartnerODBalanceStatusNormal,
	}

	s.keyGen.EXPECT().Generate(gomock.Any()).Return("123", nil)
	s.distLock.EXPECT().AcquireBalanceSyncing(gomock.Any(), "123").Return(nil)
	s.distLock.EXPECT().Release(gomock.Any(), "123").Return(nil)

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, params.ZalopayID, params.AccountID).
		Return(account, nil)

	s.cimbService.EXPECT().QueryAccountBalance(ctx, account.ZalopayID, account.PartnerAccountNumber).
		Return(accBalance, nil)

	s.accountRepo.EXPECT().UpdateAccountBalance(ctx, account).Return(errors.New("db error"))

	result, err := s.uc.SyncAccountBalance(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}
