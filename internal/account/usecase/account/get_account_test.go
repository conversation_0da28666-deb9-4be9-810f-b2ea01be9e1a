package account

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *AccountTestSuite) TestGetAccountByID_Success() {
	ctx := context.Background()
	zalopayID := int64(123)
	accountID := int64(456)

	expectedAccount := &model.Account{
		ID:        accountID,
		ZalopayID: zalopayID,
		Status:    model.StatusActive,
	}

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, zalopayID, accountID).
		Return(expectedAccount, nil)

	result, err := s.uc.GetAccountByID(ctx, zalopayID, accountID)

	s.NoError(err)
	s.Equal(expectedAccount, result)
}

func (s *AccountTestSuite) TestGetAccountByID_Failed() {
	ctx := context.Background()
	zalopayID := int64(123)
	accountID := int64(456)

	s.accountRepo.EXPECT().GetAccountByIDAndZalopayID(ctx, zalopayID, accountID).
		Return(nil, errors.New("db error"))

	result, err := s.uc.GetAccountByID(ctx, zalopayID, accountID)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestGetAccountByPartner_Success() {
	ctx := context.Background()
	zalopayID := int64(123)
	partnerCode := partner.PartnerCode("CIMB")

	expectedAccount := &model.Account{
		ID:          456,
		ZalopayID:   zalopayID,
		Status:      model.StatusActive,
		PartnerCode: partnerCode,
	}

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, zalopayID, partnerCode).
		Return(expectedAccount, nil)

	result, err := s.uc.GetAccountByPartner(ctx, zalopayID, partnerCode)

	s.NoError(err)
	s.Equal(expectedAccount, result)
}

func (s *AccountTestSuite) TestGetAccountByPartner_Failed() {
	ctx := context.Background()
	zalopayID := int64(123)
	partnerCode := partner.PartnerCode("CIMB")

	s.accountRepo.EXPECT().GetLatestAccountByPartner(ctx, zalopayID, partnerCode).
		Return(nil, errors.New("db error"))

	result, err := s.uc.GetAccountByPartner(ctx, zalopayID, partnerCode)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}
