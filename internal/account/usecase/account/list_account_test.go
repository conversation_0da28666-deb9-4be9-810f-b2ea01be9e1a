package account

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"go.uber.org/mock/gomock"
)

func (s *AccountTestSuite) TestListAccountForStmtSync_Success() {
	ctx := context.Background()
	params := &model.AccountListStmtSyncParams{
		Limit: 10,
	}

	expectedAccounts := []*model.Account{
		{
			ID:        1,
			ZalopayID: 123,
			Status:    model.StatusActive,
		},
		{
			ID:        2,
			ZalopayID: 456,
			Status:    model.StatusActive,
		},
	}

	s.accountRepo.EXPECT().ListAccountForStmtSync(ctx, params).
		Return(expectedAccounts, nil)

	result, err := s.uc.ListAccountForStmtSync(ctx, params)

	s.NoError(err)
	s.Equal(expectedAccounts, result)
}

func (s *AccountTestSuite) TestListAccountForStmtSync_Failed() {
	ctx := context.Background()
	params := &model.AccountListStmtSyncParams{
		Limit: 10,
	}

	s.accountRepo.EXPECT().ListAccountForStmtSync(ctx, params).
		Return(nil, errors.New("db error"))

	result, err := s.uc.ListAccountForStmtSync(ctx, params)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestListAccountBindings_Success() {
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}

	userOnboards := []*model.OnboardingData{
		{
			ZalopayID:    123,
			OnboardingID: 123,
			PartnerCode:  partner.PartnerCIMB,
			CurrentStep:  "APPROVED",
		},
		{
			ZalopayID:    456,
			OnboardingID: 456,
			PartnerCode:  partner.PartnerCIMB,
			CurrentStep:  "APPROVED",
		},
	}

	userAccounts := []*model.Account{
		{
			ID:        1,
			ZalopayID: 123,
			Status:    model.StatusActive,
		},
		{
			ID:        2,
			ZalopayID: 456,
			Status:    model.StatusActive,
		},
	}

	s.onboardingSvc.EXPECT().ListOnboardingByUserIDs(ctx, zalopayIDs).
		Return(userOnboards, nil)

	s.accountRepo.EXPECT().ListAccountByUserIDs(ctx, zalopayIDs).
		Return(userAccounts, nil)

	result, err := s.uc.ListAccountBindings(ctx, zalopayIDs)

	s.NoError(err)
	s.Len(result, 2)
}

func (s *AccountTestSuite) TestListAccountBindings_OnboardingServiceFailed() {
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}

	s.onboardingSvc.EXPECT().ListOnboardingByUserIDs(ctx, zalopayIDs).
		Return(nil, errors.New("service error"))

	result, err := s.uc.ListAccountBindings(ctx, zalopayIDs)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRemoteCall, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestListAccountBindings_ListAccountsFailed() {
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}

	userOnboards := []*model.OnboardingData{
		{
			ZalopayID:    123,
			OnboardingID: 123,
			PartnerCode:  partner.PartnerCIMB,
			CurrentStep:  "APPROVED",
		},
		{
			ZalopayID:    456,
			OnboardingID: 456,
			PartnerCode:  partner.PartnerCIMB,
			CurrentStep:  "APPROVED",
		},
	}

	s.onboardingSvc.EXPECT().ListOnboardingByUserIDs(gomock.Any(), zalopayIDs).
		Return(userOnboards, nil)

	s.accountRepo.EXPECT().ListAccountByUserIDs(gomock.Any(), zalopayIDs).
		Return(nil, errors.New("db error"))

	result, err := s.uc.ListAccountBindings(ctx, zalopayIDs)

	s.Error(err)
	s.Nil(result)
	s.Equal(errorkit.TypeRepository, errorkit.GetErrorKind(err))
}

func (s *AccountTestSuite) TestDedupZalopayIDs() {
	input := []int64{1, 2, 2, 3, 3, 3}
	result := dedupZalopayIDs(input)
	s.Len(result, 3)
	s.Contains(result, int64(1))
	s.Contains(result, int64(2))
	s.Contains(result, int64(3))
}

func (s *AccountTestSuite) TestInitUserBindings() {
	zalopayIDs := []int64{123, 456}
	result := initUserBindings(zalopayIDs)

	s.Len(result, 2)
	s.Contains(result, int64(123))
	s.Contains(result, int64(456))
	s.NotNil(result[123])
	s.NotNil(result[456])
}
