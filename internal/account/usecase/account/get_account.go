package account

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (uc *Usecase) GetAccountByID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error) {
	account, err := uc.accRepo.GetAccountByIDAndZalopayID(ctx, zalopayID, accountID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get account by account id failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get account by account id failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return account, nil
}

func (uc *Usecase) GetAccountByPartner(ctx context.Context,
	zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	account, err := uc.accRepo.GetLatestAccountByPartner(ctx, zalopayID, partnerCode)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get account by partner failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get account by partner failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return account, nil
}

func (uc *Usecase) GetAccountComplete(ctx context.Context, params *dto.GetAccountCompleteParams) (*dto.GetAccountCompleteResult, error) {
	account, err := uc.accRepo.GetActiveAccountByPartner(ctx, params.ZalopayID, params.PartnerCode)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get account by partner and order failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get account by partner and order failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if account == nil {
		return nil, errorkit.
			NewError(errorkit.CodeAccountNotFound, "account not found").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	outstanding, err := uc.outsSvc.GetOutstandingInfo(ctx, account.ZalopayID, account.ID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get outstanding info failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get outstanding info failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return &dto.GetAccountCompleteResult{
		Account:     account,
		Outstanding: outstanding,
	}, nil
}
