package worker

import (
	"os"

	"github.com/go-kratos/kratos/v2"
	kconfig "github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cobra"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/telemetry"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zlog"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = "account-worker"
	// Version is the version of the compiled software.
	Version = "0.0.1"

	id, _ = os.Hostname()
)

var cmd = &cobra.Command{
	Use:  "worker",
	RunE: runCmd,
}

func NewCmd() *cobra.Command {
	return cmd
}

func newApp(logger log.Logger, tWorker *server.TemporalWorker) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(tWorker),
	)
}

func runCmd(cmd *cobra.Command, args []string) error {
	confFlag := cmd.Flag("config")
	if confFlag == nil {
		panic("config flag not found")
	}

	confPath := confFlag.Value.String()
	confSource := file.NewSource(confPath)
	c := kconfig.New(kconfig.WithSource(confSource))
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc config.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	appConf := bc.GetAccount()

	err := telemetry.MustInitTracer(&telemetry.TracingInfo{
		SvcVer:      Version,
		SvcName:     appConf.GetTracing().GetServiceName(),
		Environment: appConf.GetApp().GetEnv(),
		AgentHost:   appConf.GetTracing().GetAgentHost(),
		AgentPort:   appConf.GetTracing().GetAgentPort(),
	})
	if err != nil {
		panic(err)
	}

	logger := zlog.MustNewLogger(&zlog.LoggerInfo{
		SvcID:      id,
		SvcVer:     Version,
		SvcName:    appConf.GetApp().GetName(),
		Level:      appConf.GetLogger().GetLevel(),
		Encoding:   appConf.GetLogger().GetEncoding(),
		StackTrace: appConf.GetLogger().GetStackTrace(),
	})

	app, cleanup, err := wireApp(bc.Account, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}

	return nil
}
