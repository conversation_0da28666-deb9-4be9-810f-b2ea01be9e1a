//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package account

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// wireApp init kratos application.
func wireApp(*config.Account, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		base.ProviderSet,
		adapters.ProviderSet,
		server.ProviderSet,
		repo.ProviderSet,
		usecase.ProviderSet,
		service.ProviderSet,
		maintenance.ProviderSet,
		utils.DeepLinkProviderSet,
		newApp,
	))
}
