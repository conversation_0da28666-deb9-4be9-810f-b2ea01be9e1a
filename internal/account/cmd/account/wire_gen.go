// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package account

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/crm_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/onboarding_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/outstanding_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/risk_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/account"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/content"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/sof"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(configAccount *config.Account, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := base.InitSQLDatabase(configAccount, logger)
	if err != nil {
		return nil, nil, err
	}
	repository := repo.NewRepository(db, logger)
	accountRepo := repo.NewAccountRepo(repository, logger)
	onboardingClient, cleanup2, err := base.InitOnboardingServiceClient(configAccount, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	onboardingService := onboarding_service.NewClient(onboardingClient, logger)
	zpBaseClient, cleanup3, err := base.InitRiskSystemClient(configAccount, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	fraudService := risk_system.NewClient(configAccount, zpBaseClient, logger)
	deepLinkCraft := utils.NewDeepLinkCraft(configAccount)
	usecase := sof.NewUsecase(accountRepo, onboardingService, fraudService, deepLinkCraft, logger)
	cimbConnectorClient, cleanup4, err := base.InitCIMBConnectorClient(configAccount, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	cimbService := cimb_service.NewService(cimbConnectorClient, logger)
	managementClient, cleanup5, err := base.InitManagementServiceClient(configAccount, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	outstandingService := outstanding_service.NewClient(managementClient, logger)
	publisher, cleanup6, err := base.InitCrmEventPublisher(configAccount)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	crmEventPublisher := crm_system.NewEventPub(publisher, logger)
	cacheNoCaller, cleanup7, err := base.InitRedisCache(configAccount)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	distributedLock := dist_lock.NewDistLock(cacheNoCaller, logger)
	redisKeyGenerator, cleanup8, err := base.InitRedisKeyGenerator(configAccount)
	if err != nil {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountUsecase := account.NewUsecase(accountRepo, cimbService, outstandingService, onboardingService, crmEventPublisher, distributedLock, redisKeyGenerator, logger)
	contentRepo := repo.NewContentRepo(configAccount, repository, logger)
	contentUsecase := content.NewUsecase(contentRepo, logger)
	accountService := service.NewAccountService(usecase, accountUsecase, contentUsecase, logger)
	handler := maintenance.NewMaintenance(cacheNoCaller, logger)
	grpcServer := server.NewGRPCServer(configAccount, logger, accountService, handler)
	sessionServiceClient, cleanup9, err := base.InitSessionGrpcConn(configAccount, logger)
	if err != nil {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	authenticator := auth_service.NewClient(sessionServiceClient, logger)
	httpServer := server.NewHTTPServer(configAccount, logger, accountService, authenticator, handler)
	client, cleanup10, err := base.InitTemporalClient(configAccount)
	if err != nil {
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	temporalWorker := server.NewTemporalWorker(configAccount, logger, accountService, client)
	app := newApp(logger, grpcServer, httpServer, temporalWorker)
	return app, func() {
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
