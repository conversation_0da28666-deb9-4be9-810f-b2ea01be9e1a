package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/cmd/account"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/cmd/worker"
)

var rootCmd = &cobra.Command{
	Use:              "account",
	Short:            "account service",
	RunE:             runCmd,
	TraverseChildren: true,
}

func NewCmd() *cobra.Command {
	rootCmd.AddCommand(account.NewCmd())
	rootCmd.AddCommand(worker.NewCmd())
	return rootCmd
}

func runCmd(cmd *cobra.Command, args []string) error {
	defaultCmd := account.NewCmd()
	return defaultCmd.RunE(cmd, args)
}
