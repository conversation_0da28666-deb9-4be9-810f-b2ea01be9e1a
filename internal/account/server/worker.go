package server

import (
	"context"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	tempCli "go.temporal.io/sdk/client"
	tempWorker "go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/service"
)

type TemporalWorker struct {
	transport.Server
	logger     *log.Helper
	onceExec   *sync.Once
	temporal   tempCli.Client
	workerList []*TemporalWorkerData
	accountSvc *service.AccountService
}

type TemporalWorkerData struct {
	worker  tempWorker.Worker
	wfNames []string
}

func NewTemporalWorker(
	conf *config.Account, kLogger log.Logger,
	accountSvc *service.AccountService,
	temporal tempCli.Client) *TemporalWorker {
	result := &TemporalWorker{
		temporal:   temporal,
		onceExec:   &sync.Once{},
		accountSvc: accountSvc,
	}

	// init logger
	logging := log.With(kLogger, "module", "temporal-worker")
	result.logger = log.NewHelper(logging)

	// init worker
	syncingWorker := result.RegisterAccountSyncingWorkflow(
		conf.GetSchedulers().GetSyncAccountBalance(),
		conf.GetSchedulers().GetPollingAccountBalance(),
		conf.GetSchedulers().GetSyncCimbAccountBalances(),
		conf.GetSchedulers().GetSyncCimbDebtAccountBalances(),
	)
	tempWorkerList := append([]*TemporalWorkerData{}, syncingWorker)
	result.workerList = tempWorkerList

	return result
}

func (t TemporalWorker) RegisterAccountSyncingWorkflow(
	syncBalanceTask *config.TemporalTask,
	pollBalanceTask *config.TemporalTask,
	syncBalancesTask *config.TemporalTask,
	syncDebtBalancesTask *config.TemporalTask) *TemporalWorkerData {
	queueName := syncBalanceTask.GetQueueName()
	workerRun := tempWorker.New(t.temporal, queueName, tempWorker.Options{})

	// Register Workflow
	syncBalanceWfType := syncBalanceTask.GetWorkflowType()
	syncBalanceOptions := workflow.RegisterOptions{Name: syncBalanceWfType}
	workerRun.RegisterWorkflowWithOptions(t.accountSvc.SyncAccountBalanceWorkflow, syncBalanceOptions)

	pollBalanceWfType := pollBalanceTask.GetWorkflowType()
	pollBalanceOptions := workflow.RegisterOptions{Name: pollBalanceWfType}
	workerRun.RegisterWorkflowWithOptions(t.accountSvc.PollingAccountBalanceWorkflow, pollBalanceOptions)

	syncBalancesWfType := syncBalancesTask.GetWorkflowType()
	syncBalancesOptions := workflow.RegisterOptions{Name: syncBalancesWfType}
	workerRun.RegisterWorkflowWithOptions(t.accountSvc.SyncCIMBAccountBalancesWorkflow, syncBalancesOptions)

	syncDebtBalancesWfType := syncDebtBalancesTask.GetWorkflowType()
	syncDebtBalancesOptions := workflow.RegisterOptions{Name: syncDebtBalancesWfType}
	workerRun.RegisterWorkflowWithOptions(t.accountSvc.SyncCIMBDebtAccountBalancesWorkflow, syncDebtBalancesOptions)

	// Register list activity
	workerRun.RegisterActivity(t.accountSvc.SyncAccountBalanceActivity)
	workerRun.RegisterActivity(t.accountSvc.GetCurrentAccountInfoActivity)
	workerRun.RegisterActivity(t.accountSvc.PollingAccountBalanceActivity)
	workerRun.RegisterActivity(t.accountSvc.SyncAccountBalancesActivity)
	workerRun.RegisterActivity(t.accountSvc.SyncDebtAccountBalancesActivity)

	return &TemporalWorkerData{
		worker: workerRun,
		wfNames: []string{
			syncBalanceWfType,
			pollBalanceWfType,
			syncBalancesWfType,
			syncDebtBalancesWfType,
		},
	}
}

func (t TemporalWorker) Start(ctx context.Context) error {
	var wg sync.WaitGroup

	handleStart := func(worker *TemporalWorkerData) {
		defer wg.Done()
		wfNames := strings.Join(worker.wfNames, ",")
		runErr := worker.worker.Run(tempWorker.InterruptCh())
		if runErr == nil {
			return
		}
		t.logger.Errorf("failed to start worker for workflow: %s, error: %v", wfNames, runErr)
	}

	for _, worker := range t.workerList {
		wg.Add(1)
		cloned := worker
		go handleStart(cloned)
	}

	wg.Wait()
	return nil
}

func (t TemporalWorker) Stop(ctx context.Context) error {
	handle := func() {
		for _, w := range t.workerList {
			w.worker.Stop()
		}
	}

	t.onceExec.Do(handle)
	return nil
}
