package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.opentelemetry.io/otel"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	maintMgr "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	imetrics "gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/maintenance"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	c *config.Account, logger log.Logger,
	accountService *service.AccountService,
	authenticator auth.Authenticator,
	maintManager maintMgr.Handler) *http.Server {
	codec := codec.NewRequestLogCodec(getSensitiveCensorship())
	metric := imetrics.NewOtelRequestMetrics(imetrics.Options{
		Namespace: imetrics.MetricAccountMeter,
		Module:    imetrics.MetricModuleHttpServer,
	})

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			metrics.Server(
				metrics.WithRequests(metric.HandledCounter),
				metrics.WithSeconds(metric.HandledHistogram),
			),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			logging.Server(logger, codec),
			auth.Server(authenticator),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().Http.Network != "" {
		opts = append(opts, http.Network(c.GetServer().Http.Network))
	}
	if c.GetServer().Http.Addr != "" {
		opts = append(opts, http.Address(c.GetServer().Http.Addr))
	}
	if c.GetServer().Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.GetServer().Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	srv.Handle("/metrics", promhttp.Handler())
	v1.RegisterAccountHTTPServer(srv, accountService)
	return srv
}
