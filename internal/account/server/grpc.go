package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	fsProfile "gitlab.zalopay.vn/fin/asset-management/financial-profile/api/genproto/adapter"
	"go.opentelemetry.io/otel"

	v1Ext "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/external/v1"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	maintMgr "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	imetrics "gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/maintenance"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(
	c *config.Account, logger log.Logger,
	accountService *service.AccountService,
	maintManager maintMgr.Handler) *grpc.Server {
	codec := codec.NewRequestLogCodec(getSensitiveCensorship())
	metric := imetrics.NewOtelRequestMetrics(imetrics.Options{
		Namespace: imetrics.MetricAccountMeter,
		Module:    imetrics.MetricModuleGrpcServer,
	})

	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			metrics.Server(
				metrics.WithRequests(metric.HandledCounter),
				metrics.WithSeconds(metric.HandledHistogram),
			),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			logging.Server(logger, codec),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.GetServer().Grpc.Network))
	}
	if c.GetServer().Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.GetServer().Grpc.Addr))
	}
	if c.GetServer().Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.GetServer().Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterAccountServer(srv, accountService)
	v1Ext.RegisterPaymentAccountServer(srv, accountService)
	fsProfile.RegisterProfileAdapterServiceServer(srv, accountService)
	return srv
}
