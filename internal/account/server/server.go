package server

import (
	"github.com/google/wire"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer, NewTemporalWorker)

var MaintenanceElements = []maintenance.Element{
	{
		Operation: v1.Account_CreateAccount_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
}

func getSensitiveCensorship() (sensitiveMethods, sensitiveFields []string) {
	sensitiveMethods = []string{
		v1.Account_GetAccount_FullMethodName,
		v1.Account_GetClientAccount_FullMethodName,
		v1.Account_ListAccountForStmtSync_FullMethodName,
	}
	sensitiveFields = []string{
		"account.partner_account_name",
		"account.partner_account_number",
		"accounts.*.partner_account_name",
		"accounts.*.partner_account_number",
	}
	return
}
