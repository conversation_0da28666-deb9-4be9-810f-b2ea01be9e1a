// Code generated by MockGen. DO NOT EDIT.
// Source: zalopay.io/zgo/kafka-client (interfaces: Publisher)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/publisher_mock.go -package=mocks zalopay.io/zgo/kafka-client Publisher
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	proto "github.com/gogo/protobuf/proto"
	gomock "go.uber.org/mock/gomock"
	kafka_client "zalopay.io/zgo/kafka-client"
)

// MockPublisher is a mock of Publisher interface.
type MockPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockPublisherMockRecorder
	isgomock struct{}
}

// MockPublisherMockRecorder is the mock recorder for MockPublisher.
type MockPublisherMockRecorder struct {
	mock *MockPublisher
}

// NewMockPublisher creates a new mock instance.
func NewMockPublisher(ctrl *gomock.Controller) *MockPublisher {
	mock := &MockPublisher{ctrl: ctrl}
	mock.recorder = &MockPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPublisher) EXPECT() *MockPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockPublisher) Publish(arg0 context.Context, arg1 string, arg2 proto.Message, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Publish", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockPublisherMockRecorder) Publish(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockPublisher)(nil).Publish), varargs...)
}

// PublishRaw mocks base method.
func (m *MockPublisher) PublishRaw(arg0 context.Context, arg1 string, arg2 []byte, arg3 ...kafka_client.Header) error {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaw", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaw indicates an expected call of PublishRaw.
func (mr *MockPublisherMockRecorder) PublishRaw(arg0, arg1, arg2 any, arg3 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaw", reflect.TypeOf((*MockPublisher)(nil).PublishRaw), varargs...)
}

// PublishRaws mocks base method.
func (m *MockPublisher) PublishRaws(ctx context.Context, key string, data ...kafka_client.Source) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key}
	for _, a := range data {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishRaws", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishRaws indicates an expected call of PublishRaws.
func (mr *MockPublisherMockRecorder) PublishRaws(ctx, key any, data ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key}, data...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishRaws", reflect.TypeOf((*MockPublisher)(nil).PublishRaws), varargs...)
}

// Stop mocks base method.
func (m *MockPublisher) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockPublisherMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockPublisher)(nil).Stop))
}
