package crm_system

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	kafka_client "zalopay.io/zgo/kafka-client"
)

type eventPub struct {
	hlogger  *log.Helper
	klogger  log.Logger
	logCodec codec.LogCodec
	crmPub   kafka_client.Publisher
}

func NewEventPub(crmPub kafka_client.Publisher, klogger log.Logger) _interface.CRMEventPublisher {
	hlogger := log.NewHelper(log.With(klogger, "adapters", "crm-event-publisher"))
	logCodec := codec.NewBaseLogCodec([]string{"payload.partner_account_number", "payload.partner_account_name"})
	return &eventPub{
		hlogger:  hlogger,
		klogger:  klogger,
		logCodec: logCodec,
		crmPub:   crmPub,
	}
}

func (e eventPub) PublishAccountCreatedEvent(ctx context.Context, event *dto.AccountCreatedEvent) error {
	logger := e.hlogger.WithContext(ctx)

	eventRedactBin, _ := e.logCodec.Marshal(event)
	logger.Infow("msg", "publishing account created event", "event", string(eventRedactBin))

	messageKey := uuid.NewString()
	messageData, err := json.Marshal(event)
	if err != nil {
		logger.Errorf("failed to marshal account created event: %v", err)
		return errors.Wrap(err, "failed to marshal account created event")
	}

	if err = e.crmPub.PublishRaw(ctx, messageKey, messageData); err != nil {
		logger.Errorf("failed to publish account created event: %v", err)
		return errors.Wrap(err, "failed to publish account created event")
	}

	logger.Infow("msg", "publish account created event success", "event_id", event.EventID)
	return nil
}
