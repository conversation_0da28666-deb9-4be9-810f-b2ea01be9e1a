package crm_system

import (
	"context"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"go.uber.org/mock/gomock"
)

func (s *CRMClientTestSuite) TestPublishAccountCreatedEvent_Success() {
	// Arrange
	ctx := context.Background()
	event := &dto.AccountCreatedEvent{
		EventID: "test-event-id",
		Payload: dto.AccountCreatedPayload{
			PartnerAccountNumber: "123456",
			PartnerAccountName:   "Test Account",
		},
	}

	s.mockGrpcClient.EXPECT().
		PublishRaw(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	// Act
	err := s.publisher.PublishAccountCreatedEvent(ctx, event)

	// Assert
	assert.NoError(s.T(), err)
}

func (s *CRMClientTestSuite) TestPublishAccountCreatedEvent_PublishError() {
	// Arrange
	ctx := context.Background()
	event := &dto.AccountCreatedEvent{
		EventID: "test-event-id",
		Payload: dto.AccountCreatedPayload{
			PartnerAccountNumber: "123123",
			PartnerAccountName:   "Test Account",
		},
	}

	expectedErr := errors.New("publish error")
	s.mockGrpcClient.EXPECT().
		PublishRaw(ctx, gomock.Any(), gomock.Any()).
		Return(expectedErr)

	// Act
	err := s.publisher.PublishAccountCreatedEvent(ctx, event)

	// Assert
	assert.Error(s.T(), err)
	assert.Contains(s.T(), err.Error(), "failed to publish account created event")
}
