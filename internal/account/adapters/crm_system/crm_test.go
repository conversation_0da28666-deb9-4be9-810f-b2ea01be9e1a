package crm_system

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/crm_system/mocks"
	"go.uber.org/mock/gomock"
)

type CRMClientTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	publisher      *eventPub
	mockGrpcClient *mocks.MockPublisher
}

func TestCRMClientSuite(t *testing.T) {
	suite.Run(t, new(CRMClientTestSuite))
}

func (s *CRMClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockGrpcClient = mocks.NewMockPublisher(s.ctrl)
	s.publisher = NewEventPub(s.mockGrpcClient, log.DefaultLogger).(*eventPub)
}

func (s *CRMClientTestSuite) TearDownTest() {
	s.ctrl.Finish()
}
