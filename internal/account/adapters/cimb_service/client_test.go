package cimb_service

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"
)

type CIMBClientTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	service        *service
	mockGrpcClient *connector.MockCIMBConnectorClient
}

func TestCIMBClientSuite(t *testing.T) {
	suite.Run(t, new(CIMBClientTestSuite))
}

func (s *CIMBClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockGrpcClient = connector.NewMockCIMBConnectorClient(s.ctrl)
	s.service = NewService(s.mockGrpcClient, log.DefaultLogger).(*service)
}

func (s *CIMBClientTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *CIMBClientTestSuite) TestNewService() {
	// Arrange
	mockClient := connector.NewMockCIMBConnectorClient(s.ctrl)

	// Act
	svc := NewService(mockClient, log.DefaultLogger)

	// Assert
	s.NotNil(svc)
	s.IsType(&service{}, svc)

	actualService := svc.(*service)
	s.Equal(mockClient, actualService.cimbConnector)
	s.NotNil(actualService.logger)
}
