package cimb_service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"

	"github.com/spf13/cast"
)

func (s service) QueryAccountInfo(ctx context.Context, zalopayID int64) (*model.PartnerAccountInfo, error) {
	resp, err := s.cimbConnector.InquiryMultiAccounts(ctx, &connector.InquiryMultiAccountsRequest{
		AccountTypes: []connector.AccountType{connector.AccountType_CASA},
		ZalopayIds:   []string{cast.ToString(zalopayID)},
	})
	if err != nil {
		return nil, errors.Wrap(err, "query casa account from cimb failed")
	}
	if resp == nil || len(resp.GetAccounts()) == 0 {
		return nil, errors.New("query casa account from cimb has empty response")
	}

	var instAccount *connector.UserAccount
	for _, account := range resp.GetAccounts() {
		if account.GetProductCode() == connector.ProductCode_CASA_ZALOPAY_INSTALLMENT {
			instAccount = account
			break
		}
	}

	if instAccount == nil {
		return nil, errors.New("query casa account from cimb has no installment account")
	}

	return &model.PartnerAccountInfo{
		AccountName:   instAccount.GetAccountName(),
		AccountType:   instAccount.GetAccountType(),
		AccountNumber: instAccount.GetAccountNumber(),
		AccountID:     instAccount.GetPartnerAccountId(),
	}, nil
}

func (s service) QueryAccountBalance(
	ctx context.Context, zalopayID int64,
	accountNumber string) (*model.PartnerAccountBalance, error) {
	resp, err := s.cimbConnector.InquiryCASABalance(ctx, &connector.InquiryCASABalanceRequest{
		ZalopayId:     cast.ToString(zalopayID),
		AccountNumber: accountNumber,
		Overdraft:     true,
	})
	if err != nil {
		return nil, errors.Wrap(err, "query casa balance from cimb failed")
	}
	if resp.GetCasaBalance() == nil {
		return nil, errors.Errorf("query casa balance from cimb has empty response")
	}

	casaBalance := resp.GetCasaBalance()
	odBalanceStt := model.PartnerODBalanceStatus(casaBalance.GetOdStatus())
	odExpiryDate, err := time.Parse("********", casaBalance.GetOdExpiryDate())
	if err != nil {
		s.logger.WithContext(ctx).Warnf("parse overdraft expiry date failed, error=%v", err)
	}

	return &model.PartnerAccountBalance{
		TotalLimit:             casaBalance.GetTotalOdLimit(),
		OverdraftLimit:         casaBalance.GetOverdraftLimit(),
		RepaymentBalance:       casaBalance.GetRepaymentOverBalance(),
		AvailableBalance:       casaBalance.GetAvailableOverdraftLimit(),
		CASABalanceStatus:      casaBalance.GetCasaStatus(),
		OverdraftExpiryDate:    odExpiryDate,
		OverdraftBalanceStatus: odBalanceStt,
	}, nil
}

func (s service) QueryFullAccountData(ctx context.Context, zalopayID int64) (*model.PartnerAccount, error) {
	acctInfo, err := s.QueryAccountInfo(ctx, zalopayID)
	if err != nil {
		return nil, errors.Wrap(err, "query account info failed")
	}

	acctBalance, err := s.QueryAccountBalance(ctx, zalopayID, acctInfo.AccountNumber)
	if err != nil {
		return nil, errors.Wrap(err, "query account balance failed")
	}

	acctInfo.ExpiryDate = acctBalance.OverdraftExpiryDate

	return &model.PartnerAccount{
		AccountInfo:    acctInfo,
		AccountBalance: acctBalance,
	}, nil
}
