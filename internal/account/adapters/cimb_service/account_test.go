package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
)

func (s *CIMBClientTestSuite) TestQueryAccountInfo_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	mockResp := &connector.InquiryMultiAccountsResponse{
		Accounts: []*connector.UserAccount{
			{
				AccountName:      "Test Account",
				AccountType:      "CASA",
				AccountNumber:    "**********",
				PartnerAccountId: "ACC001",
				ProductCode:      connector.ProductCode_CASA_ZALOPAY_INSTALLMENT,
			},
		},
	}

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, &connector.InquiryMultiAccountsRequest{
			AccountTypes: []connector.AccountType{connector.AccountType_CASA},
			ZalopayIds:   []string{"12345"},
		}).
		Return(mockResp, nil)

	// Act
	result, err := s.service.QueryAccountInfo(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal("Test Account", result.AccountName)
	s.Equal("CASA", result.AccountType)
	s.Equal("**********", result.AccountNumber)
	s.Equal("ACC001", result.AccountID)
}

func (s *CIMBClientTestSuite) TestQueryAccountInfo_GrpcError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.QueryAccountInfo(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "query casa account from cimb failed")
}

func (s *CIMBClientTestSuite) TestQueryAccountInfo_EmptyResponse() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	mockResp := &connector.InquiryMultiAccountsResponse{
		Accounts: []*connector.UserAccount{},
	}

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.QueryAccountInfo(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "query casa account from cimb has empty response")
}

func (s *CIMBClientTestSuite) TestQueryAccountBalance_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)
	accountNumber := "**********"

	mockResp := &connector.InquiryCASABalanceResponse{
		CasaBalance: &connector.CASABalance{
			TotalOdLimit:            1000000,
			OverdraftLimit:          500000,
			AvailableOverdraftLimit: 300000,
			CasaStatus:              "ACTIVE",
			OdStatus:                "ACTIVE",
			OdExpiryDate:            "********",
		},
	}

	s.mockGrpcClient.EXPECT().
		InquiryCASABalance(ctx, &connector.InquiryCASABalanceRequest{
			ZalopayId:     "12345",
			AccountNumber: accountNumber,
			Overdraft:     true,
		}).
		Return(mockResp, nil)

	// Act
	result, err := s.service.QueryAccountBalance(ctx, zalopayID, accountNumber)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(1000000), result.TotalLimit)
	s.Equal(int64(500000), result.OverdraftLimit)
	s.Equal(int64(300000), result.AvailableBalance)
	s.Equal("ACTIVE", result.CASABalanceStatus)
	s.Equal(model.PartnerODBalanceStatus("ACTIVE"), result.OverdraftBalanceStatus)
}

func (s *CIMBClientTestSuite) TestQueryAccountBalance_GrpcError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)
	accountNumber := "**********"

	s.mockGrpcClient.EXPECT().
		InquiryCASABalance(ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.QueryAccountBalance(ctx, zalopayID, accountNumber)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "query casa balance from cimb failed")
}

func (s *CIMBClientTestSuite) TestQueryFullAccountData_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	mockAcctResp := &connector.InquiryMultiAccountsResponse{
		Accounts: []*connector.UserAccount{
			{
				AccountName:      "Test Account",
				AccountType:      "CASA",
				AccountNumber:    "**********",
				PartnerAccountId: "ACC001",
				ProductCode:      connector.ProductCode_CASA_ZALOPAY_INSTALLMENT,
			},
		},
	}

	mockBalResp := &connector.InquiryCASABalanceResponse{
		CasaBalance: &connector.CASABalance{
			TotalOdLimit:            1000000,
			OverdraftLimit:          500000,
			AvailableOverdraftLimit: 300000,
			CasaStatus:              "ACTIVE",
			OdStatus:                "ACTIVE",
			OdExpiryDate:            "********",
		},
	}

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, gomock.Any()).
		Return(mockAcctResp, nil)

	s.mockGrpcClient.EXPECT().
		InquiryCASABalance(ctx, gomock.Any()).
		Return(mockBalResp, nil)

	// Act
	result, err := s.service.QueryFullAccountData(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal("Test Account", result.AccountInfo.AccountName)
	s.Equal("**********", result.AccountInfo.AccountNumber)
	s.Equal(int64(1000000), result.AccountBalance.TotalLimit)
	s.Equal("ACTIVE", result.AccountBalance.CASABalanceStatus)
}

func (s *CIMBClientTestSuite) TestQueryFullAccountData_AccountInfoError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.QueryFullAccountData(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "query account info failed")
}

func (s *CIMBClientTestSuite) TestQueryFullAccountData_BalanceError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)

	mockAcctResp := &connector.InquiryMultiAccountsResponse{
		Accounts: []*connector.UserAccount{
			{
				AccountName:      "Test Account",
				AccountType:      "CASA",
				AccountNumber:    "**********",
				PartnerAccountId: "ACC001",
				ProductCode:      connector.ProductCode_CASA_ZALOPAY_INSTALLMENT,
			},
		},
	}

	s.mockGrpcClient.EXPECT().
		InquiryMultiAccounts(ctx, gomock.Any()).
		Return(mockAcctResp, nil)

	s.mockGrpcClient.EXPECT().
		InquiryCASABalance(ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.QueryFullAccountData(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "query account balance failed")
}
