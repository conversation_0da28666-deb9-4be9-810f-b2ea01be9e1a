package dist_lock

import (
	"context"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
	"github.com/pkg/errors"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/platform/common/redis"
)

type distLock struct {
	logger  *log.Helper
	redSync *redsync.Redsync
	mutex   sync.RWMutex
	holder  map[string]*redsync.Mutex
}

const (
	defaultTryLockTime  = 4
	defaultTryLockDelay = 500 * time.Millisecond
	balanceSyncLockTime = 10
)

func NewDistLock(redisCache redis.CacheNoCaller, logger log.Logger) _interface.DistributedLock {
	logging := log.With(logger, "adapters", "dist_lock")
	redCli := redisCache.GetRedisClient()
	redSync := redsync.New(goredis.NewPool(redCli))
	return &distLock{
		redSync: redSync,
		mutex:   sync.RWMutex{},
		holder:  make(map[string]*redsync.Mutex),
		logger:  log.NewHelper(logging),
	}
}

func (d *distLock) Acquire(ctx context.Context, resource string, ttl time.Duration) error {
	mutex := d.redSync.NewMutex(resource,
		redsync.WithExpiry(ttl),
		redsync.WithTries(defaultTryLockTime),
		redsync.WithRetryDelay(defaultTryLockDelay),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
		return errors.Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
	}

	d.mutex.Lock()
	d.holder[resource] = mutex
	d.mutex.Unlock()

	return nil
}

func (d *distLock) Release(ctx context.Context, resource string) error {
	d.mutex.RLock()
	locker := d.holder[resource]
	d.mutex.RUnlock()

	if locker == nil {
		return nil
	}

	newCtx := context.WithoutCancel(ctx)
	if ok, err := locker.UnlockContext(newCtx); !ok || err != nil {
		d.logger.WithContext(newCtx).Errorf("release lock failed, resource=%s, error=%v", resource, err)
		return errors.Errorf("release lock failed, resource=%s, error=%v", resource, err)
	}

	// Release lock successfully
	d.mutex.Lock()
	delete(d.holder, resource)
	d.mutex.Unlock()

	return nil
}

func (d *distLock) IsLocked(ctx context.Context, resource string) (bool, error) {
	d.mutex.RLock()
	locker := d.holder[resource]
	d.mutex.RUnlock()

	if locker == nil {
		return false, nil
	}
	return true, nil
}

func (d *distLock) AcquireBalanceSyncing(ctx context.Context, resource string) error {
	// IMPORTANT: This lock is used to prevent multiple instances of the service from running the same task
	// it not have a timeout because of ensure 1 sync process run at a time
	mutex := d.redSync.NewMutex(resource,
		redsync.WithExpiry(time.Minute),
		redsync.WithTries(balanceSyncLockTime),
		redsync.WithRetryDelay(defaultTryLockDelay),
	)

	if err := mutex.LockContext(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
		return errors.Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
	}

	d.mutex.Lock()
	d.holder[resource] = mutex
	d.mutex.Unlock()

	return nil
}
