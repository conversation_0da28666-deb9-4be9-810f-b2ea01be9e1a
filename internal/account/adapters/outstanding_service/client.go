package outstanding_service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
)

type client struct {
	logger     *log.Helper
	grpcClient v1.ManagementClient
}

func NewClient(grpcClient v1.ManagementClient, kLogger log.Logger) _interface.OutstandingService {
	logger := log.NewHelper(log.With(kLogger, "adapters", "outstanding_service"))
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}

func (c client) GetOutstandingInfo(ctx context.Context,
	zalopayID int64, accountID int64) (*model.Outstanding, error) {
	req := &v1.GetOutstandingInfoRequest{
		ZalopayId: zalopayID,
		AccountId: accountID,
	}
	resp, err := c.grpcClient.GetOutstandingInfo(ctx, req)
	if err != nil {
		c.logger.WithContext(ctx).Errorf("failed to query account: %v", err)
		return nil, errors.Errorf("failed to query account: %v", err)
	}
	return &model.Outstanding{
		TotalAmount:  resp.GetTotalOutstanding(),
		DueAmount:    resp.GetTotalDueAmount(),
		DueRepaid:    resp.GetTotalDueRepaid(),
		DuePenalty:   resp.GetTotalDuePenalty(),
		DueCreatedAt: resp.GetDueCreatedAt().AsTime(),
		DueUpdatedAt: resp.GetDueUpdatedAt().AsTime(),
	}, nil
}
