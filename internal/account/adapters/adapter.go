package adapters

import (
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/crm_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/onboarding_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/outstanding_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/adapters/risk_system"
)

var ProviderSet = wire.NewSet(
	dist_lock.NewDistLock,
	risk_system.NewClient,
	crm_system.NewEventPub,
	auth_service.NewClient,
	cimb_service.NewService,
	onboarding_service.NewClient,
	outstanding_service.NewClient,
)
