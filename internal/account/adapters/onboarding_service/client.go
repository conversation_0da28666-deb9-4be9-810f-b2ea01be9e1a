package onboarding_service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type client struct {
	logger     *log.Helper
	grpcClient v1.OnboardingClient
}

func NewClient(grpcClient v1.OnboardingClient, kLogger log.Logger) _interface.OnboardingService {
	logger := log.NewHelper(log.With(kLogger, "adapters", "onboarding_service"))
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}

func (c client) QueryOnboardingStatus(ctx context.Context,
	zalopayID int64, partnerCode string) (*model.OnboardingStatus, error) {
	req := &v1.QueryOnboardingStatusRequest{
		ZalopayId:   zalopayID,
		PartnerCode: partnerCode,
	}
	resp, err := c.grpcClient.QueryOnboardingStatus(ctx, req)
	if err != nil {
		c.logger.WithContext(ctx).Errorf("failed to query account: %v", err)
		return nil, errors.Errorf("failed to query account: %v", err)
	}
	return &model.OnboardingStatus{
		CurrentStep: resp.GetCurrentStep(),
		PartnerCode: partner.CodeFromString(resp.GetPartnerCode()),
		OnboardingStatusFlag: model.OnboardingStatusFlag{
			IsOnboarding: resp.GetFlags().GetIsOnboarding(),
			IsUnregister: resp.GetFlags().GetIsUnregister(),
			IsRejected:   resp.GetFlags().GetIsRejected(),
		},
	}, nil
}

func (c client) ListOnboardingByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.OnboardingData, error) {
	resp, err := c.grpcClient.ListOnboardingByUserIDs(ctx, &v1.ListOnboardingByUserIDsRequest{
		ZalopayIds: zalopayIDs,
	})
	if err != nil {
		c.logger.WithContext(ctx).Errorf("failed to list onboarding by user ids: %v", err)
		return nil, errors.Wrap(err, "failed to list onboarding by user ids")
	}
	if len(resp.GetOnboardings()) == 0 {
		return []*model.OnboardingData{}, nil
	}

	onboardings := make([]*model.OnboardingData, 0, len(resp.GetOnboardings()))
	for _, data := range resp.GetOnboardings() {
		onboardings = append(onboardings, convertToOnboardingData(data))
	}
	return onboardings, nil
}

func convertToOnboardingData(data *v1.OnboardingData) *model.OnboardingData {
	return &model.OnboardingData{
		ZalopayID:      data.GetZalopayId(),
		OnboardingID:   data.GetId(),
		PartnerCode:    partner.CodeFromString(data.GetPartnerCode()),
		CurrentStep:    data.GetCurrentStep(),
		RejectCode:     data.GetRejectCode(),
		FullName:       data.GetProfileInfo().GetFullName(),
		PhoneNumber:    data.GetProfileInfo().GetPhoneNumber(),
		IdentityNumber: data.GetProfileInfo().GetIdNumber(),
		OnboardingStatusFlag: model.OnboardingStatusFlag{
			IsOnboarding: data.GetStatusFlags().GetIsOnboarding(),
			IsUnregister: data.GetStatusFlags().GetIsUnregister(),
			IsRejected:   data.GetStatusFlags().GetIsRejected(),
		},
		CIMBPartnerData: model.CIMBOnboarding{
			ODStatus:             data.GetPartnerData().GetCimbData().GetOdStatus(),
			CASAStatus:           data.GetPartnerData().GetCimbData().GetCasaStatus(),
			SignStatus:           data.GetPartnerData().GetCimbData().GetSignStatus(),
			ErrorDetail:          data.GetPartnerData().GetCimbData().GetErrorDetail(),
			ManualApprovalReason: data.GetPartnerData().GetCimbData().GetManualApprovalReason(),
		},
	}
}
