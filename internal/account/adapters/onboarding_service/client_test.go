package onboarding_service

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type OnboardingClientTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	service        *client
	mockGrpcClient *v1.MockOnboardingClient
}

func TestOnboardingClientSuite(t *testing.T) {
	suite.Run(t, new(OnboardingClientTestSuite))
}

func (s *OnboardingClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockGrpcClient = v1.NewMockOnboardingClient(s.ctrl)

	s.service = NewClient(s.mockGrpcClient, log.GetLogger()).(*client)
}

func (s *OnboardingClientTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *OnboardingClientTestSuite) TestQueryOnboardingStatus_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)
	partnerCode := "CIMB"

	mockResp := &v1.QueryOnboardingStatusResponse{
		CurrentStep: "STEP_1",
		PartnerCode: partnerCode,
		Flags: &v1.QueryOnboardingStatusResponse_Flags{
			IsOnboarding: true,
			IsUnregister: false,
			IsRejected:   false,
		},
	}

	s.mockGrpcClient.EXPECT().
		QueryOnboardingStatus(ctx, &v1.QueryOnboardingStatusRequest{
			ZalopayId:   zalopayID,
			PartnerCode: partnerCode,
		}).
		Return(mockResp, nil)

	// Act
	result, err := s.service.QueryOnboardingStatus(ctx, zalopayID, partnerCode)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal("STEP_1", result.CurrentStep)
	s.Equal(partner.CodeFromString(partnerCode), result.PartnerCode)
	s.True(result.OnboardingStatusFlag.IsOnboarding)
	s.False(result.OnboardingStatusFlag.IsUnregister)
	s.False(result.OnboardingStatusFlag.IsRejected)
}

func (s *OnboardingClientTestSuite) TestQueryOnboardingStatus_Error() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(12345)
	partnerCode := "CIMB"

	s.mockGrpcClient.EXPECT().
		QueryOnboardingStatus(ctx, &v1.QueryOnboardingStatusRequest{
			ZalopayId:   zalopayID,
			PartnerCode: partnerCode,
		}).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.QueryOnboardingStatus(ctx, zalopayID, partnerCode)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *OnboardingClientTestSuite) TestListOnboardingByUserIDs_Success() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{12345, 67890}

	mockResp := &v1.ListOnboardingByUserIDsResponse{
		Onboardings: []*v1.OnboardingData{
			{
				Id:          1,
				ZalopayId:   12345,
				PartnerCode: "CIMB",
				CurrentStep: "STEP_1",
				RejectCode:  "",
				ProfileInfo: &v1.OnboardingProfile{
					FullName:    "Test User 1",
					PhoneNumber: "0123456789",
					IdNumber:    "123456789",
				},
				StatusFlags: &v1.OnboardingFlags{
					IsOnboarding: true,
					IsUnregister: false,
					IsRejected:   false,
				},
				PartnerData: &v1.PartnerOnboarding{
					CimbData: &v1.CIMBOnboarding{
						OdStatus:             "APPROVED",
						CasaStatus:           "APPROVED",
						SignStatus:           "SIGNED",
						ErrorDetail:          "",
						ManualApprovalReason: "",
					},
				},
			},
			{
				Id:          2,
				ZalopayId:   67890,
				PartnerCode: "CIMB",
				CurrentStep: "STEP_2",
				RejectCode:  "",
				ProfileInfo: &v1.OnboardingProfile{
					FullName:    "Test User 2",
					PhoneNumber: "9876543210",
					IdNumber:    "987654321",
				},
				StatusFlags: &v1.OnboardingFlags{
					IsOnboarding: true,
					IsUnregister: false,
					IsRejected:   false,
				},
				PartnerData: &v1.PartnerOnboarding{
					CimbData: &v1.CIMBOnboarding{
						OdStatus:             "PENDING",
						CasaStatus:           "PENDING",
						SignStatus:           "UNSIGNED",
						ErrorDetail:          "",
						ManualApprovalReason: "",
					},
				},
			},
		},
	}

	s.mockGrpcClient.EXPECT().
		ListOnboardingByUserIDs(ctx, &v1.ListOnboardingByUserIDsRequest{
			ZalopayIds: zalopayIDs,
		}).
		Return(mockResp, nil)

	// Act
	result, err := s.service.ListOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Len(result, 2)

	// Verify first onboarding data
	s.Equal(int64(12345), result[0].ZalopayID)
	s.Equal(int64(1), result[0].OnboardingID)
	s.Equal(partner.CodeFromString("CIMB"), result[0].PartnerCode)
	s.Equal("STEP_1", result[0].CurrentStep)
	s.Equal("Test User 1", result[0].FullName)
	s.Equal("0123456789", result[0].PhoneNumber)
	s.Equal("123456789", result[0].IdentityNumber)
	s.True(result[0].OnboardingStatusFlag.IsOnboarding)
	s.Equal("APPROVED", result[0].CIMBPartnerData.ODStatus)

	// Verify second onboarding data
	s.Equal(int64(67890), result[1].ZalopayID)
	s.Equal(int64(2), result[1].OnboardingID)
	s.Equal("STEP_2", result[1].CurrentStep)
	s.Equal("Test User 2", result[1].FullName)
}

func (s *OnboardingClientTestSuite) TestListOnboardingByUserIDs_EmptyResponse() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{12345, 67890}

	mockResp := &v1.ListOnboardingByUserIDsResponse{
		Onboardings: []*v1.OnboardingData{},
	}

	s.mockGrpcClient.EXPECT().
		ListOnboardingByUserIDs(ctx, &v1.ListOnboardingByUserIDsRequest{
			ZalopayIds: zalopayIDs,
		}).
		Return(mockResp, nil)

	// Act
	result, err := s.service.ListOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Empty(result)
}

func (s *OnboardingClientTestSuite) TestListOnboardingByUserIDs_Error() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{12345, 67890}

	s.mockGrpcClient.EXPECT().
		ListOnboardingByUserIDs(ctx, &v1.ListOnboardingByUserIDsRequest{
			ZalopayIds: zalopayIDs,
		}).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.ListOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.Error(err)
	s.Nil(result)
}
