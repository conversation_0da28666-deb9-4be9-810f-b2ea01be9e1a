package risk_system

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	zpBase "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/zp_base"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/interface"
)

type riskClient struct {
	logger     *log.Helper
	hashKey    string
	grpcClient zpBase.ZPBaseClient
}

func NewClient(conf *config.Account, client zpBase.ZPBaseClient, kLogger log.Logger) _interface.FraudService {
	hashKey := conf.GetFraudRisk().GetHashKey()
	logger := log.NewHelper(log.With(kLogger, "adapters", "risk"))
	return &riskClient{
		logger:     logger,
		hashKey:    hashKey,
		grpcClient: client,
	}
}

// CheckFraud will call API to RMS to check user has fraud
// Input:
// + userID: userID of user
// + deviceID and userIP: current info of device which user is using.
// + reqTime: current time call API (millisecond)
func (r *riskClient) CheckFraud(ctx context.Context, params *dto.UserFraudCheckParams) (*dto.UserFraudCheckResult, error) {
	logger := r.logger.WithContext(ctx)

	fraudData := FraudData{
		UserID:              cast.ToString(params.ZalopayID),
		DeviceID:            params.ClientInfo.DeviceID,
		UserIP:              params.ClientInfo.UserIP,
		AppID:               params.OrderInfo.AppID,
		Amount:              params.OrderInfo.ChargeAmount,
		Platform:            params.ClientInfo.OSPlatform,
		UserLevel:           cast.ToInt32(params.ClientInfo.UserLevel),
		AppVersion:          params.ClientInfo.AppVersion,
		Mno:                 params.OrderInfo.MNO,
		ExUserInfo:          params.ClientInfo.ExUserInfo,
		EmbedData:           params.OrderInfo.EmbedData,
		MerchantName:        params.OrderInfo.MerchantName,
		Description:         params.OrderInfo.Description,
		AppTransID:          params.OrderInfo.AppTransID,
		DeviceModel:         params.ClientInfo.DeviceModel,
		ServiceType:         params.OrderInfo.ServiceType,
		PartnerCode:         params.PartnerCode.String(),
		OrderSource:         params.OrderInfo.OrderSource,
		SourceBalance:       params.AccountBalance,
		OutstandingBalance:  params.AccountOutstanding,
		TotalOverdraftLimit: params.AccountLimit,
	}
	requestID, request, err := r.buildCheckFraudRequest(ctx, fraudData, params.RequestTime, fsCredSource, fsSofEvent)
	if err != nil {
		logger.Errorw("[RiskSystem] fail to build CheckFraud request", "error", err)
		return nil, err
	}

	// Step 2: Convert to BaseRequest of gRPC model & make request
	resp, err := r.grpcClient.SendRequest(ctx, request)

	if err != nil {
		if errors.Is(err, context.Canceled) ||
			errors.Is(err, context.DeadlineExceeded) {
			return nil, model.ErrTimeout
		}
		if st, ok := status.FromError(err); ok && (st.Code() == codes.DeadlineExceeded || st.Code() == codes.Canceled) {
			return nil, model.ErrTimeout
		}
		logger.Errorw("[RiskSystem] fail to call CheckFraud from Risk", "error", err)
		return nil, err
	}

	var response CheckFraudResponse
	if err = json.Unmarshal([]byte(resp.Response), &response); err != nil {
		logger.Errorw("[RiskSystem] fail to unmarshal response to struct", "error", err)
		return nil, err
	}
	if response.ReturnCode != DataCodeSuccess {
		logger.Errorw("[RiskSystem] return code is not success", "response", response)
		return nil, fmt.Errorf("check fraud failed, error=%v", response.ReturnMessage)
	}

	response.RequestID = requestID

	return &dto.UserFraudCheckResult{
		HasFraud:    response.HasFraud(),
		RiskCode:    response.ReturnData.Code,
		InfoCode:    response.ReturnData.InfoCode,
		ActionCode:  response.ReturnData.ActionCode,
		InfoMessage: response.ReturnData.InfoMessage,
	}, nil
}

func (r *riskClient) buildCheckFraudRequest(
	ctx context.Context, data FraudData,
	reqTime int64, source string, event string) (string, *zpBase.GrpcBaseRequest, error) {
	logger := r.logger.WithContext(ctx)

	if data.IsEmpty() {
		logger.Error("Data check fraud is empty, should break flow")
		return "", nil, fmt.Errorf("could not process empty data, %v", data)
	}

	sData, err := data.Serialize()
	if err != nil {
		logger.Errorf("Failed to serialize request data, %v", err)
		return "", nil, err
	}

	// Compute signature = hash(data|curTime|hashKey)
	sign, err := computeSignature(ctx, sData, cast.ToString(reqTime), r.hashKey)
	if err != nil {
		logger.Errorf("Failed to sign request, %v", err)
		return "", nil, err
	}

	// Compute requestID = "{user_id}-{request-time}"
	requestID := fmt.Sprintf("%v-%v", data.UserID, reqTime)
	request := &CheckFraudRequest{
		Sign:        sign,
		Event:       event,
		Source:      source,
		Data:        sData,
		RequestID:   requestID,
		RequestTime: reqTime,
	}
	return requestID, &zpBase.GrpcBaseRequest{
		MethodName: MethodName,
		Params:     convert2MapString(*request),
	}, nil
}
