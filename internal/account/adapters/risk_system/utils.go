package risk_system

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cast"
)

type SignFunc func(values ...string) string

func computeSignature(ctx context.Context, values ...string) (string, error) {
	return computeSignatureWithSeparator(ctx, "|", values...)
}

func computeSignatureWithSeparator(ctx context.Context, separator string, values ...string) (string, error) {
	s := strings.Join(values, separator)
	logger := log.Context(ctx)
	logger.Infow("hash content", s)
	h := sha256.New()
	_, err := h.Write([]byte(s))
	if err != nil {
		logger.Errorf("failed to write bytes, err=%v", err)
		return "", err
	}

	b := h.Sum(nil)
	sha := hex.EncodeToString(b)
	logger.Info("msg", "compute SHA", "content", s, "SHA", sha)
	return sha, nil
}

func convert2MapString(request interface{}) map[string]string {
	var mapInterface = make(map[string]interface{})
	var res = make(map[string]string)

	// Convert struct to map[string]interface{}
	err := mapstructure.Decode(request, &mapInterface)
	if err != nil {
		return res
	}

	// Convert to map[string]string
	for k, v := range mapInterface {
		res[k] = cast.ToString(v)
	}
	return res
}
