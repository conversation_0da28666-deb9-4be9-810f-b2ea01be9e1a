package risk_system

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	zpBase "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/zp_base"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type RiskClientTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	mockGrpcClient *zpBase.MockZPBaseClient
	service        *riskClient
	ctx            context.Context
}

func TestRiskClientSuite(t *testing.T) {
	suite.Run(t, new(RiskClientTestSuite))
}

func (s *RiskClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockGrpcClient = zpBase.NewMockZPBaseClient(s.ctrl)
	s.ctx = context.Background()

	conf := &config.Account{
		FraudRisk: &config.Account_FraudRisk{
			HashKey: "test-hash-key",
		},
	}

	s.service = NewClient(conf, s.mockGrpcClient, log.DefaultLogger).(*riskClient)
}

func (s *RiskClientTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *RiskClientTestSuite) TestCheckFraud_Success() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:    "test-device-id",
			UserIP:      "127.0.0.1",
			OSVersion:   "1.0.0",
			OSPlatform:  "test-os",
			AppVersion:  "1.0.0",
			DeviceModel: "test-model",
			UserLevel:   "1",
			ExUserInfo:  "test-user-info",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
			ServiceType:  "test-service",
			OrderSource:  1,
			EmbedData:    "test-embed-data",
			MNO:          "test-mno",
		},
	}

	mockData := &CheckFraudResponse{
		ReturnCode: 1,
		ReturnData: Data{
			Code:       1,
			InfoCode:   1,
			ActionCode: 1,
		},
	}
	dataRaw, _ := json.Marshal(mockData)
	mockResp := &zpBase.GrpcBaseResponse{
		Response: string(dataRaw),
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.False(result.HasFraud)
	s.Equal(int32(1), result.RiskCode)
	s.Equal(int32(1), result.InfoCode)
	s.Equal(int32(1), result.ActionCode)
}

func (s *RiskClientTestSuite) TestCheckFraud_EmptyData() {
	// Arrange
	params := &dto.UserFraudCheckParams{}

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_GrpcError() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_InvalidResponse() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	mockResp := &zpBase.GrpcBaseResponse{
		Response: `invalid json`,
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestBuildCheckFraudRequest_Success() {
	// Arrange
	data := FraudData{
		UserID:   "12345",
		UserIP:   "127.0.0.1",
		DeviceID: "test-device-id",
	}
	reqTime := time.Now().UnixMilli()

	// Act
	requestID, request, err := s.service.buildCheckFraudRequest(s.ctx, data, reqTime, "test-source", "test-event")

	// Assert
	s.NoError(err)
	s.NotEmpty(requestID)
	s.NotNil(request)
	s.Equal(MethodName, request.MethodName)

	// Verify request params
	params := request.Params
	s.NotEmpty(params["sign"])
	s.Equal("test-source", params["source"])
	s.Equal("test-event", params["event"])
	s.NotEmpty(params["data"])
}

func (s *RiskClientTestSuite) TestBuildCheckFraudRequest_EmptyData() {
	// Arrange
	data := FraudData{}
	reqTime := time.Now().UnixMilli()

	// Act
	requestID, request, err := s.service.buildCheckFraudRequest(s.ctx, data, reqTime, "test-source", "test-event")

	// Assert
	s.Error(err)
	s.Empty(requestID)
	s.Nil(request)
}

func (s *RiskClientTestSuite) TestCheckFraud_ContextCanceled() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, context.Canceled)

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Equal(model.ErrTimeout, err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_ContextDeadlineExceeded() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, context.DeadlineExceeded)

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Equal(model.ErrTimeout, err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_GrpcStatusDeadlineExceeded() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, status.Error(codes.DeadlineExceeded, "deadline exceeded"))

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Equal(model.ErrTimeout, err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_GrpcStatusCanceled() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, status.Error(codes.Canceled, "canceled"))

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Equal(model.ErrTimeout, err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_ReturnCodeNotSuccess() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	mockData := &CheckFraudResponse{
		ReturnCode:    0,
		ReturnMessage: "error message",
		ReturnData: Data{
			Code:       1,
			InfoCode:   1,
			ActionCode: 1,
		},
	}
	dataRaw, _ := json.Marshal(mockData)
	mockResp := &zpBase.GrpcBaseResponse{
		Response: string(dataRaw),
	}

	s.mockGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_SerializeError() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	// Set ZalopayID to 0 to trigger IsEmpty check in FraudData
	params.ZalopayID = 0

	// Act
	result, err := s.service.CheckFraud(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

// Tests for model methods
func TestCheckFraudResponse_HasFraud(t *testing.T) {
	tests := []struct {
		name     string
		response CheckFraudResponse
		want     bool
	}{
		{
			name: "High risk level should return true",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code: int32(HighRiskLevel),
				},
			},
			want: true,
		},
		{
			name: "Medium risk level should return false",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code: int32(MediumRiskLevel),
				},
			},
			want: false,
		},
		{
			name: "Low risk level should return false",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code: int32(LowRiskLevel),
				},
			},
			want: false,
		},
		{
			name: "Non-success return code should return false",
			response: CheckFraudResponse{
				ReturnCode: 0,
				ReturnData: Data{
					Code: int32(HighRiskLevel),
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.response.HasFraud()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCheckFraudResponse_NeedFaceChallenge(t *testing.T) {
	tests := []struct {
		name     string
		response CheckFraudResponse
		want     bool
	}{
		{
			name: "Should return true when conditions are met",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   630_500,
				},
			},
			want: true,
		},
		{
			name: "Should return false when return code is not success",
			response: CheckFraudResponse{
				ReturnCode: 0,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   630_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when risk level is not medium",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(LowRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   630_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when action code is not face authentication",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   630_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is below range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   629_999,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is above range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   632_000,
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.response.NeedFaceChallenge()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCheckFraudResponse_NeedUpdateEKyc(t *testing.T) {
	tests := []struct {
		name     string
		response CheckFraudResponse
		want     bool
	}{
		{
			name: "Should return true when conditions are met",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_050,
				},
			},
			want: true,
		},
		{
			name: "Should return false when return code is not success",
			response: CheckFraudResponse{
				ReturnCode: 0,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_050,
				},
			},
			want: false,
		},
		{
			name: "Should return false when risk level is not medium",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(LowRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_050,
				},
			},
			want: false,
		},
		{
			name: "Should return false when action code is not update KYC",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeFaceAuthentication,
					InfoCode:   600_050,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is below range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_000,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is above range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_101,
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.response.NeedUpdateEKyc()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCheckFraudResponse_NeedContactCS(t *testing.T) {
	tests := []struct {
		name     string
		response CheckFraudResponse
		want     bool
	}{
		{
			name: "Should return true when conditions are met",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactCS,
					InfoCode:   600_150,
				},
			},
			want: true,
		},
		{
			name: "Should return false when return code is not success",
			response: CheckFraudResponse{
				ReturnCode: 0,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactCS,
					InfoCode:   600_150,
				},
			},
			want: false,
		},
		{
			name: "Should return false when risk level is not high",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeContactCS,
					InfoCode:   600_150,
				},
			},
			want: false,
		},
		{
			name: "Should return false when action code is not contact CS",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_150,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is below range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactCS,
					InfoCode:   600_100,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is above range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactCS,
					InfoCode:   600_201,
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.response.NeedContactCS()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestCheckFraudResponse_IsRejected(t *testing.T) {
	tests := []struct {
		name     string
		response CheckFraudResponse
		want     bool
	}{
		{
			name: "Should return true when conditions are met",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactAdmin,
					InfoCode:   600_500,
				},
			},
			want: true,
		},
		{
			name: "Should return false when return code is not success",
			response: CheckFraudResponse{
				ReturnCode: 0,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactAdmin,
					InfoCode:   600_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when risk level is not high",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(MediumRiskLevel),
					ActionCode: ActionCodeContactAdmin,
					InfoCode:   600_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when action code is not contact admin",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeUpdateKyc,
					InfoCode:   600_500,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is below range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactAdmin,
					InfoCode:   600_200,
				},
			},
			want: false,
		},
		{
			name: "Should return false when info code is above range",
			response: CheckFraudResponse{
				ReturnCode: SuccessCode,
				ReturnData: Data{
					Code:       int32(HighRiskLevel),
					ActionCode: ActionCodeContactAdmin,
					InfoCode:   600_701,
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.response.IsRejected()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestFraudData_Serialize(t *testing.T) {
	// Test successful serialization
	data := FraudData{
		UserID:              "12345",
		DeviceID:            "test-device-id",
		UserIP:              "127.0.0.1",
		AppID:               12345,
		Amount:              1000000,
		Platform:            "test-platform",
		UserLevel:           1,
		AppVersion:          "1.0.0",
		Mno:                 "test-mno",
		ExUserInfo:          "test-user-info",
		EmbedData:           "test-embed-data",
		MerchantName:        "test-merchant",
		Description:         "test-description",
		AppTransID:          "test-app-trans-id",
		DeviceModel:         "test-device-model",
		ServiceType:         "test-service-type",
		PartnerCode:         "test-partner-code",
		OrderSource:         1,
		SourceBalance:       500000,
		OutstandingBalance:  500000,
		TotalOverdraftLimit: 1000000,
	}

	// Act
	result, err := data.Serialize()

	// Assert
	assert.NoError(t, err)
	assert.NotEmpty(t, result)

	// Verify the serialized data can be unmarshaled back
	var unmarshaled FraudData
	err = json.Unmarshal([]byte(result), &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, data.UserID, unmarshaled.UserID)
	assert.Equal(t, data.DeviceID, unmarshaled.DeviceID)
	assert.Equal(t, data.UserIP, unmarshaled.UserIP)
}

func TestFraudData_IsEmpty(t *testing.T) {
	tests := []struct {
		name string
		data FraudData
		want bool
	}{
		{
			name: "Empty UserID should return true",
			data: FraudData{
				UserID: "",
			},
			want: true,
		},
		{
			name: "UserID = 0 should return true",
			data: FraudData{
				UserID: "0",
			},
			want: true,
		},
		{
			name: "Valid UserID should return false",
			data: FraudData{
				UserID: "12345",
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.data.IsEmpty()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestConvert2MapString(t *testing.T) {
	// Test with a valid struct
	request := CheckFraudRequest{
		RequestID:   "12345",
		Source:      "test-source",
		Event:       "test-event",
		Data:        "test-data",
		RequestTime: 123456789,
		Sign:        "test-sign",
	}

	// Act
	result := convert2MapString(request)

	// Assert
	assert.Equal(t, "12345", result["requestId"])
	assert.Equal(t, "test-source", result["source"])
	assert.Equal(t, "test-event", result["event"])
	assert.Equal(t, "test-data", result["data"])
	assert.Equal(t, "123456789", result["requestTime"])
	assert.Equal(t, "test-sign", result["sign"])

	// Test with an invalid struct that will cause mapstructure.Decode to fail
	invalidStruct := make(chan int) // channels cannot be encoded
	result = convert2MapString(invalidStruct)
	assert.Empty(t, result)
}

func TestComputeSignatureWithSeparator(t *testing.T) {
	// Test with valid values
	ctx := context.Background()
	result, err := computeSignatureWithSeparator(ctx, "|", "value1", "value2", "value3")

	// Assert
	assert.NoError(t, err)
	assert.NotEmpty(t, result)

	// Test with empty values
	result, err = computeSignatureWithSeparator(ctx, "|", "", "", "")

	// Assert
	assert.NoError(t, err)
	assert.NotEmpty(t, result)

	// Test with nil context
	result, err = computeSignatureWithSeparator(nil, "|", "value1", "value2")

	// Assert
	assert.NoError(t, err)
	assert.NotEmpty(t, result)
}

func (s *RiskClientTestSuite) TestCheckFraud_ContextCanceledWithDeadline() {
	// Arrange
	params := &dto.UserFraudCheckParams{
		ZalopayID:          12345,
		PartnerCode:        partner.PartnerCIMB,
		AccountLimit:       1000000,
		AccountBalance:     500000,
		AccountOutstanding: 500000,
		RequestTime:        time.Now().UnixMilli(),
		ClientInfo: model.ClientRequest{
			DeviceID:   "test-device-id",
			UserIP:     "127.0.0.1",
			OSVersion:  "1.0.0",
			OSPlatform: "test-os",
			AppVersion: "1.0.0",
		},
		OrderInfo: model.OrderRequest{
			AppID:        12345,
			AppTransID:   "12345",
			ChargeAmount: 3000000,
			MerchantName: "test-merchant",
			Description:  "test-description",
		},
	}

	// Create a context with a deadline that has already expired
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(-time.Hour))
	defer cancel()

	// Mock the gRPC client to return a context deadline exceeded error
	s.mockGrpcClient.EXPECT().
		SendRequest(gomock.Any(), gomock.Any()).
		Return(nil, context.DeadlineExceeded)

	// Act
	result, err := s.service.CheckFraud(ctx, params)

	// Assert
	s.Equal(model.ErrTimeout, err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestBuildCheckFraudRequest_FullCoverage() {
	// Create a context with a logger
	ctx := context.Background()

	// Arrange
	data := FraudData{
		UserID:              "12345", // Not empty to pass IsEmpty check
		DeviceID:            "test-device-id",
		UserIP:              "127.0.0.1",
		AppID:               12345,
		Amount:              1000000,
		Platform:            "test-platform",
		UserLevel:           1,
		AppVersion:          "1.0.0",
		Mno:                 "test-mno",
		ExUserInfo:          "test-user-info",
		EmbedData:           "test-embed-data",
		MerchantName:        "test-merchant",
		Description:         "test-description",
		AppTransID:          "test-app-trans-id",
		DeviceModel:         "test-device-model",
		ServiceType:         "test-service-type",
		PartnerCode:         "test-partner-code",
		OrderSource:         1,
		SourceBalance:       500000,
		OutstandingBalance:  500000,
		TotalOverdraftLimit: 1000000,
	}
	reqTime := time.Now().UnixMilli()

	// Act
	requestID, request, err := s.service.buildCheckFraudRequest(ctx, data, reqTime, "test-source", "test-event")

	// Assert
	s.NoError(err)
	s.NotEmpty(requestID)
	s.NotNil(request)
	s.Equal(MethodName, request.MethodName)

	// Verify request params
	params := request.Params
	s.NotEmpty(params["sign"])
	s.Equal("test-source", params["source"])
	s.Equal("test-event", params["event"])
	s.NotEmpty(params["data"])
	s.Equal(fmt.Sprintf("%v-%v", data.UserID, reqTime), requestID)
}
