package risk_system

import (
	"encoding/json"
)

//  API Financial Service Fraud Consulting
//  Spec: https://confluence.zalopay.vn/x/LKUXBg

const (
	MethodName  = "api"
	SuccessCode = 1
)

const (
	fsCredSource = "fs_backend"
	fsSofEvent   = "fs_installment_source_of_fund"
)

// Level is risk level
type Level int32

const (
	HighRiskLevel   Level = -1
	MediumRiskLevel Level = 0
	LowRiskLevel    Level = 1
)

const (
	DataCodeSuccess            = 1
	DataCodeIdentificationFail = 0
	DataCodeRejected           = -1

	ActionCodeFaceAuthentication = 10_000
	ActionCodeUpdateKyc          = 15_000
	ActionCodeContactAdmin       = 20
	ActionCodeContactCS          = 1
)

type CheckFraudRequest struct {
	RequestID   string `mapstructure:"requestId"`
	Source      string `mapstructure:"source"`
	Event       string `mapstructure:"event"`
	Data        string `mapstructure:"data"`
	RequestTime int64  `mapstructure:"requestTime"`
	Sign        string `mapstructure:"sign"`
}

type CheckFraudResponse struct {
	RequestID     string `json:"-"`
	ReturnCode    int    `json:"returncode"`
	ReturnMessage string `json:"returnmessage"`
	ReturnData    Data   `json:"data"`
}

type Data struct {
	Code        int32  `json:"code"`
	InfoCode    int32  `json:"infoCode"`
	ActionCode  int32  `json:"actionCode"`
	InfoMessage string `json:"infoMessage"`
}

func (r *CheckFraudResponse) HasFraud() bool {
	return r.ReturnCode == SuccessCode && Level(r.ReturnData.Code) == HighRiskLevel
}

func (r CheckFraudResponse) NeedFaceChallenge() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == MediumRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeFaceAuthentication &&
		r.ReturnData.InfoCode >= 630_000 && r.ReturnData.InfoCode <= 631_999
}

func (r CheckFraudResponse) NeedUpdateEKyc() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == MediumRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeUpdateKyc &&
		r.ReturnData.InfoCode >= 600_001 && r.ReturnData.InfoCode <= 600_100
}

func (r CheckFraudResponse) NeedContactCS() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == HighRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeContactCS &&
		r.ReturnData.InfoCode >= 600_101 && r.ReturnData.InfoCode <= 600_200
}

func (r CheckFraudResponse) IsRejected() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == HighRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeContactAdmin &&
		r.ReturnData.InfoCode >= 600_201 && r.ReturnData.InfoCode <= 600_700
}

type FraudData struct {
	UserID              string `json:"userId"`
	DeviceID            string `json:"deviceId"`
	UserIP              string `json:"userIp"`
	PmcID               int32  `json:"pmcid"`
	AppID               int32  `json:"appid"`
	Amount              int64  `json:"amount"`
	Platform            string `json:"platform"`
	UserLevel           int32  `json:"userlevel"`
	AppVersion          string `json:"appver"`
	Mno                 string `json:"mno"`
	ExUserInfo          string `json:"exuserinfo"`
	EmbedData           string `json:"embeddata"`
	MerchantName        string `json:"merchantname"`
	Description         string `json:"description"`
	AppTransID          string `json:"apptransid"`
	DeviceModel         string `json:"devicemodel"`
	ServiceType         string `json:"servicetype"`
	PartnerCode         string `json:"partnercode"`
	OrderSource         int32  `json:"ordersource"`
	SourceBalance       int64  `json:"sourcebalance"`
	OutstandingBalance  int64  `json:"outstadingbalance"`
	TotalOverdraftLimit int64  `json:"totaloverdraftlimit"`
}

func (d FraudData) Serialize() (string, error) {
	raw, err := json.Marshal(d)
	if err != nil {
		return "", err
	}
	return string(raw), nil
}

func (d FraudData) IsEmpty() bool {
	return d.UserID == "" || d.UserID == "0"
}
