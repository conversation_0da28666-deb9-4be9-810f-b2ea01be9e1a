package utils

import (
	"net/url"

	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
)

var DeepLinkProviderSet = wire.NewSet(NewDeepLinkCraft)

//go:generate mockgen --destination=./mocks/deeplink.go --package=utils_mocks . DeepLinkCraft
type DeepLinkCraft interface {
	GetApplicationLink() model.DeepLinkData
	GetApplicationLinkUtm(utmSource string) model.DeepLinkData
}

type DeepLinks map[model.DeepLinkKey]model.DeepLinkData

type deepLinkDeps struct {
	deeplinks DeepLinks
}

func NewDeepLinkCraft(config *config.Account) DeepLinkCraft {
	deepLinksConf := config.GetResources().GetDeeplinks()
	deepLinksMap := initDeepLinksFromMap(deepLinksConf)
	return &deepLinkDeps{deeplinks: deepLinksMap}
}

func initDeepLinksFromMap(data map[string]*config.DeepLink) DeepLinks {
	deepLinks := make(map[model.DeepLinkKey]model.DeepLinkData)
	for k, v := range data {
		deepLinks[model.DeepLinkKey(k)] = model.DeepLinkData{
			ZPAUrl:    v.GetZpaUrl(),
			ZPIUrl:    v.GetZpiUrl(),
			CommonUrl: v.GetCommonUrl(),
		}
	}
	return deepLinks
}

func (d *deepLinkDeps) getDeepLinkByKey(key model.DeepLinkKey) model.DeepLinkData {
	if link, ok := d.deeplinks[key]; ok {
		return link
	}
	return d.deeplinks[model.LinkKeyDefault]
}

func (d *deepLinkDeps) GetApplicationLink() model.DeepLinkData {
	linkData := d.getDeepLinkByKey(model.LinkKeyApplication)
	return linkData
}

func (d *deepLinkDeps) GetApplicationLinkUtm(utmSource string) model.DeepLinkData {
	linkParams := url.Values{}
	linkParams.Add("utm_source", utmSource)

	linkData := d.getDeepLinkByKey(model.LinkKeyApplication)
	linkData.ZPAUrl = appendParams(linkData.ZPAUrl, linkParams)
	linkData.ZPIUrl = appendParams(linkData.ZPIUrl, linkParams)
	linkData.CommonUrl = appendParams(linkData.CommonUrl, linkParams)
	return linkData
}

func appendParams(originalURL string, params url.Values) string {
	if originalURL == "" {
		return originalURL
	}
	u, err := url.Parse(originalURL)
	if err != nil {
		return originalURL
	}
	// Append query parameters
	q := u.Query()
	for key, values := range params {
		for _, value := range values {
			q.Add(key, value)
		}
	}
	u.RawQuery = q.Encode()
	return u.String()
}
