// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package cmd

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/zombie/server"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(configServer *config.Server, logger log.Logger) (*kratos.App, func(), error) {
	httpServer := server.NewHTTPServer(configServer, logger)
	app := newApp(logger, httpServer)
	return app, func() {
	}, nil
}
