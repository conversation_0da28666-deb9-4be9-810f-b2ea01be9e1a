package server

import (
	nethttp "net/http"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *config.Server, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	srv.HandleFunc("/info", handleHealth)
	srv.HandleFunc("/health", handleHealth)
	return srv
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(nethttp.StatusOK)
	w.Write([]byte("OK"))
}
