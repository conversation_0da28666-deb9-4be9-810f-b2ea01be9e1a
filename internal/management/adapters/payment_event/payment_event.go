package payment_event

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	kafka_client "zalopay.io/zgo/kafka-client"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
)

type paymentEventPub struct {
	logger         *log.Helper
	paymentCommPub kafka_client.Publisher
}

func NewPaymentEventPub(paymentCommPub kafka_client.Publisher, kLogger log.Logger) _interface.PaymentEventPublisher {
	logger := log.NewHelper(log.With(kLogger, "adapters", "payment-event-publisher"))
	return &paymentEventPub{
		logger:         logger,
		paymentCommPub: paymentCommPub,
	}
}

func (p paymentEventPub) PublishPaymentCommission(ctx context.Context, event *dto.PaymentCommissionEvent) error {
	logger := p.logger.WithContext(ctx)

	logger.Infof("publishing payment commission event: %v", event)

	messageKey := uuid.NewString()
	messageData, err := json.Marshal(event)
	if err != nil {
		logger.Errorf("failed to marshal payment commission event: %v", err)
		return errors.Wrap(err, "failed to marshal payment commission event")
	}

	if err = p.paymentCommPub.PublishRaw(ctx, messageKey, messageData); err != nil {
		logger.Errorf("failed to publish payment commission event: %v", err)
		return errors.Wrap(err, "failed to publish payment commission event")
	}

	logger.Infof("publish payment commission event success")
	return nil
}
