package adapters

import (
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/job_task"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/msteams"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/payment_event"
)

var ProviderSet = wire.NewSet(
	msteams.NewClient,
	dist_lock.NewDistLock,
	job_task.NewJobTaskMgmt,
	auth_service.NewClient,
	cimb_service.NewService,
	account_service.NewClient,
	payment_event.NewPaymentEventPub,
)
