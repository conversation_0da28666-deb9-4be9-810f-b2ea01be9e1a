package auth_service

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"

	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
)

func NewClient(client sessionv1.SessionServiceClient, logger log.Logger) auth.Authenticator {
	return &authenticator{
		client: client,
		logger: log.NewHelper(log.With(logger, "adapter", "auth_service")),
	}
}

type authenticator struct {
	logger *log.Helper
	client sessionv1.SessionServiceClient
}

func (a *authenticator) Authenticate(ctx context.Context, sessionID string) (*sessionv1.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session: id empty")
	}

	session, err := a.client.GetSession(ctx, &sessionv1.GetSessionReq{
		SessionId: sessionID,
	})

	if err != nil {
		a.logger.WithContext(ctx).Erro<PERSON>("get session failed session_id: %s, error: %v", sessionID, err)
		return nil, errors.Errorf("get session failed: %v", err)
	}

	return session, nil
}
