package adaptivecardv1

import (
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// TextBlock
// - Description: Displays text, allowing control over font sizes, weight, and color.
// - Version: 1.0
type TextBlock struct {
	// Type
	// - Description: Must be "TextBlock".
	// - Required: true.
	// - Version: 1.0
	Type Type `json:"type,omitempty"`
	// Text
	// - Description: Text to display. A subset of markdown is supported.
	// - Required: true.
	// (https://aka.ms/ACTextFeatures)
	// - Version: 1.0
	Text string `json:"text,omitempty"`
	// Color
	// - Description: Controls the color of TextBlock elements.
	// - Required: false.
	// - Version: 1.0
	Color Color `json:"color,omitempty"`
	// FontType
	// - Description: Specify which font to use for rendering.
	// - Required: false.
	// - Version: 1.0
	FontType string `json:"fontType,omitempty"`
	// HorizontalAlignment
	// - Description: Controls the horizontal text alignment.
	// When not specified, the value of horizontalAlignment is inherited
	// from the parent container. If no parent container has horizontalAlignment
	// set, it defaults to Left.
	// - Required: false.
	// - Version: 1.0
	HorizontalAlignment HorizontalAlignment `json:"horizontalAlignment,omitempty"`
	// IsSubtle
	// - Description: If true, displays text slightly toned down to appear less prominent.
	// - Required: false.
	// - Version: 1.0
	IsSubtle bool `json:"isSubtle,omitempty"`
	// MaxLines
	// - Description: Specifies the maximum number of lines to display.
	// - Required: false.
	// - Version: >= 1.0
	MaxLines int32 `json:"maxLines,omitempty"`
	// Size
	// - Description: Controls size of text.
	// - Required: false.
	// - Version: >= 1.0
	Size FontSize `json:"size,omitempty"`
	// Weight
	// - Description: Controls the weight of TextBlock elements.
	// - Required: false.
	// - Version: >= 1.0
	Weight FontWeight `json:"weight,omitempty"`
	// Wrap
	// - Description: If true, allow text to wrap. Otherwise, text is clipped.
	// - Required: false.
	// - Version: >= 1.0
	Wrap bool `json:"wrap,omitempty"`
	// Style
	// - Description: The style of this TextBlock for accessibility purposes.
	// - Required: false.
	// - Version: >= 1.0
	Style TextBlockStyle `json:"style,omitempty"`
}

func (textBlock *TextBlock) GetType() Type {
	return TextBlockType
}

func (textBlock *TextBlock) Validate() error {
	return validation.ValidateStruct(
		textBlock,
		validation.Field(textBlock.Type, validation.In(TextBlockType)),
		validation.Field(textBlock.Text, validation.Required),
	)
}
