package adaptivecardv1

import (
	"fmt"
)

// Column
// - Description: Defines a container that is part of a ColumnSet.
type Column struct {
	// Items
	// - Description: The card elements to render inside the Column.
	// - Required: false
	// - Version: >= 1.0
	Items []Node `json:"items,omitempty"`
	// BackgroundImage
	// - Description: Specifies the background image. Acceptable formats are PNG, JPEG, and GIF
	// - Required: false
	// - Version: >= 1.2
	BackgroundImage *BackgroundImage `json:"backgroundImage,omitempty"`
	// Bleed
	// - Description: Determines whether the column should bleed through its parent’s padding.
	// - Required: false
	// - Version: >= 1.2
	Bleed bool `json:"bleed,omitempty"`
	// MinHeight
	// - Description: Specifies the minimum height of the column in pixels, like "80px".
	// - Required: false
	// - Version: >= 1.2
	MinHeight Height `json:"minHeight,omitempty"`
	// Rtl
	// - Description: When true content in this column should be presented right to left.
	// When ‘false’ content in this column should be presented left to right. When unset layout
	// direction will inherit from parent container or column. If unset in all ancestors,
	// the default platform behavior will apply.
	// - Required: false
	// - Version: >= 1.5
	Rtl bool `json:"rtl,omitempty"`
	// Separator
	// - Description: When true, draw a separating line between this column and the previous column.
	// - Required: false
	// - Version: >= 1.0
	Separator bool `json:"separator,omitempty"`
	// Spacing
	// - Description: Controls the amount of spacing between this column and the preceding column.
	// - Required: false
	// - Version: >= 1.0
	Spacing Spacing `json:"spacing,omitempty"`
	// Style
	// - Description: Style hint for Column.
	// - Required: false
	// - Version: >= 1.0
	Style ContainerStyle `json:"style,omitempty"`
	// VerticalContentAlignment
	// - Description: Defines how the content should be aligned vertically within the column.
	// When not specified, the value of verticalContentAlignment is inherited from the parent container.
	// If no parent container has verticalContentAlignment set, it defaults to Top.
	// - Required: false
	// - Version: >= 1.1
	VerticalContentAlignment VerticalAlignment `json:"verticalContentAlignment,omitempty"`
	// Width
	// - Description: "auto", "stretch", a number representing relative width of the column in the
	// column group, or in version 1.1 and higher, a specific pixel width, like "50px".
	// - Required: false
	// - Version: >= 1.1
	Width int32 `json:"width,omitempty"`
	// SelectAction
	// - Description: Not implemented
	// - Required: false
	// - Version: >= 1.1
	// TODO: SelectAction SelectAction `json:"//
}

type Height int32

func (h *Height) String() string {
	return fmt.Sprintf("%dpx", h)
}
