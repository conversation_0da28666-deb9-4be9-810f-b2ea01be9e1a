package adaptivecardv1

type GridStyle CardOpt

const (
	DefaultGridStyle   GridStyle = "default"
	EmphasisGridStyle  GridStyle = "emphasis"
	AttentionGridStyle GridStyle = "attention"
	WarningGridStyle   GridStyle = "warning"
	AccentGridStyle    GridStyle = "accent"
)

type CardOpt string

type Version CardOpt

const (
	v1_0 Version = "1.0"
)

type Type CardOpt

const (
	MessageType      Type = "message"
	AdaptiveCardType Type = "AdaptiveCard"
	TextBlockType    Type = "TextBlock"
	MentionType      Type = "mention"
	TableType        Type = "Table"
	TableRowType     Type = "TableRow"
	TableCellType    Type = "TableCell"
)

type Color CardOpt

const (
	DefaultColor   Color = "default"
	DarkColor      Color = "dark"
	LightColor     Color = "light"
	AccentColor    Color = "accent"
	GoodColor      Color = "good"
	WarningColor   Color = "warning"
	AttentionColor Color = "attention"
)

type HorizontalAlignment CardOpt

const (
	LeftHorizontalAlignment   HorizontalAlignment = "left"
	CenterHorizontalAlignment HorizontalAlignment = "center"
	RightHorizontalAlignment  HorizontalAlignment = "right"
)

type VerticalAlignment CardOpt

const (
	TopVerticalAlignment    VerticalAlignment = "top"
	CenterVerticalAlignment VerticalAlignment = "center"
	BottomVerticalAlignment VerticalAlignment = "bottom"
)

type FontSize CardOpt

const (
	DefaultFontSize    FontSize = "default"
	SmallFontSize      FontSize = "small"
	MediumFontSize     FontSize = "medium"
	LargeFontSize      FontSize = "large"
	ExtraLargeFontSize FontSize = "extraLarge"
)

type FontWeight CardOpt

const (
	LighterFontWeight FontWeight = "lighter"
	DefaultFontWeight FontWeight = "default"
	BolderFontWeight  FontWeight = "bolder"
)

type TextBlockStyle CardOpt

const (
	DefaultTextBlockStyle TextBlockStyle = "default"
	HeadingTextBlockStyle TextBlockStyle = "heading"
)

type ImageFillMode CardOpt

const (
	// CoverImageFillMode - The background image covers the entire
	// width of the container. Its aspect ratio is preserved.
	// Content may be clipped if the aspect ratio of the image
	// doesn't match the aspect ratio of the container. verticalAlignment
	// is respected (horizontalAlignment is meaningless since it's stretched width).
	// This is the default mode and is the equivalent to the current model.
	CoverImageFillMode ImageFillMode = "cover"
	// RepeatHorizontallyImageFillMode - The background image isn't stretched.
	// It is repeated in the x axis as many times as necessary to cover the container's
	// width. verticalAlignment is honored (default is top), horizontalAlignment is ignored.
	RepeatHorizontallyImageFillMode ImageFillMode = "repeatHorizontally"
	// RepeatVerticallyImageFillMode - The background image isn't stretched. It is repeated
	// in the y axis as many times as necessary to cover the container's height. verticalAlignment
	// is ignored, horizontalAlignment is honored (default is left).
	RepeatVerticallyImageFillMode ImageFillMode = "repeatVertically"
	// RepeatImageFillMode - The background image isn't stretched. It is repeated first in the x
	// axis then in the y axis as many times as necessary to cover the entire container. Both
	// horizontalAlignment and verticalAlignment are honored (defaults are left and top).
	RepeatImageFillMode ImageFillMode = "repeat"
)

type Spacing CardOpt

const (
	DefaultSpacing    Spacing = "Default"
	NoneSpacing       Spacing = "none"
	SmallSpacing      Spacing = "small"
	MediumSpacing     Spacing = "medium"
	LargeSpacing      Spacing = "large"
	ExtraLargeSpacing Spacing = "extraLarge"
	PaddingSpacing    Spacing = "padding"
)

type ContainerStyle CardOpt

const (
	DefaultContainerStyle  ContainerStyle = "default"
	EmphasisContainerStyle ContainerStyle = "emphasis"
	// GoodContainerStyle
	// - Version: >= 1.2
	GoodContainerStyle ContainerStyle = "good"
	// AttentionContainerStyle
	// - Version: >= 1.2
	AttentionContainerStyle ContainerStyle = "attention"
	// WarningContainerStyle
	// - Version: >= 1.2
	WarningContainerStyle ContainerStyle = "warning"
	// AccentContainerStyle
	// - Version: >= 1.2
	AccentContainerStyle ContainerStyle = "accent"
)
