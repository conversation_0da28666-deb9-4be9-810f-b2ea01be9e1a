package adaptivecardv1

// BackgroundImage
// - Description: Specifies a background image. Acceptable formats are PNG, JPEG, and GIF
// - Version: >= 1.2
type BackgroundImage struct {
	// URL
	// Description: The URL (or data url) of the image. Acceptable formats are PNG, JPEG, and GIF
	// Required: true.
	// Version: >= 1.2
	URL string `json:"url,omitempty"`
	// FillMode
	// - Description: Describes how the image should fill the area.
	// - Required: false.
	// - Version: >= 1.2
	FillMode ImageFillMode `json:"fillMode,omitempty"`
	// HorizontalAlignment
	// - Description: Describes how the image should be aligned if it must be cropped or if using repeat fill mode.
	// - Required: false.
	// - Version: >= 1.2
	HorizontalAlignment HorizontalAlignment `json:"horizontalAlignment,omitempty"`
	// VerticalAlignment
	// - Description: Describes how the image should be aligned if it must be cropped or if using repeat fill mode.
	// - Required: false.
	// - Version: >= 1.2
	VerticalAlignment VerticalAlignment `json:"verticalAlignment,omitempty"`
}
