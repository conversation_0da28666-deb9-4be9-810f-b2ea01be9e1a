package adaptivecardv1

type PendingRedemptionCardBuilder struct {
	card AttachmentCard
}

func NewPendindRedemptionCardBuilder() *PendingRedemptionCardBuilder {
	return &PendingRedemptionCardBuilder{
		card: AttachmentCard{
			Type: MessageType,
			Attachments: []Attachment{
				{
					ContentType: AdaptiveCardContentType,
					Content: Content{
						Schema:  AdaptiveCardSchema,
						Version: v1_0,
						Type:    AdaptiveCardType,
						MsTeams: MsTeams{
							Entities: make([]Node, 0),
							Width:    "Full",
						},
						Body: make([]Node, 0),
					},
				},
			},
		},
	}
}

func (prcb *PendingRedemptionCardBuilder) AddBodyNode(nodes ...Node) *PendingRedemptionCardBuilder {
	if len(nodes) == 0 {
		return prcb
	}
	prcb.card.Attachments[0].Content.Body = append(prcb.card.Attachments[0].Content.Body, nodes...)
	return prcb
}

func (prcb *PendingRedemptionCardBuilder) AddMentions(nodes ...*Mention) *PendingRedemptionCardBuilder {
	if len(nodes) == 0 {
		return prcb
	}
	for _, m := range nodes {
		prcb.card.Attachments[0].Content.MsTeams.Entities = append(prcb.card.Attachments[0].Content.MsTeams.Entities, m)
	}
	return prcb
}

func (prcb *PendingRedemptionCardBuilder) Build() AttachmentCard {
	return prcb.card
}
