package adaptivecardv1

type TableRow struct {
	// Type
	// - Description: Must be "TableRow"
	// - Required: false
	// - Version: >= 1.5
	Type Type `json:"type,omitempty"`
	// Cells
	// - Description: Define cells in a row
	// - Required: false
	// - Version: >= 1.5
	Cells []TableCell `json:"cells,omitempty"`
	// Style
	// - Description: Row style
	// - Required: false
	// - Version: >= 1.5
	Style ContainerStyle `json:"style,omitempty"`
}
