package adaptivecardv1

import (
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// Mentioned link a @ tag to an identity in MS Teams, .i.e email, id
type Mentioned struct {
	// ID: could be MS Teams ID or email
	// Required: true
	ID string `json:"id,omitempty"`
	// Name: representation of the tag on the card
	// Required: true
	Name string `json:"name,omitempty"`
}

// Mention MS Teams entity that allow Adaptive Card to mention a person/tag
type Mention struct {
	// Type: Must be "mention"
	Type Type `json:"type,omitempty"`
	// Text: Text block in body that would be replaced by mention
	// Required: true.
	Text string `json:"text,omitempty"`
	// Mentioned: Describe how a tag should be linked to a person
	// Required: true.
	Mentioned *Mentioned `json:"mentioned,omitempty"`
}

type MentionBuilder struct {
	mention Mention
}

func NewMentionBuilder() *MentionBuilder {
	return &MentionBuilder{mention: Mention{Type: MentionType, Mentioned: &Mentioned{}}}
}

func (mb *MentionBuilder) Text(text string) *MentionBuilder {
	mb.mention.Text = text
	return mb
}

func (mb *MentionBuilder) Id(id string) *MentionBuilder {
	mb.mention.Mentioned.ID = id
	return mb
}

func (mb *MentionBuilder) Name(name string) *MentionBuilder {
	mb.mention.Mentioned.Name = name
	return mb
}

func (mb *MentionBuilder) Build() Mention {
	return mb.mention
}

func (mb *MentionBuilder) BuildNode() *Mention {
	mention := mb.mention
	return &mention
}

func (mention *Mention) GetType() Type {
	return MentionType
}

func (mention *Mention) Validate() error {
	err := validation.ValidateStruct(
		mention,
		validation.Field(mention.Type, validation.In(TextBlockType)),
		validation.Field(mention.Text, validation.Required),
		validation.Field(mention.Mentioned, validation.Required),
	)
	if err != nil {
		return err
	}
	return mention.Mentioned.Validate()
}

func (mentioned *Mentioned) Validate() error {
	return validation.ValidateStruct(
		mentioned,
		validation.Field(mentioned.ID, validation.Required),
		validation.Field(mentioned.Name, validation.Required),
	)
}
