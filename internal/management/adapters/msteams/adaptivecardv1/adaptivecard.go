package adaptivecardv1

type AttachmentCard struct {
	Type        Type         `json:"type,omitempty"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

type Attachment struct {
	ContentType string  `json:"contentType,omitempty"`
	ContentUrl  string  `json:"contentUrl,omitempty"`
	Content     Content `json:"content,omitempty"`
}

type Content struct {
	Schema  string  `json:"$schema,omitempty"`
	Type    Type    `json:"type,omitempty"`
	Version Version `json:"version,omitempty"`
	Body    []Node  `json:"body,omitempty"`
	MsTeams MsTeams `json:"msteams,omitempty"`
}

// MsTeams allows bot to access MS Teams utilities like tags, mentions, etc.
type MsTeams struct {
	// Entities contains description of MS Teams utility used
	Entities []Node `json:"entities,omitempty"`
	// Width
	// Description: control how card span on ui
	// Only support: default and Full
	Width string `json:"width,omitempty"`
}

const (
	AdaptiveCardContentType = "application/vnd.microsoft.card.adaptive"
	AdaptiveCardSchema      = "http://adaptivecards.io/schemas/adaptive-card.json"
)

type Node interface {
	GetType() Type
	Validate() error
}
