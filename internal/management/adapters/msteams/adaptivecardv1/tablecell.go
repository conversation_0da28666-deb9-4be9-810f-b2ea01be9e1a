package adaptivecardv1

import (
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// TableCell
// - Description: Represents a cell within a row of a Table element.
// - Version: >= 1.5
type TableCell struct {
	// Type
	// - Description: Must be "TableCell"
	// - Required: false
	// - Version: >= 1.0
	Type Type `json:"type,omitempty"`
	// Items
	// - Description: The card elements to render inside the TableCell.
	// - Required: true
	// - Version: >= 1.5
	Items []Node `json:"items,omitempty"`
	// Style
	// - Description: Style hint for TableCell.
	// - Required: false
	// - Version: >= 1.5
	Style ContainerStyle `json:"style,omitempty"`
	// VerticalContentAlignment
	// - Description: Defines how the content should be aligned vertically within the container.
	// When not specified, the value of verticalContentAlignment is inherited from the parent container.
	// If no parent container has verticalContentAlignment set, it defaults to Top.
	// - Required: false
	// - Version: >= 1.1
	VerticalContentAlignment VerticalAlignment `json:"verticalContentAlignment,omitempty"`
	// Bleed
	// - Description: Determines whether the element should bleed through its parent’s padding.
	// - Required: false
	// - Version: >= 1.2
	Bleed bool `json:"bleed,omitempty"`
	// BackgroundImage
	// - Description: Specifies the background image. Acceptable formats are PNG, JPEG, and GIF
	// - Required: false
	// - Version: >= 1.2
	BackgroundImage *BackgroundImage `json:"backgroundImage,omitempty"`
	// MinHeight
	// - Description: Specifies the minimum height of the container in pixels, like "80px".
	// - Required: false
	// - Version: >= 1.2
	MinHeight Height `json:"minHeight,omitempty"`
	// RightToLeft
	// - Description: When true content in this container should be presented right to left.
	// When ‘false’ content in this container should be presented left to right. When unset layout
	// direction will inherit from parent container or column. If unset in all ancestors, the default
	// platform behavior will apply.
	// - Required: false
	// - Version: >= 1.5
	RightToLeft bool `json:"rtl,omitempty"`
}

func (tableCell *TableCell) GetType() Type {
	return tableCell.Type
}

func (tableCell *TableCell) Validate() error {
	return validation.ValidateStruct(
		tableCell,
		validation.Field(tableCell.Type, validation.In(TableCellType)),
	)
}
