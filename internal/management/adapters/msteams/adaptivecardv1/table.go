package adaptivecardv1

import (
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// Table provides a way to display data in a tabular form.
// Version 1.5 and above
type Table struct {
	// Type: Must be "Table".
	// Required: true.
	// Version: >= 1.5
	Type Type `json:"type,omitempty"`
	// Text: Defines the number of columns in the table, their sizes, and more.
	// Required: false.
	// Version: >= 1.5
	Columns []*Column `json:"columns,omitempty"`
	// Rows: Defines the rows of the table.
	// Required: false.
	// Version: >= 1.5
	Rows []*TableRow `json:"rows,omitempty"`
	// FirstRowAsHeader: Specifies whether the first row of the table should
	// be treated as a header row, and be announced as such by
	// accessibility software.
	// Required: false.
	// Default: true.
	// Version: >= 1.5
	FirstRowAsHeader bool `json:"firstRowAsHeader,omitempty"`
	// ShowGridLines: Specifies whether grid lines should be displayed.
	// Required: false.
	// Default: true.
	// Version: >= 1.5
	ShowGridLines bool `json:"showGridLines,omitempty"`
	// GridStyle: Defines the style of the grid.
	// This property currently only controls the grid’s color.
	// Required: false.
	// Default: "default".
	// Version: >= 1.5
	GridStyle GridStyle `json:"gridStyle,omitempty"`
	// HorizontalCellContentAlignment: Controls how the content of all cells is
	// horizontally aligned by default. When not specified, horizontal alignment
	// is defined on a per-cell basis.
	// Required: false.
	// Version: >= 1.5
	HorizontalCellContentAlignment HorizontalAlignment `json:"horizontalCellContentAlignment,omitempty"`
	// VerticalCellContentAlignment: CControls how the content of all cells is
	// vertically aligned by default. When not specified, vertical alignment is
	// defined on a per-cell basis.
	// Required: false.
	// Version: >= 1.5
	VerticalCellContentAlignment HorizontalAlignment `json:"verticalCellContentAlignment,omitempty"`
}

func (table *Table) GetType() Type {
	return TableType
}

func (table *Table) Validate() error {
	return validation.ValidateStruct(
		table,
		validation.Field(table.Type, validation.In(TextBlockType)),
	)
}
