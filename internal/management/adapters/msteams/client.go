package msteams

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/msteams/adaptivecardv1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
)

type client struct {
	config     Config
	logger     *log.Helper
	httpClient httpclient.Client
}

func NewClient(
	logger log.Logger,
	config Config) _interface.MSTeamsNotifier {
	httpConf := &httpclient.Config{
		BaseURL:  config.Host,
		ProxyURL: config.ProxyURL,
	}

	return &client{
		config:     config,
		logger:     log.NewHelper(logger),
		httpClient: httpclient.NewClient(httpConf, metrics.ClientNoop{}),
	}
}

func (c *client) SendInstallmentSyncStatsToChannel(ctx context.Context, stats *dto.InstallmentSyncStats) error {
	params := SendMessageToTeamChannelParams{
		Title: syncInstTitle,
		Text: fmt.Sprintf(syncInstMsgTempl,
			time.Now().Format(time.RFC822),
			c.config.Env, stats.TotalItems, stats.TotalSuccess,
			stats.TotalFailed, stats.ListFailedIDs,
		),
	}
	return c.SendMessageToTeamChannel(ctx, params)
}

func (c *client) SendMessageToTeamChannel(ctx context.Context, request SendMessageToTeamChannelParams) error {
	logger := c.logger.WithContext(ctx)
	logger.Infow("sending POST request: ", "data", request)

	var err error
	var response interface{}

	ctx, cancel := context.WithTimeout(ctx, requestTimeoutUpperBound)
	defer cancel()
	httpCode, err := c.httpClient.Post(ctx, MSTeamEndpoint.String(), request, &response, err, "SendMessageToTeamChannel")
	if httpCode >= 300 { //nolint: gomnd
		err = fmt.Errorf("[MSTeams] send message error %s", err)
		return err
	}
	return err
}

func (c *client) SendAttachmentCardToTeamChannel(ctx context.Context, request adaptivecardv1.AttachmentCard) error {
	logger := c.logger.WithContext(ctx)
	logger.Infow("sending POST request: ", "data", request)

	var err error
	var response interface{}
	ctx, cancel := context.WithTimeout(ctx, requestTimeoutUpperBound)
	defer cancel()
	httpCode, err := c.httpClient.Post(ctx, MSTeamEndpoint.String(), request, &response, err, "SendAttachmentCardToTeamChannel")
	if httpCode >= 300 { //nolint: gomnd
		err = fmt.Errorf("[MSTeams] send message error %s", err)
		return err
	}
	return err
}
