package msteams

import (
	"time"
)

type Endpoint string

func (e Endpoint) String() string {
	return string(e)
}

const (
	MSTeamEndpoint Endpoint = ""

	// 34 chose as a number close to 30 that is likely to be unique enough to jump out at me the next time I see a timeout.
	// Everyone chooses 30.
	requestTimeoutUpperBound = 34 * time.Second
)

type SendMessageToTeamChannelParams struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}
