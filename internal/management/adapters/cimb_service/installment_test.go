package cimb_service

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

// CIMBServiceTestSuite defines the test suite for CIMB service
type CIMBServiceTestSuite struct {
	suite.Suite
	ctrl          *gomock.Controller
	mockConnector *connector.MockCIMBConnectorClient
	service       *service
	ctx           context.Context
}

// SetupTest prepares the test suite before each test
func (s *CIMBServiceTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockConnector = connector.NewMockCIMBConnectorClient(s.ctrl)
	s.service = &service{
		cimbConnector: s.mockConnector,
		logger:        log.NewHelper(log.With(log.DefaultLogger, "ts", log.DefaultTimestamp, "caller", log.DefaultCaller)),
	}
	s.ctx = context.Background()
}

// TearDownTest cleans up after each test
func (s *CIMBServiceTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestQueryEarlyDischargeByID_Success tests the successful case for QueryEarlyDischargeByID
func (s *CIMBServiceTestSuite) TestQueryEarlyDischargeByID_Success() {
	// Arrange
	zalopayID := int64(123456)
	loanID := "LOAN123"

	// Create mock response
	mockResponse := &connector.GetInstallmentLoanResponse{
		LoanId:     loanID,
		LoanStatus: string(model.PartnerCIMBInstStatusNormal),
		EarlyDischarge: &connector.InstEarlyDischarge{
			OutstandingPrincipal: 500000,
			OutstandingInterest:  50000,
			OutstandingPenalty:   5000,
			EarlyDischargeFee:    10000,
			TotalDischargeAmount: 565000,
		},
	}

	// Set up expectations
	s.mockConnector.EXPECT().
		GetInstallmentLoan(gomock.Any(), &connector.GetInstallmentLoanRequest{
			ZalopayId:           zalopayID,
			QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: loanID},
			IncludeDischargeFee: true,
		}).
		Return(mockResponse, nil)

	// Act
	result, err := s.service.QueryEarlyDischargeByID(s.ctx, zalopayID, loanID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(loanID, result.ID)
	s.False(result.HasDischarged)
	s.NotNil(result.DischargeData)
	s.Equal(int64(500000), *result.DischargeData.OutstandingPrincipal)
	s.Equal(int64(50000), *result.DischargeData.OutstandingInterest)
	s.Equal(int64(5000), *result.DischargeData.OutstandingPenalty)
	s.Equal(int64(10000), result.DischargeData.EarlyDischargeFee)
	s.Equal(int64(565000), result.DischargeData.TotalDischargeAmount)
}

// TestQueryEarlyDischargeByID_ClosedLoan tests the case where the loan is already closed
func (s *CIMBServiceTestSuite) TestQueryEarlyDischargeByID_ClosedLoan() {
	// Arrange
	zalopayID := int64(123456)
	loanID := "LOAN123"

	// Create mock response for a closed loan
	mockResponse := &connector.GetInstallmentLoanResponse{
		LoanId:     loanID,
		LoanStatus: string(model.PartnerCIMBInstStatusClosed),
		EarlyDischarge: &connector.InstEarlyDischarge{
			OutstandingPrincipal: 0,
			OutstandingInterest:  0,
			OutstandingPenalty:   0,
			EarlyDischargeFee:    0,
			TotalDischargeAmount: 0,
		},
	}

	// Set up expectations
	s.mockConnector.EXPECT().
		GetInstallmentLoan(gomock.Any(), &connector.GetInstallmentLoanRequest{
			ZalopayId:           zalopayID,
			QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: loanID},
			IncludeDischargeFee: true,
		}).
		Return(mockResponse, nil)

	// Act
	result, err := s.service.QueryEarlyDischargeByID(s.ctx, zalopayID, loanID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(loanID, result.ID)
	s.True(result.HasDischarged) // Should be true for closed loans
	s.NotNil(result.DischargeData)
	s.Equal(int64(0), *result.DischargeData.OutstandingPrincipal)
	s.Equal(int64(0), *result.DischargeData.OutstandingInterest)
	s.Equal(int64(0), *result.DischargeData.OutstandingPenalty)
	s.Equal(int64(0), result.DischargeData.EarlyDischargeFee)
	s.Equal(int64(0), result.DischargeData.TotalDischargeAmount)
}

// TestQueryEarlyDischargeByID_NoDischargeData tests the case where no early discharge data is returned
func (s *CIMBServiceTestSuite) TestQueryEarlyDischargeByID_NoDischargeData() {
	// Arrange
	zalopayID := int64(123456)
	loanID := "LOAN123"

	// Create mock response without early discharge data
	mockResponse := &connector.GetInstallmentLoanResponse{
		LoanId:     loanID,
		LoanStatus: string(model.PartnerCIMBInstStatusNormal),
		// No EarlyDischarge field
	}

	// Set up expectations
	s.mockConnector.EXPECT().
		GetInstallmentLoan(gomock.Any(), &connector.GetInstallmentLoanRequest{
			ZalopayId:           zalopayID,
			QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: loanID},
			IncludeDischargeFee: true,
		}).
		Return(mockResponse, nil)

	// Act
	result, err := s.service.QueryEarlyDischargeByID(s.ctx, zalopayID, loanID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(loanID, result.ID)
	s.False(result.HasDischarged)
	s.Nil(result.DischargeData) // DischargeData should be nil when no data is returned
}

// TestQueryEarlyDischargeByID_Error tests the error case
func (s *CIMBServiceTestSuite) TestQueryEarlyDischargeByID_Error() {
	// Arrange
	zalopayID := int64(123456)
	loanID := "LOAN123"
	expectedError := errors.New("connection error")

	// Set up expectations for error case
	s.mockConnector.EXPECT().
		GetInstallmentLoan(gomock.Any(), &connector.GetInstallmentLoanRequest{
			ZalopayId:           zalopayID,
			QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: loanID},
			IncludeDischargeFee: true,
		}).
		Return(nil, expectedError)

	// Act
	result, err := s.service.QueryEarlyDischargeByID(s.ctx, zalopayID, loanID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "failed to get installment loan for retrieve early discharge from cimb")
}

// TestCIMBServiceSuite runs all the tests in the suite
func TestCIMBServiceSuite(t *testing.T) {
	suite.Run(t, new(CIMBServiceTestSuite))
}
