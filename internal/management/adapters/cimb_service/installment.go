package cimb_service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (s service) QueryInstallmentByTransID(ctx context.Context,
	zalopayID, transID int64) (*model.PartnerCIMBInst, error) {
	resp, err := s.cimbConnector.GetInstallmentLoan(ctx, &connector.GetInstallmentLoanRequest{
		ZalopayId: zalopayID,
		QueryBy:   &connector.GetInstallmentLoanRequest_TransId{TransId: cast.ToString(transID)},
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get installment loan from cimb: %v", err)
		return nil, errors.Wrap(err, "failed to get installment loan from cimb")
	}
	return s.convertToInstallment(resp), nil
}

func (s service) QueryInstallmentByID(ctx context.Context, zalopayID int64, instID string) (*model.PartnerCIMBInst, error) {
	resp, err := s.cimbConnector.GetInstallmentLoan(ctx, &connector.GetInstallmentLoanRequest{
		ZalopayId:           zalopayID,
		QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: instID},
		IncludeDischargeFee: false,
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get installment loan from cimb: %v", err)
		return nil, errors.Wrap(err, "failed to get installment loan from cimb")
	}
	return s.convertToInstallment(resp), nil
}

func (s service) QueryPlanOptions(ctx context.Context, zalopayID int64, orderAmount int64) ([]*model.PartnerPlanOption, error) {
	resp, err := s.cimbConnector.GetInstallmentPlans(ctx, &connector.GetInstallmentPlansRequest{
		ZalopayId:     zalopayID,
		ProductType:   connector.ProductType_PRODUCT_TYPE_INSTALLMENT,
		RequestAmount: orderAmount,
		InstBeginDate: time.Now().Format("2006-01-02"),
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get installment plans from cimb: %v", err)
		return nil, errors.Wrap(err, "failed to get installment plans from cimb")
	}

	partnerPlans := resp.GetPlans()
	planOptions := make([]*model.PartnerPlanOption, 0, len(partnerPlans))

	for _, plan := range partnerPlans {
		avgEmiAmount := s.calcAvgPlanEmiAmount(plan)
		principalDec := decimal.NewFromInt(plan.PrincipalAmount)
		interestDec := decimal.NewFromInt(plan.InterestAmount)
		flatInterestRate := interestDec.Div(principalDec).Round(2)
		flatInterestRateVal := flatInterestRate.InexactFloat64()
		planOptions = append(planOptions, &model.PartnerPlanOption{
			EmiAmount:         avgEmiAmount,
			TenorNumber:       int64(plan.Tenor),
			RbiInterestRate:   plan.InterestRate,
			FlatInterestRate:  flatInterestRateVal,
			TotalAmount:       plan.RepaymentAmount,
			PrincipalAmount:   plan.PrincipalAmount,
			InterestAmount:    plan.InterestAmount,
			InstallmentAmount: plan.EmiAmount,
			RepaymentSchedule: s.convertPlanRepaymentSchedule(plan.RepaymentSchedule),
		})
	}
	return planOptions, nil
}

func (s service) QueryEarlyDischargeByID(ctx context.Context, zalopayID int64, loanID string) (*model.PartnerEarlyDischarge, error) {
	logger := s.logger.WithContext(ctx)

	resp, err := s.cimbConnector.GetInstallmentLoan(ctx, &connector.GetInstallmentLoanRequest{
		ZalopayId:           zalopayID,
		QueryBy:             &connector.GetInstallmentLoanRequest_LoanId{LoanId: loanID},
		IncludeDischargeFee: true,
	})
	if err != nil {
		logger.Errorf("failed to get installment loan for retrieve early discharge from cimb: %v", err)
		return nil, errors.Wrap(err, "failed to get installment loan for retrieve early discharge from cimb")
	}

	loanID = resp.GetLoanId()
	loanStatus := s.convertToPartnerStatus(resp.GetLoanStatus())
	earlyDischargeData := resp.GetEarlyDischarge()
	return s.convertToEarlyDischarge(loanID, loanStatus, earlyDischargeData), nil
}

func (s service) convertToInstallment(resp *connector.GetInstallmentLoanResponse) *model.PartnerCIMBInst {
	loanDueDate, _ := time.Parse("2006-01-02", resp.LoanDueDate)
	loanStartDate, _ := time.Parse("2006-01-02", resp.LoanStartDate)
	loanEndDate, _ := time.Parse("2006-01-02", resp.LoanEndDate)
	sysReportDate, _ := time.Parse("2006-01-02", resp.SysReportingDate)
	repayDueDate, _ := time.Parse("2006-01-02", resp.RepaymentDueDate)
	nextRepayDueDate, _ := time.Parse("2006-01-02", resp.NextRepaymentDueDate)
	loanStatus := s.convertToPartnerStatus(resp.LoanStatus)
	loanRepaysSchedule := s.convertInstRepaymentSchedule(resp.RepaymentSchedule)
	earlyDischargeData := s.convertToEarlyDischarge(resp.LoanId, loanStatus, resp.GetEarlyDischarge())

	return &model.PartnerCIMBInst{
		ID:                      resp.LoanId,
		Tenure:                  resp.ApprovedTenor,
		Status:                  loanStatus,
		DueDate:                 loanDueDate,
		StartDate:               loanStartDate,
		EndDate:                 loanEndDate,
		ReportDate:              sysReportDate,
		DaysPastDue:             resp.DayPastDue,
		InterestRate:            resp.InterestRate,
		DisburseAmount:          resp.DisbursedAmount,
		CurrRepayDueDate:        repayDueDate,
		NextRepayDueDate:        nextRepayDueDate,
		EarlyDischargeInfo:      earlyDischargeData,
		RepaymentSchedules:      loanRepaysSchedule,
		OutstandingOriginal:     resp.OutstandingPrincipal,
		OutstandingDueTotal:     resp.OutstandingDueAmount,
		OutstandingDuePenalty:   resp.OutstandingDuePenalty,
		OutstandingDuePrincipal: resp.OutstandingDuePrincipal,
		OutstandingDueInterest:  resp.OutstandingDueInterest,
	}
}

func (s service) convertToPartnerStatus(status string) model.PartnerCIMBInstStatus {
	return model.PartnerCIMBInstStatus(status)
}

func (s service) convertInstRepaymentSchedule(schedules []*connector.InstRepaySchedule) []*model.PartnerCIMBInstRepaySchedule {
	// Filter valid schedules
	schedulesValid := make([]*connector.InstRepaySchedule, 0, len(schedules))
	for _, schedule := range schedules {
		if schedule.PeriodNo != 0 && schedule.EmiAmount != 0 {
			schedulesValid = append(schedulesValid, schedule)
		}
	}

	// Convert to model
	schedulesResult := make([]*model.PartnerCIMBInstRepaySchedule, 0, len(schedulesValid))
	for _, schedule := range schedulesValid {
		startDate, _ := time.Parse("2006-01-02", schedule.PeriodStart)
		endDate, _ := time.Parse("2006-01-02", schedule.PeriodEnd)
		// CIMB does not provide repay due date, use end date instead
		repayDueDate := endDate
		graceDueDate, _ := time.Parse("2006-01-02", schedule.GraceDueDate)
		schedulesResult = append(schedulesResult, &model.PartnerCIMBInstRepaySchedule{
			IsDue:                schedule.IsDue > 0,
			SeqNo:                schedule.PeriodNo,
			StartDate:            startDate,
			EndDate:              endDate,
			RepayDueDate:         repayDueDate,
			GraceDueDate:         graceDueDate,
			PaidPenalty:          schedule.PaidPenalty,
			EmiAmount:            schedule.EmiAmount,
			InterestAmount:       schedule.InterestAmount,
			PrincipalAmount:      schedule.EmiAmount - schedule.InterestAmount,
			OutstandingAmount:    schedule.OutstandingAmount,
			OutstandingPenalty:   schedule.OutstandingPenalty,
			OutstandingInterest:  schedule.OutstandingInterest,
			OutstandingPrincipal: schedule.OutstandingPrincipal,
		})
	}

	return schedulesResult
}

func (s service) convertToEarlyDischarge(loanID string, loanStatus model.PartnerCIMBInstStatus, data *connector.InstEarlyDischarge) *model.PartnerEarlyDischarge {
	result := &model.PartnerEarlyDischarge{
		ID:            loanID,
		HasDischarged: loanStatus == model.PartnerCIMBInstStatusClosed,
	}
	if data == nil {
		return result
	}

	result.DischargeData = &model.EarlyDischargeData{
		OutstandingPrincipal: zutils.Ptr(data.GetOutstandingPrincipal()),
		OutstandingInterest:  zutils.Ptr(data.GetOutstandingInterest()),
		OutstandingPenalty:   zutils.Ptr(data.GetOutstandingPenalty()),
		EarlyDischargeFee:    data.GetEarlyDischargeFee(),
		TotalDischargeAmount: data.GetTotalDischargeAmount(),
	}
	return result
}

/**
 * CalcAvgPlanEmiAmount using cimb recipe provided to calculate the average emi amount
 * TODO,IMPORTANT: Maybe we should move this func to a usecase package for reusable and easy managed by partner
 * Repice: ROUNDUP_PMT(P,r,n,d) = [(P*r)/(1-(1+r)^(-n)) * 10^d] * 10^(-d)
 * P: Principal amount
 * r: Interest rate
 * n: Tenure
 * d: Decimal places (2)
 */
func (s service) calcAvgPlanEmiAmount(plan *connector.InstallmentPlan) int64 {
	// Convert to decimal
	ratioDec := decimal.NewFromInt(1)
	constDec := decimal.NewFromInt(10)
	tenureDec := decimal.NewFromInt32(plan.Tenor)
	yearMonthDec := decimal.NewFromInt(12)
	tenureDecNeg := tenureDec.Neg()
	principalDec := decimal.NewFromInt(plan.PrincipalAmount)
	iRatePerYearDec := decimal.NewFromFloat(plan.InterestRate)
	iRatePerMonthDec := iRatePerYearDec.Div(yearMonthDec)
	decimalPlacesDec := decimal.NewFromInt(2)
	decimalPlacesNegDec := decimalPlacesDec.Neg()

	// Calculate emi amount
	exp1Dec := principalDec.Mul(iRatePerMonthDec)
	exp2Dec := ratioDec.Sub((ratioDec.Add(iRatePerMonthDec)).Pow(tenureDecNeg))
	exp3Dec := constDec.Pow(decimalPlacesDec)
	exp4Dec := constDec.Pow(decimalPlacesNegDec)
	emiDec := exp1Dec.Div(exp2Dec).Mul(exp3Dec).Mul(exp4Dec)

	return emiDec.IntPart()
}

func (s service) convertPlanRepaymentSchedule(schedules []*connector.PlanRepaySchedule) []model.PartnerRepaySchedule {
	// Filter valid schedules
	schedulesValid := make([]*connector.PlanRepaySchedule, 0, len(schedules))
	for _, schedule := range schedules {
		if schedule.PeriodNo != 0 && schedule.EmiAmount != 0 {
			schedulesValid = append(schedulesValid, schedule)
		}
	}

	// Convert to model
	schedulesResult := make([]model.PartnerRepaySchedule, 0, len(schedulesValid))
	for _, schedule := range schedulesValid {
		startDate, _ := time.Parse("2006-01-02", schedule.PeriodStart)
		endDate, _ := time.Parse("2006-01-02", schedule.PeriodEnd)
		schedulesResult = append(schedulesResult, model.PartnerRepaySchedule{
			InstNo:          schedule.PeriodNo,
			EmiAmount:       schedule.EmiAmount,
			PrincipleAmount: schedule.PrincipalAmount,
			InterestAmount:  schedule.InterestAmount,
			StartDate:       startDate,
			EndDate:         endDate,
		})
	}
	return schedulesResult
}
