package cimb_service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func (s service) GetStatementByIncurredDate(ctx context.Context,
	account *model.Account, incurredDate time.Time) (*model.StatementResult, error) {
	logger := s.logger.WithContext(ctx)

	resp, err := s.cimbConnector.GetStatement(ctx, &connector.GetStatementRequest{
		ZalopayId:             account.ZalopayID,
		CustomerAccountNumber: account.PartnerAccountNumber,
		StatementDate:         incurredDate.Format("2006-01-02"),
	})
	if err != nil {
		logger.Errorf("GetStatement fail: %v", err)
		return nil, errors.Wrap(err, "GetStatement fail")
	}

	stmtData := resp.GetData()
	stmtDueDate, _ := time.Parse("2006-01-02", stmtData.GetRepaymentGraceEndDate())
	stmtIncurredDate, _ := time.Parse("2006-01-02", stmtData.GetStatementDate())

	stmtInfo := &model.Statement{
		AccountID:         account.ID,
		ZalopayID:         account.ZalopayID,
		PartnerCode:       account.PartnerCode,
		DueDate:           stmtDueDate,
		IncurredDate:      stmtIncurredDate,
		OutstandingAmount: stmtData.GetTotalOutstandingAmount(),
		OutstandingRepaid: stmtData.GetRepaidAmount(),
	}
	stmtInsts := toStatementInstallments(stmtInfo, stmtData.GetInstallmentListType3())

	return &model.StatementResult{
		StatementDetail: stmtInfo,
		StatementInsts:  stmtInsts,
	}, nil
}

func (s service) CheckStatementsEligibleByIncurredDate(ctx context.Context, incurredDate time.Time) (bool, error) {
	logger := s.logger.WithContext(ctx)
	resp, err := s.cimbConnector.ListStatements(ctx, &connector.ListStatementsRequest{
		Page:          1,
		PageSize:      1,
		ProductType:   productType,
		StatementDate: incurredDate.Format("2006-01-02"),
	})
	if err != nil {
		logger.Errorf("ListStatements fail: %v", err)
		return false, errors.Wrap(err, "ListStatements fail")
	}
	if len(resp.GetData().GetStatements()) == 0 {
		logger.Warnw("msg", "No statement data", "statementData", resp.GetData())
		return false, nil
	}
	if incurredDate.Format("2006-01-02") != resp.GetData().GetStatementDate() {
		logger.Warnw("msg", "No statement incurred", "incurredDate", incurredDate, "statementData", resp.GetData())
		return false, nil
	}
	return true, nil
}

/*
  - GetStatementDueOutstanding get outstanding balance of all due installment capture in current user statement
  - This method is use the api get realtime OD outstanding by account info to retrieve data
    because in Installment context, this api to return due + overdue outstanding balance of all due installment
    capture in current user statement
*/
func (s service) GetStatementDueOutstanding(ctx context.Context, account *model.Account) (*model.StatementOutstanding, error) {
	logger := s.logger.WithContext(ctx)

	resp, err := s.cimbConnector.InquiryODOutstanding(ctx, &connector.InquiryODOutstandingRequest{
		ZalopayId:     cast.ToString(account.ZalopayID),
		AccountNumber: account.PartnerAccountNumber,
	})
	if err != nil {
		logger.Errorf("InquiryODOutstanding fail: %v", err)
		return nil, errors.Wrap(err, "InquiryODOutstanding fail from cimb")
	}
	return &model.StatementOutstanding{OutstandingBalance: resp.GetOutstandingAmount()}, nil
}

func toStatementInstallments(
	stmtInfo *model.Statement,
	listInst []*connector.StatementInstallment) []*model.StatementInstallment {
	stmtInsts := make([]*model.StatementInstallment, 0, len(listInst))
	for _, inst := range listInst {
		dueDateTime, _ := time.Parse(time.RFC3339, inst.GetDueDate())
		stmtInsts = append(stmtInsts, &model.StatementInstallment{
			DueDate:           dueDateTime,
			ZalopayID:         stmtInfo.ZalopayID,
			AccountID:         stmtInfo.AccountID,
			StatementID:       stmtInfo.ID,
			StatementDate:     stmtInfo.IncurredDate,
			PartnerCode:       stmtInfo.PartnerCode,
			PartnerInstID:     inst.GetLoanId(),
			InstallmentAmount: inst.GetInstallmentAmount(),
			OutstandingData: &model.StatementInstallmentOuts{
				TotalOutstanding:        inst.GetTotalOutstandingAmount(),
				OutstandingPrincipal:    inst.GetOutstandingPrincipal(),
				OutstandingInterest:     inst.GetOutstandingInterest(),
				OutstandingPenalty:      inst.GetOutstandingPenalty(),
				OutstandingDuePrincipal: inst.GetOutstandingDuePrincipal(),
				OutstandingDueInterest:  inst.GetOutstandingDueInterest(),
				OutstandingDuePenalty:   inst.GetOutstandingDuePenalty(),
			},
		})
	}
	return stmtInsts
}
