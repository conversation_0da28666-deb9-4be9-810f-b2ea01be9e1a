package cimb_service

import (
	"math/rand"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func buildMockStatementInstallments(stmt *model.Statement) []*model.StatementInstallment {
	installments := make([]*model.StatementInstallment, 0)
	for i := 0; i < 3; i++ {
		installment := &model.StatementInstallment{
			DueDate:           time.Now().AddDate(0, 0, i*7),
			AccountID:         stmt.AccountID,
			ZalopayID:         stmt.ZalopayID,
			PartnerCode:       stmt.PartnerCode,
			PartnerInstID:     uuid.NewString(),
			StatementDate:     stmt.IncurredDate,
			InstallmentAmount: int64(rand.Intn(500000-200000) + 200000),
		}
		installments = append(installments, installment)
	}
	return installments
}

func buildMockPlanOptions(orderAmount int64) []*connector.InstallmentPlan {
	tenureList := []int64{3, 6, 9, 12}
	flatInterestRate := 0.36    // 36% per year, annual plat interest rate
	dimesInterestRate := 0.3974 // 39.74% per year, annual dimes interest rate
	mockData := make([]*connector.InstallmentPlan, 0, len(tenureList))

	for _, tenure := range tenureList {
		tenureDec := decimal.NewFromInt(tenure)
		disburseDate := time.Now().Format("2006-01-02")
		interestRateDec := decimal.NewFromFloat(flatInterestRate)
		interestRatePerM := interestRateDec.Div(decimal.NewFromInt(12))
		refPrincipleAmt := decimal.NewFromInt(orderAmount)
		refInterestRate := interestRatePerM.Mul(decimal.NewFromInt(tenure))
		refInterestAmt := decimal.NewFromInt(orderAmount).Mul(refInterestRate).Ceil()
		refOutstandingAmt := refPrincipleAmt.Add(refInterestAmt)
		refEmiAmt := refOutstandingAmt.Div(tenureDec).Ceil()
		planOption := &connector.InstallmentPlan{
			Tenor:             int32(tenure),
			EmiAmount:         refEmiAmt.IntPart(),
			PrincipalAmount:   refPrincipleAmt.IntPart(),
			InterestAmount:    refInterestAmt.IntPart(),
			InterestRate:      dimesInterestRate,
			RepaymentAmount:   refOutstandingAmt.IntPart(),
			DisbursementDate:  disburseDate,
			RepaymentSchedule: buildMockRepaymentSchedule(tenure, refPrincipleAmt, refInterestAmt),
		}
		mockData = append(mockData, planOption)
	}
	return mockData
}

func buildMockRepaymentSchedule(tenure int64,
	principleAmt decimal.Decimal,
	interestAmt decimal.Decimal) []*connector.PlanRepaySchedule {
	const stmtDay = 15
	var result []*connector.PlanRepaySchedule

	timeNow := time.Now()
	fPeriodTime := time.Date(timeNow.Year(), timeNow.Month(), stmtDay, 0, 0, 0, 0, timeNow.Location())
	if timeNow.Day() > stmtDay {
		fPeriodTime = fPeriodTime.AddDate(0, 1, 0)
	}

	for no := int64(0); no < tenure; no++ {
		periodPrincipleAmt := principleAmt.Div(decimal.NewFromInt(tenure)).Ceil().IntPart()
		periodInterestAmt := interestAmt.Div(decimal.NewFromInt(tenure)).Ceil().IntPart()
		periodExpectEmiAmt := periodPrincipleAmt + periodInterestAmt
		periodStartTime := fPeriodTime.AddDate(0, int(no), 0)
		periodEndTime := periodStartTime.AddDate(0, 0, 2)

		if no+1 == tenure {
			periodPrincipleAmt = principleAmt.IntPart() - periodPrincipleAmt*(tenure-1)
			periodInterestAmt = interestAmt.IntPart() - periodInterestAmt*(tenure-1)
			periodExpectEmiAmt = periodPrincipleAmt + periodInterestAmt
		}

		result = append(result, &connector.PlanRepaySchedule{
			PeriodNo:        int32(no + 1),
			PeriodStart:     periodStartTime.Format("2006-01-02"),
			PeriodEnd:       periodEndTime.Format("2006-01-02"),
			EmiAmount:       periodExpectEmiAmt,
			InterestAmount:  periodInterestAmt,
			PrincipalAmount: periodPrincipleAmt,
		})
	}

	return result

}
