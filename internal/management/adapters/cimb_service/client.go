package cimb_service

import (
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
)

type service struct {
	logger        *log.Helper
	cimbConnector connector.CIMBConnectorClient
}

const (
	productType = "INSTALLMENT"
)

func NewService(client connector.CIMBConnectorClient, kLogger log.Logger) _interface.CIMBService {
	logging := log.With(kLogger, "adapters", "cimb_connector")
	return &service{
		cimbConnector: client,
		logger:        log.NewHelper(logging),
	}
}
