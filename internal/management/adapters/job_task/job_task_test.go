package job_task

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.temporal.io/sdk/mocks"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type JobTaskTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	tempCli *mocks.Client
	jobTask _interface.JobTaskMgmt
}

func TestJobTaskTestSuite(t *testing.T) {
	suite.Run(t, new(JobTaskTestSuite))
}

func (s *JobTaskTestSuite) SetupTest() {
	conf := &config.Management{
		Schedulers: &config.Management_Schedulers{
			SyncInstallmentInfo: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
			SyncLatestStatement: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
			SyncAccountBalance: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
		},
	}

	s.ctrl = gomock.NewController(s.T())
	s.tempCli = mocks.NewClient(s.T())
	s.jobTask = NewJobTaskMgmt(conf, s.tempCli, log.DefaultLogger)
}

func (s *JobTaskTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// Test ExecuteSyncLatestStatementTask
func (s *JobTaskTestSuite) TestExecuteSyncLatestStatementTask_Success() {
	ctx := context.Background()
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   12345,
		AccountID:   67890,
		PartnerCode: partner.PartnerCIMB,
	}

	mockWorkflowRun := mocks.NewWorkflowRun(s.T())
	mockWorkflowRun.On("GetRunID").Return("test-run-id")

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(mockWorkflowRun, nil)

	err := s.jobTask.ExecuteSyncLatestStatementTask(ctx, params)
	s.NoError(err)
}

func (s *JobTaskTestSuite) TestExecuteSyncLatestStatementTask_ExecuteWorkflowError() {
	ctx := context.Background()
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   12345,
		AccountID:   67890,
		PartnerCode: partner.PartnerCIMB,
	}

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(nil, errors.New("workflow execution failed"))

	err := s.jobTask.ExecuteSyncLatestStatementTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "register sync latest statement task failed")
}

// Test ExecuteSyncInstallmentTask
func (s *JobTaskTestSuite) TestExecuteSyncInstallmentTask_Success() {
	ctx := context.Background()
	params := &dto.SyncInstallmentParams{
		InstID:    123,
		ZPTransID: 456,
		InstallmentSyncOptions: dto.InstallmentSyncOptions{
			PollingTime: 5 * time.Second,
		},
	}

	mockWorkflowRun := mocks.NewWorkflowRun(s.T())
	mockWorkflowRun.On("GetRunID").Return("test-run-id")

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(mockWorkflowRun, nil)

	err := s.jobTask.ExecuteSyncInstallmentTask(ctx, params)
	s.NoError(err)
}

func (s *JobTaskTestSuite) TestExecuteSyncInstallmentTask_ExecuteWorkflowError() {
	ctx := context.Background()
	params := &dto.SyncInstallmentParams{
		InstID:    123,
		ZPTransID: 456,
	}

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(nil, errors.New("workflow execution failed"))

	err := s.jobTask.ExecuteSyncInstallmentTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "register sync installment task failed")
}

// Test ExecuteSyncAccountBalanceAfterDischargeTask
func (s *JobTaskTestSuite) TestExecuteSyncAccountBalanceAfterDischargeTask_Success() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceAfterDischargeParams{
		ZalopayID: 12345,
		AccountID: 67890,
	}

	mockWorkflowRun := mocks.NewWorkflowRun(s.T())
	mockWorkflowRun.On("GetRunID").Return("test-run-id")

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(mockWorkflowRun, nil)

	err := s.jobTask.ExecuteSyncAccountBalanceAfterDischargeTask(ctx, params)
	s.NoError(err)
}

func (s *JobTaskTestSuite) TestExecuteSyncAccountBalanceAfterDischargeTask_ExecuteWorkflowError() {
	ctx := context.Background()
	params := &dto.SyncAccountBalanceAfterDischargeParams{
		ZalopayID: 12345,
		AccountID: 67890,
	}

	s.tempCli.On("ExecuteWorkflow", mock.Anything, mock.Anything, "test-workflow", params).
		Return(nil, errors.New("workflow execution failed"))

	err := s.jobTask.ExecuteSyncAccountBalanceAfterDischargeTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "register sync account balance task failed")
}

// Test nil temporal client scenarios
func (s *JobTaskTestSuite) TestExecuteSyncLatestStatementTask_NilTemporalClient() {
	// Create job task with nil temporal client
	conf := &config.Management{
		Schedulers: &config.Management_Schedulers{
			SyncLatestStatement: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
		},
	}
	jobTaskWithNilClient := NewJobTaskMgmt(conf, nil, log.DefaultLogger)

	ctx := context.Background()
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   12345,
		AccountID:   67890,
		PartnerCode: partner.PartnerCIMB,
	}

	err := jobTaskWithNilClient.ExecuteSyncLatestStatementTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "temporal client is not initialized")
}

func (s *JobTaskTestSuite) TestExecuteSyncInstallmentTask_NilTemporalClient() {
	// Create job task with nil temporal client
	conf := &config.Management{
		Schedulers: &config.Management_Schedulers{
			SyncInstallmentInfo: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
		},
	}
	jobTaskWithNilClient := NewJobTaskMgmt(conf, nil, log.DefaultLogger)

	ctx := context.Background()
	params := &dto.SyncInstallmentParams{
		InstID:    123,
		ZPTransID: 456,
	}

	err := jobTaskWithNilClient.ExecuteSyncInstallmentTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "temporal client is not initialized")
}

func (s *JobTaskTestSuite) TestExecuteSyncAccountBalanceAfterDischargeTask_NilTemporalClient() {
	// Create job task with nil temporal client
	conf := &config.Management{
		Schedulers: &config.Management_Schedulers{
			SyncAccountBalance: &config.TemporalTask{
				QueueName:    "test-queue",
				WorkflowType: "test-workflow",
			},
		},
	}
	jobTaskWithNilClient := NewJobTaskMgmt(conf, nil, log.DefaultLogger)

	ctx := context.Background()
	params := &dto.SyncAccountBalanceAfterDischargeParams{
		ZalopayID: 12345,
		AccountID: 67890,
	}

	err := jobTaskWithNilClient.ExecuteSyncAccountBalanceAfterDischargeTask(ctx, params)
	s.Error(err)
	s.Contains(err.Error(), "temporal client is not initialized")
}
