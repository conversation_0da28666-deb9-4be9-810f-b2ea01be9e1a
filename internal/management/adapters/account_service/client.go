package account_service

import (
	"context"
	"slices"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type client struct {
	logger     *log.Helper
	grpcClient v1.AccountClient
}

func NewClient(grpcClient v1.AccountClient, kLogger log.Logger) _interface.AccountService {
	logging := log.With(kLogger, "adapters", "account_service")
	return &client{
		grpcClient: grpcClient,
		logger:     log.NewHelper(logging),
	}
}

func (c client) GetActiveAccountByID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error) {
	logger := c.logger.WithContext(ctx)

	resp, err := c.grpcClient.GetAccount(ctx, &v1.GetAccountRequest{
		ZalopayId: zalopayID,
		QueryBy:   &v1.GetAccountRequest_AccountId{AccountId: accountID},
	})
	if err != nil {
		logger.Errorf("failed to get account by account id: %v", err)
		return nil, errors.Wrap(err, "failed to get account by account id")
	}

	return fromUserAccountToAccount(zalopayID, resp.GetAccount()), nil
}

func (c client) GetActiveAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	logger := c.logger.WithContext(ctx)

	resp, err := c.grpcClient.GetAccount(ctx, &v1.GetAccountRequest{
		ZalopayId: zalopayID,
		QueryBy:   &v1.GetAccountRequest_PartnerCode{PartnerCode: partnerCode.String()},
	})
	if err != nil {
		logger.Errorf("failed to get active account by partner: %v", err)
		return nil, errors.Wrap(err, "failed to get active account by partner")
	}

	return fromUserAccountToAccount(zalopayID, resp.GetAccount()), nil
}

func (c client) ListAccountForStmtSync(ctx context.Context, params *dto.AccountForStatementParams) ([]*model.Account, error) {
	logger := c.logger.WithContext(ctx)

	accounts, err := c.grpcClient.ListAccountForStmtSync(ctx, &v1.ListAccountForStmtSyncRequest{
		ItemsLimit:    params.TotalLimit,
		AccountIdFrom: params.FromAccountID,
		StatementDate: timestamppb.New(params.StatementDate),
		PartnerCodes:  partner.ListToStrings(params.PartnerCodes),
	})
	if err != nil {
		logger.Errorf("failed to list account for statement sync: %v", err)
		return nil, err
	}

	var result []*model.Account
	for _, account := range accounts.GetAccounts() {
		result = append(result, fromStmtAccountToAccount(account))
	}

	slices.SortFunc(result, func(i, j *model.Account) int {
		return int(i.ID - j.ID)
	})

	return result, nil
}

func fromStmtAccountToAccount(account *v1.StmtAccount) *model.Account {
	return &model.Account{
		ID:                   account.GetAccountId(),
		ZalopayID:            account.GetZalopayId(),
		IsActive:             isAccountActive(account.GetStatus()),
		PartnerCode:          partner.CodeFromString(account.GetPartnerCode()),
		PartnerAccountName:   account.GetPartnerAccountName(),
		PartnerAccountNumber: account.GetPartnerAccountNumber(),
	}
}

func fromUserAccountToAccount(zalopayID int64, account *v1.UserAccount) *model.Account {
	return &model.Account{
		ID:                   account.GetAccountId(),
		ZalopayID:            zalopayID,
		IsActive:             isAccountActive(account.GetStatus()),
		PartnerCode:          partner.CodeFromString(account.GetPartnerCode()),
		PartnerAccountName:   account.GetPartnerAccountName(),
		PartnerAccountNumber: account.GetPartnerAccountNumber(),
	}
}

func isAccountActive(accountStatus v1.Status) bool {
	return accountStatus == v1.Status_ACTIVE || accountStatus == v1.Status_BLOCKED
}
