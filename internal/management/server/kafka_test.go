package server

import (
	"context"
	"errors"
	"io"
	"math"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/server/mocks"
	kafka_client "zalopay.io/zgo/kafka-client"
)

type KafkaConsumerTestSuite struct {
	suite.Suite
	ctrl        *gomock.Controller
	mockWorker1 *mocks.MockRetryWorker
	mockWorker2 *mocks.MockRetryWorker
	mockConfig  *config.Kafka
	processFunc kafka_client.ProcessFunc
}

func TestKafkaConsumerSuite(t *testing.T) {
	suite.Run(t, new(KafkaConsumerTestSuite))
}

func (s *KafkaConsumerTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockWorker1 = mocks.NewMockRetryWorker(s.ctrl)
	s.mockWorker2 = mocks.NewMockRetryWorker(s.ctrl)

	// Create a simple process function for testing
	s.processFunc = nil

	// Setup default mock config
	numWorkers := int32(2)
	groupId := "test-group"
	s.mockConfig = &config.Kafka{
		Brokers:    "localhost:9092,localhost:9093",
		Topic:      "test-topic",
		GroupId:    &groupId,
		NumWorkers: &numWorkers,
	}
}

func (s *KafkaConsumerTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func TestNewKafkaConsumer_NewKafkaConsumer(t *testing.T) {
	kafkaConf := &config.Kafka{
		Brokers: "localhost:9092,localhost:9093",
		Topic:   "test-topic",
		GroupId: func() *string { v := "test-group"; return &v }(),
	}
	processFunc := func(message kafka.Message) error {
		return nil
	}
	NewKafkaConsumer(kafkaConf, processFunc)
}

func (s *KafkaConsumerTestSuite) TestNewKafkaConsumer_WorkerNumsCalculation() {
	// Test worker nums calculation logic
	testCases := []struct {
		name            string
		numWorkers      *int32
		expectedWorkers int32
	}{
		{
			name:            "Multiple workers",
			numWorkers:      func() *int32 { v := int32(3); return &v }(),
			expectedWorkers: 3,
		},
		{
			name:            "Zero workers defaults to 1",
			numWorkers:      func() *int32 { v := int32(0); return &v }(),
			expectedWorkers: 1,
		},
		{
			name:            "Nil workers defaults to 1",
			numWorkers:      nil,
			expectedWorkers: 1,
		},
		{
			name:            "Negative workers defaults to 1",
			numWorkers:      func() *int32 { v := int32(-5); return &v }(),
			expectedWorkers: 1,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Test the math.Max logic used in NewKafkaConsumer
			var actualWorkers int32
			if tc.numWorkers != nil {
				actualWorkers = int32(math.Max(1, float64(*tc.numWorkers)))
			} else {
				actualWorkers = int32(math.Max(1, float64(0)))
			}

			s.Equal(tc.expectedWorkers, actualWorkers)
		})
	}
}

func (s *KafkaConsumerTestSuite) TestStart_Success() {
	// Create consumer with mock workers
	consumer := &KafkaConsumer{
		workerNums:  2,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1, s.mockWorker2},
	}

	// Setup expectations - workers should return non-nil errors to avoid the bug in line 57
	// The original code has a bug where it calls err.Error() without checking if err is nil
	s.mockWorker1.EXPECT().Start().Return(io.EOF)
	s.mockWorker2.EXPECT().Start().Return(io.EOF)

	err := consumer.Start(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStart_WithEOFError() {
	// Test that io.EOF errors are handled gracefully (no log.Fatal)
	consumer := &KafkaConsumer{
		workerNums:  1,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1},
	}

	// Setup expectation - worker returns io.EOF
	s.mockWorker1.EXPECT().Start().Return(io.EOF)

	err := consumer.Start(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStart_WithEOFInErrorMessage() {
	// Test that errors containing io.EOF message are handled gracefully
	consumer := &KafkaConsumer{
		workerNums:  1,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1},
	}

	// Setup expectation - worker returns error containing EOF
	eofError := errors.New("connection closed: " + io.EOF.Error())
	s.mockWorker1.EXPECT().Start().Return(eofError)

	err := consumer.Start(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStart_EmptySubscribers() {
	// Test start with no subscribers
	consumer := &KafkaConsumer{
		workerNums:  0,
		subscribers: []kafka_client.RetryWorker{},
	}

	err := consumer.Start(context.Background())

	s.NoError(err)
}

func (s *KafkaConsumerTestSuite) TestStop_Success() {
	// Test successful stop
	consumer := &KafkaConsumer{
		workerNums:  2,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1, s.mockWorker2},
	}

	// Setup expectations - workers should stop
	s.mockWorker1.EXPECT().Stop()
	s.mockWorker2.EXPECT().Stop()

	err := consumer.Stop(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStop_EmptySubscribers() {
	// Test stop with no subscribers
	consumer := &KafkaConsumer{
		workerNums:  0,
		subscribers: []kafka_client.RetryWorker{},
	}

	err := consumer.Stop(context.Background())

	s.NoError(err)
}

func (s *KafkaConsumerTestSuite) TestStop_NilSubscribers() {
	// Test stop with nil subscribers
	consumer := &KafkaConsumer{
		workerNums:  0,
		subscribers: nil,
	}

	err := consumer.Stop(context.Background())

	s.NoError(err)
}

func (s *KafkaConsumerTestSuite) TestKafkaConsumer_StructFields() {
	// Test that KafkaConsumer struct has expected fields
	consumer := &KafkaConsumer{
		workerNums:  5,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1},
	}

	s.Equal(int32(5), consumer.workerNums)
	s.Len(consumer.subscribers, 1)
	s.Equal(s.mockWorker1, consumer.subscribers[0])
}

func (s *KafkaConsumerTestSuite) TestStart_MultipleWorkersWithMixedResults() {
	// Test start with multiple workers where some succeed and some have EOF
	mockWorker3 := mocks.NewMockRetryWorker(s.ctrl)
	consumer := &KafkaConsumer{
		workerNums:  3,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1, s.mockWorker2, mockWorker3},
	}

	// Setup expectations - all return non-nil errors to avoid the bug
	s.mockWorker1.EXPECT().Start().Return(io.EOF)
	s.mockWorker2.EXPECT().Start().Return(io.EOF)
	mockWorker3.EXPECT().Start().Return(errors.New("contains " + io.EOF.Error() + " in message"))

	err := consumer.Start(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStop_MultipleWorkers() {
	// Test stop with multiple workers
	mockWorker3 := mocks.NewMockRetryWorker(s.ctrl)
	consumer := &KafkaConsumer{
		workerNums:  3,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1, s.mockWorker2, mockWorker3},
	}

	// Setup expectations - all workers should stop
	s.mockWorker1.EXPECT().Stop()
	s.mockWorker2.EXPECT().Stop()
	mockWorker3.EXPECT().Stop()

	err := consumer.Stop(context.Background())

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestKafkaConsumer_ImplementsTransportServer() {
	// Test that KafkaConsumer implements transport.Server interface
	consumer := &KafkaConsumer{}

	// This should compile if KafkaConsumer implements transport.Server
	var _ transport.Server = consumer

	s.NotNil(consumer)
}

func (s *KafkaConsumerTestSuite) TestStart_ContextPropagation() {
	// Test that context is properly handled (though not used in current implementation)
	consumer := &KafkaConsumer{
		workerNums:  1,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1},
	}

	s.mockWorker1.EXPECT().Start().Return(io.EOF)

	ctx := context.WithValue(context.Background(), "test", "value")
	err := consumer.Start(ctx)

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}

func (s *KafkaConsumerTestSuite) TestStop_ContextPropagation() {
	// Test that context is properly handled (though not used in current implementation)
	consumer := &KafkaConsumer{
		workerNums:  1,
		subscribers: []kafka_client.RetryWorker{s.mockWorker1},
	}

	s.mockWorker1.EXPECT().Stop()

	ctx := context.WithValue(context.Background(), "test", "value")
	err := consumer.Stop(ctx)

	s.NoError(err)
	// Give goroutines time to complete
	time.Sleep(10 * time.Millisecond)
}
