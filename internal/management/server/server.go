package server

import (
	"github.com/google/wire"

	v1Inst "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer, NewTemporalWorker, NewWorkerHTTPServer, NewLoanRefundSettleConsumer)

var MaintenanceElements = []maintenance.Element{
	{
		Operation: v1Inst.Installment_ListClientEligiblePlans_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatPurchase},
	},
	{
		Operation: v1Inst.Installment_GetClientPlanDetail_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatPurchase},
	},
	{
		Operation: v1Inst.Installment_AcceptClientPlan_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatPurchase},
	},
	{
		Operation: v1Inst.Installment_GetPaymentPlanDetail_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatPurchase},
	},
}

func getSensitiveCensorship() (sensitiveMethods, sensitiveFields []string) {
	sensitiveMethods = []string{}
	sensitiveFields = []string{}
	return
}
