package server

import (
	"context"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	tempCli "go.temporal.io/sdk/client"
	tempWorker "go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/service"
)

type TemporalWorker struct {
	transport.Server
	logger         *log.Helper
	onceExec       *sync.Once
	temporal       tempCli.Client
	workerList     []*TemporalWorkerData
	statementSvc   *service.StatementService
	installmentSvc *service.InstallmentService
	managementSvc  *service.ManagementService
}

type TemporalWorkerData struct {
	worker  tempWorker.Worker
	wfNames []string
}

func NewTemporalWorker(
	conf *config.Management, kLogger log.Logger,
	statementSvc *service.StatementService,
	installmentSvc *service.InstallmentService,
	managementSvc *service.ManagementService,
	temporal tempCli.Client) *TemporalWorker {
	result := &TemporalWorker{
		temporal:       temporal,
		onceExec:       &sync.Once{},
		statementSvc:   statementSvc,
		installmentSvc: installmentSvc,
		managementSvc:  managementSvc,
	}

	// init logger
	logging := log.With(kLogger, "module", "temporal-worker")
	result.logger = log.NewHelper(logging)

	// init workers
	statementWorkers := result.RegisterStatementWorkflows(
		conf.Schedulers.GetSyncLatestStatement(),
		conf.GetSchedulers().GetSyncStatementsPeriodic(),
		conf.GetSchedulers().GetSyncStatementsPenalty(),
		conf.GetSchedulers().GetSyncStatementsBatchProc(),
	)
	installmentWorkers := result.RegisterInstallmentWorkflows(
		conf.GetSchedulers().GetSyncInstallmentInfo(),
		conf.GetSchedulers().GetCreateInstallmentLoan(),
		conf.GetSchedulers().GetSyncInstallmentsDaily(),
	)
	managementWorkers := result.RegisterLoanManagementWorkflow(
		conf.GetSchedulers().GetSyncLoanRepayment(),
	)
	tempWorkerList := append([]*TemporalWorkerData{}, statementWorkers...)
	tempWorkerList = append(tempWorkerList, installmentWorkers...)
	tempWorkerList = append(tempWorkerList, managementWorkers...)
	result.workerList = tempWorkerList

	return result
}

func (t TemporalWorker) RegisterLoanManagementWorkflow(
	loanMgmtTask *config.TemporalTask) []*TemporalWorkerData {
	queueName := loanMgmtTask.GetQueueName()
	workflowType := loanMgmtTask.GetWorkflowType()
	workerRun := tempWorker.New(t.temporal, queueName, tempWorker.Options{})

	// Register Workflow
	options := workflow.RegisterOptions{Name: workflowType}
	workerRun.RegisterWorkflowWithOptions(t.managementSvc.SyncLoanRepaymentWorkflow, options)

	// Register list activity
	workerRun.RegisterActivity(t.managementSvc.GetStatementByIDActivity)
	workerRun.RegisterActivity(t.managementSvc.GetStatementByIncurDateActivity)
	workerRun.RegisterActivity(t.managementSvc.PollingStatementOutstandingActivity)
	workerRun.RegisterActivity(t.managementSvc.SyncInstallmentsBelongToStatementActivity)

	workerData := &TemporalWorkerData{
		worker:  workerRun,
		wfNames: []string{workflowType},
	}

	return []*TemporalWorkerData{workerData}
}

func (t TemporalWorker) RegisterStatementWorkflows(
	syncLatestStmtTask *config.TemporalTask,
	syncStmtsPeriodicTask *config.TemporalTask,
	syncStmtsPenaltyTask *config.TemporalTask,
	stmtsBatchProcessTask *config.TemporalTask) []*TemporalWorkerData {
	syncQueueName := syncStmtsPeriodicTask.GetQueueName()
	syncWorkerRun := tempWorker.New(t.temporal, syncQueueName, tempWorker.Options{})

	// Register Workflow
	stmtPeriodicWorkflowType := syncStmtsPeriodicTask.GetWorkflowType()
	stmtPeriodOpts := workflow.RegisterOptions{Name: stmtPeriodicWorkflowType}
	syncWorkerRun.RegisterWorkflowWithOptions(t.statementSvc.SyncStatementsPeriodicWorkflow, stmtPeriodOpts)

	stmtPenaltyWorkflowType := syncStmtsPenaltyTask.GetWorkflowType()
	stmtPenaltyOpts := workflow.RegisterOptions{Name: stmtPenaltyWorkflowType}
	syncWorkerRun.RegisterWorkflowWithOptions(t.statementSvc.SyncStatementsPenaltyWorkflow, stmtPenaltyOpts)

	stmtLatestWorkflowType := syncLatestStmtTask.GetWorkflowType()
	stmtLatestOpts := workflow.RegisterOptions{Name: stmtLatestWorkflowType}
	syncWorkerRun.RegisterWorkflowWithOptions(t.statementSvc.SyncLatestStatementWorkflow, stmtLatestOpts)

	// Register list activity
	syncWorkerRun.RegisterActivity(t.statementSvc.InitStatementSyncBatchesActivity)
	syncWorkerRun.RegisterActivity(t.statementSvc.ClassifyAndCheckSyncStatusActivity)
	syncWorkerRun.RegisterActivity(t.statementSvc.InitSyncBatchesFromExistInfoActivity)
	syncWorkerRun.RegisterActivity(t.statementSvc.RetrieveLatestStatementIncurredActivity)
	syncWorkerRun.RegisterActivity(t.statementSvc.SyncStatementsPenaltyActivity)
	syncWorkerRun.RegisterActivity(t.statementSvc.SyncLatestStatementActivity)

	// Separate sync process to another task queue to control concurrency
	procQueueName := stmtsBatchProcessTask.GetQueueName()
	procWorkflowType := stmtsBatchProcessTask.GetWorkflowType()
	procWorkerRun := tempWorker.New(t.temporal, procQueueName, tempWorker.Options{
		MaxConcurrentWorkflowTaskExecutionSize: 5,
	})

	// Register Workflow
	stmtProcOpts := workflow.RegisterOptions{Name: procWorkflowType}
	procWorkerRun.RegisterWorkflowWithOptions(t.statementSvc.StatementSyncBatchProcessingWorkflow, stmtProcOpts)

	// Register list activity
	procWorkerRun.RegisterActivity(t.statementSvc.MarkSyncBatchToRunningActivity)
	procWorkerRun.RegisterActivity(t.statementSvc.ProcessAccountStatementActivity)
	procWorkerRun.RegisterActivity(t.statementSvc.UpdateStatementSyncInfoActivity)

	syncWorkerData := &TemporalWorkerData{
		worker:  syncWorkerRun,
		wfNames: []string{stmtPeriodicWorkflowType, stmtPenaltyWorkflowType},
	}
	procWorkerData := &TemporalWorkerData{
		worker:  procWorkerRun,
		wfNames: []string{procWorkflowType},
	}

	return []*TemporalWorkerData{syncWorkerData, procWorkerData}
}

func (t TemporalWorker) RegisterInstallmentWorkflows(
	syncInstInfoTask *config.TemporalTask,
	createInstLoanTask *config.TemporalTask,
	syncInstsDailyTask *config.TemporalTask) []*TemporalWorkerData {
	queueName := createInstLoanTask.GetQueueName()
	workerRun := tempWorker.New(t.temporal, queueName, tempWorker.Options{})

	// Register Workflow
	createInstWfType := createInstLoanTask.GetWorkflowType()
	createInstWfOpts := workflow.RegisterOptions{Name: createInstWfType}
	workerRun.RegisterWorkflowWithOptions(t.installmentSvc.CreateInstallmentWorkflow, createInstWfOpts)

	syncInstsDailyWfType := syncInstsDailyTask.GetWorkflowType()
	syncInstsDailyWfOpts := workflow.RegisterOptions{Name: syncInstsDailyWfType}
	workerRun.RegisterWorkflowWithOptions(t.installmentSvc.SyncInstallmentsDailyWorkflow, syncInstsDailyWfOpts)

	syncInstInfoWfType := syncInstInfoTask.GetWorkflowType()
	syncInstInfoWfOpts := workflow.RegisterOptions{Name: syncInstInfoWfType}
	workerRun.RegisterWorkflowWithOptions(t.installmentSvc.SyncInstallmentInfoWorkflow, syncInstInfoWfOpts)

	// Register list activity
	workerRun.RegisterActivity(t.installmentSvc.CreateInstallmentActivity)
	workerRun.RegisterActivity(t.installmentSvc.SyncInstallmentInfoActivity)
	workerRun.RegisterActivity(t.installmentSvc.SyncInstallmentsDailyActivity)

	workerData := &TemporalWorkerData{
		worker:  workerRun,
		wfNames: []string{createInstWfType, syncInstsDailyWfType, syncInstInfoWfType},
	}

	return []*TemporalWorkerData{workerData}
}

func (t TemporalWorker) Start(ctx context.Context) error {
	var wg sync.WaitGroup

	handleStart := func(worker *TemporalWorkerData) {
		defer wg.Done()
		wfNames := strings.Join(worker.wfNames, ",")
		runErr := worker.worker.Run(tempWorker.InterruptCh())
		if runErr == nil {
			return
		}
		t.logger.Errorf("failed to start worker for workflow: %s, error: %v", wfNames, runErr)
	}

	for _, worker := range t.workerList {
		wg.Add(1)
		cloned := worker
		go handleStart(cloned)
	}

	wg.Wait()
	return nil
}

func (t TemporalWorker) Stop(ctx context.Context) error {
	handle := func() {
		for _, w := range t.workerList {
			w.worker.Stop()
		}
	}

	t.onceExec.Do(handle)
	return nil
}
