// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/server (interfaces: RetryWorker)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/kafka_mocks.go -package=mocks . RetryWorker
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRetryWorker is a mock of RetryWorker interface.
type MockRetryWorker struct {
	ctrl     *gomock.Controller
	recorder *MockRetryWorkerMockRecorder
}

// MockRetryWorkerMockRecorder is the mock recorder for MockRetryWorker.
type MockRetryWorkerMockRecorder struct {
	mock *MockRetryWorker
}

// NewMockRetryWorker creates a new mock instance.
func NewMockRetryWorker(ctrl *gomock.Controller) *MockRetryWorker {
	mock := &MockRetryWorker{ctrl: ctrl}
	mock.recorder = &MockRetryWorkerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRetryWorker) EXPECT() *MockRetryWorkerMockRecorder {
	return m.recorder
}

// Start mocks base method.
func (m *MockRetryWorker) Start() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start")
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockRetryWorkerMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockRetryWorker)(nil).Start))
}

// Stop mocks base method.
func (m *MockRetryWorker) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockRetryWorkerMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockRetryWorker)(nil).Stop))
}
