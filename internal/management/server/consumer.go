package server

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/service"
)

type LoanRefundSettleConsumer struct {
	transport.Server
	logger   log.Logger
	consumer *KafkaConsumer
}

func NewLoanRefundSettleConsumer(config *config.Management, logger log.Logger, mgmtSvc *service.ManagementService) *LoanRefundSettleConsumer {
	consConf := config.GetAdapters().GetRefundSettleSub()
	consumer := NewKafkaConsumer(consConf, mgmtSvc.HandleLoanRefundSettleEvent)
	return &LoanRefundSettleConsumer{
		logger:   logger,
		consumer: consumer,
	}
}

func (c *LoanRefundSettleConsumer) Start(ctx context.Context) error {
	return c.consumer.Start(ctx)
}

func (c *LoanRefundSettleConsumer) Stop(ctx context.Context) error {
	return c.consumer.Stop(ctx)
}
