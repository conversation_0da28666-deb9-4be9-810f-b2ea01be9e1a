package model

type CTAType int

const (
	CTATypeDeepLink = iota + 1
	CTATypePlanSelection
)

type Action struct {
	Type   CTAType
	Title  string
	ZpiUrl string
	ZpaUrl string
}

type Message struct {
	Text  string `json:"text"`
	Color string `json:"color"`
}

func NewMessage(text string) *Message {
	return &Message{
		Text: text,
	}
}

type CTATitle string

const (
	CTAChangePlan CTATitle = "Đổi gói"
	CTASelectPlan CTATitle = "Chọn gói"
)

func (t CTATitle) String() string {
	return string(t)

}

type MessageText string

const (
	MessageMinEmiAmount           MessageText = "Chỉ từ %s/tháng"
	MessageEmiAmountWithTenor     MessageText = "%d tháng - %s/tháng"
	MessageScheduleShouldBeUpdate MessageText = "Lịch thanh toán cần được cập nhật lại. Bấm lại nguồn tiền “Trả góp” để kiểm tra lại lịch thanh toán."
	MessageInvalidInstallmentTerm MessageText = "Kỳ hạn trước đó không áp dụng được cho giá trị đơn hàng này. Bấm lại nguồn tiền “Trả góp” để chọn lại kỳ hạn thanh toán."
)

func (t MessageText) String() string {
	return string(t)
}
