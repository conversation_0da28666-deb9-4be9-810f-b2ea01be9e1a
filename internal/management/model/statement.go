package model

import (
	"math"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type Statement struct {
	ID                int64
	Period            int32
	AccountID         int64
	ZalopayID         int64
	PartnerCode       partner.PartnerCode
	DueDate           time.Time
	IncurredDate      time.Time
	PenaltyAmount     int64
	OutstandingAmount int64
	OutstandingRepaid int64
	Metadata          StatementMetadata
	CreatedAt         time.Time
	UpdatedAt         time.Time

	// NOTE: Currently, these fields about fee are not used, but maybe we will use them in the future
	InstallmentFeeAmount int64
	InstallmentFeeRepaid int64
}

type StatementMetadata struct {
	PenaltySource StatementPenaltySource `json:"penalty_source"`
}

func (s *Statement) AttachID(id int64) *Statement {
	s.ID = id
	return s
}

func (s *Statement) AttachPeriod(period int32) *Statement {
	s.Period = period
	return s
}

func (s *Statement) GetPaidStatus() StatementPaidStatus {
	if s.ID <= 0 {
		return StatementPaidStatusUnknown
	}
	if s.CalcTotalDueRemaining() == 0 {
		return StatementPaidStatusPaid
	}
	if s.OutstandingRepaid == 0 {
		return StatementPaidStatusUnpaid
	}
	return StatementPaidStatusPartiallyPaid
}

func (s *Statement) CalcTotalDueAmount() int64 {
	return s.OutstandingAmount + s.InstallmentFeeAmount + s.PenaltyAmount
}

func (s *Statement) CalcTotalDueRepaid() int64 {
	return s.OutstandingRepaid + s.InstallmentFeeRepaid
}

func (s *Statement) CalcTotalDueRemaining() int64 {
	balance := s.CalcTotalDueAmount() - s.CalcTotalDueRepaid()
	return int64(math.Max(float64(balance), 0))
}

func (s *Statement) EvalPenaltyFromCIMBOutstanding(balance int64) *Statement {
	penaltyAmount := balance + s.OutstandingRepaid - s.OutstandingAmount
	s.PenaltyAmount = int64(math.Max(float64(penaltyAmount), 0))
	s.Metadata.PenaltySource = StatementPenaltySourceCalculated
	return s
}

func (s *Statement) ToOutstandingDue() OutstandingDue {
	return OutstandingDue{
		DueAmount:  s.CalcTotalDueAmount(),
		DueRepaid:  s.CalcTotalDueRepaid(),
		DuePenalty: s.PenaltyAmount,
		CreatedAt:  s.IncurredDate,
		UpdatedAt:  s.UpdatedAt,
	}
}

type StatementInstallment struct {
	ID                int64
	DueDate           time.Time
	StatementID       int64
	StatementDate     time.Time
	ZalopayID         int64
	AccountID         int64
	RefZPTransID      int64
	RefOriInstID      int64
	PartnerCode       partner.PartnerCode
	PartnerInstID     string
	TransactionRemark string
	InstallmentAmount int64
	OutstandingData   *StatementInstallmentOuts
}

type StatementInstallmentOuts struct {
	TotalOutstanding        int64
	OutstandingPrincipal    int64
	OutstandingInterest     int64
	OutstandingPenalty      int64
	OutstandingDuePrincipal int64
	OutstandingDueInterest  int64
	OutstandingDuePenalty   int64
}

func (s *StatementInstallment) GetOutstandingTotal() int64 {
	if s.OutstandingData == nil {
		return 0
	}
	return s.OutstandingData.TotalOutstanding
}

func (s *StatementInstallment) GetOutstandingDue() int64 {
	if s.OutstandingData == nil {
		return 0
	}
	return s.OutstandingData.OutstandingDuePrincipal + s.OutstandingData.OutstandingDueInterest
}

func (s *StatementInstallment) GetOutstandingOverdue() int64 {
	if s.OutstandingData == nil {
		return 0
	}
	return s.OutstandingData.OutstandingPrincipal + s.OutstandingData.OutstandingInterest - s.GetOutstandingDue()
}

func (s *StatementInstallment) GetOutstandingPenalty() int64 {
	if s.OutstandingData == nil {
		return 0
	}
	return s.OutstandingData.OutstandingPenalty
}

type StatementInstallments []*StatementInstallment

// StatementResult is a struct that aggregates a statement and its installments
type StatementResult struct {
	StatementDetail *Statement
	StatementInsts  StatementInstallments
}

type StatementOutstanding struct {
	OutstandingBalance int64
}

func (s StatementInstallments) FulfillInstallments(id int64) {
	for _, inst := range s {
		inst.StatementID = id
	}
}

func (s StatementInstallments) GetListPartnerInstID() []string {
	var listPartnerInstID []string
	for _, inst := range s {
		listPartnerInstID = append(listPartnerInstID, inst.PartnerInstID)
	}
	return listPartnerInstID
}

type StatementPeriod struct {
	Year         int
	Month        int
	Period       int32
	IncurredDate time.Time
}

func (s *StatementPeriod) SimulateNextPeriod() *StatementPeriod {
	nextMonth := s.Month + 1
	nextYear := s.Year
	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}
	nextPeriod := int32(nextYear*100 + nextMonth)
	nextIncurredDate := time.Date(nextYear, time.Month(nextMonth), s.IncurredDate.Day(), 0, 0, 0, 0, time.Local)

	return &StatementPeriod{
		Year:         nextYear,
		Month:        nextMonth,
		Period:       nextPeriod,
		IncurredDate: nextIncurredDate,
	}
}

type StatementPaidStatus int

const (
	StatementPaidStatusUnknown StatementPaidStatus = iota
	StatementPaidStatusPaid
	StatementPaidStatusUnpaid
	StatementPaidStatusPartiallyPaid
)

type StatementPenaltySource string

const (
	StatementPenaltySourceUnknown    StatementPenaltySource = "unknown"
	StatementPenaltySourceCalculated StatementPenaltySource = "calculated"
)

func (s StatementPenaltySource) String() string {
	return string(s)
}
