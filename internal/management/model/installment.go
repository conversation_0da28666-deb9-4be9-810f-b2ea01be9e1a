package model

import (
	"math"
	"slices"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

// Installment is aggregate root of installment info and details
type Installment struct {
	Info   *InstallmentInfo
	Repays InstallmentRepays
}

type Installments []*Installment

func (i Installments) CalcTotalOutsAmount() int64 {
	outstandingAmount := int64(0)
	for _, inst := range i {
		outstandingAmount += inst.Repays.CalcTotalOutsAmount()
	}
	return outstandingAmount
}

type InstallmentInfo struct {
	ID              int64
	Tenure          int32
	Status          InstallmentStatus
	DischargeStatus DischargeStatus
	ZalopayID       int64
	AccountID       int64
	ZPTransID       int64
	PaidTenure      int32
	StartDate       time.Time
	EndDate         time.Time
	CurrDueDate     time.Time
	NextDueDate     time.Time
	DaysPastDue     int32
	PartnerCode     partner.PartnerCode
	PartnerInstID   string
	DisburseAmount  int64
	RefundInfo      *InstallmentRefund
	SimulationInfo  InstallmentSimulation
	TransactionInfo InstallmentTransaction
	OutstandingInfo *InstallmentOutstanding
	EarlyDischarge  *InstallmentEarlyDischarge
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func NewInstallmentInfo(tenure int32, disburseAmount int64) *InstallmentInfo {
	return &InstallmentInfo{
		Tenure:         tenure,
		DisburseAmount: disburseAmount,
	}
}

func (i *InstallmentInfo) SyncWithCIMBInstallment(inst *PartnerCIMBInst) *InstallmentInfo {
	// In-case new status not valid by transition check, we keep current status for prevent issue
	newStatus := inst.ToInstallmentStatus()
	statusValid := i.CheckStatusTransition(i.Status, newStatus)
	if !statusValid {
		newStatus = i.Status
	}

	i.Status = newStatus
	i.PartnerInstID = inst.ID
	i.DisburseAmount = inst.DisburseAmount
	i.PaidTenure = inst.CalcPaidTenure()
	i.DaysPastDue = inst.DaysPastDue
	i.StartDate = inst.StartDate
	i.EndDate = inst.EndDate
	i.CurrDueDate = inst.CurrRepayDueDate
	i.NextDueDate = inst.NextRepayDueDate
	i.EarlyDischarge = i.EvalEarlyDischarge()
	i.DischargeStatus = i.EvalDischargeStatus()
	return i
}

func (i *InstallmentInfo) SetPartner(code partner.PartnerCode) *InstallmentInfo {
	i.PartnerCode = code
	return i
}

func (i *InstallmentInfo) SetPartnerInstID(partnerInstID string) *InstallmentInfo {
	i.PartnerInstID = partnerInstID
	return i
}

func (i *InstallmentInfo) SetPaidTenure(paidTenure int32) *InstallmentInfo {
	i.PaidTenure = paidTenure
	return i
}

func (i *InstallmentInfo) SetSimulationInfo(data *InstallmentSimulation) *InstallmentInfo {
	i.SimulationInfo = *data
	return i
}

func (i *InstallmentInfo) SetOutstandingInfo(data *InstallmentOutstanding) *InstallmentInfo {
	i.OutstandingInfo = data
	return i
}

func (i *InstallmentInfo) SetRepaymentDueDate(currDueDate, nextDueDate time.Time) *InstallmentInfo {
	i.CurrDueDate = currDueDate
	i.NextDueDate = nextDueDate
	return i
}

func (i *InstallmentInfo) AttachUserIdentity(zalopayID, accountID int64) *InstallmentInfo {
	i.ZalopayID = zalopayID
	i.AccountID = accountID
	return i
}

func (i *InstallmentInfo) AttachTransactionInfo(transID, zpTransID int64, transDesc string) *InstallmentInfo {
	i.ZPTransID = zpTransID
	i.TransactionInfo.TransID = transID
	i.TransactionInfo.TransDesc = transDesc
	return i
}

func (i *InstallmentInfo) MarkFirstTenureSettled() *InstallmentInfo {
	i.TransactionInfo.FirstTenureSettled = true
	return i
}

func (i *InstallmentInfo) CheckStatusTransition(curStatus, newStatus InstallmentStatus) bool {
	if i.Status == "" || i.Status == InstallmentStatusUnknown {
		return true
	}
	statusTransitionMap := map[InstallmentStatus][]InstallmentStatus{
		InstallmentStatusInit:   {InstallmentStatusInit, InstallmentStatusOpen, InstallmentStatusClosed},
		InstallmentStatusOpen:   {InstallmentStatusOpen, InstallmentStatusClosed},
		InstallmentStatusClosed: {},
	}
	allowedStatus := statusTransitionMap[curStatus]
	return slices.Contains(allowedStatus, newStatus)
}

// EvalDischargeStatus evaluate discharge status based on current status
// ensure status of this loan is final before evaluate discharge status
func (i *InstallmentInfo) EvalDischargeStatus() DischargeStatus {
	switch i.Status {
	case InstallmentStatusClosed:
		return DischargeStatusComplete
	default:
		return i.DischargeStatus
	}
}

// EvalEarlyDischarge evaluate early discharge info before sync with new data
func (i *InstallmentInfo) EvalEarlyDischarge() *InstallmentEarlyDischarge {
	if !i.IsClosed() {
		return i.EarlyDischarge
	}
	return nil
}

func (i *InstallmentInfo) GetInstID() int64 {
	if i == nil {
		return 0
	}
	return i.ID
}

func (i *InstallmentInfo) GetTenure() int32 {
	if i == nil {
		return 0
	}
	return i.Tenure
}

func (i *InstallmentInfo) GetPartnerInstID() string {
	if i == nil {
		return ""
	}
	return i.PartnerInstID
}

func (i *InstallmentInfo) GetPrincipalAmount() int64 {
	if i == nil {
		return 0
	}
	return i.DisburseAmount
}

func (i *InstallmentInfo) GetInterestAmount() int64 {
	if i == nil {
		return 0
	}
	return i.SimulationInfo.InterestAmount
}

func (i *InstallmentInfo) PostProcessQuery() *InstallmentInfo {
	return i
}

func (i *InstallmentInfo) ensureEarlyDischarge() {
	if i.EarlyDischarge != nil {
		return
	}
	i.EarlyDischarge = &InstallmentEarlyDischarge{
		SyncedAt: time.Now(),
	}
}

func (i *InstallmentInfo) GetEarlyDischarge() *InstallmentEarlyDischarge {
	if i.EarlyDischarge == nil {
		return nil
	}
	return i.EarlyDischarge
}

func (i *InstallmentInfo) GetEarlyDischargeKind() EarlyDischargeKind {
	i.ensureEarlyDischarge()
	switch {
	case i.RefundInfo != nil:
		return EarlyDischargeKindRefund
	case !i.EarlyDischarge.IsEmpty():
		return EarlyDischargeKindNormal
	default:
		return EarlyDischargeKindUnknown
	}
}

func (i *InstallmentInfo) IsEarlyDischargeAllowed() bool {
	if i.IsClosed() || !i.IsDischargePending() {
		return false
	}
	switch i.GetEarlyDischargeKind() {
	case EarlyDischargeKindRefund:
		return i.RefundInfo.RequireUserTopup() && i.GetAdjustEarlyDischargeAmount() > 0
	default:
		return false
	}
}

func (i *InstallmentInfo) GetTotalEarlyDischargeAmount() int64 {
	if i.EarlyDischarge == nil {
		return 0
	}
	return i.EarlyDischarge.GetTotalDischargeAmount()
}

func (i *InstallmentInfo) GetAdjustEarlyDischargeAmount() int64 {
	if i.RefundInfo == nil {
		return i.GetTotalEarlyDischargeAmount()
	}
	refundedAmount := i.RefundInfo.NetRefundAmount
	userTopupAmount := i.RefundInfo.UserTopupAmount
	dischargeAmount := i.GetTotalEarlyDischargeAmount()
	adjustedAmount := dischargeAmount - refundedAmount - userTopupAmount
	return int64(math.Max(float64(adjustedAmount), 0))
}

func (i *InstallmentInfo) GetEarlyDischargeFee() int64 {
	if i.EarlyDischarge == nil {
		return 0
	}
	return i.EarlyDischarge.GetEarlyDischargeFee()
}

func (i *InstallmentInfo) GetEarlyDischargeOuts() int64 {
	return i.GetAdjustEarlyDischargeAmount() - i.GetEarlyDischargeFee()
}

func (i *InstallmentInfo) HasEarlyDischargeChanged(data *InstallmentEarlyDischarge) bool {
	if i.EarlyDischarge == nil || data == nil {
		return true
	}
	return i.EarlyDischarge.GetTotalDischargeAmount() != data.GetTotalDischargeAmount()
}

func (i *InstallmentInfo) SetEarlyDischarge(data *InstallmentEarlyDischarge) *InstallmentInfo {
	i.ensureEarlyDischarge()
	i.EarlyDischarge.Details = data.GetDetails()
	i.EarlyDischarge.SyncedAt = data.GetSyncedAt()
	return i
}

func (i *InstallmentInfo) MarkDischargeComplete() *InstallmentInfo {
	i.DischargeStatus = DischargeStatusComplete
	return i
}

// SetEarlyDischargeIfExist set early discharge data if it's not nil to ensure current data not overwritten
func (i *InstallmentInfo) SetEarlyDischargeIfExist(data *InstallmentEarlyDischarge) *InstallmentInfo {
	if i.IsClosed() || data == nil {
		return i
	}
	return i.SetEarlyDischarge(data)
}

func (i *InstallmentInfo) IsOpened() bool {
	return i.Status == InstallmentStatusOpen
}

func (i *InstallmentInfo) IsClosed() bool {
	return i.Status == InstallmentStatusClosed
}

func (i *InstallmentInfo) IsDischargeDone() bool {
	return i.DischargeStatus == DischargeStatusComplete
}

func (i *InstallmentInfo) IsDischargePending() bool {
	return i.DischargeStatus == DischargeStatusPending
}

func (i *InstallmentInfo) MismatchedEarlyDischarge() bool {
	if i.IsClosed() && i.IsDischargeDone() {
		return false
	}
	return i.EarlyDischarge == nil ||
		i.EarlyDischarge.IsEmpty() ||
		i.EarlyDischarge.IsOutdated() ||
		(i.IsClosed() && !i.IsDischargeDone()) ||
		(!i.IsClosed() && i.IsDischargeDone())
}

type InstallmentSimulation struct {
	EmiAmount       int64
	InterestRate    float64
	InterestAmount  int64
	PrincipalAmount int64
	TotalFeeAmount  int64
	TotalAmountDue  int64
	FeeDetails      []FeeDetail
}

// InstallmentOutstanding is the outstanding amount of an installment
type InstallmentOutstanding struct {
	OutstandingOriginal     int64 `json:"outstanding_original"`
	OutstandingDueTotal     int64 `json:"outstanding_due_total"`
	OutstandingDuePenalty   int64 `json:"outstanding_due_penalty"`
	OutstandingDueInterest  int64 `json:"outstanding_due_interest"`
	OutstandingDuePrincipal int64 `json:"outstanding_due_principal"`
}

type InstallmentEarlyDischarge struct {
	Details  *EarlyDischargeDetails `json:"details"`
	SyncedAt time.Time              `json:"synced_at"`
}

func (i *InstallmentEarlyDischarge) IsEmpty() bool {
	return i == nil || i.Details == nil
}

func (i *InstallmentEarlyDischarge) IsOutdated() bool {
	if i == nil || i.SyncedAt.IsZero() {
		return true
	}
	timeNow := time.Now()
	year, month, day := timeNow.Date()
	timeStartAllow := time.Date(year, month, day, 9, 30, 0, 0, timeNow.Location())
	timeEndAllow := time.Date(year, month, day, 21, 0, 0, 0, timeNow.Location())
	return i.SyncedAt.Before(timeStartAllow) || i.SyncedAt.After(timeEndAllow)
}

func (i *InstallmentEarlyDischarge) GetDetails() *EarlyDischargeDetails {
	if i == nil {
		return nil
	}
	return i.Details
}

func (i *InstallmentEarlyDischarge) GetSyncedAt() time.Time {
	if i == nil {
		return time.Time{}
	}
	return i.SyncedAt
}

func (i *InstallmentEarlyDischarge) GetEarlyDischargeFee() int64 {
	if i == nil {
		return 0
	}
	return i.GetDetails().GetEarlyDischargeFee()
}

func (i *InstallmentEarlyDischarge) GetTotalDischargeAmount() int64 {
	if i == nil {
		return 0
	}
	return i.GetDetails().GetTotalDischargeAmount()
}

type EarlyDischargeDetails struct {
	TotalDischargeAmount int64  `json:"total_discharge_amount"`
	EarlyDischargeFee    int64  `json:"early_discharge_fee"`
	OutstandingPrincipal *int64 `json:"outstanding_principal"`
	OutstandingInterest  *int64 `json:"outstanding_interest"`
	OutstandingPenalty   *int64 `json:"outstanding_penalty"`
}

func (i *EarlyDischargeDetails) GetTotalDischargeAmount() int64 {
	if i == nil {
		return 0
	}
	return i.TotalDischargeAmount
}

func (i *EarlyDischargeDetails) GetEarlyDischargeFee() int64 {
	if i == nil {
		return 0
	}
	return i.EarlyDischargeFee
}

type InstallmentRepay struct {
	ID                 int64
	SeqNo              int32
	Status             RepayScheduleStatus
	InstallmentID      int64  // ref to InstallmentInfo.ID
	PartnerInstID      string // ref to InstallmentInfo.PartnerInstID
	RepayDueDate       time.Time
	GraceDueDate       time.Time
	EmiAmount          int64
	InterestAmount     int64
	PrincipalAmount    int64
	PenaltyDetails     *InstallmentRepayPenalty
	OutstandingDetails *InstallmentRepayOutstanding
}

func (r *InstallmentRepay) IsDue() bool {
	return r != nil && r.Status == RepayScheduleStatusDue
}

func (r *InstallmentRepay) IsInit() bool {
	return r != nil && r.Status == RepayScheduleStatusDue
}

func (r *InstallmentRepay) IsDueOrPaid() bool {
	return r != nil && r.Status == RepayScheduleStatusDue || r.Status == RepayScheduleStatusPaid
}

func (r *InstallmentRepay) GetTotalAmountDue() int64 {
	if r == nil {
		return 0
	}
	return r.EmiAmount + r.GetTotalPenalty()
}

func (r *InstallmentRepay) GetTotalDueAmount() int64 {
	if r == nil {
		return 0
	}
	if r.IsDueOrPaid() {
		return r.GetTotalAmountDue()
	}
	return 0
}

func (r *InstallmentRepay) GetTotalDueOuts() int64 {
	if r == nil {
		return 0
	}
	if r.IsDueOrPaid() {
		return r.OutstandingDetails.OutstandingAmount
	}
	return 0
}

func (r *InstallmentRepay) GetTotalOutsAmount() int64 {
	if r == nil {
		return 0
	}
	if r.IsDueOrPaid() {
		return r.OutstandingDetails.OutstandingAmount
	}
	return r.EmiAmount + r.GetOutsPenalty()
}

func (r *InstallmentRepay) GetTotalPaidAmount() int64 {
	if r == nil {
		return 0
	}
	return r.GetTotalAmountDue() - r.GetTotalOutsAmount()
}

func (r *InstallmentRepay) GetEmiAmount() int64 {
	if r == nil {
		return 0
	}
	return r.EmiAmount
}

func (r *InstallmentRepay) GetPrincipalAmount() int64 {
	if r == nil {
		return 0
	}
	return r.PrincipalAmount
}

func (r *InstallmentRepay) GetInterestAmount() int64 {
	if r == nil {
		return 0
	}
	return r.InterestAmount
}

func (r *InstallmentRepay) GetTotalPenalty() int64 {
	if r == nil || r.PenaltyDetails == nil {
		return 0
	}
	return r.PenaltyDetails.TotalPenalty
}

func (r *InstallmentRepay) GetOutsPenalty() int64 {
	if r == nil || r.PenaltyDetails == nil {
		return 0
	}
	return r.PenaltyDetails.OutstandingPenalty
}

type InstallmentRepays []*InstallmentRepay

func (r InstallmentRepays) CalcTotalAmountDue() int64 {
	interestAmount := int64(0)
	principalAmount := int64(0)
	penaltyAmount := int64(0)
	for _, repay := range r {
		principalAmount += repay.PrincipalAmount
		interestAmount += repay.InterestAmount
		penaltyAmount += repay.GetTotalPenalty()
	}
	return principalAmount + interestAmount + penaltyAmount
}

func (r InstallmentRepays) CalcTotalDueAmount() int64 {
	dueAmount := int64(0)
	for _, repay := range r {
		dueAmount += repay.GetTotalDueAmount()
	}
	return dueAmount
}

func (r InstallmentRepays) CalcTotalDueOuts() int64 {
	dueOutstanding := int64(0)
	for _, repay := range r {
		dueOutstanding += repay.GetTotalDueOuts()
	}
	return dueOutstanding
}

func (r InstallmentRepays) CalcTotalDueRepaid() int64 {
	dueRepaid := r.CalcTotalDueAmount() - r.CalcTotalDueOuts()
	if dueRepaid < 0 {
		return 0
	}
	return dueRepaid
}

func (r InstallmentRepays) CalcTotalOutsAmount() int64 {
	outstandingAmount := int64(0)
	for _, repay := range r {
		outstandingAmount += repay.GetTotalOutsAmount()
	}
	return outstandingAmount
}

func (r InstallmentRepays) CalcTotalPaidAmount() int64 {
	result := r.CalcTotalAmountDue() - r.CalcTotalOutsAmount()
	if result < 0 {
		return 0
	}
	return result
}

func (r InstallmentRepays) CalcPenaltyAmount() int64 {
	penaltyAmount := int64(0)
	for _, repay := range r {
		penaltyAmount += repay.GetTotalPenalty()
	}
	return penaltyAmount
}

type InstallmentRepayPenalty struct {
	TotalPenalty       int64 `json:"total_penalty"`
	PaidPenalty        int64 `json:"paid_penalty"`
	OutstandingPenalty int64 `json:"outstanding_penalty"`
}

type InstallmentRepayOutstanding struct {
	OutstandingAmount    int64 `json:"outstanding_amount"`
	OutstandingInterest  int64 `json:"outstanding_interest"`
	OutstandingPenalty   int64 `json:"outstanding_penalty"`
	OutstandingPrincipal int64 `json:"outstanding_principal"`
}

type InstallmentRefund struct {
	UpdateVersion     int   `json:"update_version"`
	NetRefundAmount   int64 `json:"net_refund_amount"`
	UserTopupAmount   int64 `json:"user_topup_amount"`
	TotalRefundAmount int64 `json:"total_refund_amount"`
	UserTopupRequired bool  `json:"user_topup_required"`
}

func (ir *InstallmentRefund) RequireUserTopup() bool {
	if ir == nil {
		return false
	}
	return ir.UserTopupRequired
}

type InstallmentTransaction struct {
	TransID            int64  `json:"trans_id"`
	TransDesc          string `json:"trans_desc"`
	FirstTenureSettled bool   `json:"first_tenure_settled"`
}

func (is InstallmentTransaction) GetTransDesc() string {
	return is.TransDesc
}

type InstallmentStatus string

const (
	InstallmentStatusUnknown InstallmentStatus = "unknown"
	InstallmentStatusInit    InstallmentStatus = "init"
	InstallmentStatusOpen    InstallmentStatus = "open"
	InstallmentStatusClosed  InstallmentStatus = "closed"
)

type RepayScheduleStatus string

const (
	RepayScheduleStatusUnknown RepayScheduleStatus = "unknown"
	RepayScheduleStatusDue     RepayScheduleStatus = "due"
	RepayScheduleStatusPaid    RepayScheduleStatus = "paid"
	RepayScheduleStatusInit    RepayScheduleStatus = "init"
)

type EarlyDischargeKind string

const (
	EarlyDischargeKindUnknown EarlyDischargeKind = "unknown"
	EarlyDischargeKindRefund  EarlyDischargeKind = "refund"
	EarlyDischargeKindNormal  EarlyDischargeKind = "normal"
)

type DischargeStatus string

const (
	DischargeStatusUnknown    DischargeStatus = "unknown"
	DischargeStatusPending    DischargeStatus = "pending"
	DischargeStatusProcessing DischargeStatus = "processing"
	DischargeStatusComplete   DischargeStatus = "complete"
)

func (d DischargeStatus) String() string {
	return string(d)
}

func (d DischargeStatus) IsUnknown() bool {
	return d == DischargeStatusUnknown
}
