package model

import (
	"time"
)

type PartnerAccount struct {
	AccountInfo    *PartnerAccountInfo
	AccountBalance *PartnerAccountBalance
}

type PartnerAccountInfo struct {
	AccountName   string
	AccountID     string
	AccountType   string
	AccountNumber string

	/**
	 * Expiry date of overdraft limit, depend on partner
	 * With CIMB: it will be set from OverdraftExpiryDate in PartnerAccountBalance
	 * Ensure check this value in this struct before using
	 */
	ExpiryDate time.Time
}

type PartnerAccountBalance struct {
	TotalLimit          int64
	OverdraftLimit      int64
	AvailableBalance    int64
	OverdraftExpiryDate time.Time
}

type PartnerRepaySchedule struct {
	InstNo          int32
	EmiAmount       int64
	InterestAmount  int64
	PrincipleAmount int64
	StartDate       time.Time
	EndDate         time.Time
}

type PartnerPlanOption struct {
	// EmiAmount can be a fixed emi amount or average emi amount
	EmiAmount         int64
	TenorNumber       int64
	RbiInterestRate   float64
	FlatInterestRate  float64
	TotalAmount       int64
	InterestAmount    int64
	PrincipalAmount   int64
	InstallmentAmount int64
	RepaymentSchedule []PartnerRepaySchedule
}

// BuildConversionFee builds conversion fee detail from difference partner context
// + CIMB: Only support interest amount and rbi interest rate, so we can build a conversion fee from this info
// + Another partner: ...
func (p PartnerPlanOption) BuildConversionFee() *FeeDetail {
	return &FeeDetail{
		Type:   ConversionFee,
		Rate:   p.FlatInterestRate,
		Amount: p.InterestAmount,
	}
}

type PartnerCIMBInst struct {
	ID                      string
	Tenure                  int32
	Status                  PartnerCIMBInstStatus
	DueDate                 time.Time
	StartDate               time.Time
	EndDate                 time.Time
	ReportDate              time.Time
	DaysPastDue             int32
	InterestRate            float64
	DisburseAmount          int64
	CurrRepayDueDate        time.Time
	NextRepayDueDate        time.Time
	OutstandingOriginal     int64
	OutstandingDueTotal     int64
	OutstandingDuePrincipal int64
	OutstandingDueInterest  int64
	OutstandingDuePenalty   int64
	EarlyDischargeInfo      *PartnerEarlyDischarge
	RepaymentSchedules      []*PartnerCIMBInstRepaySchedule
}

type PartnerEarlyDischarge struct {
	ID            string
	HasDischarged bool
	DischargeData *EarlyDischargeData
}

type EarlyDischargeData struct {
	OutstandingPrincipal *int64
	OutstandingInterest  *int64
	OutstandingPenalty   *int64
	EarlyDischargeFee    int64
	TotalDischargeAmount int64
}

type PartnerCIMBInstRepaySchedule struct {
	IsDue                bool
	SeqNo                int32
	StartDate            time.Time
	EndDate              time.Time
	RepayDueDate         time.Time
	GraceDueDate         time.Time
	PaidPenalty          int64
	EmiAmount            int64
	InterestAmount       int64
	PrincipalAmount      int64
	OutstandingAmount    int64
	OutstandingPenalty   int64
	OutstandingInterest  int64
	OutstandingPrincipal int64
}

type PartnerCIMBInstStatus string

const (
	PartnerCIMBInstStatusNormal PartnerCIMBInstStatus = "NORMAL"
	PartnerCIMBInstStatusActive PartnerCIMBInstStatus = "ACTIVE"
	PartnerCIMBInstStatusClosed PartnerCIMBInstStatus = "CLOSE"
)

func (i *PartnerCIMBInst) GetCurrentPeriod() int32 {
	for _, schedule := range i.RepaymentSchedules {
		periodDueDate := schedule.RepayDueDate
		currDueDateStr := i.CurrRepayDueDate.Format("2006-01-02")
		periodDueDateStr := schedule.RepayDueDate.Format("2006-01-02")
		if !periodDueDate.IsZero() && currDueDateStr == periodDueDateStr {
			return schedule.SeqNo
		}
	}
	return 0
}

func (i *PartnerCIMBInst) CalcPaidTenure() int32 {
	result := int32(0)
	for _, schedule := range i.RepaymentSchedules {
		if schedule.IsDue && schedule.OutstandingAmount == 0 {
			result++
		}
	}
	return result
}

func (i *PartnerCIMBInst) CalcTotalAmountDue() int64 {
	result := int64(0)
	for _, schedule := range i.RepaymentSchedules {
		result += schedule.EmiAmount
	}
	return result
}

func (i *PartnerCIMBInst) CalcInterestAmount() int64 {
	result := int64(0)
	for _, schedule := range i.RepaymentSchedules {
		result += schedule.InterestAmount
	}
	return result
}

func (i *PartnerCIMBInst) CalcPrincipalAmount() int64 {
	result := int64(0)
	for _, schedule := range i.RepaymentSchedules {
		result += schedule.PrincipalAmount
	}
	return result
}

func (i *PartnerCIMBInst) ToInstallmentStatus() InstallmentStatus {
	switch i.Status {
	case PartnerCIMBInstStatusNormal:
		return InstallmentStatusOpen
	case PartnerCIMBInstStatusActive:
		return InstallmentStatusOpen
	case PartnerCIMBInstStatusClosed:
		return InstallmentStatusClosed
	default:
		return InstallmentStatusUnknown
	}
}

func (i *PartnerCIMBInstRepaySchedule) CalcTotalPenaltyAmount() int64 {
	return i.PaidPenalty + i.OutstandingPenalty
}

func (i *PartnerCIMBInstRepaySchedule) IsPaid() bool {
	return i.OutstandingAmount == 0 && i.OutstandingPenalty == 0
}

func (i *PartnerCIMBInstRepaySchedule) ToRepaymentStatus() RepayScheduleStatus {
	switch {
	case i.IsPaid():
		return RepayScheduleStatusPaid
	case i.IsDue:
		return RepayScheduleStatusDue
	default:
		return RepayScheduleStatusInit
	}
}
