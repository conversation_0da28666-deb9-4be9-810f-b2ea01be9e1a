package model

import (
	"encoding/json"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type StatementSyncBatch struct {
	BatchNo     int
	QueryOffset int64
	TotalItems  int
	RefExecID   string // Reference to StatementSyncInfo.ExecID, optional
	RefSyncID   int64  // Reference to StatementSyncInfo.ID, optional
	Accounts    []*Account
	StmtPeriod  *StatementPeriod
	PartnerCode partner.PartnerCode
}

type StatementSyncInfo struct {
	ID            int64
	ExecID        string
	Period        int32
	BatchNo       int
	Status        StatementSyncStatus
	SyncStats     StatementSyncStats
	PartnerCode   partner.PartnerCode
	StatementDate time.Time
}

type StatementSyncSummary struct {
	HasFailed  bool
	AllSynced  bool
	AllSuccess bool

	Success []*StatementSyncInfo
	Failed  []*StatementSyncInfo
	Pending []*StatementSyncInfo
	Running []*StatementSyncInfo
}

func (s *StatementSyncSummary) EvaluateState() *StatementSyncSummary {
	totalSync := len(s.Success) +
		len(s.Failed) +
		len(s.Pending) +
		len(s.Running)

	if totalSync == 0 {
		s.AllSynced = false
		s.AllSuccess = false
		s.HasFailed = false
		return s
	}

	s.AllSynced = true

	if len(s.Failed) > 0 {
		s.HasFailed = true
	}
	if len(s.Running) > 0 || len(s.Pending) > 0 {
		s.AllSynced = false
	}
	if len(s.Success) == totalSync {
		s.AllSuccess = true
	}
	return s
}

func (s *StatementSyncSummary) StillHaveIncompleteSyncs() bool {
	return s.HasFailed
}

func (s *StatementSyncSummary) CollectIncompleteSyncs() []*StatementSyncInfo {
	return s.Failed
}

type StatementSyncStats struct {
	TotalItems  int    `json:"total_items"`
	TotalSynced int    `json:"total_synced"`
	TotalFailed int    `json:"total_failed"`
	QueryOffset string `json:"query_offset"`
	FirstFailed string `json:"first_failed"`
}

func (s *StatementSyncStats) Marshal() ([]byte, error) {
	b, err := json.Marshal(s)
	return b, err
}

func (s *StatementSyncStats) Unmarshal(data []byte) error {
	return json.Unmarshal(data, s)
}

type StatementSyncStatus string

const (
	StatementSyncStatusPending StatementSyncStatus = "PENDING"
	StatementSyncStatusRunning StatementSyncStatus = "RUNNING"
	StatementSyncStatusSuccess StatementSyncStatus = "SUCCESS"
	StatementSyncStatusFailed  StatementSyncStatus = "FAILED"
)

func (s StatementSyncStatus) String() string {
	return string(s)
}

type StatementPenaltySync struct {
	Error  error
	Data   *Statement
	Params *Statement
}
