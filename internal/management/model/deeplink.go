package model

import (
	"fmt"
	"net/url"
	"reflect"
)

type DeepLinkKey string

const (
	LinkKeyDefault      DeepLinkKey = "default"
	LinkKeyApplication  DeepLinkKey = "application"
	LinkKeyPlanDetail   DeepLinkKey = "plan_detail"
	LinkKeyPlanDetailMF DeepLinkKey = "plan_detail_mf"
)

type DeepLinkData struct {
	ZPAUrl    string `json:"zpa_url"`
	ZPIUrl    string `json:"zpi_url"`
	CommonUrl string `json:"common_url"`
}

func (d *DeepLinkData) BuildModuleFederation(link string, props map[string]any) ModuleFederation {
	const (
		mfScopeField  = "mf_scope"
		mfModuleField = "mf_module"
	)

	urlParsed, _ := url.Parse(link)
	urlQuery := urlParsed.Query()
	urlFull := fmt.Sprintf("%s://%s%s", urlParsed.Scheme, urlParsed.Host, urlParsed.Path)
	return ModuleFederation{
		Entry:  urlFull,
		Scope:  urlQuery.Get(mfScopeField),
		Module: urlQuery.Get(mfModuleField),
		Props:  props,
	}
}

func (d *DeepLinkData) BuildZPIModuleFederation(props map[string]any) ModuleFederation {
	return d.BuildModuleFederation(d.ZPIUrl, props)
}

func (d *DeepLinkData) BuildZPAModuleFederation(props map[string]any) ModuleFederation {
	return d.BuildModuleFederation(d.ZPAUrl, props)
}

func (d *DeepLinkData) BuildCommonModuleFederation(props map[string]any) ModuleFederation {
	return d.BuildModuleFederation(d.CommonUrl, props)
}

type ModuleFederation struct {
	Entry  string         `json:"entry"`
	Scope  string         `json:"scope"`
	Module string         `json:"module"`
	Props  map[string]any `json:"props"`
}

func (m *ModuleFederation) IsEmpty() bool {
	return reflect.DeepEqual(m, ModuleFederation{})
}
