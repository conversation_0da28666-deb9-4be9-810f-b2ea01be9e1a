package model

type OrderRequest struct {
	AppID        int32  `json:"app_id"`
	AppTransID   string `json:"app_trans_id"`
	ChargeAmount int64  `json:"charge_amount"`
	MerchantName string `json:"merchant_name,omitempty"`
	ServiceType  string `json:"service_type,omitempty"`
	Description  string `json:"description,omitempty"`
	OrderSource  int32  `json:"order_source,omitempty"`
	EmbedData    string `json:"embed_data,omitempty"`
	MNO          string `json:"mno,omitempty"`
}
