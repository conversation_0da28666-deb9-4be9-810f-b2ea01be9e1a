package model

import (
	"fmt"
	"reflect"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type Plan struct {
	PlanID        string
	PlanKey       string
	Tenure        int64
	Status        PlanStatus
	PlanInfo      PlanInfo
	CostInfo      CostInfo
	OrderInfo     OrderRequest
	OtherInfo     OtherInfo
	RepaySchedule PlanRepaySchedule
}

func (p *Plan) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type Plans []*Plan

func (p Plans) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type PlanStatus int

const (
	PlanStatusUnknown PlanStatus = iota
	PlanStatusActive
	PlanStatusInactive
	PlanStatusInvalid
	PlanStatusNotSelected
)

type PlanInfo struct {
	EmiAmount       int64
	TotalAmount     int64
	PrincipalAmount int64
}

type CostInfo struct {
	RbiInterestRate     float64
	FlatInterestRate    float64 // Optional
	InterestAmount      int64
	TotalCostAmount     int64
	TotalFeeAmount      int64
	ListFeeDetails      []FeeDetail
	PlatformFeeAmount   int64
	ConversionFeeAmount int64
}

type OtherInfo struct {
	IsPopular     bool
	PlanDetailUrl DeepLinkData
}

type PlanRepayment struct {
	Amount  int64
	DueDate string
	InstNum int32
}

type PlanRepaySchedule struct {
	Repayments []*PlanRepayment
}

type PlanKeyData struct {
	PlanKey    string
	ZalopayID  int64
	Tenure     int64
	AppID      int32
	AppTransID string
}

type PlanPersist struct {
	OrderAmount  int64
	PlanOptions  []*Plan
	PlanSelected *Plan
}

func NewPlan(tenure int64, orderAmount int64) *Plan {
	return &Plan{
		Tenure:   tenure,
		Status:   PlanStatusActive,
		PlanInfo: PlanInfo{PrincipalAmount: orderAmount},
	}
}

func NewPendingPlan() *Plan {
	return &Plan{Status: PlanStatusNotSelected}
}

func NewInactivePlan(planKey string, tenure int64, orderAmount int64) *Plan {
	return &Plan{
		Tenure:   tenure,
		PlanKey:  planKey,
		Status:   PlanStatusInactive,
		PlanInfo: PlanInfo{PrincipalAmount: orderAmount},
	}
}

func NewInvalidPlan(planKey string, tenure int64, orderAmount int64) *Plan {
	return &Plan{
		Tenure:   tenure,
		PlanKey:  planKey,
		Status:   PlanStatusInvalid,
		PlanInfo: PlanInfo{PrincipalAmount: orderAmount},
	}
}

func NewUnknownPlan(planKey string, tenure int64) *Plan {
	return &Plan{
		Tenure:  tenure,
		PlanKey: planKey,
		Status:  PlanStatusUnknown,
	}
}

func (p PlanKeyData) IsEmpty() bool {
	return p == PlanKeyData{}
}

func (p *Plan) IsEmiInRange(minAmount int64) bool {
	return p.PlanInfo.EmiAmount >= minAmount
}

func (p *Plan) SetCostInfo(costInfo CostInfo) {
	p.CostInfo = costInfo
}

func (p *Plan) SetOrderInfo(orderInfo OrderRequest) {
	p.OrderInfo = orderInfo
}

func (p *Plan) SetPlanInfo(planInfo PlanInfo) {
	p.PlanInfo = planInfo
}

func (p *Plan) SetRepaySchedule(repaySchedule PlanRepaySchedule) {
	p.RepaySchedule = repaySchedule
}

func (p *Plan) SetPlanRepayments(listRepayment []*PlanRepayment) {
	if p == nil {
		*p = Plan{}
	}
	p.RepaySchedule.Repayments = listRepayment
}

func (p *Plan) IsEmptyCost() bool {
	return p == nil || p.CostInfo.IsEmpty()
}

func (p *Plan) IsEmptyPlan() bool {
	return p == nil || reflect.DeepEqual(p.PlanInfo, PlanInfo{})
}

func (p *Plan) IsEmptyOrder() bool {
	return p == nil || reflect.DeepEqual(p.OrderInfo, OrderRequest{})
}

func (p *Plan) IsPlanEligible() bool {
	return p != nil && p.Status == PlanStatusActive
}

func (p *Plan) IsPlanIneligible() bool {
	return p != nil && p.Status != PlanStatusActive
}

func (p *Plan) IsPlanUnknown() bool {
	return p == nil || p.Status == PlanStatusUnknown
}

func (p *Plan) IsPlanPopular() bool {
	return p.OtherInfo.GetIsPopular()
}

func (p *Plan) GetPlanKey() string {
	if p == nil {
		return ""
	}
	return p.PlanKey
}

func (p *Plan) GetPlanDetailUrl() DeepLinkData {
	return p.OtherInfo.GetPlanDetailUrl()
}

func (p *Plan) BuildFSChargeInfo() string {
	if !p.IsPlanEligible() {
		return ""
	}

	type FSChargeInfo struct {
		PlanKey        string      `json:"plan_key"`
		PlanTenure     int64       `json:"plan_tenure"`
		PartnerCode    string      `json:"partner_code"`
		EmiAmount      int64       `json:"emi_amount"`
		InterestRate   float64     `json:"interest_rate"`
		InterestAmount int64       `json:"interest_amount"`
		TotalFeeAmount int64       `json:"total_fee_amount"`
		TotalDueAmount int64       `json:"total_due_amount"`
		ListFeeDetails []FeeDetail `json:"list_fee_details"`
	}
	fsChargeInfo := FSChargeInfo{
		PlanKey:        p.PlanKey,
		PlanTenure:     p.Tenure,
		PartnerCode:    partner.PartnerCIMB.String(),
		EmiAmount:      p.PlanInfo.EmiAmount,
		InterestRate:   p.CostInfo.RbiInterestRate,
		InterestAmount: p.CostInfo.InterestAmount,
		TotalFeeAmount: p.CostInfo.TotalFeeAmount,
		TotalDueAmount: p.PlanInfo.TotalAmount,
		ListFeeDetails: p.CostInfo.ListFeeDetails,
	}
	fsChargeInfoBytes, _ := jsoniter.Marshal(fsChargeInfo)
	return string(fsChargeInfoBytes)
}

func (p *Plan) GetScheduledRepayments() []*PlanRepayment {
	if !p.IsPlanEligible() {
		return nil
	}
	return p.RepaySchedule.Repayments
}

func (c *CostInfo) IsEmpty() bool {
	return reflect.DeepEqual(c, CostInfo{})
}

func (c *CostInfo) GetTotalCost() int64 {
	if c.IsEmpty() {
		return 0
	}
	return c.TotalCostAmount
}

func (c *CostInfo) GetFlatRatePerTenure(tenure int64) float64 {
	return decimal.
		NewFromFloat(c.FlatInterestRate).
		Div(decimal.NewFromInt(tenure)).
		InexactFloat64()
}

func (c *CostInfo) GetFlatPercentPerTenure(tenure int64) float64 {
	return decimal.
		NewFromFloat(c.GetFlatRatePerTenure(tenure)).
		Mul(decimal.NewFromInt(100)).
		InexactFloat64()
}

func (c *CostInfo) GetCostExplanation(tenure int64) string {
	if c.IsEmpty() {
		return ""
	}

	listFee := c.ListFeeDetails
	if len(listFee) == 0 {
		return ""
	}

	flatIPercent := c.GetFlatPercentPerTenure(tenure)
	if flatIPercent == 0 {
		return ""
	}

	listExplain := make([]string, 0, len(listFee))
	listExplain = append(listExplain, fmt.Sprintf(InterestExplain, flatIPercent))
	for _, fee := range listFee {
		listExplain = append(listExplain, fee.Explain)
	}

	return fmt.Sprintf(BaseFeeExplain, strings.Join(listExplain, " + "))
}

func (o *OtherInfo) GetIsPopular() bool {
	return o != nil && o.IsPopular
}

func (o *OtherInfo) GetPlanDetailUrl() DeepLinkData {
	if o == nil {
		return DeepLinkData{}
	}
	return o.PlanDetailUrl
}

func (o *OtherInfo) SetIsPopular(value bool) {
	if o == nil {
		*o = OtherInfo{}
	}
	o.IsPopular = value
}

func (o *OtherInfo) SetPlanDetailUrl(urls DeepLinkData) {
	if o == nil {
		*o = OtherInfo{}
	}
	o.PlanDetailUrl = urls
}
