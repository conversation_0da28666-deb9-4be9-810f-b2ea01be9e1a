package model

import "github.com/shopspring/decimal"

type FeeType string

/**
 * In the future we can have another fee type like zlp_conversion_fee, ...
 */
const (
	UnknownFee    FeeType = "unknown_fee"
	PlatformFee   FeeType = "platform_fee"
	ConversionFee FeeType = "conversion_fee"
)

const (
	BaseFeeExplain  = "Phí trả góp = %s"
	InterestExplain = "%.1f%% giá trị đơn hàng x số kỳ trả góp"
	PlatFeeExplain  = "%dđ"
	ConvFeeExplain  = "%.1f%%"
)

func (f FeeType) String() string {
	return string(f)
}

type FeeConfig struct {
	Rate   float64 `json:"rate"`
	Type   FeeType `json:"type"`
	Amount int64   `json:"amount"`
}

type FeeInfo struct {
	Total   int64       `json:"total"`
	Details []FeeDetail `json:"detail"`
}

type FeeDetail struct {
	Type    FeeType `json:"type"`
	Rate    float64 `json:"rate"` // Total fee rate
	Amount  int64   `json:"amount"`
	Explain string  `json:"explain"`
}

func (f FeeDetail) GetRatePerTenure(tenure int64) float64 {
	return decimal.NewFromFloat(f.Rate).Div(decimal.NewFromInt(tenure)).InexactFloat64()
}

func (f FeeDetail) GetPercentPerTenure(tenure int64) float64 {
	return decimal.NewFromFloat(f.Rate).
		Div(decimal.NewFromInt(tenure)).
		Mul(decimal.NewFromInt(100)).
		InexactFloat64()
}
