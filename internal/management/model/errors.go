package model

import "github.com/pkg/errors"

var (
	ErrFullPlanInvalid     = errors.New("full plan info invalid")
	ErrFullPlanNotFound    = errors.New("full plan info not found")
	ErrPlanItemIsEmpty     = errors.New("plan item is empty")
	ErrPlanItemNotFound    = errors.New("plan item not found")
	ErrPlanOptionsIsEmpty  = errors.New("plan options is empty")
	ErrPlanOptionsNotFound = errors.New("plan options not found")

	ErrStatementNotFound  = errors.New("statement not found")
	ErrStatementEmptyData = errors.New("statement empty data")

	ErrInstallmentNotFound       = errors.New("installment not found")
	ErrInstallmentEmptyData      = errors.New("installment empty data")
	ErrInstallmentRepaysNotFound = errors.New("installment repays not found")
)
