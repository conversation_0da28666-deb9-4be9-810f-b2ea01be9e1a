// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: StatementRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/statement_repo.go --package=repository_mocks . StatementRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockStatementRepo is a mock of StatementRepo interface.
type MockStatementRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStatementRepoMockRecorder
	isgomock struct{}
}

// MockStatementRepoMockRecorder is the mock recorder for MockStatementRepo.
type MockStatementRepoMockRecorder struct {
	mock *MockStatementRepo
}

// NewMockStatementRepo creates a new mock instance.
func NewMockStatementRepo(ctrl *gomock.Controller) *MockStatementRepo {
	mock := &MockStatementRepo{ctrl: ctrl}
	mock.recorder = &MockStatementRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatementRepo) EXPECT() *MockStatementRepoMockRecorder {
	return m.recorder
}

// CreateInstallment mocks base method.
func (m *MockStatementRepo) CreateInstallment(ctx context.Context, installment *model.StatementInstallment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstallment", ctx, installment)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstallment indicates an expected call of CreateInstallment.
func (mr *MockStatementRepoMockRecorder) CreateInstallment(ctx, installment any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstallment", reflect.TypeOf((*MockStatementRepo)(nil).CreateInstallment), ctx, installment)
}

// CreateStatement mocks base method.
func (m *MockStatementRepo) CreateStatement(ctx context.Context, statement *model.Statement) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStatement", ctx, statement)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateStatement indicates an expected call of CreateStatement.
func (mr *MockStatementRepoMockRecorder) CreateStatement(ctx, statement any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStatement", reflect.TypeOf((*MockStatementRepo)(nil).CreateStatement), ctx, statement)
}

// QueryDueDateByIncurDateAndPartner mocks base method.
func (m *MockStatementRepo) QueryDueDateByIncurDateAndPartner(ctx context.Context, partner partner.PartnerCode, incurredDate time.Time) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDueDateByIncurDateAndPartner", ctx, partner, incurredDate)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDueDateByIncurDateAndPartner indicates an expected call of QueryDueDateByIncurDateAndPartner.
func (mr *MockStatementRepoMockRecorder) QueryDueDateByIncurDateAndPartner(ctx, partner, incurredDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDueDateByIncurDateAndPartner", reflect.TypeOf((*MockStatementRepo)(nil).QueryDueDateByIncurDateAndPartner), ctx, partner, incurredDate)
}

// QueryInstallmentsByStatementID mocks base method.
func (m *MockStatementRepo) QueryInstallmentsByStatementID(ctx context.Context, stmtID int64) ([]*model.StatementInstallment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryInstallmentsByStatementID", ctx, stmtID)
	ret0, _ := ret[0].([]*model.StatementInstallment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryInstallmentsByStatementID indicates an expected call of QueryInstallmentsByStatementID.
func (mr *MockStatementRepoMockRecorder) QueryInstallmentsByStatementID(ctx, stmtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryInstallmentsByStatementID", reflect.TypeOf((*MockStatementRepo)(nil).QueryInstallmentsByStatementID), ctx, stmtID)
}

// QueryInstallmentsByUserAndStatementID mocks base method.
func (m *MockStatementRepo) QueryInstallmentsByUserAndStatementID(ctx context.Context, zalopayID, accountID, stmtID int64) ([]*model.StatementInstallment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryInstallmentsByUserAndStatementID", ctx, zalopayID, accountID, stmtID)
	ret0, _ := ret[0].([]*model.StatementInstallment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryInstallmentsByUserAndStatementID indicates an expected call of QueryInstallmentsByUserAndStatementID.
func (mr *MockStatementRepoMockRecorder) QueryInstallmentsByUserAndStatementID(ctx, zalopayID, accountID, stmtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryInstallmentsByUserAndStatementID", reflect.TypeOf((*MockStatementRepo)(nil).QueryInstallmentsByUserAndStatementID), ctx, zalopayID, accountID, stmtID)
}

// QueryLatestStatement mocks base method.
func (m *MockStatementRepo) QueryLatestStatement(ctx context.Context, zalopayID, accountID int64) (*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryLatestStatement", ctx, zalopayID, accountID)
	ret0, _ := ret[0].(*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryLatestStatement indicates an expected call of QueryLatestStatement.
func (mr *MockStatementRepoMockRecorder) QueryLatestStatement(ctx, zalopayID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryLatestStatement", reflect.TypeOf((*MockStatementRepo)(nil).QueryLatestStatement), ctx, zalopayID, accountID)
}

// QueryOutstandingStatementsByIncurDateAndPartner mocks base method.
func (m *MockStatementRepo) QueryOutstandingStatementsByIncurDateAndPartner(ctx context.Context, partner partner.PartnerCode, incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryOutstandingStatementsByIncurDateAndPartner", ctx, partner, incurredDate, paging)
	ret0, _ := ret[0].([]*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOutstandingStatementsByIncurDateAndPartner indicates an expected call of QueryOutstandingStatementsByIncurDateAndPartner.
func (mr *MockStatementRepoMockRecorder) QueryOutstandingStatementsByIncurDateAndPartner(ctx, partner, incurredDate, paging any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOutstandingStatementsByIncurDateAndPartner", reflect.TypeOf((*MockStatementRepo)(nil).QueryOutstandingStatementsByIncurDateAndPartner), ctx, partner, incurredDate, paging)
}

// QueryStatementByID mocks base method.
func (m *MockStatementRepo) QueryStatementByID(ctx context.Context, stmtID int64) (*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryStatementByID", ctx, stmtID)
	ret0, _ := ret[0].(*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStatementByID indicates an expected call of QueryStatementByID.
func (mr *MockStatementRepoMockRecorder) QueryStatementByID(ctx, stmtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStatementByID", reflect.TypeOf((*MockStatementRepo)(nil).QueryStatementByID), ctx, stmtID)
}

// QueryStatementByIncurDate mocks base method.
func (m *MockStatementRepo) QueryStatementByIncurDate(ctx context.Context, zalopayID, accountID int64, incurredDate time.Time) (*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryStatementByIncurDate", ctx, zalopayID, accountID, incurredDate)
	ret0, _ := ret[0].(*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStatementByIncurDate indicates an expected call of QueryStatementByIncurDate.
func (mr *MockStatementRepoMockRecorder) QueryStatementByIncurDate(ctx, zalopayID, accountID, incurredDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStatementByIncurDate", reflect.TypeOf((*MockStatementRepo)(nil).QueryStatementByIncurDate), ctx, zalopayID, accountID, incurredDate)
}

// QueryStatementsByIncurDateAndPartner mocks base method.
func (m *MockStatementRepo) QueryStatementsByIncurDateAndPartner(ctx context.Context, partner partner.PartnerCode, incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryStatementsByIncurDateAndPartner", ctx, partner, incurredDate, paging)
	ret0, _ := ret[0].([]*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStatementsByIncurDateAndPartner indicates an expected call of QueryStatementsByIncurDateAndPartner.
func (mr *MockStatementRepoMockRecorder) QueryStatementsByIncurDateAndPartner(ctx, partner, incurredDate, paging any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStatementsByIncurDateAndPartner", reflect.TypeOf((*MockStatementRepo)(nil).QueryStatementsByIncurDateAndPartner), ctx, partner, incurredDate, paging)
}

// QueryStatementsByIncurDateRange mocks base method.
func (m *MockStatementRepo) QueryStatementsByIncurDateRange(ctx context.Context, zalopayID, accountID int64, from, to time.Time) ([]*model.Statement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryStatementsByIncurDateRange", ctx, zalopayID, accountID, from, to)
	ret0, _ := ret[0].([]*model.Statement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryStatementsByIncurDateRange indicates an expected call of QueryStatementsByIncurDateRange.
func (mr *MockStatementRepoMockRecorder) QueryStatementsByIncurDateRange(ctx, zalopayID, accountID, from, to any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryStatementsByIncurDateRange", reflect.TypeOf((*MockStatementRepo)(nil).QueryStatementsByIncurDateRange), ctx, zalopayID, accountID, from, to)
}

// UpdateStatementOutstandingAndPenaltyByID mocks base method.
func (m *MockStatementRepo) UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, statement *model.Statement) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatementOutstandingAndPenaltyByID", ctx, statement)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatementOutstandingAndPenaltyByID indicates an expected call of UpdateStatementOutstandingAndPenaltyByID.
func (mr *MockStatementRepoMockRecorder) UpdateStatementOutstandingAndPenaltyByID(ctx, statement any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatementOutstandingAndPenaltyByID", reflect.TypeOf((*MockStatementRepo)(nil).UpdateStatementOutstandingAndPenaltyByID), ctx, statement)
}

// UpdateStatementOutstandingByIncurDate mocks base method.
func (m *MockStatementRepo) UpdateStatementOutstandingByIncurDate(ctx context.Context, statement *model.Statement) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatementOutstandingByIncurDate", ctx, statement)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatementOutstandingByIncurDate indicates an expected call of UpdateStatementOutstandingByIncurDate.
func (mr *MockStatementRepoMockRecorder) UpdateStatementOutstandingByIncurDate(ctx, statement any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatementOutstandingByIncurDate", reflect.TypeOf((*MockStatementRepo)(nil).UpdateStatementOutstandingByIncurDate), ctx, statement)
}
