// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: StatementSyncerRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/statement_syncer_repo.go --package=repository_mocks . StatementSyncerRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockStatementSyncerRepo is a mock of StatementSyncerRepo interface.
type MockStatementSyncerRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStatementSyncerRepoMockRecorder
	isgomock struct{}
}

// MockStatementSyncerRepoMockRecorder is the mock recorder for MockStatementSyncerRepo.
type MockStatementSyncerRepoMockRecorder struct {
	mock *MockStatementSyncerRepo
}

// NewMockStatementSyncerRepo creates a new mock instance.
func NewMockStatementSyncerRepo(ctrl *gomock.Controller) *MockStatementSyncerRepo {
	mock := &MockStatementSyncerRepo{ctrl: ctrl}
	mock.recorder = &MockStatementSyncerRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatementSyncerRepo) EXPECT() *MockStatementSyncerRepoMockRecorder {
	return m.recorder
}

// CreateSyncInfo mocks base method.
func (m *MockStatementSyncerRepo) CreateSyncInfo(ctx context.Context, syncInfo *model.StatementSyncInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSyncInfo", ctx, syncInfo)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSyncInfo indicates an expected call of CreateSyncInfo.
func (mr *MockStatementSyncerRepoMockRecorder) CreateSyncInfo(ctx, syncInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSyncInfo", reflect.TypeOf((*MockStatementSyncerRepo)(nil).CreateSyncInfo), ctx, syncInfo)
}

// GetSyncerBatchByPeriodAndPartner mocks base method.
func (m *MockStatementSyncerRepo) GetSyncerBatchByPeriodAndPartner(ctx context.Context, stmtPeriod *model.StatementPeriod, partnerCode partner.PartnerCode) ([]*model.StatementSyncInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncerBatchByPeriodAndPartner", ctx, stmtPeriod, partnerCode)
	ret0, _ := ret[0].([]*model.StatementSyncInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncerBatchByPeriodAndPartner indicates an expected call of GetSyncerBatchByPeriodAndPartner.
func (mr *MockStatementSyncerRepoMockRecorder) GetSyncerBatchByPeriodAndPartner(ctx, stmtPeriod, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncerBatchByPeriodAndPartner", reflect.TypeOf((*MockStatementSyncerRepo)(nil).GetSyncerBatchByPeriodAndPartner), ctx, stmtPeriod, partnerCode)
}

// UpdateSyncStatus mocks base method.
func (m *MockStatementSyncerRepo) UpdateSyncStatus(ctx context.Context, syncInfo *model.StatementSyncInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSyncStatus", ctx, syncInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSyncStatus indicates an expected call of UpdateSyncStatus.
func (mr *MockStatementSyncerRepoMockRecorder) UpdateSyncStatus(ctx, syncInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSyncStatus", reflect.TypeOf((*MockStatementSyncerRepo)(nil).UpdateSyncStatus), ctx, syncInfo)
}

// UpdateSyncStatusAndStats mocks base method.
func (m *MockStatementSyncerRepo) UpdateSyncStatusAndStats(ctx context.Context, syncInfo *model.StatementSyncInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSyncStatusAndStats", ctx, syncInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSyncStatusAndStats indicates an expected call of UpdateSyncStatusAndStats.
func (mr *MockStatementSyncerRepoMockRecorder) UpdateSyncStatusAndStats(ctx, syncInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSyncStatusAndStats", reflect.TypeOf((*MockStatementSyncerRepo)(nil).UpdateSyncStatusAndStats), ctx, syncInfo)
}
