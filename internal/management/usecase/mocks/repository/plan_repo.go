// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: PlanRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/plan_repo.go --package=repository_mocks . PlanRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	gomock "go.uber.org/mock/gomock"
)

// MockPlanRepo is a mock of PlanRepo interface.
type MockPlanRepo struct {
	ctrl     *gomock.Controller
	recorder *MockPlanRepoMockRecorder
	isgomock struct{}
}

// MockPlanRepoMockRecorder is the mock recorder for MockPlanRepo.
type MockPlanRepoMockRecorder struct {
	mock *MockPlanRepo
}

// NewMockPlanRepo creates a new mock instance.
func NewMockPlanRepo(ctrl *gomock.Controller) *MockPlanRepo {
	mock := &MockPlanRepo{ctrl: ctrl}
	mock.recorder = &MockPlanRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlanRepo) EXPECT() *MockPlanRepoMockRecorder {
	return m.recorder
}

// PersistFullPlanInfo mocks base method.
func (m *MockPlanRepo) PersistFullPlanInfo(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest, planInfo *model.PlanPersist) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PersistFullPlanInfo", ctx, zalopayID, orderInfo, planInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// PersistFullPlanInfo indicates an expected call of PersistFullPlanInfo.
func (mr *MockPlanRepoMockRecorder) PersistFullPlanInfo(ctx, zalopayID, orderInfo, planInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PersistFullPlanInfo", reflect.TypeOf((*MockPlanRepo)(nil).PersistFullPlanInfo), ctx, zalopayID, orderInfo, planInfo)
}

// PersistPlanOptions mocks base method.
func (m *MockPlanRepo) PersistPlanOptions(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest, plansOpts []*model.Plan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PersistPlanOptions", ctx, zalopayID, orderInfo, plansOpts)
	ret0, _ := ret[0].(error)
	return ret0
}

// PersistPlanOptions indicates an expected call of PersistPlanOptions.
func (mr *MockPlanRepoMockRecorder) PersistPlanOptions(ctx, zalopayID, orderInfo, plansOpts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PersistPlanOptions", reflect.TypeOf((*MockPlanRepo)(nil).PersistPlanOptions), ctx, zalopayID, orderInfo, plansOpts)
}

// PersistPlanSelected mocks base method.
func (m *MockPlanRepo) PersistPlanSelected(ctx context.Context, planKeyData model.PlanKeyData, planResult *model.Plan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PersistPlanSelected", ctx, planKeyData, planResult)
	ret0, _ := ret[0].(error)
	return ret0
}

// PersistPlanSelected indicates an expected call of PersistPlanSelected.
func (mr *MockPlanRepoMockRecorder) PersistPlanSelected(ctx, planKeyData, planResult any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PersistPlanSelected", reflect.TypeOf((*MockPlanRepo)(nil).PersistPlanSelected), ctx, planKeyData, planResult)
}

// RetrieveFullPlanInfo mocks base method.
func (m *MockPlanRepo) RetrieveFullPlanInfo(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) (*model.PlanPersist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveFullPlanInfo", ctx, zalopayID, orderInfo)
	ret0, _ := ret[0].(*model.PlanPersist)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrieveFullPlanInfo indicates an expected call of RetrieveFullPlanInfo.
func (mr *MockPlanRepoMockRecorder) RetrieveFullPlanInfo(ctx, zalopayID, orderInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveFullPlanInfo", reflect.TypeOf((*MockPlanRepo)(nil).RetrieveFullPlanInfo), ctx, zalopayID, orderInfo)
}

// RetrievePlanOptions mocks base method.
func (m *MockPlanRepo) RetrievePlanOptions(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) ([]*model.Plan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrievePlanOptions", ctx, zalopayID, orderInfo)
	ret0, _ := ret[0].([]*model.Plan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrievePlanOptions indicates an expected call of RetrievePlanOptions.
func (mr *MockPlanRepoMockRecorder) RetrievePlanOptions(ctx, zalopayID, orderInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrievePlanOptions", reflect.TypeOf((*MockPlanRepo)(nil).RetrievePlanOptions), ctx, zalopayID, orderInfo)
}

// RetrievePlanSelected mocks base method.
func (m *MockPlanRepo) RetrievePlanSelected(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) (*model.Plan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrievePlanSelected", ctx, zalopayID, orderInfo)
	ret0, _ := ret[0].(*model.Plan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RetrievePlanSelected indicates an expected call of RetrievePlanSelected.
func (mr *MockPlanRepoMockRecorder) RetrievePlanSelected(ctx, zalopayID, orderInfo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrievePlanSelected", reflect.TypeOf((*MockPlanRepo)(nil).RetrievePlanSelected), ctx, zalopayID, orderInfo)
}
