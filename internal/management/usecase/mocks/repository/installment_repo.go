// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: InstallmentRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/installment_repo.go --package=repository_mocks . InstallmentRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	gomock "go.uber.org/mock/gomock"
)

// MockInstallmentRepo is a mock of InstallmentRepo interface.
type MockInstallmentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockInstallmentRepoMockRecorder
	isgomock struct{}
}

// MockInstallmentRepoMockRecorder is the mock recorder for MockInstallmentRepo.
type MockInstallmentRepoMockRecorder struct {
	mock *MockInstallmentRepo
}

// NewMockInstallmentRepo creates a new mock instance.
func NewMockInstallmentRepo(ctrl *gomock.Controller) *MockInstallmentRepo {
	mock := &MockInstallmentRepo{ctrl: ctrl}
	mock.recorder = &MockInstallmentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstallmentRepo) EXPECT() *MockInstallmentRepoMockRecorder {
	return m.recorder
}

// CreateInstallment mocks base method.
func (m *MockInstallmentRepo) CreateInstallment(ctx context.Context, installment *model.InstallmentInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstallment", ctx, installment)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstallment indicates an expected call of CreateInstallment.
func (mr *MockInstallmentRepoMockRecorder) CreateInstallment(ctx, installment any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstallment", reflect.TypeOf((*MockInstallmentRepo)(nil).CreateInstallment), ctx, installment)
}

// CreateRepaymentSchedule mocks base method.
func (m *MockInstallmentRepo) CreateRepaymentSchedule(ctx context.Context, repay *model.InstallmentRepay) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRepaymentSchedule", ctx, repay)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRepaymentSchedule indicates an expected call of CreateRepaymentSchedule.
func (mr *MockInstallmentRepoMockRecorder) CreateRepaymentSchedule(ctx, repay any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRepaymentSchedule", reflect.TypeOf((*MockInstallmentRepo)(nil).CreateRepaymentSchedule), ctx, repay)
}

// GetFullInstallmentByUserIDAndZPTransID mocks base method.
func (m *MockInstallmentRepo) GetFullInstallmentByUserIDAndZPTransID(ctx context.Context, zalopayID, transID int64) (*model.Installment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFullInstallmentByUserIDAndZPTransID", ctx, zalopayID, transID)
	ret0, _ := ret[0].(*model.Installment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFullInstallmentByUserIDAndZPTransID indicates an expected call of GetFullInstallmentByUserIDAndZPTransID.
func (mr *MockInstallmentRepoMockRecorder) GetFullInstallmentByUserIDAndZPTransID(ctx, zalopayID, transID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFullInstallmentByUserIDAndZPTransID", reflect.TypeOf((*MockInstallmentRepo)(nil).GetFullInstallmentByUserIDAndZPTransID), ctx, zalopayID, transID)
}

// GetFullInstallmentByZPTransID mocks base method.
func (m *MockInstallmentRepo) GetFullInstallmentByZPTransID(ctx context.Context, transID int64) (*model.Installment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFullInstallmentByZPTransID", ctx, transID)
	ret0, _ := ret[0].(*model.Installment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFullInstallmentByZPTransID indicates an expected call of GetFullInstallmentByZPTransID.
func (mr *MockInstallmentRepoMockRecorder) GetFullInstallmentByZPTransID(ctx, transID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFullInstallmentByZPTransID", reflect.TypeOf((*MockInstallmentRepo)(nil).GetFullInstallmentByZPTransID), ctx, transID)
}

// GetInstallmentByID mocks base method.
func (m *MockInstallmentRepo) GetInstallmentByID(ctx context.Context, instID int64) (*model.InstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByID", ctx, instID)
	ret0, _ := ret[0].(*model.InstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByID indicates an expected call of GetInstallmentByID.
func (mr *MockInstallmentRepoMockRecorder) GetInstallmentByID(ctx, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByID", reflect.TypeOf((*MockInstallmentRepo)(nil).GetInstallmentByID), ctx, instID)
}

// GetInstallmentByIDForUpdate mocks base method.
func (m *MockInstallmentRepo) GetInstallmentByIDForUpdate(ctx context.Context, instID int64) (*model.InstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByIDForUpdate", ctx, instID)
	ret0, _ := ret[0].(*model.InstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByIDForUpdate indicates an expected call of GetInstallmentByIDForUpdate.
func (mr *MockInstallmentRepoMockRecorder) GetInstallmentByIDForUpdate(ctx, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByIDForUpdate", reflect.TypeOf((*MockInstallmentRepo)(nil).GetInstallmentByIDForUpdate), ctx, instID)
}

// GetInstallmentByZPTransID mocks base method.
func (m *MockInstallmentRepo) GetInstallmentByZPTransID(ctx context.Context, transID int64) (*model.InstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByZPTransID", ctx, transID)
	ret0, _ := ret[0].(*model.InstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByZPTransID indicates an expected call of GetInstallmentByZPTransID.
func (mr *MockInstallmentRepoMockRecorder) GetInstallmentByZPTransID(ctx, transID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByZPTransID", reflect.TypeOf((*MockInstallmentRepo)(nil).GetInstallmentByZPTransID), ctx, transID)
}

// ListFullOpenInstallmentByUserAndAcctID mocks base method.
func (m *MockInstallmentRepo) ListFullOpenInstallmentByUserAndAcctID(ctx context.Context, zalopayID, accountID int64) ([]*model.Installment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListFullOpenInstallmentByUserAndAcctID", ctx, zalopayID, accountID)
	ret0, _ := ret[0].([]*model.Installment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFullOpenInstallmentByUserAndAcctID indicates an expected call of ListFullOpenInstallmentByUserAndAcctID.
func (mr *MockInstallmentRepoMockRecorder) ListFullOpenInstallmentByUserAndAcctID(ctx, zalopayID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFullOpenInstallmentByUserAndAcctID", reflect.TypeOf((*MockInstallmentRepo)(nil).ListFullOpenInstallmentByUserAndAcctID), ctx, zalopayID, accountID)
}

// ListInstallmentByPartnerIDs mocks base method.
func (m *MockInstallmentRepo) ListInstallmentByPartnerIDs(ctx context.Context, partnerInstIDs []string) ([]*model.InstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstallmentByPartnerIDs", ctx, partnerInstIDs)
	ret0, _ := ret[0].([]*model.InstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstallmentByPartnerIDs indicates an expected call of ListInstallmentByPartnerIDs.
func (mr *MockInstallmentRepoMockRecorder) ListInstallmentByPartnerIDs(ctx, partnerInstIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstallmentByPartnerIDs", reflect.TypeOf((*MockInstallmentRepo)(nil).ListInstallmentByPartnerIDs), ctx, partnerInstIDs)
}

// ListInstallmentPeriodicSync mocks base method.
func (m *MockInstallmentRepo) ListInstallmentPeriodicSync(ctx context.Context, cutoffTime time.Time, paging *model.Pagination) ([]*model.InstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstallmentPeriodicSync", ctx, cutoffTime, paging)
	ret0, _ := ret[0].([]*model.InstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstallmentPeriodicSync indicates an expected call of ListInstallmentPeriodicSync.
func (mr *MockInstallmentRepoMockRecorder) ListInstallmentPeriodicSync(ctx, cutoffTime, paging any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstallmentPeriodicSync", reflect.TypeOf((*MockInstallmentRepo)(nil).ListInstallmentPeriodicSync), ctx, cutoffTime, paging)
}

// MarkFirstTenureSettled mocks base method.
func (m *MockInstallmentRepo) MarkFirstTenureSettled(ctx context.Context, instID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkFirstTenureSettled", ctx, instID)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkFirstTenureSettled indicates an expected call of MarkFirstTenureSettled.
func (mr *MockInstallmentRepoMockRecorder) MarkFirstTenureSettled(ctx, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkFirstTenureSettled", reflect.TypeOf((*MockInstallmentRepo)(nil).MarkFirstTenureSettled), ctx, instID)
}

// UpdateEarlyDischargeInfo mocks base method.
func (m *MockInstallmentRepo) UpdateEarlyDischargeInfo(ctx context.Context, inst *model.InstallmentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEarlyDischargeInfo", ctx, inst)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEarlyDischargeInfo indicates an expected call of UpdateEarlyDischargeInfo.
func (mr *MockInstallmentRepoMockRecorder) UpdateEarlyDischargeInfo(ctx, inst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEarlyDischargeInfo", reflect.TypeOf((*MockInstallmentRepo)(nil).UpdateEarlyDischargeInfo), ctx, inst)
}

// UpdateEarlyDischargeStatus mocks base method.
func (m *MockInstallmentRepo) UpdateEarlyDischargeStatus(ctx context.Context, instID int64, status model.DischargeStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEarlyDischargeStatus", ctx, instID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEarlyDischargeStatus indicates an expected call of UpdateEarlyDischargeStatus.
func (mr *MockInstallmentRepoMockRecorder) UpdateEarlyDischargeStatus(ctx, instID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEarlyDischargeStatus", reflect.TypeOf((*MockInstallmentRepo)(nil).UpdateEarlyDischargeStatus), ctx, instID, status)
}

// UpdateInstallmentAfterSync mocks base method.
func (m *MockInstallmentRepo) UpdateInstallmentAfterSync(ctx context.Context, inst *model.InstallmentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallmentAfterSync", ctx, inst)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallmentAfterSync indicates an expected call of UpdateInstallmentAfterSync.
func (mr *MockInstallmentRepoMockRecorder) UpdateInstallmentAfterSync(ctx, inst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallmentAfterSync", reflect.TypeOf((*MockInstallmentRepo)(nil).UpdateInstallmentAfterSync), ctx, inst)
}

// UpdateInstallmentRefund mocks base method.
func (m *MockInstallmentRepo) UpdateInstallmentRefund(ctx context.Context, instID int64, params *model.InstallmentRefund) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallmentRefund", ctx, instID, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallmentRefund indicates an expected call of UpdateInstallmentRefund.
func (mr *MockInstallmentRepoMockRecorder) UpdateInstallmentRefund(ctx, instID, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallmentRefund", reflect.TypeOf((*MockInstallmentRepo)(nil).UpdateInstallmentRefund), ctx, instID, params)
}

// UpdateRepayScheduleAfterSync mocks base method.
func (m *MockInstallmentRepo) UpdateRepayScheduleAfterSync(ctx context.Context, repay *model.InstallmentRepay) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRepayScheduleAfterSync", ctx, repay)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRepayScheduleAfterSync indicates an expected call of UpdateRepayScheduleAfterSync.
func (mr *MockInstallmentRepoMockRecorder) UpdateRepayScheduleAfterSync(ctx, repay any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRepayScheduleAfterSync", reflect.TypeOf((*MockInstallmentRepo)(nil).UpdateRepayScheduleAfterSync), ctx, repay)
}
