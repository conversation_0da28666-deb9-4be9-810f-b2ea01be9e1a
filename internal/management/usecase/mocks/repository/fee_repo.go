// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: FeeRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/fee_repo.go --package=repository_mocks . FeeRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	gomock "go.uber.org/mock/gomock"
)

// MockFeeRepo is a mock of FeeRepo interface.
type MockFeeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockFeeRepoMockRecorder
	isgomock struct{}
}

// MockFeeRepoMockRecorder is the mock recorder for MockFeeRepo.
type MockFeeRepoMockRecorder struct {
	mock *MockFeeRepo
}

// NewMockFeeRepo creates a new mock instance.
func NewMockFeeRepo(ctrl *gomock.Controller) *MockFeeRepo {
	mock := &MockFeeRepo{ctrl: ctrl}
	mock.recorder = &MockFeeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeeRepo) EXPECT() *MockFeeRepoMockRecorder {
	return m.recorder
}

// EvaluateFeesInfo mocks base method.
func (m *MockFeeRepo) EvaluateFeesInfo(ctx context.Context, orderAmount int64) []*model.FeeDetail {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EvaluateFeesInfo", ctx, orderAmount)
	ret0, _ := ret[0].([]*model.FeeDetail)
	return ret0
}

// EvaluateFeesInfo indicates an expected call of EvaluateFeesInfo.
func (mr *MockFeeRepoMockRecorder) EvaluateFeesInfo(ctx, orderAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvaluateFeesInfo", reflect.TypeOf((*MockFeeRepo)(nil).EvaluateFeesInfo), ctx, orderAmount)
}

// RetrieveFeesInfo mocks base method.
func (m *MockFeeRepo) RetrieveFeesInfo(ctx context.Context) []*model.FeeConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RetrieveFeesInfo", ctx)
	ret0, _ := ret[0].([]*model.FeeConfig)
	return ret0
}

// RetrieveFeesInfo indicates an expected call of RetrieveFeesInfo.
func (mr *MockFeeRepoMockRecorder) RetrieveFeesInfo(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RetrieveFeesInfo", reflect.TypeOf((*MockFeeRepo)(nil).RetrieveFeesInfo), ctx)
}
