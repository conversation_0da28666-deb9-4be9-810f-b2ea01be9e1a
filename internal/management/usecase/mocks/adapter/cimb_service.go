// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: CIMBService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	gomock "go.uber.org/mock/gomock"
)

// MockCIMBService is a mock of CIMBService interface.
type MockCIMBService struct {
	ctrl     *gomock.Controller
	recorder *MockCIMBServiceMockRecorder
	isgomock struct{}
}

// MockCIMBServiceMockRecorder is the mock recorder for MockCIMBService.
type MockCIMBServiceMockRecorder struct {
	mock *MockCIMBService
}

// NewMockCIMBService creates a new mock instance.
func NewMockCIMBService(ctrl *gomock.Controller) *MockCIMBService {
	mock := &MockCIMBService{ctrl: ctrl}
	mock.recorder = &MockCIMBServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCIMBService) EXPECT() *MockCIMBServiceMockRecorder {
	return m.recorder
}

// CheckStatementsEligibleByIncurredDate mocks base method.
func (m *MockCIMBService) CheckStatementsEligibleByIncurredDate(ctx context.Context, date time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStatementsEligibleByIncurredDate", ctx, date)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStatementsEligibleByIncurredDate indicates an expected call of CheckStatementsEligibleByIncurredDate.
func (mr *MockCIMBServiceMockRecorder) CheckStatementsEligibleByIncurredDate(ctx, date any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStatementsEligibleByIncurredDate", reflect.TypeOf((*MockCIMBService)(nil).CheckStatementsEligibleByIncurredDate), ctx, date)
}

// GetStatementByIncurredDate mocks base method.
func (m *MockCIMBService) GetStatementByIncurredDate(ctx context.Context, account *model.Account, stmtDate time.Time) (*model.StatementResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatementByIncurredDate", ctx, account, stmtDate)
	ret0, _ := ret[0].(*model.StatementResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatementByIncurredDate indicates an expected call of GetStatementByIncurredDate.
func (mr *MockCIMBServiceMockRecorder) GetStatementByIncurredDate(ctx, account, stmtDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatementByIncurredDate", reflect.TypeOf((*MockCIMBService)(nil).GetStatementByIncurredDate), ctx, account, stmtDate)
}

// GetStatementDueOutstanding mocks base method.
func (m *MockCIMBService) GetStatementDueOutstanding(ctx context.Context, account *model.Account) (*model.StatementOutstanding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatementDueOutstanding", ctx, account)
	ret0, _ := ret[0].(*model.StatementOutstanding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatementDueOutstanding indicates an expected call of GetStatementDueOutstanding.
func (mr *MockCIMBServiceMockRecorder) GetStatementDueOutstanding(ctx, account any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatementDueOutstanding", reflect.TypeOf((*MockCIMBService)(nil).GetStatementDueOutstanding), ctx, account)
}

// QueryEarlyDischargeByID mocks base method.
func (m *MockCIMBService) QueryEarlyDischargeByID(ctx context.Context, zalopayID int64, instID string) (*model.PartnerEarlyDischarge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryEarlyDischargeByID", ctx, zalopayID, instID)
	ret0, _ := ret[0].(*model.PartnerEarlyDischarge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryEarlyDischargeByID indicates an expected call of QueryEarlyDischargeByID.
func (mr *MockCIMBServiceMockRecorder) QueryEarlyDischargeByID(ctx, zalopayID, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryEarlyDischargeByID", reflect.TypeOf((*MockCIMBService)(nil).QueryEarlyDischargeByID), ctx, zalopayID, instID)
}

// QueryInstallmentByID mocks base method.
func (m *MockCIMBService) QueryInstallmentByID(ctx context.Context, zalopayID int64, instID string) (*model.PartnerCIMBInst, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryInstallmentByID", ctx, zalopayID, instID)
	ret0, _ := ret[0].(*model.PartnerCIMBInst)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryInstallmentByID indicates an expected call of QueryInstallmentByID.
func (mr *MockCIMBServiceMockRecorder) QueryInstallmentByID(ctx, zalopayID, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryInstallmentByID", reflect.TypeOf((*MockCIMBService)(nil).QueryInstallmentByID), ctx, zalopayID, instID)
}

// QueryInstallmentByTransID mocks base method.
func (m *MockCIMBService) QueryInstallmentByTransID(ctx context.Context, zalopayID, transID int64) (*model.PartnerCIMBInst, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryInstallmentByTransID", ctx, zalopayID, transID)
	ret0, _ := ret[0].(*model.PartnerCIMBInst)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryInstallmentByTransID indicates an expected call of QueryInstallmentByTransID.
func (mr *MockCIMBServiceMockRecorder) QueryInstallmentByTransID(ctx, zalopayID, transID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryInstallmentByTransID", reflect.TypeOf((*MockCIMBService)(nil).QueryInstallmentByTransID), ctx, zalopayID, transID)
}

// QueryPlanOptions mocks base method.
func (m *MockCIMBService) QueryPlanOptions(ctx context.Context, zalopayID, orderAmount int64) ([]*model.PartnerPlanOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryPlanOptions", ctx, zalopayID, orderAmount)
	ret0, _ := ret[0].([]*model.PartnerPlanOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryPlanOptions indicates an expected call of QueryPlanOptions.
func (mr *MockCIMBServiceMockRecorder) QueryPlanOptions(ctx, zalopayID, orderAmount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryPlanOptions", reflect.TypeOf((*MockCIMBService)(nil).QueryPlanOptions), ctx, zalopayID, orderAmount)
}
