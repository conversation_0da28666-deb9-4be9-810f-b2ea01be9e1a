// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: JobTaskMgmt)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/job_task_mgmt.go --package=adapter_mocks . JobTaskMgmt
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockJobTaskMgmt is a mock of JobTaskMgmt interface.
type MockJobTaskMgmt struct {
	ctrl     *gomock.Controller
	recorder *MockJobTaskMgmtMockRecorder
	isgomock struct{}
}

// MockJobTaskMgmtMockRecorder is the mock recorder for MockJobTaskMgmt.
type MockJobTaskMgmtMockRecorder struct {
	mock *MockJobTaskMgmt
}

// NewMockJobTaskMgmt creates a new mock instance.
func NewMockJobTaskMgmt(ctrl *gomock.Controller) *MockJobTaskMgmt {
	mock := &MockJobTaskMgmt{ctrl: ctrl}
	mock.recorder = &MockJobTaskMgmtMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJobTaskMgmt) EXPECT() *MockJobTaskMgmtMockRecorder {
	return m.recorder
}

// CanExecuteSyncInstallmentsDailyTask mocks base method.
func (m *MockJobTaskMgmt) CanExecuteSyncInstallmentsDailyTask(ctx context.Context) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CanExecuteSyncInstallmentsDailyTask", ctx)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CanExecuteSyncInstallmentsDailyTask indicates an expected call of CanExecuteSyncInstallmentsDailyTask.
func (mr *MockJobTaskMgmtMockRecorder) CanExecuteSyncInstallmentsDailyTask(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanExecuteSyncInstallmentsDailyTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).CanExecuteSyncInstallmentsDailyTask), ctx)
}

// ExecuteSyncAccountBalanceAfterDischargeTask mocks base method.
func (m *MockJobTaskMgmt) ExecuteSyncAccountBalanceAfterDischargeTask(ctx context.Context, params *dto.SyncAccountBalanceAfterDischargeParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSyncAccountBalanceAfterDischargeTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSyncAccountBalanceAfterDischargeTask indicates an expected call of ExecuteSyncAccountBalanceAfterDischargeTask.
func (mr *MockJobTaskMgmtMockRecorder) ExecuteSyncAccountBalanceAfterDischargeTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSyncAccountBalanceAfterDischargeTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).ExecuteSyncAccountBalanceAfterDischargeTask), ctx, params)
}

// ExecuteSyncAccountBalanceAfterStatementTask mocks base method.
func (m *MockJobTaskMgmt) ExecuteSyncAccountBalanceAfterStatementTask(ctx context.Context, params *dto.SyncAccountBalanceAfterStatementParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSyncAccountBalanceAfterStatementTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSyncAccountBalanceAfterStatementTask indicates an expected call of ExecuteSyncAccountBalanceAfterStatementTask.
func (mr *MockJobTaskMgmtMockRecorder) ExecuteSyncAccountBalanceAfterStatementTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSyncAccountBalanceAfterStatementTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).ExecuteSyncAccountBalanceAfterStatementTask), ctx, params)
}

// ExecuteSyncInstallmentDailyTask mocks base method.
func (m *MockJobTaskMgmt) ExecuteSyncInstallmentDailyTask(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSyncInstallmentDailyTask", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSyncInstallmentDailyTask indicates an expected call of ExecuteSyncInstallmentDailyTask.
func (mr *MockJobTaskMgmtMockRecorder) ExecuteSyncInstallmentDailyTask(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSyncInstallmentDailyTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).ExecuteSyncInstallmentDailyTask), ctx)
}

// ExecuteSyncInstallmentTask mocks base method.
func (m *MockJobTaskMgmt) ExecuteSyncInstallmentTask(ctx context.Context, params *dto.SyncInstallmentParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSyncInstallmentTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSyncInstallmentTask indicates an expected call of ExecuteSyncInstallmentTask.
func (mr *MockJobTaskMgmtMockRecorder) ExecuteSyncInstallmentTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSyncInstallmentTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).ExecuteSyncInstallmentTask), ctx, params)
}

// ExecuteSyncLatestStatementTask mocks base method.
func (m *MockJobTaskMgmt) ExecuteSyncLatestStatementTask(ctx context.Context, params *dto.SyncLatestStatementParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSyncLatestStatementTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSyncLatestStatementTask indicates an expected call of ExecuteSyncLatestStatementTask.
func (mr *MockJobTaskMgmtMockRecorder) ExecuteSyncLatestStatementTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSyncLatestStatementTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).ExecuteSyncLatestStatementTask), ctx, params)
}
