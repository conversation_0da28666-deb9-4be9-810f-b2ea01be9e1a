// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: AccountService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/account_service.go --package=adapter_mocks . AccountService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockAccountService is a mock of AccountService interface.
type MockAccountService struct {
	ctrl     *gomock.Controller
	recorder *MockAccountServiceMockRecorder
	isgomock struct{}
}

// MockAccountServiceMockRecorder is the mock recorder for MockAccountService.
type MockAccountServiceMockRecorder struct {
	mock *MockAccountService
}

// NewMockAccountService creates a new mock instance.
func NewMockAccountService(ctrl *gomock.Controller) *MockAccountService {
	mock := &MockAccountService{ctrl: ctrl}
	mock.recorder = &MockAccountServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountService) EXPECT() *MockAccountServiceMockRecorder {
	return m.recorder
}

// GetActiveAccountByID mocks base method.
func (m *MockAccountService) GetActiveAccountByID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveAccountByID", ctx, zalopayID, accountID)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveAccountByID indicates an expected call of GetActiveAccountByID.
func (mr *MockAccountServiceMockRecorder) GetActiveAccountByID(ctx, zalopayID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveAccountByID", reflect.TypeOf((*MockAccountService)(nil).GetActiveAccountByID), ctx, zalopayID, accountID)
}

// GetActiveAccountByPartner mocks base method.
func (m *MockAccountService) GetActiveAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveAccountByPartner", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveAccountByPartner indicates an expected call of GetActiveAccountByPartner.
func (mr *MockAccountServiceMockRecorder) GetActiveAccountByPartner(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveAccountByPartner", reflect.TypeOf((*MockAccountService)(nil).GetActiveAccountByPartner), ctx, zalopayID, partnerCode)
}

// ListAccountForStmtSync mocks base method.
func (m *MockAccountService) ListAccountForStmtSync(ctx context.Context, params *dto.AccountForStatementParams) ([]*model.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountForStmtSync", ctx, params)
	ret0, _ := ret[0].([]*model.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountForStmtSync indicates an expected call of ListAccountForStmtSync.
func (mr *MockAccountServiceMockRecorder) ListAccountForStmtSync(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountForStmtSync", reflect.TypeOf((*MockAccountService)(nil).ListAccountForStmtSync), ctx, params)
}
