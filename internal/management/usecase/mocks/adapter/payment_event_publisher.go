// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface (interfaces: PaymentEventPublisher)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/payment_event_publisher.go --package=adapter_mocks . PaymentEventPublisher
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockPaymentEventPublisher is a mock of PaymentEventPublisher interface.
type MockPaymentEventPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentEventPublisherMockRecorder
	isgomock struct{}
}

// MockPaymentEventPublisherMockRecorder is the mock recorder for MockPaymentEventPublisher.
type MockPaymentEventPublisherMockRecorder struct {
	mock *MockPaymentEventPublisher
}

// NewMockPaymentEventPublisher creates a new mock instance.
func NewMockPaymentEventPublisher(ctrl *gomock.Controller) *MockPaymentEventPublisher {
	mock := &MockPaymentEventPublisher{ctrl: ctrl}
	mock.recorder = &MockPaymentEventPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentEventPublisher) EXPECT() *MockPaymentEventPublisherMockRecorder {
	return m.recorder
}

// PublishPaymentCommission mocks base method.
func (m *MockPaymentEventPublisher) PublishPaymentCommission(ctx context.Context, event *dto.PaymentCommissionEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishPaymentCommission", ctx, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishPaymentCommission indicates an expected call of PublishPaymentCommission.
func (mr *MockPaymentEventPublisherMockRecorder) PublishPaymentCommission(ctx, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishPaymentCommission", reflect.TypeOf((*MockPaymentEventPublisher)(nil).PublishPaymentCommission), ctx, event)
}
