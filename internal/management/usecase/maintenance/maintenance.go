package maintenance

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type Usecase struct {
	logger   *log.Helper
	maintMgr maintenance.Handler
}

func NewUsecase(maintenanceManager maintenance.Handler, kLogger log.Logger) *Usecase {
	logger := log.With(kLogger, "usecase", "maintenance")
	return &Usecase{
		logger:   log.NewHelper(logger),
		maintMgr: maintenanceManager,
	}
}

func (uc *Usecase) SetMaintenance(ctx context.Context, data *dto.MaintenanceData) error {
	logger := uc.logger.WithContext(ctx)

	mStateData := maintenance.State{
		All:        data.MaintenanceState.All,
		Onboarding: data.MaintenanceState.Onboarding,
		Purchase:   data.MaintenanceState.Purchase,
		Repayment:  data.MaintenanceState.Repayment,
	}
	if err := uc.maintMgr.SetMaintenanceState(ctx, data.PartnerCode, mStateData); err != nil {
		logger.Errorf("SetMaintenance error: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "SetMaintenance error").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	return nil
}

func (uc *Usecase) GetMaintenance(ctx context.Context, partnerCode partner.PartnerCode) (*dto.MaintenanceData, error) {
	logger := uc.logger.WithContext(ctx)

	mData, err := uc.maintMgr.GetMaintenanceState(ctx, partnerCode)
	if err != nil {
		logger.Errorf("GetMaintenance error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "GetMaintenance error").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	return &dto.MaintenanceData{
		PartnerCode:      partnerCode,
		MaintenanceState: dto.MaintenanceState(mData),
	}, nil
}
