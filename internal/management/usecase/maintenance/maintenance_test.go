package maintenance

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	maintenance_mocks "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance/mocks"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type MaintenanceTestSuite struct {
	suite.Suite
	ctrl      *gomock.Controller
	mockMaint *maintenance_mocks.MockHandler
	usecase   *Usecase
}

func TestMaintenanceSuite(t *testing.T) {
	suite.Run(t, new(MaintenanceTestSuite))
}

func (s *MaintenanceTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockMaint = maintenance_mocks.NewMockHandler(s.ctrl)
	s.usecase = NewUsecase(s.mockMaint, log.DefaultLogger)
}

func (s *MaintenanceTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *MaintenanceTestSuite) TestSetMaintenance_Success() {
	// Arrange
	ctx := context.Background()
	data := &dto.MaintenanceData{
		PartnerCode: partner.PartnerCIMB,
		MaintenanceState: dto.MaintenanceState{
			All:        true,
			Onboarding: true,
			Purchase:   true,
			Repayment:  true,
		},
	}

	s.mockMaint.EXPECT().
		SetMaintenanceState(gomock.Any(), partner.PartnerCIMB, maintenance.State{
			All:        true,
			Onboarding: true,
			Purchase:   true,
			Repayment:  true,
		}).
		Return(nil)

	// Act
	err := s.usecase.SetMaintenance(ctx, data)

	// Assert
	s.NoError(err)
}

func (s *MaintenanceTestSuite) TestSetMaintenance_Error() {
	// Arrange
	ctx := context.Background()
	data := &dto.MaintenanceData{
		PartnerCode: partner.PartnerCIMB,
		MaintenanceState: dto.MaintenanceState{
			All:        true,
			Onboarding: true,
			Purchase:   true,
			Repayment:  true,
		},
	}

	s.mockMaint.EXPECT().
		SetMaintenanceState(gomock.Any(), partner.PartnerCIMB, gomock.Any()).
		Return(errorkit.NewError(errorkit.CodeInternalError, "internal error"))

	// Act
	err := s.usecase.SetMaintenance(ctx, data)

	// Assert
	s.Error(err)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
}

func (s *MaintenanceTestSuite) TestGetMaintenance_Success() {
	// Arrange
	ctx := context.Background()
	partnerCode := partner.PartnerCIMB
	expectedState := maintenance.State{
		All:        true,
		Onboarding: true,
		Purchase:   true,
		Repayment:  true,
	}

	s.mockMaint.EXPECT().
		GetMaintenanceState(gomock.Any(), partnerCode).
		Return(expectedState, nil)

	// Act
	result, err := s.usecase.GetMaintenance(ctx, partnerCode)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(partnerCode, result.PartnerCode)
	s.Equal(expectedState.All, result.MaintenanceState.All)
	s.Equal(expectedState.Onboarding, result.MaintenanceState.Onboarding)
	s.Equal(expectedState.Purchase, result.MaintenanceState.Purchase)
	s.Equal(expectedState.Repayment, result.MaintenanceState.Repayment)
}

func (s *MaintenanceTestSuite) TestGetMaintenance_Error() {
	// Arrange
	ctx := context.Background()
	partnerCode := partner.PartnerCIMB

	s.mockMaint.EXPECT().
		GetMaintenanceState(gomock.Any(), partnerCode).
		Return(maintenance.State{}, errorkit.NewError(errorkit.CodeInternalError, "internal error"))

	// Act
	result, err := s.usecase.GetMaintenance(ctx, partnerCode)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
}
