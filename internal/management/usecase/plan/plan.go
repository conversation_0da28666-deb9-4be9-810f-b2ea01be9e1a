package plan

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

const (
	planKeyPattern   = "%d|%d|%s|%d"
	planMinEmiAmount = 150_000
)

type BasePlanConfig struct {
	PlanID         int64
	TenorNumber    int64
	IsActive       bool
	MinOrderAmount int64
	IsPopular      bool
}

type AppIDPlanConfig struct {
	AppID          int32
	TenorNumber    int64
	IsActive       bool
	MinOrderAmount int64
	IsPopular      bool
}

type Usecase struct {
	logger         *log.Helper
	feeRepo        _interface.FeeRepo
	planRepo       _interface.PlanRepo
	cimbService    _interface.CIMBService
	accountService _interface.AccountService
	deeplinks      utils.DeepLinkCraft
}

func NewUsecase(
	feeRepo _interface.FeeRepo,
	planRepo _interface.PlanRepo,
	cimbService _interface.CIMBService,
	accountService _interface.AccountService,
	deeplinks utils.DeepLinkCraft,
	kLogger log.Logger) *Usecase {
	logger := log.With(kLogger, "usecase", "plan")
	return &Usecase{
		feeRepo:        feeRepo,
		planRepo:       planRepo,
		deeplinks:      deeplinks,
		cimbService:    cimbService,
		accountService: accountService,
		logger:         log.NewHelper(logger),
	}
}

var cimbPlanConfigs = []BasePlanConfig{
	{PlanID: 1, TenorNumber: 1, IsActive: false},
	{PlanID: 2, TenorNumber: 2, IsActive: false},
	{PlanID: 3, TenorNumber: 3, IsActive: true, IsPopular: true},
	{PlanID: 4, TenorNumber: 4, IsActive: false},
	{PlanID: 5, TenorNumber: 5, IsActive: false},
	{PlanID: 6, TenorNumber: 6, IsActive: true},
	{PlanID: 7, TenorNumber: 7, IsActive: false},
	{PlanID: 8, TenorNumber: 8, IsActive: false},
	{PlanID: 9, TenorNumber: 9, IsActive: true},
	{PlanID: 10, TenorNumber: 10, IsActive: false},
	{PlanID: 11, TenorNumber: 11, IsActive: false},
	{PlanID: 12, TenorNumber: 12, IsActive: true},
}

var cimbPlanConfigsMap = zutils.KeyBy(cimbPlanConfigs, func(config BasePlanConfig) int64 {
	return config.TenorNumber
})

var appIDPlanConfigs = []AppIDPlanConfig{
	{AppID: -1, TenorNumber: 3, IsActive: true, IsPopular: true},
	{AppID: -1, TenorNumber: 6, IsActive: true},
	{AppID: -1, TenorNumber: 9, IsActive: true},
}

var appIDPlanConfigsGroup = zutils.GroupByProperty(appIDPlanConfigs, func(config AppIDPlanConfig) int32 {
	return config.AppID
})

func (planUc *Usecase) generatePlanKey(ctx context.Context,
	zalopayID int64, tenure int64, orderInfo model.OrderRequest) string {
	appID := orderInfo.AppID
	appTransID := orderInfo.AppTransID
	keyString := fmt.Sprintf(planKeyPattern, zalopayID, appID, appTransID, tenure)
	return base64.RawURLEncoding.EncodeToString([]byte(keyString))
}

func (planUc *Usecase) reversePlanKey(ctx context.Context, planKey string) model.PlanKeyData {
	var zalopayID int64
	var appID int32
	var tenure int64
	var appTransID string

	if planKey == "" {
		return model.PlanKeyData{}
	}

	keyDecoded, err := base64.RawURLEncoding.DecodeString(planKey)
	if err != nil {
		planUc.logger.WithContext(ctx).Errorf("Failed to decode plan key, %v", err)
		return model.PlanKeyData{}
	}

	keyString := strings.ReplaceAll(string(keyDecoded), "|", " ")
	keyPattern := strings.ReplaceAll(planKeyPattern, "|", " ")

	_, err = fmt.Sscanf(keyString, keyPattern, &zalopayID, &appID, &appTransID, &tenure)
	if err != nil {
		planUc.logger.WithContext(ctx).Errorf("Failed to parse plan key, %v", err)
		return model.PlanKeyData{}
	}
	return model.PlanKeyData{
		PlanKey:    planKey,
		AppID:      appID,
		Tenure:     tenure,
		ZalopayID:  zalopayID,
		AppTransID: appTransID,
	}
}
