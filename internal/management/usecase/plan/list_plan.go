package plan

import (
	"context"
	"slices"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (planUc *Usecase) ListPlan(ctx context.Context, req dto.ListPlanParams) (*dto.ListPlanResult, error) {
	logger := planUc.logger.WithContext(ctx)

	logger.Infow("msg", "get list plans", "req", req)

	userAcc, err := planUc.accountService.GetActiveAccountByPartner(ctx, req.ZalopayID, req.PartnerCode)
	if err != nil {
		logger.Errorf("get active account by partner failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get and validate account failed").
			WithCause(err).WithKind(errorkit.TypePrecondition)
	}

	orderInfo := model.OrderRequest{
		AppID:        req.AppID,
		AppTransID:   req.AppTransID,
		ChargeAmount: req.OrderAmount,
	}

	plansOpts, err := planUc.retrievePlanOptions(ctx, userAcc, orderInfo)
	if err != nil {
		logger.Errorf("retrieve plans failed: %v", err)
		return nil, err
	}

	planItem, err := planUc.planRepo.RetrievePlanSelected(ctx, req.ZalopayID, orderInfo)
	if errors.Is(err, model.ErrPlanItemNotFound) || errors.Is(err, model.ErrPlanItemIsEmpty) {
		logger.Infow("msg", "plan is not selected by client", "order_info", orderInfo)
	}
	if findPlanByPlanKey(plansOpts, planItem.GetPlanKey()) == nil {
		planItem = &model.Plan{}
	}

	logger.Infow(
		"msg", "get list plans success",
		"plan_opt_len", len(plansOpts),
		"plan_selected", planItem.GetPlanKey(),
	)

	return &dto.ListPlanResult{
		PlanOptions:     plansOpts,
		PlanSelected:    planItem,
		PlanKeySelected: planItem.GetPlanKey(),
	}, nil
}

func (planUc *Usecase) retrievePlanOptions(ctx context.Context,
	userAcc *model.Account, orderInfo model.OrderRequest) ([]*model.Plan, error) {
	zalopayID := userAcc.ZalopayID
	logger := planUc.logger.WithContext(ctx)

	planData, err := planUc.planRepo.RetrieveFullPlanInfo(ctx, zalopayID, orderInfo)
	if err == nil && !isPlanDataDirty(planData, orderInfo.ChargeAmount) {
		return planData.PlanOptions, nil
	}

	planOpts, err := planUc.fetchPlanOptions(ctx, zalopayID, orderInfo)
	if errors.Is(err, model.ErrPlanOptionsNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodePlanOptsNotFound, "no plan options found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("fetch plan options failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "fetch plan options failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	if err = planUc.planRepo.PersistPlanOptions(ctx, zalopayID, orderInfo, planOpts); err != nil {
		logger.Warnw("msg", "set plan options to cache failed", "order", orderInfo)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "set plan options to cache failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return planOpts, nil
}

func (planUc *Usecase) fetchPlanOptions(
	ctx context.Context, zalopayID int64,
	orderInfo model.OrderRequest) ([]*model.Plan, error) {
	logger := planUc.logger.WithContext(ctx)
	orderAmount := orderInfo.ChargeAmount
	planOpts, err := planUc.cimbService.QueryPlanOptions(ctx, zalopayID, orderAmount)
	if err != nil {
		logger.Errorf("query plan options failed: %v", err)
		return nil, errors.Wrap(err, "query plan options failed")
	}
	if len(planOpts) == 0 {
		logger.Warn("no plan options found")
		return nil, model.ErrPlanOptionsNotFound
	}

	localFees := planUc.feeRepo.EvaluateFeesInfo(ctx, orderAmount)

	plansResult := make([]*model.Plan, 0)
	for _, planOpt := range planOpts {
		planFees := planUc.evaluatePlanFees(planOpt, localFees)
		planItem := planUc.buildPlanItem(ctx, planOpt, planFees, orderInfo, zalopayID)
		plansResult = append(plansResult, planItem)
	}

	return modifyPlansWithRules(plansResult, orderInfo), nil
}

func (planUc *Usecase) buildPlanItem(
	ctx context.Context,
	planOpt *model.PartnerPlanOption,
	planFees []*model.FeeDetail,
	orderInfo model.OrderRequest,
	zalopayID int64) *model.Plan {
	emiAmt := planOpt.EmiAmount
	tenure := planOpt.TenorNumber
	orderAmt := orderInfo.ChargeAmount
	schedules := planOpt.RepaymentSchedule
	planItem := model.NewPlan(tenure, orderAmt)
	costInfo := makeCostInfo(planOpt, planFees)
	planInfo := makePlanInfo(costInfo, emiAmt, orderAmt)
	planKey := planUc.generatePlanKey(ctx, zalopayID, tenure, orderInfo)
	planUrls := planUc.deeplinks.GetPlanDetailLink(planKey)
	planRepays := buildScheduledRepayments(planInfo, costInfo, schedules)

	planItem.PlanKey = planKey
	planItem.SetCostInfo(costInfo)
	planItem.SetPlanInfo(planInfo)
	planItem.SetOrderInfo(orderInfo)
	planItem.SetPlanRepayments(planRepays)
	planItem.OtherInfo.SetPlanDetailUrl(planUrls)

	return planItem
}

func makeCostInfo(plan *model.PartnerPlanOption, fees []*model.FeeDetail) model.CostInfo {
	costInfo := model.CostInfo{}
	totalFee := int64(0)
	listFees := make([]model.FeeDetail, 0, len(fees))

	for _, fee := range fees {
		switch fee.Type {
		case model.PlatformFee:
			costInfo.PlatformFeeAmount = fee.Amount
		case model.ConversionFee:
			costInfo.ConversionFeeAmount = fee.Amount
		}

		listFees = append(listFees, *fee)
		totalFee += fee.Amount
	}

	costInfo.ListFeeDetails = listFees
	costInfo.TotalFeeAmount = totalFee
	costInfo.InterestAmount = plan.InterestAmount
	costInfo.RbiInterestRate = plan.RbiInterestRate
	costInfo.FlatInterestRate = plan.FlatInterestRate
	costInfo.TotalCostAmount = totalFee + costInfo.InterestAmount

	return costInfo
}

func makePlanInfo(costInfo model.CostInfo, emiAmount, orderAmount int64) model.PlanInfo {
	disburseAmount := orderAmount
	totalDueAmount := orderAmount + costInfo.TotalCostAmount
	return model.PlanInfo{
		EmiAmount:       emiAmount,
		TotalAmount:     totalDueAmount,
		PrincipalAmount: disburseAmount,
	}
}

func buildScheduledRepayments(
	planInfo model.PlanInfo, costInfo model.CostInfo,
	planRepays []model.PartnerRepaySchedule) []*model.PlanRepayment {
	lenRepay := len(planRepays)
	listRepay := make([]*model.PlanRepayment, 0, lenRepay)

	for _, r := range planRepays {
		/**
		 * IMPORTANT: Please do not remove this block code
		 * Currently it is not used, but it will be used in the future
		 */
		//tenureDec := decimal.NewFromInt(int64(lenRepay))
		//totalAmountDec := decimal.NewFromInt(planInfo.TotalAmount)
		//emiAmountDec := totalAmountDec.Div(tenureDec).Ceil()
		//
		//if i+1 == lenRepay {
		//	addedTenure := decimal.NewFromInt(int64(i))
		//	addedAmount := emiAmountDec.Mul(addedTenure)
		//	emiAmountDec = totalAmountDec.Sub(addedAmount)
		//}
		//
		//emiAmount := emiAmountDec.IntPart()

		listRepay = append(listRepay, &model.PlanRepayment{
			Amount:  r.EmiAmount,
			InstNum: r.InstNo,
			DueDate: r.EndDate.Format("02/01/2006"),
		})
	}

	return listRepay
}

func isPlanDataDirty(planData *model.PlanPersist, latestOrderAmount int64) bool {
	return planData == nil || len(planData.PlanOptions) == 0 || planData.OrderAmount != latestOrderAmount
}

func isTransformByAppID(appID int32) bool {
	_, hasFilterBaseAppID := appIDPlanConfigsGroup[appID]
	return hasFilterBaseAppID
}

func modifyPlansWithRules(plans []*model.Plan, orderInfo model.OrderRequest) []*model.Plan {
	result := slices.Clone(plans)

	// Filter and transform plans by rule configs
	if isTransformByAppID(orderInfo.AppID) {
		result = modifyPlansByAppID(plans, orderInfo.AppID)
	} else {
		result = modifyPlansGeneric(plans)
	}

	return result
}

func modifyPlansGeneric(plans []*model.Plan) []*model.Plan {
	var result []*model.Plan

	for _, plan := range plans {
		planConf, ok := cimbPlanConfigsMap[plan.Tenure]
		if !ok || !planConf.IsActive {
			continue
		}
		//if !plan.IsEmiInRange(planMinEmiAmount) {
		//	continue
		//}
		if planConf.IsPopular {
			plan.OtherInfo.SetIsPopular(true)
		}
		result = append(result, plan)
	}

	return result
}

func modifyPlansByAppID(plans []*model.Plan, appID int32) []*model.Plan {
	var result []*model.Plan

	planConfigs := appIDPlanConfigsGroup[appID]
	planConfigsMap := zutils.KeyBy(planConfigs, func(config AppIDPlanConfig) int64 { return config.TenorNumber })

	for _, plan := range plans {
		planConf, ok := planConfigsMap[plan.Tenure]
		if !ok || !planConf.IsActive {
			continue
		}
		//if !plan.IsEmiInRange(planMinEmiAmount) {
		//	continue
		//}
		if planConf.IsPopular {
			plan.OtherInfo.SetIsPopular(true)
		}
		result = append(result, plan)
	}

	return result
}
