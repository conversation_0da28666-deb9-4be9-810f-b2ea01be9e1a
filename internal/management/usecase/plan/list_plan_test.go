package plan

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *PlanTestSuite) TestListPlan_Success() {
	req := dto.ListPlanParams{
		ZalopayID:   123,
		AppID:       1,
		AppTransID:  "trans_1",
		OrderAmount: 1000000,
		PartnerCode: partner.PartnerCIMB,
	}

	account := &model.Account{
		ZalopayID: 123,
	}
	s.mockAccountService.EXPECT().
		GetActiveAccountByPartner(gomock.Any(), int64(123), req.PartnerCode).
		Return(account, nil)

	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "trans_1",
		ChargeAmount: 1000000,
	}

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), int64(123), orderInfo).
		Return(nil, model.ErrPlanItemNotFound)

	planOpts := []*model.PartnerPlanOption{
		{TenorNumber: 3},
		{TenorNumber: 6},
	}
	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), int64(123), req.OrderAmount).
		Return(planOpts, nil)

	s.mockFeeRepo.EXPECT().
		EvaluateFeesInfo(gomock.Any(), req.OrderAmount).
		Return([]*model.FeeDetail{})

	s.mockPlanRepo.EXPECT().
		PersistPlanOptions(gomock.Any(), int64(123), orderInfo, gomock.Any()).
		Return(nil)

	s.mockPlanRepo.EXPECT().
		RetrievePlanSelected(gomock.Any(), int64(123), orderInfo).
		Return(&model.Plan{}, model.ErrPlanItemNotFound)

	result, err := s.usecase.ListPlan(context.Background(), req)
	s.NoError(err)
	s.NotNil(result)
	s.Equal(len(result.PlanOptions), 2)
	s.Equal(result.PlanSelected.GetPlanKey(), "")
	s.Equal(result.PlanKeySelected, "")
}

func (s *PlanTestSuite) TestListPlan_FailedGetActiveAccount() {
	req := dto.ListPlanParams{
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
	}

	s.mockAccountService.EXPECT().
		GetActiveAccountByPartner(gomock.Any(), int64(123), req.PartnerCode).
		Return(nil, errors.New("account not found"))

	result, err := s.usecase.ListPlan(context.Background(), req)
	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeRepositoryError, "get and validate account failed").Error(),
		err.Error(),
	)
	s.Nil(result)
}

func (s *PlanTestSuite) TestListPlan_FailedQueryPlanOptions() {
	req := dto.ListPlanParams{
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
		OrderAmount: 1000000,
	}

	account := &model.Account{
		ZalopayID: 123,
	}
	s.mockAccountService.EXPECT().
		GetActiveAccountByPartner(gomock.Any(), int64(123), req.PartnerCode).
		Return(account, nil)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), int64(123), gomock.Any()).
		Return(nil, errors.New("failed to retrieve plan selected"))

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), int64(123), int64(1000000)).
		Return(nil, errors.New("failed to query plans"))

	result, err := s.usecase.ListPlan(context.Background(), req)
	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeCallCIMBFailed, "fetch plan options failed").Error(),
		err.Error(),
	)
	s.Nil(result)
}

func (s *PlanTestSuite) TestRetrievePlanOptions_FailedPersistPlanOptions() {
	account := &model.Account{
		ZalopayID: 123,
	}
	orderInfo := model.OrderRequest{
		ChargeAmount: 1000000,
	}

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), int64(123), orderInfo).
		Return(nil, errors.New("failed to retrieve plan info"))

	partnerPlanOpts := []*model.PartnerPlanOption{
		{
			TenorNumber:      3,
			EmiAmount:        1000000,
			RbiInterestRate:  0.52,
			FlatInterestRate: 0.01,
			InterestAmount:   10000,
			TotalAmount:      1010000,
		},
		{
			TenorNumber:      6,
			EmiAmount:        1000000,
			RbiInterestRate:  0.98,
			FlatInterestRate: 0.01,
			InterestAmount:   10000,
			TotalAmount:      1010000,
		},
	}
	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), int64(123), int64(1000000)).
		Return(partnerPlanOpts, nil)

	s.mockFeeRepo.EXPECT().
		EvaluateFeesInfo(gomock.Any(), int64(1000000)).
		Return([]*model.FeeDetail{})

	s.mockPlanRepo.EXPECT().
		PersistPlanOptions(gomock.Any(), int64(123), orderInfo, gomock.Any()).
		Return(errors.New("failed to persist"))

	result, err := s.usecase.retrievePlanOptions(context.Background(), account, orderInfo)
	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeRepositoryError, "set plan options to cache failed").Error(),
		err.Error(),
	)
	s.Nil(result)
}

func (s *PlanTestSuite) TestRetrievePlanOptions_EmptyPlanOptions() {
	account := &model.Account{
		ZalopayID: 123,
	}
	orderInfo := model.OrderRequest{
		ChargeAmount: 1000000,
	}

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), int64(123), orderInfo).
		Return(nil, errors.New("failed to retrieve plan info"))

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), int64(123), int64(1000000)).
		Return([]*model.PartnerPlanOption{}, nil)

	result, err := s.usecase.retrievePlanOptions(context.Background(), account, orderInfo)
	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodePlanOptsNotFound, "no plan options found").Error(),
		err.Error(),
	)
	s.Nil(result)
}

func (s *PlanTestSuite) TestRetrievePlanOptions_FailedQueryPlanOptions() {
	account := &model.Account{
		ZalopayID: 123,
	}
	orderInfo := model.OrderRequest{
		ChargeAmount: 1000000,
	}

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), int64(123), orderInfo).
		Return(nil, errors.New("failed to retrieve plan info"))

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), int64(123), int64(1000000)).
		Return(nil, errors.New("failed to query plans"))

	result, err := s.usecase.retrievePlanOptions(context.Background(), account, orderInfo)
	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeCallCIMBFailed, "fetch plan options failed").Error(),
		err.Error(),
	)
	s.Nil(result)
}

func (s *PlanTestSuite) TestBuildScheduledRepayments() {
	planInfo := model.PlanInfo{
		EmiAmount:       100000,
		TotalAmount:     300000,
		PrincipalAmount: 250000,
	}

	costInfo := model.CostInfo{
		PlatformFeeAmount:   20000,
		ConversionFeeAmount: 10000,
		InterestAmount:      20000,
		RbiInterestRate:     12.5,
		FlatInterestRate:    10.0,
		TotalFeeAmount:      30000,
		TotalCostAmount:     50000,
	}

	planRepays := []model.PartnerRepaySchedule{
		{
			InstNo:    1,
			EmiAmount: 100000,
			EndDate:   time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			InstNo:    2,
			EmiAmount: 100000,
			EndDate:   time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			InstNo:    3,
			EmiAmount: 100000,
			EndDate:   time.Date(2024, 4, 1, 0, 0, 0, 0, time.UTC),
		},
	}

	result := buildScheduledRepayments(planInfo, costInfo, planRepays)

	s.Len(result, 3)
	s.Equal(int64(100000), result[0].Amount)
	s.Equal(int32(1), result[0].InstNum)
	s.Equal("01/02/2024", result[0].DueDate)

	s.Equal(int64(100000), result[1].Amount)
	s.Equal(int32(2), result[1].InstNum)
	s.Equal("01/03/2024", result[1].DueDate)

	s.Equal(int64(100000), result[2].Amount)
	s.Equal(int32(3), result[2].InstNum)
	s.Equal("01/04/2024", result[2].DueDate)
}

func (s *PlanTestSuite) TestModifyPlansByAppID() {
	plans := []*model.Plan{
		{
			Tenure: 3,
			OtherInfo: model.OtherInfo{
				IsPopular: false,
			},
		},
		{
			Tenure: 6,
			OtherInfo: model.OtherInfo{
				IsPopular: false,
			},
		},
		{
			Tenure: 9,
			OtherInfo: model.OtherInfo{
				IsPopular: false,
			},
		},
	}

	appIDPlanConfigsGroup = map[int32][]AppIDPlanConfig{
		1: {
			{TenorNumber: 3, IsActive: true, IsPopular: true},
			{TenorNumber: 6, IsActive: true, IsPopular: false},
			{TenorNumber: 9, IsActive: false, IsPopular: false},
		},
	}

	result := modifyPlansByAppID(plans, 1)

	s.Len(result, 2)
	s.Equal(int64(3), result[0].Tenure)
	s.True(result[0].OtherInfo.IsPopular)
	s.Equal(int64(6), result[1].Tenure)
	s.False(result[1].OtherInfo.IsPopular)
}
