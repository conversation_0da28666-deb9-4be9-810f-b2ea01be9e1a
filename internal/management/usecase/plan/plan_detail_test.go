package plan

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

func (s *PlanTestSuite) TestGetPlanDetailByPlanKey_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, orderInfo).
		Return([]*model.Plan{
			{
				PlanKey: planKey,
				Status:  model.PlanStatusActive,
			},
		}, nil)

	// Act
	result, err := s.usecase.GetPlanDetailByPlanKey(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(planKey, result.Plan.PlanKey)
}

func (s *PlanTestSuite) TestGetPlanDetailByPlanKey_InvalidPlanKey() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)

	// Act
	result, err := s.usecase.GetPlanDetailByPlanKey(ctx, &dto.GetPlanParams{
		PlanKey:   "invalid_key",
		ZalopayID: zalopayID,
	})

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInvalidPlanKey))
}

func (s *PlanTestSuite) TestGetPlanDetailByPlanKey_PlanNotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, orderInfo).
		Return([]*model.Plan{}, model.ErrPlanOptionsNotFound)

	// Act
	result, err := s.usecase.GetPlanDetailByPlanKey(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
	})

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodePlanDataNotFound))
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, *orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), zalopayID, *orderInfo).
		Return(&model.PlanPersist{
			OrderAmount: orderInfo.ChargeAmount,
			PlanSelected: &model.Plan{
				Tenure:   3,
				PlanKey:  planKey,
				Status:   model.PlanStatusActive,
				PlanInfo: model.PlanInfo{EmiAmount: 100000},
			},
			PlanOptions: []*model.Plan{
				{
					PlanKey: planKey,
					Status:  model.PlanStatusActive,
				},
			},
		}, nil)

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(planKey, result.Plan.PlanKey)
	s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_InvalidPlanKey() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   "invalid_key",
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err) // Returns response with error state instead of error
	s.NotNil(result)
	s.Equal(model.PlanStatusInvalid, result.Plan.Status)
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_OrderInfoMismatch() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      2, // Different AppID
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	})

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotNil(result.Notice)
}

func (s *PlanTestSuite) TestRefreshPlanData_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)
	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	s.mockPlanRepo.EXPECT().
		PersistFullPlanInfo(gomock.Any(), zalopayID, orderInfo, gomock.Any()).
		Return(nil)

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return([]*model.PartnerPlanOption{
			{
				EmiAmount:        100000,
				TenorNumber:      3,
				RbiInterestRate:  10,
				FlatInterestRate: 10,
				PrincipalAmount:  1000000,
			},
		}, nil)

	s.mockFeeRepo.EXPECT().
		EvaluateFeesInfo(gomock.Any(), int64(1000000)).
		Return([]*model.FeeDetail{})

	// Act
	result, err := s.usecase.refreshPlanData(ctx, planKeyData, orderInfo)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(planKey, result.PlanSelected.PlanKey)
}

func (s *PlanTestSuite) TestRefreshPlanData_PersistError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)
	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return([]*model.PartnerPlanOption{
			{
				EmiAmount:        100000,
				TenorNumber:      3,
				RbiInterestRate:  10,
				FlatInterestRate: 10,
				PrincipalAmount:  1000000,
			},
		}, nil)

	s.mockFeeRepo.EXPECT().
		EvaluateFeesInfo(gomock.Any(), int64(1000000)).
		Return([]*model.FeeDetail{})

	s.mockPlanRepo.EXPECT().
		PersistFullPlanInfo(gomock.Any(), zalopayID, orderInfo, gomock.Any()).
		Return(errors.New("persist error"))

	// Act
	result, err := s.usecase.refreshPlanData(ctx, planKeyData, orderInfo)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
}

func (s *PlanTestSuite) TestRefreshPlanData_QueryPlanOptionsError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)
	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return(nil, errors.New("query error"))

	// Act
	result, err := s.usecase.refreshPlanData(ctx, planKeyData, orderInfo)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
}

func (s *PlanTestSuite) TestRefreshPlanData_EmptyPlanOptions() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)
	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return([]*model.PartnerPlanOption{}, nil)

	// Act
	result, err := s.usecase.refreshPlanData(ctx, planKeyData, orderInfo)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, model.ErrPlanOptionsNotFound)
}

func (s *PlanTestSuite) TestRefreshPlanData_PlanNotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)
	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	// Different tenure to simulate plan not found
	planOpts := []*model.PartnerPlanOption{
		{
			EmiAmount:        100000,
			TenorNumber:      6,
			RbiInterestRate:  10,
			FlatInterestRate: 10,
			PrincipalAmount:  1000000,
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return(planOpts, nil)

	s.mockFeeRepo.EXPECT().
		EvaluateFeesInfo(gomock.Any(), int64(1000000)).
		Return([]*model.FeeDetail{})

	s.mockPlanRepo.EXPECT().
		PersistFullPlanInfo(gomock.Any(), zalopayID, orderInfo, gomock.Any()).
		Return(nil)

	// Act
	result, err := s.usecase.refreshPlanData(ctx, planKeyData, orderInfo)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(orderInfo.ChargeAmount, result.OrderAmount)
	s.Len(result.PlanOptions, 1)
	s.NotNil(result.PlanSelected)
	s.True(result.PlanSelected.IsPlanIneligible())
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_RefreshPlanDataFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), zalopayID, orderInfo).
		Return(&model.PlanPersist{
			OrderAmount:  500000, // Different amount to trigger refresh
			PlanSelected: &model.Plan{PlanKey: planKey},
		}, nil)

	s.mockCIMBSvc.EXPECT().
		QueryPlanOptions(gomock.Any(), zalopayID, orderInfo.ChargeAmount).
		Return(nil, errors.New("query failed"))

	// Act
	result, _ := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: &orderInfo,
	})

	// Assert
	s.NotNil(result)
	s.NotNil(result.Plan)
	s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
	s.Equal(model.CTASelectPlan.String(), result.Action.Title)
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_RetrieveFullPlanInfoFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, *orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), zalopayID, *orderInfo).
		Return(nil, errors.New("database error"))

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err) // Should not return error as per implementation
	s.NotNil(result)
	s.Equal(model.PlanStatusUnknown, result.Plan.Status)
	s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_RetrieveFullPlanInfoInvalid() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, *orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), zalopayID, *orderInfo).
		Return(nil, model.ErrFullPlanInvalid)

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.PlanStatusUnknown, result.Plan.Status)
	s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
	s.Equal(model.CTASelectPlan.String(), result.Action.Title)
	s.Equal(model.MessageScheduleShouldBeUpdate.String(), result.Notice.Text)
}

func (s *PlanTestSuite) TestGetPlanDetailInPayment_PlanKeyNotMatchSelected() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:        1,
		AppTransID:   "test_trans",
		ChargeAmount: 1000000,
	}
	requestPlanKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, *orderInfo)
	selectedPlanKey := s.usecase.generatePlanKey(ctx, zalopayID, 6, *orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrieveFullPlanInfo(gomock.Any(), zalopayID, *orderInfo).
		Return(&model.PlanPersist{
			OrderAmount: orderInfo.ChargeAmount,
			PlanSelected: &model.Plan{
				PlanKey: selectedPlanKey,
			},
		}, nil)

	// Act
	result, err := s.usecase.GetPlanDetailInPayment(ctx, &dto.GetPlanParams{
		PlanKey:   requestPlanKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.PlanStatusInvalid, result.Plan.Status)
	s.Equal(model.CTAType(model.CTATypePlanSelection), result.Action.Type)
	s.Equal(model.CTASelectPlan.String(), result.Action.Title)
}

func (s *PlanTestSuite) TestGetPlanDetailByPlanKey_PlanOptionsEmpty() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, orderInfo).
		Return([]*model.Plan{}, model.ErrPlanOptionsIsEmpty)

	// Act
	result, err := s.usecase.GetPlanDetailByPlanKey(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
	})

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodePlanDataIsEmpty))
}

func (s *PlanTestSuite) TestGetPlanDetailByPlanKey_NetworkError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, orderInfo)

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, orderInfo).
		Return(nil, errors.New("network error"))

	// Act
	result, err := s.usecase.GetPlanDetailByPlanKey(ctx, &dto.GetPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
	})

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
}
