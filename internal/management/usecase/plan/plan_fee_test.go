package plan

import (
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func (s *PlanTestSuite) TestEvaluatePlanFees() {
	testCases := []struct {
		name      string
		planOpt   *model.PartnerPlanOption
		localFees []*model.FeeDetail
		expected  []*model.FeeDetail
	}{
		{
			name:      "Empty fees",
			planOpt:   &model.PartnerPlanOption{},
			localFees: []*model.FeeDetail{},
			expected:  []*model.FeeDetail{},
		},
		{
			name:    "Single platform fee",
			planOpt: &model.PartnerPlanOption{},
			localFees: []*model.FeeDetail{
				{
					Type:   model.PlatformFee,
					Amount: 10000,
				},
			},
			expected: []*model.FeeDetail{
				{
					Type:    model.PlatformFee,
					Amount:  10000,
					Explain: fmt.Sprintf(model.PlatFeeExplain, 10000),
				},
			},
		},
		{
			name:    "Multiple platform fees should be summed",
			planOpt: &model.PartnerPlanOption{},
			localFees: []*model.FeeDetail{
				{
					Type:   model.PlatformFee,
					Amount: 10000,
				},
				{
					Type:   model.PlatformFee,
					Amount: 20000,
				},
			},
			expected: []*model.FeeDetail{
				{
					Type:    model.PlatformFee,
					Amount:  30000,
					Explain: fmt.Sprintf(model.PlatFeeExplain, 30000),
				},
			},
		},
		{
			name:    "Multiple fee types should be ordered by priority",
			planOpt: &model.PartnerPlanOption{},
			localFees: []*model.FeeDetail{
				{
					Type:   model.PlatformFee,
					Amount: 10000,
				},
				{
					Type:   model.ConversionFee,
					Amount: 20000,
				},
			},
			expected: []*model.FeeDetail{
				{
					Type:   model.ConversionFee,
					Amount: 20000,
				},
				{
					Type:    model.PlatformFee,
					Amount:  10000,
					Explain: fmt.Sprintf(model.PlatFeeExplain, 10000),
				},
			},
		},
		{
			name:    "Multiple conversion fees should be summed",
			planOpt: &model.PartnerPlanOption{},
			localFees: []*model.FeeDetail{
				{
					Type:   model.ConversionFee,
					Amount: 10000,
				},
				{
					Type:   model.ConversionFee,
					Amount: 20000,
				},
			},
			expected: []*model.FeeDetail{
				{
					Type:   model.ConversionFee,
					Amount: 30000,
				},
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			result := s.usecase.evaluatePlanFees(tc.planOpt, tc.localFees)
			s.Equal(len(tc.expected), len(result))

			for i := range result {
				s.Equal(tc.expected[i].Type, result[i].Type)
				s.Equal(tc.expected[i].Amount, result[i].Amount)
				s.Equal(tc.expected[i].Explain, result[i].Explain)
			}
		})
	}
}
