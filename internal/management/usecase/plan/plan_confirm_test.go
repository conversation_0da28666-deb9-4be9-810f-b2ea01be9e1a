package plan

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

func (s *PlanTestSuite) TestAcceptPlan_Success() {
	ctx := context.Background()
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(ctx, zalopayID, 3, *orderInfo)

	planKeyData := model.PlanKeyData{
		PlanKey:    planKey,
		Tenure:     3,
		AppID:      1,
		AppTransID: "test_trans",
		ZalopayID:  zalopayID,
	}

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, model.OrderRequest{
			AppID:      planKeyData.AppID,
			AppTransID: planKeyData.AppTransID,
		}).
		Return([]*model.Plan{
			{
				PlanKey: planKey,
				Status:  model.PlanStatusActive,
			},
		}, nil)

	s.mockPlanRepo.EXPECT().
		PersistPlanSelected(gomock.Any(), planKeyData, gomock.Any()).
		Return(nil)

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.NoError(err)
}

func (s *PlanTestSuite) TestAcceptPlan_InvalidPlanKey() {
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   "invalid_plan_key",
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeInvalidPlanKey, "invalid plan key").Error(),
		err.Error(),
	)
}

func (s *PlanTestSuite) TestAcceptPlan_OrderInfoMismatch() {
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      2, // Different AppID
		AppTransID: "test_trans",
	}

	planKey := s.usecase.generatePlanKey(context.Background(), zalopayID, 3, model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	})

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeOrderInfoMismatch, "order info mismatch with plan key data").Error(),
		err.Error(),
	)
}

func (s *PlanTestSuite) TestAcceptPlan_PlanNotFound() {
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(context.Background(), zalopayID, 3, *orderInfo)

	planKeyData := model.PlanKeyData{
		AppID:      1,
		AppTransID: "test_trans",
	}

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, model.OrderRequest{
			AppID:      planKeyData.AppID,
			AppTransID: planKeyData.AppTransID,
		}).
		Return([]*model.Plan{}, nil)

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodePlanDataNotFound, "plan not found").Error(),
		err.Error(),
	)
}

func (s *PlanTestSuite) TestAcceptPlan_PlanNotEligible() {
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(context.Background(), zalopayID, 3, *orderInfo)

	planKeyData := model.PlanKeyData{
		AppID:      1,
		Tenure:     3,
		AppTransID: "test_trans",
	}

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, model.OrderRequest{
			AppID:      planKeyData.AppID,
			AppTransID: planKeyData.AppTransID,
		}).
		Return([]*model.Plan{
			{
				PlanKey: planKey,
				Tenure:  planKeyData.Tenure,
				Status:  model.PlanStatusInactive,
			},
		}, nil)

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodePlanDataNotFound, "plan not found").Error(),
		err.Error(),
	)
}

func (s *PlanTestSuite) TestAcceptPlan_PersistError() {
	zalopayID := int64(123)
	orderInfo := &model.OrderRequest{
		AppID:      1,
		AppTransID: "test_trans",
	}
	planKey := s.usecase.generatePlanKey(context.Background(), zalopayID, 3, *orderInfo)

	planKeyData := model.PlanKeyData{
		AppID:      1,
		AppTransID: "test_trans",
		PlanKey:    planKey,
		Tenure:     3,
		ZalopayID:  zalopayID,
	}

	s.mockPlanRepo.EXPECT().
		RetrievePlanOptions(gomock.Any(), zalopayID, model.OrderRequest{
			AppID:      planKeyData.AppID,
			AppTransID: planKeyData.AppTransID,
		}).
		Return([]*model.Plan{
			{
				PlanKey: planKey,
				Status:  model.PlanStatusActive,
			},
		}, nil)

	s.mockPlanRepo.EXPECT().
		PersistPlanSelected(gomock.Any(), planKeyData, gomock.Any()).
		Return(errors.New("persist error"))

	err := s.usecase.AcceptPlan(context.Background(), &dto.AcceptPlanParams{
		PlanKey:   planKey,
		ZalopayID: zalopayID,
		OrderInfo: orderInfo,
	})

	s.Error(err)
	s.Equal(
		errorkit.NewError(errorkit.CodeRepositoryError, "persist plan selection failed").Error(),
		err.Error(),
	)
}
