package plan

import (
	"fmt"
	"slices"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

// evaluatePlanFees evaluates plan fees based on a partner plan option and local fees
func (planUc *Usecase) evaluatePlanFees(planOpt *model.PartnerPlanOption,
	localFees []*model.FeeDetail) []*model.FeeDetail {
	mergedFees := append(localFees)
	planFeesMap := make(map[model.FeeType]*model.FeeDetail)
	feePriority := map[model.FeeType]int{
		model.ConversionFee: 1,
		model.PlatformFee:   2,
	}

	for _, fee := range mergedFees {
		feeExist, ok := planFeesMap[fee.Type]
		if !ok {
			planFeesMap[fee.Type] = fee
			continue
		}
		if fee.Type == model.PlatformFee {
			feeExist.Amount += fee.Amount
		}
		if fee.Type == model.ConversionFee {
			feeExist.Amount += fee.Amount
		}
	}

	for _, fee := range planFeesMap {
		if fee.Type == model.PlatformFee {
			fee.Explain = fmt.Sprintf(model.PlatFeeExplain, fee.Amount)
		}
	}

	planFees := zutils.Values(planFeesMap)
	slices.SortFunc(planFees, func(i, j *model.FeeDetail) int {
		return feePriority[i.Type] - feePriority[j.Type]
	})

	return planFees
}
