package plan

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	transaction_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	utils_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils/mocks"
	"go.uber.org/mock/gomock"
)

type PlanTestSuite struct {
	suite.Suite
	ctrl               *gomock.Controller
	usecase            *Usecase
	mockTx             *transaction_mocks.MockTransaction
	mockPlanRepo       *repository_mocks.MockPlanRepo
	mockFeeRepo        *repository_mocks.MockFeeRepo
	mockCIMBSvc        *adapter_mocks.MockCIMBService
	mockAccountService *adapter_mocks.MockAccountService
	mockDeeplinks      *utils_mocks.MockDeepLinkCraft
}

func TestPlanSuite(t *testing.T) {
	suite.Run(t, new(PlanTestSuite))
}

func (s *PlanTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockTx = transaction_mocks.NewMockTransaction(s.ctrl)
	s.mockPlanRepo = repository_mocks.NewMockPlanRepo(s.ctrl)
	s.mockFeeRepo = repository_mocks.NewMockFeeRepo(s.ctrl)
	s.mockCIMBSvc = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockAccountService = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockDeeplinks = utils_mocks.NewMockDeepLinkCraft(s.ctrl)
	s.usecase = &Usecase{
		planRepo:       s.mockPlanRepo,
		feeRepo:        s.mockFeeRepo,
		cimbService:    s.mockCIMBSvc,
		accountService: s.mockAccountService,
		deeplinks:      s.mockDeeplinks,
		logger:         log.NewHelper(log.DefaultLogger),
	}
	s.mockDeeplinks.EXPECT().GetPlanDetailLink(gomock.Any()).Return(model.DeepLinkData{
		ZPAUrl:    "https://example.com",
		ZPIUrl:    "https://example.com",
		CommonUrl: "https://example.com",
	}).AnyTimes()
	s.mockDeeplinks.EXPECT().GetPlanDetailMFLink().Return(model.DeepLinkData{
		ZPAUrl:    "https://example.com",
		ZPIUrl:    "https://example.com",
		CommonUrl: "https://example.com",
	}).AnyTimes()
}

func (s *PlanTestSuite) TearDownTest() {
	s.ctrl.Finish()
}
