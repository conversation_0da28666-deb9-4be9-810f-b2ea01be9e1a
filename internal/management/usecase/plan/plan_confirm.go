package plan

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (planUc *Usecase) AcceptPlan(ctx context.Context, req *dto.AcceptPlanParams) error {
	logger := planUc.logger.WithContext(ctx)
	planKeyData := planUc.reversePlanKey(ctx, req.PlanKey)
	if planKeyData.IsEmpty() {
		logger.Errorf("invalid plan key: %s", req.PlanKey)
		return errorkit.
			NewError(errorkit.CodeInvalidPlanKey, "invalid plan key").
			WithKind(errorkit.TypeInvalidArg)
	}
	if !checkPlanKeyWithOrderInfo(planKeyData, req.OrderInfo, req.ZalopayID) {
		logger.Errorf("order info mismatch with plan key data: order=%v, planKey=%v", req.OrderInfo, planKeyData)
		return errorkit.
			NewError(errorkit.CodeOrderInfoMismatch, "order info mismatch with plan key data").
			WithKind(errorkit.TypeValidation)
	}

	// Retrieve plan options
	orderInfo := model.OrderRequest{
		AppID:      planKeyData.AppID,
		AppTransID: planKeyData.AppTransID,
	}
	planOpts, err := planUc.planRepo.RetrievePlanOptions(ctx, req.ZalopayID, orderInfo)
	if err != nil {
		logger.Errorf("retrieve plan options failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "retrieve plan options failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	planResult := findPlanByPlanKey(planOpts, req.PlanKey)
	if planResult == nil {
		logger.Errorf("plan not found, planKey=%s", req.PlanKey)
		return errorkit.
			NewError(errorkit.CodePlanDataNotFound, "plan not found").
			WithKind(errorkit.TypeNotFound)
	}
	if !planResult.IsPlanEligible() {
		logger.Errorf("plan is not eligible to confirm, planKey=%s", req.PlanKey)
		return errorkit.
			NewError(errorkit.CodePlanDataInvalid, "plan is not eligible to confirm").
			WithKind(errorkit.TypePrecondition)
	}

	// Persist plan selection
	if err = planUc.planRepo.PersistPlanSelected(ctx, planKeyData, planResult); err != nil {
		logger.Errorf("persist plan selection failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "persist plan selection failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return nil
}
