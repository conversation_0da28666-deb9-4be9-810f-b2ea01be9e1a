package plan

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (planUc *Usecase) GetPlanDetailByPlanKey(ctx context.Context, req *dto.GetPlanParams) (*dto.GetPlanResult, error) {
	logger := planUc.logger.WithContext(ctx)

	planKeyData := planUc.reversePlanKey(ctx, req.PlanKey)
	if planKeyData.IsEmpty() {
		logger.Errorf("invalid plan key: %v", req.PlanKey)
		return nil, errorkit.
			NewError(errorkit.CodeInvalidPlanKey, "invalid plan key").
			With<PERSON>ind(errorkit.TypeInvalidArg)
	}

	orderInfo := model.OrderRequest{
		AppID:      planKeyData.AppID,
		AppTransID: planKeyData.AppTransID,
	}

	planOpts, err := planUc.planRepo.RetrievePlanOptions(ctx, req.ZalopayID, orderInfo)
	if errors.Is(err, model.ErrPlanOptionsNotFound) {
		logger.Errorf("plan options not found, orderInfo=%v", orderInfo)
		return nil, errorkit.
			NewError(errorkit.CodePlanDataNotFound, "plan options not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if errors.Is(err, model.ErrPlanOptionsIsEmpty) {
		logger.Errorf("plan options is empty, orderInfo=%v", orderInfo)
		return nil, errorkit.
			NewError(errorkit.CodePlanDataIsEmpty, "plan options is empty").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("retrieve plan options failed, orderInfo=%v, error=%v", orderInfo, err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "retrieve plan options failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	planResult := findPlanByPlanKey(planOpts, req.PlanKey)
	if planResult == nil {
		logger.Errorf("plan not found in list, planKey=%v", req.PlanKey)
		return nil, errorkit.
			NewError(errorkit.CodePlanDataNotFound, "plan not found in list").
			WithKind(errorkit.TypeNotFound)
	}

	return &dto.GetPlanResult{Plan: planResult}, nil
}

func (planUc *Usecase) GetPlanDetailInPayment(ctx context.Context,
	req *dto.GetPlanParams) (*dto.GetPlanResult, error) {
	logger := planUc.logger.WithContext(ctx)

	planKeyData := planUc.reversePlanKey(ctx, req.PlanKey)
	if planKeyData.IsEmpty() {
		logger.Errorf("invalid plan key: %v", req.PlanKey)
		err := errorkit.NewError(errorkit.CodeInvalidPlanKey, "invalid plan key")
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}
	if !checkPlanKeyWithOrderInfo(planKeyData, req.OrderInfo, req.ZalopayID) {
		logger.Errorf("order info mismatch with plan key data, order=%+v, planKey=%+v", req.OrderInfo, planKeyData)
		err := errorkit.NewError(errorkit.CodeOrderInfoMismatch, "order info mismatch with plan key data")
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}

	orderInfo := *req.OrderInfo
	planData, err := planUc.planRepo.RetrieveFullPlanInfo(ctx, req.ZalopayID, orderInfo)
	if errors.Is(err, model.ErrFullPlanInvalid) || errors.Is(err, model.ErrPlanItemIsEmpty) {
		logger.Errorf("plan data invalid, order=%v, error=%v", orderInfo, err)
		err = errorkit.NewError(errorkit.CodePlanDataInvalid, "plan not exist or broken")
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}
	if err != nil {
		logger.Errorf("retrieve plan options failed, order=%v, error=%v", orderInfo, err)
		err = errorkit.NewError(errorkit.CodeRepositoryError, "retrieve full plan info failed").WithCause(err)
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}
	if planData.PlanSelected.GetPlanKey() != req.PlanKey {
		logger.Errorf("plan request not same with selected, requestKey=%v, selectedKey=%v", req.PlanKey, planData.PlanSelected.GetPlanKey())
		err = errorkit.NewError(errorkit.CodePlanSelectedNotMatch, "plan request not same with selected")
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}

	if !isPlanDataDirty(planData, orderInfo.ChargeAmount) {
		if planData.PlanSelected.IsPlanEligible() {
			return planUc.buildValidPlanResponse(planData.PlanSelected), nil
		}
		err = errorkit.NewWithCode(errorkit.CodePlanDataNotFound)
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}

	// Handle refresh all information of plan
	planData, err = planUc.refreshPlanData(ctx, planKeyData, orderInfo)
	if err != nil {
		logger.Errorf("refresh plan data failed, planKeyData=%v, error=%v", planKeyData, err)
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}
	if planData.PlanSelected.IsPlanIneligible() || planData.PlanSelected.IsEmptyPlan() {
		logger.Warnf("current plan info not found in new list, planKeyData=%v", planKeyData)
		err = errorkit.NewWithCode(errorkit.CodePlanDataNotFound)
		return planUc.buildInvalidPlanResponse(planKeyData, err), nil
	}

	return planUc.buildValidPlanResponse(planData.PlanSelected), nil
}

func (planUc *Usecase) refreshPlanData(
	ctx context.Context, planKeyData model.PlanKeyData,
	orderInfo model.OrderRequest) (*model.PlanPersist, error) {
	logger := planUc.logger.WithContext(ctx)
	planKey := planKeyData.PlanKey
	zalopayID := planKeyData.ZalopayID

	planOptions, err := planUc.fetchPlanOptions(ctx, zalopayID, orderInfo)
	if err != nil {
		logger.Errorf("fetch plan options for refreshing failed, order=%v, error=%v", orderInfo, err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "fetch plan options failed").
			WithCause(err)
	}

	// If we cannot find the plan tenure data in a new list, we still save the current tenure but data it invalid
	planSelected := findPlanByPlanKey(planOptions, planKey)
	if planSelected == nil {
		planSelected = model.NewInactivePlan(planKeyData.PlanKey, planKeyData.Tenure, 0)
	}

	planInfo := &model.PlanPersist{
		OrderAmount:  orderInfo.ChargeAmount,
		PlanOptions:  planOptions,
		PlanSelected: planSelected,
	}

	if err = planUc.planRepo.PersistFullPlanInfo(ctx, zalopayID, orderInfo, planInfo); err != nil {
		logger.Errorf("persist plan data failed, err=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "persist plan data failed").
			WithCause(err)
	}

	return planInfo, nil
}

func (planUc *Usecase) buildValidPlanResponse(planDetail *model.Plan) *dto.GetPlanResult {
	planCta := &model.Action{
		Type:  model.CTATypePlanSelection,
		Title: model.CTAChangePlan.String(),
	}
	planDesc := &model.Message{
		Text: fmt.Sprintf(
			model.MessageEmiAmountWithTenor.String(), planDetail.Tenure,
			utils.FormatCurrencyKMB(planDetail.PlanInfo.EmiAmount),
		),
	}
	planMFLink := planUc.deeplinks.GetPlanDetailMFLink()
	planMFProp := map[string]any{"plan_key": planDetail.PlanKey}
	planMFInfo := planMFLink.BuildCommonModuleFederation(planMFProp)

	return &dto.GetPlanResult{
		Plan:      planDetail,
		Action:    planCta,
		Message:   planDesc,
		ModuleFed: &planMFInfo,
	}
}

func (planUc *Usecase) buildInvalidPlanResponse(planKeyData model.PlanKeyData, err error) *dto.GetPlanResult {
	planData := model.NewUnknownPlan(planKeyData.PlanKey, planKeyData.Tenure)
	planCta := &model.Action{
		Type:  model.CTATypePlanSelection,
		Title: model.CTASelectPlan.String(),
	}

	var planNotice *model.Message
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodePlanDataNotFound)) ||
		errors.Is(err, errorkit.NewWithCode(errorkit.CodePlanDataIsEmpty)) {
		planData = model.NewInactivePlan(planKeyData.PlanKey, planKeyData.Tenure, 0)
		planNotice = model.NewMessage(model.MessageInvalidInstallmentTerm.String())
		planCta = &model.Action{Type: model.CTATypePlanSelection, Title: model.CTAChangePlan.String()}
	}
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodeInvalidPlanKey)) ||
		errors.Is(err, errorkit.NewWithCode(errorkit.CodeOrderInfoMismatch)) ||
		errors.Is(err, errorkit.NewWithCode(errorkit.CodePlanSelectedNotMatch)) {
		planData = model.NewInvalidPlan(planKeyData.PlanKey, planKeyData.Tenure, 0)
		planNotice = model.NewMessage(model.MessageScheduleShouldBeUpdate.String())
	}

	// Default case: plan should be updated
	if planData.IsPlanUnknown() || planNotice == nil {
		planNotice = model.NewMessage(model.MessageScheduleShouldBeUpdate.String())
	}

	return &dto.GetPlanResult{
		Plan:   planData,
		Action: planCta,
		Notice: planNotice,
	}
}

func checkPlanKeyWithOrderInfo(planKeyData model.PlanKeyData, orderInfo *model.OrderRequest, zalopayID int64) bool {
	return planKeyData.ZalopayID == zalopayID &&
		planKeyData.AppID == orderInfo.AppID &&
		planKeyData.AppTransID == orderInfo.AppTransID
}

func findPlanByPlanKey(plans []*model.Plan, planKey string) *model.Plan {
	for _, plan := range plans {
		if !plan.IsPlanEligible() {
			continue
		}
		if plan.PlanKey == planKey {
			return plan
		}
	}
	return nil
}
