package outstanding

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

type Usecase struct {
	logger   *log.Helper
	stmtRepo _interface.StatementRepo
	instRepo _interface.InstallmentRepo
}

func NewUsecase(
	stmtRepo _interface.StatementRepo,
	instRepo _interface.InstallmentRepo,
	kLogger log.Logger) *Usecase {
	logger := log.With(kLogger, "usecase", "outstanding")
	return &Usecase{
		logger:   log.NewHelper(logger),
		stmtRepo: stmtRepo,
		instRepo: instRepo,
	}
}

func (s *Usecase) GetLoanOutstanding(ctx context.Context, zalopayID, accountID int64) (*model.OutstandingInfo, error) {
	logger := s.logger.WithContext(ctx)
	outstanding := &model.OutstandingInfo{}

	acctInsts, err := s.instRepo.ListFullOpenInstallmentByUserAndAcctID(ctx, zalopayID, accountID)
	if err != nil {
		logger.Errorf("ListFullOpenInstallmentByUserAndAcctID has error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get installment error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if len(acctInsts) == 0 {
		logger.Infof("list open installment by user has no data")
		return outstanding, nil
	}

	outstanding.TotalAmount = (model.Installments(acctInsts)).CalcTotalOutsAmount()

	acctStmt, err := s.stmtRepo.QueryLatestStatement(ctx, zalopayID, accountID)
	if errors.Is(err, model.ErrStatementNotFound) {
		logger.Infof("query latest statement has not found data")
		return outstanding, nil
	}
	if err != nil {
		logger.Errorf("QueryLatestStatement has error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get statement error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	outstanding.DueDetails = acctStmt.ToOutstandingDue()

	return outstanding, nil
}
