package installment

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
)

type Usecase struct {
	logger         *log.Helper
	txn            _interface.Transaction
	distLock       _interface.DistributedLock
	instRepo       _interface.InstallmentRepo
	jobTaskMgmt    _interface.JobTaskMgmt
	cimbService    _interface.CIMBService
	accountService _interface.AccountService
	paymentEvtPub  _interface.PaymentEventPublisher
	redisKeyGen    keygen.RedisKeyGenerator
	dischargeCfg   *config.Management_EarlyDischarge // Store only the relevant early discharge config
}

func NewUsecase(
	txn _interface.Transaction,
	distLock _interface.DistributedLock,
	instRepo _interface.InstallmentRepo,
	jobTaskMgmt _interface.JobTaskMgmt,
	cimbService _interface.CIMBService,
	accountService _interface.AccountService,
	paymentEvtPub _interface.PaymentEventPublisher,
	redisKeyGen keygen.RedisKeyGenerator,
	config *config.Management,
	kLogger log.Logger) *Usecase {
	logger := log.With(kLogger, "usecase", "installment")
	return &Usecase{
		logger:         log.NewHelper(logger),
		txn:            txn,
		distLock:       distLock,
		instRepo:       instRepo,
		redisKeyGen:    redisKeyGen,
		jobTaskMgmt:    jobTaskMgmt,
		cimbService:    cimbService,
		accountService: accountService,
		paymentEvtPub:  paymentEvtPub,
		dischargeCfg:   config.GetEarlyDischarge(), // Only store the early discharge config
	}
}

// Helper function to convert TimeOfDay proto to time.Time
func timeOfDayToTime(tod *config.TimeOfDay) time.Time {
	if tod == nil {
		return time.Time{}
	}

	now := time.Now()
	return time.Date(
		now.Year(), now.Month(), now.Day(),
		int(tod.Hours),
		int(tod.Minutes),
		int(tod.Seconds),
		int(tod.Nanos),
		now.Location(),
	)
}
