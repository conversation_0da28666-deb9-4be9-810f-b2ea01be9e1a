package installment

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

func (s *InstallmentTestSuite) TestTriggerSyncInstallmentDaily_Success() {
	// Arrange
	ctx := context.Background()

	s.mockJobTaskMgmt.EXPECT().
		CanExecuteSyncInstallmentsDailyTask(gomock.Any()).
		Return(true, nil)

	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncInstallmentDailyTask(gomock.Any()).
		Return(nil)

	// Act
	err := s.usecase.TriggerSyncInstallmentDaily(ctx)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestTriggerSyncInstallmentDaily_AlreadyRunning() {
	// Arrange
	ctx := context.Background()

	s.mockJobTaskMgmt.EXPECT().
		CanExecuteSyncInstallmentsDailyTask(gomock.Any()).
		Return(false, nil)

	// Act
	err := s.usecase.TriggerSyncInstallmentDaily(ctx)

	// Assert
	s.Error(err)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeResourceBusy))
}

func (s *InstallmentTestSuite) TestSyncInstallmentDaily_Success() {
	// Arrange
	ctx := context.Background()
	lockKey := "test-lock-key"

	s.mockRedisKeyGen.EXPECT().
		Generate(syncInstDailyLockKey).
		Return(lockKey, nil)

	s.mockDistLock.EXPECT().
		AcquireInstallmentSyncing(gomock.Any(), lockKey).
		Return(nil)

	s.mockDistLock.EXPECT().
		Release(gomock.Any(), lockKey).
		Return(nil)

	s.mockInstRepo.EXPECT().
		ListInstallmentPeriodicSync(gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]*model.InstallmentInfo{
			{ID: 1, ZalopayID: 123},
		}, nil)

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByID(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.PartnerCIMBInst{}, nil).
		Times(1)

	s.mockTxn.EXPECT().
		IsTxActive(gomock.Any()).
		Return(false).
		Times(1)

	s.mockTxn.EXPECT().
		BeginOrReuseTx(gomock.Any()).
		Return(ctx, nil).
		Times(1)

	s.mockInstRepo.EXPECT().
		GetInstallmentByIDForUpdate(gomock.Any(), gomock.Any()).
		Return(&model.InstallmentInfo{}, nil).
		Times(1)

	s.mockInstRepo.EXPECT().
		UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(1)

	s.mockInstRepo.EXPECT().
		UpdateRepayScheduleAfterSync(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	s.mockTxn.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil).
		Times(1)

	// Second call returns empty to stop the loop
	s.mockInstRepo.EXPECT().
		ListInstallmentPeriodicSync(gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]*model.InstallmentInfo{}, nil)

	// Act
	result, err := s.usecase.SyncInstallmentDaily(ctx)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int(1), result.TotalItems)
	s.Equal(int(1), result.TotalSuccess)
	s.Equal(int(0), result.TotalFailed)
}

func (s *InstallmentTestSuite) TestSyncInstallmentsByPartnerInstIDs_Success() {
	// Arrange
	ctx := context.Background()
	partnerIDs := []string{"test-id-1", "test-id-2"}

	s.mockInstRepo.EXPECT().
		ListInstallmentByPartnerIDs(gomock.Any(), partnerIDs).
		Return([]*model.InstallmentInfo{
			{ID: 1, PartnerInstID: "test-id-1"},
			{ID: 2, PartnerInstID: "test-id-2"},
		}, nil)

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByID(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&model.PartnerCIMBInst{}, nil).
		Times(2)

	s.mockTxn.EXPECT().
		IsTxActive(gomock.Any()).
		Return(true).
		Times(2)

	s.mockTxn.EXPECT().
		BeginOrReuseTx(gomock.Any()).
		Return(ctx, nil).
		Times(2)

	s.mockInstRepo.EXPECT().
		GetInstallmentByIDForUpdate(gomock.Any(), gomock.Any()).
		Return(&model.InstallmentInfo{}, nil).
		Times(2)

	s.mockInstRepo.EXPECT().
		UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(2)

	s.mockInstRepo.EXPECT().
		UpdateRepayScheduleAfterSync(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	s.mockTxn.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil)

	// Act
	result, err := s.usecase.SyncInstallmentsByPartnerInstIDs(ctx, partnerIDs)

	// Assert
	s.NoError(err)
	s.Len(result, 2)
}

func (s *InstallmentTestSuite) TestBuildInstallmentOutstanding() {
	// Arrange
	cimbInst := &model.PartnerCIMBInst{
		ID:                      "1",
		Tenure:                  12,
		Status:                  model.PartnerCIMBInstStatusNormal,
		OutstandingOriginal:     1000000,
		OutstandingDueTotal:     1000000,
		OutstandingDuePenalty:   1000000,
		OutstandingDueInterest:  1000000,
		OutstandingDuePrincipal: 1000000,
	}

	// Act
	result := buildInstallmentOutstanding(cimbInst)

	// Assert
	s.NotNil(result)
	s.Equal(cimbInst.OutstandingOriginal, result.OutstandingOriginal)
	s.Equal(cimbInst.OutstandingDueTotal, result.OutstandingDueTotal)
	s.Equal(cimbInst.OutstandingDuePenalty, result.OutstandingDuePenalty)
	s.Equal(cimbInst.OutstandingDueInterest, result.OutstandingDueInterest)
	s.Equal(cimbInst.OutstandingDuePrincipal, result.OutstandingDuePrincipal)
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_Success() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		ID:         1,
		ZPTransID:  123,
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 1,
		TransactionInfo: model.InstallmentTransaction{
			TransID:            123,
			TransDesc:          "test",
			FirstTenureSettled: false,
		},
	}

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	s.mockInstRepo.EXPECT().
		MarkFirstTenureSettled(gomock.Any(), inst.ID).
		Return(nil)

	s.mockPaymentEvtPub.EXPECT().
		PublishPaymentCommission(gomock.Any(), gomock.Any()).
		Return(nil)

	s.mockTxn.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil)

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_AlreadySettled() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		TransactionInfo: model.InstallmentTransaction{
			FirstTenureSettled: true,
		},
	}

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_BeginTxError() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		ID: 123,
		TransactionInfo: model.InstallmentTransaction{
			TransID:            456,
			FirstTenureSettled: false,
		},
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 1,
	}

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, errors.New("begin tx error")).Times(4)

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "failed to track first paid tenure")
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_MarkFirstTenureError() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		ID: 123,
		TransactionInfo: model.InstallmentTransaction{
			TransID:            456,
			FirstTenureSettled: false,
		},
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 1,
	}

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).Times(4)

	s.mockInstRepo.EXPECT().
		MarkFirstTenureSettled(gomock.Any(), inst.ID).
		Return(errors.New("mark first tenure error")).Times(4)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil).
		Times(4)
	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "failed to track first paid tenure")
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_PublishEventError() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		ID: 123,
		TransactionInfo: model.InstallmentTransaction{
			TransID:            456,
			FirstTenureSettled: false,
		},
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 1,
	}

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		Times(4)
	s.mockInstRepo.EXPECT().
		MarkFirstTenureSettled(gomock.Any(), inst.ID).
		Return(nil).
		Times(4)

	s.mockPaymentEvtPub.EXPECT().
		PublishPaymentCommission(gomock.Any(), gomock.Any()).
		Return(errors.New("publish event error")).
		Times(4)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil).
		Times(4)

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "failed to track first paid tenure")
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_CommitTxError() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		ID: 123,
		TransactionInfo: model.InstallmentTransaction{
			TransID:            456,
			FirstTenureSettled: false,
		},
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 1,
	}

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil).
		Times(4)

	s.mockInstRepo.EXPECT().
		MarkFirstTenureSettled(gomock.Any(), inst.ID).
		Return(nil).
		Times(4)

	s.mockPaymentEvtPub.EXPECT().
		PublishPaymentCommission(gomock.Any(), gomock.Any()).
		Return(nil).
		Times(4)

	s.mockTxn.EXPECT().
		CommitTx(gomock.Any()).
		Return(errors.New("commit tx error")).
		Times(4)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil).
		Times(4)

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "failed to track first paid tenure")
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_NilInstallment() {
	// Arrange
	ctx := context.Background()

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, nil)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "installment info is nil")
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_NotActiveInstallment() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		Status:     model.InstallmentStatusClosed,
		PaidTenure: 1,
	}

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestTrackFirstTenureSettled_NoPaidTenure() {
	// Arrange
	ctx := context.Background()
	inst := &model.InstallmentInfo{
		Status:     model.InstallmentStatusOpen,
		PaidTenure: 0,
	}

	// Act
	err := s.usecase.TrackFirstTenureSettled(ctx, inst)

	// Assert
	s.NoError(err)
}
