package installment

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/stretchr/testify/suite"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type UpdateEarlyDischargeTestSuite struct {
	InstallmentTestSuite
}

func TestUpdateEarlyDischargeSuite(t *testing.T) {
	suite.Run(t, new(UpdateEarlyDischargeTestSuite))
}

// TestMarkEarlyDischargeProcessing_Success tests successful case
func (s *UpdateEarlyDischargeTestSuite) TestMarkEarlyDischargeProcessing_Success() {
	ctx := context.Background()
	zpTransID := int64(12345)

	// Create test installment info
	instInfo := createTestInstallmentInfoForUpdate()

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(instInfo, nil)

	s.mockInstRepo.EXPECT().
		UpdateEarlyDischargeStatus(ctx, instInfo.ID, model.DischargeStatusProcessing).
		Return(nil)

	// Execute
	err := s.usecase.MarkEarlyDischargeProcessing(ctx, zpTransID)

	// Assertions
	s.NoError(err)
}

// TestMarkEarlyDischargeProcessing_GetInstallmentError tests error from GetInstallmentByZPTransID
func (s *UpdateEarlyDischargeTestSuite) TestMarkEarlyDischargeProcessing_GetInstallmentError() {
	ctx := context.Background()
	zpTransID := int64(12345)

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(nil, errors.NotFound(errorkit.CodeDataNotFound.String(), "installment not found"))

	// Execute
	err := s.usecase.MarkEarlyDischargeProcessing(ctx, zpTransID)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "installment not found")
	s.Contains(err.Error(), errorkit.CodeRepositoryError.String())
}

// TestMarkEarlyDischargeProcessing_UpdateStatusError tests error from UpdateEarlyDischargeStatus
func (s *UpdateEarlyDischargeTestSuite) TestMarkEarlyDischargeProcessing_UpdateStatusError() {
	ctx := context.Background()
	zpTransID := int64(12345)

	// Create test installment info
	instInfo := createTestInstallmentInfoForUpdate()

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(instInfo, nil)

	s.mockInstRepo.EXPECT().
		UpdateEarlyDischargeStatus(ctx, instInfo.ID, model.DischargeStatusProcessing).
		Return(errors.InternalServer(errorkit.CodeInternalError.String(), "database error"))

	// Execute
	err := s.usecase.MarkEarlyDischargeProcessing(ctx, zpTransID)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "update early discharge status failed")
	s.Contains(err.Error(), errorkit.CodeRepositoryError.String())
}

// TestHandleEarlyDischargeByRefundSettleResult_Success_Complete tests successful case with complete status
func (s *UpdateEarlyDischargeTestSuite) TestHandleEarlyDischargeByRefundSettleResult_Success_Complete() {
	ctx := context.Background()
	zpTransID := int64(12345)
	status := model.DischargeStatusComplete

	// Create test installment info
	instInfo := createTestInstallmentInfoForUpdate()

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(instInfo, nil)

	s.mockInstRepo.EXPECT().
		UpdateEarlyDischargeStatus(ctx, instInfo.ID, status).
		Return(nil)

	// Execute
	err := s.usecase.HandleEarlyDischargeByRefundSettleResult(ctx, zpTransID, status)

	// Assertions
	s.NoError(err)
}

// TestHandleEarlyDischargeByRefundSettleResult_Success_NonComplete tests successful case with non-complete status
func (s *UpdateEarlyDischargeTestSuite) TestHandleEarlyDischargeByRefundSettleResult_Success_NonComplete() {
	ctx := context.Background()
	zpTransID := int64(12345)
	status := model.DischargeStatusProcessing

	// Create test installment info
	instInfo := createTestInstallmentInfoForUpdate()

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(instInfo, nil)

	s.mockInstRepo.EXPECT().
		UpdateEarlyDischargeStatus(ctx, instInfo.ID, status).
		Return(nil)

	// Execute
	err := s.usecase.HandleEarlyDischargeByRefundSettleResult(ctx, zpTransID, status)

	// Assertions
	s.NoError(err)
}

// TestHandleEarlyDischargeByRefundSettleResult_InvalidStatus tests error with unknown status
func (s *UpdateEarlyDischargeTestSuite) TestHandleEarlyDischargeByRefundSettleResult_InvalidStatus() {
	ctx := context.Background()
	zpTransID := int64(12345)
	status := model.DischargeStatusUnknown

	// Execute (no mock setup needed as validation should fail first)
	err := s.usecase.HandleEarlyDischargeByRefundSettleResult(ctx, zpTransID, status)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "invalid status")
	s.Contains(err.Error(), errorkit.CodeInvalidArgument.String())
}

// TestHandleEarlyDischargeByRefundSettleResult_GetInstallmentError tests error from GetInstallmentByZPTransID
func (s *UpdateEarlyDischargeTestSuite) TestHandleEarlyDischargeByRefundSettleResult_GetInstallmentError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	status := model.DischargeStatusComplete

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(nil, errors.NotFound(errorkit.CodeDataNotFound.String(), "installment not found"))

	// Execute
	err := s.usecase.HandleEarlyDischargeByRefundSettleResult(ctx, zpTransID, status)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "installment not found")
}

// TestHandleEarlyDischargeByRefundSettleResult_UpdateStatusError tests error from UpdateEarlyDischargeStatus
func (s *UpdateEarlyDischargeTestSuite) TestHandleEarlyDischargeByRefundSettleResult_UpdateStatusError() {
	ctx := context.Background()
	zpTransID := int64(12345)
	status := model.DischargeStatusComplete

	// Create test installment info
	instInfo := createTestInstallmentInfoForUpdate()

	// Setup mock expectations
	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(ctx, zpTransID).
		Return(instInfo, nil)

	s.mockInstRepo.EXPECT().
		UpdateEarlyDischargeStatus(ctx, instInfo.ID, status).
		Return(errors.InternalServer(errorkit.CodeInternalError.String(), "database error"))

	// Execute
	err := s.usecase.HandleEarlyDischargeByRefundSettleResult(ctx, zpTransID, status)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "update early discharge status failed")
	s.Contains(err.Error(), errorkit.CodeRepositoryError.String())
}

// Helper function to create test installment info for update tests
func createTestInstallmentInfoForUpdate() *model.InstallmentInfo {
	return &model.InstallmentInfo{
		ID:              123,
		Tenure:          12,
		Status:          model.InstallmentStatusOpen,
		DischargeStatus: model.DischargeStatusPending,
		ZalopayID:       456789,
		AccountID:       789,
		ZPTransID:       987654,
		PaidTenure:      3,
		PartnerCode:     partner.PartnerCIMB,
		PartnerInstID:   "CIMB-INST-123",
		DisburseAmount:  1000000,
	}
}
