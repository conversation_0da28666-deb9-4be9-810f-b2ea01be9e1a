package installment

import (
	"context"
	"errors"

	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_Success() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)
	instID := int64(789)

	refundData := &model.InstallmentRefund{
		UpdateVersion:     2,
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	existingInst := &model.InstallmentInfo{
		ID:         instID,
		ZPTransID:  zpTransID,
		RefundInfo: nil, // No existing refund info
	}

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(existingInst, nil)

	s.mockInstRepo.EXPECT().
		UpdateInstallmentRefund(gomock.Any(), instID, refundData).
		Return(nil)

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, refundData)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_AlreadyUpdated() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)
	instID := int64(789)

	existingRefundInfo := &model.InstallmentRefund{
		UpdateVersion:     3, // Higher version than incoming data
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	newRefundData := &model.InstallmentRefund{
		UpdateVersion:     2, // Lower version
		NetRefundAmount:   12000,
		UserTopupAmount:   6000,
		TotalRefundAmount: 18000,
		UserTopupRequired: true,
	}

	existingInst := &model.InstallmentInfo{
		ID:         instID,
		ZPTransID:  zpTransID,
		RefundInfo: existingRefundInfo,
	}

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(existingInst, nil)

	// No UpdateInstallmentRefund call expected since version is already higher

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, newRefundData)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_SameVersion() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)
	instID := int64(789)

	existingRefundInfo := &model.InstallmentRefund{
		UpdateVersion:     2, // Same version as incoming data
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	newRefundData := &model.InstallmentRefund{
		UpdateVersion:     2, // Same version
		NetRefundAmount:   12000,
		UserTopupAmount:   6000,
		TotalRefundAmount: 18000,
		UserTopupRequired: true,
	}

	existingInst := &model.InstallmentInfo{
		ID:         instID,
		ZPTransID:  zpTransID,
		RefundInfo: existingRefundInfo,
	}

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(existingInst, nil)

	// No UpdateInstallmentRefund call expected since version is the same

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, newRefundData)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_GetInstallmentError() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)

	refundData := &model.InstallmentRefund{
		UpdateVersion:     2,
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	expectedErr := errors.New("database connection error")

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, expectedErr)

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, refundData)

	// Assert
	s.Error(err)
	s.Equal(expectedErr, err)
}

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_UpdateError() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)
	instID := int64(789)

	refundData := &model.InstallmentRefund{
		UpdateVersion:     2,
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	existingInst := &model.InstallmentInfo{
		ID:         instID,
		ZPTransID:  zpTransID,
		RefundInfo: nil, // No existing refund info
	}

	expectedErr := errors.New("update failed")

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(existingInst, nil)

	s.mockInstRepo.EXPECT().
		UpdateInstallmentRefund(gomock.Any(), instID, refundData).
		Return(expectedErr)

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, refundData)

	// Assert
	s.Error(err)
	s.Equal(expectedErr, err)
}

func (s *InstallmentTestSuite) TestHandleRefundEventNotified_NewerVersion() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(123456)
	instID := int64(789)

	existingRefundInfo := &model.InstallmentRefund{
		UpdateVersion:     1, // Lower version than incoming data
		NetRefundAmount:   10000,
		UserTopupAmount:   5000,
		TotalRefundAmount: 15000,
		UserTopupRequired: true,
	}

	newRefundData := &model.InstallmentRefund{
		UpdateVersion:     2, // Higher version
		NetRefundAmount:   12000,
		UserTopupAmount:   6000,
		TotalRefundAmount: 18000,
		UserTopupRequired: true,
	}

	existingInst := &model.InstallmentInfo{
		ID:         instID,
		ZPTransID:  zpTransID,
		RefundInfo: existingRefundInfo,
	}

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(existingInst, nil)

	s.mockInstRepo.EXPECT().
		UpdateInstallmentRefund(gomock.Any(), instID, newRefundData).
		Return(nil)

	// Act
	err := s.usecase.HandleRefundEventNotified(ctx, zpTransID, newRefundData)

	// Assert
	s.NoError(err)
}
