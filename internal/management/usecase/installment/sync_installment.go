package installment

import (
	"context"
	"sync"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

const (
	batchPeriodicQuery = 256

	/**
	 * Number of worker to sync installment
	 * Should be same with channelPeriodicSize because each worker can consume 1 item from channel
	 */
	workerPeriodicSync  = 16
	channelPeriodicSize = 16

	syncInstDailyLockKey = "installment:syncing:daily"
)

func (s *Usecase) TriggerSyncInstallmentInfo(ctx context.Context, inst *model.InstallmentInfo) error {
	logger := s.logger.WithContext(ctx)
	retryer := retry.NewRetry(3, 5*time.Second, nil)

	err := retryer.Execute(ctx, func() error {
		params := &dto.SyncInstallmentParams{
			InstID:    inst.ID,
			ZPTransID: inst.ZPTransID,
		}
		if err := s.jobTaskMgmt.ExecuteSyncInstallmentTask(ctx, params); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("failed to trigger sync installment: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to trigger sync installment").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	logger.Infof("trigger sync installment success")

	return nil
}

func (s *Usecase) TriggerSyncInstallmentDaily(ctx context.Context) error {
	logger := s.logger.WithContext(ctx)

	canSync, err := s.jobTaskMgmt.CanExecuteSyncInstallmentsDailyTask(ctx)
	if err != nil {
		logger.Errorf("failed to check can sync installment daily: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to check can sync installment daily").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if !canSync {
		logger.Warn("sync installment daily task is already running")
		return errorkit.
			NewError(errorkit.CodeResourceBusy, "sync installment daily task is already running").
			WithKind(errorkit.TypePrecondition)
	}

	if err = s.jobTaskMgmt.ExecuteSyncInstallmentDailyTask(ctx); err != nil {
		logger.Errorf("failed to trigger sync installment daily: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to trigger sync installment daily").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	logger.Infof("trigger sync installment daily success")
	return nil
}

func (s *Usecase) SyncInstallmentDaily(ctx context.Context) (*dto.InstallmentSyncStats, error) {
	logger := s.logger.WithContext(ctx)

	lockKey, err := s.redisKeyGen.Generate(syncInstDailyLockKey)
	if err != nil {
		logger.Errorf("failed to generate lock key: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = s.distLock.AcquireInstallmentSyncing(ctx, lockKey); err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "sync installment daily task is already running").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer s.distLock.Release(ctx, lockKey)

	// Sync installment
	timeNow := time.Now()
	syncTime := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, timeNow.Location())
	waitGroup := sync.WaitGroup{}
	waitGroup.Add(workerPeriodicSync)

	syncStats := &dto.InstallmentSyncStats{}
	listInstCh := s.buildInstallmentsPeriodicSync(ctx, syncTime)
	syncResultCh := make(chan model.InstallmentPeriodicSync, workerPeriodicSync)

	for i := 0; i < workerPeriodicSync; i++ {
		go func() {
			defer waitGroup.Done()
			s.workerSyncInstallment(ctx, listInstCh, syncResultCh)
		}()
	}

	// Wait for all worker to finish
	go func() {
		waitGroup.Wait()
		close(syncResultCh)
	}()

	// Handle data
	for result := range syncResultCh {
		syncStats.TotalItems++
		if result.Error == nil && result.Data != nil {
			syncStats.TotalSuccess++
			logger.Infof("sync installment success: %+v", result.Params)
			continue
		}
		syncStats.TotalFailed++
		syncStats.ListFailedIDs = append(syncStats.ListFailedIDs, result.Params.ID)
		logger.Errorf("sync installment failed: %v", result.Error)
	}

	logger.Infof("sync installment daily done: %+v", syncStats)
	return syncStats, nil
}

func (s *Usecase) buildInstallmentsPeriodicSync(ctx context.Context, cutoffTime time.Time) <-chan *model.InstallmentInfo {
	logger := s.logger.WithContext(ctx)

	queryOffset := 0
	listInstCh := make(chan *model.InstallmentInfo, channelPeriodicSize)

	go func() {
		defer close(listInstCh)
		for {
			paging := &model.Pagination{
				Offset: &queryOffset,
				Limit:  batchPeriodicQuery,
			}
			listInst, err := s.instRepo.ListInstallmentPeriodicSync(ctx, cutoffTime, paging)
			if err != nil {
				logger.Errorf("failed to retrieve installments for periodic sync: %v", err)
				return
			}
			if len(listInst) == 0 {
				break
			}
			for _, inst := range listInst {
				listInstCh <- inst
			}
			queryOffset += batchPeriodicQuery
		}
	}()

	return listInstCh
}

func (s *Usecase) workerSyncInstallment(
	ctx context.Context,
	listInstCh <-chan *model.InstallmentInfo,
	syncResultCh chan<- model.InstallmentPeriodicSync) {
	retryer := retry.NewRetry(3, 5*time.Second, nil)

	for inst := range listInstCh {
		var err error
		var data *model.InstallmentInfo
		err = retryer.Execute(ctx, func() error {
			data, err = s.SyncInstallmentData(ctx, inst)
			return err
		})
		syncResultCh <- model.InstallmentPeriodicSync{
			Params: inst,
			Data:   data,
			Error:  err,
		}
		newCtx := context.WithoutCancel(ctx)
		go s.TrackFirstTenureSettled(newCtx, inst)
	}
}

func (s *Usecase) SyncInstallmentsByPartnerInstIDs(ctx context.Context, partnerInstIDs []string) ([]*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)
	result := make([]*model.InstallmentInfo, 0, len(partnerInstIDs))

	// Get installment from cimb partner
	insts, err := s.instRepo.ListInstallmentByPartnerIDs(ctx, partnerInstIDs)
	if err != nil {
		logger.Errorf("failed to get installment by partner ids: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to get installment by partner ids").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	// Sync installment
	tCtx, err := s.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to begin transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	defer s.txn.RollbackTx(tCtx)

	for _, inst := range insts {
		if _, err = s.SyncInstallmentData(tCtx, inst); err != nil {
			logger.Errorf("failed to sync installment data: %v", err)
			return nil, err
		}
		result = append(result, inst)
	}

	if err = s.txn.CommitTx(tCtx); err != nil {
		logger.Errorf("failed to commit transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to commit transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return result, nil
}

func (s *Usecase) SyncInstallmentData(ctx context.Context, inst *model.InstallmentInfo) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	// Get installment from cimb partner
	partnerInst, err := s.cimbService.QueryInstallmentByID(ctx, inst.ZalopayID, inst.PartnerInstID)
	if err != nil {
		logger.Errorf("failed to get installment from partner: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to get installment from partner").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	/**
	 * Sync installment data
	 *
	 */
	isTxExist := s.txn.IsTxActive(ctx)
	tCtx, err := s.txn.BeginOrReuseTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to begin transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	defer func() {
		if !isTxExist {
			_ = s.txn.RollbackTx(tCtx)
		}
	}()

	inst, err = s.instRepo.GetInstallmentByIDForUpdate(tCtx, inst.ID)
	if err != nil {
		logger.Errorf("failed to get installment for update: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to get installment for update").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if inst.IsClosed() && inst.IsDischargeDone() {
		return inst, nil
	}

	// Update installment
	inst = inst.
		SyncWithCIMBInstallment(partnerInst).
		SetOutstandingInfo(buildInstallmentOutstanding(partnerInst)).
		SetEarlyDischargeIfExist(buildEarlyDischargeInfo(partnerInst.EarlyDischargeInfo))

	if err = s.instRepo.UpdateInstallmentAfterSync(tCtx, inst); err != nil {
		logger.Errorf("failed to update installment after sync: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to update installment after sync").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	instRepays := buildRepaysSchedule(inst.ID, partnerInst)
	for _, repayItem := range instRepays {
		if err = s.instRepo.UpdateRepayScheduleAfterSync(tCtx, repayItem); err != nil {
			logger.Errorf("failed to create installment repay: %v", err)
			return nil, errorkit.
				NewError(errorkit.CodeRepositoryError, "failed to create installment repay").
				WithCause(err).WithKind(errorkit.TypeRepository)
		}
	}

	result := &model.Installment{
		Info:   inst,
		Repays: instRepays,
	}

	if isTxExist {
		return result.Info, nil
	}

	if err = s.txn.CommitTx(tCtx); err != nil {
		logger.Errorf("failed to commit transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to commit transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return result.Info, nil
}

/**
 * TrackFirstTenureSettled to track the first tenure settled by customer
 * It will validate installment first paid tenure and push event to mark biz commission
 */
func (s *Usecase) TrackFirstTenureSettled(ctx context.Context, inst *model.InstallmentInfo) error {
	logger := s.logger.WithContext(ctx)

	if inst == nil {
		logger.Warn("installment info is nil")
		return errors.New("installment info is nil")
	}
	if inst.TransactionInfo.FirstTenureSettled {
		logger.Infof("installment first tenure already settled, info=%+v", inst)
		return nil
	}
	if !inst.IsOpened() || inst.PaidTenure == 0 {
		logger.Infof("installment is not active or not has first paid tenure, info=%+v", inst)
		return nil
	}

	inst = inst.MarkFirstTenureSettled()
	retryer := retry.NewRetry(3, 5*time.Second, nil)
	err := retryer.Execute(ctx, func() error {
		tCtx, err := s.txn.BeginTx(ctx)
		if err != nil {
			logger.Errorf("failed to begin transaction: %v", err)
			return err
		}
		defer s.txn.RollbackTx(tCtx)

		if err = s.instRepo.MarkFirstTenureSettled(tCtx, inst.ID); err != nil {
			logger.Errorf("failed to mark first paid tenure: %v", err)
			return err
		}

		commissionEvt := dto.NewPaymentCommissionEvent(inst.ZPTransID)
		if err = s.paymentEvtPub.PublishPaymentCommission(ctx, commissionEvt); err != nil {
			logger.Errorf("failed to publish payment commission event: %v", err)
			return err
		}
		logger.Infof("publish payment commission event success, event=%+v", commissionEvt)

		if err = s.txn.CommitTx(tCtx); err != nil {
			logger.Errorf("failed to commit transaction: %v", err)
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("failed to track first paid tenure: %v", err)
		return errors.Wrap(err, "failed to track first paid tenure")
	}
	logger.Infow("msg", "track first paid tenure success", "info", inst)
	return nil
}
