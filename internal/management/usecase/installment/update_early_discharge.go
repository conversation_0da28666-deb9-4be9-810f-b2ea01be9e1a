package installment

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (uc *Usecase) MarkEarlyDischargeProcessing(ctx context.Context, zpTransID int64) error {
	logger := uc.logger.WithContext(ctx)

	// Check if the transaction ID is valid
	inst, err := uc.instRepo.GetInstallmentByZPTransID(ctx, zpTransID)
	if err != nil {
		logger.Errorf("GetInstallmentByZPTransID has error: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "installment not found").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	err = uc.instRepo.UpdateEarlyDischargeStatus(ctx, inst.ID, model.DischargeStatusProcessing)
	if err != nil {
		logger.Errorf("UpdateEarlyDischargeStatus has error: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update early discharge status failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return nil
}

func (uc *Usecase) HandleEarlyDischargeByRefundSettleResult(ctx context.Context,
	zpTransID int64, status model.DischargeStatus) error {
	logger := uc.logger.WithContext(ctx)

	if status == model.DischargeStatusUnknown {
		logger.Errorf("Invalid status: %s", status)
		return errorkit.
			NewError(errorkit.CodeInvalidArgument, "invalid status").
			WithKind(errorkit.TypeBadRequest)
	}

	// Check if the transaction ID is valid
	inst, err := uc.instRepo.GetInstallmentByZPTransID(ctx, zpTransID)
	if err != nil {
		logger.Errorf("GetInstallmentByZPTransID has error: %v", err)
		return err
	}

	err = uc.instRepo.UpdateEarlyDischargeStatus(ctx, inst.ID, status)
	if err != nil {
		logger.Errorf("UpdateEarlyDischargeStatus has error: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update early discharge status failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if status != model.DischargeStatusComplete {
		return nil
	}

	logger.Infof("Early discharge completed for transaction ID: %d", zpTransID)

	return nil
}
