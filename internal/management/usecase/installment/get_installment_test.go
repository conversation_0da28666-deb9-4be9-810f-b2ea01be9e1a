package installment

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"go.uber.org/mock/gomock"
)

func (s *InstallmentTestSuite) TestGetFullInstallment_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentQueryParams{
		ZalopayID: 123,
		ZPTransID: 456,
	}

	expectedInst := &model.Installment{
		Info: &model.InstallmentInfo{
			ZalopayID: params.ZalopayID,
		},
	}

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), params.ZalopayID, params.ZPTransID).
		Return(expectedInst, nil)

	// Act
	result, err := s.usecase.GetFullInstallment(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedInst, result)
}

func (s *InstallmentTestSuite) TestGetFullInstallment_NotFound() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentQueryParams{
		ZalopayID: 123,
		ZPTransID: 456,
	}

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), params.ZalopayID, params.ZPTransID).
		Return(nil, model.ErrInstallmentNotFound)

	// Act
	result, err := s.usecase.GetFullInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInstallmentNotFound))
}

func (s *InstallmentTestSuite) TestGetFullInstallment_RepaysNotFound() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentQueryParams{
		ZalopayID: 123,
		ZPTransID: 456,
	}

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), params.ZalopayID, params.ZPTransID).
		Return(nil, model.ErrInstallmentRepaysNotFound)

	// Act
	result, err := s.usecase.GetFullInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInstallmentDataEmpty))
}

func (s *InstallmentTestSuite) TestGetFullInstallment_RepositoryError() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentQueryParams{
		ZalopayID: 123,
		ZPTransID: 456,
	}

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), params.ZalopayID, params.ZPTransID).
		Return(nil, errorkit.NewError(errorkit.CodeRepositoryError, "repository error"))

	// Act
	result, err := s.usecase.GetFullInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
}

// Tests for GetFullInstallmentByZPTransID
func (s *InstallmentTestSuite) TestGetFullInstallmentByZPTransID_Success() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)

	expectedInst := &model.Installment{
		Info: &model.InstallmentInfo{
			ZPTransID: zpTransID,
			Status:    model.InstallmentStatusOpen,
		},
		Repays: []*model.InstallmentRepay{
			{
				SeqNo:           1,
				EmiAmount:       1000,
				PrincipalAmount: 800,
				InterestAmount:  200,
			},
		},
	}

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(expectedInst, nil)

	// Act
	result, err := s.usecase.GetFullInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedInst, result)
	s.Equal(zpTransID, result.Info.ZPTransID)
	s.Len(result.Repays, 1)
}

func (s *InstallmentTestSuite) TestGetFullInstallmentByZPTransID_NotFound() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, model.ErrInstallmentNotFound)

	// Act
	result, err := s.usecase.GetFullInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInstallmentNotFound))
	s.Contains(err.Error(), "installment not found")
}

func (s *InstallmentTestSuite) TestGetFullInstallmentByZPTransID_RepaysNotFound() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, model.ErrInstallmentRepaysNotFound)

	// Act
	result, err := s.usecase.GetFullInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInstallmentDataEmpty))
	s.Contains(err.Error(), "installment repay schedules not found")
}

func (s *InstallmentTestSuite) TestGetFullInstallmentByZPTransID_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)
	repoError := errors.New("database connection error")

	s.mockInstRepo.EXPECT().
		GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, repoError)

	// Act
	result, err := s.usecase.GetFullInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
	s.Contains(err.Error(), "get installment error")
}

// Tests for GetInstallmentByZPTransID
func (s *InstallmentTestSuite) TestGetInstallmentByZPTransID_Success() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)

	expectedInst := &model.InstallmentInfo{
		ID:        123,
		ZPTransID: zpTransID,
		Status:    model.InstallmentStatusOpen, // Using the correct type
		Tenure:    12,
	}

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(expectedInst, nil)

	// Act
	result, err := s.usecase.GetInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedInst, result)
	s.Equal(zpTransID, result.ZPTransID)
	s.Equal(model.InstallmentStatusOpen, result.Status) // Using the correct type
}

func (s *InstallmentTestSuite) TestGetInstallmentByZPTransID_NotFound() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, model.ErrInstallmentNotFound)

	// Act
	result, err := s.usecase.GetInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInstallmentNotFound))
	s.Contains(err.Error(), "installment not found")
}

func (s *InstallmentTestSuite) TestGetInstallmentByZPTransID_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zpTransID := int64(456)
	repoError := errors.New("database connection error")

	s.mockInstRepo.EXPECT().
		GetInstallmentByZPTransID(gomock.Any(), zpTransID).
		Return(nil, repoError)

	// Act
	result, err := s.usecase.GetInstallmentByZPTransID(ctx, zpTransID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
	s.Contains(err.Error(), "get installment error")
}
