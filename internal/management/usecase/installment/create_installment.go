package installment

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *Usecase) CreateInstallment(ctx context.Context,
	params *dto.InstallmentCreateParams) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	cimbInst, err := s.cimbService.QueryInstallmentByTransID(ctx, params.ZalopayID, params.Transaction.TransID)
	if err != nil {
		logger.Errorf("failed to get installment loan from cimb: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to get installment loan from cimb").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	ctx, err = s.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to begin transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	defer s.txn.RollbackTx(ctx)

	instInfo := buildInstallmentInfo(cimbInst, params)
	instID, err := s.instRepo.CreateInstallment(ctx, instInfo)
	if err != nil {
		logger.Errorf("failed to create installment: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to create installment").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	instRepays := buildRepaysSchedule(instID, cimbInst)
	for _, repayItem := range instRepays {
		err = s.instRepo.CreateRepaymentSchedule(ctx, repayItem)
		if err != nil {
			logger.Errorf("failed to create installment repay: %v", err)
			return nil, errorkit.
				NewError(errorkit.CodeRepositoryError, "failed to create installment repay").
				WithCause(err).WithKind(errorkit.TypeRepository)
		}
	}

	if err = s.txn.CommitTx(ctx); err != nil {
		logger.Errorf("failed to commit transaction: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to commit transaction").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return instInfo, nil
}

func buildInstallmentInfo(
	cimbInst *model.PartnerCIMBInst,
	params *dto.InstallmentCreateParams) *model.InstallmentInfo {
	installmentInfo := model.NewInstallmentInfo(cimbInst.Tenure, cimbInst.DisburseAmount)
	installmentSim := buildInstallmentSimulation(cimbInst, params)
	installmentOuts := buildInstallmentOutstanding(cimbInst)
	transactionInfo := params.Transaction
	return installmentInfo.
		SyncWithCIMBInstallment(cimbInst).
		SetPartner(params.PartnerCode).
		SetPartnerInstID(cimbInst.ID).
		SetSimulationInfo(installmentSim).
		SetOutstandingInfo(installmentOuts).
		AttachUserIdentity(params.ZalopayID, params.AccountID).
		AttachTransactionInfo(transactionInfo.TransID, transactionInfo.ZpTransID, transactionInfo.TransDesc)
}

func buildInstallmentSimulation(
	cimbInst *model.PartnerCIMBInst,
	params *dto.InstallmentCreateParams) *model.InstallmentSimulation {
	installment := params.Installment
	interestAmount := cimbInst.CalcInterestAmount()
	totalAmountDue := cimbInst.CalcTotalAmountDue() + installment.TotalFeeAmount
	return &model.InstallmentSimulation{
		EmiAmount:      installment.EmiAmount,
		InterestRate:   installment.InterestRate,
		TotalFeeAmount: installment.TotalFeeAmount,
		FeeDetails:     installment.FeeDetails,
		TotalAmountDue: totalAmountDue,
		InterestAmount: interestAmount,
	}
}

func buildInstallmentOutstanding(cimbInst *model.PartnerCIMBInst) *model.InstallmentOutstanding {
	return &model.InstallmentOutstanding{
		OutstandingOriginal:     cimbInst.OutstandingOriginal,
		OutstandingDueTotal:     cimbInst.OutstandingDueTotal,
		OutstandingDuePenalty:   cimbInst.OutstandingDuePenalty,
		OutstandingDueInterest:  cimbInst.OutstandingDueInterest,
		OutstandingDuePrincipal: cimbInst.OutstandingDuePrincipal,
	}
}

func buildRepaysSchedule(instID int64, cimbInst *model.PartnerCIMBInst) []*model.InstallmentRepay {
	repays := cimbInst.RepaymentSchedules
	result := make([]*model.InstallmentRepay, 0, len(repays))

	for _, repay := range repays {
		penaltyDetails := &model.InstallmentRepayPenalty{
			PaidPenalty:        repay.PaidPenalty,
			TotalPenalty:       repay.CalcTotalPenaltyAmount(),
			OutstandingPenalty: repay.OutstandingPenalty,
		}
		outstandingDetails := &model.InstallmentRepayOutstanding{
			OutstandingAmount:    repay.OutstandingAmount,
			OutstandingInterest:  repay.OutstandingInterest,
			OutstandingPrincipal: repay.OutstandingPrincipal,
		}
		result = append(result, &model.InstallmentRepay{
			SeqNo:              repay.SeqNo,
			Status:             repay.ToRepaymentStatus(),
			InstallmentID:      instID,
			PartnerInstID:      cimbInst.ID,
			RepayDueDate:       repay.RepayDueDate,
			GraceDueDate:       repay.GraceDueDate,
			EmiAmount:          repay.EmiAmount,
			InterestAmount:     repay.InterestAmount,
			PrincipalAmount:    repay.PrincipalAmount,
			PenaltyDetails:     penaltyDetails,
			OutstandingDetails: outstandingDetails,
		})
	}
	return result
}
