package installment

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_Success() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotNil(result.EarlyDischarge)
	s.NotNil(result.EarlyDischarge.Details)
	s.Equal(earlyDischarge.DischargeData.TotalDischargeAmount, result.EarlyDischarge.Details.TotalDischargeAmount)
	s.Equal(earlyDischarge.DischargeData.EarlyDischargeFee, result.EarlyDischarge.Details.EarlyDischargeFee)
	s.Equal(model.DischargeStatusPending, result.DischargeStatus)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_SuccessWithDischargeComplete() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(true) // HasDischarged = true

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Mock the async SyncInstallmentData call that happens when HasDischarged = true
	s.mockCIMBSvc.EXPECT().QueryInstallmentByID(gomock.Any(), inst.ZalopayID, inst.PartnerInstID).Return(createTestPartnerCIMBInst(), nil).AnyTimes()
	s.mockTxn.EXPECT().IsTxActive(gomock.Any()).Return(false).AnyTimes()
	s.mockTxn.EXPECT().BeginOrReuseTx(gomock.Any()).Return(context.Background(), nil).AnyTimes()
	s.mockTxn.EXPECT().RollbackTx(gomock.Any()).Return(nil).AnyTimes()
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(gomock.Any(), gomock.Any()).Return(inst, nil).AnyTimes()
	s.mockInstRepo.EXPECT().UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockInstRepo.EXPECT().UpdateRepayScheduleAfterSync(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockTxn.EXPECT().CommitTx(gomock.Any()).Return(nil).AnyTimes()

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.DischargeStatusComplete, result.DischargeStatus)
	s.NotNil(result.EarlyDischarge)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_CIMBServiceError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	expectedError := errors.New("CIMB service error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(nil, expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_EarlyDischargeNil() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(nil, nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "early discharge info is nil")
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_TransactionBeginError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)
	expectedError := errors.New("transaction begin error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(nil, expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_GetInstallmentError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)
	expectedError := errors.New("get installment error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(nil, expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_UpdateEarlyDischargeInfoError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)
	expectedError := errors.New("update early discharge info error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_TransactionCommitError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)
	expectedError := errors.New("transaction commit error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}

// Helper functions for creating test data
func createTestInstallmentInfo() *model.InstallmentInfo {
	now := time.Now()
	return &model.InstallmentInfo{
		ID:              123,
		Tenure:          12,
		Status:          model.InstallmentStatusOpen,
		DischargeStatus: model.DischargeStatusPending,
		ZalopayID:       456789,
		AccountID:       789,
		ZPTransID:       987654,
		PaidTenure:      3,
		StartDate:       now.Add(-90 * 24 * time.Hour),
		EndDate:         now.Add(270 * 24 * time.Hour),
		CurrDueDate:     now.Add(30 * 24 * time.Hour),
		NextDueDate:     now.Add(60 * 24 * time.Hour),
		DaysPastDue:     0,
		PartnerCode:     partner.PartnerCIMB,
		PartnerInstID:   "CIMB-INST-123",
		DisburseAmount:  1000000,
		CreatedAt:       now.Add(-90 * 24 * time.Hour),
		UpdatedAt:       now,
	}
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_WithNilDischargeData() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := &model.PartnerEarlyDischarge{
		ID:            "early-discharge-123",
		HasDischarged: false,
		DischargeData: nil, // Nil discharge data
	}

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotNil(result.EarlyDischarge)
	s.Nil(result.EarlyDischarge.Details) // Should be nil when DischargeData is nil
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_WithDischargedButNilData() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := &model.PartnerEarlyDischarge{
		ID:            "early-discharge-123",
		HasDischarged: true, // Discharged but no data
		DischargeData: nil,
	}

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Mock the async SyncInstallmentData call that happens when HasDischarged = true
	// Use a CIMB instance without early discharge info since the test is for nil discharge data
	cimbInstWithoutEarlyDischarge := createTestPartnerCIMBInst()
	cimbInstWithoutEarlyDischarge.EarlyDischargeInfo = nil
	s.mockCIMBSvc.EXPECT().QueryInstallmentByID(gomock.Any(), inst.ZalopayID, inst.PartnerInstID).Return(cimbInstWithoutEarlyDischarge, nil).AnyTimes()
	s.mockTxn.EXPECT().IsTxActive(gomock.Any()).Return(false).AnyTimes()
	s.mockTxn.EXPECT().BeginOrReuseTx(gomock.Any()).Return(context.Background(), nil).AnyTimes()
	s.mockTxn.EXPECT().RollbackTx(gomock.Any()).Return(nil).AnyTimes()
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(gomock.Any(), gomock.Any()).Return(inst, nil).AnyTimes()
	s.mockInstRepo.EXPECT().UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockInstRepo.EXPECT().UpdateRepayScheduleAfterSync(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockTxn.EXPECT().CommitTx(gomock.Any()).Return(nil).AnyTimes()

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.DischargeStatusComplete, result.DischargeStatus)
	s.NotNil(result.EarlyDischarge)
	s.Nil(result.EarlyDischarge.Details) // Should be nil when DischargeData is nil
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_WithExistingEarlyDischarge() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	inst.EarlyDischarge = &model.InstallmentEarlyDischarge{
		Details: &model.EarlyDischargeDetails{
			TotalDischargeAmount: 500000,
			EarlyDischargeFee:    15000,
		},
		SyncedAt: time.Now().Add(-1 * time.Hour),
	}
	earlyDischarge := createTestPartnerEarlyDischarge(false)

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotNil(result.EarlyDischarge)
	s.NotNil(result.EarlyDischarge.Details)
	// Should be updated with new data
	s.Equal(earlyDischarge.DischargeData.TotalDischargeAmount, result.EarlyDischarge.Details.TotalDischargeAmount)
}

func createTestPartnerEarlyDischarge(hasDischarged bool) *model.PartnerEarlyDischarge {
	return &model.PartnerEarlyDischarge{
		ID:            "early-discharge-123",
		HasDischarged: hasDischarged,
		DischargeData: &model.EarlyDischargeData{
			OutstandingPrincipal: zutils.Ptr(int64(800000)),
			OutstandingInterest:  zutils.Ptr(int64(50000)),
			OutstandingPenalty:   zutils.Ptr(int64(10000)),
			EarlyDischargeFee:    25000,
			TotalDischargeAmount: 885000,
		},
	}
}

func createTestPartnerCIMBInst() *model.PartnerCIMBInst {
	now := time.Now()
	return &model.PartnerCIMBInst{
		ID:                      "CIMB-INST-123",
		Tenure:                  12,
		Status:                  model.PartnerCIMBInstStatusActive,
		DueDate:                 now.Add(30 * 24 * time.Hour),
		StartDate:               now.Add(-90 * 24 * time.Hour),
		EndDate:                 now.Add(270 * 24 * time.Hour),
		ReportDate:              now,
		DaysPastDue:             0,
		InterestRate:            12.5,
		DisburseAmount:          1000000,
		CurrRepayDueDate:        now.Add(30 * 24 * time.Hour),
		NextRepayDueDate:        now.Add(60 * 24 * time.Hour),
		OutstandingOriginal:     1000000,
		OutstandingDueTotal:     850000,
		OutstandingDuePrincipal: 800000,
		OutstandingDueInterest:  40000,
		OutstandingDuePenalty:   10000,
		EarlyDischargeInfo:      createTestPartnerEarlyDischarge(false),
		RepaymentSchedules:      []*model.PartnerCIMBInstRepaySchedule{},
	}
}
