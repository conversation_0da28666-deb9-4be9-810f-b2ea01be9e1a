package installment

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/platform/common/retry"
)

func (uc *Usecase) TriggerSyncAccountBalanceAfterDischarged(
	ctx context.Context, zalopayID, accountID int64) error {
	logger := uc.logger.WithContext(ctx)
	retryer := retry.NewRetry(3, time.Second*5, nil)

	err := retryer.Execute(ctx, func() error {
		params := &dto.SyncAccountBalanceAfterDischargeParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}
		if err := uc.jobTaskMgmt.ExecuteSyncAccountBalanceAfterDischargeTask(ctx, params); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		logger.Errorw("msg", "failed to execute sync account balance after statement task",
			"zalopay_id", zalopayID, "account_id", accountID, "error", err,
		)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to execute sync account balance after statement task").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	return nil
}
