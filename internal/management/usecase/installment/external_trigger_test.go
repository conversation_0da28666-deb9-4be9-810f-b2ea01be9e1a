package installment

import (
	"context"
	"errors"

	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *InstallmentTestSuite) TestTriggerSyncAccountBalanceAfterDischarged_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	accountID := int64(456)

	expectedParams := &dto.SyncAccountBalanceAfterDischargeParams{
		ZalopayID: zalopayID,
		AccountID: accountID,
	}

	// Expect the ExecuteSyncAccountBalanceAfterDischargeTask to be called once with the correct parameters
	// In the success case, the retry mechanism will only call this once
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncAccountBalanceAfterDischargeTask(gomock.Any(), gomock.Eq(expectedParams)).
		Return(nil)

	// Act
	err := s.usecase.TriggerSyncAccountBalanceAfterDischarged(ctx, zalopayID, accountID)

	// Assert
	s.NoError(err)
}

func (s *InstallmentTestSuite) TestTriggerSyncAccountBalanceAfterDischarged_ExecuteError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	accountID := int64(456)

	expectedParams := &dto.SyncAccountBalanceAfterDischargeParams{
		ZalopayID: zalopayID,
		AccountID: accountID,
	}

	// Create an error to be returned by the mock
	mockError := errors.New("execution failed")

	// With the retry mechanism, this will be called multiple times
	// We need to use AnyTimes() to allow for the retries
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncAccountBalanceAfterDischargeTask(gomock.Any(), gomock.Eq(expectedParams)).
		Return(mockError).
		AnyTimes()

	// Act
	err := s.usecase.TriggerSyncAccountBalanceAfterDischarged(ctx, zalopayID, accountID)

	// Assert
	s.Error(err)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
	s.Contains(err.Error(), "failed to execute sync account balance after statement task")
}
