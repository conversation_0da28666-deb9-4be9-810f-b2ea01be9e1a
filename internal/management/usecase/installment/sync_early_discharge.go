package installment

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func (s *Usecase) SyncEarlyDischarge(ctx context.Context, inst *model.InstallmentInfo) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	earlyDischarge, err := s.cimbService.QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID)
	if err != nil {
		logger.Errorf("QueryEarlyDischargeByID has error: %v", err)
		return nil, err
	}
	if earlyDischarge == nil {
		logger.Erro<PERSON>("QueryEarlyDischargeByID return nil")
		return nil, errors.New("early discharge info is nil")
	}

	tCtx, err := s.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("BeginTx has error: %v", err)
		return nil, err
	}
	defer s.txn.RollbackTx(tCtx)

	currInst, err := s.instRepo.GetInstallmentByIDForUpdate(tCtx, inst.ID)
	if err != nil {
		logger.Errorf("GetInstallmentByIDForUpdate has error: %v", err)
		return nil, err
	}

	eadInfo := buildEarlyDischargeInfo(earlyDischarge)
	currInst = currInst.SetEarlyDischarge(eadInfo)
	if earlyDischarge.HasDischarged {
		currInst = currInst.MarkDischargeComplete()
		defer func() {
			newCtx := context.WithoutCancel(ctx)
			go s.SyncInstallmentData(newCtx, inst)
		}()
	}

	if err := s.instRepo.UpdateEarlyDischargeInfo(tCtx, currInst); err != nil {
		logger.Errorf("UpdateEarlyDischargeInfo has error: %v", err)
		return nil, err
	}

	if err := s.txn.CommitTx(tCtx); err != nil {
		logger.Errorf("CommitTx has error: %v", err)
		return nil, err
	}

	return currInst, nil
}
