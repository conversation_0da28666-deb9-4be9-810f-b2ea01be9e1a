package installment

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
	"go.uber.org/mock/gomock"
)

func (s *InstallmentTestSuite) TestCreateInstallment_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentCreateParams{
		ZalopayID:     123,
		AccountID:     456,
		PartnerCode:   partner.PartnerCIMB,
		PartnerInstID: "test-inst-id",
		Installment: dto.InstallmentPlanParams{
			Tenure:         3,
			EmiAmount:      1000000,
			InterestRate:   1.5,
			InterestAmount: 50000,
			DisburseAmount: 950000,
			TotalFeeAmount: 10000,
			TotalDueAmount: 1060000,
			FeeDetails: []model.FeeDetail{
				{
					Type:   model.PlatformFee,
					Amount: 10000,
				},
			},
		},
		Transaction: dto.InstallmentTransParams{
			TransID:   789,
			TransDesc: "Test transaction",
			ZpTransID: 101112,
		},
	}

	cimbInst := &model.PartnerCIMBInst{
		ID:             "test-inst-id",
		Tenure:         3,
		DisburseAmount: 950000,
		RepaymentSchedules: []*model.PartnerCIMBInstRepaySchedule{
			{
				SeqNo:                1,
				EmiAmount:            350000,
				InterestAmount:       20000,
				PrincipalAmount:      330000,
				OutstandingAmount:    700000,
				OutstandingInterest:  30000,
				OutstandingPrincipal: 670000,
			},
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByTransID(gomock.Any(), params.ZalopayID, params.Transaction.TransID).
		Return(cimbInst, nil)

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	s.mockInstRepo.EXPECT().
		CreateInstallment(gomock.Any(), gomock.Any()).
		Return(int64(1), nil)

	s.mockInstRepo.EXPECT().
		CreateRepaymentSchedule(gomock.Any(), gomock.Any()).
		Return(nil)

	s.mockTxn.EXPECT().
		CommitTx(gomock.Any()).
		Return(nil)

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil)

	// Act
	result, err := s.usecase.CreateInstallment(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(params.ZalopayID, result.ZalopayID)
	s.Equal(params.AccountID, result.AccountID)
	s.Equal(params.PartnerCode, result.PartnerCode)
	s.Equal(params.PartnerInstID, result.PartnerInstID)
}

func (s *InstallmentTestSuite) TestCreateInstallment_QueryInstallmentError() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentCreateParams{
		ZalopayID: 123,
		Transaction: dto.InstallmentTransParams{
			TransID: 789,
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByTransID(gomock.Any(), params.ZalopayID, params.Transaction.TransID).
		Return(nil, errorkit.NewError(errorkit.CodeInternalError, "remote call error"))

	// Act
	result, err := s.usecase.CreateInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
}

func (s *InstallmentTestSuite) TestCreateInstallment_BeginTxError() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentCreateParams{
		ZalopayID: 123,
		Transaction: dto.InstallmentTransParams{
			TransID: 789,
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByTransID(gomock.Any(), params.ZalopayID, params.Transaction.TransID).
		Return(&model.PartnerCIMBInst{}, nil)

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(nil, errorkit.NewError(errorkit.CodeInternalError, "begin tx error"))

	// Act
	result, err := s.usecase.CreateInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeInternalError))
}

func (s *InstallmentTestSuite) TestCreateInstallment_CreateInstallmentError() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentCreateParams{
		ZalopayID: 123,
		Transaction: dto.InstallmentTransParams{
			TransID: 789,
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByTransID(gomock.Any(), params.ZalopayID, params.Transaction.TransID).
		Return(&model.PartnerCIMBInst{}, nil)

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	s.mockInstRepo.EXPECT().
		CreateInstallment(gomock.Any(), gomock.Any()).
		Return(int64(0), errorkit.NewError(errorkit.CodeRepositoryError, "create installment error"))

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil)

	// Act
	result, err := s.usecase.CreateInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
}

func (s *InstallmentTestSuite) TestBuildInstallmentInfo() {
	cimbInst := &model.PartnerCIMBInst{
		ID:             "test-inst-id",
		Tenure:         3,
		DisburseAmount: 950000,
	}

	params := &dto.InstallmentCreateParams{
		ZalopayID:     123,
		AccountID:     456,
		PartnerCode:   partner.PartnerCIMB,
		PartnerInstID: "test-inst-id",
		Transaction: dto.InstallmentTransParams{
			TransID:   789,
			TransDesc: "Test transaction",
			ZpTransID: 101112,
		},
	}

	result := buildInstallmentInfo(cimbInst, params)

	s.NotNil(result)
	s.Equal(params.ZalopayID, result.ZalopayID)
	s.Equal(params.AccountID, result.AccountID)
	s.Equal(params.PartnerCode, result.PartnerCode)
	s.Equal(cimbInst.ID, result.PartnerInstID)
}

func (s *InstallmentTestSuite) TestBuildRepaysSchedule() {
	instID := int64(123)
	cimbInst := &model.PartnerCIMBInst{
		ID: "test-inst-id",
		RepaymentSchedules: []*model.PartnerCIMBInstRepaySchedule{
			{
				SeqNo:                1,
				RepayDueDate:         time.Now(),
				GraceDueDate:         time.Now().Add(24 * time.Hour),
				EmiAmount:            1000000,
				InterestAmount:       50000,
				PrincipalAmount:      950000,
				PaidPenalty:          0,
				OutstandingPenalty:   0,
				OutstandingAmount:    1000000,
				OutstandingInterest:  50000,
				OutstandingPrincipal: 950000,
			},
		},
	}

	result := buildRepaysSchedule(instID, cimbInst)

	s.NotNil(result)
	s.Len(result, 1)
	s.Equal(instID, result[0].InstallmentID)
	s.Equal(cimbInst.ID, result[0].PartnerInstID)
}

func (s *InstallmentTestSuite) TestCreateInstallment_CreateRepaymentError() {
	// Arrange
	ctx := context.Background()
	params := &dto.InstallmentCreateParams{
		ZalopayID: 123,
		Transaction: dto.InstallmentTransParams{
			TransID: 456,
		},
	}

	cimbInst := &model.PartnerCIMBInst{
		ID: "test-inst-id",
		RepaymentSchedules: []*model.PartnerCIMBInstRepaySchedule{
			{SeqNo: 1},
		},
	}

	s.mockCIMBSvc.EXPECT().
		QueryInstallmentByTransID(gomock.Any(), params.ZalopayID, params.Transaction.TransID).
		Return(cimbInst, nil)

	s.mockTxn.EXPECT().
		BeginTx(gomock.Any()).
		Return(ctx, nil)

	s.mockInstRepo.EXPECT().
		CreateInstallment(gomock.Any(), gomock.Any()).
		Return(int64(789), nil)

	s.mockInstRepo.EXPECT().
		CreateRepaymentSchedule(gomock.Any(), gomock.Any()).
		Return(errorkit.NewError(errorkit.CodeRepositoryError, "create repayment error"))

	s.mockTxn.EXPECT().
		RollbackTx(gomock.Any()).
		Return(nil)

	// Act
	result, err := s.usecase.CreateInstallment(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.ErrorIs(err, errorkit.NewWithCode(errorkit.CodeRepositoryError))
}

func (s *InstallmentTestSuite) TestBuildInstallmentEarlyDischarge() {
	// Test case 1: should return a non-nil result with nil Details when early discharge is nil
	cimbInst1 := &model.PartnerCIMBInst{
		EarlyDischargeInfo: nil,
	}

	result1 := buildEarlyDischargeInfo(cimbInst1.EarlyDischargeInfo)
	s.NotNil(result1)
	s.Nil(result1.Details)

	// Test case 2: should return early discharge data when available
	cimbInst2 := &model.PartnerCIMBInst{
		EarlyDischargeInfo: &model.PartnerEarlyDischarge{
			ID: "test-early-discharge",
			DischargeData: &model.EarlyDischargeData{
				OutstandingPrincipal: zutils.Ptr(int64(1000)),
				OutstandingInterest:  zutils.Ptr(int64(200)),
				OutstandingPenalty:   zutils.Ptr(int64(50)),
				EarlyDischargeFee:    100,
				TotalDischargeAmount: 1350,
			},
		},
	}

	result2 := buildEarlyDischargeInfo(cimbInst2.EarlyDischargeInfo)

	s.NotNil(result2)
	s.NotNil(result2.Details)
	s.NotNil(result2.Details.OutstandingPrincipal)
	s.NotNil(result2.Details.OutstandingInterest)
	s.NotNil(result2.Details.OutstandingPenalty)
	s.Equal(int64(1000), *result2.Details.OutstandingPrincipal)
	s.Equal(int64(200), *result2.Details.OutstandingInterest)
	s.Equal(int64(50), *result2.Details.OutstandingPenalty)
	s.Equal(int64(100), result2.Details.EarlyDischargeFee)
	s.Equal(int64(1350), result2.Details.TotalDischargeAmount)
}
