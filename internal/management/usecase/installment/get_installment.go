package installment

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *Usecase) GetFullInstallment(ctx context.Context,
	params *dto.InstallmentQueryParams) (*model.Installment, error) {
	logger := s.logger.WithContext(ctx)

	instFull, err := s.GetFullInstallmentByZalopayAndZPTransID(ctx, params.ZalopayID, params.ZPTransID)
	if err != nil {
		logger.Errorf("GetFullInstallmentByZalopayAndZPTransID has error: %v", err)
		return nil, err
	}
	return instFull, nil
}

func (s *Usecase) GetFullInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*model.Installment, error) {
	logger := s.logger.WithContext(ctx)

	instFull, err := s.instRepo.GetFullInstallmentByZPTransID(ctx, zpTransID)
	if errors.Is(err, model.ErrInstallmentNotFound) {
		logger.Warnf("GetFullInstallmentByZPTransID has not found data: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInstallmentNotFound, "installment not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if errors.Is(err, model.ErrInstallmentRepaysNotFound) {
		logger.Warnf("GetFullInstallmentByZPTransID has not found repay schedules: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInstallmentDataEmpty, "installment repay schedules not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("GetFullInstallmentByZPTransID has error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get installment error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return instFull, nil
}

func (s *Usecase) GetFullInstallmentByZalopayAndZPTransID(
	ctx context.Context, zalopayID, zpTransID int64) (*model.Installment, error) {
	logger := s.logger.WithContext(ctx)

	instFull, err := s.instRepo.GetFullInstallmentByUserIDAndZPTransID(ctx, zalopayID, zpTransID)
	if errors.Is(err, model.ErrInstallmentNotFound) {
		logger.Warnf("GetFullInstallmentByUserIDAndZPTransID has not found data: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInstallmentNotFound, "installment not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if errors.Is(err, model.ErrInstallmentRepaysNotFound) {
		logger.Warnf("GetFullInstallmentByUserIDAndZPTransID has not found repay schedules: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInstallmentDataEmpty, "installment repay schedules not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("GetFullInstallmentByUserIDAndZPTransID has error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get installment error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return instFull, nil
}

func (s *Usecase) GetInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	inst, err := s.instRepo.GetInstallmentByZPTransID(ctx, zpTransID)
	if errors.Is(err, model.ErrInstallmentNotFound) {
		logger.Warnf("GetInstallmentByZPTransID has not found data: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInstallmentNotFound, "installment not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("GetInstallmentByZPTransID has error: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get installment error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return inst, nil
}
