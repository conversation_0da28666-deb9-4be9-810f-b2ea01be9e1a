package installment

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
)

func (s *Usecase) GetLatestEarlyDischarge(ctx context.Context, params *dto.EarlyDischargeQueryParams) (*dto.EarlyDischargeNormalize, error) {
	logger := s.logger.WithContext(ctx)

	inst, err := s.instRepo.GetInstallmentByZPTransID(ctx, params.ZPTransID)
	if err != nil {
		logger.Errorf("GetInstallmentByZPTransID has error: %v", err)
		return nil, err
	}

	if !params.ForceSync && !inst.MismatchedEarlyDischarge() {
		return s.buildNormalizeEarlyDischarge(inst), nil
	}

	newInst, err := s.SyncEarlyDischarge(ctx, inst)
	if err != nil {
		logger.Warnf("SyncEarlyDischarge has failed: %v", err)
		logger.Warn("Return the current installment info")
		return s.buildNormalizeEarlyDischarge(inst), nil
	}

	return s.buildNormalizeEarlyDischarge(newInst), nil
}

func (s *Usecase) BuildNormalizeEarlyDischarge(inst *model.InstallmentInfo) *dto.EarlyDischargeNormalize {
	return s.buildNormalizeEarlyDischarge(inst)
}

// buildNormalizeEarlyDischarge creates the normalized early discharge response with proper validation
func (s *Usecase) buildNormalizeEarlyDischarge(installment *model.InstallmentInfo) *dto.EarlyDischargeNormalize {
	// Create normalized object with basic properties
	eadConfig := s.dischargeCfg
	normalized := &dto.EarlyDischargeNormalize{
		InSession:             true,
		RefInstID:             installment.ID,
		RefZPTransID:          installment.ZPTransID,
		RefInstInfo:           installment,
		DischargeStatus:       installment.DischargeStatus,
		EarlyDischargeAllowed: installment.IsEarlyDischargeAllowed(),
		EarlyDischargeKind:    installment.GetEarlyDischargeKind(),
		EarlyDischargeFee:     installment.GetEarlyDischargeFee(),
		EarlyDischargeOuts:    installment.GetEarlyDischargeOuts(),
		TotalDischargeAmount:  installment.GetTotalEarlyDischargeAmount(),
		AdjustDischargeAmount: installment.GetAdjustEarlyDischargeAmount(),
	}

	if eadConfig == nil {
		return normalized
	}

	switch normalized.EarlyDischargeKind {
	case model.EarlyDischargeKindRefund:
		if eadConfig.GetRefund().GetTimeWindow() == nil {
			return normalized
		}

		tWindow := eadConfig.GetRefund().GetTimeWindow()
		endTime := timeOfDayToTime(tWindow.EndTime)
		startTime := timeOfDayToTime(tWindow.StartTime)
		normalized = enrichEarlyDischargeSession(normalized, startTime, endTime)
	}

	return normalized
}

func enrichEarlyDischargeSession(normalized *dto.EarlyDischargeNormalize, startTime, endTime time.Time) *dto.EarlyDischargeNormalize {
	inSession := isEarlyDischargeTimeAllowed(startTime, endTime)
	normalized.InSession = inSession
	// Currently we combine the session condition with domain condition
	normalized.EarlyDischargeAllowed = normalized.EarlyDischargeAllowed && inSession

	if startTime.IsZero() && endTime.IsZero() {
		return normalized
	}

	normalized.SessionInfo = &dto.Session{}
	if !startTime.IsZero() {
		normalized.SessionInfo.StartTime = &startTime
	}
	if !endTime.IsZero() {
		normalized.SessionInfo.EndTime = &endTime
	}

	return normalized
}

func isEarlyDischargeTimeAllowed(startTime, endTime time.Time) bool {
	if startTime.IsZero() || endTime.IsZero() {
		return false
	}

	now := time.Now()
	return now.After(startTime) && now.Before(endTime)
}

func buildEarlyDischargeInfo(dischargeInfo *model.PartnerEarlyDischarge) *model.InstallmentEarlyDischarge {
	result := &model.InstallmentEarlyDischarge{
		SyncedAt: time.Now(),
	}
	if dischargeInfo == nil ||
		dischargeInfo.HasDischarged ||
		dischargeInfo.DischargeData == nil {
		return result
	}
	result.Details = &model.EarlyDischargeDetails{
		OutstandingPrincipal: dischargeInfo.DischargeData.OutstandingPrincipal,
		OutstandingInterest:  dischargeInfo.DischargeData.OutstandingInterest,
		OutstandingPenalty:   dischargeInfo.DischargeData.OutstandingPenalty,
		EarlyDischargeFee:    dischargeInfo.DischargeData.EarlyDischargeFee,
		TotalDischargeAmount: dischargeInfo.DischargeData.TotalDischargeAmount,
	}
	return result
}
