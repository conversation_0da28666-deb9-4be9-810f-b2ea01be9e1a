package installment

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func (s *Usecase) HandleRefundEventNotified(ctx context.Context, zpTransID int64, refundData *model.InstallmentRefund) error {
	logger := s.logger.WithContext(ctx)

	inst, err := s.instRepo.GetInstallmentByZPTransID(ctx, zpTransID)
	if err != nil {
		logger.Errorf("GetInstallmentByZPTransID has error: %v", err)
		return err
	}
	if inst.RefundInfo != nil &&
		inst.RefundInfo.UpdateVersion >= refundData.UpdateVersion {
		logger.Infof("Refund info has been updated: %v, zpTransID=%v", inst.RefundInfo, zpTransID)
		return nil
	}

	err = s.instRepo.UpdateInstallmentRefund(ctx, inst.ID, refundData)
	if err != nil {
		logger.Errorf("UpdateInstallmentRefund has error: %v", err)
		return err
	}

	logger.Infow(
		"msg", "Refund info has been updated",
		"instID ", inst.ID,
		"zpTransID", inst.ZPTransID,
		"refundData", refundData,
	)

	return nil
}
