package dto

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type MaintenanceData struct {
	PartnerCode      partner.PartnerCode
	MaintenanceState MaintenanceState
}

type MaintenanceState maintenance.State

// SessionInfo represents a time-bounded session with start and end times
type Session struct {
	StartTime *time.Time // Start of allowed time session
	EndTime   *time.Time // End of allowed time session
}
