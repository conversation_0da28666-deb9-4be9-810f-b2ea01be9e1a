package dto

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type AccountForStatementParams struct {
	TotalLimit    int32
	FromAccountID int64
	PartnerCodes  []partner.PartnerCode
	StatementDate time.Time
}

type SyncAccountBalanceAfterStatementParams struct {
	ZalopayID     int64     `json:"zalopay_id"`
	AccountID     int64     `json:"account_id"`
	StatementDate time.Time `json:"-"`
}

type SyncAccountBalanceAfterDischargeParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
}
