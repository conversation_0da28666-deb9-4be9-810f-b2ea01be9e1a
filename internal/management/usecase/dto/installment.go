package dto

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type ListPlanParams struct {
	ZalopayID   int64
	AppID       int32
	AppTransID  string
	OrderAmount int64
	PartnerCode partner.PartnerCode
}

type ListPlanResult struct {
	PlanOptions     []*model.Plan
	PlanSelected    *model.Plan
	PlanKeySelected string
}

type GetPlanParams struct {
	PlanKey   string
	ZalopayID int64
	OrderInfo *model.OrderRequest
}

type GetPlanResult struct {
	Plan      *model.Plan
	Action    *model.Action
	Notice    *model.Message
	Message   *model.Message
	ModuleFed *model.ModuleFederation
}

type AcceptPlanParams struct {
	PlanKey   string
	ZalopayID int64
	OrderInfo *model.OrderRequest
}

type InstallmentCreateParams struct {
	ZalopayID     int64                  `json:"zalopay_id"`
	AccountID     int64                  `json:"account_id"`
	PartnerCode   partner.PartnerCode    `json:"partner_code"`
	PartnerInstID string                 `json:"partner_inst_id"`
	Installment   InstallmentPlanParams  `json:"installment"`
	Transaction   InstallmentTransParams `json:"transaction"`
}

type InstallmentCreateByRefIDParams struct {
	ZalopayID     int64               `json:"zalopay_id"`
	AccountID     int64               `json:"account_id"`
	PartnerCode   partner.PartnerCode `json:"partner_code"`
	PartnerInstID string              `json:"partner_inst_id"`
}

type InstallmentTransParams struct {
	TransID   int64  `json:"trans_id"`
	TransDesc string `json:"trans_desc"`
	ZpTransID int64  `json:"zp_trans_id"`
}

type InstallmentPlanParams struct {
	Tenure         int32             `json:"tenure"`
	EmiAmount      int64             `json:"emi_amount"`
	InterestRate   float64           `json:"interest_rate"`
	InterestAmount int64             `json:"interest_amount"`
	DisburseAmount int64             `json:"disburse_amount"`
	TotalFeeAmount int64             `json:"total_fee_amount"`
	TotalDueAmount int64             `json:"total_due_amount"`
	FeeDetails     []model.FeeDetail `json:"fee_details"`
}

type InstallmentQueryParams struct {
	InstID    int64
	ZPTransID int64
	ZalopayID int64
}

type InstallmentSyncStats struct {
	TotalItems    int     `json:"total_items"`
	TotalSuccess  int     `json:"total_success"`
	TotalFailed   int     `json:"total_failed"`
	ListFailedIDs []int64 `json:"list_failed_ids"`
}

type InstallmentSyncOptions struct {
	PollingTime time.Duration
}

type SyncInstallmentParams struct {
	InstID    int64
	ZPTransID int64
	InstallmentSyncOptions
}

type EarlyDischargeQueryParams struct {
	InstID    int64
	ZPTransID int64
	ForceSync bool // Force sync installment info, default is false
}

type EarlyDischargeNormalize struct {
	InSession             bool
	RefInstID             int64
	RefZPTransID          int64
	SessionInfo           *Session
	RefInstInfo           *model.InstallmentInfo // optional
	DischargeStatus       model.DischargeStatus
	EarlyDischargeKind    model.EarlyDischargeKind
	EarlyDischargeFee     int64
	EarlyDischargeOuts    int64
	EarlyDischargeAllowed bool // This field may only be used for FE
	TotalDischargeAmount  int64
	AdjustDischargeAmount int64
}

func (e EarlyDischargeNormalize) GetZalopayID() int64 {
	if e.RefInstInfo != nil {
		return e.RefInstInfo.ZalopayID
	}
	return 0
}
