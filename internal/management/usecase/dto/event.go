package dto

type PaymentEventType string

const (
	PaymentEventCommission PaymentEventType = "COMMISSION"
)

type PaymentCommissionEvent struct {
	Type       PaymentEventType       `json:"type"`
	Commission *PaymentCommissionData `json:"commission,omitempty"`
}

type PaymentCommissionData struct {
	ZPTransID    int64 `json:"zp_trans_id"`
	IsCommission bool  `json:"is_commission"`
}

func NewPaymentCommissionEvent(zpTransID int64) *PaymentCommissionEvent {
	return &PaymentCommissionEvent{
		Type: PaymentEventCommission,
		Commission: &PaymentCommissionData{
			ZPTransID:    zpTransID,
			IsCommission: true,
		},
	}
}
