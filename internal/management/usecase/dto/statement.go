package dto

import (
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type QueryStatementParams struct {
	ZalopayID     int64
	AccountID     int64
	StatementDate time.Time
}

type QueryListStatementsParams struct {
	ZalopayID         int64
	AccountID         int64
	StatementDateFrom time.Time
	StatementDateTo   time.Time
}

type QueryStatementInstallmentsParams struct {
	ZalopayID   int64
	AccountID   int64
	StatementID int64
}

type SyncLoanRepaymentWorkflow struct {
	ZalopayID     int64          `json:"zalopay_id"`
	AccountID     int64          `json:"account_id"`
	StatementID   int64          `json:"statement_id"`
	StatementDate time.Time      `json:"statement_date"`
	EventData     map[string]any `json:"event_data"`
}

type StatementSyncStats struct {
	TotalItems    int     `json:"total_items"`
	TotalSuccess  int     `json:"total_success"`
	TotalFailed   int     `json:"total_failed"`
	ListFailedIDs []int64 `json:"list_failed_ids"`
}

type SyncLatestStatementParams struct {
	ZalopayID   int64
	AccountID   int64
	PartnerCode partner.PartnerCode
}
