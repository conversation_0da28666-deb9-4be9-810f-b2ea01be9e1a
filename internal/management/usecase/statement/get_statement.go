package statement

import (
	"context"
	"errors"
	"slices"
	"time"

	"github.com/spf13/cast"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (uc *Usecase) GetStatementByUserAndIncurDate(ctx context.Context,
	params *dto.QueryStatementParams) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)

	stmt, err := uc.stmtRepo.QueryStatementByIncurDate(ctx,
		params.ZalopayID, params.AccountID, params.StatementDate)
	if errors.Is(err, model.ErrStatementNotFound) {
		logger.Errorf("failed to get statement by incurred date: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeStatementNotFound, "statement not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("failed to get statement by incurred date: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "internal error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return stmt, nil
}

func (uc *Usecase) GetLatestStatementByUser(ctx context.Context,
	params *dto.QueryStatementParams) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)

	stmt, err := uc.stmtRepo.QueryLatestStatement(ctx, params.ZalopayID, params.AccountID)
	if errors.Is(err, model.ErrStatementNotFound) {
		logger.Errorf("failed to get latest statement: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeStatementNotFound, "statement not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("failed to get latest statemente: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get latest statement has error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return stmt, nil
}

func (uc *Usecase) GetStatementByID(ctx context.Context, stmtID int64) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)

	stmt, err := uc.stmtRepo.QueryStatementByID(ctx, stmtID)
	if errors.Is(err, model.ErrStatementNotFound) {
		logger.Errorf("failed to get statement by id: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeStatementNotFound, "statement not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("failed to get statement by id: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "internal error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return stmt, nil
}

func (uc *Usecase) BuildListStatementSyncPenaltyCh(ctx context.Context) (<-chan *model.Statement, error) {
	logger := uc.logger.WithContext(ctx)
	timeNow := time.Now()
	currStmtPeriod := uc.getCurrentStatementPeriod()
	nextStmtPeriod := currStmtPeriod.SimulateNextPeriod()

	// TODO: Can be remove after testing
	if slices.Contains([]string{"dev", "qc"}, uc.env) {
		stmtDateMock, err := uc.mocking.GetStatementDateOverdue()
		if err == nil && !stmtDateMock.IsZero() {
			timeNow = stmtDateMock
		}
	}

	if timeNow.After(nextStmtPeriod.IncurredDate) {
		logger.Warnf("time is in time range has not penalty incurred, no statement need sync, time: %v", timeNow)
		return nil, nil
	}

	currDueDate, err := uc.stmtRepo.QueryDueDateByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate)
	if err != nil {
		logger.Errorf("failed to get current statement due date: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get current statement due date has error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	statements := make(<-chan *model.Statement, channelPenaltySize)
	syncAllBufferDays := uc.stmtSyncConfig.PenaltyAllBufferDays

	switch {
	case timeNow.After(currDueDate.AddDate(0, 0, syncAllBufferDays)):
		statements, err = uc.GetOutsStatementsSyncPenaltyByIncurDateCh(ctx, currStmtPeriod.IncurredDate)
	default:
		statements, err = uc.GetStatementsSyncPenaltyByIncurDateCh(ctx, currStmtPeriod.IncurredDate)
	}

	if err != nil {
		logger.Errorf("failed to get statement for penalty sync by incurred date and partner: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to get statement for penalty sync by incurred date and partner").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return statements, nil
}

func (uc *Usecase) GetStatementsSyncPenaltyByIncurDateCh(ctx context.Context, incurredDate time.Time) (<-chan *model.Statement, error) {
	lastID := int64(0)
	logger := uc.logger.WithContext(ctx)
	statements := make(chan *model.Statement, channelPenaltySize)

	go func() {
		defer close(statements)
		for {
			cursor := cast.ToString(lastID)
			pagination := &model.Pagination{
				Limit:  syncPenaltyBatchSize,
				Cursor: &cursor,
			}
			stmts, err := uc.stmtRepo.QueryStatementsByIncurDateAndPartner(
				ctx, partner.PartnerCIMB, incurredDate, pagination)
			if err != nil {
				logger.Errorf("failed to get statement by incurred date and status: %v", err)
				return
			}
			if len(stmts) == 0 {
				logger.Infof("no statement has outstanding status, incurred date: %v", incurredDate)
				break
			}
			for _, stmt := range stmts {
				statements <- stmt
			}
			lastID = stmts[len(stmts)-1].ID
		}
	}()

	return statements, nil
}

func (uc *Usecase) GetOutsStatementsSyncPenaltyByIncurDateCh(ctx context.Context, incurredDate time.Time) (<-chan *model.Statement, error) {
	lastID := int64(0)
	logger := uc.logger.WithContext(ctx)
	statements := make(chan *model.Statement, channelPenaltySize)

	go func() {
		defer close(statements)
		for {
			cursor := cast.ToString(lastID)
			pagination := &model.Pagination{
				Limit:  syncPenaltyBatchSize,
				Cursor: &cursor,
			}
			stmts, err := uc.stmtRepo.QueryStatementsByIncurDateAndPartner(
				ctx, partner.PartnerCIMB, incurredDate, pagination)
			if err != nil {
				logger.Errorf("failed to get statement by incurred date and status: %v", err)
				return
			}
			if len(stmts) == 0 {
				logger.Infof("no statement has outstanding status, incurred date: %v", incurredDate)
				break
			}
			for _, stmt := range stmts {
				statements <- stmt
			}
			lastID = stmts[len(stmts)-1].ID
		}
	}()

	return statements, nil
}
