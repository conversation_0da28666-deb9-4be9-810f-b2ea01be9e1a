package statement

import (
	"context"
	"slices"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (uc *Usecase) ListStatementsByUserAndIncurDateRange(ctx context.Context,
	params *dto.QueryListStatementsParams) ([]*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)

	stmts, err := uc.stmtRepo.QueryStatementsByIncurDateRange(
		ctx, params.ZalopayID, params.AccountID,
		params.StatementDateFrom, params.StatementDateTo,
	)
	if errors.Is(err, model.ErrStatementNotFound) {
		logger.Warnf("no statements found for user %d in date range %s - %s", params.ZalopayID, params.StatementDateFrom, params.StatementDateTo)
		return []*model.Statement{}, nil
	}
	if err != nil {
		logger.Errorf("failed to get user statements by incurred date range: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to query statements by incur date range in db ").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if len(stmts) == 0 {
		return []*model.Statement{}, nil
	}

	slices.SortFunc(stmts, func(a, b *model.Statement) int {
		return int(b.IncurredDate.Unix() - a.IncurredDate.Unix())
	})

	return stmts, nil
}
