package statement

import (
	"context"
	"errors"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
)

func (s *StatementTestSuite) TestListStatementsByUserAndIncurDateRange_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryListStatementsParams{
		ZalopayID:         123,
		AccountID:         123,
		StatementDateFrom: time.Now().AddDate(0, -1, 0),
		StatementDateTo:   time.Now(),
	}
	expectedStmts := []*model.Statement{
		{ID: 1, IncurredDate: time.Now()},
		{ID: 2, IncurredDate: time.Now().AddDate(0, -1, 0)},
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementsByIncurDateRange(ctx, params.ZalopayID, params.AccountID,
			params.StatementDateFrom, params.StatementDateTo).
		Return(expectedStmts, nil)

	// Act
	result, err := s.usecase.ListStatementsByUserAndIncurDateRange(ctx, params)

	// Assert
	s.NoError(err)
	s.Len(result, 2)
	s.Equal(expectedStmts[0], result[0]) // Should be sorted by IncurredDate desc
}

func (s *StatementTestSuite) TestListStatementsByUserAndIncurDateRange_NotFound() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryListStatementsParams{
		ZalopayID:         123,
		AccountID:         123,
		StatementDateFrom: time.Now().AddDate(0, -1, 0),
		StatementDateTo:   time.Now(),
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementsByIncurDateRange(ctx, params.ZalopayID, params.AccountID,
			params.StatementDateFrom, params.StatementDateTo).
		Return(nil, model.ErrStatementNotFound)

	// Act
	result, err := s.usecase.ListStatementsByUserAndIncurDateRange(ctx, params)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *StatementTestSuite) TestListStatementsByUserAndIncurDateRange_EmptyResult() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryListStatementsParams{
		ZalopayID:         123,
		AccountID:         123,
		StatementDateFrom: time.Now().AddDate(0, -1, 0),
		StatementDateTo:   time.Now(),
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementsByIncurDateRange(ctx, params.ZalopayID, params.AccountID,
			params.StatementDateFrom, params.StatementDateTo).
		Return([]*model.Statement{}, nil)

	// Act
	result, err := s.usecase.ListStatementsByUserAndIncurDateRange(ctx, params)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *StatementTestSuite) TestListStatementsByUserAndIncurDateRange_Error() {
	// Arrange
	ctx := context.Background()
	dateFrom := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	dateTo := time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC)
	params := &dto.QueryListStatementsParams{
		ZalopayID:         123,
		AccountID:         123,
		StatementDateFrom: dateFrom,
		StatementDateTo:   dateTo,
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementsByIncurDateRange(ctx, params.ZalopayID, params.AccountID,
			params.StatementDateFrom, params.StatementDateTo).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.ListStatementsByUserAndIncurDateRange(ctx, params)

	// Assert
	s.Error(err)
	s.Empty(result)
	s.Contains(err.Error(), "failed to query statements by incur date range in db")
}
