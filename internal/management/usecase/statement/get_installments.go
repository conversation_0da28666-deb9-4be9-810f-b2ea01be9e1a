package statement

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (uc *Usecase) GetInstallmentsByUserAndStatementID(ctx context.Context,
	params *dto.QueryStatementInstallmentsParams) ([]*model.StatementInstallment, error) {
	logger := uc.logger.WithContext(ctx)

	listStmtInst, err := uc.stmtRepo.QueryInstallmentsByUserAndStatementID(ctx,
		params.ZalopayID, params.AccountID, params.StatementID)
	if err != nil {
		logger.Errorf("failed to get statement installments: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get statement installments failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	listStmtInst = filterDueInstallments(listStmtInst)
	partnerInstIDs := make([]string, 0, len(listStmtInst))
	for _, stmtInst := range listStmtInst {
		partnerInstIDs = append(partnerInstIDs, stmtInst.PartnerInstID)
	}

	listTransInst, err := uc.instRepo.ListInstallmentByPartnerIDs(ctx, partnerInstIDs)
	if err != nil {
		logger.Errorf("failed to get installments by partner inst ids: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "internal error").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if len(listTransInst) == 0 {
		return []*model.StatementInstallment{}, nil
	}

	return mergeStatementInstallments(listStmtInst, listTransInst), nil
}

func (uc *Usecase) GetInstallmentsByStatementID(ctx context.Context, stmtID int64) ([]*model.StatementInstallment, error) {
	logger := uc.logger.WithContext(ctx)

	listStmtInst, err := uc.stmtRepo.QueryInstallmentsByStatementID(ctx, stmtID)
	if err != nil {
		logger.Errorf("failed to get statement installments by statement id: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get statement installments by statement id failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return listStmtInst, nil
}

func filterDueInstallments(insts []*model.StatementInstallment) []*model.StatementInstallment {
	availableInsts := make([]*model.StatementInstallment, 0, len(insts))
	for _, inst := range insts {
		if inst.GetOutstandingTotal() > 0 {
			availableInsts = append(availableInsts, inst)
		}
	}
	return availableInsts
}

func mergeStatementInstallments(
	stmtInsts []*model.StatementInstallment,
	transInsts []*model.InstallmentInfo) []*model.StatementInstallment {
	instMap := zutils.KeyBy(transInsts, func(t *model.InstallmentInfo) string {
		return t.PartnerInstID
	})

	for _, stmtInst := range stmtInsts {
		inst, ok := instMap[stmtInst.PartnerInstID]
		if !ok || inst == nil {
			continue
		}
		stmtInst.RefOriInstID = inst.ID
		stmtInst.RefZPTransID = inst.ZPTransID
		stmtInst.TransactionRemark = inst.TransactionInfo.GetTransDesc()
	}

	return stmtInsts
}
