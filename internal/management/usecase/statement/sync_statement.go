package statement

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"sync"
	"time"

	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (uc *Usecase) CheckLatestStatementIncurred(ctx context.Context) (*model.StatementPeriod, bool, error) {
	logger := uc.logger.WithContext(ctx)
	stmtPeriod := uc.getCurrentStatementPeriod()
	retryer := retry.NewRetry(3, time.Second*30, nil)

	users, err := uc.acctService.ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
		TotalLimit:    5,
		FromAccountID: 0,
		PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
		StatementDate: stmtPeriod.IncurredDate,
	})
	if err != nil {
		logger.Errorf("failed to list users for statement sync: %v", err)
		return nil, false, errorkit.
			NewError(errorkit.CodeCallAccountFailed, "failed to list users for statement sync").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if len(users) == 0 {
		logger.Warn("no users for statement sync")
		return stmtPeriod, false, nil
	}

	/**
	 * Block code below use to check our assumed statement data is available in CIMB
	 * It only check for 1 user, if it has a statement data, then we can assume that all users have statement data
	 * But now it can be used for monitoring, not the condition to make decision to sync statement or not
	 */
	user := users[0]
	err = retryer.Execute(ctx, func() error {
		data, rErr := uc.cimbService.GetStatementByIncurredDate(ctx, user, stmtPeriod.IncurredDate)
		if rErr != nil {
			return rErr
		}
		if data == nil {
			return errors.New("no statement data")
		}
		return nil
	})
	if err != nil {
		logger.Warnf("no statement data for syncing, err=%v", err)
	}
	// End block code

	logger.Infof("latest statement incurred, period=%+v", stmtPeriod)
	return stmtPeriod, true, nil
}

func (uc *Usecase) ListAccountsForStatementSync(ctx context.Context,
	stmtPeriod *model.StatementPeriod) ([]*model.Account, error) {
	var cursor = int64(0)
	var accounts []*model.Account

	for {
		params := &dto.AccountForStatementParams{
			FromAccountID: cursor,
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}
		users, err := uc.acctService.ListAccountForStmtSync(ctx, params)
		if err != nil {
			uc.logger.WithContext(ctx).Errorf("failed to list users for statement sync: %v", err)
			return nil, errorkit.
				NewError(errorkit.CodeCallAccountFailed, "failed to list users for statement sync").
				WithCause(err).WithKind(errorkit.TypeRemoteCall)
		}
		if len(users) == 0 {
			break
		}
		accounts = append(accounts, users...)
		cursor = users[len(users)-1].ID
	}
	return accounts, nil
}

func (uc *Usecase) ListAccountsForStatementSyncByCursor(ctx context.Context,
	cursor int64, stmtPeriod *model.StatementPeriod) ([]*model.Account, error) {
	params := &dto.AccountForStatementParams{
		FromAccountID: cursor,
		TotalLimit:    syncStmtAccBatchSize,
		PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
		StatementDate: stmtPeriod.IncurredDate,
	}
	accounts, err := uc.acctService.ListAccountForStmtSync(ctx, params)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("failed to list users for statement sync: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallAccountFailed, "failed to list users for statement sync").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	return accounts, nil
}

func (uc *Usecase) FulfillAccountsForSyncIntoBatches(ctx context.Context,
	batches []*model.StatementSyncBatch) ([]*model.StatementSyncBatch, error) {
	const workerCount = 3
	logger := uc.logger.WithContext(ctx)

	waitGroup := sync.WaitGroup{}
	batchInCh := make(chan *model.StatementSyncBatch, len(batches))
	batchOutCh := make(chan *model.StatementSyncBatch, len(batches))
	batchesResult := make([]*model.StatementSyncBatch, 0, len(batches))

	for _, batch := range batches {
		batchInCh <- batch
	}
	close(batchInCh)

	// Handle fulfillment
	handleFulfilment := func() {
		defer waitGroup.Done()
		for batch := range batchInCh {
			accounts, err := uc.ListAccountsForStatementSyncByCursor(ctx, batch.QueryOffset, batch.StmtPeriod)
			if err != nil {
				logger.Errorw(
					"msg", "failed to list accounts for statement sync", "error", err,
					"batch_no", batch.BatchNo, "stmt_period", batch.StmtPeriod,
					"ref_exec_id", batch.RefExecID, "ref_sync_id", batch.RefSyncID,
				)
				continue
			}
			batch.Accounts = accounts
			batchOutCh <- batch
		}
	}

	for i := 0; i < workerCount; i++ {
		waitGroup.Add(1)
		go handleFulfilment()
	}

	waitGroup.Wait()
	close(batchOutCh)

	for batch := range batchOutCh {
		batchesResult = append(batchesResult, batch)
	}
	slices.SortStableFunc(batchesResult, func(a, b *model.StatementSyncBatch) int {
		return a.BatchNo - b.BatchNo
	})

	return batchesResult, nil
}

func (uc *Usecase) SplitAccountsIntoBatches(
	stmtPeriod *model.StatementPeriod,
	accounts []*model.Account) []*model.StatementSyncBatch {
	// Split users into batches
	var batches []*model.StatementSyncBatch
	var batchNo = 0
	for i := 0; i < len(accounts); i += syncStmtAccBatchSize {
		end := min(i+syncStmtAccBatchSize, len(accounts))

		batchNo = batchNo + 1
		batchActs := accounts[i:end]
		queryOffset := int64(0)
		if i > 0 {
			// Use the last account ID from the previous batch as the offset
			queryOffset = accounts[i-1].ID
		}

		batches = append(batches, &model.StatementSyncBatch{
			BatchNo:     batchNo,
			Accounts:    batchActs,
			QueryOffset: queryOffset,
			StmtPeriod:  stmtPeriod,
			TotalItems:  len(batchActs),
			PartnerCode: partner.PartnerCIMB,
		})
	}
	return batches
}

func (uc *Usecase) StoreStatementSyncBatches(ctx context.Context,
	batches []*model.StatementSyncBatch) error {
	logger := uc.logger.WithContext(ctx)
	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "failed to begin transaction").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	for _, batch := range batches {
		stmtDate := batch.StmtPeriod.IncurredDate
		execID := generateBatchExecID(stmtDate, batch.BatchNo)
		syncStats := model.StatementSyncStats{
			TotalItems:  batch.TotalItems,
			QueryOffset: cast.ToString(batch.QueryOffset),
		}
		syncInfo := &model.StatementSyncInfo{
			ExecID:        execID,
			BatchNo:       batch.BatchNo,
			Period:        batch.StmtPeriod.Period,
			Status:        model.StatementSyncStatusPending,
			SyncStats:     syncStats,
			PartnerCode:   batch.PartnerCode,
			StatementDate: batch.StmtPeriod.IncurredDate,
		}
		syncID, sErr := uc.stmtSyncerRepo.CreateSyncInfo(ctx, syncInfo)
		if sErr != nil {
			logger.Errorf("failed to create statement sync infos: %v, syncInfo=%+v", sErr, syncInfo)
			return errorkit.
				NewError(errorkit.CodeRepositoryError, "failed to create statement sync infos").
				WithCause(sErr).WithKind(errorkit.TypeRepository)
		}

		batch.RefExecID = execID
		batch.RefSyncID = syncID
	}
	if err = uc.txn.CommitTx(ctx); err != nil {
		uc.logger.Errorf("failed to commit transaction: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "failed to commit transaction").WithCause(err)
	}
	return nil
}

func (uc *Usecase) UpdateSyncInfoStatusAndStats(ctx context.Context,
	syncInfo *model.StatementSyncInfo) error {
	if err := validateSyncInfoID(syncInfo); err != nil {
		return errorkit.
			NewError(errorkit.CodeInvalidArgument, "sync info ID and exec ID are required").
			WithCause(err).WithKind(errorkit.TypeInvalidArg)
	}

	err := uc.stmtSyncerRepo.UpdateSyncStatusAndStats(ctx, syncInfo)
	if err != nil {
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to update statement sync status and stats").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return nil
}

func (uc *Usecase) UpdateSyncInfoStatus(ctx context.Context,
	syncInfo *model.StatementSyncInfo) error {
	if err := validateSyncInfoID(syncInfo); err != nil {
		return errorkit.
			NewError(errorkit.CodeInvalidArgument, "sync info ID and exec ID are required").
			WithCause(err).WithKind(errorkit.TypeInvalidArg)
	}

	err := uc.stmtSyncerRepo.UpdateSyncStatus(ctx, syncInfo)
	if err != nil {
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to update statement sync status").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return nil
}

func (uc *Usecase) SyncCreateAccountStatement(ctx context.Context,
	account *model.Account, stmtDate time.Time) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)
	stmtPeriod := uc.getStatementPeriodByIncurredDate(stmtDate)

	stmt, err := uc.cimbService.GetStatementByIncurredDate(ctx, account, stmtDate)
	if err != nil {
		logger.Errorw(
			"msg", "failed to get statement by incurred date", "error", err,
			"period", stmtPeriod, "partner_code", account.PartnerCode,
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to get statement by incurred date").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if stmt == nil {
		logger.Warnw(
			"msg", "no statement data for account", "stmt_period", stmtPeriod,
			"account_id", account.ID, "zalopay_id", account.ZalopayID, "partner_code", account.PartnerCode,
		)
		return nil, errorkit.
			NewError(errorkit.CodeStatementEmptyData, "no statement data").
			WithKind(errorkit.TypeNotFound)
	}

	stmtDetail := stmt.StatementDetail
	stmtInsts := stmt.StatementInsts
	stmtDetail.AttachPeriod(stmtPeriod.Period)

	ctx, err = uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return nil, errorkit.NewError(errorkit.CodeInternalError, "failed to begin transaction").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	stmtID, err := uc.stmtRepo.CreateStatement(ctx, stmtDetail)
	if err != nil {
		logger.Errorf("failed to create statement: %v, statement=%+v", err, stmt)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to create statement").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	stmtDetail.AttachID(stmtID)
	stmtInsts.FulfillInstallments(stmtID)

	for _, inst := range stmtInsts {
		if err = uc.stmtRepo.CreateInstallment(ctx, inst); err != nil {
			logger.Errorf("failed to create statement installment: %v, installment=%+v", err, inst)
			return nil, errorkit.
				NewError(errorkit.CodeRepositoryError, "failed to create statement installment").
				WithCause(err).WithKind(errorkit.TypeRepository)
		}
	}

	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("failed to commit transaction: %v", err)
		return nil, errorkit.NewError(errorkit.CodeInternalError, "failed to commit transaction").WithCause(err)
	}

	return stmtDetail, nil
}

func (uc *Usecase) SyncCreateAccountStatementLatest(ctx context.Context, account *model.Account) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)
	stmtPeriod := uc.getCurrentStatementPeriod()

	stmt, err := uc.cimbService.GetStatementByIncurredDate(ctx, account, stmtPeriod.IncurredDate)
	if err != nil {
		logger.Errorw(
			"msg", "failed to get statement by incurred date", "error", err,
			"period", stmtPeriod, "partner_code", account.PartnerCode,
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to get statement by incurred date").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if stmt == nil {
		logger.Warnw(
			"msg", "no statement data for account",
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
			"stmt_period", stmtPeriod, "partner_code", account.PartnerCode)
		return nil, errorkit.
			NewError(errorkit.CodeStatementEmptyData, "no statement data").
			WithKind(errorkit.TypeNotFound)
	}

	stmtDetail := stmt.StatementDetail
	stmtInsts := stmt.StatementInsts
	stmtDetail.AttachPeriod(stmtPeriod.Period)

	ctx, err = uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("failed to begin transaction: %v", err)
		return nil, errorkit.NewError(errorkit.CodeInternalError, "failed to begin transaction").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	stmtID, err := uc.stmtRepo.CreateStatement(ctx, stmtDetail)
	if err != nil {
		logger.Errorf("failed to create statement: %v, statement=%+v", err, stmt)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to create statement").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	stmtDetail.AttachID(stmtID)
	stmtInsts.FulfillInstallments(stmtID)

	for _, inst := range stmtInsts {
		if err = uc.stmtRepo.CreateInstallment(ctx, inst); err != nil {
			logger.Errorf("failed to create statement installment: %v, installment=%+v", err, inst)
			return nil, errorkit.
				NewError(errorkit.CodeRepositoryError, "failed to create statement installment").
				WithCause(err).WithKind(errorkit.TypeRepository)
		}
	}
	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("failed to commit transaction: %v", err)
		return nil, errorkit.NewError(errorkit.CodeInternalError, "failed to commit transaction").WithCause(err)
	}

	return stmtDetail, nil
}

func (uc *Usecase) SyncUpdateAccountStatementByIncrDate(ctx context.Context,
	zalopayID, accountID int64, stmtDate time.Time) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)
	stmtPeriod := uc.getStatementPeriodByIncurredDate(stmtDate)
	stmtDateStr := stmtDate.Format("2006-01-02")

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(statementSyncUpdateLockKeyPattern, accountID, stmtDateStr))
	if err != nil {
		logger.Errorf("failed to generate lock key: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLock.AcquireStatementSyncing(ctx, lockKey); err != nil {
		logger.Warnf("failed to acquire lock for statement syncing: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "failed to acquire lock for statement syncing").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLock.Release(ctx, lockKey)

	account, err := uc.acctService.GetActiveAccountByID(ctx, zalopayID, accountID)
	if err != nil {
		logger.Errorf("failed to get account by id: %v, zalopay_id=%d, account_id=%d", err, zalopayID, accountID)
		return nil, errorkit.
			NewError(errorkit.CodeCallAccountFailed, "failed to get account by id").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if !account.IsActive {
		return nil, errorkit.
			NewError(errorkit.CodeAccountNotActive, "account is inactive").
			WithKind(errorkit.TypeNotFound)
	}

	stmtData, err := uc.cimbService.GetStatementByIncurredDate(ctx, account, stmtDate)
	if err != nil {
		logger.Errorw(
			"msg", "failed to get statement by incurred date", "error", err,
			"period", stmtPeriod, "partner_code", account.PartnerCode,
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to get statement by incurred date").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	stmtDetail := stmtData.StatementDetail
	stmtDetail.AttachPeriod(stmtPeriod.Period)

	if err = uc.stmtRepo.UpdateStatementOutstandingByIncurDate(ctx, stmtDetail); err != nil {
		logger.Errorf("failed to update statement outstanding by incurred date: %v, statement=%+v", err, stmtDetail)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to update statement outstanding by incurred date").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return stmtDetail, nil
}

func (uc *Usecase) TriggerSyncAccountBalanceAfterStatement(
	ctx context.Context, zalopayID, accountID int64, stmtDate time.Time) error {
	logger := uc.logger.WithContext(ctx)

	params := &dto.SyncAccountBalanceAfterStatementParams{
		ZalopayID:     zalopayID,
		AccountID:     accountID,
		StatementDate: stmtDate,
	}
	err := uc.jobTaskMgmt.ExecuteSyncAccountBalanceAfterStatementTask(ctx, params)
	if err != nil {
		logger.Errorw("msg", "failed to execute sync account balance after statement task",
			"zalopay_id", zalopayID, "account_id", accountID,
			"stmt_date", stmtDate, "error", err,
		)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to execute sync account balance after statement task").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	return nil
}

func (uc *Usecase) TriggerSyncLatestStatement(ctx context.Context, zalopayID, accountID int64) error {
	logger := uc.logger.WithContext(ctx)
	retryer := retry.NewRetry(3, time.Second*5, nil)

	err := retryer.Execute(ctx, func() error {
		params := &dto.SyncLatestStatementParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}
		if err := uc.jobTaskMgmt.ExecuteSyncLatestStatementTask(ctx, params); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorw("msg", "failed to execute sync latest statement task",
			"zalopay_id", zalopayID, "account_id", accountID,
			"error", err,
		)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to execute sync latest statement task").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	return nil
}

func (uc *Usecase) GetStatementSyncBatchInfoByPeriod(ctx context.Context,
	stmtPeriod *model.StatementPeriod) ([]*model.StatementSyncInfo, error) {
	logger := uc.logger.WithContext(ctx)

	syncInfos, err := uc.stmtSyncerRepo.GetSyncerBatchByPeriodAndPartner(ctx, stmtPeriod, partner.PartnerCIMB)
	if err != nil {
		logger.Errorf("failed to get syncer batch by period and partner: %v, period=%+v", err, stmtPeriod)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to get syncer batch by period and partner").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return syncInfos, nil
}

func (uc *Usecase) SummarizeStatementSyncBatches(
	syncInfos []*model.StatementSyncInfo) *model.StatementSyncSummary {
	result := &model.StatementSyncSummary{
		Success: []*model.StatementSyncInfo{},
		Failed:  []*model.StatementSyncInfo{},
		Pending: []*model.StatementSyncInfo{},
		Running: []*model.StatementSyncInfo{},
	}
	for _, syncInfo := range syncInfos {
		switch syncInfo.Status {
		case model.StatementSyncStatusSuccess:
			result.Success = append(result.Success, syncInfo)
		case model.StatementSyncStatusFailed:
			result.Failed = append(result.Failed, syncInfo)
		case model.StatementSyncStatusPending:
			result.Pending = append(result.Pending, syncInfo)
		case model.StatementSyncStatusRunning:
			result.Running = append(result.Running, syncInfo)
		}
	}
	return result.EvaluateState()
}

func (uc *Usecase) BuildStatementSyncInfoFromBatch(
	batch *model.StatementSyncBatch,
	syncStats *model.StatementSyncStats) *model.StatementSyncInfo {
	syncStatus := model.StatementSyncStatusSuccess
	if syncStats.TotalFailed > 0 {
		syncStatus = model.StatementSyncStatusFailed
	}

	return &model.StatementSyncInfo{
		ID:            batch.RefSyncID,
		ExecID:        batch.RefExecID,
		Period:        batch.StmtPeriod.Period,
		Status:        syncStatus,
		SyncStats:     *syncStats,
		PartnerCode:   batch.PartnerCode,
		StatementDate: batch.StmtPeriod.IncurredDate,
	}
}

func (uc *Usecase) BuildStatementSyncBatchFromInfo(syncInfo *model.StatementSyncInfo) *model.StatementSyncBatch {
	syncStats := syncInfo.SyncStats
	stmtPeriod := uc.getStatementPeriodByIncurredDate(syncInfo.StatementDate)
	return &model.StatementSyncBatch{
		BatchNo:     syncInfo.BatchNo,
		QueryOffset: cast.ToInt64(syncStats.QueryOffset),
		TotalItems:  syncStats.TotalItems,
		RefExecID:   syncInfo.ExecID,
		RefSyncID:   syncInfo.ID,
		StmtPeriod:  stmtPeriod,
		PartnerCode: syncInfo.PartnerCode,
	}
}

func (uc *Usecase) ListStatementSyncBatchesFromInfos(
	syncInfos []*model.StatementSyncInfo) []*model.StatementSyncBatch {
	var batches []*model.StatementSyncBatch
	for _, syncInfo := range syncInfos {
		batch := uc.BuildStatementSyncBatchFromInfo(syncInfo)
		batches = append(batches, batch)
	}
	return batches
}

func (uc *Usecase) SyncStatementsPenalty(ctx context.Context) (*dto.StatementSyncStats, error) {
	logger := uc.logger.WithContext(ctx)

	listStmtCh, err := uc.BuildListStatementSyncPenaltyCh(ctx)
	if err != nil {
		logger.Errorf("failed to build list statement sync penalty channel: %v", err)
		return nil, err
	}
	if listStmtCh == nil {
		logger.Warn("no statement channel for penalty sync")
		return &dto.StatementSyncStats{}, nil
	}

	waitGroup := sync.WaitGroup{}
	waitGroup.Add(workerPenaltySync)
	syncStats := &dto.StatementSyncStats{}
	syncResultCh := make(chan model.StatementPenaltySync, workerPenaltySync)

	for i := 0; i < workerPenaltySync; i++ {
		go func() {
			defer waitGroup.Done()
			uc.workerSyncStatementPenalty(ctx, listStmtCh, syncResultCh)
		}()
	}

	go func() {
		waitGroup.Wait()
		close(syncResultCh)
	}()

	for result := range syncResultCh {
		syncStats.TotalItems++
		if result.Error == nil && result.Data != nil {
			syncStats.TotalSuccess++
			logger.Infof("sync statement penalty success, statement=%+v", result.Data)
			continue
		}
		syncStats.TotalFailed++
		syncStats.ListFailedIDs = append(syncStats.ListFailedIDs, result.Params.ID)
		logger.Errorf("sync statement penalty failed: %v, params=%+v", result.Error, result.Params)
	}

	return syncStats, nil
}

func (uc *Usecase) workerSyncStatementPenalty(
	ctx context.Context,
	listStmtCh <-chan *model.Statement,
	syncResultCh chan<- model.StatementPenaltySync) {
	retryer := retry.NewRetry(3, 5*time.Second, nil)

	var err error
	var data *model.Statement

	for stmt := range listStmtCh {
		err = retryer.Execute(ctx, func() error {
			data, err = uc.SyncUpdateFullStatementByInfo(ctx, stmt)
			return err
		})
		syncResultCh <- model.StatementPenaltySync{
			Error:  err,
			Data:   data,
			Params: stmt,
		}
	}
}

func (uc *Usecase) SyncUpdateFullStatementByInfo(ctx context.Context, stmt *model.Statement) (*model.Statement, error) {
	logger := uc.logger.WithContext(ctx)
	accountID := stmt.AccountID
	zalopayID := stmt.ZalopayID
	stmtDate := stmt.IncurredDate
	stmtDateStr := stmtDate.Format("2006-01-02")

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(statementSyncUpdateLockKeyPattern, accountID, stmtDateStr))
	if err != nil {
		logger.Errorf("failed to generate lock key: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLock.AcquireStatementSyncing(ctx, lockKey); err != nil {
		logger.Warnf("failed to acquire lock for statement syncing: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "failed to acquire lock for statement syncing").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLock.Release(ctx, lockKey)

	account, err := uc.acctService.GetActiveAccountByID(ctx, zalopayID, accountID)
	if err != nil {
		logger.Errorf("failed to get account by id: %v, zalopay_id=%d, account_id=%d", err, zalopayID, accountID)
		return nil, errorkit.
			NewError(errorkit.CodeCallAccountFailed, "failed to get account by id").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	stmtData, err := uc.cimbService.GetStatementByIncurredDate(ctx, account, stmtDate)
	if err != nil {
		logger.Errorw(
			"msg", "failed to get statement by incurred date", "error", err,
			"stmt_date", stmtDate, "partner_code", account.PartnerCode,
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "failed to get statement by incurred date").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	curStmtPeriod := uc.getCurrentStatementPeriod()
	reqStmtPeriod := uc.getStatementPeriodByIncurredDate(stmtDate)
	updStmtDetail := stmtData.StatementDetail.AttachID(stmt.ID).AttachPeriod(stmt.Period)

	// TODO: Temporary implement to calculate penalty from CIMB outstanding, will be replaced by new API
	if curStmtPeriod.Period == reqStmtPeriod.Period {
		stmtOuts, cErr := uc.cimbService.GetStatementDueOutstanding(ctx, account)
		if cErr != nil {
			logger.Errorw("msg", "failed to get statement due outstanding", "error", cErr)
			return nil, errorkit.
				NewError(errorkit.CodeCallCIMBFailed, "failed to get statement due outstanding").
				WithCause(err).WithKind(errorkit.TypeRemoteCall)
		}
		updStmtDetail.EvalPenaltyFromCIMBOutstanding(stmtOuts.OutstandingBalance)
	}

	if err = uc.stmtRepo.UpdateStatementOutstandingAndPenaltyByID(ctx, updStmtDetail); err != nil {
		logger.Errorf("failed to update statement outstanding and penalty by id: %v, statement=%+v", err, updStmtDetail)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to update statement outstanding and penalty by id").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return updStmtDetail, nil
}

func (uc *Usecase) MockStatementDate(date time.Time) error {
	err := uc.mocking.SetStatementDate(context.Background(), date)
	if err != nil {
		return err
	}
	return nil
}

func (uc *Usecase) MockStatementDateOverdue(date time.Time) error {
	err := uc.mocking.SetStatementDateOverdue(context.Background(), date)
	if err != nil {
		return err
	}
	return nil
}

func (uc *Usecase) getCurrentStatementPeriod() *model.StatementPeriod {
	now := uc.timeNow()
	year, month, day := now.Date()

	if slices.Contains([]string{"dev", "qc"}, uc.env) {
		stmtDateMock, err := uc.mocking.GetStatementDate()
		if err == nil && !stmtDateMock.IsZero() {
			now = stmtDateMock
			year, month, day = stmtDateMock.Date()
		}
	}

	// Default statement month is the previous month
	stmtYear := year
	stmtMonth := month

	if day < statementDay {
		stmtMonth = stmtMonth - 1
		if month == time.January {
			stmtMonth = time.December
			stmtYear = stmtYear - 1
		}
	}

	monthInt := int(stmtMonth)
	stmtPeriod := int32(stmtYear*100 + monthInt)
	stmtIncrDate := time.Date(stmtYear, stmtMonth, statementDay, 0, 0, 0, 0, time.Local)

	return &model.StatementPeriod{
		Year:         stmtYear,
		Month:        monthInt,
		Period:       stmtPeriod,
		IncurredDate: stmtIncrDate,
	}
}

func (uc *Usecase) getStatementPeriodByIncurredDate(incurredDate time.Time) *model.StatementPeriod {
	year, month, _ := incurredDate.Date()
	monthInt := int(month)
	stmtPeriod := int32(year*100 + monthInt)
	stmtIncurDate := time.Date(year, month, statementDay, 0, 0, 0, 0, time.Local)

	return &model.StatementPeriod{
		Year:         year,
		Month:        monthInt,
		Period:       stmtPeriod,
		IncurredDate: stmtIncurDate,
	}
}

func generateBatchExecID(stmtDate time.Time, batchNo int) string {
	const pattern = "sync.batch.%s.%s"
	return fmt.Sprintf(pattern, stmtDate.Format("20060102"), strconv.Itoa(batchNo))
}

func validateSyncInfoID(syncInfo *model.StatementSyncInfo) error {
	if syncInfo.ID == 0 && syncInfo.ExecID == "" {
		return errors.New("sync info ID and exec ID are required")
	}
	return nil
}
