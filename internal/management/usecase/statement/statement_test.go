package statement

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	transaction_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocking"
	"go.uber.org/mock/gomock"
)

type StatementTestSuite struct {
	suite.Suite
	ctrl               *gomock.Controller
	mockStmtRepo       *repository_mocks.MockStatementRepo
	mockInstRepo       *repository_mocks.MockInstallmentRepo
	mockTxn            *transaction_mocks.MockTransaction
	mockMocking        *mocking.Mocking
	mockDistLock       *adapter_mocks.MockDistributedLock
	mockRedisKeyGen    *mock.MockRedisKeyGenerator
	mockCIMBSvc        *adapter_mocks.MockCIMBService
	mockAcctSvc        *adapter_mocks.MockAccountService
	mockJobTaskMgmt    *adapter_mocks.MockJobTaskMgmt
	mockStmtSyncerRepo *repository_mocks.MockStatementSyncerRepo
	usecase            *Usecase
}

func TestStatementTestSuite(t *testing.T) {
	suite.Run(t, new(StatementTestSuite))
}

func (s *StatementTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockStmtRepo = repository_mocks.NewMockStatementRepo(s.ctrl)
	s.mockInstRepo = repository_mocks.NewMockInstallmentRepo(s.ctrl)
	s.mockTxn = transaction_mocks.NewMockTransaction(s.ctrl)
	s.mockDistLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.mockRedisKeyGen = mock.NewMockRedisKeyGenerator(s.ctrl)
	s.mockCIMBSvc = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockAcctSvc = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockJobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.mockStmtSyncerRepo = repository_mocks.NewMockStatementSyncerRepo(s.ctrl)
	s.mockMocking = nil

	mockConfig := &config.Management{}

	s.usecase = NewUsecase(
		log.GetLogger(),
		mockConfig,
		s.mockTxn,
		s.mockMocking,
		s.mockDistLock,
		s.mockJobTaskMgmt,
		s.mockRedisKeyGen,
		s.mockCIMBSvc,
		s.mockAcctSvc,
		s.mockStmtRepo,
		s.mockInstRepo,
		s.mockStmtSyncerRepo,
	)
	s.usecase.timeNow = time.Now
}

func (s *StatementTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

// TestTriggerSyncLatestStatement_Success tests successful case
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_Success() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}).
		Return(nil)

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.NoError(err)
}

// TestTriggerSyncLatestStatement_ExecuteTaskError tests error from ExecuteSyncLatestStatementTask
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_ExecuteTaskError() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations - simulate task execution failure
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}).
		Return(errors.New("task execution failed")).
		Times(3) // Retry mechanism will attempt 3 times

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "failed to execute sync latest statement task")
	s.Contains(err.Error(), errorkit.CodeInternalError.String())
}

// TestTriggerSyncLatestStatement_RetrySuccess tests retry mechanism success on second attempt
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_RetrySuccess() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations - fail first time, succeed second time
	gomock.InOrder(
		s.mockJobTaskMgmt.EXPECT().
			ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
				ZalopayID: zalopayID,
				AccountID: accountID,
			}).
			Return(errors.New("temporary failure")),
		s.mockJobTaskMgmt.EXPECT().
			ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
				ZalopayID: zalopayID,
				AccountID: accountID,
			}).
			Return(nil),
	)

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.NoError(err)
}
