package statement

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *StatementTestSuite) TestCheckLatestStatementIncurred() {
	ctx := context.Background()
	mockAccounts := []*model.Account{
		{ID: 1, ZalopayID: 123},
	}
	mockStmt := &model.Statement{
		ID:        1,
		AccountID: 1,
	}

	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, gomock.Any()).
		Return(mockAccounts, nil)

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, mockAccounts[0], gomock.Any()).
		Return(&model.StatementResult{StatementDetail: mockStmt}, nil)

	period, ok, err := s.usecase.CheckLatestStatementIncurred(ctx)

	assert.NoError(s.T(), err)
	assert.True(s.T(), ok)
	assert.NotNil(s.T(), period)
}

func (s *StatementTestSuite) TestCheckLatestStatementIncurred_NoAccounts() {
	ctx := context.Background()

	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, gomock.Any()).
		Return([]*model.Account{}, nil)

	period, ok, err := s.usecase.CheckLatestStatementIncurred(ctx)

	assert.NoError(s.T(), err)
	assert.False(s.T(), ok)
	assert.NotNil(s.T(), period)
}

func (s *StatementTestSuite) TestSyncCreateAccountStatement() {
	ctx := context.Background()
	mockAccount := &model.Account{ID: 1, ZalopayID: 123}
	mockStmt := &model.Statement{ID: 1, AccountID: 1}
	mockStmtData := &model.StatementResult{
		StatementDetail: mockStmt,
		StatementInsts:  model.StatementInstallments{},
	}
	mockDate := time.Now()

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, mockAccount, mockDate).
		Return(mockStmtData, nil)

	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockStmtRepo.EXPECT().CreateStatement(ctx, gomock.Any()).Return(int64(1), nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)

	result, err := s.usecase.SyncCreateAccountStatement(ctx, mockAccount, mockDate)

	assert.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
}

func (s *StatementTestSuite) TestSyncCreateAccountStatementLatest() {
	ctx := context.Background()
	mockAccount := &model.Account{ID: 1, ZalopayID: 123}
	mockStmt := &model.Statement{ID: 1, AccountID: 1}
	mockStmtData := &model.StatementResult{
		StatementDetail: mockStmt,
		StatementInsts:  model.StatementInstallments{},
	}

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, mockAccount, gomock.Any()).
		Return(mockStmtData, nil)

	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockStmtRepo.EXPECT().CreateStatement(ctx, gomock.Any()).Return(int64(1), nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)

	result, err := s.usecase.SyncCreateAccountStatementLatest(ctx, mockAccount)

	assert.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
}

func (s *StatementTestSuite) TestSyncUpdateAccountStatementByIncrDate() {
	ctx := context.Background()
	mockAccount := &model.Account{ID: 1, ZalopayID: 123, IsActive: true}
	mockStmt := &model.Statement{ID: 1, AccountID: 1}
	mockStmtData := &model.StatementResult{
		StatementDetail: mockStmt,
		StatementInsts:  model.StatementInstallments{},
	}
	mockDate := time.Now()

	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(ctx, gomock.Any()).Return(nil)
	s.mockDistLock.EXPECT().Release(ctx, gomock.Any()).Return(nil)

	s.mockAcctSvc.EXPECT().
		GetActiveAccountByID(ctx, int64(123), int64(1)).
		Return(mockAccount, nil)

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, mockAccount, mockDate).
		Return(mockStmtData, nil)

	s.mockStmtRepo.EXPECT().
		UpdateStatementOutstandingByIncurDate(ctx, gomock.Any()).
		Return(nil)

	result, err := s.usecase.SyncUpdateAccountStatementByIncrDate(ctx, 123, 1, mockDate)

	assert.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
}

func (s *StatementTestSuite) TestGetStatementSyncBatchInfoByPeriod() {
	ctx := context.Background()
	mockPeriod := &model.StatementPeriod{
		Period: 202401,
	}
	mockSyncInfos := []*model.StatementSyncInfo{
		{ID: 1, Period: 202401},
	}

	s.mockStmtSyncerRepo.EXPECT().
		GetSyncerBatchByPeriodAndPartner(ctx, mockPeriod, partner.PartnerCIMB).
		Return(mockSyncInfos, nil)

	result, err := s.usecase.GetStatementSyncBatchInfoByPeriod(ctx, mockPeriod)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), mockSyncInfos, result)
}

func (s *StatementTestSuite) TestSummarizeStatementSyncBatches() {
	mockSyncInfos := []*model.StatementSyncInfo{
		{Status: model.StatementSyncStatusSuccess},
		{Status: model.StatementSyncStatusFailed},
		{Status: model.StatementSyncStatusPending},
		{Status: model.StatementSyncStatusRunning},
	}

	result := s.usecase.SummarizeStatementSyncBatches(mockSyncInfos)

	assert.Len(s.T(), result.Success, 1)
	assert.Len(s.T(), result.Failed, 1)
	assert.Len(s.T(), result.Pending, 1)
	assert.Len(s.T(), result.Running, 1)
}

func (s *StatementTestSuite) TestBuildStatementSyncInfoFromBatch() {
	mockBatch := &model.StatementSyncBatch{
		RefSyncID:   1,
		RefExecID:   "exec-1",
		StmtPeriod:  &model.StatementPeriod{Period: 202401},
		PartnerCode: partner.PartnerCIMB,
	}
	mockStats := &model.StatementSyncStats{
		TotalItems:  1,
		TotalSynced: 1,
		TotalFailed: 0,
	}

	result := s.usecase.BuildStatementSyncInfoFromBatch(mockBatch, mockStats)

	assert.Equal(s.T(), mockBatch.RefSyncID, result.ID)
	assert.Equal(s.T(), mockBatch.RefExecID, result.ExecID)
	assert.Equal(s.T(), mockBatch.StmtPeriod.Period, result.Period)
	assert.Equal(s.T(), model.StatementSyncStatusSuccess, result.Status)
}

func (s *StatementTestSuite) TestBuildStatementSyncBatchFromInfo() {
	mockSyncInfo := &model.StatementSyncInfo{
		ID:            1,
		ExecID:        "exec-1",
		Period:        202401,
		StatementDate: time.Now(),
		PartnerCode:   partner.PartnerCIMB,
		SyncStats: model.StatementSyncStats{
			TotalItems: 10,
		},
		BatchNo: 1,
	}

	result := s.usecase.BuildStatementSyncBatchFromInfo(mockSyncInfo)

	assert.Equal(s.T(), mockSyncInfo.ID, result.RefSyncID)
	assert.Equal(s.T(), mockSyncInfo.ExecID, result.RefExecID)
	assert.Equal(s.T(), mockSyncInfo.BatchNo, result.BatchNo)
	assert.Equal(s.T(), mockSyncInfo.SyncStats.TotalItems, result.TotalItems)
}

func (s *StatementTestSuite) TestSyncStatementsPenalty() {
	ctx := context.Background()
	mockStmt := &model.Statement{ID: 1, AccountID: 1}
	dueDateTime := time.Now().AddDate(0, 0, -1)
	currStmtPeriod := s.usecase.getCurrentStatementPeriod()
	expectedStmt := &model.Statement{ID: 1}

	s.mockStmtRepo.EXPECT().
		QueryDueDateByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate).
		Return(dueDateTime, nil)

	gomock.InOrder(
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{expectedStmt}, nil),
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{}, nil),
	)

	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(ctx, gomock.Any()).Return(nil)
	s.mockDistLock.EXPECT().Release(ctx, gomock.Any()).Return(nil)

	s.mockAcctSvc.EXPECT().
		GetActiveAccountByID(ctx, gomock.Any(), gomock.Any()).
		Return(&model.Account{IsActive: true}, nil)

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, gomock.Any(), gomock.Any()).
		Return(&model.StatementResult{StatementDetail: mockStmt}, nil)

	s.mockStmtRepo.EXPECT().
		UpdateStatementOutstandingAndPenaltyByID(ctx, gomock.Any()).
		Return(nil)

	result, err := s.usecase.SyncStatementsPenalty(ctx)

	assert.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
}

func (s *StatementTestSuite) TestSyncUpdateFullStatementByInfo() {
	ctx := context.Background()
	mockStmt := &model.Statement{
		ID:           1,
		AccountID:    1,
		ZalopayID:    123,
		IncurredDate: time.Date(2024, 1, 10, 0, 0, 0, 0, time.Local),
	}
	mockAccount := &model.Account{ID: 1, ZalopayID: 123, IsActive: true}

	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(ctx, gomock.Any()).Return(nil)
	s.mockDistLock.EXPECT().Release(ctx, gomock.Any()).Return(nil)

	s.mockAcctSvc.EXPECT().
		GetActiveAccountByID(ctx, mockStmt.ZalopayID, mockStmt.AccountID).
		Return(mockAccount, nil)

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, mockAccount, mockStmt.IncurredDate).
		Return(&model.StatementResult{StatementDetail: mockStmt}, nil)

	s.mockStmtRepo.EXPECT().
		UpdateStatementOutstandingAndPenaltyByID(ctx, gomock.Any()).
		Return(nil)

	result, err := s.usecase.SyncUpdateFullStatementByInfo(ctx, mockStmt)

	assert.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
}

func (s *StatementTestSuite) TestListStatementSyncBatchesFromInfos() {
	syncInfos := []*model.StatementSyncInfo{
		{
			ID:            1,
			ExecID:        "exec-1",
			BatchNo:       1,
			Period:        202401,
			PartnerCode:   partner.PartnerCIMB,
			StatementDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
			SyncStats: model.StatementSyncStats{
				TotalItems: 10,
			},
		},
		{
			ID:            2,
			ExecID:        "exec-2",
			BatchNo:       2,
			Period:        202401,
			PartnerCode:   partner.PartnerCIMB,
			StatementDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
			SyncStats: model.StatementSyncStats{
				TotalItems: 20,
			},
		},
	}

	result := s.usecase.ListStatementSyncBatchesFromInfos(syncInfos)

	assert.Len(s.T(), result, 2)
	assert.Equal(s.T(), syncInfos[0].BatchNo, result[0].BatchNo)
	assert.Equal(s.T(), syncInfos[1].BatchNo, result[1].BatchNo)
}

func (s *StatementTestSuite) TestGetCurrentStatementPeriod() {
	result := s.usecase.getCurrentStatementPeriod()

	now := time.Now()
	year, month, day := now.Date()

	expectedMonth := int(month)
	if day < statementDay {
		expectedMonth = expectedMonth - 1
		if expectedMonth == 0 {
			expectedMonth = 12
			year--
		}
	}

	assert.Equal(s.T(), year, result.Year)
	assert.Equal(s.T(), expectedMonth, result.Month)
	assert.Equal(s.T(), int32(year*100+expectedMonth), result.Period)
}

func (s *StatementTestSuite) TestGetCurrentStatementPeriodForFirstStatementOfYear() {
	s.usecase.timeNow = func() time.Time {
		return time.Date(2025, 1, 10, 0, 0, 0, 0, time.Local)
	}
	result := s.usecase.getCurrentStatementPeriod()

	assert.Equal(s.T(), 2025, result.Year)
	assert.Equal(s.T(), 01, result.Month)
	assert.Equal(s.T(), int32(202501), result.Period)
}

func (s *StatementTestSuite) TestGetStatementPeriodByIncurredDate() {
	incurredDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local)

	result := s.usecase.getStatementPeriodByIncurredDate(incurredDate)

	assert.Equal(s.T(), 2024, result.Year)
	assert.Equal(s.T(), 1, result.Month)
	assert.Equal(s.T(), int32(202401), result.Period)
	assert.Equal(s.T(), time.Date(2024, 1, statementDay, 0, 0, 0, 0, time.Local), result.IncurredDate)
}

func (s *StatementTestSuite) TestGenerateBatchExecID() {
	stmtDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local)
	batchNo := 1

	result := generateBatchExecID(stmtDate, batchNo)

	assert.Equal(s.T(), "sync.batch.20240115.1", result)
}

func (s *StatementTestSuite) TestValidateSyncInfoID() {
	tests := []struct {
		name     string
		syncInfo *model.StatementSyncInfo
		wantErr  bool
	}{
		{
			name: "valid with ID",
			syncInfo: &model.StatementSyncInfo{
				ID: 1,
			},
			wantErr: false,
		},
		{
			name: "valid with ExecID",
			syncInfo: &model.StatementSyncInfo{
				ExecID: "exec-1",
			},
			wantErr: false,
		},
		{
			name: "invalid - no ID and ExecID",
			syncInfo: &model.StatementSyncInfo{
				ID:     0,
				ExecID: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			err := validateSyncInfoID(tt.syncInfo)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func (s *StatementTestSuite) TestListAccountsForStatementSync() {
	// Arrange
	ctx := context.Background()
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}

	// Mock first batch of accounts
	firstBatch := []*model.Account{
		{ID: 1, ZalopayID: 101},
		{ID: 2, ZalopayID: 102},
	}
	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: int64(0),
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return(firstBatch, nil)

	// Mock second batch of accounts
	secondBatch := []*model.Account{
		{ID: 3, ZalopayID: 103},
		{ID: 4, ZalopayID: 104},
	}
	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: int64(2),
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return(secondBatch, nil)

	// Mock empty final batch to end pagination
	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: int64(4),
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return([]*model.Account{}, nil)

	// Act
	result, err := s.usecase.ListAccountsForStatementSync(ctx, stmtPeriod)

	// Assert
	s.NoError(err)
	s.Len(result, 4)
	s.Equal(int64(1), result[0].ID)
	s.Equal(int64(2), result[1].ID)
	s.Equal(int64(3), result[2].ID)
	s.Equal(int64(4), result[3].ID)
}

func (s *StatementTestSuite) TestListAccountsForStatementSync_Error() {
	// Arrange
	ctx := context.Background()
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}

	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: int64(0),
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return(nil, errors.New("service error"))

	// Act
	result, err := s.usecase.ListAccountsForStatementSync(ctx, stmtPeriod)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "failed to list users for statement sync")
}

func (s *StatementTestSuite) TestListAccountsForStatementSyncByCursor_Success() {
	// Arrange
	ctx := context.Background()
	cursor := int64(10)
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}
	expectedAccounts := []*model.Account{
		{ID: 11, ZalopayID: 111},
		{ID: 12, ZalopayID: 112},
	}

	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: cursor,
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return(expectedAccounts, nil)

	// Act
	result, err := s.usecase.ListAccountsForStatementSyncByCursor(ctx, cursor, stmtPeriod)

	// Assert
	s.NoError(err)
	s.Equal(expectedAccounts, result)
}

func (s *StatementTestSuite) TestListAccountsForStatementSyncByCursor_Error() {
	// Arrange
	ctx := context.Background()
	cursor := int64(10)
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}

	s.mockAcctSvc.EXPECT().
		ListAccountForStmtSync(ctx, &dto.AccountForStatementParams{
			FromAccountID: cursor,
			TotalLimit:    syncStmtAccBatchSize,
			PartnerCodes:  []partner.PartnerCode{partner.PartnerCIMB},
			StatementDate: stmtPeriod.IncurredDate,
		}).
		Return(nil, errors.New("service error"))

	// Act
	result, err := s.usecase.ListAccountsForStatementSyncByCursor(ctx, cursor, stmtPeriod)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "failed to list users for statement sync")
}

func (s *StatementTestSuite) TestSplitAccountsIntoBatches() {
	// Arrange
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}

	// Test case 1: Single batch (less than batch size)
	accounts1 := []*model.Account{
		{ID: 1, ZalopayID: 101},
		{ID: 2, ZalopayID: 102},
		{ID: 3, ZalopayID: 103},
	}

	batches1 := s.usecase.SplitAccountsIntoBatches(stmtPeriod, accounts1)

	// Assert for single batch
	s.Len(batches1, 1)
	s.Equal(1, batches1[0].BatchNo)
	s.Equal(int64(0), batches1[0].QueryOffset) // First batch should have offset 0
	s.Equal(3, batches1[0].TotalItems)
	s.Equal(accounts1, batches1[0].Accounts)

	// Test case 2: Multiple batches
	// Create a larger array of accounts to test multiple batches
	accounts2 := make([]*model.Account, syncStmtAccBatchSize*2+10) // 2 full batches + partial batch
	for i := 0; i < len(accounts2); i++ {
		accounts2[i] = &model.Account{ID: int64(i + 1), ZalopayID: int64(i + 101)}
	}

	batches2 := s.usecase.SplitAccountsIntoBatches(stmtPeriod, accounts2)

	// Assert for multiple batches
	s.Len(batches2, 3) // Should have 3 batches

	// First batch
	s.Equal(1, batches2[0].BatchNo)
	s.Equal(int64(0), batches2[0].QueryOffset) // First batch should have offset 0
	s.Equal(syncStmtAccBatchSize, batches2[0].TotalItems)

	// Second batch
	s.Equal(2, batches2[1].BatchNo)
	s.Equal(accounts2[syncStmtAccBatchSize-1].ID, batches2[1].QueryOffset) // Should use last ID from previous batch
	s.Equal(syncStmtAccBatchSize, batches2[1].TotalItems)

	// Third batch
	s.Equal(3, batches2[2].BatchNo)
	s.Equal(accounts2[syncStmtAccBatchSize*2-1].ID, batches2[2].QueryOffset) // Should use last ID from previous batch
	s.Equal(10, batches2[2].TotalItems)
}

func (s *StatementTestSuite) TestFulfillAccountsForSyncIntoBatches() {
	// Arrange
	ctx := context.Background()
	accounts := []*model.Account{
		{ID: 10, ZalopayID: 110},
		{ID: 11, ZalopayID: 111},
		{ID: 12, ZalopayID: 112},
	}
	stmtPeriod := &model.StatementPeriod{
		Year:         2024,
		Month:        1,
		Period:       202401,
		IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
	}

	// Convert accounts to batches first
	batches := s.usecase.SplitAccountsIntoBatches(stmtPeriod, accounts)
	s.mockAcctSvc.EXPECT().ListAccountForStmtSync(ctx, gomock.Any()).Return(accounts, nil)

	// Then fulfill the batches
	result, _ := s.usecase.FulfillAccountsForSyncIntoBatches(ctx, batches)

	// Assert
	s.Len(result, 1)
	s.Equal(accounts, result[0].Accounts)
	s.Equal(stmtPeriod, result[0].StmtPeriod)
}

func (s *StatementTestSuite) TestStoreStatementSyncBatches() {
	// Arrange
	ctx := context.Background()
	batches := []*model.StatementSyncBatch{
		{
			BatchNo: 1,
			Accounts: []*model.Account{
				{ID: 10, ZalopayID: 110},
				{ID: 11, ZalopayID: 111},
			},
			StmtPeriod: &model.StatementPeriod{
				Year:         2024,
				Month:        1,
				Period:       202401,
				IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
			},
			PartnerCode: partner.PartnerCIMB,
			TotalItems:  2,
		},
	}
	syncInfo := &model.StatementSyncInfo{
		ExecID:  generateBatchExecID(batches[0].StmtPeriod.IncurredDate, batches[0].BatchNo),
		BatchNo: batches[0].BatchNo,
		Period:  batches[0].StmtPeriod.Period,
		Status:  model.StatementSyncStatusPending,
		SyncStats: model.StatementSyncStats{
			TotalItems:  batches[0].TotalItems,
			TotalSynced: 0,
			TotalFailed: 0,
			QueryOffset: "0",
		},
		PartnerCode:   batches[0].PartnerCode,
		StatementDate: batches[0].StmtPeriod.IncurredDate,
	}

	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)

	s.mockStmtSyncerRepo.EXPECT().CreateSyncInfo(ctx, syncInfo).Return(int64(1), nil)

	// Act
	err := s.usecase.StoreStatementSyncBatches(ctx, batches)

	// Assert
	s.NoError(err)
}

func (s *StatementTestSuite) TestStoreStatementSyncBatches_Error() {
	// Arrange
	ctx := context.Background()
	batches := []*model.StatementSyncBatch{
		{
			Accounts: []*model.Account{
				{ID: 10, ZalopayID: 110},
			},
			StmtPeriod: &model.StatementPeriod{
				Year:         2024,
				Month:        1,
				Period:       202401,
				IncurredDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
			},
			PartnerCode: partner.PartnerCIMB,
			TotalItems:  1,
		},
	}

	syncInfo := &model.StatementSyncInfo{
		ExecID:  generateBatchExecID(batches[0].StmtPeriod.IncurredDate, batches[0].BatchNo),
		BatchNo: batches[0].BatchNo,
		Period:  batches[0].StmtPeriod.Period,
		Status:  model.StatementSyncStatusPending,
		SyncStats: model.StatementSyncStats{
			TotalItems:  batches[0].TotalItems,
			TotalSynced: 0,
			TotalFailed: 0,
			QueryOffset: "0",
		},
		PartnerCode:   batches[0].PartnerCode,
		StatementDate: batches[0].StmtPeriod.IncurredDate,
	}

	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)

	s.mockStmtSyncerRepo.EXPECT().CreateSyncInfo(ctx, syncInfo).Return(int64(-1), errors.New("db error"))

	// Act
	err := s.usecase.StoreStatementSyncBatches(ctx, batches)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "code: REPOSITORY_ERROR, message: failed to create statement sync infos")
}

func (s *StatementTestSuite) TestUpdateSyncInfoStatusAndStats() {
	// Arrange
	ctx := context.Background()
	syncInfo := &model.StatementSyncInfo{
		ID:     1,
		Status: model.StatementSyncStatusRunning,
		SyncStats: model.StatementSyncStats{
			TotalItems:  100,
			TotalSynced: 50,
			TotalFailed: 0,
		},
	}

	s.mockStmtSyncerRepo.EXPECT().
		UpdateSyncStatusAndStats(ctx, syncInfo).
		Return(nil)

	// Act
	err := s.usecase.UpdateSyncInfoStatusAndStats(ctx, syncInfo)

	// Assert
	s.NoError(err)
}

func (s *StatementTestSuite) TestUpdateSyncInfoStatusAndStats_Error() {
	// Arrange
	ctx := context.Background()
	syncInfo := &model.StatementSyncInfo{
		ID:     1,
		Status: model.StatementSyncStatusRunning,
		SyncStats: model.StatementSyncStats{
			TotalItems:  100,
			TotalSynced: 50,
			TotalFailed: 0,
		},
	}

	s.mockStmtSyncerRepo.EXPECT().
		UpdateSyncStatusAndStats(ctx, syncInfo).
		Return(errors.New("db error"))

	// Act
	err := s.usecase.UpdateSyncInfoStatusAndStats(ctx, syncInfo)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "code: REPOSITORY_ERROR, message: failed to update statement sync status and stats")
}

func (s *StatementTestSuite) TestUpdateSyncInfoStatus() {
	// Arrange
	ctx := context.Background()
	syncInfo := &model.StatementSyncInfo{
		ID:     1,
		Status: model.StatementSyncStatusRunning,
	}

	s.mockStmtSyncerRepo.EXPECT().
		UpdateSyncStatus(ctx, syncInfo).
		Return(nil)

	// Act
	err := s.usecase.UpdateSyncInfoStatus(ctx, syncInfo)

	// Assert
	s.NoError(err)
}

func (s *StatementTestSuite) TestUpdateSyncInfoStatus_Error() {
	// Arrange
	ctx := context.Background()
	syncInfo := &model.StatementSyncInfo{
		ID:     1,
		Status: model.StatementSyncStatusRunning,
	}

	s.mockStmtSyncerRepo.EXPECT().
		UpdateSyncStatus(ctx, syncInfo).
		Return(errors.New("db error"))

	// Act
	err := s.usecase.UpdateSyncInfoStatus(ctx, syncInfo)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "code: REPOSITORY_ERROR, message: failed to update statement sync status")
}

func (s *StatementTestSuite) TestSyncCreateAccountStatement_GetStatementError() {
	// Arrange
	ctx := context.Background()
	account := &model.Account{
		ID:          1,
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
	}
	stmtDate := time.Now()

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, account, stmtDate).
		Return(nil, errors.New("remote error"))

	// Act
	stmt, err := s.usecase.SyncCreateAccountStatement(ctx, account, stmtDate)

	// Assert
	s.Error(err)
	s.Nil(stmt)
	s.Contains(err.Error(), "code: CALL_CIMB_FAILED, message: failed to get statement by incurred date")
}

func (s *StatementTestSuite) TestSyncCreateAccountStatement_EmptyStatement() {
	// Arrange
	ctx := context.Background()
	account := &model.Account{
		ID:          1,
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
	}
	stmtDate := time.Now()

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, account, stmtDate).
		Return(nil, nil)

	// Act
	stmt, err := s.usecase.SyncCreateAccountStatement(ctx, account, stmtDate)

	// Assert
	s.Error(err)
	s.Nil(stmt)
	s.Contains(err.Error(), "code: STATEMENT_EMPTY_DATA, message: no statement data")
}

func (s *StatementTestSuite) TestSyncCreateAccountStatement_BeginTxError() {
	// Arrange
	ctx := context.Background()
	account := &model.Account{
		ID:          1,
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
	}
	stmtDate := time.Now()
	statement := &model.StatementResult{
		StatementDetail: &model.Statement{},
		StatementInsts:  []*model.StatementInstallment{},
	}

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, account, stmtDate).
		Return(statement, nil)

	s.mockTxn.EXPECT().
		BeginTx(ctx).
		Return(nil, errors.New("tx error"))

	// Act
	stmt, err := s.usecase.SyncCreateAccountStatement(ctx, account, stmtDate)

	// Assert
	s.Error(err)
	s.Nil(stmt)
	s.Contains(err.Error(), "code: INTERNAL_ERROR, message: failed to begin transaction")
}

func (s *StatementTestSuite) TestSyncCreateAccountStatement_CreateStatementError() {
	// Arrange
	ctx := context.Background()
	account := &model.Account{
		ID:          1,
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB,
	}
	stmtDate := time.Now()
	statement := &model.StatementResult{
		StatementDetail: &model.Statement{},
		StatementInsts:  []*model.StatementInstallment{},
	}

	s.mockCIMBSvc.EXPECT().
		GetStatementByIncurredDate(ctx, account, stmtDate).
		Return(statement, nil)

	txCtx := context.Background()
	s.mockTxn.EXPECT().
		BeginTx(ctx).
		Return(txCtx, nil)

	s.mockTxn.EXPECT().
		RollbackTx(txCtx)

	s.mockStmtRepo.EXPECT().
		CreateStatement(txCtx, statement.StatementDetail).
		Return(int64(0), errors.New("db error"))

	// Act
	stmt, err := s.usecase.SyncCreateAccountStatement(ctx, account, stmtDate)

	// Assert
	s.Error(err)
	s.Nil(stmt)
	s.Contains(err.Error(), "code: REPOSITORY_ERROR, message: failed to create statement")
}

// TestTriggerSyncLatestStatement_Success tests successful case
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_Success() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}).
		Return(nil)

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.NoError(err)
}

// TestTriggerSyncLatestStatement_ExecuteTaskError tests error from ExecuteSyncLatestStatementTask
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_ExecuteTaskError() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations - simulate task execution failure
	s.mockJobTaskMgmt.EXPECT().
		ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
			ZalopayID: zalopayID,
			AccountID: accountID,
		}).
		Return(errors.New("task execution failed")).
		AnyTimes() // Allow any number of retries

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.Error(err)
	s.Contains(err.Error(), "failed to execute sync latest statement task")
	s.Contains(err.Error(), errorkit.CodeInternalError.String())
}

// TestTriggerSyncLatestStatement_RetrySuccess tests retry mechanism success on second attempt
func (s *StatementTestSuite) TestTriggerSyncLatestStatement_RetrySuccess() {
	ctx := context.Background()
	zalopayID := int64(12345)
	accountID := int64(67890)

	// Setup mock expectations - fail first time, succeed second time
	gomock.InOrder(
		s.mockJobTaskMgmt.EXPECT().
			ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
				ZalopayID: zalopayID,
				AccountID: accountID,
			}).
			Return(errors.New("temporary failure")),
		s.mockJobTaskMgmt.EXPECT().
			ExecuteSyncLatestStatementTask(ctx, &dto.SyncLatestStatementParams{
				ZalopayID: zalopayID,
				AccountID: accountID,
			}).
			Return(nil),
	)

	// Execute
	err := s.usecase.TriggerSyncLatestStatement(ctx, zalopayID, accountID)

	// Assertions
	s.NoError(err)
}
