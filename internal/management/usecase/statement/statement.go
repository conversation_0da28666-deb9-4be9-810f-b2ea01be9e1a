package statement

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocking"
)

type Usecase struct {
	logger   *log.Helper
	env      string
	txn      _interface.Transaction
	mocking  *mocking.Mocking
	timeNow  func() time.Time
	distLock _interface.DistributedLock

	jobTaskMgmt    _interface.JobTaskMgmt
	cimbService    _interface.CIMBService
	acctService    _interface.AccountService
	stmtRepo       _interface.StatementRepo
	instRepo       _interface.InstallmentRepo
	stmtSyncerRepo _interface.StatementSyncerRepo
	stmtSyncConfig SyncConfig
	redisKeyGen    keygen.RedisKeyGenerator
}

type SyncConfig struct {
	PenaltyAllBufferDays int
}

const (
	statementDay         = 10
	syncPenaltyBatchSize = 100
	syncStmtAccBatchSize = 128
	workerPenaltySync    = 20
	channelPenaltySize   = 20
)

const (
	statementSyncUpdateLockKeyPattern = "statement:syncing:account_%d:stmt_date_%s"
)

func NewUsecase(
	kLogger log.Logger,
	cfg *config.Management,
	txn _interface.Transaction,
	mocking *mocking.Mocking,
	distLock _interface.DistributedLock,
	jobTaskMgmt _interface.JobTaskMgmt,
	redisKeyGen keygen.RedisKeyGenerator,
	cimbService _interface.CIMBService,
	acctService _interface.AccountService,
	stmtRepo _interface.StatementRepo,
	instRepo _interface.InstallmentRepo,
	stmtSyncerRepo _interface.StatementSyncerRepo) *Usecase {
	logging := log.With(kLogger, "usecase", "statement")
	logger := log.NewHelper(logging)
	appEnv := cfg.GetApp().GetEnv()
	stmtSync := cfg.GetStatementSync()
	syncConfig := SyncConfig{PenaltyAllBufferDays: int(stmtSync.GetPenaltyAllBufferDays())}
	return &Usecase{
		txn:            txn,
		env:            appEnv,
		logger:         logger,
		mocking:        mocking,
		timeNow:        time.Now,
		distLock:       distLock,
		jobTaskMgmt:    jobTaskMgmt,
		redisKeyGen:    redisKeyGen,
		cimbService:    cimbService,
		acctService:    acctService,
		instRepo:       instRepo,
		stmtRepo:       stmtRepo,
		stmtSyncerRepo: stmtSyncerRepo,
		stmtSyncConfig: syncConfig,
	}
}
