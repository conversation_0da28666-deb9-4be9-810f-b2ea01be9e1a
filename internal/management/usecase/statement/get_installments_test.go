package statement

import (
	"context"
	"errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
)

func (s *StatementTestSuite) TestGetInstallmentsByUserAndStatementID_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementInstallmentsParams{
		ZalopayID:   123,
		AccountID:   123,
		StatementID: 123,
	}
	stmtInst := &model.StatementInstallment{
		ID:            1,
		PartnerInstID: "inst123",
		OutstandingData: &model.StatementInstallmentOuts{
			TotalOutstanding:     1110,
			OutstandingPrincipal: 1000,
			OutstandingInterest:  100,
			OutstandingPenalty:   10,
		},
	}
	transInst := &model.InstallmentInfo{
		ID:            1,
		PartnerInstID: "inst123",
		ZPTransID:     123,
		TransactionInfo: model.InstallmentTransaction{
			TransDesc: "test transaction",
		},
	}

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByUserAndStatementID(ctx, params.ZalopayID, params.AccountID, params.StatementID).
		Return([]*model.StatementInstallment{stmtInst}, nil)

	s.mockInstRepo.EXPECT().
		ListInstallmentByPartnerIDs(ctx, []string{stmtInst.PartnerInstID}).
		Return([]*model.InstallmentInfo{transInst}, nil)

	// Act
	result, err := s.usecase.GetInstallmentsByUserAndStatementID(ctx, params)

	// Assert
	s.NoError(err)
	s.Len(result, 1)
	s.Equal(transInst.ID, result[0].RefOriInstID)
	s.Equal(transInst.ZPTransID, result[0].RefZPTransID)
	s.Equal(transInst.TransactionInfo.GetTransDesc(), result[0].TransactionRemark)
}

func (s *StatementTestSuite) TestGetInstallmentsByUserAndStatementID_QueryError() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementInstallmentsParams{
		ZalopayID:   123,
		AccountID:   123,
		StatementID: 123,
	}

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByUserAndStatementID(ctx, params.ZalopayID, params.AccountID, params.StatementID).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetInstallmentsByUserAndStatementID(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "get statement installments failed")
}

func (s *StatementTestSuite) TestGetInstallmentsByUserAndStatementID_ListInstallmentError() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementInstallmentsParams{
		ZalopayID:   123,
		AccountID:   123,
		StatementID: 123,
	}
	stmtInst := &model.StatementInstallment{
		ID:            1,
		PartnerInstID: "inst123",
		OutstandingData: &model.StatementInstallmentOuts{
			TotalOutstanding:     1110,
			OutstandingPrincipal: 1000,
			OutstandingInterest:  100,
			OutstandingPenalty:   10,
		},
	}

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByUserAndStatementID(ctx, params.ZalopayID, params.AccountID, params.StatementID).
		Return([]*model.StatementInstallment{stmtInst}, nil)

	s.mockInstRepo.EXPECT().
		ListInstallmentByPartnerIDs(ctx, []string{stmtInst.PartnerInstID}).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetInstallmentsByUserAndStatementID(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "internal error")
}

func (s *StatementTestSuite) TestGetInstallmentsByUserAndStatementID_EmptyInstallments() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementInstallmentsParams{
		ZalopayID:   123,
		AccountID:   123,
		StatementID: 123,
	}
	stmtInst := &model.StatementInstallment{
		ID:            1,
		PartnerInstID: "inst123",
		OutstandingData: &model.StatementInstallmentOuts{
			TotalOutstanding:     1110,
			OutstandingPrincipal: 1000,
			OutstandingInterest:  100,
			OutstandingPenalty:   10,
		},
	}

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByUserAndStatementID(ctx, params.ZalopayID, params.AccountID, params.StatementID).
		Return([]*model.StatementInstallment{stmtInst}, nil)

	s.mockInstRepo.EXPECT().
		ListInstallmentByPartnerIDs(ctx, []string{stmtInst.PartnerInstID}).
		Return([]*model.InstallmentInfo{}, nil)

	// Act
	result, err := s.usecase.GetInstallmentsByUserAndStatementID(ctx, params)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *StatementTestSuite) TestGetInstallmentsByStatementID_Success() {
	// Arrange
	ctx := context.Background()
	stmtID := int64(123)
	expectedInst := &model.StatementInstallment{ID: 1}

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByStatementID(ctx, stmtID).
		Return([]*model.StatementInstallment{expectedInst}, nil)

	// Act
	result, err := s.usecase.GetInstallmentsByStatementID(ctx, stmtID)

	// Assert
	s.NoError(err)
	s.Len(result, 1)
	s.Equal(expectedInst, result[0])
}

func (s *StatementTestSuite) TestGetInstallmentsByStatementID_Error() {
	// Arrange
	ctx := context.Background()
	stmtID := int64(123)

	s.mockStmtRepo.EXPECT().
		QueryInstallmentsByStatementID(ctx, stmtID).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetInstallmentsByStatementID(ctx, stmtID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "get statement installments by statement id failed")
}

func (s *StatementTestSuite) TestFilterDueInstallments() {
	// Arrange
	insts := []*model.StatementInstallment{
		{
			ID: 1,
			OutstandingData: &model.StatementInstallmentOuts{
				TotalOutstanding:     1110,
				OutstandingPrincipal: 1000,
				OutstandingInterest:  100,
				OutstandingPenalty:   10,
			},
		},
		{
			ID: 2,
		},
		{
			ID: 3,
		},
	}

	// Act
	result := filterDueInstallments(insts)

	// Assert
	s.Len(result, 1)
	s.Equal(result[0], insts[0])
}

func (s *StatementTestSuite) TestMergeStatementInstallments() {
	// Arrange
	stmtInsts := []*model.StatementInstallment{
		{
			ID:            1,
			PartnerInstID: "inst1",
		},
		{
			ID:            2,
			PartnerInstID: "inst2",
		},
	}

	transInsts := []*model.InstallmentInfo{
		{
			ID:            101,
			PartnerInstID: "inst1",
			ZPTransID:     101,
			TransactionInfo: model.InstallmentTransaction{
				TransDesc: "trans1",
			},
		},
	}

	// Act
	result := mergeStatementInstallments(stmtInsts, transInsts)

	// Assert
	s.Len(result, 2)
	s.Equal(int64(101), result[0].RefOriInstID)
	s.Equal(int64(101), result[0].RefZPTransID)
	s.Equal("trans1", result[0].TransactionRemark)
	s.Zero(result[1].RefOriInstID)
	s.Empty(result[1].RefZPTransID)
	s.Empty(result[1].TransactionRemark)
}
