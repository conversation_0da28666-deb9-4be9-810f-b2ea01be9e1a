package statement

import (
	"context"
	"errors"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"go.uber.org/mock/gomock"
)

func (s *StatementTestSuite) TestGetStatementByUserAndIncurDate_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID:     123,
		AccountID:     456,
		StatementDate: time.Now(),
	}
	expectedStmt := &model.Statement{ID: 1}

	s.mockStmtRepo.EXPECT().
		QueryStatementByIncurDate(ctx, params.ZalopayID, params.AccountID, params.StatementDate).
		Return(expectedStmt, nil)

	// Act
	result, err := s.usecase.GetStatementByUserAndIncurDate(ctx, params)

	// Assert
	s.NoError(err)
	s.Equal(expectedStmt, result)
}

func (s *StatementTestSuite) TestGetStatementByUserAndIncurDate_NotFound() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID:     123,
		AccountID:     456,
		StatementDate: time.Now(),
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementByIncurDate(ctx, params.ZalopayID, params.AccountID, params.StatementDate).
		Return(nil, model.ErrStatementNotFound)

	// Act
	result, err := s.usecase.GetStatementByUserAndIncurDate(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "statement not found")
}

func (s *StatementTestSuite) TestGetStatementByUserAndIncurDate_Error() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID:     123,
		AccountID:     456,
		StatementDate: time.Now(),
	}

	s.mockStmtRepo.EXPECT().
		QueryStatementByIncurDate(ctx, params.ZalopayID, params.AccountID, params.StatementDate).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetStatementByUserAndIncurDate(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "internal error")
}

func (s *StatementTestSuite) TestGetLatestStatementByUser_Success() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID: 123,
		AccountID: 456,
	}
	expectedStmt := &model.Statement{ID: 1}

	s.mockStmtRepo.EXPECT().
		QueryLatestStatement(ctx, params.ZalopayID, params.AccountID).
		Return(expectedStmt, nil)

	// Act
	result, err := s.usecase.GetLatestStatementByUser(ctx, params)

	// Assert
	s.NoError(err)
	s.Equal(expectedStmt, result)
}

func (s *StatementTestSuite) TestGetLatestStatementByUser_NotFound() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	s.mockStmtRepo.EXPECT().
		QueryLatestStatement(ctx, params.ZalopayID, params.AccountID).
		Return(nil, model.ErrStatementNotFound)

	// Act
	result, err := s.usecase.GetLatestStatementByUser(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "statement not found")
}

func (s *StatementTestSuite) TestGetLatestStatementByUser_Error() {
	// Arrange
	ctx := context.Background()
	params := &dto.QueryStatementParams{
		ZalopayID: 123,
		AccountID: 456,
	}

	s.mockStmtRepo.EXPECT().
		QueryLatestStatement(ctx, params.ZalopayID, params.AccountID).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetLatestStatementByUser(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "get latest statement has error")
}

func (s *StatementTestSuite) TestGetStatementByID_Success() {
	// Arrange
	ctx := context.Background()
	stmtID := int64(123)
	expectedStmt := &model.Statement{ID: stmtID}

	s.mockStmtRepo.EXPECT().
		QueryStatementByID(ctx, stmtID).
		Return(expectedStmt, nil)

	// Act
	result, err := s.usecase.GetStatementByID(ctx, stmtID)

	// Assert
	s.NoError(err)
	s.Equal(expectedStmt, result)
}

func (s *StatementTestSuite) TestGetStatementByID_NotFound() {
	// Arrange
	ctx := context.Background()
	stmtID := int64(123)

	s.mockStmtRepo.EXPECT().
		QueryStatementByID(ctx, stmtID).
		Return(nil, model.ErrStatementNotFound)

	// Act
	result, err := s.usecase.GetStatementByID(ctx, stmtID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "statement not found")
}

func (s *StatementTestSuite) TestGetStatementByID_Error() {
	// Arrange
	ctx := context.Background()
	stmtID := int64(123)

	s.mockStmtRepo.EXPECT().
		QueryStatementByID(ctx, stmtID).
		Return(nil, errors.New("db error"))

	// Act
	result, err := s.usecase.GetStatementByID(ctx, stmtID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "internal error")
}

func (s *StatementTestSuite) TestBuildListStatementSyncPenaltyCh_Success() {
	// Arrange
	ctx := context.Background()
	dueDateTime := time.Now().AddDate(0, 0, -1)
	currStmtPeriod := s.usecase.getCurrentStatementPeriod()
	expectedStmt := &model.Statement{ID: 1}

	s.mockStmtRepo.EXPECT().
		QueryDueDateByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate).
		Return(dueDateTime, nil)

	gomock.InOrder(
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{expectedStmt}, nil),
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{}, nil),
	)

	// Act
	result, err := s.usecase.BuildListStatementSyncPenaltyCh(ctx)

	<-result

	// Assert
	s.NoError(err)
	s.NotNil(result)
}

func (s *StatementTestSuite) TestBuildListStatementSyncPenaltyCh_QueryDueDateError() {
	// Arrange
	ctx := context.Background()
	currStmtPeriod := s.usecase.getCurrentStatementPeriod()

	s.mockStmtRepo.EXPECT().
		QueryDueDateByIncurDateAndPartner(ctx, partner.PartnerCIMB, currStmtPeriod.IncurredDate).
		Return(time.Time{}, errors.New("db error"))

	// Act
	result, err := s.usecase.BuildListStatementSyncPenaltyCh(ctx)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "get current statement due date has error")
}

func (s *StatementTestSuite) TestGetStatementsSyncPenaltyByIncurDateCh_Success() {
	// Arrange
	ctx := context.Background()
	stmtPeriod := s.usecase.getCurrentStatementPeriod()
	expectedStmt := &model.Statement{ID: 1}

	gomock.InOrder(
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, stmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{expectedStmt}, nil).Times(1),
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, stmtPeriod.IncurredDate, gomock.Any()).
			Return([]*model.Statement{}, nil).Times(1),
	)

	// Act
	result, err := s.usecase.GetStatementsSyncPenaltyByIncurDateCh(ctx, stmtPeriod.IncurredDate)

	<-result

	// Assert
	s.NoError(err)
	s.NotNil(result)
}

func (s *StatementTestSuite) TestGetOutsStatementsSyncPenaltyByIncurDateCh_Success() {
	// Arrange
	ctx := context.Background()
	incurredDate := time.Now()
	expectedStmt := &model.Statement{ID: 1}

	gomock.InOrder(
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, incurredDate, gomock.Any()).
			Return([]*model.Statement{expectedStmt}, nil),
		s.mockStmtRepo.EXPECT().
			QueryStatementsByIncurDateAndPartner(ctx, partner.PartnerCIMB, incurredDate, gomock.Any()).
			Return([]*model.Statement{}, nil),
	)

	// Act
	result, err := s.usecase.GetOutsStatementsSyncPenaltyByIncurDateCh(ctx, incurredDate)

	<-result

	// Assert
	s.NoError(err)
	s.NotNil(result)
}
