package _interface

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

//go:generate mockgen --destination=../mocks/transaction/transaction.go --package=transaction_mocks . Transaction
type Transaction interface {
	IsInTx(ctx context.Context) bool
	WithTx(ctx context.Context, exec func(ctx context.Context) error) error
	BeginTx(ctx context.Context) (context.Context, error)
	BeginOrReuseTx(ctx context.Context) (context.Context, error)
	CommitTx(ctx context.Context) error
	RollbackTx(ctx context.Context) error
	IsTxActive(ctx context.Context) bool
}

//go:generate mockgen --destination=../mocks/adapter/dist_lock.go --package=adapter_mocks . DistributedLock
type DistributedLock interface {
	// Acquire attempts to acquire the lock for the specified resource.
	// It returns an error if the lock could not be acquired.
	Acquire(ctx context.Context, resource string, ttl time.Duration) error

	// Release releases the lock for the specified resource.
	// It returns an error if the lock could not be released.
	Release(ctx context.Context, resource string) error

	// IsLocked checks if the specified resource is currently locked.
	// It returns a boolean indicating the lock status and an error if the status could not be determined.
	IsLocked(ctx context.Context, resource string) (bool, error)

	AcquireStatementSyncing(ctx context.Context, resource string) error
	AcquireInstallmentSyncing(ctx context.Context, resource string) error
	AcquireInstallmentRepaysCreation(ctx context.Context, resource string) error
}

//go:generate mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
type CIMBService interface {
	QueryPlanOptions(ctx context.Context, zalopayID, orderAmount int64) ([]*model.PartnerPlanOption, error)
	QueryInstallmentByID(ctx context.Context, zalopayID int64, instID string) (*model.PartnerCIMBInst, error)
	QueryEarlyDischargeByID(ctx context.Context, zalopayID int64, instID string) (*model.PartnerEarlyDischarge, error)
	QueryInstallmentByTransID(ctx context.Context, zalopayID int64, transID int64) (*model.PartnerCIMBInst, error)
	GetStatementByIncurredDate(ctx context.Context, account *model.Account, stmtDate time.Time) (*model.StatementResult, error)
	GetStatementDueOutstanding(ctx context.Context, account *model.Account) (*model.StatementOutstanding, error)
	// Deprecated: use GetStatementByIncurredDate instead
	CheckStatementsEligibleByIncurredDate(ctx context.Context, date time.Time) (bool, error)
}

//go:generate mockgen --destination=../mocks/adapter/account_service.go --package=adapter_mocks . AccountService
type AccountService interface {
	GetActiveAccountByID(ctx context.Context, zalopayID, accountID int64) (*model.Account, error)
	GetActiveAccountByPartner(ctx context.Context, zalopayID int64, partnerCode partner.PartnerCode) (*model.Account, error)
	ListAccountForStmtSync(ctx context.Context, params *dto.AccountForStatementParams) ([]*model.Account, error)
}

//go:generate mockgen --destination=../mocks/repository/statement_repo.go --package=repository_mocks . StatementRepo
type StatementRepo interface {
	CreateStatement(ctx context.Context, statement *model.Statement) (stmtID int64, err error)
	CreateInstallment(ctx context.Context, installment *model.StatementInstallment) error
	QueryLatestStatement(ctx context.Context, zalopayID, accountID int64) (*model.Statement, error)
	QueryStatementByID(ctx context.Context, stmtID int64) (*model.Statement, error)
	QueryStatementByIncurDate(ctx context.Context, zalopayID, accountID int64, incurredDate time.Time) (*model.Statement, error)
	QueryStatementsByIncurDateRange(ctx context.Context, zalopayID, accountID int64, from, to time.Time) ([]*model.Statement, error)
	UpdateStatementOutstandingByIncurDate(ctx context.Context, statement *model.Statement) error
	UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, statement *model.Statement) error
	QueryInstallmentsByStatementID(ctx context.Context, stmtID int64) ([]*model.StatementInstallment, error)
	QueryInstallmentsByUserAndStatementID(ctx context.Context, zalopayID, accountID, stmtID int64) ([]*model.StatementInstallment, error)
	QueryDueDateByIncurDateAndPartner(ctx context.Context, partner partner.PartnerCode, incurredDate time.Time) (time.Time, error)
	QueryStatementsByIncurDateAndPartner(ctx context.Context,
		partner partner.PartnerCode, incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error)
	QueryOutstandingStatementsByIncurDateAndPartner(ctx context.Context,
		partner partner.PartnerCode, incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error)
}

//go:generate mockgen --destination=../mocks/repository/statement_syncer_repo.go --package=repository_mocks . StatementSyncerRepo
type StatementSyncerRepo interface {
	CreateSyncInfo(ctx context.Context, syncInfo *model.StatementSyncInfo) (lastID int64, err error)
	UpdateSyncStatus(ctx context.Context, syncInfo *model.StatementSyncInfo) error
	UpdateSyncStatusAndStats(ctx context.Context, syncInfo *model.StatementSyncInfo) error
	GetSyncerBatchByPeriodAndPartner(ctx context.Context, stmtPeriod *model.StatementPeriod,
		partnerCode partner.PartnerCode) ([]*model.StatementSyncInfo, error)
}

//go:generate mockgen --destination=../mocks/repository/installment_repo.go --package=repository_mocks . InstallmentRepo
type InstallmentRepo interface {
	CreateInstallment(ctx context.Context, installment *model.InstallmentInfo) (instID int64, err error)
	CreateRepaymentSchedule(ctx context.Context, repay *model.InstallmentRepay) error
	UpdateInstallmentAfterSync(ctx context.Context, inst *model.InstallmentInfo) error
	UpdateRepayScheduleAfterSync(ctx context.Context, repay *model.InstallmentRepay) error
	UpdateEarlyDischargeInfo(ctx context.Context, inst *model.InstallmentInfo) error
	UpdateEarlyDischargeStatus(ctx context.Context, instID int64, status model.DischargeStatus) error
	UpdateInstallmentRefund(ctx context.Context, instID int64, params *model.InstallmentRefund) error
	MarkFirstTenureSettled(ctx context.Context, instID int64) error
	ListInstallmentPeriodicSync(ctx context.Context, cutoffTime time.Time, paging *model.Pagination) ([]*model.InstallmentInfo, error)
	ListInstallmentByPartnerIDs(ctx context.Context, partnerInstIDs []string) ([]*model.InstallmentInfo, error)
	GetInstallmentByID(ctx context.Context, instID int64) (*model.InstallmentInfo, error)
	GetInstallmentByZPTransID(ctx context.Context, transID int64) (*model.InstallmentInfo, error)
	GetInstallmentByIDForUpdate(ctx context.Context, instID int64) (*model.InstallmentInfo, error)
	GetFullInstallmentByZPTransID(ctx context.Context, transID int64) (*model.Installment, error)
	GetFullInstallmentByUserIDAndZPTransID(ctx context.Context, zalopayID, transID int64) (*model.Installment, error)
	ListFullOpenInstallmentByUserAndAcctID(ctx context.Context, zalopayID, accountID int64) ([]*model.Installment, error)
}

//go:generate mockgen --destination=../mocks/repository/fee_repo.go --package=repository_mocks . FeeRepo
type FeeRepo interface {
	RetrieveFeesInfo(ctx context.Context) []*model.FeeConfig
	EvaluateFeesInfo(ctx context.Context, orderAmount int64) []*model.FeeDetail
}

//go:generate mockgen --destination=../mocks/repository/plan_repo.go --package=repository_mocks . PlanRepo
type PlanRepo interface {
	PersistPlanOptions(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest, plansOpts []*model.Plan) error
	RetrievePlanOptions(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) ([]*model.Plan, error)
	RetrievePlanSelected(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) (*model.Plan, error)
	PersistPlanSelected(ctx context.Context, planKeyData model.PlanKeyData, planResult *model.Plan) error
	RetrieveFullPlanInfo(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest) (*model.PlanPersist, error)
	PersistFullPlanInfo(ctx context.Context, zalopayID int64, orderInfo model.OrderRequest, planInfo *model.PlanPersist) error
}

type MSTeamsNotifier interface {
	SendInstallmentSyncStatsToChannel(ctx context.Context, stats *dto.InstallmentSyncStats) error
}

//go:generate mockgen --destination=../mocks/adapter/job_task_mgmt.go --package=adapter_mocks . JobTaskMgmt
type JobTaskMgmt interface {
	ExecuteSyncInstallmentTask(ctx context.Context, params *dto.SyncInstallmentParams) error
	ExecuteSyncLatestStatementTask(ctx context.Context, params *dto.SyncLatestStatementParams) error
	ExecuteSyncInstallmentDailyTask(ctx context.Context) error
	CanExecuteSyncInstallmentsDailyTask(ctx context.Context) (bool, error)
	ExecuteSyncAccountBalanceAfterDischargeTask(ctx context.Context, params *dto.SyncAccountBalanceAfterDischargeParams) error
	ExecuteSyncAccountBalanceAfterStatementTask(ctx context.Context, params *dto.SyncAccountBalanceAfterStatementParams) error
}

//go:generate mockgen --destination=../mocks/adapter/payment_event_publisher.go --package=adapter_mocks . PaymentEventPublisher
type PaymentEventPublisher interface {
	PublishPaymentCommission(ctx context.Context, event *dto.PaymentCommissionEvent) error
}
