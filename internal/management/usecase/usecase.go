package usecase

import (
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/installment"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/outstanding"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/plan"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/statement"
)

var ProviderSet = wire.NewSet(
	plan.NewUsecase,
	statement.NewUsecase,
	outstanding.NewUsecase,
	installment.NewUsecase,
	maintenance.NewUsecase,
)
