package utils

import (
	"net/url"

	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

var DeepLinkProviderSet = wire.NewSet(NewDeepLinkCraft)

//go:generate mockgen --destination=./mocks/deeplink.go --package=utils_mocks . DeepLinkCraft
type DeepLinkCraft interface {
	GetApplicationLink() model.DeepLinkData
	GetPlanDetailLink(planKey string) model.DeepLinkData
	GetPlanDetailMFLink() model.DeepLinkData
}

type DeepLinks map[model.DeepLinkKey]model.DeepLinkData

type deepLinkDeps struct {
	deeplinks DeepLinks
}

func NewDeepLinkCraft(config *config.Management) DeepLinkCraft {
	deepLinksConf := config.GetResources().GetDeeplinks()
	deepLinksMap := initDeepLinksFromMap(deepLinksConf)
	return &deepLinkDeps{deeplinks: deepLinksMap}
}

func initDeepLinksFromMap(data map[string]*config.DeepLink) DeepLinks {
	deepLinks := make(map[model.DeepLinkKey]model.DeepLinkData)
	for k, v := range data {
		deepLinks[model.DeepLinkKey(k)] = model.DeepLinkData{
			ZPAUrl:    v.GetZpaUrl(),
			ZPIUrl:    v.GetZpiUrl(),
			CommonUrl: v.GetCommonUrl(),
		}
	}
	return deepLinks
}

func (d *deepLinkDeps) getDeepLinkByKey(key model.DeepLinkKey) model.DeepLinkData {
	if link, ok := d.deeplinks[key]; ok {
		return link
	}
	return d.deeplinks[model.LinkKeyDefault]
}

func (d *deepLinkDeps) GetApplicationLink() model.DeepLinkData {
	linkData := d.getDeepLinkByKey(model.LinkKeyApplication)
	return linkData
}

func (d *deepLinkDeps) GetPlanDetailLink(planKey string) model.DeepLinkData {
	linkData := d.getDeepLinkByKey(model.LinkKeyPlanDetail)
	linkParams := map[string]string{"plan_key": planKey}
	buildFunc := func(link string) string {
		if link == "" {
			return ""
		}
		urlObj, err := url.Parse(link)
		if err != nil {
			return ""
		}
		urlQuery := urlObj.Query()
		for k, v := range linkParams {
			urlQuery.Add(k, v)
		}
		urlObj.RawQuery = urlQuery.Encode()
		return urlObj.String()
	}

	return model.DeepLinkData{
		ZPAUrl:    buildFunc(linkData.ZPAUrl),
		ZPIUrl:    buildFunc(linkData.ZPIUrl),
		CommonUrl: buildFunc(linkData.CommonUrl),
	}
}

func (d *deepLinkDeps) GetPlanDetailMFLink() model.DeepLinkData {
	linkData := d.getDeepLinkByKey(model.LinkKeyPlanDetailMF)
	return linkData
}
