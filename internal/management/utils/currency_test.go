package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFormatCurrencyKMB(t *testing.T) {
	testCases := []struct {
		name     string
		value    int64
		expected string
	}{
		{
			name:     "value less than 1000",
			value:    999,
			expected: "999",
		},
		{
			name:     "value in thousands",
			value:    1500,
			expected: "1k",
		},
		{
			name:     "value in millions",
			value:    2500000,
			expected: "2M",
		},
		{
			name:     "value in billions",
			value:    3500000000,
			expected: "3B",
		},
		{
			name:     "value in trillions",
			value:    4500000000000,
			expected: "4T",
		},
		{
			name:     "zero value",
			value:    0,
			expected: "0",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := FormatCurrencyKMB(tc.value)
			assert.Equal(t, tc.expected, result)
		})
	}
}
