// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils (interfaces: DeepLinkCraft)
//
// Generated by this command:
//
//	mockgen --destination=./mocks/deeplink.go --package=utils_mocks . DeepLinkCraft
//

// Package utils_mocks is a generated GoMock package.
package utils_mocks

import (
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	gomock "go.uber.org/mock/gomock"
)

// MockDeepLinkCraft is a mock of DeepLinkCraft interface.
type MockDeepLinkCraft struct {
	ctrl     *gomock.Controller
	recorder *MockDeepLinkCraftMockRecorder
	isgomock struct{}
}

// MockDeepLinkCraftMockRecorder is the mock recorder for MockDeepLinkCraft.
type MockDeepLinkCraftMockRecorder struct {
	mock *MockDeepLinkCraft
}

// NewMockDeepLinkCraft creates a new mock instance.
func NewMockDeepLinkCraft(ctrl *gomock.Controller) *MockDeepLinkCraft {
	mock := &MockDeepLinkCraft{ctrl: ctrl}
	mock.recorder = &MockDeepLinkCraftMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeepLinkCraft) EXPECT() *MockDeepLinkCraftMockRecorder {
	return m.recorder
}

// GetApplicationLink mocks base method.
func (m *MockDeepLinkCraft) GetApplicationLink() model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicationLink")
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetApplicationLink indicates an expected call of GetApplicationLink.
func (mr *MockDeepLinkCraftMockRecorder) GetApplicationLink() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetApplicationLink))
}

// GetPlanDetailLink mocks base method.
func (m *MockDeepLinkCraft) GetPlanDetailLink(planKey string) model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlanDetailLink", planKey)
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetPlanDetailLink indicates an expected call of GetPlanDetailLink.
func (mr *MockDeepLinkCraftMockRecorder) GetPlanDetailLink(planKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlanDetailLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetPlanDetailLink), planKey)
}

// GetPlanDetailMFLink mocks base method.
func (m *MockDeepLinkCraft) GetPlanDetailMFLink() model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlanDetailMFLink")
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetPlanDetailMFLink indicates an expected call of GetPlanDetailMFLink.
func (mr *MockDeepLinkCraftMockRecorder) GetPlanDetailMFLink() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlanDetailMFLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetPlanDetailMFLink))
}
