package utils

import (
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/platform/common/secure/aes"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
)

var EncryptProviderSet = wire.NewSet(NewPlanEncryption)

type Encryptor interface {
	Encrypt(data []byte) ([]byte, error)
	Decrypt(data []byte) ([]byte, error)
}

type PlanEncryptor interface {
	Encryptor
}

type PlanEncryption struct {
	Key []byte
	IV  []byte
}

func NewPlanEncryption(config *config.Management) PlanEncryptor {
	cryptoConf := config.GetCryptoSets()
	secretKey := cryptoConf.GetPlanEncryption().GetKey()
	initVector := cryptoConf.GetPlanEncryption().GetIv()
	return &PlanEncryption{Key: []byte(secretKey), IV: []byte(initVector)}
}

func (p PlanEncryption) Encrypt(data []byte) ([]byte, error) {
	return aes.CBCEncrypt(p.Key, p.IV, data)
}

func (p PlanEncryption) Decrypt(data []byte) ([]byte, error) {
	return aes.CBCDecrypt(p.Key, p.IV, data)
}
