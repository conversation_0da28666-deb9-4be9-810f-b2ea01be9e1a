package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

func TestNewDeepLinkCraft(t *testing.T) {
	// Setup test config
	conf := &config.Management{}
	deeplinks := map[string]*config.DeepLink{
		"default": {
			ZpaUrl:    "https://zpa.default.com",
			ZpiUrl:    "https://zpi.default.com",
			CommonUrl: "https://common.default.com",
		},
		"application": {
			ZpaUrl:    "https://zpa.app.com",
			ZpiUrl:    "https://zpi.app.com",
			CommonUrl: "https://common.app.com",
		},
		"plan_detail": {
			ZpaUrl:    "https://zpa.plan.com",
			ZpiUrl:    "https://zpi.plan.com",
			CommonUrl: "https://common.plan.com",
		},
		"plan_detail_mf": {
			ZpaUrl:    "https://zpa.mf.com",
			ZpiUrl:    "https://zpi.mf.com",
			CommonUrl: "https://common.mf.com",
		},
	}

	resources := &config.Management_Resources{
		Deeplinks: deeplinks,
	}
	conf.Resources = resources

	// Create deeplink craft
	craft := NewDeepLinkCraft(conf)

	t.Run("GetApplicationLink", func(t *testing.T) {
		result := craft.GetApplicationLink()
		expected := model.DeepLinkData{
			ZPAUrl:    "https://zpa.app.com",
			ZPIUrl:    "https://zpi.app.com",
			CommonUrl: "https://common.app.com",
		}
		assert.Equal(t, expected, result)
	})

	t.Run("GetPlanDetailLink", func(t *testing.T) {
		planKey := "test-plan"
		result := craft.GetPlanDetailLink(planKey)
		expected := model.DeepLinkData{
			ZPAUrl:    "https://zpa.plan.com?plan_key=test-plan",
			ZPIUrl:    "https://zpi.plan.com?plan_key=test-plan",
			CommonUrl: "https://common.plan.com?plan_key=test-plan",
		}
		assert.Equal(t, expected, result)
	})

	t.Run("GetPlanDetailMFLink", func(t *testing.T) {
		result := craft.GetPlanDetailMFLink()
		expected := model.DeepLinkData{
			ZPAUrl:    "https://zpa.mf.com",
			ZPIUrl:    "https://zpi.mf.com",
			CommonUrl: "https://common.mf.com",
		}
		assert.Equal(t, expected, result)
	})

	t.Run("getDeepLinkByKey with invalid key", func(t *testing.T) {
		deps := craft.(*deepLinkDeps)
		result := deps.getDeepLinkByKey("invalid_key")
		expected := model.DeepLinkData{
			ZPAUrl:    "https://zpa.default.com",
			ZPIUrl:    "https://zpi.default.com",
			CommonUrl: "https://common.default.com",
		}
		assert.Equal(t, expected, result)
	})
}

func TestGetPlanDetailLinkWithInvalidURL(t *testing.T) {
	conf := &config.Management{}
	deeplinks := map[string]*config.DeepLink{
		"default": {},
		"plan_detail": {
			ZpaUrl: "://invalid-url",
		},
	}

	resources := &config.Management_Resources{
		Deeplinks: deeplinks,
	}
	conf.Resources = resources

	craft := NewDeepLinkCraft(conf)
	result := craft.GetPlanDetailLink("test-plan")

	assert.Equal(t, "", result.ZPAUrl)
	assert.Equal(t, "", result.ZPIUrl)
	assert.Equal(t, "", result.CommonUrl)
}
