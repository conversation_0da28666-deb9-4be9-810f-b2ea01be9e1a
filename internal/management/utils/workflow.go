package utils

type WorkflowID string

const (
	WfIDSyncInstallment        WorkflowID = "SYNC-INSTALLMENT-%v-%v"
	WfIDSyncStatementsPeriodic WorkflowID = "SYNC-STATEMENTS-PERIODIC"
	WfIDSyncStatementsBatch    WorkflowID = "SYNC-STATEMENTS-BATCH-%s-%d"
	WfIDSyncStatementsLatest   WorkflowID = "SYNC-STATEMENTS-LATEST-%d-%d"
	WfIDSyncStatementsManual   WorkflowID = "SYNC-STATEMENTS-MANUAL-%s"
	WfIDSyncInstallmentsDaily  WorkflowID = "SYNC-INSTALLMENTS-DAILY-%s"
	// Currently we only have 1 workflow for sync account balance after statement
	// If we have more than 1, should refactor to use one generic workflow ID
	WfIDSyncAccountBalanceAfterStatement WorkflowID = "STATEMENT-SYNC-ACCOUNT-BALANCE-%s-%d-%d"
	WfIDSyncAccountBalanceAfterDischarge WorkflowID = "DISCHARGE-SYNC-ACCOUNT-BALANCE-%d-%d"
)

func (w WorkflowID) String() string {
	return string(w)
}
