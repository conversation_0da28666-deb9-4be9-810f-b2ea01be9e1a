// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package worker

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/job_task"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters/payment_event"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/installment"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/outstanding"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/plan"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/statement"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocking"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(management *config.Management, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := base.InitSQLDatabase(management, logger)
	if err != nil {
		return nil, nil, err
	}
	cacheNoCaller, cleanup2, err := base.InitRedisCache(management)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	repository := repo.NewRepository(logger, db, cacheNoCaller)
	transaction := repo.NewTransaction(repository)
	mockingMocking := mocking.NewMocking(cacheNoCaller, logger)
	distributedLock := dist_lock.NewDistLock(cacheNoCaller, logger)
	client, cleanup3, err := base.InitTemporalClient(management)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jobTaskMgmt := job_task.NewJobTaskMgmt(management, client, logger)
	redisKeyGenerator, cleanup4, err := base.InitRedisKeyGenerator(management)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	cimbConnectorClient, cleanup5, err := base.InitCIMBConnectorClient(management, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	cimbService := cimb_service.NewService(cimbConnectorClient, logger)
	accountClient, cleanup6, err := base.InitAccountServiceClient(management, logger)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountService := account_service.NewClient(accountClient, logger)
	statementRepo := repo.NewStatementRepo(repository, logger)
	installmentRepo := repo.NewInstallmentRepo(repository, logger)
	statementSyncerRepo := repo.NewStatementSyncRepo(repository, logger)
	usecase := statement.NewUsecase(logger, management, transaction, mockingMocking, distributedLock, jobTaskMgmt, redisKeyGenerator, cimbService, accountService, statementRepo, installmentRepo, statementSyncerRepo)
	statementService := service.NewStatementService(logger, management, usecase)
	feeRepo := repo.NewFeeRepo(management, repository, logger)
	planRepo := repo.NewPlanRepo(logger, repository, redisKeyGenerator)
	deepLinkCraft := utils.NewDeepLinkCraft(management)
	planUsecase := plan.NewUsecase(feeRepo, planRepo, cimbService, accountService, deepLinkCraft, logger)
	publisher, cleanup7, err := base.InitPaymentCommPublisher(management)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	paymentEventPublisher := payment_event.NewPaymentEventPub(publisher, logger)
	installmentUsecase := installment.NewUsecase(transaction, distributedLock, installmentRepo, jobTaskMgmt, cimbService, accountService, paymentEventPublisher, redisKeyGenerator, management, logger)
	installmentService := service.NewInstallmentService(logger, management, planUsecase, installmentUsecase)
	outstandingUsecase := outstanding.NewUsecase(statementRepo, installmentRepo, logger)
	managementService := service.NewManagementService(logger, usecase, installmentUsecase, outstandingUsecase)
	temporalWorker := server.NewTemporalWorker(management, logger, statementService, installmentService, managementService, client)
	workerHTTPServer := server.NewWorkerHTTPServer(management, logger)
	loanRefundSettleConsumer := server.NewLoanRefundSettleConsumer(management, logger, managementService)
	app := newApp(logger, temporalWorker, workerHTTPServer, loanRefundSettleConsumer)
	return app, func() {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
