//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package management

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/adapters"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocking"
)

// wireApp init kratos application.
func wireApp(*config.Management, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		base.ProviderSet,
		adapters.ProviderSet,
		server.ProviderSet,
		repo.ProviderSet,
		usecase.ProviderSet,
		service.ProviderSet,
		utils.ProviderSet,
		mocking.ProviderSet,
		maintenance.ProviderSet,
		newApp,
	))
}
