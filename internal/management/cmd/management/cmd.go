package management

import (
	"os"

	"github.com/spf13/cobra"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/telemetry"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zlog"

	"github.com/go-kratos/kratos/v2"
	kconfig "github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string = "management-service"
	// Version is the version of the compiled software.
	Version string = "v1.0.0"
	// id is the hostname of the compiled software.
	id, _ = os.Hostname()
)

var cmd = &cobra.Command{
	Use:   "server",
	Short: `start management service`,
	RunE:  runCmd,
}

func NewCmd() *cobra.Command {
	return cmd
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
	)
}

type ServiceInfo struct {
	Id      string
	Name    string
	Version string
}

func runCmd(cmd *cobra.Command, args []string) error {
	confFlag := cmd.Flag("config")
	if confFlag == nil {
		panic("config flag not found")
	}

	confPath := confFlag.Value.String()
	confSource := file.NewSource(confPath)
	c := kconfig.New(kconfig.WithSource(confSource))
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc config.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	appConf := bc.GetManagement()

	err := telemetry.MustInitTracer(&telemetry.TracingInfo{
		SvcVer:      Version,
		SvcName:     appConf.GetTracing().GetServiceName(),
		Environment: appConf.GetApp().GetEnv(),
		AgentHost:   appConf.GetTracing().GetAgentHost(),
		AgentPort:   appConf.GetTracing().GetAgentPort(),
	})
	if err != nil {
		panic(err)
	}

	logger := zlog.MustNewLogger(&zlog.LoggerInfo{
		SvcID:      id,
		SvcVer:     Version,
		SvcName:    appConf.GetApp().GetName(),
		Level:      appConf.GetLogger().GetLevel(),
		Encoding:   appConf.GetLogger().GetEncoding(),
		StackTrace: appConf.GetLogger().GetStackTrace(),
	})

	app, cleanup, err := wireApp(appConf, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}

	return nil
}
