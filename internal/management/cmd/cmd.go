package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/cmd/management"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/cmd/worker"
)

var rootCmd = &cobra.Command{
	Use:              "management",
	Short:            "management service",
	RunE:             runCmd,
	TraverseChildren: true,
}

func NewCmd() *cobra.Command {
	rootCmd.AddCommand(worker.NewCmd())
	rootCmd.AddCommand(management.NewCmd())
	return rootCmd
}

func runCmd(cmd *cobra.Command, args []string) error {
	defaultCmd := management.NewCmd()
	return defaultCmd.RunE(cmd, args)
}
