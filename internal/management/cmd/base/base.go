package base

import (
	"database/sql"
	"strings"
	"time"

	"github.com/XSAM/otelsql"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/extra/redisotel/v8"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"gitlab.zalopay.vn/fin/platform/common/redis"
	"gitlab.zalopay.vn/fin/platform/common/workflowengine/temporalclient"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	tempCli "go.temporal.io/sdk/client"
	"go.temporal.io/sdk/contrib/opentelemetry"
	"google.golang.org/grpc"
	kafka_client "zalopay.io/zgo/kafka-client"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/bootstrap"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/uberfx"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

var ProviderSet = wire.NewSet(
	InitRedisCache,
	InitSQLDatabase,
	InitTemporalClient,
	InitSessionGrpcConn,
	InitRedisKeyGenerator,
	InitCIMBConnectorClient,
	InitAccountServiceClient,
	InitPaymentCommPublisher,
)

func InitSessionGrpcConn(config *config.Management, logger log.Logger) (sessionv1.SessionServiceClient, func(), error) {
	sessConfig := config.GetAdapters().GetSession()
	log.Info("InitSessionGrpcConn connecting..., target: ", sessConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: sessConfig.GetAddress(),
			Timeout: sessConfig.GetTimeout().AsDuration(),
			Secured: sessConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)
	if err != nil {
		log.Error("InitSessionGrpcConn, err: ", err, ", target: ", config.GetAdapters().GetSession().GetAddress())
		return nil, nil, err
	}
	log.Info("InitSessionGrpcConn success, target: ", config.GetAdapters().GetSession().GetAddress())

	return sessionv1.NewSessionServiceClient(conn), func() { _ = conn.Close() }, nil
}

func InitCIMBConnectorClient(config *config.Management, logger log.Logger) (connector.CIMBConnectorClient, func(), error) {
	cimbConfig := config.GetAdapters().GetCimbConnector()

	log.Info("InitCIMBConnectorClient is starting..., target: ", cimbConfig.GetAddress())

	codec := codec.NewRequestLogCodec(
		[]string{connector.CIMBConnector_GetStatement_FullMethodName},
		[]string{"customer_account_number"},
	)
	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: cimbConfig.GetAddress(),
			Timeout: cimbConfig.GetTimeout().AsDuration(),
			Secured: cimbConfig.GetSecured(),
			Codec:   codec,
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliCredsInterceptor(cimbConfig.GetClientId(), cimbConfig.GetClientKey()),
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
			bootstrap.WithMetadataAppendInterceptor(
				common.MetadataKey_METADATA_PRODUCT_LINE.String(),
				common.ProductLine_PRODUCT_INSTALLMENT.String(),
			),
		),
	)

	if err != nil {
		log.Error("InitCIMBConnectorClient error", err)
		return nil, nil, err
	}

	log.Info("InitCIMBConnectorClient success, target: ", cimbConfig.GetAddress())

	return connector.NewCIMBConnectorClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitSQLDatabase(conf *config.Management, kLogger log.Logger) (*sql.DB, func(), error) {
	logger := log.NewHelper(kLogger)
	dbConf := conf.GetData().GetDatabase()
	dbSource := zutils.FormatDBSource(dbConf.GetSource())

	db, err := otelsql.Open(
		dbConf.GetDriver(), dbSource,
		otelsql.WithSQLCommenter(true),
		otelsql.WithAttributes(semconv.DBSystemMySQL))
	if err != nil {
		logger.Error("Open database error", err)
		return nil, nil, err
	}

	err = otelsql.RegisterDBStatsMetrics(db, otelsql.WithAttributes(semconv.DBSystemMySQL))
	if err != nil {
		logger.Error("RegisterDBStatsMetrics error", err)
		return nil, nil, err
	}

	db.SetMaxOpenConns(int(dbConf.GetMaxOpenConn()))
	db.SetMaxIdleConns(int(dbConf.GetMaxIdleConn()))
	db.SetConnMaxLifetime(dbConf.GetConnMaxLifetime().AsDuration())

	cleanup := func() {
		_ = db.Close()
	}
	return db, cleanup, nil
}

func InitRedisCache(config *config.Management) (redis.CacheNoCaller, func(), error) {
	redisConfig := config.GetData().GetRedisCache()
	log.Info("InitRedisCache is starting..., target: ", redisConfig.GetAddress())

	cacheConf := &redis.Config{
		Addr:             []string{redisConfig.GetAddress()},
		Username:         redisConfig.GetUsername(),
		Password:         redisConfig.GetPassword(),
		PoolSize:         int(redisConfig.GetPoolSize()),
		MinIdleConn:      int(redisConfig.GetMinIdleConn()),
		MaxConnAge:       time.Minute * 5,
		MasterName:       redisConfig.GetMasterName(),
		SentinelUsername: redisConfig.GetUsername(),
		SentinelPassword: redisConfig.GetPassword(),
	}
	redisCache := redis.CreateRedisCache(&uberfx.NoopLifeCycle{}, cacheConf)
	redisClient := redisCache.GetRedisClient()
	redisClient.AddHook(redisotel.NewTracingHook())

	log.Info("InitRedisCache success, target: ", redisConfig.GetAddress())

	return redisCache, func() {
		if redisClient == nil {
			return
		}
		_ = redisClient.Close()
	}, nil
}

func InitAccountServiceClient(config *config.Management, logger log.Logger) (v1.AccountClient, func(), error) {
	accountConfig := config.GetAdapters().GetAccountService()
	log.Info("InitAccountServiceClient is starting..., target: ", accountConfig.GetAddress())

	codec := codec.NewRequestLogCodec(
		[]string{v1.Account_GetAccount_FullMethodName, v1.Account_ListAccountForStmtSync_FullMethodName},
		[]string{"partner_account_name", "partner_account_number", "accounts.*.partner_account_name", "accounts.*.partner_account_number"},
	)
	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: accountConfig.GetAddress(),
			Timeout: accountConfig.GetTimeout().AsDuration(),
			Secured: accountConfig.GetSecured(),
			Codec:   codec,
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitAccountServiceClient error", err)
		return nil, nil, err
	}

	log.Info("InitAccountServiceClient success, target: ", accountConfig.GetAddress())

	return v1.NewAccountClient(conn), func() { _ = conn.Close() }, nil
}

func InitTemporalClient(config *config.Management) (tempClient tempCli.Client, cleanup func(), err error) {
	tempConf := &temporalclient.Config{
		HostPort:  config.GetAdapters().GetTemporal().GetAddress(),
		Namespace: config.GetAdapters().GetTemporal().GetNamespace(),
		EnableTls: true,
	}

	otelOption := func(options tempCli.Options) tempCli.Options {
		tracerOpts := opentelemetry.TracerOptions{}
		otelIntercept, tErr := opentelemetry.NewTracingInterceptor(tracerOpts)
		if tErr != nil {
			return options
		}

		interceptors := options.Interceptors
		interceptors = append(interceptors, otelIntercept)
		options.Interceptors = interceptors
		return options
	}

	client, err := temporalclient.NewClient(tempConf, otelOption)
	if err != nil {
		return nil, nil, err
	}

	return client, client.Close, nil
}

func InitPaymentCommPublisher(config *config.Management) (publisher kafka_client.Publisher, cleanup func(), err error) {
	pubConfig := config.GetAdapters().GetPaymentCommPub()
	pubBrokers := strings.Split(pubConfig.Brokers, ",")
	publisher, err = kafka_client.NewPublisher(&kafka_client.PublisherConfig{
		Brokers: pubBrokers,
		Topic:   pubConfig.Topic,
	})
	if err != nil {
		return nil, nil, err
	}
	return publisher, func() { _ = publisher.Stop() }, nil
}

func InitRedisKeyGenerator(config *config.Management) (keygen.RedisKeyGenerator, func(), error) {
	appConfig := config.GetApp()
	keyGenerator, err := keygen.NewRedisKeyGenerator(appConfig.GetEnv(), appConfig.GetName())
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create redis key generator")
	}
	return keyGenerator, func() {}, nil
}
