package entity

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
)

type JsonRawMessage json.RawMessage

func (o JsonRawMessage) Value() (driver.Value, error) {
	if len(o) == 0 {
		return nil, nil
	}
	if string(o) == "null" {
		return nil, nil
	}
	return driver.Value([]byte(o)), nil
}

func (o *JsonRawMessage) Scan(src any) error {
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case string:
		source = []byte(t)
	case nil:
		source = nil
	default:
		return errors.New("incompatible type for JsonRawMessage")
	}
	*o = source
	return nil
}
