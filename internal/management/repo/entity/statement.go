package entity

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/pkg/errors"
)

type StatementInstallmentOuts struct {
	OutstandingTotal        int64 `json:"outstanding_amount"`
	OutstandingPrincipal    int64 `json:"outstanding_principal"`
	OutstandingInterest     int64 `json:"outstanding_interest"`
	OutstandingPenalty      int64 `json:"outstanding_penalty"`
	OutstandingDuePrincipal int64 `json:"outstanding_due_principal"`
	OutstandingDueInterest  int64 `json:"outstanding_due_interest"`
	OutstandingDuePenalty   int64 `json:"outstanding_due_penalty"`
}

func (o StatementInstallmentOuts) Value() (driver.Value, error) {
	j, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	return driver.Value(j), nil
}

func (o *StatementInstallmentOuts) Scan(src any) error {
	if src == nil {
		*o = StatementInstallmentOuts{}
		return nil
	}

	var _i StatementInstallmentOuts
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for OutstandingDetails")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*o = _i
	return nil
}

type StatementMetadata struct {
	PenaltySource string `json:"penalty_source"`
}

func (s StatementMetadata) Value() (driver.Value, error) {
	j, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return driver.Value(j), nil
}

func (s *StatementMetadata) Scan(src any) error {
	if src == nil {
		*s = StatementMetadata{}
		return nil
	}

	var _i StatementMetadata
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for StatementMetadata")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*s = _i
	return nil
}
