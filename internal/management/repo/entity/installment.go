package entity

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
)

type EarlyDischargeDetails struct {
	SyncedAt             time.Time `json:"synced_at"`
	TotalDischargeAmount int64     `json:"total_discharge_amount"`
	EarlyDischargeFee    int64     `json:"early_discharge_fee"`
	OutstandingPrincipal *int64    `json:"outstanding_principal,omitempty"`
	OutstandingInterest  *int64    `json:"outstanding_interest,omitempty"`
	OutstandingPenalty   *int64    `json:"outstanding_penalty,omitempty"`
}

type InstallmentRefund struct {
	UpdateVersion     int   `json:"update_version"`
	UserTopupAmount   int64 `json:"user_topup_amount"`
	NetRefundAmount   int64 `json:"net_refund_amount"`
	TotalRefundAmount int64 `json:"total_refund_amount"`
	UserTopupRequired bool  `json:"user_topup_required"`
}

func (o InstallmentRefund) Value() (driver.Value, error) {
	j, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	if string(j) == "null" {
		return nil, nil
	}
	return driver.Value(j), nil
}

func (o *InstallmentRefund) Scan(src any) error {
	if src == nil {
		*o = InstallmentRefund{}
		return nil
	}

	var _i InstallmentRefund
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for InstallmentRefund")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*o = _i
	return nil
}

type InstallmentTransaction struct {
	TransactionID      int64  `json:"transaction_id"`
	TransactionDesc    string `json:"transaction_desc"`
	FirstTenureSettled bool   `json:"first_tenure_settled"`
}

func (o InstallmentTransaction) Value() (driver.Value, error) {
	j, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	return driver.Value(j), nil
}

func (o *InstallmentTransaction) Scan(src any) error {
	if src == nil {
		*o = InstallmentTransaction{}
		return nil
	}

	var _i InstallmentTransaction
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for InstallmentTransaction")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*o = _i
	return nil
}

type InstallmentProvisional struct {
	SimEmiAmount         int64   `json:"sim_emi_amount"`
	SimInterestRate      float64 `json:"sim_interest_rate"`
	SimInterestAmount    int64   `json:"sim_interest_amount"`
	SimTotalDueAmount    int64   `json:"sim_total_due_amount"`
	SimInstallmentAmount int64   `json:"sim_installment_amount"`
}

func (o InstallmentProvisional) Value() (driver.Value, error) {
	j, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	return driver.Value(j), nil
}

func (o *InstallmentProvisional) Scan(src any) error {
	if src == nil {
		*o = InstallmentProvisional{}
		return nil
	}

	var _i InstallmentProvisional
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for InstallmentProvisional")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*o = _i
	return nil
}
