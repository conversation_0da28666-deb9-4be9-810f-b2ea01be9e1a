package entity

type Fee struct {
	Rate     float64   `json:"rate"`
	Amount   int64     `json:"amount"`
	Currency string    `json:"currency"`
	Priority int       `json:"priority"`
	Status   FeeStatus `json:"status"`
}

type FeeStatus string

const (
	FeeActive   FeeStatus = "active"
	FeeInactive FeeStatus = "inactive"
)

type FeeType string

const (
	PlatformFee   FeeType = "platform_fee"
	ConversionFee FeeType = "conversion_fee"
)

type FeeResources map[FeeType]Fee

func (f FeeResources) IsEmpty() bool {
	return len(f) == 0
}
