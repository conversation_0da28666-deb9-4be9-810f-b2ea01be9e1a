package entity

import (
	jsoniter "github.com/json-iterator/go"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
)

type PlanCache struct {
	OrderAmount     int64         `json:"order_amount" redis:"order_amount"`
	PlanOptions     []*model.Plan `json:"plan_options" redis:"plan_options"`
	PlanSelected    *model.Plan   `json:"plan_selected" redis:"plan_selected"`
	PlanKeySelected string        `json:"plan_key_selected" redis:"plan_key_selected"`
}

type PlanCacheField string

const (
	OrderAmountField      PlanCacheField = "order_amount"
	PlanOptionsField      PlanCacheField = "plan_options"
	PlanSelectedField     PlanCacheField = "plan_selected"
	SessionExpiredAtField PlanCacheField = "session_exp_at"
)

func (p PlanCacheField) String() string {
	return string(p)
}

func NewPlanCacheFromMap(value map[string]string) (*PlanCache, error) {
	var result PlanCache
	for k, v := range value {
		switch PlanCacheField(k) {
		case OrderAmountField:
			_ = jsoniter.Unmarshal([]byte(v), &result.OrderAmount)
		case PlanOptionsField:
			_ = jsoniter.Unmarshal([]byte(v), &result.PlanOptions)
		case PlanSelectedField:
			_ = jsoniter.Unmarshal([]byte(v), &result.PlanSelected)
		}
	}
	return &result, nil
}
