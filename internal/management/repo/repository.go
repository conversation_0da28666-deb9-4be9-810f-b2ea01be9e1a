package repo

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	jsoniter "github.com/json-iterator/go"
	"gitlab.zalopay.vn/fin/platform/common/redis"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
)

//go:generate mockgen --destination=./mocks/queries.go --package=damocks ./da Querier

var ProviderSet = wire.NewSet(
	NewRepository,
	NewTransaction,
	NewFeeRepo,
	NewPlanRepo,
	NewStatementRepo,
	NewStatementSyncRepo,
	NewInstallmentRepo,
	wire.Bind(new(_interface.InstallmentRepo), new(*installmentRepo)),
)

type Repository struct {
	sqlDB      *sql.DB
	logger     *log.Helper
	metrics    *metrics.RepositoryMetrics
	redisCache redis.CacheNoCaller

	// mockQuerier is used for testing
	mockQuerier da.Querier
}

type ctxTxKey struct{}

const (
	feeRepoName           = "fees"
	planRepoName          = "plans"
	statementRepoName     = "statements"
	statementSyncRepoName = "statement_sync"
	installmentRepoName   = "installments"
)

func NewRepository(kLogger log.Logger, sqlDB *sql.DB, redisCache redis.CacheNoCaller) *Repository {
	repos := &Repository{sqlDB: sqlDB, redisCache: redisCache}
	repos.logger = log.NewHelper(log.With(kLogger, "module", "repository"))
	repos.metrics = metrics.NewRepositoryMetrics(metrics.Options{Module: metrics.MetricModuleRepository})
	return repos
}

func NewTransaction(d *Repository) _interface.Transaction {
	return d
}

func LoadResources[T entity.FeeResources](path string, res T) T {
	filePath := filepath.Clean(path)
	fileObj, err := os.OpenFile(filePath, os.O_RDONLY, 0644)
	if err != nil {
		fmt.Println("Error opening file: ", err)
		return res
	}
	defer fileObj.Close()

	fileBytes, err := os.ReadFile(fileObj.Name())
	if err != nil {
		fmt.Println("Error reading file: ", err)
		return res
	}

	err = jsoniter.Unmarshal(fileBytes, &res)
	if err != nil {
		return res
	}
	return res
}

func (r *Repository) IsInTx(ctx context.Context) bool {
	tx := getTxFromCtx(ctx)
	return tx != nil
}

func (r *Repository) BeginTx(ctx context.Context) (context.Context, error) {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return nil, err
	}
	return context.WithValue(ctx, ctxTxKey{}, tx), nil
}

func (r *Repository) BeginOrReuseTx(ctx context.Context) (context.Context, error) {
	tx := getTxFromCtx(ctx)
	if tx != nil {
		return ctx, nil
	}
	return r.BeginTx(ctx)
}

func (r *Repository) CommitTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Commit()
}

func (r *Repository) RollbackTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Rollback()
}

func (r *Repository) WithTx(ctx context.Context, exec func(ctx context.Context) error) error {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	err = exec(context.WithValue(ctx, ctxTxKey{}, tx))
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (r *Repository) IsTxActive(ctx context.Context) bool {
	return getTxFromCtx(ctx) != nil
}

func getTxFromCtx(ctx context.Context) *sql.Tx {
	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if !ok {
		return nil
	}
	return tx
}

func (r *Repository) Queries(ctx context.Context) da.Querier {
	if r.mockQuerier != nil {
		return r.mockQuerier
	}

	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if ok {
		return da.New(tx)
	}
	return da.New(r.sqlDB)
}
