package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"slices"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type installmentRepo struct {
	repos  *Repository
	logger *log.Helper
}

func NewInstallmentRepo(repos *Repository, kLogger log.Logger) _interface.InstallmentRepo {
	logger := log.With(kLogger, "repo", "installment")
	return &installmentRepo{
		repos:  repos,
		logger: log.NewHelper(logger),
	}
}

func (i installmentRepo) CreateInstallment(ctx context.Context, installment *model.InstallmentInfo) (int64, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("CreateInstallment")
	logger := i.logger.WithContext(ctx)

	feesBin, err := jsoniter.Marshal(installment.SimulationInfo.FeeDetails)
	if err != nil {
		logger.Errorf("failed to marshal fee details: %v", err)
		return -1, errors.Wrap(err, "failed to marshal fee details")
	}

	// In case create installment cannot marshal outstanding details, set to nil and log error
	outsBin, err := jsoniter.Marshal(installment.OutstandingInfo)
	if err != nil {
		logger.Errorf("failed to marshal outstanding details: %v", err)
		outsBin = nil
	}

	// In case create installment cannot marshal earlyDischarge, set to nil and log error
	dischargeBin, err := jsoniter.Marshal(fromInstallmentEarlyDischarge(installment.EarlyDischarge))
	if err != nil {
		logger.Errorf("failed to marshal early discharge: %v", err)
		dischargeBin = nil
	}

	params := &da.InsertInstallmentParams{
		Tenure:          installment.Tenure,
		Status:          fromInstallmentStatus(installment.Status),
		ZalopayID:       installment.ZalopayID,
		AccountID:       installment.AccountID,
		ZpTransID:       installment.ZPTransID,
		DaysPastDue:     installment.DaysPastDue,
		PartnerCode:     installment.PartnerCode.String(),
		PaidTenure:      zutils.NewNullInt32(0),
		StartDate:       zutils.NewNullTime(installment.StartDate),
		EndDate:         zutils.NewNullTime(installment.EndDate),
		CurrDueDate:     zutils.NewNullTime(installment.CurrDueDate),
		NextDueDate:     zutils.NewNullTime(installment.NextDueDate),
		PartnerInstID:   zutils.NewNullString(installment.PartnerInstID),
		DisburseAmount:  zutils.NewNullInt64(installment.DisburseAmount),
		EmiAmount:       zutils.NewNullInt64(installment.SimulationInfo.EmiAmount),
		InterestRate:    zutils.NewNullFloat64(installment.SimulationInfo.InterestRate),
		InterestAmount:  zutils.NewNullInt64(installment.SimulationInfo.InterestAmount),
		TotalFeeAmount:  zutils.NewNullInt64(installment.SimulationInfo.TotalFeeAmount),
		TotalDueAmount:  zutils.NewNullInt64(installment.SimulationInfo.TotalAmountDue),
		FeeDetails:      feesBin,
		OutstandingInfo: outsBin,
		EarlyDischarge:  dischargeBin,
		TransactionInfo: entity.InstallmentTransaction{
			TransactionID:   installment.TransactionInfo.TransID,
			TransactionDesc: installment.TransactionInfo.TransDesc,
		},
	}

	result, err := i.repos.Queries(ctx).InsertInstallment(ctx, params)
	if err != nil {
		logger.Errorf("failed to create installment into db: %v", err)
		return -1, errors.Wrap(err, "failed to create installment")
	}

	instID, err := result.LastInsertId()
	if err != nil {
		logger.Errorf("failed to get last installment insert id: %v", err)
		return -1, errors.Wrap(err, "failed to get last insert id")
	}
	return instID, nil
}

func (i installmentRepo) CreateRepaymentSchedule(ctx context.Context, repay *model.InstallmentRepay) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("CreateRepaymentSchedule")
	logger := i.logger.WithContext(ctx)

	penaltyBin, err := jsoniter.Marshal(repay.PenaltyDetails)
	if err != nil {
		logger.Errorf("failed to marshal penalty details: %v", err)
		return errors.Wrap(err, "failed to marshal penalty details")
	}
	outstandingBin, err := jsoniter.Marshal(repay.OutstandingDetails)
	if err != nil {
		logger.Errorf("failed to marshal outstanding details: %v", err)
		return errors.Wrap(err, "failed to marshal outstanding details")
	}

	params := &da.InsertRepaymentScheduleParams{
		SeqNo:              repay.SeqNo,
		InstID:             repay.InstallmentID,
		PartnerInstID:      repay.PartnerInstID,
		Status:             fromRepayScheduleStatus(repay.Status),
		DueDate:            zutils.NewNullTime(repay.RepayDueDate),
		GraceDueDate:       zutils.NewNullTime(repay.GraceDueDate),
		EmiAmount:          zutils.NewNullInt64(repay.EmiAmount),
		InterestAmount:     zutils.NewNullInt64(repay.InterestAmount),
		PrincipalAmount:    zutils.NewNullInt64(repay.PrincipalAmount),
		PenaltyDetails:     penaltyBin,
		OutstandingDetails: outstandingBin,
	}
	if err = i.repos.Queries(ctx).InsertRepaymentSchedule(ctx, params); err != nil {
		logger.Errorf("failed to create installment repay into db: %v", err)
		return errors.Wrap(err, "failed to create installment repay")
	}
	return nil
}

func (i installmentRepo) UpdateInstallmentAfterSync(ctx context.Context, inst *model.InstallmentInfo) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("UpdateInstallmentAfterSync")
	logger := i.logger.WithContext(ctx)

	outsBin, err := jsoniter.Marshal(inst.OutstandingInfo)
	if err != nil {
		logger.Errorf("failed to marshal outstanding details: %v", err)
		return errors.Wrap(err, "failed to marshal outstanding details")
	}
	dischargeBin, err := jsoniter.Marshal(fromInstallmentEarlyDischarge(inst.EarlyDischarge))
	if err != nil {
		logger.Errorf("failed to marshal early discharge: %v", err)
		return errors.Wrap(err, "failed to marshal early discharge")
	}
	dischargeStatus, err := fromDischargeStatus(inst.DischargeStatus)
	if err != nil {
		logger.Errorf("failed to convert discharge status: %v", err)
		return errors.Wrap(err, "failed to convert discharge status")
	}

	params := &da.UpdateInstallmentAfterSyncParams{
		ID:              inst.ID,
		Status:          fromInstallmentStatus(inst.Status),
		DaysPastDue:     inst.DaysPastDue,
		PaidTenure:      zutils.NewNullInt32(inst.PaidTenure),
		StartDate:       zutils.NewNullTime(inst.StartDate),
		EndDate:         zutils.NewNullTime(inst.EndDate),
		CurrDueDate:     zutils.NewNullTime(inst.CurrDueDate),
		NextDueDate:     zutils.NewNullTime(inst.NextDueDate),
		OutstandingInfo: outsBin,
		EarlyDischarge:  dischargeBin,
		DischargeStatus: dischargeStatus,
	}
	err = i.repos.Queries(ctx).UpdateInstallmentAfterSync(ctx, params)
	if err != nil {
		return errors.Wrap(err, "failed to update installment after sync")
	}
	return nil
}

func (i installmentRepo) MarkFirstTenureSettled(ctx context.Context, instID int64) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("MarkFirstTenureSettled")
	logger := i.logger.WithContext(ctx)

	inst, err := i.repos.Queries(ctx).GetInstallmentByIDForUpdate(ctx, instID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("installment not found in db with instID=%d", instID)
		return errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with instID=%d", instID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by instID from db: %v", err)
		return errors.Wrap(err, "failed to get installment by instID from db")
	}

	transactionInfo := inst.TransactionInfo
	transactionInfo.FirstTenureSettled = true
	params := &da.UpdateInstallmentTransactionParams{
		ID:              inst.ID,
		TransactionInfo: transactionInfo,
	}
	if err = i.repos.Queries(ctx).UpdateInstallmentTransaction(ctx, params); err != nil {
		return errors.Wrap(err, "failed to update first tenure settled")
	}
	return nil
}

func (i installmentRepo) UpdateFirstTenureSettled(ctx context.Context, inst *model.InstallmentInfo) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("UpdateFirstTenureSettled")

	// Re-set to ensure the first tenure settled is updated
	transactionInfo := inst.TransactionInfo
	if !transactionInfo.FirstTenureSettled {
		transactionInfo.FirstTenureSettled = true
	}

	params := &da.UpdateInstallmentTransactionParams{
		ID:              inst.ID,
		TransactionInfo: fromInstallmentTransaction(transactionInfo),
	}
	if err := i.repos.Queries(ctx).UpdateInstallmentTransaction(ctx, params); err != nil {
		i.logger.WithContext(ctx).Errorf("failed to update first tenure settled: %v", err)
		return errors.Wrap(err, "failed to update first tenure settled")
	}
	return nil
}

func (i installmentRepo) UpdateRepayScheduleAfterSync(ctx context.Context, repay *model.InstallmentRepay) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("UpdateRepayScheduleAfterSync")
	logger := i.logger.WithContext(ctx)

	penaltyBin, err := jsoniter.Marshal(repay.PenaltyDetails)
	if err != nil {
		logger.Errorf("failed to marshal penalty details: %v", err)
		return errors.Wrap(err, "failed to marshal penalty details")
	}
	outstandingBin, err := jsoniter.Marshal(repay.OutstandingDetails)
	if err != nil {
		logger.Errorf("failed to marshal outstanding details: %v", err)
		return errors.Wrap(err, "failed to marshal outstanding details")
	}

	params := &da.UpdateRepaymentScheduleAfterSyncParams{
		SeqNo:              repay.SeqNo,
		InstID:             repay.InstallmentID,
		Status:             fromRepayScheduleStatus(repay.Status),
		DueDate:            zutils.NewNullTime(repay.RepayDueDate),
		GraceDueDate:       zutils.NewNullTime(repay.GraceDueDate),
		PenaltyDetails:     penaltyBin,
		OutstandingDetails: outstandingBin,
	}
	err = i.repos.Queries(ctx).UpdateRepaymentScheduleAfterSync(ctx, params)
	if err != nil {
		logger.Errorf("failed to update installment repay after sync: %v", err)
		return errors.Wrap(err, "failed to update installment repay after sync")
	}
	return nil
}

func (i installmentRepo) UpdateEarlyDischargeInfo(ctx context.Context, inst *model.InstallmentInfo) error {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("UpdateEarlyDischargeInfo")
	logger := i.logger.WithContext(ctx)

	earlyDischarge := fromInstallmentEarlyDischarge(inst.EarlyDischarge)
	dischargeBin, err := jsoniter.Marshal(earlyDischarge)
	if err != nil {
		logger.Errorf("failed to marshal early discharge: %v", err)
		return errors.Wrap(err, "failed to marshal early discharge")
	}

	dischargeStatus, err := fromDischargeStatus(inst.DischargeStatus)
	if err != nil {
		logger.Errorf("failed to convert discharge status: %v", err)
		return errors.Wrap(err, "failed to convert discharge status")
	}

	params := &da.UpdateEarlyDischargeInfoParams{
		ID:              inst.ID,
		EarlyDischarge:  dischargeBin,
		DischargeStatus: dischargeStatus,
	}
	err = i.repos.Queries(ctx).UpdateEarlyDischargeInfo(ctx, params)
	if err != nil {
		logger.Errorf("failed to update early discharge info: %v", err)
		return errors.Wrap(err, "failed to update early discharge info")
	}
	return nil
}

func (i *installmentRepo) UpdateEarlyDischargeStatus(ctx context.Context, instID int64, status model.DischargeStatus) error {
	logger := i.logger.WithContext(ctx)

	dischargeStatus, err := fromDischargeStatus(status)
	if err != nil {
		logger.Errorf("failed to convert discharge status: %v", err)
		return errors.Wrap(err, "failed to convert discharge status")
	}

	err = i.repos.Queries(ctx).UpdateDischargeStatus(ctx, &da.UpdateDischargeStatusParams{
		ID:              instID,
		DischargeStatus: dischargeStatus,
	})
	if err != nil {
		logger.Errorf("failed to update discharge status: %v", err)
		return errors.Wrap(err, "failed to update discharge status")
	}
	return nil
}

func (i installmentRepo) UpdateInstallmentRefund(ctx context.Context, instID int64, params *model.InstallmentRefund) error {
	logger := i.logger.WithContext(ctx)

	err := i.repos.Queries(ctx).UpdateInstallmentRefund(ctx, &da.UpdateInstallmentRefundParams{
		ID:         instID,
		RefundInfo: fromInstallmentRefund(params),
	})
	if err != nil {
		logger.Errorf("failed to update installment refund: %v", err)
		return errors.Wrap(err, "failed to update installment refund")
	}
	return nil
}

func (i installmentRepo) ListInstallmentByPartnerIDs(ctx context.Context,
	partnerInstIDs []string) ([]*model.InstallmentInfo, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("ListInstallmentByPartnerIDs")
	logger := i.logger.WithContext(ctx)

	if len(partnerInstIDs) == 0 {
		logger.Warn("partnerInstIDs is empty")
		return []*model.InstallmentInfo{}, nil
	}

	partnerInstIDsParams := make([]sql.NullString, 0, len(partnerInstIDs))
	for _, id := range partnerInstIDs {
		partnerInstIDsParams = append(partnerInstIDsParams, zutils.NewNullString(id))
	}

	insts, err := i.repos.Queries(ctx).ListInstallmentByPartnerRefIDs(ctx, partnerInstIDsParams)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warn("installments not found in db")
		return []*model.InstallmentInfo{}, nil
	}
	if err != nil {
		logger.Errorf("failed to list installments by partner ref ids: %v", err)
		return nil, errors.Wrap(err, "failed to list installments by partner ref ids")
	}
	return toInstallmentsInfo(insts), nil
}

func (i installmentRepo) GetInstallmentByID(ctx context.Context, instID int64) (*model.InstallmentInfo, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("GetInstallmentByID")
	logger := i.logger.WithContext(ctx)

	inst, err := i.repos.Queries(ctx).GetInstallmentByID(ctx, instID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("data not found in db with instID=%d", instID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with instID=%d", instID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by instID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get installment by instID from db")
	}
	return toInstallmentInfo(inst), nil
}

func (i installmentRepo) GetInstallmentByIDForUpdate(ctx context.Context, instID int64) (*model.InstallmentInfo, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("GetInstallmentByIDForUpdate")
	logger := i.logger.WithContext(ctx)

	inst, err := i.repos.Queries(ctx).GetInstallmentByIDForUpdate(ctx, instID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("data not found in db with instID=%d", instID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with instID=%d", instID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by instID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get installment by instID from db")
	}
	return toInstallmentInfo(inst), nil
}

func (i installmentRepo) GetInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*model.InstallmentInfo, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("GetInstallmentByZPTransID")
	logger := i.logger.WithContext(ctx)

	inst, err := i.repos.Queries(ctx).GetInstallmentByZPTransID(ctx, zpTransID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("data not found in db with zpTransID=%d", zpTransID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with zpTransID=%d", zpTransID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by zpTransID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get installment by zpTransID from db")
	}
	return toInstallmentInfo(inst), nil
}

func (i installmentRepo) GetFullInstallmentByUserIDAndZPTransID(
	ctx context.Context, zalopayID, zpTransID int64) (*model.Installment, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("GetFullInstallmentByUserIDAndZPTransID")
	logger := i.logger.WithContext(ctx)

	instInfo, err := i.repos.Queries(ctx).GetInstallmentByZPTransID(ctx, zpTransID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("data not found in db with zpTransID=%d", zpTransID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with zpTransID=%d", zpTransID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by zpTransID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get installment by zpTransID from db")
	}

	if instInfo.ZalopayID != zalopayID {
		logger.Errorf("zalopayID=%d does not match with zalopayID=%d in db", zalopayID, instInfo.ZalopayID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with zalopayID=%d, zpTransID=%d", zalopayID, zpTransID)
	}

	repaySchedules, err := i.repos.Queries(ctx).GetRepaymentScheduleByInstID(ctx, instInfo.ID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("repayment schedules not found in db with instID=%d", instInfo.ID)
		return nil, errors.Wrap(model.ErrInstallmentRepaysNotFound, "repayment schedules not found in db")
	}
	if err != nil {
		logger.Errorf("failed to get repayment schedules by instID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get repayment schedules by instID from db")
	}

	return toFullInstallmentData(instInfo, repaySchedules), nil
}

func (i *installmentRepo) GetFullInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*model.Installment, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("GetFullInstallmentByZPTransID")
	logger := i.logger.WithContext(ctx)

	instInfo, err := i.repos.Queries(ctx).GetInstallmentByZPTransID(ctx, zpTransID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("data not found in db with zpTransID=%d", zpTransID)
		return nil, errors.Wrapf(model.ErrInstallmentNotFound, "data not found in db with zpTransID=%d", zpTransID)
	}
	if err != nil {
		logger.Errorf("failed to get installment by zpTransID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get installment by zpTransID from db")
	}

	repaySchedules, err := i.repos.Queries(ctx).GetRepaymentScheduleByInstID(ctx, instInfo.ID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("repayment schedules not found in db with instID=%d", instInfo.ID)
		return nil, errors.Wrap(model.ErrInstallmentRepaysNotFound, "repayment schedules not found in db")
	}
	if err != nil {
		logger.Errorf("failed to get repayment schedules by instID from db: %v", err)
		return nil, errors.Wrap(err, "failed to get repayment schedules by instID from db")
	}

	return toFullInstallmentData(instInfo, repaySchedules), nil
}

func (i installmentRepo) ListInstallmentPeriodicSync(ctx context.Context,
	cutoffTime time.Time, pagination *model.Pagination) ([]*model.InstallmentInfo, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("ListInstallmentPeriodicSync")
	logger := i.logger.WithContext(ctx)

	insts, err := i.repos.Queries(ctx).ListInstallmentPeriodicSync(ctx, &da.ListInstallmentPeriodicSyncParams{
		Limit:     int32(pagination.Limit),
		Offset:    int32(*pagination.Offset),
		CreatedAt: zutils.NewNullTime(cutoffTime),
	})
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warn("installments not found in db")
		return []*model.InstallmentInfo{}, nil
	}
	if err != nil {
		logger.Errorf("failed to list installments for periodic sync: %v", err)
		return nil, errors.Wrap(err, "failed to get list opened installments")
	}
	return toInstallmentsInfo(insts), nil
}

func (i *installmentRepo) ListFullOpenInstallmentByUserAndAcctID(ctx context.Context, zalopayID int64, accountID int64) ([]*model.Installment, error) {
	defer i.repos.metrics.MonitoredTime(installmentRepoName)("ListFullOpenInstallmentByUserAndAcctID")
	logger := i.logger.WithContext(ctx)

	params := &da.ListOpenInstallmentByZalopayIDAndAcctIDParams{
		ZalopayID: zalopayID,
		AccountID: accountID,
	}
	insts, err := i.repos.Queries(ctx).ListOpenInstallmentByZalopayIDAndAcctID(ctx, params)
	if err != nil {
		logger.Errorf("failed to get list installment by zalopayID and accountID, zalopayID=%v, accountID=%v", zalopayID, accountID)
		return []*model.Installment{}, errors.Wrapf(err, "failed to get list installment by zalopayID and accountID")
	}
	if len(insts) == 0 {
		logger.Warn("empty installments data")
		return []*model.Installment{}, nil
	}

	instIDs := make([]int64, len(insts))
	for i, inst := range insts {
		instIDs[i] = inst.ID
	}

	repays, err := i.repos.Queries(ctx).ListRepaymentScheduleByInstIDs(ctx, instIDs)
	if err != nil {
		return []*model.Installment{}, errors.Wrap(err, "failed to get list repayment schedule by installment ids")
	}
	if len(repays) == 0 {
		return []*model.Installment{}, errors.Errorf("empty list schedules")
	}

	result := make([]*model.Installment, 0, len(insts))
	for _, inst := range insts {
		instRepays := filterRepaySchedules(inst, repays)
		if len(instRepays) == 0 {
			return []*model.Installment{}, errors.Errorf("empty list schedules")
		}
		result = append(result, &model.Installment{
			Info:   toInstallmentInfo(inst),
			Repays: toRepaySchedules(instRepays),
		})
	}
	return result, nil
}

func toFullInstallmentData(
	instInfo *da.Installments,
	schedules []*da.ScheduledRepayments) *model.Installment {
	return &model.Installment{
		Info:   toInstallmentInfo(instInfo),
		Repays: toRepaySchedules(schedules),
	}
}

func toInstallmentsInfo(insts []*da.Installments) []*model.InstallmentInfo {
	var result []*model.InstallmentInfo
	for _, inst := range insts {
		result = append(result, toInstallmentInfo(inst))
	}
	return result
}

func toRepaySchedules(schedules []*da.ScheduledRepayments) []*model.InstallmentRepay {
	var result []*model.InstallmentRepay
	for _, schedule := range schedules {
		result = append(result, toRepaySchedule(schedule))
	}
	return result
}

func toInstallmentInfo(inst *da.Installments) *model.InstallmentInfo {
	result := &model.InstallmentInfo{
		ID:              inst.ID,
		Tenure:          inst.Tenure,
		Status:          toInstallmentStatus(inst.Status),
		ZalopayID:       inst.ZalopayID,
		AccountID:       inst.AccountID,
		ZPTransID:       inst.ZpTransID,
		PaidTenure:      inst.PaidTenure.Int32,
		DaysPastDue:     inst.DaysPastDue,
		StartDate:       inst.StartDate.Time,
		EndDate:         inst.EndDate.Time,
		CurrDueDate:     inst.CurrDueDate.Time,
		NextDueDate:     inst.NextDueDate.Time,
		PartnerCode:     partner.CodeFromString(inst.PartnerCode),
		PartnerInstID:   inst.PartnerInstID.String,
		DisburseAmount:  inst.DisburseAmount.Int64,
		DischargeStatus: toDischargeStatus(inst.DischargeStatus),
		RefundInfo:      toInstallmentRefund(inst.RefundInfo),
		SimulationInfo:  toInstallmentSimulation(inst),
		TransactionInfo: toInstallmentTransaction(inst.TransactionInfo),
		OutstandingInfo: toInstallmentOutstanding(inst.OutstandingInfo),
		EarlyDischarge:  toInstallmentEarlyDischarge(inst.EarlyDischarge),
		CreatedAt:       inst.CreatedAt.Time,
		UpdatedAt:       inst.UpdatedAt.Time,
	}
	return result
}

func fromInstallmentRefund(refund *model.InstallmentRefund) *entity.InstallmentRefund {
	if refund == nil {
		return nil
	}
	return &entity.InstallmentRefund{
		UpdateVersion:     refund.UpdateVersion,
		UserTopupAmount:   refund.UserTopupAmount,
		NetRefundAmount:   refund.NetRefundAmount,
		TotalRefundAmount: refund.TotalRefundAmount,
		UserTopupRequired: refund.UserTopupRequired,
	}
}

func fromInstallmentStatus(status model.InstallmentStatus) da.InstallmentsStatus {
	switch status {
	case model.InstallmentStatusInit:
		return da.InstallmentsStatusInit
	case model.InstallmentStatusOpen:
		return da.InstallmentsStatusOpen
	case model.InstallmentStatusClosed:
		return da.InstallmentsStatusClosed
	default:
		return da.InstallmentsStatusInit
	}
}

func fromDischargeStatus(status model.DischargeStatus) (string, error) {
	switch status {
	case model.DischargeStatusPending:
		return model.DischargeStatusPending.String(), nil
	case model.DischargeStatusProcessing:
		return model.DischargeStatusProcessing.String(), nil
	case model.DischargeStatusComplete:
		return model.DischargeStatusComplete.String(), nil
	default:
		return "", errors.Errorf("unknown discharge status: %v", status)
	}
}

func fromRepayScheduleStatus(status model.RepayScheduleStatus) da.ScheduledRepaymentsStatus {
	switch status {
	case model.RepayScheduleStatusInit:
		return da.ScheduledRepaymentsStatusInit
	case model.RepayScheduleStatusDue:
		return da.ScheduledRepaymentsStatusDue
	case model.RepayScheduleStatusPaid:
		return da.ScheduledRepaymentsStatusPaid
	default:
		return da.ScheduledRepaymentsStatusInit
	}
}

func fromInstallmentTransaction(transaction model.InstallmentTransaction) entity.InstallmentTransaction {
	return entity.InstallmentTransaction{
		TransactionID:      transaction.TransID,
		TransactionDesc:    transaction.TransDesc,
		FirstTenureSettled: transaction.FirstTenureSettled,
	}
}

func fromInstallmentEarlyDischarge(info *model.InstallmentEarlyDischarge) *entity.EarlyDischargeDetails {
	if info == nil || info.Details == nil {
		return nil
	}
	return &entity.EarlyDischargeDetails{
		SyncedAt:             info.SyncedAt,
		OutstandingPrincipal: info.Details.OutstandingPrincipal,
		OutstandingInterest:  info.Details.OutstandingInterest,
		OutstandingPenalty:   info.Details.OutstandingPenalty,
		EarlyDischargeFee:    info.Details.EarlyDischargeFee,
		TotalDischargeAmount: info.Details.TotalDischargeAmount,
	}
}

func toRepayScheduleStatus(status da.ScheduledRepaymentsStatus) model.RepayScheduleStatus {
	switch status {
	case da.ScheduledRepaymentsStatusInit:
		return model.RepayScheduleStatusInit
	case da.ScheduledRepaymentsStatusDue:
		return model.RepayScheduleStatusDue
	case da.ScheduledRepaymentsStatusPaid:
		return model.RepayScheduleStatusPaid
	default:
		return model.RepayScheduleStatusInit
	}
}

func toInstallmentStatus(status da.InstallmentsStatus) model.InstallmentStatus {
	switch status {
	case da.InstallmentsStatusInit:
		return model.InstallmentStatusInit
	case da.InstallmentsStatusOpen:
		return model.InstallmentStatusOpen
	case da.InstallmentsStatusClosed:
		return model.InstallmentStatusClosed
	default:
		return model.InstallmentStatusUnknown
	}
}

func toDischargeStatus(status string) model.DischargeStatus {
	switch status {
	case model.DischargeStatusPending.String():
		return model.DischargeStatusPending
	case model.DischargeStatusProcessing.String():
		return model.DischargeStatusProcessing
	case model.DischargeStatusComplete.String():
		return model.DischargeStatusComplete
	default:
		return model.DischargeStatusPending
	}
}

func toInstallmentTransaction(transaction entity.InstallmentTransaction) model.InstallmentTransaction {
	return model.InstallmentTransaction{
		TransID:            transaction.TransactionID,
		TransDesc:          transaction.TransactionDesc,
		FirstTenureSettled: transaction.FirstTenureSettled,
	}
}

func toInstallmentFeeDetails(feeDetails entity.JsonRawMessage) []model.FeeDetail {
	var result []model.FeeDetail
	if feeDetails == nil {
		return []model.FeeDetail{}
	}
	if err := jsoniter.Unmarshal(feeDetails, &result); err != nil {
		return []model.FeeDetail{}
	}
	return result
}

func toInstallmentRefund(refund *entity.InstallmentRefund) *model.InstallmentRefund {
	if refund == nil {
		return nil
	}
	return &model.InstallmentRefund{
		UpdateVersion:     refund.UpdateVersion,
		NetRefundAmount:   refund.NetRefundAmount,
		TotalRefundAmount: refund.TotalRefundAmount,
		UserTopupAmount:   refund.UserTopupAmount,
		UserTopupRequired: refund.UserTopupRequired,
	}
}

func toInstallmentSimulation(inst *da.Installments) model.InstallmentSimulation {
	return model.InstallmentSimulation{
		EmiAmount:      inst.EmiAmount.Int64,
		InterestRate:   inst.InterestRate.Float64,
		InterestAmount: inst.InterestAmount.Int64,
		TotalFeeAmount: inst.TotalFeeAmount.Int64,
		TotalAmountDue: inst.TotalDueAmount.Int64,
		FeeDetails:     toInstallmentFeeDetails(inst.FeeDetails),
	}
}

func toInstallmentOutstanding(outstanding entity.JsonRawMessage) *model.InstallmentOutstanding {
	var result model.InstallmentOutstanding
	if outstanding == nil {
		return nil
	}
	if err := jsoniter.Unmarshal(outstanding, &result); err != nil {
		return nil
	}
	return &result
}

func toInstallmentEarlyDischarge(discharge entity.JsonRawMessage) *model.InstallmentEarlyDischarge {
	var result entity.EarlyDischargeDetails
	if discharge == nil {
		return nil
	}
	if err := jsoniter.Unmarshal(discharge, &result); err != nil {
		return nil
	}
	return &model.InstallmentEarlyDischarge{
		SyncedAt: result.SyncedAt,
		Details: &model.EarlyDischargeDetails{
			OutstandingPrincipal: result.OutstandingPrincipal,
			OutstandingInterest:  result.OutstandingInterest,
			OutstandingPenalty:   result.OutstandingPenalty,
			EarlyDischargeFee:    result.EarlyDischargeFee,
			TotalDischargeAmount: result.TotalDischargeAmount,
		},
	}

}

func toRepaySchedule(schedule *da.ScheduledRepayments) *model.InstallmentRepay {
	return &model.InstallmentRepay{
		ID:                 schedule.ID,
		SeqNo:              schedule.SeqNo,
		Status:             toRepayScheduleStatus(schedule.Status),
		InstallmentID:      schedule.InstID,
		PartnerInstID:      schedule.PartnerInstID,
		RepayDueDate:       schedule.DueDate.Time,
		GraceDueDate:       schedule.GraceDueDate.Time,
		EmiAmount:          schedule.EmiAmount.Int64,
		InterestAmount:     schedule.InterestAmount.Int64,
		PrincipalAmount:    schedule.PrincipalAmount.Int64,
		PenaltyDetails:     toInstallmentRepayPenalty(schedule.PenaltyDetails),
		OutstandingDetails: toInstallmentRepayOutstanding(schedule.OutstandingDetails),
	}
}

func toInstallmentRepayPenalty(penalty json.RawMessage) *model.InstallmentRepayPenalty {
	var result model.InstallmentRepayPenalty
	if err := jsoniter.Unmarshal(penalty, &result); err != nil {
		return nil
	}
	return &result
}

func toInstallmentRepayOutstanding(outstanding json.RawMessage) *model.InstallmentRepayOutstanding {
	var result model.InstallmentRepayOutstanding
	if err := jsoniter.Unmarshal(outstanding, &result); err != nil {
		return nil
	}
	return &result
}

func filterRepaySchedules(inst *da.Installments, repays []*da.ScheduledRepayments) []*da.ScheduledRepayments {
	result := make([]*da.ScheduledRepayments, 0)
	for _, r := range repays {
		if r.InstID == inst.ID {
			result = append(result, r)
		}
	}
	slices.SortFunc(result, func(a *da.ScheduledRepayments, b *da.ScheduledRepayments) int {
		return int(a.ID) - int(b.ID)
	})
	return result
}
