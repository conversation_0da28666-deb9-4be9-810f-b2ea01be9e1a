package repo

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/redis"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
)

type planRepo struct {
	logger *log.Helper
	repos  *Repository
	cache  redis.CacheNoCaller
	keygen keygen.RedisKeyGenerator
}

const (
	instPlanKeyPattern = "installment:plans:%d:%d:%s"
)

func NewPlanRepo(kLogger log.Logger, repos *Repository, keygen keygen.RedisKeyGenerator) _interface.PlanRepo {
	logger := log.NewHelper(log.With(kLogger, "repo", "plans"))
	return &planRepo{
		logger: logger,
		repos:  repos,
		keygen: keygen,
		cache:  repos.redisCache,
	}
}

func (p planRepo) PersistPlanOptions(ctx context.Context,
	zalopayID int64, orderInfo model.OrderRequest, plansOpts []*model.Plan) error {
	defer p.repos.metrics.MonitoredTime(planRepoName)("PersistPlanOptions")
	logger := p.logger.WithContext(ctx)
	cacheKey := p.getInstallmentPlanKey(zalopayID, orderInfo.AppID, orderInfo.AppTransID)
	cacheExp := time.Minute * 30
	cacheData := map[string]interface{}{
		entity.OrderAmountField.String(): orderInfo.ChargeAmount,
		entity.PlanOptionsField.String(): model.Plans(plansOpts),
	}

	err := p.repos.redisCache.HSetWithMap(ctx, cacheKey, cacheData)
	if err != nil {
		logger.Errorf("set plan options to cache failed, err=%v", err)
		return errors.Wrap(err, "set plan options to cache failed")
	}

	go p.SetSessionExpired(ctx, cacheKey, cacheExp)
	return nil
}

func (p planRepo) SetSessionExpired(ctx context.Context, cacheKey string, expiredIn time.Duration) error {
	defer p.repos.metrics.MonitoredTime(planRepoName)("SetSessionExpired")
	logger := p.logger.WithContext(ctx)
	redisCli := p.cache.GetRedisClient()
	newCtx := context.WithoutCancel(ctx)
	expAt := time.Now().Add(expiredIn)
	expAtKey := entity.SessionExpiredAtField.String()

	ttl, err := redisCli.TTL(newCtx, cacheKey).Result()
	if err != nil {
		logger.Errorf("get plan info ttl failed, err=%v", err)
		return errors.Wrap(err, "get plan info ttl failed")
	}
	if ttl == -2 || ttl >= 0 {
		logger.Errorf("plan info not found or has set ttl, cacheKey=%s", cacheKey)
		return nil
	}

	if err = redisCli.Expire(newCtx, cacheKey, expiredIn).Err(); err != nil {
		logger.Errorf("set plan info expire failed, err=%v", err)
		return errors.Wrap(err, "set plan info expire failed")
	}

	if err = redisCli.HSet(newCtx, cacheKey, expAtKey, expAt).Err(); err != nil {
		logger.Errorf("set plan session expire time failed, err=%v", err)
		return errors.Wrap(err, "set plan session expire time failed")
	}

	return nil
}

func (p planRepo) RetrievePlanOptions(ctx context.Context,
	zalopayID int64, orderInfo model.OrderRequest) ([]*model.Plan, error) {
	defer p.repos.metrics.MonitoredTime(planRepoName)("RetrievePlanOptions")
	logger := p.logger.WithContext(ctx)
	cacheKey := p.getInstallmentPlanKey(zalopayID, orderInfo.AppID, orderInfo.AppTransID)
	cacheField := entity.PlanOptionsField.String()

	var plans []*model.Plan
	err := p.cache.HGet(ctx, cacheKey, cacheField, &plans)
	if errors.Is(err, redis.ErrNotFound) {
		logger.Errorf("plan options not found in cache, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrPlanOptionsNotFound, "plan options not found in cache")
	}
	if err != nil {
		logger.Errorf("get plan options from cache failed, err=%v", err)
		return nil, errors.Wrap(err, "get plan options from cache failed")
	}
	if len(plans) == 0 {
		logger.Errorf("plan options is empty, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrPlanOptionsIsEmpty, "plan options is empty")
	}

	return plans, nil
}

func (p planRepo) RetrieveFullPlanInfo(ctx context.Context,
	zalopayID int64, orderInfo model.OrderRequest) (*model.PlanPersist, error) {
	defer p.repos.metrics.MonitoredTime(planRepoName)("RetrieveFullPlanInfo")
	logger := p.logger.WithContext(ctx)
	cacheKey := p.getInstallmentPlanKey(zalopayID, orderInfo.AppID, orderInfo.AppTransID)

	cacheData, err := p.cache.HGetAll(ctx, cacheKey)
	if errors.Is(err, redis.ErrNotFound) {
		logger.Errorf("plan info not found in cache, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrFullPlanNotFound, "plan info not found in cache")
	}
	if err != nil {
		logger.Errorf("get plan info from cache failed, err=%v", err)
		return nil, errors.Wrap(err, "get plan info from cache failed")
	}

	planInfo, err := entity.NewPlanCacheFromMap(cacheData)
	if err != nil {
		logger.Errorf("convert cache data to plan info failed, err=%v", err)
		return nil, errors.Wrap(model.ErrFullPlanInvalid, "convert cache data to plan info failed")
	}
	if planInfo == nil {
		logger.Errorf("plan info is nil, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrFullPlanInvalid, "plan info is nil")
	}

	return &model.PlanPersist{
		OrderAmount:  planInfo.OrderAmount,
		PlanOptions:  planInfo.PlanOptions,
		PlanSelected: planInfo.PlanSelected,
	}, nil
}

func (p planRepo) RetrievePlanSelected(ctx context.Context,
	zalopayID int64, orderInfo model.OrderRequest) (*model.Plan, error) {
	defer p.repos.metrics.MonitoredTime(planRepoName)("RetrievePlanSelected")
	logger := p.logger.WithContext(ctx)
	cacheKey := p.getInstallmentPlanKey(zalopayID, orderInfo.AppID, orderInfo.AppTransID)

	var plan *model.Plan
	err := p.cache.HGet(ctx, cacheKey, entity.PlanSelectedField.String(), &plan)
	if errors.Is(err, redis.ErrNotFound) {
		logger.Errorf("plan selected not found in cache, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrPlanItemNotFound, "plan selected not found in cache")
	}
	if err != nil {
		logger.Errorf("get plan selected from cache failed, err=%v", err)
		return nil, errors.Wrap(err, "get plan selected from cache failed")
	}
	if plan == nil {
		logger.Errorf("plan selected is nil, cacheKey=%s", cacheKey)
		return nil, errors.Wrap(model.ErrPlanItemIsEmpty, "plan selected is nil")
	}

	return plan, nil
}

func (p planRepo) PersistPlanSelected(ctx context.Context, planKeyData model.PlanKeyData, planResult *model.Plan) error {
	defer p.repos.metrics.MonitoredTime(planRepoName)("PersistPlanSelected")
	cacheKey := p.getInstallmentPlanKey(planKeyData.ZalopayID, planKeyData.AppID, planKeyData.AppTransID)
	cacheData := map[string]interface{}{
		entity.PlanSelectedField.String(): planResult,
	}

	if err := p.cache.HSetWithMap(ctx, cacheKey, cacheData); err != nil {
		p.logger.WithContext(ctx).Errorf("persist plan selected to cache failed, err=%v", err)
		return errors.Wrap(err, "set plan selected to cache failed")
	}
	return nil
}

func (p planRepo) PersistFullPlanInfo(
	ctx context.Context, zalopayID int64,
	orderInfo model.OrderRequest,
	planData *model.PlanPersist) error {
	defer p.repos.metrics.MonitoredTime(planRepoName)("PersistFullPlanInfo")
	logger := p.logger.WithContext(ctx)
	cacheKey := p.getInstallmentPlanKey(zalopayID, orderInfo.AppID, orderInfo.AppTransID)
	cacheData := map[string]interface{}{
		entity.OrderAmountField.String():  orderInfo.ChargeAmount,
		entity.PlanOptionsField.String():  model.Plans(planData.PlanOptions),
		entity.PlanSelectedField.String(): planData.PlanSelected,
	}

	if err := p.cache.HSetWithMap(ctx, cacheKey, cacheData); err != nil {
		logger.Errorf("set full plan data to cache failed, err=%v", err)
		return errors.Wrap(err, "set plan data to cache failed")
	}

	return nil
}

func (p planRepo) getInstallmentPlanKey(zalopayID int64, appID int32, appTransID string) string {
	key, err := p.keygen.Generate(fmt.Sprintf(instPlanKeyPattern, zalopayID, appID, appTransID))
	if err != nil {
		p.logger.Errorf("generate plan key failed, err=%v", err)
		return fmt.Sprintf(instPlanKeyPattern, zalopayID, appID, appTransID)
	}
	return key
}
