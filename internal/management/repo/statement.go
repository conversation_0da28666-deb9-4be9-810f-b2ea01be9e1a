package repo

import (
	"context"
	"database/sql"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type statementRepo struct {
	repos  *Repository
	logger *log.Helper
}

func NewStatementRepo(repos *Repository, kLogger log.Logger) _interface.StatementRepo {
	logger := log.With(kLogger, "repo", "statement")
	return &statementRepo{
		repos:  repos,
		logger: log.<PERSON>elper(logger),
	}
}

func (s statementRepo) CreateStatement(ctx context.Context, statement *model.Statement) (int64, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("CreateStatement")
	logger := s.logger.WithContext(ctx)

	result, err := s.repos.Queries(ctx).UpsertStatement(ctx, &da.UpsertStatementParams{
		Period:            statement.Period,
		ZalopayID:         statement.ZalopayID,
		AccountID:         statement.AccountID,
		PartnerCode:       statement.PartnerCode.String(),
		DueDate:           statement.DueDate,
		IncurredDate:      statement.IncurredDate,
		OutstandingAmount: statement.OutstandingAmount,
		OutstandingRepaid: statement.OutstandingRepaid,
	})
	if err != nil {
		logger.Errorf("failed to create statement into db: %v", err)
		return 0, errors.Wrap(err, "failed to create statement into db")
	}
	stmtID, err := result.LastInsertId()
	if err != nil {
		logger.Errorf("failed to get last insert id: %v", err)
		return 0, errors.Wrap(err, "failed to get last insert id")
	}
	return stmtID, nil
}

func (s statementRepo) CreateInstallment(ctx context.Context, installment *model.StatementInstallment) error {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("CreateInstallment")
	logger := s.logger.WithContext(ctx)
	dueDate := installment.DueDate
	stmtDate := installment.StatementDate

	if dueDate.IsZero() {
		dueDate = time.Time{}
	}
	if stmtDate.IsZero() {
		stmtDate = time.Time{}
	}

	err := s.repos.Queries(ctx).UpsertStmtInstallment(ctx, &da.UpsertStmtInstallmentParams{
		StatementDate:      stmtDate,
		DueDate:            zutils.NewNullTime(dueDate),
		ZalopayID:          installment.ZalopayID,
		AccountID:          installment.AccountID,
		StatementID:        installment.StatementID,
		PartnerCode:        installment.PartnerCode.String(),
		PartnerInstID:      installment.PartnerInstID,
		InstallmentAmount:  installment.InstallmentAmount,
		OutstandingDetails: convertToStatementInstallmentOutsEntity(installment.OutstandingData),
	})
	if err != nil {
		logger.Errorf("failed to create installment into db: %v", err)
		return errors.Wrap(err, "failed to create installment into db")
	}
	return nil
}

func (s statementRepo) QueryStatementByIncurDate(ctx context.Context,
	zalopayID, accountID int64, incurredDate time.Time) (*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryStatementByIncurDate")
	logger := s.logger.WithContext(ctx)

	stmt, err := s.repos.Queries(ctx).GetUserStatementByIncurredDate(ctx, &da.GetUserStatementByIncurredDateParams{
		ZalopayID:    zalopayID,
		AccountID:    accountID,
		IncurredDate: incurredDate,
	})
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("failed to get statement by incurred date: %v", err)
		return nil, model.ErrStatementNotFound
	}
	if err != nil {
		logger.Errorf("failed to get statement by incurred date: %v", err)
		return nil, errors.Wrap(err, "failed to get statement by incurred date")
	}
	if stmt == nil {
		logger.Errorf("statement is empty data")
		return nil, model.ErrStatementEmptyData
	}
	return convertToStatement(stmt), nil
}

func (s statementRepo) QueryLatestStatement(ctx context.Context, zalopayID, accountID int64) (*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryLatestStatement")
	logger := s.logger.WithContext(ctx)

	stmt, err := s.repos.Queries(ctx).GetUserLatestStatement(ctx, &da.GetUserLatestStatementParams{
		ZalopayID: zalopayID,
		AccountID: accountID,
	})
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("latest statement not found in db: %v", err)
		return nil, model.ErrStatementNotFound
	}
	if err != nil {
		logger.Errorf("failed to get latest statement in db: %v", err)
		return nil, errors.Wrap(err, "failed to get latest statement")
	}
	if stmt == nil {
		logger.Errorf("latest statement is empty data")
		return nil, model.ErrStatementEmptyData
	}
	return convertToStatement(stmt), nil
}

func (s statementRepo) QueryStatementByID(ctx context.Context, stmtID int64) (*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryStatementByID")
	logger := s.logger.WithContext(ctx)

	stmt, err := s.repos.Queries(ctx).GetStatementByID(ctx, stmtID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("statement not found with id %d: %v", stmtID, err)
		return nil, model.ErrStatementNotFound
	}
	if err != nil {
		logger.Errorf("failed to get statement by id %d: %v", stmtID, err)
		return nil, errors.Wrap(err, "failed to get statement by id")
	}
	if stmt == nil {
		logger.Errorf("statement is empty data")
		return nil, model.ErrStatementEmptyData
	}
	return convertToStatement(stmt), nil
}

func (s statementRepo) QueryDueDateByIncurDateAndPartner(ctx context.Context,
	partner partner.PartnerCode, incurredDate time.Time) (time.Time, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryDueDateByIncurDateAndPartner")
	logger := s.logger.WithContext(ctx)

	params := &da.GetStatementDueDateByIncurredDateAndPartnerParams{
		IncurredDate: incurredDate,
		PartnerCode:  partner.String(),
	}
	dueDate, err := s.repos.Queries(ctx).GetStatementDueDateByIncurredDateAndPartner(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Errorf("statement data not found by incurred date and parnter: %v", err)
		return time.Time{}, model.ErrStatementNotFound
	}
	if err != nil {
		logger.Errorf("failed to get statement due date by incurred date: %v", err)
		return time.Time{}, errors.Wrap(err, "failed to get statement by incurred date")
	}
	if dueDate.IsZero() {
		logger.Errorf("statement due date invalid")
		return time.Time{}, model.ErrStatementEmptyData
	}
	return dueDate, nil
}

func (s statementRepo) QueryStatementsByIncurDateAndPartner(
	ctx context.Context, partner partner.PartnerCode,
	incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryStatementsByIncurDateAndPartner")
	logger := s.logger.WithContext(ctx)

	params := &da.ListStatementByIncurredDateAndPartnerParams{
		PartnerCode:  partner.String(),
		IncurredDate: incurredDate,
		ID:           cast.ToInt64(paging.Cursor),
		Limit:        cast.ToInt32(paging.Limit),
	}
	stmts, err := s.repos.Queries(ctx).ListStatementByIncurredDateAndPartner(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("list statements not found by incurred date and partner: %v", err)
		return []*model.Statement{}, nil
	}
	if err != nil {
		logger.Errorf("failed to get list statements by incurred date and partner: %v", err)
		return nil, errors.Wrap(err, "failed to get list statements by incurred date")
	}
	return convertToStatements(stmts), nil
}

func (s statementRepo) QueryOutstandingStatementsByIncurDateAndPartner(
	ctx context.Context, partner partner.PartnerCode,
	incurredDate time.Time, paging *model.Pagination) ([]*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryOutstandingStatementsByIncurDateAndPartner")
	logger := s.logger.WithContext(ctx)

	params := &da.ListOutstandingStatementByIncurredDateAndPartnerParams{
		PartnerCode:  partner.String(),
		IncurredDate: incurredDate,
		ID:           cast.ToInt64(paging.Cursor),
		Limit:        cast.ToInt32(paging.Limit),
	}
	stmts, err := s.repos.Queries(ctx).ListOutstandingStatementByIncurredDateAndPartner(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("list outstanding statements not found by incurred date and partner: %v", err)
		return []*model.Statement{}, nil
	}
	if err != nil {
		logger.Errorf("failed to get list outstanding statements by incurred date and partner: %v", err)
		return nil, errors.Wrap(err, "failed to get outstanding statements by incurred date")
	}
	return convertToStatements(stmts), nil
}

func (s statementRepo) QueryInstallmentsByStatementID(ctx context.Context, stmtID int64) ([]*model.StatementInstallment, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryInstallmentsByStatementID")
	logger := s.logger.WithContext(ctx)

	stmts, err := s.repos.Queries(ctx).ListStmtInstallmentByStmtId(ctx, stmtID)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("list installments not found by statement id %v", stmtID)
		return []*model.StatementInstallment{}, nil
	}
	if err != nil {
		logger.Errorf("failed to get installments by statement id %v: %v", stmtID, err)
		return nil, errors.Wrap(err, "failed to get installments by statement id")
	}
	return convertToStatementInstallments(stmts), nil
}

func (s statementRepo) QueryInstallmentsByUserAndStatementID(ctx context.Context,
	zalopayID, accountID, stmtID int64) ([]*model.StatementInstallment, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryInstallmentsByUserAndStatementID")
	logger := s.logger.WithContext(ctx)

	params := &da.ListStmtInstallmentByUserAndStmtIdParams{
		ZalopayID:   zalopayID,
		AccountID:   accountID,
		StatementID: stmtID,
	}
	stmts, err := s.repos.Queries(ctx).ListStmtInstallmentByUserAndStmtId(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnw("msg", "list installments not found by user and statement id",
			"zalopayID", zalopayID, "accountID", accountID, "stmtID", stmtID)
		return []*model.StatementInstallment{}, nil
	}
	if err != nil {
		logger.Errorw("failed to get installments by user and statement id",
			"zalopayID", zalopayID, "accountID", accountID, "stmtID", stmtID, "err", err.Error())
		return nil, errors.Wrap(err, "failed to get installments by statement id")
	}
	return convertToStatementInstallments(stmts), nil
}

func (s statementRepo) QueryStatementsByIncurDateRange(ctx context.Context,
	zalopayID, accountID int64, fromTime, toTime time.Time) ([]*model.Statement, error) {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("QueryStatementsByIncurDateRange")
	logger := s.logger.WithContext(ctx)

	stmts, err := s.repos.Queries(ctx).ListUserStatementByIncurredDateRange(ctx, &da.ListUserStatementByIncurredDateRangeParams{
		ZalopayID:        zalopayID,
		AccountID:        accountID,
		FromIncurredDate: fromTime,
		ToIncurredDate:   toTime,
	})
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("list statements not found by incurred date range: %v", err)
		return nil, model.ErrStatementNotFound
	}
	if err != nil {
		logger.Errorf("failed to get list statements by incurred date range: %v", err)
		return nil, errors.Wrap(err, "failed to get list statements by incurred date range")
	}
	return convertToStatements(stmts), nil
}

func (s statementRepo) UpdateStatementOutstandingByIncurDate(ctx context.Context, statement *model.Statement) error {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("UpdateStatementOutstandingByIncurDate")
	logger := s.logger.WithContext(ctx)

	err := s.repos.Queries(ctx).UpdateStatementOutstandingByIncurredDate(ctx, &da.UpdateStatementOutstandingByIncurredDateParams{
		ZalopayID:         statement.ZalopayID,
		AccountID:         statement.AccountID,
		IncurredDate:      statement.IncurredDate,
		OutstandingAmount: statement.OutstandingAmount,
		OutstandingRepaid: statement.OutstandingRepaid,
	})
	if err != nil {
		logger.Errorf("failed to update statement outstanding info by incurred date: %v", err)
		return errors.Wrap(err, "failed to update statement outstanding info by incurred date")
	}
	return nil
}

func (s statementRepo) UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, statement *model.Statement) error {
	defer s.repos.metrics.MonitoredTime(statementRepoName)("UpdateStatementOutstandingAndPenaltyByID")
	logger := s.logger.WithContext(ctx)

	err := s.repos.Queries(ctx).UpdateStatementOutstandingAndPenaltyByID(ctx, &da.UpdateStatementOutstandingAndPenaltyByIDParams{
		ID:                statement.ID,
		PenaltyAmount:     statement.PenaltyAmount,
		OutstandingAmount: statement.OutstandingAmount,
		OutstandingRepaid: statement.OutstandingRepaid,
		Metadata:          convertToStatementMetadataEntity(statement.Metadata),
	})
	if err != nil {
		logger.Errorf("failed to update statement outstanding and penalty by id %d: %v", statement.ID, err)
		return errors.Wrapf(err, "failed to update statement outstanding and penalty by id %d", statement.ID)
	}
	return nil
}

func convertToStatements(stmt []*da.Statements) []*model.Statement {
	var entities []*model.Statement
	for _, s := range stmt {
		entities = append(entities, convertToStatement(s))
	}
	return entities
}

func convertToStatement(stmt *da.Statements) *model.Statement {
	metadata := convertToStatementMetadata(stmt.Metadata)
	partnerCode := partner.CodeFromString(stmt.PartnerCode)
	return &model.Statement{
		ID:                stmt.ID,
		Period:            stmt.Period,
		AccountID:         stmt.AccountID,
		ZalopayID:         stmt.ZalopayID,
		PartnerCode:       partnerCode,
		DueDate:           stmt.DueDate,
		IncurredDate:      stmt.IncurredDate,
		PenaltyAmount:     stmt.PenaltyAmount,
		OutstandingAmount: stmt.OutstandingAmount,
		OutstandingRepaid: stmt.OutstandingRepaid,
		Metadata:          metadata,
		CreatedAt:         stmt.CreatedAt.Time,
		UpdatedAt:         stmt.UpdatedAt.Time,
	}
}

func convertToStatementMetadata(metadata entity.StatementMetadata) model.StatementMetadata {
	return model.StatementMetadata{
		PenaltySource: convertToStatementPenaltySource(metadata.PenaltySource),
	}
}

func convertToStatementPenaltySource(source string) model.StatementPenaltySource {
	switch source {
	case model.StatementPenaltySourceCalculated.String():
		return model.StatementPenaltySourceCalculated
	default:
		return model.StatementPenaltySourceUnknown
	}
}

func convertToStatementInstallments(stmts []*da.StatementsInstallments) []*model.StatementInstallment {
	var entities []*model.StatementInstallment
	for _, stmt := range stmts {
		entities = append(entities, convertToStatementInstallment(stmt))
	}
	return entities
}

func convertToStatementInstallment(stmt *da.StatementsInstallments) *model.StatementInstallment {
	return &model.StatementInstallment{
		ID:                stmt.ID,
		ZalopayID:         stmt.ZalopayID,
		AccountID:         stmt.AccountID,
		DueDate:           stmt.DueDate.Time,
		StatementID:       stmt.StatementID,
		StatementDate:     stmt.StatementDate,
		PartnerCode:       partner.CodeFromString(stmt.PartnerCode),
		PartnerInstID:     stmt.PartnerInstID,
		InstallmentAmount: stmt.InstallmentAmount,
		OutstandingData:   convertToStatementInstallmentOuts(stmt.OutstandingDetails),
	}
}

func convertToStatementInstallmentOuts(outs entity.StatementInstallmentOuts) *model.StatementInstallmentOuts {
	return &model.StatementInstallmentOuts{
		TotalOutstanding:        outs.OutstandingTotal,
		OutstandingPrincipal:    outs.OutstandingPrincipal,
		OutstandingInterest:     outs.OutstandingInterest,
		OutstandingPenalty:      outs.OutstandingPenalty,
		OutstandingDuePrincipal: outs.OutstandingDuePrincipal,
		OutstandingDueInterest:  outs.OutstandingDueInterest,
		OutstandingDuePenalty:   outs.OutstandingDuePenalty,
	}
}

func convertToStatementInstallmentOutsEntity(outs *model.StatementInstallmentOuts) entity.StatementInstallmentOuts {
	return entity.StatementInstallmentOuts{
		OutstandingTotal:        outs.TotalOutstanding,
		OutstandingPrincipal:    outs.OutstandingPrincipal,
		OutstandingInterest:     outs.OutstandingInterest,
		OutstandingPenalty:      outs.OutstandingPenalty,
		OutstandingDuePrincipal: outs.OutstandingDuePrincipal,
		OutstandingDueInterest:  outs.OutstandingDueInterest,
		OutstandingDuePenalty:   outs.OutstandingDuePenalty,
	}
}

func convertToStatementMetadataEntity(metadata model.StatementMetadata) entity.StatementMetadata {
	return entity.StatementMetadata{
		PenaltySource: metadata.PenaltySource.String(),
	}
}
