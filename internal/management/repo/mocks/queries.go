// Code generated by MockGen. DO NOT EDIT.
// Source: ./da (interfaces: Querier)
//
// Generated by this command:
//
//	mockgen --destination=./mocks/queries.go --package=damocks ./da Querier
//

// Package damocks is a generated GoMock package.
package damocks

import (
	context "context"
	sql "database/sql"
	reflect "reflect"
	time "time"

	da "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	gomock "go.uber.org/mock/gomock"
)

// MockQuerier is a mock of Querier interface.
type MockQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockQuerierMockRecorder
	isgomock struct{}
}

// MockQuerierMockRecorder is the mock recorder for MockQuerier.
type MockQuerierMockRecorder struct {
	mock *MockQuerier
}

// NewMockQuerier creates a new mock instance.
func NewMockQuerier(ctrl *gomock.Controller) *MockQuerier {
	mock := &MockQuerier{ctrl: ctrl}
	mock.recorder = &MockQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerier) EXPECT() *MockQuerierMockRecorder {
	return m.recorder
}

// GetInstallmentByID mocks base method.
func (m *MockQuerier) GetInstallmentByID(ctx context.Context, id int64) (*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByID", ctx, id)
	ret0, _ := ret[0].(*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByID indicates an expected call of GetInstallmentByID.
func (mr *MockQuerierMockRecorder) GetInstallmentByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByID", reflect.TypeOf((*MockQuerier)(nil).GetInstallmentByID), ctx, id)
}

// GetInstallmentByIDForUpdate mocks base method.
func (m *MockQuerier) GetInstallmentByIDForUpdate(ctx context.Context, id int64) (*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByIDForUpdate", ctx, id)
	ret0, _ := ret[0].(*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByIDForUpdate indicates an expected call of GetInstallmentByIDForUpdate.
func (mr *MockQuerierMockRecorder) GetInstallmentByIDForUpdate(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByIDForUpdate", reflect.TypeOf((*MockQuerier)(nil).GetInstallmentByIDForUpdate), ctx, id)
}

// GetInstallmentByZPTransID mocks base method.
func (m *MockQuerier) GetInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByZPTransID", ctx, zpTransID)
	ret0, _ := ret[0].(*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByZPTransID indicates an expected call of GetInstallmentByZPTransID.
func (mr *MockQuerierMockRecorder) GetInstallmentByZPTransID(ctx, zpTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByZPTransID", reflect.TypeOf((*MockQuerier)(nil).GetInstallmentByZPTransID), ctx, zpTransID)
}

// GetInstallmentByZalopayIDAndZPTransID mocks base method.
func (m *MockQuerier) GetInstallmentByZalopayIDAndZPTransID(ctx context.Context, arg *da.GetInstallmentByZalopayIDAndZPTransIDParams) (*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentByZalopayIDAndZPTransID", ctx, arg)
	ret0, _ := ret[0].(*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentByZalopayIDAndZPTransID indicates an expected call of GetInstallmentByZalopayIDAndZPTransID.
func (mr *MockQuerierMockRecorder) GetInstallmentByZalopayIDAndZPTransID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentByZalopayIDAndZPTransID", reflect.TypeOf((*MockQuerier)(nil).GetInstallmentByZalopayIDAndZPTransID), ctx, arg)
}

// GetRepaymentScheduleByInstID mocks base method.
func (m *MockQuerier) GetRepaymentScheduleByInstID(ctx context.Context, instID int64) ([]*da.ScheduledRepayments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRepaymentScheduleByInstID", ctx, instID)
	ret0, _ := ret[0].([]*da.ScheduledRepayments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRepaymentScheduleByInstID indicates an expected call of GetRepaymentScheduleByInstID.
func (mr *MockQuerierMockRecorder) GetRepaymentScheduleByInstID(ctx, instID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRepaymentScheduleByInstID", reflect.TypeOf((*MockQuerier)(nil).GetRepaymentScheduleByInstID), ctx, instID)
}

// GetStatementByID mocks base method.
func (m *MockQuerier) GetStatementByID(ctx context.Context, id int64) (*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatementByID", ctx, id)
	ret0, _ := ret[0].(*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatementByID indicates an expected call of GetStatementByID.
func (mr *MockQuerierMockRecorder) GetStatementByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatementByID", reflect.TypeOf((*MockQuerier)(nil).GetStatementByID), ctx, id)
}

// GetStatementDueDateByIncurredDateAndPartner mocks base method.
func (m *MockQuerier) GetStatementDueDateByIncurredDateAndPartner(ctx context.Context, arg *da.GetStatementDueDateByIncurredDateAndPartnerParams) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatementDueDateByIncurredDateAndPartner", ctx, arg)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatementDueDateByIncurredDateAndPartner indicates an expected call of GetStatementDueDateByIncurredDateAndPartner.
func (mr *MockQuerierMockRecorder) GetStatementDueDateByIncurredDateAndPartner(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatementDueDateByIncurredDateAndPartner", reflect.TypeOf((*MockQuerier)(nil).GetStatementDueDateByIncurredDateAndPartner), ctx, arg)
}

// GetSyncerBatchByPeriodAndPartner mocks base method.
func (m *MockQuerier) GetSyncerBatchByPeriodAndPartner(ctx context.Context, arg *da.GetSyncerBatchByPeriodAndPartnerParams) ([]*da.StatementsSyncer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncerBatchByPeriodAndPartner", ctx, arg)
	ret0, _ := ret[0].([]*da.StatementsSyncer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncerBatchByPeriodAndPartner indicates an expected call of GetSyncerBatchByPeriodAndPartner.
func (mr *MockQuerierMockRecorder) GetSyncerBatchByPeriodAndPartner(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncerBatchByPeriodAndPartner", reflect.TypeOf((*MockQuerier)(nil).GetSyncerBatchByPeriodAndPartner), ctx, arg)
}

// GetUserLatestStatement mocks base method.
func (m *MockQuerier) GetUserLatestStatement(ctx context.Context, arg *da.GetUserLatestStatementParams) (*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLatestStatement", ctx, arg)
	ret0, _ := ret[0].(*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLatestStatement indicates an expected call of GetUserLatestStatement.
func (mr *MockQuerierMockRecorder) GetUserLatestStatement(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLatestStatement", reflect.TypeOf((*MockQuerier)(nil).GetUserLatestStatement), ctx, arg)
}

// GetUserLatestStatementByPeriod mocks base method.
func (m *MockQuerier) GetUserLatestStatementByPeriod(ctx context.Context, arg *da.GetUserLatestStatementByPeriodParams) (*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLatestStatementByPeriod", ctx, arg)
	ret0, _ := ret[0].(*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLatestStatementByPeriod indicates an expected call of GetUserLatestStatementByPeriod.
func (mr *MockQuerierMockRecorder) GetUserLatestStatementByPeriod(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLatestStatementByPeriod", reflect.TypeOf((*MockQuerier)(nil).GetUserLatestStatementByPeriod), ctx, arg)
}

// GetUserStatementByIncurredDate mocks base method.
func (m *MockQuerier) GetUserStatementByIncurredDate(ctx context.Context, arg *da.GetUserStatementByIncurredDateParams) (*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserStatementByIncurredDate", ctx, arg)
	ret0, _ := ret[0].(*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserStatementByIncurredDate indicates an expected call of GetUserStatementByIncurredDate.
func (mr *MockQuerierMockRecorder) GetUserStatementByIncurredDate(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserStatementByIncurredDate", reflect.TypeOf((*MockQuerier)(nil).GetUserStatementByIncurredDate), ctx, arg)
}

// GetUserStatementByPeriodAndIncurredDate mocks base method.
func (m *MockQuerier) GetUserStatementByPeriodAndIncurredDate(ctx context.Context, arg *da.GetUserStatementByPeriodAndIncurredDateParams) (*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserStatementByPeriodAndIncurredDate", ctx, arg)
	ret0, _ := ret[0].(*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserStatementByPeriodAndIncurredDate indicates an expected call of GetUserStatementByPeriodAndIncurredDate.
func (mr *MockQuerierMockRecorder) GetUserStatementByPeriodAndIncurredDate(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserStatementByPeriodAndIncurredDate", reflect.TypeOf((*MockQuerier)(nil).GetUserStatementByPeriodAndIncurredDate), ctx, arg)
}

// InsertInstallment mocks base method.
func (m *MockQuerier) InsertInstallment(ctx context.Context, arg *da.InsertInstallmentParams) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertInstallment", ctx, arg)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertInstallment indicates an expected call of InsertInstallment.
func (mr *MockQuerierMockRecorder) InsertInstallment(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertInstallment", reflect.TypeOf((*MockQuerier)(nil).InsertInstallment), ctx, arg)
}

// InsertRepaymentSchedule mocks base method.
func (m *MockQuerier) InsertRepaymentSchedule(ctx context.Context, arg *da.InsertRepaymentScheduleParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertRepaymentSchedule", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertRepaymentSchedule indicates an expected call of InsertRepaymentSchedule.
func (mr *MockQuerierMockRecorder) InsertRepaymentSchedule(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRepaymentSchedule", reflect.TypeOf((*MockQuerier)(nil).InsertRepaymentSchedule), ctx, arg)
}

// InsertSyncer mocks base method.
func (m *MockQuerier) InsertSyncer(ctx context.Context, arg *da.InsertSyncerParams) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertSyncer", ctx, arg)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertSyncer indicates an expected call of InsertSyncer.
func (mr *MockQuerierMockRecorder) InsertSyncer(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertSyncer", reflect.TypeOf((*MockQuerier)(nil).InsertSyncer), ctx, arg)
}

// ListInstallmentByPartnerRefIDs mocks base method.
func (m *MockQuerier) ListInstallmentByPartnerRefIDs(ctx context.Context, partnerInstIds []sql.NullString) ([]*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstallmentByPartnerRefIDs", ctx, partnerInstIds)
	ret0, _ := ret[0].([]*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstallmentByPartnerRefIDs indicates an expected call of ListInstallmentByPartnerRefIDs.
func (mr *MockQuerierMockRecorder) ListInstallmentByPartnerRefIDs(ctx, partnerInstIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstallmentByPartnerRefIDs", reflect.TypeOf((*MockQuerier)(nil).ListInstallmentByPartnerRefIDs), ctx, partnerInstIds)
}

// ListInstallmentPeriodicSync mocks base method.
func (m *MockQuerier) ListInstallmentPeriodicSync(ctx context.Context, arg *da.ListInstallmentPeriodicSyncParams) ([]*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstallmentPeriodicSync", ctx, arg)
	ret0, _ := ret[0].([]*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstallmentPeriodicSync indicates an expected call of ListInstallmentPeriodicSync.
func (mr *MockQuerierMockRecorder) ListInstallmentPeriodicSync(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstallmentPeriodicSync", reflect.TypeOf((*MockQuerier)(nil).ListInstallmentPeriodicSync), ctx, arg)
}

// ListOpenInstallmentByZalopayIDAndAcctID mocks base method.
func (m *MockQuerier) ListOpenInstallmentByZalopayIDAndAcctID(ctx context.Context, arg *da.ListOpenInstallmentByZalopayIDAndAcctIDParams) ([]*da.Installments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOpenInstallmentByZalopayIDAndAcctID", ctx, arg)
	ret0, _ := ret[0].([]*da.Installments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOpenInstallmentByZalopayIDAndAcctID indicates an expected call of ListOpenInstallmentByZalopayIDAndAcctID.
func (mr *MockQuerierMockRecorder) ListOpenInstallmentByZalopayIDAndAcctID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOpenInstallmentByZalopayIDAndAcctID", reflect.TypeOf((*MockQuerier)(nil).ListOpenInstallmentByZalopayIDAndAcctID), ctx, arg)
}

// ListOutstandingStatementByIncurredDateAndPartner mocks base method.
func (m *MockQuerier) ListOutstandingStatementByIncurredDateAndPartner(ctx context.Context, arg *da.ListOutstandingStatementByIncurredDateAndPartnerParams) ([]*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOutstandingStatementByIncurredDateAndPartner", ctx, arg)
	ret0, _ := ret[0].([]*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOutstandingStatementByIncurredDateAndPartner indicates an expected call of ListOutstandingStatementByIncurredDateAndPartner.
func (mr *MockQuerierMockRecorder) ListOutstandingStatementByIncurredDateAndPartner(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOutstandingStatementByIncurredDateAndPartner", reflect.TypeOf((*MockQuerier)(nil).ListOutstandingStatementByIncurredDateAndPartner), ctx, arg)
}

// ListRepaymentScheduleByInstIDs mocks base method.
func (m *MockQuerier) ListRepaymentScheduleByInstIDs(ctx context.Context, instIds []int64) ([]*da.ScheduledRepayments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRepaymentScheduleByInstIDs", ctx, instIds)
	ret0, _ := ret[0].([]*da.ScheduledRepayments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRepaymentScheduleByInstIDs indicates an expected call of ListRepaymentScheduleByInstIDs.
func (mr *MockQuerierMockRecorder) ListRepaymentScheduleByInstIDs(ctx, instIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRepaymentScheduleByInstIDs", reflect.TypeOf((*MockQuerier)(nil).ListRepaymentScheduleByInstIDs), ctx, instIds)
}

// ListStatementByIncurredDateAndPartner mocks base method.
func (m *MockQuerier) ListStatementByIncurredDateAndPartner(ctx context.Context, arg *da.ListStatementByIncurredDateAndPartnerParams) ([]*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStatementByIncurredDateAndPartner", ctx, arg)
	ret0, _ := ret[0].([]*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStatementByIncurredDateAndPartner indicates an expected call of ListStatementByIncurredDateAndPartner.
func (mr *MockQuerierMockRecorder) ListStatementByIncurredDateAndPartner(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStatementByIncurredDateAndPartner", reflect.TypeOf((*MockQuerier)(nil).ListStatementByIncurredDateAndPartner), ctx, arg)
}

// ListStmtInstallmentByClientAndStmtDate mocks base method.
func (m *MockQuerier) ListStmtInstallmentByClientAndStmtDate(ctx context.Context, arg *da.ListStmtInstallmentByClientAndStmtDateParams) ([]*da.StatementsInstallments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStmtInstallmentByClientAndStmtDate", ctx, arg)
	ret0, _ := ret[0].([]*da.StatementsInstallments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStmtInstallmentByClientAndStmtDate indicates an expected call of ListStmtInstallmentByClientAndStmtDate.
func (mr *MockQuerierMockRecorder) ListStmtInstallmentByClientAndStmtDate(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStmtInstallmentByClientAndStmtDate", reflect.TypeOf((*MockQuerier)(nil).ListStmtInstallmentByClientAndStmtDate), ctx, arg)
}

// ListStmtInstallmentByStatementId mocks base method.
func (m *MockQuerier) ListStmtInstallmentByStatementId(ctx context.Context, statementID int64) ([]*da.StatementsInstallments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStmtInstallmentByStatementId", ctx, statementID)
	ret0, _ := ret[0].([]*da.StatementsInstallments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStmtInstallmentByStatementId indicates an expected call of ListStmtInstallmentByStatementId.
func (mr *MockQuerierMockRecorder) ListStmtInstallmentByStatementId(ctx, statementID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStmtInstallmentByStatementId", reflect.TypeOf((*MockQuerier)(nil).ListStmtInstallmentByStatementId), ctx, statementID)
}

// ListStmtInstallmentByStmtId mocks base method.
func (m *MockQuerier) ListStmtInstallmentByStmtId(ctx context.Context, statementID int64) ([]*da.StatementsInstallments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStmtInstallmentByStmtId", ctx, statementID)
	ret0, _ := ret[0].([]*da.StatementsInstallments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStmtInstallmentByStmtId indicates an expected call of ListStmtInstallmentByStmtId.
func (mr *MockQuerierMockRecorder) ListStmtInstallmentByStmtId(ctx, statementID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStmtInstallmentByStmtId", reflect.TypeOf((*MockQuerier)(nil).ListStmtInstallmentByStmtId), ctx, statementID)
}

// ListStmtInstallmentByUserAndStmtId mocks base method.
func (m *MockQuerier) ListStmtInstallmentByUserAndStmtId(ctx context.Context, arg *da.ListStmtInstallmentByUserAndStmtIdParams) ([]*da.StatementsInstallments, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStmtInstallmentByUserAndStmtId", ctx, arg)
	ret0, _ := ret[0].([]*da.StatementsInstallments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStmtInstallmentByUserAndStmtId indicates an expected call of ListStmtInstallmentByUserAndStmtId.
func (mr *MockQuerierMockRecorder) ListStmtInstallmentByUserAndStmtId(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStmtInstallmentByUserAndStmtId", reflect.TypeOf((*MockQuerier)(nil).ListStmtInstallmentByUserAndStmtId), ctx, arg)
}

// ListUserStatementByIncurredDateRange mocks base method.
func (m *MockQuerier) ListUserStatementByIncurredDateRange(ctx context.Context, arg *da.ListUserStatementByIncurredDateRangeParams) ([]*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserStatementByIncurredDateRange", ctx, arg)
	ret0, _ := ret[0].([]*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserStatementByIncurredDateRange indicates an expected call of ListUserStatementByIncurredDateRange.
func (mr *MockQuerierMockRecorder) ListUserStatementByIncurredDateRange(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserStatementByIncurredDateRange", reflect.TypeOf((*MockQuerier)(nil).ListUserStatementByIncurredDateRange), ctx, arg)
}

// ListUserStatementByPeriod mocks base method.
func (m *MockQuerier) ListUserStatementByPeriod(ctx context.Context, arg *da.ListUserStatementByPeriodParams) ([]*da.Statements, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserStatementByPeriod", ctx, arg)
	ret0, _ := ret[0].([]*da.Statements)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserStatementByPeriod indicates an expected call of ListUserStatementByPeriod.
func (mr *MockQuerierMockRecorder) ListUserStatementByPeriod(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserStatementByPeriod", reflect.TypeOf((*MockQuerier)(nil).ListUserStatementByPeriod), ctx, arg)
}

// UpdateDischargeStatus mocks base method.
func (m *MockQuerier) UpdateDischargeStatus(ctx context.Context, arg *da.UpdateDischargeStatusParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDischargeStatus", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDischargeStatus indicates an expected call of UpdateDischargeStatus.
func (mr *MockQuerierMockRecorder) UpdateDischargeStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDischargeStatus", reflect.TypeOf((*MockQuerier)(nil).UpdateDischargeStatus), ctx, arg)
}

// UpdateEarlyDischargeInfo mocks base method.
func (m *MockQuerier) UpdateEarlyDischargeInfo(ctx context.Context, arg *da.UpdateEarlyDischargeInfoParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEarlyDischargeInfo", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEarlyDischargeInfo indicates an expected call of UpdateEarlyDischargeInfo.
func (mr *MockQuerierMockRecorder) UpdateEarlyDischargeInfo(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEarlyDischargeInfo", reflect.TypeOf((*MockQuerier)(nil).UpdateEarlyDischargeInfo), ctx, arg)
}

// UpdateInstallment mocks base method.
func (m *MockQuerier) UpdateInstallment(ctx context.Context, arg *da.UpdateInstallmentParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallment", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallment indicates an expected call of UpdateInstallment.
func (mr *MockQuerierMockRecorder) UpdateInstallment(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallment", reflect.TypeOf((*MockQuerier)(nil).UpdateInstallment), ctx, arg)
}

// UpdateInstallmentAfterSync mocks base method.
func (m *MockQuerier) UpdateInstallmentAfterSync(ctx context.Context, arg *da.UpdateInstallmentAfterSyncParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallmentAfterSync", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallmentAfterSync indicates an expected call of UpdateInstallmentAfterSync.
func (mr *MockQuerierMockRecorder) UpdateInstallmentAfterSync(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallmentAfterSync", reflect.TypeOf((*MockQuerier)(nil).UpdateInstallmentAfterSync), ctx, arg)
}

// UpdateInstallmentRefund mocks base method.
func (m *MockQuerier) UpdateInstallmentRefund(ctx context.Context, arg *da.UpdateInstallmentRefundParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallmentRefund", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallmentRefund indicates an expected call of UpdateInstallmentRefund.
func (mr *MockQuerierMockRecorder) UpdateInstallmentRefund(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallmentRefund", reflect.TypeOf((*MockQuerier)(nil).UpdateInstallmentRefund), ctx, arg)
}

// UpdateInstallmentTransaction mocks base method.
func (m *MockQuerier) UpdateInstallmentTransaction(ctx context.Context, arg *da.UpdateInstallmentTransactionParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstallmentTransaction", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstallmentTransaction indicates an expected call of UpdateInstallmentTransaction.
func (mr *MockQuerierMockRecorder) UpdateInstallmentTransaction(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstallmentTransaction", reflect.TypeOf((*MockQuerier)(nil).UpdateInstallmentTransaction), ctx, arg)
}

// UpdateRepaymentScheduleAfterSync mocks base method.
func (m *MockQuerier) UpdateRepaymentScheduleAfterSync(ctx context.Context, arg *da.UpdateRepaymentScheduleAfterSyncParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRepaymentScheduleAfterSync", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRepaymentScheduleAfterSync indicates an expected call of UpdateRepaymentScheduleAfterSync.
func (mr *MockQuerierMockRecorder) UpdateRepaymentScheduleAfterSync(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRepaymentScheduleAfterSync", reflect.TypeOf((*MockQuerier)(nil).UpdateRepaymentScheduleAfterSync), ctx, arg)
}

// UpdateStatementOutstandingAndPenaltyByID mocks base method.
func (m *MockQuerier) UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, arg *da.UpdateStatementOutstandingAndPenaltyByIDParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatementOutstandingAndPenaltyByID", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatementOutstandingAndPenaltyByID indicates an expected call of UpdateStatementOutstandingAndPenaltyByID.
func (mr *MockQuerierMockRecorder) UpdateStatementOutstandingAndPenaltyByID(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatementOutstandingAndPenaltyByID", reflect.TypeOf((*MockQuerier)(nil).UpdateStatementOutstandingAndPenaltyByID), ctx, arg)
}

// UpdateStatementOutstandingByIncurredDate mocks base method.
func (m *MockQuerier) UpdateStatementOutstandingByIncurredDate(ctx context.Context, arg *da.UpdateStatementOutstandingByIncurredDateParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatementOutstandingByIncurredDate", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatementOutstandingByIncurredDate indicates an expected call of UpdateStatementOutstandingByIncurredDate.
func (mr *MockQuerierMockRecorder) UpdateStatementOutstandingByIncurredDate(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatementOutstandingByIncurredDate", reflect.TypeOf((*MockQuerier)(nil).UpdateStatementOutstandingByIncurredDate), ctx, arg)
}

// UpdateSyncerStatus mocks base method.
func (m *MockQuerier) UpdateSyncerStatus(ctx context.Context, arg *da.UpdateSyncerStatusParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSyncerStatus", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSyncerStatus indicates an expected call of UpdateSyncerStatus.
func (mr *MockQuerierMockRecorder) UpdateSyncerStatus(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSyncerStatus", reflect.TypeOf((*MockQuerier)(nil).UpdateSyncerStatus), ctx, arg)
}

// UpdateSyncerStatusAndStats mocks base method.
func (m *MockQuerier) UpdateSyncerStatusAndStats(ctx context.Context, arg *da.UpdateSyncerStatusAndStatsParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSyncerStatusAndStats", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSyncerStatusAndStats indicates an expected call of UpdateSyncerStatusAndStats.
func (mr *MockQuerierMockRecorder) UpdateSyncerStatusAndStats(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSyncerStatusAndStats", reflect.TypeOf((*MockQuerier)(nil).UpdateSyncerStatusAndStats), ctx, arg)
}

// UpsertStatement mocks base method.
func (m *MockQuerier) UpsertStatement(ctx context.Context, arg *da.UpsertStatementParams) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertStatement", ctx, arg)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertStatement indicates an expected call of UpsertStatement.
func (mr *MockQuerierMockRecorder) UpsertStatement(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertStatement", reflect.TypeOf((*MockQuerier)(nil).UpsertStatement), ctx, arg)
}

// UpsertStmtInstallment mocks base method.
func (m *MockQuerier) UpsertStmtInstallment(ctx context.Context, arg *da.UpsertStmtInstallmentParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertStmtInstallment", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertStmtInstallment indicates an expected call of UpsertStmtInstallment.
func (mr *MockQuerierMockRecorder) UpsertStmtInstallment(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertStmtInstallment", reflect.TypeOf((*MockQuerier)(nil).UpsertStmtInstallment), ctx, arg)
}

// UpsertSyncer mocks base method.
func (m *MockQuerier) UpsertSyncer(ctx context.Context, arg *da.UpsertSyncerParams) (sql.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertSyncer", ctx, arg)
	ret0, _ := ret[0].(sql.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertSyncer indicates an expected call of UpsertSyncer.
func (mr *MockQuerierMockRecorder) UpsertSyncer(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSyncer", reflect.TypeOf((*MockQuerier)(nil).UpsertSyncer), ctx, arg)
}
