package repo

import (
	"context"
	"database/sql"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type statementSyncerRepo struct {
	repos  *Repository
	logger *log.Helper
}

func NewStatementSyncRepo(repos *Repository, kLogger log.Logger) _interface.StatementSyncerRepo {
	logger := log.With(kLogger, "repo", "statement_syncer")
	return &statementSyncerRepo{
		repos:  repos,
		logger: log.NewHelper(logger),
	}
}

func (s statementSyncerRepo) CreateSyncInfo(ctx context.Context, syncInfo *model.StatementSyncInfo) (int64, error) {
	defer s.repos.metrics.MonitoredTime(statementSyncRepoName)("CreateSyncInfo")
	logger := s.logger.WithContext(ctx)

	result, err := s.repos.Queries(ctx).UpsertSyncer(ctx, &da.UpsertSyncerParams{
		ExecID:          syncInfo.ExecID,
		BatchNo:         buildBatchNo(syncInfo.BatchNo),
		Status:          convertToSyncStatus(syncInfo.Status),
		PartnerCode:     syncInfo.PartnerCode.String(),
		SyncStats:       convertToSyncStats(syncInfo.SyncStats),
		StatementPeriod: syncInfo.Period,
		StatementDate:   zutils.NewNullTime(syncInfo.StatementDate),
	})
	if err != nil {
		logger.Errorf("failed to create sync info into db, err=%v", err)
		return -1, errors.Wrap(err, "failed to create sync info into db")
	}
	lastID, err := result.LastInsertId()
	if err != nil {
		logger.Errorf("failed to get last insert id of sync info, err=%v", err)
		return -1, errors.Wrap(err, "failed to get last insert id of sync info")
	}
	return lastID, nil
}

func (s statementSyncerRepo) UpdateSyncStatus(ctx context.Context, syncInfo *model.StatementSyncInfo) error {
	defer s.repos.metrics.MonitoredTime(statementSyncRepoName)("UpdateSyncStatus")
	logger := s.logger.WithContext(ctx)

	if syncInfo.ID == 0 && syncInfo.ExecID == "" {
		logger.Errorf("sync info ID is required, %+v", syncInfo)
		return errors.Errorf("sync info ID is required, %+v", syncInfo)
	}
	err := s.repos.Queries(ctx).UpdateSyncerStatus(ctx, &da.UpdateSyncerStatusParams{
		ID:     syncInfo.ID,
		ExecID: syncInfo.ExecID,
		Status: convertToSyncStatus(syncInfo.Status),
	})
	if err != nil {
		logger.Errorf("failed to update sync status into db, err=%v", err)
		return errors.Wrap(err, "failed to update sync status and stats into db")
	}
	return nil
}

func (s statementSyncerRepo) UpdateSyncStatusAndStats(ctx context.Context, syncInfo *model.StatementSyncInfo) error {
	defer s.repos.metrics.MonitoredTime(statementSyncRepoName)("UpdateSyncStatusAndStats")
	logger := s.logger.WithContext(ctx)

	if syncInfo.ID == 0 && syncInfo.ExecID == "" {
		logger.Errorf("sync info ID is required, %+v", syncInfo)
		return errors.New("sync info ID is required")
	}
	err := s.repos.Queries(ctx).UpdateSyncerStatusAndStats(ctx, &da.UpdateSyncerStatusAndStatsParams{
		ID:        syncInfo.ID,
		ExecID:    syncInfo.ExecID,
		Status:    convertToSyncStatus(syncInfo.Status),
		SyncStats: convertToSyncStats(syncInfo.SyncStats),
	})
	if err != nil {
		logger.Errorf("failed to update sync status and stats into db, err=%v", err)
		return errors.Wrap(err, "failed to update sync status and stats into db")
	}
	return nil
}

func (s statementSyncerRepo) GetSyncerBatchByPeriodAndPartner(
	ctx context.Context, stmtPeriod *model.StatementPeriod,
	partnerCode partner.PartnerCode) ([]*model.StatementSyncInfo, error) {
	defer s.repos.metrics.MonitoredTime(statementSyncRepoName)("GetSyncerBatchByPeriodAndPartner")
	logger := s.logger.WithContext(ctx)

	items, err := s.repos.Queries(ctx).GetSyncerBatchByPeriodAndPartner(ctx, &da.GetSyncerBatchByPeriodAndPartnerParams{
		PartnerCode:     partnerCode.String(),
		StatementPeriod: stmtPeriod.Period,
		StatementDate:   zutils.NewNullTime(stmtPeriod.IncurredDate),
	})
	if errors.Is(err, sql.ErrNoRows) {
		logger.Warnf("no syncer batch found by period and partner, partner=%s, period=%+v", partnerCode, stmtPeriod.Period)
		return []*model.StatementSyncInfo{}, nil
	}
	if err != nil {
		logger.Errorf("failed to get syncer batch by period and partner, err=%v", err)
		return nil, errors.Wrap(err, "failed to get syncer batch by period and partner")
	}

	var syncInfos []*model.StatementSyncInfo
	for _, item := range items {
		syncStatus := convertFromSyncStatus(item.Status)
		syncStats := &model.StatementSyncStats{}
		err = syncStats.Unmarshal(item.SyncStats)

		syncInfo := &model.StatementSyncInfo{
			ID:            item.ID,
			ExecID:        item.ExecID,
			BatchNo:       cast.ToInt(item.BatchNo.Int32),
			Period:        item.StatementPeriod,
			Status:        syncStatus,
			SyncStats:     *syncStats,
			PartnerCode:   partnerCode,
			StatementDate: item.StatementDate.Time,
		}
		syncInfos = append(syncInfos, syncInfo)
	}
	return syncInfos, nil
}

func buildBatchNo(batchNo int) sql.NullInt32 {
	if batchNo == 0 {
		return sql.NullInt32{}
	}
	return sql.NullInt32{
		Int32: int32(batchNo),
		Valid: true,
	}
}

func convertToSyncStats(stats model.StatementSyncStats) json.RawMessage {
	data, _ := stats.Marshal()
	return data
}

func convertToSyncStatus(status model.StatementSyncStatus) da.StatementsSyncerStatus {
	mapping := map[model.StatementSyncStatus]da.StatementsSyncerStatus{
		model.StatementSyncStatusPending: da.StatementsSyncerStatusPENDING,
		model.StatementSyncStatusRunning: da.StatementsSyncerStatusRUNNING,
		model.StatementSyncStatusSuccess: da.StatementsSyncerStatusSUCCESS,
		model.StatementSyncStatusFailed:  da.StatementsSyncerStatusFAILED,
	}
	return mapping[status]
}

func convertFromSyncStatus(status da.StatementsSyncerStatus) model.StatementSyncStatus {
	mapping := map[da.StatementsSyncerStatus]model.StatementSyncStatus{
		da.StatementsSyncerStatusPENDING: model.StatementSyncStatusPending,
		da.StatementsSyncerStatusRUNNING: model.StatementSyncStatusRunning,
		da.StatementsSyncerStatusSUCCESS: model.StatementSyncStatusSuccess,
		da.StatementsSyncerStatusFAILED:  model.StatementSyncStatusFailed,
	}
	return mapping[status]
}
