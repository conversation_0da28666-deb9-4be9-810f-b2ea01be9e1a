package repo

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
)

type feeRepo struct {
	repos   *Repository
	logger  *log.Helper
	feeRes  entity.FeeResources
	feePath string
}

func NewFeeRepo(conf *config.Management, repos *Repository, kLogger log.Logger) _interface.FeeRepo {
	feePath := conf.GetResources().GetFeeFilePath()
	feeRes := LoadResources(feePath, entity.FeeResources{})
	logger := log.NewHelper(log.With(kLogger, "repo", "fees"))
	return &feeRepo{
		repos:   repos,
		logger:  logger,
		feeRes:  feeRes,
		feePath: feePath,
	}
}

func (f *feeRepo) initFeeResources() {
	f.feeRes = LoadResources(f.feePath, entity.FeeResources{})
}

func (f *feeRepo) RetrieveFeesInfo(_ context.Context) []*model.FeeConfig {
	defer f.repos.metrics.MonitoredTime(feeRepoName)("RetrieveFeesInfo")

	if f.feeRes.IsEmpty() {
		f.initFeeResources()
	}

	feeRes := f.feeRes
	feeList := make([]*model.FeeConfig, 0, len(feeRes))

	for feeType, feeInfo := range feeRes {
		if feeInfo.Status != entity.FeeActive {
			continue
		}
		feeList = append(feeList, &model.FeeConfig{
			Rate:   feeInfo.Rate,
			Amount: feeInfo.Amount,
			Type:   f.toFeeTypeModel(feeType),
		})
	}

	return feeList
}

func (f *feeRepo) EvaluateFeesInfo(ctx context.Context, orderAmount int64) []*model.FeeDetail {
	defer f.repos.metrics.MonitoredTime(feeRepoName)("EvaluateFeesInfo")

	feeConfig := f.RetrieveFeesInfo(ctx)
	feeList := make([]*model.FeeDetail, 0, len(feeConfig))

	for _, fee := range feeConfig {
		feeAmount := float64(0)
		if fee.Rate != 0 {
			feeAmount = fee.Rate * float64(orderAmount)
		}
		if fee.Amount != 0 {
			feeAmount += float64(fee.Amount)
		}
		feeList = append(feeList, &model.FeeDetail{
			Type:   fee.Type,
			Amount: cast.ToInt64(feeAmount),
		})
	}

	return feeList
}

func (f *feeRepo) toFeeTypeExplain(feeType model.FeeType, rate float64, amount int64) string {
	switch feeType {
	case model.PlatformFee:
		return fmt.Sprintf(model.PlatFeeExplain, amount)
	case model.ConversionFee:
		return fmt.Sprintf(model.ConvFeeExplain, rate*100)
	default:
		return ""
	}
}

func (f *feeRepo) toFeeTypeModel(feeType entity.FeeType) model.FeeType {
	switch feeType {
	case entity.PlatformFee:
		return model.PlatformFee
	case entity.ConversionFee:
		return model.ConversionFee
	default:
		return model.UnknownFee
	}
}
