package repo

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/da"
	damocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/mocks"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
)

// InstallmentRepoTestSuite is a test suite for the installment repository
type InstallmentRepoTestSuite struct {
	suite.Suite
	mockCtrl    *gomock.Controller
	mockQuerier *damocks.MockQuerier
	repo        *installmentRepo
	ctx         context.Context
}

// SetupTest sets up the test suite
func (s *InstallmentRepoTestSuite) SetupTest() {
	s.mockCtrl = gomock.NewController(s.T())
	s.mockQuerier = damocks.NewMockQuerier(s.mockCtrl)
	s.ctx = context.Background()

	db, _, _ := sqlmock.New()
	defer db.Close()

	mockRepo := &Repository{
		sqlDB:       db,
		mockQuerier: s.mockQuerier,
		metrics:     metrics.NewRepositoryMetrics(metrics.Options{Module: metrics.MetricModuleRepository}),
	}

	s.repo = &installmentRepo{
		repos:  mockRepo,
		logger: log.NewHelper(log.DefaultLogger),
	}
}

// TearDownTest tears down the test suite
func (s *InstallmentRepoTestSuite) TearDownTest() {
	s.mockCtrl.Finish()
}

// TestInstallmentRepoTestSuite runs the test suite
func TestInstallmentRepoTestSuite(t *testing.T) {
	suite.Run(t, new(InstallmentRepoTestSuite))
}

// TestUpdateInstallmentAfterSync tests the UpdateInstallmentAfterSync method
func (s *InstallmentRepoTestSuite) TestUpdateInstallmentAfterSync() {
	// Create test cases
	testCases := []struct {
		name          string
		installment   *model.InstallmentInfo
		expectedError bool
		setupMock     func(*model.InstallmentInfo)
	}{
		{
			name: "Success",
			installment: &model.InstallmentInfo{
				ID:              123,
				Status:          model.InstallmentStatusOpen,
				DaysPastDue:     2,
				PaidTenure:      3,
				StartDate:       time.Now(),
				EndDate:         time.Now().AddDate(0, 3, 0),
				CurrDueDate:     time.Now().AddDate(0, 1, 0),
				NextDueDate:     time.Now().AddDate(0, 2, 0),
				OutstandingInfo: &model.InstallmentOutstanding{},
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: false,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "Error from UpdateInstallmentAfterSync",
			installment: &model.InstallmentInfo{
				ID:              123,
				Status:          model.InstallmentStatusOpen,
				DaysPastDue:     2,
				PaidTenure:      3,
				StartDate:       time.Now(),
				EndDate:         time.Now().AddDate(0, 3, 0),
				CurrDueDate:     time.Now().AddDate(0, 1, 0),
				NextDueDate:     time.Now().AddDate(0, 2, 0),
				OutstandingInfo: &model.InstallmentOutstanding{},
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: true,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
					Return(errors.New("database error"))
			},
		},
		{
			name: "Nil OutstandingInfo",
			installment: &model.InstallmentInfo{
				ID:              123,
				Status:          model.InstallmentStatusOpen,
				DaysPastDue:     2,
				PaidTenure:      3,
				StartDate:       time.Now(),
				EndDate:         time.Now().AddDate(0, 3, 0),
				CurrDueDate:     time.Now().AddDate(0, 1, 0),
				NextDueDate:     time.Now().AddDate(0, 2, 0),
				OutstandingInfo: nil,
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: false,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "Nil EarlyDischarge",
			installment: &model.InstallmentInfo{
				ID:              123,
				Status:          model.InstallmentStatusOpen,
				DaysPastDue:     2,
				PaidTenure:      3,
				StartDate:       time.Now(),
				EndDate:         time.Now().AddDate(0, 3, 0),
				CurrDueDate:     time.Now().AddDate(0, 1, 0),
				NextDueDate:     time.Now().AddDate(0, 2, 0),
				OutstandingInfo: &model.InstallmentOutstanding{},
				EarlyDischarge:  nil,
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: false,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "Unknown DischargeStatus",
			installment: &model.InstallmentInfo{
				ID:              123,
				Status:          model.InstallmentStatusOpen,
				DaysPastDue:     2,
				PaidTenure:      3,
				StartDate:       time.Now(),
				EndDate:         time.Now().AddDate(0, 3, 0),
				CurrDueDate:     time.Now().AddDate(0, 1, 0),
				NextDueDate:     time.Now().AddDate(0, 2, 0),
				OutstandingInfo: &model.InstallmentOutstanding{},
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: "invalid_status",
			},
			expectedError: true,
			setupMock:     func(inst *model.InstallmentInfo) {},
		},
	}

	// Run test cases
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Setup mock
			tc.setupMock(tc.installment)

			// Call function
			err := s.repo.UpdateInstallmentAfterSync(s.ctx, tc.installment)

			// Assert result
			if tc.expectedError {
				s.Error(err)
			} else {
				s.NoError(err)
			}
		})
	}
}

// TestUpdateEarlyDischargeInfo tests the UpdateEarlyDischargeInfo method
func (s *InstallmentRepoTestSuite) TestUpdateEarlyDischargeInfo() {
	testCases := []struct {
		name          string
		installment   *model.InstallmentInfo
		expectedError bool
		setupMock     func(*model.InstallmentInfo)
	}{
		{
			name: "Success",
			installment: &model.InstallmentInfo{
				ID:              123,
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: false,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateEarlyDischargeInfo(gomock.Any(), gomock.Any()).
					Return(nil)
			},
		},
		{
			name: "Invalid DischargeStatus",
			installment: &model.InstallmentInfo{
				ID:              123,
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: "invalid_status",
			},
			expectedError: true,
			setupMock:     func(inst *model.InstallmentInfo) {},
		},
		{
			name: "Error from UpdateEarlyDischargeInfo",
			installment: &model.InstallmentInfo{
				ID:              123,
				EarlyDischarge:  &model.InstallmentEarlyDischarge{},
				DischargeStatus: model.DischargeStatusPending,
			},
			expectedError: true,
			setupMock: func(inst *model.InstallmentInfo) {
				s.mockQuerier.EXPECT().
					UpdateEarlyDischargeInfo(gomock.Any(), gomock.Any()).
					Return(errors.New("database error"))
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			tc.setupMock(tc.installment)

			err := s.repo.UpdateEarlyDischargeInfo(s.ctx, tc.installment)

			if tc.expectedError {
				s.Error(err)
			} else {
				s.NoError(err)
			}
		})
	}
}

// TestGetInstallmentByID tests the GetInstallmentByID method
func (s *InstallmentRepoTestSuite) TestGetInstallmentByID() {
	mockInstallment := &da.Installments{
		ID:            123,
		Tenure:        12,
		Status:        "open",
		ZalopayID:     456,
		PartnerCode:   "partner_code",
		PartnerInstID: sql.NullString{String: "partner_inst_id", Valid: true},
	}

	testCases := []struct {
		name          string
		instID        int64
		mockResult    *da.Installments
		mockError     error
		expectedError bool
	}{
		{
			name:          "Success",
			instID:        123,
			mockResult:    mockInstallment,
			mockError:     nil,
			expectedError: false,
		},
		{
			name:          "Installment Not Found",
			instID:        123,
			mockResult:    nil,
			mockError:     sql.ErrNoRows,
			expectedError: true,
		},
		{
			name:          "Database Error",
			instID:        123,
			mockResult:    nil,
			mockError:     errors.New("database error"),
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			s.mockQuerier.EXPECT().
				GetInstallmentByID(gomock.Any(), tc.instID).
				Return(tc.mockResult, tc.mockError)

			result, err := s.repo.GetInstallmentByID(s.ctx, tc.instID)

			if tc.expectedError {
				s.Error(err)
				if tc.mockError == sql.ErrNoRows {
					s.True(errors.Is(err, model.ErrInstallmentNotFound))
				}
			} else {
				s.NoError(err)
				s.NotNil(result)
				s.Equal(tc.mockResult.ID, result.ID)
			}
		})
	}
}

// TestGetInstallmentByZPTransID tests the GetInstallmentByZPTransID method
func (s *InstallmentRepoTestSuite) TestGetInstallmentByZPTransID() {
	mockInstallment := &da.Installments{
		ID:            123,
		Tenure:        12,
		Status:        "open",
		ZalopayID:     456,
		PartnerCode:   "partner_code",
		PartnerInstID: sql.NullString{String: "partner_inst_id", Valid: true},
	}

	testCases := []struct {
		name          string
		zpTransID     int64
		mockResult    *da.Installments
		mockError     error
		expectedError bool
	}{
		{
			name:          "Success",
			zpTransID:     456,
			mockResult:    mockInstallment,
			mockError:     nil,
			expectedError: false,
		},
		{
			name:          "Installment Not Found",
			zpTransID:     456,
			mockResult:    nil,
			mockError:     sql.ErrNoRows,
			expectedError: true,
		},
		{
			name:          "Database Error",
			zpTransID:     456,
			mockResult:    nil,
			mockError:     errors.New("database error"),
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			s.mockQuerier.EXPECT().
				GetInstallmentByZPTransID(gomock.Any(), tc.zpTransID).
				Return(tc.mockResult, tc.mockError)

			result, err := s.repo.GetInstallmentByZPTransID(s.ctx, tc.zpTransID)

			if tc.expectedError {
				s.Error(err)
				if tc.mockError == sql.ErrNoRows {
					s.True(errors.Is(err, model.ErrInstallmentNotFound))
				}
			} else {
				s.NoError(err)
				s.NotNil(result)
				s.Equal(tc.mockResult.ID, result.ID)
			}
		})
	}
}

// TestGetFullInstallmentByZPTransID tests the GetFullInstallmentByZPTransID method
func (s *InstallmentRepoTestSuite) TestGetFullInstallmentByZPTransID() {
	mockInstallment := &da.Installments{
		ID:            123,
		Tenure:        12,
		Status:        "open",
		ZalopayID:     456,
		PartnerCode:   "partner_code",
		PartnerInstID: sql.NullString{String: "partner_inst_id", Valid: true},
	}

	mockScheduledRepayments := []*da.ScheduledRepayments{
		{
			ID:     1,
			InstID: 123,
			SeqNo:  1,
			Status: "paid",
		},
		{
			ID:     2,
			InstID: 123,
			SeqNo:  2,
			Status: "pending",
		},
	}

	testCases := []struct {
		name                   string
		zpTransID              int64
		mockInstallment        *da.Installments
		mockInstallmentError   error
		mockSchedules          []*da.ScheduledRepayments
		mockSchedulesError     error
		expectedError          bool
		expectedInstallmentErr bool
		expectedSchedulesErr   bool
	}{
		{
			name:                   "Success",
			zpTransID:              456,
			mockInstallment:        mockInstallment,
			mockInstallmentError:   nil,
			mockSchedules:          mockScheduledRepayments,
			mockSchedulesError:     nil,
			expectedError:          false,
			expectedInstallmentErr: false,
			expectedSchedulesErr:   false,
		},
		{
			name:                   "Installment Not Found",
			zpTransID:              456,
			mockInstallment:        nil,
			mockInstallmentError:   sql.ErrNoRows,
			mockSchedules:          nil,
			mockSchedulesError:     nil,
			expectedError:          true,
			expectedInstallmentErr: true,
			expectedSchedulesErr:   false,
		},
		{
			name:                   "Installment Database Error",
			zpTransID:              456,
			mockInstallment:        nil,
			mockInstallmentError:   errors.New("database error"),
			mockSchedules:          nil,
			mockSchedulesError:     nil,
			expectedError:          true,
			expectedInstallmentErr: true,
			expectedSchedulesErr:   false,
		},
		{
			name:                   "Schedules Not Found",
			zpTransID:              456,
			mockInstallment:        mockInstallment,
			mockInstallmentError:   nil,
			mockSchedules:          nil,
			mockSchedulesError:     sql.ErrNoRows,
			expectedError:          true,
			expectedInstallmentErr: false,
			expectedSchedulesErr:   true,
		},
		{
			name:                   "Schedules Database Error",
			zpTransID:              456,
			mockInstallment:        mockInstallment,
			mockInstallmentError:   nil,
			mockSchedules:          nil,
			mockSchedulesError:     errors.New("database error"),
			expectedError:          true,
			expectedInstallmentErr: false,
			expectedSchedulesErr:   true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Setup mocks
			s.mockQuerier.EXPECT().
				GetInstallmentByZPTransID(gomock.Any(), tc.zpTransID).
				Return(tc.mockInstallment, tc.mockInstallmentError)

			if !tc.expectedInstallmentErr {
				s.mockQuerier.EXPECT().
					GetRepaymentScheduleByInstID(gomock.Any(), tc.mockInstallment.ID).
					Return(tc.mockSchedules, tc.mockSchedulesError)
			}

			// Call function
			result, err := s.repo.GetFullInstallmentByZPTransID(s.ctx, tc.zpTransID)

			// Assert results
			if tc.expectedError {
				s.Error(err)
				if tc.expectedInstallmentErr && tc.mockInstallmentError == sql.ErrNoRows {
					s.True(errors.Is(err, model.ErrInstallmentNotFound))
				}
				if tc.expectedSchedulesErr && tc.mockSchedulesError == sql.ErrNoRows {
					s.True(errors.Is(err, model.ErrInstallmentRepaysNotFound))
				}
			} else {
				s.NoError(err)
				s.NotNil(result)
				s.Equal(tc.mockInstallment.ID, result.Info.ID)
				s.Equal(len(tc.mockSchedules), len(result.Repays))
			}
		})
	}
}
