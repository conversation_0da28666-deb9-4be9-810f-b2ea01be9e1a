// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
)

type InstallmentsStatus string

const (
	InstallmentsStatusInit   InstallmentsStatus = "init"
	InstallmentsStatusOpen   InstallmentsStatus = "open"
	InstallmentsStatusClosed InstallmentsStatus = "closed"
)

func (e *InstallmentsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = InstallmentsStatus(s)
	case string:
		*e = InstallmentsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for InstallmentsStatus: %T", src)
	}
	return nil
}

type NullInstallmentsStatus struct {
	InstallmentsStatus InstallmentsStatus `json:"installments_status"`
	Valid              bool               `json:"valid"` // Valid is true if InstallmentsStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullInstallmentsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.InstallmentsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.InstallmentsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullInstallmentsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.InstallmentsStatus), nil
}

type ScheduledRepaymentsStatus string

const (
	ScheduledRepaymentsStatusInit ScheduledRepaymentsStatus = "init"
	ScheduledRepaymentsStatusDue  ScheduledRepaymentsStatus = "due"
	ScheduledRepaymentsStatusPaid ScheduledRepaymentsStatus = "paid"
)

func (e *ScheduledRepaymentsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ScheduledRepaymentsStatus(s)
	case string:
		*e = ScheduledRepaymentsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ScheduledRepaymentsStatus: %T", src)
	}
	return nil
}

type NullScheduledRepaymentsStatus struct {
	ScheduledRepaymentsStatus ScheduledRepaymentsStatus `json:"scheduled_repayments_status"`
	Valid                     bool                      `json:"valid"` // Valid is true if ScheduledRepaymentsStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullScheduledRepaymentsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ScheduledRepaymentsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ScheduledRepaymentsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullScheduledRepaymentsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ScheduledRepaymentsStatus), nil
}

type StatementsSyncerStatus string

const (
	StatementsSyncerStatusPENDING StatementsSyncerStatus = "PENDING"
	StatementsSyncerStatusRUNNING StatementsSyncerStatus = "RUNNING"
	StatementsSyncerStatusFAILED  StatementsSyncerStatus = "FAILED"
	StatementsSyncerStatusSUCCESS StatementsSyncerStatus = "SUCCESS"
)

func (e *StatementsSyncerStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = StatementsSyncerStatus(s)
	case string:
		*e = StatementsSyncerStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for StatementsSyncerStatus: %T", src)
	}
	return nil
}

type NullStatementsSyncerStatus struct {
	StatementsSyncerStatus StatementsSyncerStatus `json:"statements_syncer_status"`
	Valid                  bool                   `json:"valid"` // Valid is true if StatementsSyncerStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullStatementsSyncerStatus) Scan(value interface{}) error {
	if value == nil {
		ns.StatementsSyncerStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.StatementsSyncerStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullStatementsSyncerStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.StatementsSyncerStatus), nil
}

type Installments struct {
	// The incremental id
	ID int64 `json:"id"`
	// Number of the installment
	Tenure int32 `json:"tenure"`
	// The status of the installment package
	Status InstallmentsStatus `json:"status"`
	// User zalopay id
	ZalopayID int64 `json:"zalopay_id"`
	// User account id
	AccountID int64 `json:"account_id"`
	// Ref to the zalopay transaction id
	ZpTransID int64 `json:"zp_trans_id"`
	// Partner code
	PartnerCode string `json:"partner_code"`
	// Ref to the partner installment id
	PartnerInstID sql.NullString `json:"partner_inst_id"`
	// The number of paid tenure
	PaidTenure sql.NullInt32 `json:"paid_tenure"`
	// The equated monthly installment amount (provisional or actual)
	EmiAmount sql.NullInt64 `json:"emi_amount"`
	// The proportion of an installment that is charged (provisional or actual)
	InterestRate sql.NullFloat64 `json:"interest_rate"`
	// The interest amount calculated by annual interest rate (provisional or actual)
	InterestAmount sql.NullInt64 `json:"interest_amount"`
	// The approved disbursed loan amount
	DisburseAmount sql.NullInt64 `json:"disburse_amount"`
	// Total fees of the installment
	TotalFeeAmount sql.NullInt64 `json:"total_fee_amount"`
	// Total amount including disbursed amount, interest amount, and fees (provisional)
	TotalDueAmount sql.NullInt64 `json:"total_due_amount"`
	// The object include some refund information
	RefundInfo *entity.InstallmentRefund `json:"refund_info"`
	// The object include some transaction information
	TransactionInfo entity.InstallmentTransaction `json:"transaction_info"`
	// The object include some outstanding information
	OutstandingInfo entity.JsonRawMessage `json:"outstanding_info"`
	// The object or array object include all fee information
	FeeDetails entity.JsonRawMessage `json:"fee_details"`
	// The object include some early discharge information
	EarlyDischarge entity.JsonRawMessage `json:"early_discharge"`
	// The latest snapshot discharge state
	DischargeStatus string `json:"discharge_status"`
	// Installment start DATE
	StartDate sql.NullTime `json:"start_date"`
	// Installment end DATE
	EndDate sql.NullTime `json:"end_date"`
	// Curr repayment due date
	CurrDueDate sql.NullTime `json:"curr_due_date"`
	// Next repayment due date
	NextDueDate sql.NullTime `json:"next_due_date"`
	// The number of days past due
	DaysPastDue int32        `json:"days_past_due"`
	CreatedAt   sql.NullTime `json:"created_at"`
	UpdatedAt   sql.NullTime `json:"updated_at"`
}

type ScheduledRepayments struct {
	// The repayment id
	ID int64 `json:"id"`
	// Installment sequence number
	SeqNo int32 `json:"seq_no"`
	// Ref to the installment id
	InstID int64 `json:"inst_id"`
	// Ref to the partner installment id
	PartnerInstID string `json:"partner_inst_id"`
	// Status of this period repayment
	Status ScheduledRepaymentsStatus `json:"status"`
	// Due date of this installment period
	DueDate sql.NullTime `json:"due_date"`
	// Grace due date of this installment period
	GraceDueDate sql.NullTime `json:"grace_due_date"`
	// Period emi amount
	EmiAmount sql.NullInt64 `json:"emi_amount"`
	// Period interest amount
	InterestAmount sql.NullInt64 `json:"interest_amount"`
	// Period outstanding principal amount
	PrincipalAmount sql.NullInt64 `json:"principal_amount"`
	// Penalty info include total, paid, outstanding
	PenaltyDetails json.RawMessage `json:"penalty_details"`
	// Outstanding info include principal, interest, total
	OutstandingDetails json.RawMessage `json:"outstanding_details"`
	CreatedAt          sql.NullTime    `json:"created_at"`
	UpdatedAt          sql.NullTime    `json:"updated_at"`
}

type Statements struct {
	// The statement id
	ID int64 `json:"id"`
	// Statement period format [YYYYMM] example 202101
	Period int32 `json:"period"`
	// Zalopay user id
	ZalopayID int64 `json:"zalopay_id"`
	// User account id
	AccountID int64 `json:"account_id"`
	// The partner code
	PartnerCode string `json:"partner_code"`
	// The repayment grace end date
	DueDate time.Time `json:"due_date"`
	// Statement incurred date
	IncurredDate time.Time `json:"incurred_date"`
	// Accumulated penalty amount
	PenaltyAmount int64 `json:"penalty_amount"`
	// Statement outstanding amount
	OutstandingAmount int64 `json:"outstanding_amount"`
	// Statement outstanding repaid
	OutstandingRepaid int64 `json:"outstanding_repaid"`
	// Outstanding balance calculated from penalty, repaid, and outstanding amount
	OutstandingBalance int64 `json:"outstanding_balance"`
	// The statement metadata
	Metadata  entity.StatementMetadata `json:"metadata"`
	CreatedAt sql.NullTime             `json:"created_at"`
	UpdatedAt sql.NullTime             `json:"updated_at"`
}

type StatementsInstallments struct {
	// The statement installment id
	ID int64 `json:"id"`
	// The statement id
	StatementID int64 `json:"statement_id"`
	// Statement date
	StatementDate time.Time `json:"statement_date"`
	// Zalopay user id
	ZalopayID int64 `json:"zalopay_id"`
	// User account id
	AccountID int64 `json:"account_id"`
	// The partner code
	PartnerCode string `json:"partner_code"`
	// Reference installment id of partner
	PartnerInstID string `json:"partner_inst_id"`
	// The installment due date
	DueDate sql.NullTime `json:"due_date"`
	// The installment amount
	InstallmentAmount int64 `json:"installment_amount"`
	// The installment outstanding info
	OutstandingDetails entity.StatementInstallmentOuts `json:"outstanding_details"`
	CreatedAt          sql.NullTime                    `json:"created_at"`
	UpdatedAt          sql.NullTime                    `json:"updated_at"`
}

type StatementsSyncer struct {
	// The statement syncer id
	ID int64 `json:"id"`
	// Statement incurred date
	StatementDate sql.NullTime `json:"statement_date"`
	// Statement period format [YYYYMM] example 202101
	StatementPeriod int32 `json:"statement_period"`
	// The partner code of the statement
	PartnerCode string `json:"partner_code"`
	// The batch number of the sync process, optional
	BatchNo sql.NullInt32 `json:"batch_no"`
	// The status of the sync process
	Status StatementsSyncerStatus `json:"status"`
	// The execution id of the sync process
	ExecID string `json:"exec_id"`
	// The sync statistics of the statement
	SyncStats json.RawMessage `json:"sync_stats"`
	CreatedAt sql.NullTime    `json:"created_at"`
	UpdatedAt sql.NullTime    `json:"updated_at"`
}
