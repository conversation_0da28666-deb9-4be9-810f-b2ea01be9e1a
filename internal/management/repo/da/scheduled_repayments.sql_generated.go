// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: scheduled_repayments.sql

package da

import (
	"context"
	"database/sql"
	"encoding/json"
	"strings"
)

const getRepaymentScheduleByInstID = `-- name: GetRepaymentScheduleByInstID :many
SELECT id, seq_no, inst_id, partner_inst_id, status, due_date, grace_due_date, emi_amount, interest_amount, principal_amount, penalty_details, outstanding_details, created_at, updated_at FROM scheduled_repayments
WHERE ` + "`" + `inst_id` + "`" + ` = ?
`

func (q *Queries) GetRepaymentScheduleByInstID(ctx context.Context, instID int64) ([]*ScheduledRepayments, error) {
	rows, err := q.query(ctx, q.getRepaymentScheduleByInstIDStmt, getRepaymentScheduleByInstID, instID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*ScheduledRepayments
	for rows.Next() {
		var i ScheduledRepayments
		if err := rows.Scan(
			&i.ID,
			&i.SeqNo,
			&i.InstID,
			&i.PartnerInstID,
			&i.Status,
			&i.DueDate,
			&i.GraceDueDate,
			&i.EmiAmount,
			&i.InterestAmount,
			&i.PrincipalAmount,
			&i.PenaltyDetails,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const insertRepaymentSchedule = `-- name: InsertRepaymentSchedule :exec
INSERT INTO scheduled_repayments (
    ` + "`" + `seq_no` + "`" + `, ` + "`" + `inst_id` + "`" + `, ` + "`" + `partner_inst_id` + "`" + `, ` + "`" + `status` + "`" + `, ` + "`" + `due_date` + "`" + `, ` + "`" + `grace_due_date` + "`" + `,
    ` + "`" + `emi_amount` + "`" + `, ` + "`" + `interest_amount` + "`" + `, ` + "`" + `principal_amount` + "`" + `,
    ` + "`" + `penalty_details` + "`" + `, ` + "`" + `outstanding_details` + "`" + `
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type InsertRepaymentScheduleParams struct {
	SeqNo              int32                     `json:"seq_no"`
	InstID             int64                     `json:"inst_id"`
	PartnerInstID      string                    `json:"partner_inst_id"`
	Status             ScheduledRepaymentsStatus `json:"status"`
	DueDate            sql.NullTime              `json:"due_date"`
	GraceDueDate       sql.NullTime              `json:"grace_due_date"`
	EmiAmount          sql.NullInt64             `json:"emi_amount"`
	InterestAmount     sql.NullInt64             `json:"interest_amount"`
	PrincipalAmount    sql.NullInt64             `json:"principal_amount"`
	PenaltyDetails     json.RawMessage           `json:"penalty_details"`
	OutstandingDetails json.RawMessage           `json:"outstanding_details"`
}

func (q *Queries) InsertRepaymentSchedule(ctx context.Context, arg *InsertRepaymentScheduleParams) error {
	_, err := q.exec(ctx, q.insertRepaymentScheduleStmt, insertRepaymentSchedule,
		arg.SeqNo,
		arg.InstID,
		arg.PartnerInstID,
		arg.Status,
		arg.DueDate,
		arg.GraceDueDate,
		arg.EmiAmount,
		arg.InterestAmount,
		arg.PrincipalAmount,
		arg.PenaltyDetails,
		arg.OutstandingDetails,
	)
	return err
}

const listRepaymentScheduleByInstIDs = `-- name: ListRepaymentScheduleByInstIDs :many
SELECT id, seq_no, inst_id, partner_inst_id, status, due_date, grace_due_date, emi_amount, interest_amount, principal_amount, penalty_details, outstanding_details, created_at, updated_at FROM scheduled_repayments
WHERE ` + "`" + `inst_id` + "`" + ` IN (/*SLICE:inst_ids*/?)
`

func (q *Queries) ListRepaymentScheduleByInstIDs(ctx context.Context, instIds []int64) ([]*ScheduledRepayments, error) {
	query := listRepaymentScheduleByInstIDs
	var queryParams []interface{}
	if len(instIds) > 0 {
		for _, v := range instIds {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:inst_ids*/?", strings.Repeat(",?", len(instIds))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:inst_ids*/?", "NULL", 1)
	}
	rows, err := q.query(ctx, nil, query, queryParams...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*ScheduledRepayments
	for rows.Next() {
		var i ScheduledRepayments
		if err := rows.Scan(
			&i.ID,
			&i.SeqNo,
			&i.InstID,
			&i.PartnerInstID,
			&i.Status,
			&i.DueDate,
			&i.GraceDueDate,
			&i.EmiAmount,
			&i.InterestAmount,
			&i.PrincipalAmount,
			&i.PenaltyDetails,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateRepaymentScheduleAfterSync = `-- name: UpdateRepaymentScheduleAfterSync :exec
UPDATE scheduled_repayments
SET ` + "`" + `status` + "`" + ` = ?, ` + "`" + `due_date` + "`" + ` = ?, ` + "`" + `grace_due_date` + "`" + ` = ?,
    ` + "`" + `penalty_details` + "`" + ` = ?, ` + "`" + `outstanding_details` + "`" + ` = ?,
    ` + "`" + `updated_at` + "`" + ` = CURRENT_TIMESTAMP
WHERE ` + "`" + `inst_id` + "`" + ` = ? AND ` + "`" + `seq_no` + "`" + ` = ?
`

type UpdateRepaymentScheduleAfterSyncParams struct {
	Status             ScheduledRepaymentsStatus `json:"status"`
	DueDate            sql.NullTime              `json:"due_date"`
	GraceDueDate       sql.NullTime              `json:"grace_due_date"`
	PenaltyDetails     json.RawMessage           `json:"penalty_details"`
	OutstandingDetails json.RawMessage           `json:"outstanding_details"`
	InstID             int64                     `json:"inst_id"`
	SeqNo              int32                     `json:"seq_no"`
}

// Force updated_at to NOW() because we want to track the last sync time instead data not changed
func (q *Queries) UpdateRepaymentScheduleAfterSync(ctx context.Context, arg *UpdateRepaymentScheduleAfterSyncParams) error {
	_, err := q.exec(ctx, q.updateRepaymentScheduleAfterSyncStmt, updateRepaymentScheduleAfterSync,
		arg.Status,
		arg.DueDate,
		arg.GraceDueDate,
		arg.PenaltyDetails,
		arg.OutstandingDetails,
		arg.InstID,
		arg.SeqNo,
	)
	return err
}
