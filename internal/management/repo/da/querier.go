// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
	"time"
)

type Querier interface {
	GetInstallmentByID(ctx context.Context, id int64) (*Installments, error)
	GetInstallmentByIDForUpdate(ctx context.Context, id int64) (*Installments, error)
	GetInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*Installments, error)
	GetInstallmentByZalopayIDAndZPTransID(ctx context.Context, arg *GetInstallmentByZalopayIDAndZPTransIDParams) (*Installments, error)
	GetRepaymentScheduleByInstID(ctx context.Context, instID int64) ([]*ScheduledRepayments, error)
	GetStatementByID(ctx context.Context, id int64) (*Statements, error)
	GetStatementDueDateByIncurredDateAndPartner(ctx context.Context, arg *GetStatementDueDateByIncurredDateAndPartnerParams) (time.Time, error)
	GetSyncerBatchByPeriodAndPartner(ctx context.Context, arg *GetSyncerBatchByPeriodAndPartnerParams) ([]*StatementsSyncer, error)
	GetUserLatestStatement(ctx context.Context, arg *GetUserLatestStatementParams) (*Statements, error)
	GetUserLatestStatementByPeriod(ctx context.Context, arg *GetUserLatestStatementByPeriodParams) (*Statements, error)
	GetUserStatementByIncurredDate(ctx context.Context, arg *GetUserStatementByIncurredDateParams) (*Statements, error)
	GetUserStatementByPeriodAndIncurredDate(ctx context.Context, arg *GetUserStatementByPeriodAndIncurredDateParams) (*Statements, error)
	InsertInstallment(ctx context.Context, arg *InsertInstallmentParams) (sql.Result, error)
	InsertRepaymentSchedule(ctx context.Context, arg *InsertRepaymentScheduleParams) error
	InsertSyncer(ctx context.Context, arg *InsertSyncerParams) (sql.Result, error)
	ListInstallmentByPartnerRefIDs(ctx context.Context, partnerInstIds []sql.NullString) ([]*Installments, error)
	ListInstallmentPeriodicSync(ctx context.Context, arg *ListInstallmentPeriodicSyncParams) ([]*Installments, error)
	ListOpenInstallmentByZalopayIDAndAcctID(ctx context.Context, arg *ListOpenInstallmentByZalopayIDAndAcctIDParams) ([]*Installments, error)
	ListOutstandingStatementByIncurredDateAndPartner(ctx context.Context, arg *ListOutstandingStatementByIncurredDateAndPartnerParams) ([]*Statements, error)
	ListRepaymentScheduleByInstIDs(ctx context.Context, instIds []int64) ([]*ScheduledRepayments, error)
	ListStatementByIncurredDateAndPartner(ctx context.Context, arg *ListStatementByIncurredDateAndPartnerParams) ([]*Statements, error)
	ListStmtInstallmentByClientAndStmtDate(ctx context.Context, arg *ListStmtInstallmentByClientAndStmtDateParams) ([]*StatementsInstallments, error)
	ListStmtInstallmentByStatementId(ctx context.Context, statementID int64) ([]*StatementsInstallments, error)
	ListStmtInstallmentByStmtId(ctx context.Context, statementID int64) ([]*StatementsInstallments, error)
	ListStmtInstallmentByUserAndStmtId(ctx context.Context, arg *ListStmtInstallmentByUserAndStmtIdParams) ([]*StatementsInstallments, error)
	ListUserStatementByIncurredDateRange(ctx context.Context, arg *ListUserStatementByIncurredDateRangeParams) ([]*Statements, error)
	ListUserStatementByPeriod(ctx context.Context, arg *ListUserStatementByPeriodParams) ([]*Statements, error)
	UpdateDischargeStatus(ctx context.Context, arg *UpdateDischargeStatusParams) error
	UpdateEarlyDischargeInfo(ctx context.Context, arg *UpdateEarlyDischargeInfoParams) error
	UpdateInstallment(ctx context.Context, arg *UpdateInstallmentParams) error
	// Force updated_at to NOW() because we want to track the last sync time instead data not changed
	UpdateInstallmentAfterSync(ctx context.Context, arg *UpdateInstallmentAfterSyncParams) error
	UpdateInstallmentRefund(ctx context.Context, arg *UpdateInstallmentRefundParams) error
	UpdateInstallmentTransaction(ctx context.Context, arg *UpdateInstallmentTransactionParams) error
	// Force updated_at to NOW() because we want to track the last sync time instead data not changed
	UpdateRepaymentScheduleAfterSync(ctx context.Context, arg *UpdateRepaymentScheduleAfterSyncParams) error
	UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, arg *UpdateStatementOutstandingAndPenaltyByIDParams) error
	UpdateStatementOutstandingByIncurredDate(ctx context.Context, arg *UpdateStatementOutstandingByIncurredDateParams) error
	UpdateSyncerStatus(ctx context.Context, arg *UpdateSyncerStatusParams) error
	UpdateSyncerStatusAndStats(ctx context.Context, arg *UpdateSyncerStatusAndStatsParams) error
	UpsertStatement(ctx context.Context, arg *UpsertStatementParams) (sql.Result, error)
	UpsertStmtInstallment(ctx context.Context, arg *UpsertStmtInstallmentParams) error
	UpsertSyncer(ctx context.Context, arg *UpsertSyncerParams) (sql.Result, error)
}

var _ Querier = (*Queries)(nil)
