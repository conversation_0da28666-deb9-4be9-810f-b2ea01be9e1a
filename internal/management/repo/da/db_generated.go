// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.getInstallmentByIDStmt, err = db.PrepareContext(ctx, getInstallmentByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetInstallmentByID: %w", err)
	}
	if q.getInstallmentByIDForUpdateStmt, err = db.PrepareContext(ctx, getInstallmentByIDForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetInstallmentByIDForUpdate: %w", err)
	}
	if q.getInstallmentByZPTransIDStmt, err = db.PrepareContext(ctx, getInstallmentByZPTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetInstallmentByZPTransID: %w", err)
	}
	if q.getInstallmentByZalopayIDAndZPTransIDStmt, err = db.PrepareContext(ctx, getInstallmentByZalopayIDAndZPTransID); err != nil {
		return nil, fmt.Errorf("error preparing query GetInstallmentByZalopayIDAndZPTransID: %w", err)
	}
	if q.getRepaymentScheduleByInstIDStmt, err = db.PrepareContext(ctx, getRepaymentScheduleByInstID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRepaymentScheduleByInstID: %w", err)
	}
	if q.getStatementByIDStmt, err = db.PrepareContext(ctx, getStatementByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetStatementByID: %w", err)
	}
	if q.getStatementDueDateByIncurredDateAndPartnerStmt, err = db.PrepareContext(ctx, getStatementDueDateByIncurredDateAndPartner); err != nil {
		return nil, fmt.Errorf("error preparing query GetStatementDueDateByIncurredDateAndPartner: %w", err)
	}
	if q.getSyncerBatchByPeriodAndPartnerStmt, err = db.PrepareContext(ctx, getSyncerBatchByPeriodAndPartner); err != nil {
		return nil, fmt.Errorf("error preparing query GetSyncerBatchByPeriodAndPartner: %w", err)
	}
	if q.getUserLatestStatementStmt, err = db.PrepareContext(ctx, getUserLatestStatement); err != nil {
		return nil, fmt.Errorf("error preparing query GetUserLatestStatement: %w", err)
	}
	if q.getUserLatestStatementByPeriodStmt, err = db.PrepareContext(ctx, getUserLatestStatementByPeriod); err != nil {
		return nil, fmt.Errorf("error preparing query GetUserLatestStatementByPeriod: %w", err)
	}
	if q.getUserStatementByIncurredDateStmt, err = db.PrepareContext(ctx, getUserStatementByIncurredDate); err != nil {
		return nil, fmt.Errorf("error preparing query GetUserStatementByIncurredDate: %w", err)
	}
	if q.getUserStatementByPeriodAndIncurredDateStmt, err = db.PrepareContext(ctx, getUserStatementByPeriodAndIncurredDate); err != nil {
		return nil, fmt.Errorf("error preparing query GetUserStatementByPeriodAndIncurredDate: %w", err)
	}
	if q.insertInstallmentStmt, err = db.PrepareContext(ctx, insertInstallment); err != nil {
		return nil, fmt.Errorf("error preparing query InsertInstallment: %w", err)
	}
	if q.insertRepaymentScheduleStmt, err = db.PrepareContext(ctx, insertRepaymentSchedule); err != nil {
		return nil, fmt.Errorf("error preparing query InsertRepaymentSchedule: %w", err)
	}
	if q.insertSyncerStmt, err = db.PrepareContext(ctx, insertSyncer); err != nil {
		return nil, fmt.Errorf("error preparing query InsertSyncer: %w", err)
	}
	if q.listInstallmentByPartnerRefIDsStmt, err = db.PrepareContext(ctx, listInstallmentByPartnerRefIDs); err != nil {
		return nil, fmt.Errorf("error preparing query ListInstallmentByPartnerRefIDs: %w", err)
	}
	if q.listInstallmentPeriodicSyncStmt, err = db.PrepareContext(ctx, listInstallmentPeriodicSync); err != nil {
		return nil, fmt.Errorf("error preparing query ListInstallmentPeriodicSync: %w", err)
	}
	if q.listOpenInstallmentByZalopayIDAndAcctIDStmt, err = db.PrepareContext(ctx, listOpenInstallmentByZalopayIDAndAcctID); err != nil {
		return nil, fmt.Errorf("error preparing query ListOpenInstallmentByZalopayIDAndAcctID: %w", err)
	}
	if q.listOutstandingStatementByIncurredDateAndPartnerStmt, err = db.PrepareContext(ctx, listOutstandingStatementByIncurredDateAndPartner); err != nil {
		return nil, fmt.Errorf("error preparing query ListOutstandingStatementByIncurredDateAndPartner: %w", err)
	}
	if q.listRepaymentScheduleByInstIDsStmt, err = db.PrepareContext(ctx, listRepaymentScheduleByInstIDs); err != nil {
		return nil, fmt.Errorf("error preparing query ListRepaymentScheduleByInstIDs: %w", err)
	}
	if q.listStatementByIncurredDateAndPartnerStmt, err = db.PrepareContext(ctx, listStatementByIncurredDateAndPartner); err != nil {
		return nil, fmt.Errorf("error preparing query ListStatementByIncurredDateAndPartner: %w", err)
	}
	if q.listStmtInstallmentByClientAndStmtDateStmt, err = db.PrepareContext(ctx, listStmtInstallmentByClientAndStmtDate); err != nil {
		return nil, fmt.Errorf("error preparing query ListStmtInstallmentByClientAndStmtDate: %w", err)
	}
	if q.listStmtInstallmentByStatementIdStmt, err = db.PrepareContext(ctx, listStmtInstallmentByStatementId); err != nil {
		return nil, fmt.Errorf("error preparing query ListStmtInstallmentByStatementId: %w", err)
	}
	if q.listStmtInstallmentByStmtIdStmt, err = db.PrepareContext(ctx, listStmtInstallmentByStmtId); err != nil {
		return nil, fmt.Errorf("error preparing query ListStmtInstallmentByStmtId: %w", err)
	}
	if q.listStmtInstallmentByUserAndStmtIdStmt, err = db.PrepareContext(ctx, listStmtInstallmentByUserAndStmtId); err != nil {
		return nil, fmt.Errorf("error preparing query ListStmtInstallmentByUserAndStmtId: %w", err)
	}
	if q.listUserStatementByIncurredDateRangeStmt, err = db.PrepareContext(ctx, listUserStatementByIncurredDateRange); err != nil {
		return nil, fmt.Errorf("error preparing query ListUserStatementByIncurredDateRange: %w", err)
	}
	if q.listUserStatementByPeriodStmt, err = db.PrepareContext(ctx, listUserStatementByPeriod); err != nil {
		return nil, fmt.Errorf("error preparing query ListUserStatementByPeriod: %w", err)
	}
	if q.updateDischargeStatusStmt, err = db.PrepareContext(ctx, updateDischargeStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateDischargeStatus: %w", err)
	}
	if q.updateEarlyDischargeInfoStmt, err = db.PrepareContext(ctx, updateEarlyDischargeInfo); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateEarlyDischargeInfo: %w", err)
	}
	if q.updateInstallmentStmt, err = db.PrepareContext(ctx, updateInstallment); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateInstallment: %w", err)
	}
	if q.updateInstallmentAfterSyncStmt, err = db.PrepareContext(ctx, updateInstallmentAfterSync); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateInstallmentAfterSync: %w", err)
	}
	if q.updateInstallmentRefundStmt, err = db.PrepareContext(ctx, updateInstallmentRefund); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateInstallmentRefund: %w", err)
	}
	if q.updateInstallmentTransactionStmt, err = db.PrepareContext(ctx, updateInstallmentTransaction); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateInstallmentTransaction: %w", err)
	}
	if q.updateRepaymentScheduleAfterSyncStmt, err = db.PrepareContext(ctx, updateRepaymentScheduleAfterSync); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateRepaymentScheduleAfterSync: %w", err)
	}
	if q.updateStatementOutstandingAndPenaltyByIDStmt, err = db.PrepareContext(ctx, updateStatementOutstandingAndPenaltyByID); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStatementOutstandingAndPenaltyByID: %w", err)
	}
	if q.updateStatementOutstandingByIncurredDateStmt, err = db.PrepareContext(ctx, updateStatementOutstandingByIncurredDate); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStatementOutstandingByIncurredDate: %w", err)
	}
	if q.updateSyncerStatusStmt, err = db.PrepareContext(ctx, updateSyncerStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateSyncerStatus: %w", err)
	}
	if q.updateSyncerStatusAndStatsStmt, err = db.PrepareContext(ctx, updateSyncerStatusAndStats); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateSyncerStatusAndStats: %w", err)
	}
	if q.upsertStatementStmt, err = db.PrepareContext(ctx, upsertStatement); err != nil {
		return nil, fmt.Errorf("error preparing query UpsertStatement: %w", err)
	}
	if q.upsertStmtInstallmentStmt, err = db.PrepareContext(ctx, upsertStmtInstallment); err != nil {
		return nil, fmt.Errorf("error preparing query UpsertStmtInstallment: %w", err)
	}
	if q.upsertSyncerStmt, err = db.PrepareContext(ctx, upsertSyncer); err != nil {
		return nil, fmt.Errorf("error preparing query UpsertSyncer: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.getInstallmentByIDStmt != nil {
		if cerr := q.getInstallmentByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getInstallmentByIDStmt: %w", cerr)
		}
	}
	if q.getInstallmentByIDForUpdateStmt != nil {
		if cerr := q.getInstallmentByIDForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getInstallmentByIDForUpdateStmt: %w", cerr)
		}
	}
	if q.getInstallmentByZPTransIDStmt != nil {
		if cerr := q.getInstallmentByZPTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getInstallmentByZPTransIDStmt: %w", cerr)
		}
	}
	if q.getInstallmentByZalopayIDAndZPTransIDStmt != nil {
		if cerr := q.getInstallmentByZalopayIDAndZPTransIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getInstallmentByZalopayIDAndZPTransIDStmt: %w", cerr)
		}
	}
	if q.getRepaymentScheduleByInstIDStmt != nil {
		if cerr := q.getRepaymentScheduleByInstIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRepaymentScheduleByInstIDStmt: %w", cerr)
		}
	}
	if q.getStatementByIDStmt != nil {
		if cerr := q.getStatementByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getStatementByIDStmt: %w", cerr)
		}
	}
	if q.getStatementDueDateByIncurredDateAndPartnerStmt != nil {
		if cerr := q.getStatementDueDateByIncurredDateAndPartnerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getStatementDueDateByIncurredDateAndPartnerStmt: %w", cerr)
		}
	}
	if q.getSyncerBatchByPeriodAndPartnerStmt != nil {
		if cerr := q.getSyncerBatchByPeriodAndPartnerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getSyncerBatchByPeriodAndPartnerStmt: %w", cerr)
		}
	}
	if q.getUserLatestStatementStmt != nil {
		if cerr := q.getUserLatestStatementStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getUserLatestStatementStmt: %w", cerr)
		}
	}
	if q.getUserLatestStatementByPeriodStmt != nil {
		if cerr := q.getUserLatestStatementByPeriodStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getUserLatestStatementByPeriodStmt: %w", cerr)
		}
	}
	if q.getUserStatementByIncurredDateStmt != nil {
		if cerr := q.getUserStatementByIncurredDateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getUserStatementByIncurredDateStmt: %w", cerr)
		}
	}
	if q.getUserStatementByPeriodAndIncurredDateStmt != nil {
		if cerr := q.getUserStatementByPeriodAndIncurredDateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getUserStatementByPeriodAndIncurredDateStmt: %w", cerr)
		}
	}
	if q.insertInstallmentStmt != nil {
		if cerr := q.insertInstallmentStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing insertInstallmentStmt: %w", cerr)
		}
	}
	if q.insertRepaymentScheduleStmt != nil {
		if cerr := q.insertRepaymentScheduleStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing insertRepaymentScheduleStmt: %w", cerr)
		}
	}
	if q.insertSyncerStmt != nil {
		if cerr := q.insertSyncerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing insertSyncerStmt: %w", cerr)
		}
	}
	if q.listInstallmentByPartnerRefIDsStmt != nil {
		if cerr := q.listInstallmentByPartnerRefIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listInstallmentByPartnerRefIDsStmt: %w", cerr)
		}
	}
	if q.listInstallmentPeriodicSyncStmt != nil {
		if cerr := q.listInstallmentPeriodicSyncStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listInstallmentPeriodicSyncStmt: %w", cerr)
		}
	}
	if q.listOpenInstallmentByZalopayIDAndAcctIDStmt != nil {
		if cerr := q.listOpenInstallmentByZalopayIDAndAcctIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listOpenInstallmentByZalopayIDAndAcctIDStmt: %w", cerr)
		}
	}
	if q.listOutstandingStatementByIncurredDateAndPartnerStmt != nil {
		if cerr := q.listOutstandingStatementByIncurredDateAndPartnerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listOutstandingStatementByIncurredDateAndPartnerStmt: %w", cerr)
		}
	}
	if q.listRepaymentScheduleByInstIDsStmt != nil {
		if cerr := q.listRepaymentScheduleByInstIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listRepaymentScheduleByInstIDsStmt: %w", cerr)
		}
	}
	if q.listStatementByIncurredDateAndPartnerStmt != nil {
		if cerr := q.listStatementByIncurredDateAndPartnerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listStatementByIncurredDateAndPartnerStmt: %w", cerr)
		}
	}
	if q.listStmtInstallmentByClientAndStmtDateStmt != nil {
		if cerr := q.listStmtInstallmentByClientAndStmtDateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listStmtInstallmentByClientAndStmtDateStmt: %w", cerr)
		}
	}
	if q.listStmtInstallmentByStatementIdStmt != nil {
		if cerr := q.listStmtInstallmentByStatementIdStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listStmtInstallmentByStatementIdStmt: %w", cerr)
		}
	}
	if q.listStmtInstallmentByStmtIdStmt != nil {
		if cerr := q.listStmtInstallmentByStmtIdStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listStmtInstallmentByStmtIdStmt: %w", cerr)
		}
	}
	if q.listStmtInstallmentByUserAndStmtIdStmt != nil {
		if cerr := q.listStmtInstallmentByUserAndStmtIdStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listStmtInstallmentByUserAndStmtIdStmt: %w", cerr)
		}
	}
	if q.listUserStatementByIncurredDateRangeStmt != nil {
		if cerr := q.listUserStatementByIncurredDateRangeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listUserStatementByIncurredDateRangeStmt: %w", cerr)
		}
	}
	if q.listUserStatementByPeriodStmt != nil {
		if cerr := q.listUserStatementByPeriodStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listUserStatementByPeriodStmt: %w", cerr)
		}
	}
	if q.updateDischargeStatusStmt != nil {
		if cerr := q.updateDischargeStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateDischargeStatusStmt: %w", cerr)
		}
	}
	if q.updateEarlyDischargeInfoStmt != nil {
		if cerr := q.updateEarlyDischargeInfoStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateEarlyDischargeInfoStmt: %w", cerr)
		}
	}
	if q.updateInstallmentStmt != nil {
		if cerr := q.updateInstallmentStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateInstallmentStmt: %w", cerr)
		}
	}
	if q.updateInstallmentAfterSyncStmt != nil {
		if cerr := q.updateInstallmentAfterSyncStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateInstallmentAfterSyncStmt: %w", cerr)
		}
	}
	if q.updateInstallmentRefundStmt != nil {
		if cerr := q.updateInstallmentRefundStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateInstallmentRefundStmt: %w", cerr)
		}
	}
	if q.updateInstallmentTransactionStmt != nil {
		if cerr := q.updateInstallmentTransactionStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateInstallmentTransactionStmt: %w", cerr)
		}
	}
	if q.updateRepaymentScheduleAfterSyncStmt != nil {
		if cerr := q.updateRepaymentScheduleAfterSyncStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateRepaymentScheduleAfterSyncStmt: %w", cerr)
		}
	}
	if q.updateStatementOutstandingAndPenaltyByIDStmt != nil {
		if cerr := q.updateStatementOutstandingAndPenaltyByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStatementOutstandingAndPenaltyByIDStmt: %w", cerr)
		}
	}
	if q.updateStatementOutstandingByIncurredDateStmt != nil {
		if cerr := q.updateStatementOutstandingByIncurredDateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStatementOutstandingByIncurredDateStmt: %w", cerr)
		}
	}
	if q.updateSyncerStatusStmt != nil {
		if cerr := q.updateSyncerStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateSyncerStatusStmt: %w", cerr)
		}
	}
	if q.updateSyncerStatusAndStatsStmt != nil {
		if cerr := q.updateSyncerStatusAndStatsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateSyncerStatusAndStatsStmt: %w", cerr)
		}
	}
	if q.upsertStatementStmt != nil {
		if cerr := q.upsertStatementStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing upsertStatementStmt: %w", cerr)
		}
	}
	if q.upsertStmtInstallmentStmt != nil {
		if cerr := q.upsertStmtInstallmentStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing upsertStmtInstallmentStmt: %w", cerr)
		}
	}
	if q.upsertSyncerStmt != nil {
		if cerr := q.upsertSyncerStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing upsertSyncerStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                                                   DBTX
	tx                                                   *sql.Tx
	getInstallmentByIDStmt                               *sql.Stmt
	getInstallmentByIDForUpdateStmt                      *sql.Stmt
	getInstallmentByZPTransIDStmt                        *sql.Stmt
	getInstallmentByZalopayIDAndZPTransIDStmt            *sql.Stmt
	getRepaymentScheduleByInstIDStmt                     *sql.Stmt
	getStatementByIDStmt                                 *sql.Stmt
	getStatementDueDateByIncurredDateAndPartnerStmt      *sql.Stmt
	getSyncerBatchByPeriodAndPartnerStmt                 *sql.Stmt
	getUserLatestStatementStmt                           *sql.Stmt
	getUserLatestStatementByPeriodStmt                   *sql.Stmt
	getUserStatementByIncurredDateStmt                   *sql.Stmt
	getUserStatementByPeriodAndIncurredDateStmt          *sql.Stmt
	insertInstallmentStmt                                *sql.Stmt
	insertRepaymentScheduleStmt                          *sql.Stmt
	insertSyncerStmt                                     *sql.Stmt
	listInstallmentByPartnerRefIDsStmt                   *sql.Stmt
	listInstallmentPeriodicSyncStmt                      *sql.Stmt
	listOpenInstallmentByZalopayIDAndAcctIDStmt          *sql.Stmt
	listOutstandingStatementByIncurredDateAndPartnerStmt *sql.Stmt
	listRepaymentScheduleByInstIDsStmt                   *sql.Stmt
	listStatementByIncurredDateAndPartnerStmt            *sql.Stmt
	listStmtInstallmentByClientAndStmtDateStmt           *sql.Stmt
	listStmtInstallmentByStatementIdStmt                 *sql.Stmt
	listStmtInstallmentByStmtIdStmt                      *sql.Stmt
	listStmtInstallmentByUserAndStmtIdStmt               *sql.Stmt
	listUserStatementByIncurredDateRangeStmt             *sql.Stmt
	listUserStatementByPeriodStmt                        *sql.Stmt
	updateDischargeStatusStmt                            *sql.Stmt
	updateEarlyDischargeInfoStmt                         *sql.Stmt
	updateInstallmentStmt                                *sql.Stmt
	updateInstallmentAfterSyncStmt                       *sql.Stmt
	updateInstallmentRefundStmt                          *sql.Stmt
	updateInstallmentTransactionStmt                     *sql.Stmt
	updateRepaymentScheduleAfterSyncStmt                 *sql.Stmt
	updateStatementOutstandingAndPenaltyByIDStmt         *sql.Stmt
	updateStatementOutstandingByIncurredDateStmt         *sql.Stmt
	updateSyncerStatusStmt                               *sql.Stmt
	updateSyncerStatusAndStatsStmt                       *sql.Stmt
	upsertStatementStmt                                  *sql.Stmt
	upsertStmtInstallmentStmt                            *sql.Stmt
	upsertSyncerStmt                                     *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                              tx,
		tx:                              tx,
		getInstallmentByIDStmt:          q.getInstallmentByIDStmt,
		getInstallmentByIDForUpdateStmt: q.getInstallmentByIDForUpdateStmt,
		getInstallmentByZPTransIDStmt:   q.getInstallmentByZPTransIDStmt,
		getInstallmentByZalopayIDAndZPTransIDStmt:            q.getInstallmentByZalopayIDAndZPTransIDStmt,
		getRepaymentScheduleByInstIDStmt:                     q.getRepaymentScheduleByInstIDStmt,
		getStatementByIDStmt:                                 q.getStatementByIDStmt,
		getStatementDueDateByIncurredDateAndPartnerStmt:      q.getStatementDueDateByIncurredDateAndPartnerStmt,
		getSyncerBatchByPeriodAndPartnerStmt:                 q.getSyncerBatchByPeriodAndPartnerStmt,
		getUserLatestStatementStmt:                           q.getUserLatestStatementStmt,
		getUserLatestStatementByPeriodStmt:                   q.getUserLatestStatementByPeriodStmt,
		getUserStatementByIncurredDateStmt:                   q.getUserStatementByIncurredDateStmt,
		getUserStatementByPeriodAndIncurredDateStmt:          q.getUserStatementByPeriodAndIncurredDateStmt,
		insertInstallmentStmt:                                q.insertInstallmentStmt,
		insertRepaymentScheduleStmt:                          q.insertRepaymentScheduleStmt,
		insertSyncerStmt:                                     q.insertSyncerStmt,
		listInstallmentByPartnerRefIDsStmt:                   q.listInstallmentByPartnerRefIDsStmt,
		listInstallmentPeriodicSyncStmt:                      q.listInstallmentPeriodicSyncStmt,
		listOpenInstallmentByZalopayIDAndAcctIDStmt:          q.listOpenInstallmentByZalopayIDAndAcctIDStmt,
		listOutstandingStatementByIncurredDateAndPartnerStmt: q.listOutstandingStatementByIncurredDateAndPartnerStmt,
		listRepaymentScheduleByInstIDsStmt:                   q.listRepaymentScheduleByInstIDsStmt,
		listStatementByIncurredDateAndPartnerStmt:            q.listStatementByIncurredDateAndPartnerStmt,
		listStmtInstallmentByClientAndStmtDateStmt:           q.listStmtInstallmentByClientAndStmtDateStmt,
		listStmtInstallmentByStatementIdStmt:                 q.listStmtInstallmentByStatementIdStmt,
		listStmtInstallmentByStmtIdStmt:                      q.listStmtInstallmentByStmtIdStmt,
		listStmtInstallmentByUserAndStmtIdStmt:               q.listStmtInstallmentByUserAndStmtIdStmt,
		listUserStatementByIncurredDateRangeStmt:             q.listUserStatementByIncurredDateRangeStmt,
		listUserStatementByPeriodStmt:                        q.listUserStatementByPeriodStmt,
		updateDischargeStatusStmt:                            q.updateDischargeStatusStmt,
		updateEarlyDischargeInfoStmt:                         q.updateEarlyDischargeInfoStmt,
		updateInstallmentStmt:                                q.updateInstallmentStmt,
		updateInstallmentAfterSyncStmt:                       q.updateInstallmentAfterSyncStmt,
		updateInstallmentRefundStmt:                          q.updateInstallmentRefundStmt,
		updateInstallmentTransactionStmt:                     q.updateInstallmentTransactionStmt,
		updateRepaymentScheduleAfterSyncStmt:                 q.updateRepaymentScheduleAfterSyncStmt,
		updateStatementOutstandingAndPenaltyByIDStmt:         q.updateStatementOutstandingAndPenaltyByIDStmt,
		updateStatementOutstandingByIncurredDateStmt:         q.updateStatementOutstandingByIncurredDateStmt,
		updateSyncerStatusStmt:                               q.updateSyncerStatusStmt,
		updateSyncerStatusAndStatsStmt:                       q.updateSyncerStatusAndStatsStmt,
		upsertStatementStmt:                                  q.upsertStatementStmt,
		upsertStmtInstallmentStmt:                            q.upsertStmtInstallmentStmt,
		upsertSyncerStmt:                                     q.upsertSyncerStmt,
	}
}
