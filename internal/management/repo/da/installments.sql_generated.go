// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: installments.sql

package da

import (
	"context"
	"database/sql"
	"strings"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
)

const getInstallmentByID = `-- name: GetInstallmentByID :one
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `id` + "`" + ` = ?
`

func (q *Queries) GetInstallmentByID(ctx context.Context, id int64) (*Installments, error) {
	row := q.queryRow(ctx, q.getInstallmentByIDStmt, getInstallmentByID, id)
	var i Installments
	err := row.Scan(
		&i.ID,
		&i.Tenure,
		&i.Status,
		&i.ZalopayID,
		&i.AccountID,
		&i.ZpTransID,
		&i.PartnerCode,
		&i.PartnerInstID,
		&i.PaidTenure,
		&i.EmiAmount,
		&i.InterestRate,
		&i.InterestAmount,
		&i.DisburseAmount,
		&i.TotalFeeAmount,
		&i.TotalDueAmount,
		&i.RefundInfo,
		&i.TransactionInfo,
		&i.OutstandingInfo,
		&i.FeeDetails,
		&i.EarlyDischarge,
		&i.DischargeStatus,
		&i.StartDate,
		&i.EndDate,
		&i.CurrDueDate,
		&i.NextDueDate,
		&i.DaysPastDue,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getInstallmentByIDForUpdate = `-- name: GetInstallmentByIDForUpdate :one
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `id` + "`" + ` = ? FOR UPDATE
`

func (q *Queries) GetInstallmentByIDForUpdate(ctx context.Context, id int64) (*Installments, error) {
	row := q.queryRow(ctx, q.getInstallmentByIDForUpdateStmt, getInstallmentByIDForUpdate, id)
	var i Installments
	err := row.Scan(
		&i.ID,
		&i.Tenure,
		&i.Status,
		&i.ZalopayID,
		&i.AccountID,
		&i.ZpTransID,
		&i.PartnerCode,
		&i.PartnerInstID,
		&i.PaidTenure,
		&i.EmiAmount,
		&i.InterestRate,
		&i.InterestAmount,
		&i.DisburseAmount,
		&i.TotalFeeAmount,
		&i.TotalDueAmount,
		&i.RefundInfo,
		&i.TransactionInfo,
		&i.OutstandingInfo,
		&i.FeeDetails,
		&i.EarlyDischarge,
		&i.DischargeStatus,
		&i.StartDate,
		&i.EndDate,
		&i.CurrDueDate,
		&i.NextDueDate,
		&i.DaysPastDue,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getInstallmentByZPTransID = `-- name: GetInstallmentByZPTransID :one
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `zp_trans_id` + "`" + ` = ?
`

func (q *Queries) GetInstallmentByZPTransID(ctx context.Context, zpTransID int64) (*Installments, error) {
	row := q.queryRow(ctx, q.getInstallmentByZPTransIDStmt, getInstallmentByZPTransID, zpTransID)
	var i Installments
	err := row.Scan(
		&i.ID,
		&i.Tenure,
		&i.Status,
		&i.ZalopayID,
		&i.AccountID,
		&i.ZpTransID,
		&i.PartnerCode,
		&i.PartnerInstID,
		&i.PaidTenure,
		&i.EmiAmount,
		&i.InterestRate,
		&i.InterestAmount,
		&i.DisburseAmount,
		&i.TotalFeeAmount,
		&i.TotalDueAmount,
		&i.RefundInfo,
		&i.TransactionInfo,
		&i.OutstandingInfo,
		&i.FeeDetails,
		&i.EarlyDischarge,
		&i.DischargeStatus,
		&i.StartDate,
		&i.EndDate,
		&i.CurrDueDate,
		&i.NextDueDate,
		&i.DaysPastDue,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getInstallmentByZalopayIDAndZPTransID = `-- name: GetInstallmentByZalopayIDAndZPTransID :one
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE zalopay_id = ? AND ` + "`" + `zp_trans_id` + "`" + ` = ?
`

type GetInstallmentByZalopayIDAndZPTransIDParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	ZpTransID int64 `json:"zp_trans_id"`
}

func (q *Queries) GetInstallmentByZalopayIDAndZPTransID(ctx context.Context, arg *GetInstallmentByZalopayIDAndZPTransIDParams) (*Installments, error) {
	row := q.queryRow(ctx, q.getInstallmentByZalopayIDAndZPTransIDStmt, getInstallmentByZalopayIDAndZPTransID, arg.ZalopayID, arg.ZpTransID)
	var i Installments
	err := row.Scan(
		&i.ID,
		&i.Tenure,
		&i.Status,
		&i.ZalopayID,
		&i.AccountID,
		&i.ZpTransID,
		&i.PartnerCode,
		&i.PartnerInstID,
		&i.PaidTenure,
		&i.EmiAmount,
		&i.InterestRate,
		&i.InterestAmount,
		&i.DisburseAmount,
		&i.TotalFeeAmount,
		&i.TotalDueAmount,
		&i.RefundInfo,
		&i.TransactionInfo,
		&i.OutstandingInfo,
		&i.FeeDetails,
		&i.EarlyDischarge,
		&i.DischargeStatus,
		&i.StartDate,
		&i.EndDate,
		&i.CurrDueDate,
		&i.NextDueDate,
		&i.DaysPastDue,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const insertInstallment = `-- name: InsertInstallment :execresult
INSERT INTO installments (
    ` + "`" + `tenure` + "`" + `, ` + "`" + `status` + "`" + `, ` + "`" + `zalopay_id` + "`" + `, ` + "`" + `account_id` + "`" + `,
    ` + "`" + `zp_trans_id` + "`" + `, ` + "`" + `partner_code` + "`" + `, ` + "`" + `partner_inst_id` + "`" + `,
    ` + "`" + `emi_amount` + "`" + `, ` + "`" + `disburse_amount` + "`" + `, ` + "`" + `interest_rate` + "`" + `,
    ` + "`" + `interest_amount` + "`" + `,` + "`" + `total_fee_amount` + "`" + `, ` + "`" + `total_due_amount` + "`" + `,
    ` + "`" + `transaction_info` + "`" + `, ` + "`" + `outstanding_info` + "`" + `, ` + "`" + `fee_details` + "`" + `,
    ` + "`" + `paid_tenure` + "`" + `, ` + "`" + `start_date` + "`" + `, ` + "`" + `end_date` + "`" + `, ` + "`" + `curr_due_date` + "`" + `,
    ` + "`" + `next_due_date` + "`" + `, ` + "`" + `days_past_due` + "`" + `, ` + "`" + `early_discharge` + "`" + `
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type InsertInstallmentParams struct {
	Tenure          int32                         `json:"tenure"`
	Status          InstallmentsStatus            `json:"status"`
	ZalopayID       int64                         `json:"zalopay_id"`
	AccountID       int64                         `json:"account_id"`
	ZpTransID       int64                         `json:"zp_trans_id"`
	PartnerCode     string                        `json:"partner_code"`
	PartnerInstID   sql.NullString                `json:"partner_inst_id"`
	EmiAmount       sql.NullInt64                 `json:"emi_amount"`
	DisburseAmount  sql.NullInt64                 `json:"disburse_amount"`
	InterestRate    sql.NullFloat64               `json:"interest_rate"`
	InterestAmount  sql.NullInt64                 `json:"interest_amount"`
	TotalFeeAmount  sql.NullInt64                 `json:"total_fee_amount"`
	TotalDueAmount  sql.NullInt64                 `json:"total_due_amount"`
	TransactionInfo entity.InstallmentTransaction `json:"transaction_info"`
	OutstandingInfo entity.JsonRawMessage         `json:"outstanding_info"`
	FeeDetails      entity.JsonRawMessage         `json:"fee_details"`
	PaidTenure      sql.NullInt32                 `json:"paid_tenure"`
	StartDate       sql.NullTime                  `json:"start_date"`
	EndDate         sql.NullTime                  `json:"end_date"`
	CurrDueDate     sql.NullTime                  `json:"curr_due_date"`
	NextDueDate     sql.NullTime                  `json:"next_due_date"`
	DaysPastDue     int32                         `json:"days_past_due"`
	EarlyDischarge  entity.JsonRawMessage         `json:"early_discharge"`
}

func (q *Queries) InsertInstallment(ctx context.Context, arg *InsertInstallmentParams) (sql.Result, error) {
	return q.exec(ctx, q.insertInstallmentStmt, insertInstallment,
		arg.Tenure,
		arg.Status,
		arg.ZalopayID,
		arg.AccountID,
		arg.ZpTransID,
		arg.PartnerCode,
		arg.PartnerInstID,
		arg.EmiAmount,
		arg.DisburseAmount,
		arg.InterestRate,
		arg.InterestAmount,
		arg.TotalFeeAmount,
		arg.TotalDueAmount,
		arg.TransactionInfo,
		arg.OutstandingInfo,
		arg.FeeDetails,
		arg.PaidTenure,
		arg.StartDate,
		arg.EndDate,
		arg.CurrDueDate,
		arg.NextDueDate,
		arg.DaysPastDue,
		arg.EarlyDischarge,
	)
}

const listInstallmentByPartnerRefIDs = `-- name: ListInstallmentByPartnerRefIDs :many
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `partner_inst_id` + "`" + ` IS NOT NULL
AND ` + "`" + `partner_inst_id` + "`" + ` IN (/*SLICE:partner_inst_ids*/?)
`

func (q *Queries) ListInstallmentByPartnerRefIDs(ctx context.Context, partnerInstIds []sql.NullString) ([]*Installments, error) {
	query := listInstallmentByPartnerRefIDs
	var queryParams []interface{}
	if len(partnerInstIds) > 0 {
		for _, v := range partnerInstIds {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:partner_inst_ids*/?", strings.Repeat(",?", len(partnerInstIds))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:partner_inst_ids*/?", "NULL", 1)
	}
	rows, err := q.query(ctx, nil, query, queryParams...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Installments
	for rows.Next() {
		var i Installments
		if err := rows.Scan(
			&i.ID,
			&i.Tenure,
			&i.Status,
			&i.ZalopayID,
			&i.AccountID,
			&i.ZpTransID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.PaidTenure,
			&i.EmiAmount,
			&i.InterestRate,
			&i.InterestAmount,
			&i.DisburseAmount,
			&i.TotalFeeAmount,
			&i.TotalDueAmount,
			&i.RefundInfo,
			&i.TransactionInfo,
			&i.OutstandingInfo,
			&i.FeeDetails,
			&i.EarlyDischarge,
			&i.DischargeStatus,
			&i.StartDate,
			&i.EndDate,
			&i.CurrDueDate,
			&i.NextDueDate,
			&i.DaysPastDue,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listInstallmentPeriodicSync = `-- name: ListInstallmentPeriodicSync :many
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `status` + "`" + ` IN ('init', 'open') AND ` + "`" + `created_at` + "`" + ` <= ? LIMIT ? OFFSET ?
`

type ListInstallmentPeriodicSyncParams struct {
	CreatedAt sql.NullTime `json:"created_at"`
	Limit     int32        `json:"limit"`
	Offset    int32        `json:"offset"`
}

func (q *Queries) ListInstallmentPeriodicSync(ctx context.Context, arg *ListInstallmentPeriodicSyncParams) ([]*Installments, error) {
	rows, err := q.query(ctx, q.listInstallmentPeriodicSyncStmt, listInstallmentPeriodicSync, arg.CreatedAt, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Installments
	for rows.Next() {
		var i Installments
		if err := rows.Scan(
			&i.ID,
			&i.Tenure,
			&i.Status,
			&i.ZalopayID,
			&i.AccountID,
			&i.ZpTransID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.PaidTenure,
			&i.EmiAmount,
			&i.InterestRate,
			&i.InterestAmount,
			&i.DisburseAmount,
			&i.TotalFeeAmount,
			&i.TotalDueAmount,
			&i.RefundInfo,
			&i.TransactionInfo,
			&i.OutstandingInfo,
			&i.FeeDetails,
			&i.EarlyDischarge,
			&i.DischargeStatus,
			&i.StartDate,
			&i.EndDate,
			&i.CurrDueDate,
			&i.NextDueDate,
			&i.DaysPastDue,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOpenInstallmentByZalopayIDAndAcctID = `-- name: ListOpenInstallmentByZalopayIDAndAcctID :many
SELECT id, tenure, status, zalopay_id, account_id, zp_trans_id, partner_code, partner_inst_id, paid_tenure, emi_amount, interest_rate, interest_amount, disburse_amount, total_fee_amount, total_due_amount, refund_info, transaction_info, outstanding_info, fee_details, early_discharge, discharge_status, start_date, end_date, curr_due_date, next_due_date, days_past_due, created_at, updated_at FROM installments
WHERE ` + "`" + `zalopay_id` + "`" + ` = ? AND ` + "`" + `account_id` + "`" + ` = ? AND ` + "`" + `status` + "`" + ` = 'open'
`

type ListOpenInstallmentByZalopayIDAndAcctIDParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
}

func (q *Queries) ListOpenInstallmentByZalopayIDAndAcctID(ctx context.Context, arg *ListOpenInstallmentByZalopayIDAndAcctIDParams) ([]*Installments, error) {
	rows, err := q.query(ctx, q.listOpenInstallmentByZalopayIDAndAcctIDStmt, listOpenInstallmentByZalopayIDAndAcctID, arg.ZalopayID, arg.AccountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Installments
	for rows.Next() {
		var i Installments
		if err := rows.Scan(
			&i.ID,
			&i.Tenure,
			&i.Status,
			&i.ZalopayID,
			&i.AccountID,
			&i.ZpTransID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.PaidTenure,
			&i.EmiAmount,
			&i.InterestRate,
			&i.InterestAmount,
			&i.DisburseAmount,
			&i.TotalFeeAmount,
			&i.TotalDueAmount,
			&i.RefundInfo,
			&i.TransactionInfo,
			&i.OutstandingInfo,
			&i.FeeDetails,
			&i.EarlyDischarge,
			&i.DischargeStatus,
			&i.StartDate,
			&i.EndDate,
			&i.CurrDueDate,
			&i.NextDueDate,
			&i.DaysPastDue,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateDischargeStatus = `-- name: UpdateDischargeStatus :exec
UPDATE installments
SET ` + "`" + `discharge_status` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateDischargeStatusParams struct {
	DischargeStatus string `json:"discharge_status"`
	ID              int64  `json:"id"`
}

func (q *Queries) UpdateDischargeStatus(ctx context.Context, arg *UpdateDischargeStatusParams) error {
	_, err := q.exec(ctx, q.updateDischargeStatusStmt, updateDischargeStatus, arg.DischargeStatus, arg.ID)
	return err
}

const updateEarlyDischargeInfo = `-- name: UpdateEarlyDischargeInfo :exec
UPDATE installments
SET early_discharge = ?,
    discharge_status = ?
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateEarlyDischargeInfoParams struct {
	EarlyDischarge  entity.JsonRawMessage `json:"early_discharge"`
	DischargeStatus string                `json:"discharge_status"`
	ID              int64                 `json:"id"`
}

func (q *Queries) UpdateEarlyDischargeInfo(ctx context.Context, arg *UpdateEarlyDischargeInfoParams) error {
	_, err := q.exec(ctx, q.updateEarlyDischargeInfoStmt, updateEarlyDischargeInfo, arg.EarlyDischarge, arg.DischargeStatus, arg.ID)
	return err
}

const updateInstallment = `-- name: UpdateInstallment :exec
UPDATE installments
SET ` + "`" + `status` + "`" + ` = ?, ` + "`" + `paid_tenure` + "`" + ` = ?, ` + "`" + `emi_amount` + "`" + ` = ?, ` + "`" + `interest_rate` + "`" + ` = ?, ` + "`" + `interest_amount` + "`" + ` = ?,
    ` + "`" + `disburse_amount` + "`" + ` = ?, ` + "`" + `total_fee_amount` + "`" + ` = ?, ` + "`" + `total_due_amount` + "`" + ` = ?, ` + "`" + `transaction_info` + "`" + ` = ?,
    ` + "`" + `outstanding_info` + "`" + ` = ?, ` + "`" + `fee_details` + "`" + ` = ?, ` + "`" + `start_date` + "`" + ` = ?, ` + "`" + `end_date` + "`" + ` = ?, ` + "`" + `curr_due_date` + "`" + ` = ?,
    ` + "`" + `next_due_date` + "`" + ` = ?, ` + "`" + `days_past_due` + "`" + ` = ?, ` + "`" + `early_discharge` + "`" + ` = ?, ` + "`" + `discharge_status` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateInstallmentParams struct {
	Status          InstallmentsStatus            `json:"status"`
	PaidTenure      sql.NullInt32                 `json:"paid_tenure"`
	EmiAmount       sql.NullInt64                 `json:"emi_amount"`
	InterestRate    sql.NullFloat64               `json:"interest_rate"`
	InterestAmount  sql.NullInt64                 `json:"interest_amount"`
	DisburseAmount  sql.NullInt64                 `json:"disburse_amount"`
	TotalFeeAmount  sql.NullInt64                 `json:"total_fee_amount"`
	TotalDueAmount  sql.NullInt64                 `json:"total_due_amount"`
	TransactionInfo entity.InstallmentTransaction `json:"transaction_info"`
	OutstandingInfo entity.JsonRawMessage         `json:"outstanding_info"`
	FeeDetails      entity.JsonRawMessage         `json:"fee_details"`
	StartDate       sql.NullTime                  `json:"start_date"`
	EndDate         sql.NullTime                  `json:"end_date"`
	CurrDueDate     sql.NullTime                  `json:"curr_due_date"`
	NextDueDate     sql.NullTime                  `json:"next_due_date"`
	DaysPastDue     int32                         `json:"days_past_due"`
	EarlyDischarge  entity.JsonRawMessage         `json:"early_discharge"`
	DischargeStatus string                        `json:"discharge_status"`
	ID              int64                         `json:"id"`
}

func (q *Queries) UpdateInstallment(ctx context.Context, arg *UpdateInstallmentParams) error {
	_, err := q.exec(ctx, q.updateInstallmentStmt, updateInstallment,
		arg.Status,
		arg.PaidTenure,
		arg.EmiAmount,
		arg.InterestRate,
		arg.InterestAmount,
		arg.DisburseAmount,
		arg.TotalFeeAmount,
		arg.TotalDueAmount,
		arg.TransactionInfo,
		arg.OutstandingInfo,
		arg.FeeDetails,
		arg.StartDate,
		arg.EndDate,
		arg.CurrDueDate,
		arg.NextDueDate,
		arg.DaysPastDue,
		arg.EarlyDischarge,
		arg.DischargeStatus,
		arg.ID,
	)
	return err
}

const updateInstallmentAfterSync = `-- name: UpdateInstallmentAfterSync :exec
UPDATE installments
SET ` + "`" + `status` + "`" + ` = ?, ` + "`" + `paid_tenure` + "`" + ` = ?, ` + "`" + `outstanding_info` + "`" + ` = ?,
    ` + "`" + `start_date` + "`" + ` = ?, ` + "`" + `end_date` + "`" + ` = ?, ` + "`" + `curr_due_date` + "`" + ` = ?,
    ` + "`" + `next_due_date` + "`" + ` = ?, ` + "`" + `days_past_due` + "`" + ` = ?, ` + "`" + `early_discharge` + "`" + ` = ?,
    ` + "`" + `discharge_status` + "`" + ` = ?, ` + "`" + `updated_at` + "`" + ` = CURRENT_TIMESTAMP
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateInstallmentAfterSyncParams struct {
	Status          InstallmentsStatus    `json:"status"`
	PaidTenure      sql.NullInt32         `json:"paid_tenure"`
	OutstandingInfo entity.JsonRawMessage `json:"outstanding_info"`
	StartDate       sql.NullTime          `json:"start_date"`
	EndDate         sql.NullTime          `json:"end_date"`
	CurrDueDate     sql.NullTime          `json:"curr_due_date"`
	NextDueDate     sql.NullTime          `json:"next_due_date"`
	DaysPastDue     int32                 `json:"days_past_due"`
	EarlyDischarge  entity.JsonRawMessage `json:"early_discharge"`
	DischargeStatus string                `json:"discharge_status"`
	ID              int64                 `json:"id"`
}

// Force updated_at to NOW() because we want to track the last sync time instead data not changed
func (q *Queries) UpdateInstallmentAfterSync(ctx context.Context, arg *UpdateInstallmentAfterSyncParams) error {
	_, err := q.exec(ctx, q.updateInstallmentAfterSyncStmt, updateInstallmentAfterSync,
		arg.Status,
		arg.PaidTenure,
		arg.OutstandingInfo,
		arg.StartDate,
		arg.EndDate,
		arg.CurrDueDate,
		arg.NextDueDate,
		arg.DaysPastDue,
		arg.EarlyDischarge,
		arg.DischargeStatus,
		arg.ID,
	)
	return err
}

const updateInstallmentRefund = `-- name: UpdateInstallmentRefund :exec
UPDATE installments
SET ` + "`" + `refund_info` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateInstallmentRefundParams struct {
	RefundInfo *entity.InstallmentRefund `json:"refund_info"`
	ID         int64                     `json:"id"`
}

func (q *Queries) UpdateInstallmentRefund(ctx context.Context, arg *UpdateInstallmentRefundParams) error {
	_, err := q.exec(ctx, q.updateInstallmentRefundStmt, updateInstallmentRefund, arg.RefundInfo, arg.ID)
	return err
}

const updateInstallmentTransaction = `-- name: UpdateInstallmentTransaction :exec
UPDATE installments
SET ` + "`" + `transaction_info` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ?
`

type UpdateInstallmentTransactionParams struct {
	TransactionInfo entity.InstallmentTransaction `json:"transaction_info"`
	ID              int64                         `json:"id"`
}

func (q *Queries) UpdateInstallmentTransaction(ctx context.Context, arg *UpdateInstallmentTransactionParams) error {
	_, err := q.exec(ctx, q.updateInstallmentTransactionStmt, updateInstallmentTransaction, arg.TransactionInfo, arg.ID)
	return err
}
