// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: statements_installments.sql

package da

import (
	"context"
	"database/sql"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
)

const listStmtInstallmentByClientAndStmtDate = `-- name: ListStmtInstallmentByClientAndStmtDate :many
SELECT id, statement_id, statement_date, zalopay_id, account_id, partner_code, partner_inst_id, due_date, installment_amount, outstanding_details, created_at, updated_at FROM statements_installments
WHERE ` + "`" + `zalopay_id` + "`" + ` = ? AND ` + "`" + `account_id` + "`" + ` = ? AND ` + "`" + `statement_date` + "`" + ` = ?
`

type ListStmtInstallmentByClientAndStmtDateParams struct {
	ZalopayID     int64     `json:"zalopay_id"`
	AccountID     int64     `json:"account_id"`
	StatementDate time.Time `json:"statement_date"`
}

func (q *Queries) ListStmtInstallmentByClientAndStmtDate(ctx context.Context, arg *ListStmtInstallmentByClientAndStmtDateParams) ([]*StatementsInstallments, error) {
	rows, err := q.query(ctx, q.listStmtInstallmentByClientAndStmtDateStmt, listStmtInstallmentByClientAndStmtDate, arg.ZalopayID, arg.AccountID, arg.StatementDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*StatementsInstallments
	for rows.Next() {
		var i StatementsInstallments
		if err := rows.Scan(
			&i.ID,
			&i.StatementID,
			&i.StatementDate,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.DueDate,
			&i.InstallmentAmount,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listStmtInstallmentByStatementId = `-- name: ListStmtInstallmentByStatementId :many
SELECT id, statement_id, statement_date, zalopay_id, account_id, partner_code, partner_inst_id, due_date, installment_amount, outstanding_details, created_at, updated_at FROM statements_installments
WHERE ` + "`" + `statement_id` + "`" + ` = ?
`

func (q *Queries) ListStmtInstallmentByStatementId(ctx context.Context, statementID int64) ([]*StatementsInstallments, error) {
	rows, err := q.query(ctx, q.listStmtInstallmentByStatementIdStmt, listStmtInstallmentByStatementId, statementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*StatementsInstallments
	for rows.Next() {
		var i StatementsInstallments
		if err := rows.Scan(
			&i.ID,
			&i.StatementID,
			&i.StatementDate,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.DueDate,
			&i.InstallmentAmount,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listStmtInstallmentByStmtId = `-- name: ListStmtInstallmentByStmtId :many
SELECT id, statement_id, statement_date, zalopay_id, account_id, partner_code, partner_inst_id, due_date, installment_amount, outstanding_details, created_at, updated_at FROM statements_installments
WHERE ` + "`" + `statement_id` + "`" + ` = ?
`

func (q *Queries) ListStmtInstallmentByStmtId(ctx context.Context, statementID int64) ([]*StatementsInstallments, error) {
	rows, err := q.query(ctx, q.listStmtInstallmentByStmtIdStmt, listStmtInstallmentByStmtId, statementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*StatementsInstallments
	for rows.Next() {
		var i StatementsInstallments
		if err := rows.Scan(
			&i.ID,
			&i.StatementID,
			&i.StatementDate,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.DueDate,
			&i.InstallmentAmount,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listStmtInstallmentByUserAndStmtId = `-- name: ListStmtInstallmentByUserAndStmtId :many
SELECT id, statement_id, statement_date, zalopay_id, account_id, partner_code, partner_inst_id, due_date, installment_amount, outstanding_details, created_at, updated_at FROM statements_installments
WHERE ` + "`" + `zalopay_id` + "`" + ` = ? AND ` + "`" + `account_id` + "`" + ` = ? AND ` + "`" + `statement_id` + "`" + ` = ?
`

type ListStmtInstallmentByUserAndStmtIdParams struct {
	ZalopayID   int64 `json:"zalopay_id"`
	AccountID   int64 `json:"account_id"`
	StatementID int64 `json:"statement_id"`
}

func (q *Queries) ListStmtInstallmentByUserAndStmtId(ctx context.Context, arg *ListStmtInstallmentByUserAndStmtIdParams) ([]*StatementsInstallments, error) {
	rows, err := q.query(ctx, q.listStmtInstallmentByUserAndStmtIdStmt, listStmtInstallmentByUserAndStmtId, arg.ZalopayID, arg.AccountID, arg.StatementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*StatementsInstallments
	for rows.Next() {
		var i StatementsInstallments
		if err := rows.Scan(
			&i.ID,
			&i.StatementID,
			&i.StatementDate,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.PartnerInstID,
			&i.DueDate,
			&i.InstallmentAmount,
			&i.OutstandingDetails,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const upsertStmtInstallment = `-- name: UpsertStmtInstallment :exec
/**
* Note: Currently we still store some field like zalopay_id, account_id, partner_code in this table
* to make it easier to query and ensure the data is belong to user account.
* Maybe we must take care of the consistency and normalization in the future.
*/

INSERT INTO statements_installments (
    ` + "`" + `statement_id` + "`" + `, ` + "`" + `statement_date` + "`" + `, ` + "`" + `partner_inst_id` + "`" + `,
    ` + "`" + `zalopay_id` + "`" + `, ` + "`" + `account_id` + "`" + `, ` + "`" + `partner_code` + "`" + `,
    ` + "`" + `due_date` + "`" + `, ` + "`" + `installment_amount` + "`" + `, ` + "`" + `outstanding_details` + "`" + `
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    ` + "`" + `due_date` + "`" + ` = VALUES(due_date),
    ` + "`" + `installment_amount` + "`" + ` = VALUES(installment_amount),
    ` + "`" + `outstanding_details` + "`" + ` = VALUES(outstanding_details)
`

type UpsertStmtInstallmentParams struct {
	StatementID        int64                           `json:"statement_id"`
	StatementDate      time.Time                       `json:"statement_date"`
	PartnerInstID      string                          `json:"partner_inst_id"`
	ZalopayID          int64                           `json:"zalopay_id"`
	AccountID          int64                           `json:"account_id"`
	PartnerCode        string                          `json:"partner_code"`
	DueDate            sql.NullTime                    `json:"due_date"`
	InstallmentAmount  int64                           `json:"installment_amount"`
	OutstandingDetails entity.StatementInstallmentOuts `json:"outstanding_details"`
}

func (q *Queries) UpsertStmtInstallment(ctx context.Context, arg *UpsertStmtInstallmentParams) error {
	_, err := q.exec(ctx, q.upsertStmtInstallmentStmt, upsertStmtInstallment,
		arg.StatementID,
		arg.StatementDate,
		arg.PartnerInstID,
		arg.ZalopayID,
		arg.AccountID,
		arg.PartnerCode,
		arg.DueDate,
		arg.InstallmentAmount,
		arg.OutstandingDetails,
	)
	return err
}
