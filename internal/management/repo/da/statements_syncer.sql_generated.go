// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: statements_syncer.sql

package da

import (
	"context"
	"database/sql"
	"encoding/json"
)

const getSyncerBatchByPeriodAndPartner = `-- name: GetSyncerBatchByPeriodAndPartner :many
SELECT id, statement_date, statement_period, partner_code, batch_no, status, exec_id, sync_stats, created_at, updated_at FROM statements_syncer
WHERE ` + "`" + `batch_no` + "`" + ` IS NOT NULL
  AND ` + "`" + `partner_code` + "`" + ` = ?
  AND ` + "`" + `statement_period` + "`" + ` = ?
  AND ` + "`" + `statement_date` + "`" + ` = ?
`

type GetSyncerBatchByPeriodAndPartnerParams struct {
	PartnerCode     string       `json:"partner_code"`
	StatementPeriod int32        `json:"statement_period"`
	StatementDate   sql.NullTime `json:"statement_date"`
}

func (q *Queries) GetSyncerBatchByPeriodAndPartner(ctx context.Context, arg *GetSyncerBatchByPeriodAndPartnerParams) ([]*StatementsSyncer, error) {
	rows, err := q.query(ctx, q.getSyncerBatchByPeriodAndPartnerStmt, getSyncerBatchByPeriodAndPartner, arg.PartnerCode, arg.StatementPeriod, arg.StatementDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*StatementsSyncer
	for rows.Next() {
		var i StatementsSyncer
		if err := rows.Scan(
			&i.ID,
			&i.StatementDate,
			&i.StatementPeriod,
			&i.PartnerCode,
			&i.BatchNo,
			&i.Status,
			&i.ExecID,
			&i.SyncStats,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const insertSyncer = `-- name: InsertSyncer :execresult
INSERT INTO statements_syncer (
    ` + "`" + `statement_period` + "`" + `, ` + "`" + `statement_date` + "`" + `, ` + "`" + `partner_code` + "`" + `, ` + "`" + `batch_no` + "`" + `, ` + "`" + `status` + "`" + `, ` + "`" + `exec_id` + "`" + `, ` + "`" + `sync_stats` + "`" + `
) VALUES (?, ?, ?, ?, ?, ?, ?)
`

type InsertSyncerParams struct {
	StatementPeriod int32                  `json:"statement_period"`
	StatementDate   sql.NullTime           `json:"statement_date"`
	PartnerCode     string                 `json:"partner_code"`
	BatchNo         sql.NullInt32          `json:"batch_no"`
	Status          StatementsSyncerStatus `json:"status"`
	ExecID          string                 `json:"exec_id"`
	SyncStats       json.RawMessage        `json:"sync_stats"`
}

func (q *Queries) InsertSyncer(ctx context.Context, arg *InsertSyncerParams) (sql.Result, error) {
	return q.exec(ctx, q.insertSyncerStmt, insertSyncer,
		arg.StatementPeriod,
		arg.StatementDate,
		arg.PartnerCode,
		arg.BatchNo,
		arg.Status,
		arg.ExecID,
		arg.SyncStats,
	)
}

const updateSyncerStatus = `-- name: UpdateSyncerStatus :exec
UPDATE statements_syncer SET
     ` + "`" + `status` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ? OR ` + "`" + `exec_id` + "`" + ` = ?
`

type UpdateSyncerStatusParams struct {
	Status StatementsSyncerStatus `json:"status"`
	ID     int64                  `json:"id"`
	ExecID string                 `json:"exec_id"`
}

func (q *Queries) UpdateSyncerStatus(ctx context.Context, arg *UpdateSyncerStatusParams) error {
	_, err := q.exec(ctx, q.updateSyncerStatusStmt, updateSyncerStatus, arg.Status, arg.ID, arg.ExecID)
	return err
}

const updateSyncerStatusAndStats = `-- name: UpdateSyncerStatusAndStats :exec
UPDATE statements_syncer SET
    ` + "`" + `status` + "`" + ` = ?,
    ` + "`" + `sync_stats` + "`" + ` = ?
WHERE ` + "`" + `id` + "`" + ` = ? OR ` + "`" + `exec_id` + "`" + ` = ?
`

type UpdateSyncerStatusAndStatsParams struct {
	Status    StatementsSyncerStatus `json:"status"`
	SyncStats json.RawMessage        `json:"sync_stats"`
	ID        int64                  `json:"id"`
	ExecID    string                 `json:"exec_id"`
}

func (q *Queries) UpdateSyncerStatusAndStats(ctx context.Context, arg *UpdateSyncerStatusAndStatsParams) error {
	_, err := q.exec(ctx, q.updateSyncerStatusAndStatsStmt, updateSyncerStatusAndStats,
		arg.Status,
		arg.SyncStats,
		arg.ID,
		arg.ExecID,
	)
	return err
}

const upsertSyncer = `-- name: UpsertSyncer :execresult
INSERT INTO statements_syncer (
   ` + "`" + `statement_period` + "`" + `, ` + "`" + `statement_date` + "`" + `, ` + "`" + `partner_code` + "`" + `, batch_no, ` + "`" + `status` + "`" + `, ` + "`" + `exec_id` + "`" + `, ` + "`" + `sync_stats` + "`" + `
) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    ` + "`" + `status` + "`" + ` = VALUES(` + "`" + `status` + "`" + `),
    ` + "`" + `sync_stats` + "`" + ` = VALUES(` + "`" + `sync_stats` + "`" + `)
`

type UpsertSyncerParams struct {
	StatementPeriod int32                  `json:"statement_period"`
	StatementDate   sql.NullTime           `json:"statement_date"`
	PartnerCode     string                 `json:"partner_code"`
	BatchNo         sql.NullInt32          `json:"batch_no"`
	Status          StatementsSyncerStatus `json:"status"`
	ExecID          string                 `json:"exec_id"`
	SyncStats       json.RawMessage        `json:"sync_stats"`
}

func (q *Queries) UpsertSyncer(ctx context.Context, arg *UpsertSyncerParams) (sql.Result, error) {
	return q.exec(ctx, q.upsertSyncerStmt, upsertSyncer,
		arg.StatementPeriod,
		arg.StatementDate,
		arg.PartnerCode,
		arg.BatchNo,
		arg.Status,
		arg.ExecID,
		arg.SyncStats,
	)
}
