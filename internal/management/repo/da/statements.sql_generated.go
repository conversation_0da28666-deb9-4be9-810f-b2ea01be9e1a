// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: statements.sql

package da

import (
	"context"
	"database/sql"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/repo/entity"
)

const getStatementByID = `-- name: GetStatementByID :one
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE id = ?
`

func (q *Queries) GetStatementByID(ctx context.Context, id int64) (*Statements, error) {
	row := q.queryRow(ctx, q.getStatementByIDStmt, getStatementByID, id)
	var i Statements
	err := row.Scan(
		&i.ID,
		&i.Period,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.DueDate,
		&i.IncurredDate,
		&i.<PERSON>,
		&i.OutstandingAmount,
		&i.OutstandingRepaid,
		&i.OutstandingBalance,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getStatementDueDateByIncurredDateAndPartner = `-- name: GetStatementDueDateByIncurredDateAndPartner :one
SELECT due_date FROM statements
WHERE partner_code = ? AND incurred_date = ?
`

type GetStatementDueDateByIncurredDateAndPartnerParams struct {
	PartnerCode  string    `json:"partner_code"`
	IncurredDate time.Time `json:"incurred_date"`
}

func (q *Queries) GetStatementDueDateByIncurredDateAndPartner(ctx context.Context, arg *GetStatementDueDateByIncurredDateAndPartnerParams) (time.Time, error) {
	row := q.queryRow(ctx, q.getStatementDueDateByIncurredDateAndPartnerStmt, getStatementDueDateByIncurredDateAndPartner, arg.PartnerCode, arg.IncurredDate)
	var due_date time.Time
	err := row.Scan(&due_date)
	return due_date, err
}

const getUserLatestStatement = `-- name: GetUserLatestStatement :one
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
ORDER BY period DESC LIMIT 1
`

type GetUserLatestStatementParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
}

func (q *Queries) GetUserLatestStatement(ctx context.Context, arg *GetUserLatestStatementParams) (*Statements, error) {
	row := q.queryRow(ctx, q.getUserLatestStatementStmt, getUserLatestStatement, arg.ZalopayID, arg.AccountID)
	var i Statements
	err := row.Scan(
		&i.ID,
		&i.Period,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.DueDate,
		&i.IncurredDate,
		&i.PenaltyAmount,
		&i.OutstandingAmount,
		&i.OutstandingRepaid,
		&i.OutstandingBalance,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getUserLatestStatementByPeriod = `-- name: GetUserLatestStatementByPeriod :one
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?
ORDER BY period DESC LIMIT 1
`

type GetUserLatestStatementByPeriodParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
	Period    int32 `json:"period"`
}

func (q *Queries) GetUserLatestStatementByPeriod(ctx context.Context, arg *GetUserLatestStatementByPeriodParams) (*Statements, error) {
	row := q.queryRow(ctx, q.getUserLatestStatementByPeriodStmt, getUserLatestStatementByPeriod, arg.ZalopayID, arg.AccountID, arg.Period)
	var i Statements
	err := row.Scan(
		&i.ID,
		&i.Period,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.DueDate,
		&i.IncurredDate,
		&i.PenaltyAmount,
		&i.OutstandingAmount,
		&i.OutstandingRepaid,
		&i.OutstandingBalance,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getUserStatementByIncurredDate = `-- name: GetUserStatementByIncurredDate :one
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date = DATE(?)
`

type GetUserStatementByIncurredDateParams struct {
	ZalopayID    int64     `json:"zalopay_id"`
	AccountID    int64     `json:"account_id"`
	IncurredDate time.Time `json:"incurred_date"`
}

func (q *Queries) GetUserStatementByIncurredDate(ctx context.Context, arg *GetUserStatementByIncurredDateParams) (*Statements, error) {
	row := q.queryRow(ctx, q.getUserStatementByIncurredDateStmt, getUserStatementByIncurredDate, arg.ZalopayID, arg.AccountID, arg.IncurredDate)
	var i Statements
	err := row.Scan(
		&i.ID,
		&i.Period,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.DueDate,
		&i.IncurredDate,
		&i.PenaltyAmount,
		&i.OutstandingAmount,
		&i.OutstandingRepaid,
		&i.OutstandingBalance,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getUserStatementByPeriodAndIncurredDate = `-- name: GetUserStatementByPeriodAndIncurredDate :one
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?
  AND incurred_date = ?
`

type GetUserStatementByPeriodAndIncurredDateParams struct {
	ZalopayID    int64     `json:"zalopay_id"`
	AccountID    int64     `json:"account_id"`
	Period       int32     `json:"period"`
	IncurredDate time.Time `json:"incurred_date"`
}

func (q *Queries) GetUserStatementByPeriodAndIncurredDate(ctx context.Context, arg *GetUserStatementByPeriodAndIncurredDateParams) (*Statements, error) {
	row := q.queryRow(ctx, q.getUserStatementByPeriodAndIncurredDateStmt, getUserStatementByPeriodAndIncurredDate,
		arg.ZalopayID,
		arg.AccountID,
		arg.Period,
		arg.IncurredDate,
	)
	var i Statements
	err := row.Scan(
		&i.ID,
		&i.Period,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.DueDate,
		&i.IncurredDate,
		&i.PenaltyAmount,
		&i.OutstandingAmount,
		&i.OutstandingRepaid,
		&i.OutstandingBalance,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const listOutstandingStatementByIncurredDateAndPartner = `-- name: ListOutstandingStatementByIncurredDateAndPartner :many
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE partner_code = ?
  AND incurred_date = DATE(?)
  AND outstanding_balance > 0
  AND id > ? LIMIT ?
`

type ListOutstandingStatementByIncurredDateAndPartnerParams struct {
	PartnerCode  string    `json:"partner_code"`
	IncurredDate time.Time `json:"incurred_date"`
	ID           int64     `json:"id"`
	Limit        int32     `json:"limit"`
}

func (q *Queries) ListOutstandingStatementByIncurredDateAndPartner(ctx context.Context, arg *ListOutstandingStatementByIncurredDateAndPartnerParams) ([]*Statements, error) {
	rows, err := q.query(ctx, q.listOutstandingStatementByIncurredDateAndPartnerStmt, listOutstandingStatementByIncurredDateAndPartner,
		arg.PartnerCode,
		arg.IncurredDate,
		arg.ID,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Statements
	for rows.Next() {
		var i Statements
		if err := rows.Scan(
			&i.ID,
			&i.Period,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.DueDate,
			&i.IncurredDate,
			&i.PenaltyAmount,
			&i.OutstandingAmount,
			&i.OutstandingRepaid,
			&i.OutstandingBalance,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listStatementByIncurredDateAndPartner = `-- name: ListStatementByIncurredDateAndPartner :many
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE partner_code = ?
  AND incurred_date = DATE(?)
  AND id > ? LIMIT ?
`

type ListStatementByIncurredDateAndPartnerParams struct {
	PartnerCode  string    `json:"partner_code"`
	IncurredDate time.Time `json:"incurred_date"`
	ID           int64     `json:"id"`
	Limit        int32     `json:"limit"`
}

func (q *Queries) ListStatementByIncurredDateAndPartner(ctx context.Context, arg *ListStatementByIncurredDateAndPartnerParams) ([]*Statements, error) {
	rows, err := q.query(ctx, q.listStatementByIncurredDateAndPartnerStmt, listStatementByIncurredDateAndPartner,
		arg.PartnerCode,
		arg.IncurredDate,
		arg.ID,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Statements
	for rows.Next() {
		var i Statements
		if err := rows.Scan(
			&i.ID,
			&i.Period,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.DueDate,
			&i.IncurredDate,
			&i.PenaltyAmount,
			&i.OutstandingAmount,
			&i.OutstandingRepaid,
			&i.OutstandingBalance,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserStatementByIncurredDateRange = `-- name: ListUserStatementByIncurredDateRange :many
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date >= DATE(?)
  AND incurred_date <= DATE(?)
`

type ListUserStatementByIncurredDateRangeParams struct {
	ZalopayID        int64     `json:"zalopay_id"`
	AccountID        int64     `json:"account_id"`
	FromIncurredDate time.Time `json:"from_incurred_date"`
	ToIncurredDate   time.Time `json:"to_incurred_date"`
}

func (q *Queries) ListUserStatementByIncurredDateRange(ctx context.Context, arg *ListUserStatementByIncurredDateRangeParams) ([]*Statements, error) {
	rows, err := q.query(ctx, q.listUserStatementByIncurredDateRangeStmt, listUserStatementByIncurredDateRange,
		arg.ZalopayID,
		arg.AccountID,
		arg.FromIncurredDate,
		arg.ToIncurredDate,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Statements
	for rows.Next() {
		var i Statements
		if err := rows.Scan(
			&i.ID,
			&i.Period,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.DueDate,
			&i.IncurredDate,
			&i.PenaltyAmount,
			&i.OutstandingAmount,
			&i.OutstandingRepaid,
			&i.OutstandingBalance,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUserStatementByPeriod = `-- name: ListUserStatementByPeriod :many
SELECT id, period, zalopay_id, account_id, partner_code, due_date, incurred_date, penalty_amount, outstanding_amount, outstanding_repaid, outstanding_balance, metadata, created_at, updated_at FROM statements
WHERE zalopay_id = ?
  AND account_id = ?
  AND period = ?
`

type ListUserStatementByPeriodParams struct {
	ZalopayID int64 `json:"zalopay_id"`
	AccountID int64 `json:"account_id"`
	Period    int32 `json:"period"`
}

func (q *Queries) ListUserStatementByPeriod(ctx context.Context, arg *ListUserStatementByPeriodParams) ([]*Statements, error) {
	rows, err := q.query(ctx, q.listUserStatementByPeriodStmt, listUserStatementByPeriod, arg.ZalopayID, arg.AccountID, arg.Period)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Statements
	for rows.Next() {
		var i Statements
		if err := rows.Scan(
			&i.ID,
			&i.Period,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.DueDate,
			&i.IncurredDate,
			&i.PenaltyAmount,
			&i.OutstandingAmount,
			&i.OutstandingRepaid,
			&i.OutstandingBalance,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateStatementOutstandingAndPenaltyByID = `-- name: UpdateStatementOutstandingAndPenaltyByID :exec
UPDATE statements
set penalty_amount = ?,
    outstanding_amount = ?,
    outstanding_repaid = ?,
    metadata = ?
WHERE id = ?
`

type UpdateStatementOutstandingAndPenaltyByIDParams struct {
	PenaltyAmount     int64                    `json:"penalty_amount"`
	OutstandingAmount int64                    `json:"outstanding_amount"`
	OutstandingRepaid int64                    `json:"outstanding_repaid"`
	Metadata          entity.StatementMetadata `json:"metadata"`
	ID                int64                    `json:"id"`
}

func (q *Queries) UpdateStatementOutstandingAndPenaltyByID(ctx context.Context, arg *UpdateStatementOutstandingAndPenaltyByIDParams) error {
	_, err := q.exec(ctx, q.updateStatementOutstandingAndPenaltyByIDStmt, updateStatementOutstandingAndPenaltyByID,
		arg.PenaltyAmount,
		arg.OutstandingAmount,
		arg.OutstandingRepaid,
		arg.Metadata,
		arg.ID,
	)
	return err
}

const updateStatementOutstandingByIncurredDate = `-- name: UpdateStatementOutstandingByIncurredDate :exec
UPDATE statements
SET outstanding_amount = ?,
    outstanding_repaid = ?
WHERE zalopay_id = ?
  AND account_id = ?
  AND incurred_date = DATE(?)
`

type UpdateStatementOutstandingByIncurredDateParams struct {
	OutstandingAmount int64     `json:"outstanding_amount"`
	OutstandingRepaid int64     `json:"outstanding_repaid"`
	ZalopayID         int64     `json:"zalopay_id"`
	AccountID         int64     `json:"account_id"`
	IncurredDate      time.Time `json:"incurred_date"`
}

func (q *Queries) UpdateStatementOutstandingByIncurredDate(ctx context.Context, arg *UpdateStatementOutstandingByIncurredDateParams) error {
	_, err := q.exec(ctx, q.updateStatementOutstandingByIncurredDateStmt, updateStatementOutstandingByIncurredDate,
		arg.OutstandingAmount,
		arg.OutstandingRepaid,
		arg.ZalopayID,
		arg.AccountID,
		arg.IncurredDate,
	)
	return err
}

const upsertStatement = `-- name: UpsertStatement :execresult
INSERT INTO statements (
    period, zalopay_id, account_id,
    partner_code, due_date, incurred_date,
    outstanding_amount, outstanding_repaid
) VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE
    id = LAST_INSERT_ID(id),
    outstanding_amount = VALUES(outstanding_amount),
    outstanding_repaid = VALUES(outstanding_repaid)
`

type UpsertStatementParams struct {
	Period            int32     `json:"period"`
	ZalopayID         int64     `json:"zalopay_id"`
	AccountID         int64     `json:"account_id"`
	PartnerCode       string    `json:"partner_code"`
	DueDate           time.Time `json:"due_date"`
	IncurredDate      time.Time `json:"incurred_date"`
	OutstandingAmount int64     `json:"outstanding_amount"`
	OutstandingRepaid int64     `json:"outstanding_repaid"`
}

func (q *Queries) UpsertStatement(ctx context.Context, arg *UpsertStatementParams) (sql.Result, error) {
	return q.exec(ctx, q.upsertStatementStmt, upsertStatement,
		arg.Period,
		arg.ZalopayID,
		arg.AccountID,
		arg.PartnerCode,
		arg.DueDate,
		arg.IncurredDate,
		arg.OutstandingAmount,
		arg.OutstandingRepaid,
	)
}
