package service

import (
	"context"
	"errors"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *InstallmentService) CreateInstallmentWorkflow(ctx workflow.Context,
	workflowData *dto.InstallmentCreateParams) (*model.InstallmentInfo, error) {
	logger := workflow.GetLogger(ctx)

	if err := workflow.Sleep(ctx, time.Second*5); err != nil {
		logger.Error("Error sleep")
		return nil, err
	}

	logger.Info("Start process create installment workflow", "data", workflowData)

	actOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 24,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 5,
			MaximumInterval:    time.Minute * 5,
			BackoffCoefficient: 2.0,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, actOpts)

	var result *model.InstallmentInfo
	err := workflow.ExecuteActivity(ctx, s.CreateInstallmentActivity, workflowData).Get(ctx, &result)
	if err != nil {
		logger.Error("Error create installment", "error", err)
		return nil, err
	}

	logger.Info("End process create installment workflow", "result", result)

	return result, nil
}

func (s *InstallmentService) SyncInstallmentsDailyWorkflow(ctx workflow.Context) (*dto.InstallmentSyncStats, error) {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync installment periodic workflow")

	syncActOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 12,
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts:    5,
			InitialInterval:    time.Minute * 1,
			MaximumInterval:    time.Minute * 10,
			BackoffCoefficient: 2.0,
		},
	}
	syncCtx := workflow.WithActivityOptions(ctx, syncActOpts)
	var syncStats *dto.InstallmentSyncStats
	if err := workflow.ExecuteActivity(syncCtx, s.SyncInstallmentsDailyActivity).Get(syncCtx, &syncStats); err != nil {
		logger.Error("Error create installment", "error", err)
		return nil, err
	}

	logger.Info("End process sync installment periodic workflow")

	return syncStats, nil
}

func (s *InstallmentService) CreateInstallmentActivity(ctx context.Context,
	workflowData *dto.InstallmentCreateParams) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	// Create installment
	installment, err := s.instUc.CreateInstallment(ctx, workflowData)
	if err != nil {
		logger.Errorf("failed to create installment: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to create installment", "", err)
	}

	return installment, nil
}

func (s *InstallmentService) SyncInstallmentsDailyActivity(ctx context.Context) (*dto.InstallmentSyncStats, error) {
	logger := s.logger.WithContext(ctx)

	// Sync installment
	newCtx := context.WithoutCancel(ctx)
	syncStats, err := s.instUc.SyncInstallmentDaily(newCtx)
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodeResourceLockedForProcessing)) {
		logger.Warn("sync installment daily task is already running")
		return nil, temporal.NewNonRetryableApplicationError("sync installment daily task is already running", "", err)
	}
	if err != nil {
		logger.Errorf("failed to sync installment periodic: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync installment", "", err)
	}

	return syncStats, nil
}

func (s *InstallmentService) SyncInstallmentInfoWorkflow(ctx workflow.Context,
	workflowData *dto.SyncInstallmentParams) (*model.InstallmentInfo, error) {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync installment workflow", "data", workflowData)

	actOpts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute * 5,
		ScheduleToCloseTimeout: time.Minute * 15,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 15,
			MaximumInterval:    time.Minute,
			BackoffCoefficient: 2.0,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, actOpts)

	var result *model.InstallmentInfo
	err := workflow.ExecuteActivity(ctx, s.SyncInstallmentInfoActivity, workflowData).Get(ctx, &result)
	if err != nil {
		logger.Error("Error sync installment", "error", err)
		return nil, err
	}

	logger.Info("End process sync installment workflow", "result", result)

	return result, nil
}

func (s *InstallmentService) SyncInstallmentInfoActivity(ctx context.Context,
	workflowData *dto.SyncInstallmentParams) (*model.InstallmentInfo, error) {
	logger := s.logger.WithContext(ctx)

	// Sync installment
	inst, err := s.instUc.GetInstallmentByZPTransID(ctx, workflowData.ZPTransID)
	if err != nil {
		logger.Errorf("failed to get installment by ZPTransID: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get installment by ZPTransID", "", err)
	}

	inst, err = s.instUc.SyncInstallmentData(ctx, inst)
	if err != nil {
		logger.Errorf("failed to sync installment: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync installment", "", err)
	}

	return inst, nil
}
