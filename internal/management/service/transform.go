package service

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"

	v1Inst "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	v1Stmt "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/statement/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkts"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func convertToListPlanOptions(plans []*model.Plan) []*v1Inst.ListClientEligiblePlansResponse_Plan {
	result := make([]*v1Inst.ListClientEligiblePlansResponse_Plan, 0, len(plans))

	for _, plan := range plans {
		result = append(result, &v1Inst.ListClientEligiblePlansResponse_Plan{
			PlanKey:             plan.PlanKey,
			PlanInfo:            convertToPlanInfo(plan),
			CostInfo:            convertToCostInfo(plan),
			IsPopular:           plan.IsPlanPopular(),
			PlanDetailUrl:       plan.GetPlanDetailUrl().ZPAUrl,
			ScheduledRepayments: convertToPlanRepaySchedules(plan.GetScheduledRepayments()),
		})
	}
	return result
}

func convertToPlanRepaySchedules(repayments []*model.PlanRepayment) []*v1Inst.PlanRepaymentSchedule {
	result := make([]*v1Inst.PlanRepaymentSchedule, 0, len(repayments))

	for _, repayment := range repayments {
		result = append(result, &v1Inst.PlanRepaymentSchedule{
			Amount:            repayment.Amount,
			DueDate:           repayment.DueDate,
			InstallmentNumber: repayment.InstNum,
		})
	}
	return result
}

func convertToPlanInfo(plan *model.Plan) *v1Inst.BasePlan {
	return &v1Inst.BasePlan{
		PlanKey:         &plan.PlanKey,
		TenorNumber:     plan.Tenure,
		EmiAmount:       plan.PlanInfo.EmiAmount,
		TotalAmount:     plan.PlanInfo.TotalAmount,
		PrincipalAmount: plan.PlanInfo.PrincipalAmount,
		Status:          convertToPlanStatus(plan.Status),
	}
}

func convertToPlanStatus(status model.PlanStatus) v1Inst.PlanStatus {
	planMap := map[model.PlanStatus]v1Inst.PlanStatus{
		model.PlanStatusActive:   v1Inst.PlanStatus_PLAN_STATUS_ACTIVE,
		model.PlanStatusInactive: v1Inst.PlanStatus_PLAN_STATUS_INACTIVE,
		model.PlanStatusInvalid:  v1Inst.PlanStatus_PLAN_STATUS_INVALID,
		model.PlanStatusUnknown:  v1Inst.PlanStatus_PLAN_STATUS_UNSPECIFIED,
	}
	value, ok := planMap[status]
	if !ok {
		return v1Inst.PlanStatus_PLAN_STATUS_UNSPECIFIED
	}
	return value
}

func convertToCostInfo(plan *model.Plan) *v1Inst.CostInfo {
	return &v1Inst.CostInfo{
		TotalCostAmount:     plan.CostInfo.TotalCostAmount,
		TotalFeeAmount:      plan.CostInfo.TotalFeeAmount,
		InterestAmount:      plan.CostInfo.InterestAmount,
		PlatformFeeAmount:   plan.CostInfo.PlatformFeeAmount,
		ConversionFeeAmount: plan.CostInfo.ConversionFeeAmount,
		FeeExplanation:      plan.CostInfo.GetCostExplanation(plan.Tenure),
		ListFeeInfo:         convertToFeeList(plan.CostInfo.ListFeeDetails),
	}
}

func convertToFeeList(fees []model.FeeDetail) []*v1Inst.Fee {
	result := make([]*v1Inst.Fee, 0, len(fees))
	for _, fee := range fees {
		result = append(result, &v1Inst.Fee{
			FeeType:   convertToFeeType(fee.Type),
			FeeAmount: cast.ToInt64(fee.Amount),
			Message:   fee.Explain,
		})
	}
	return result
}

func convertToFeeType(fee model.FeeType) v1Inst.FeeType {
	mapping := map[model.FeeType]v1Inst.FeeType{
		model.PlatformFee:   v1Inst.FeeType_FEE_TYPE_PLATFORM,
		model.ConversionFee: v1Inst.FeeType_FEE_TYPE_CONVERSION,
		model.UnknownFee:    v1Inst.FeeType_FEE_TYPE_INVALID,
	}
	value, found := mapping[fee]
	if !found {
		return v1Inst.FeeType_FEE_TYPE_INVALID
	}
	return value
}

func convertToAction(action *model.Action) *v1Inst.Action {
	if action == nil {
		return nil
	}

	var actTypeMap = map[model.CTAType]v1Inst.ActionType{
		model.CTATypePlanSelection: v1Inst.ActionType_ACTION_TYPE_PLAN_SELECTION,
	}
	actionType, found := actTypeMap[action.Type]
	if !found {
		actionType = v1Inst.ActionType_ACTION_TYPE_UNSPECIFIED
	}

	return &v1Inst.Action{
		Type:   actionType,
		Text:   action.Title,
		ZpiUrl: action.ZpiUrl,
		ZpaUrl: action.ZpaUrl,
	}
}

func convertToMessage(msg *model.Message) *v1Inst.Message {
	if msg == nil {
		return nil
	}
	return &v1Inst.Message{
		Text:  msg.Text,
		Color: msg.Color,
	}
}

func convertToStatementInfo(statement *model.Statement) *v1Stmt.StatementData {
	stmtPaidStatus := statement.GetPaidStatus()
	return &v1Stmt.StatementData{
		Id:                   statement.ID,
		AccountId:            statement.AccountID,
		DueDate:              statement.DueDate.Format("2006-01-02"),
		IncurredDate:         statement.IncurredDate.Format("2006-01-02"),
		PaidStatus:           convertToStatementPaidStatus(stmtPaidStatus),
		PenaltyAmount:        statement.PenaltyAmount,
		OutstandingAmount:    statement.OutstandingAmount,
		OutstandingRepaid:    statement.OutstandingRepaid,
		InstallmentFeeAmount: statement.InstallmentFeeAmount,
		InstallmentFeeRepaid: statement.InstallmentFeeRepaid,
		TotalDueAmount:       statement.CalcTotalDueAmount(),
		TotalDueRepaid:       statement.CalcTotalDueRepaid(),
		TotalDueRemaining:    statement.CalcTotalDueRemaining(),
	}
}

func convertToStatementList(statements []*model.Statement) []*v1Stmt.StatementItem {
	result := make([]*v1Stmt.StatementItem, 0, len(statements))
	for _, statement := range statements {
		result = append(result, convertToStatementListItem(statement))
	}
	return result
}

func convertToStatementListItem(statement *model.Statement) *v1Stmt.StatementItem {
	return &v1Stmt.StatementItem{
		Id:                statement.ID,
		AccountId:         statement.AccountID,
		DueDate:           statement.DueDate.Format("2006-01-02"),
		IncurredDate:      statement.IncurredDate.Format("2006-01-02"),
		PaidStatus:        convertToStatementPaidStatus(statement.GetPaidStatus()),
		TotalDueAmount:    statement.CalcTotalDueAmount(),
		TotalDueRepaid:    statement.CalcTotalDueRepaid(),
		TotalDueRemaining: statement.CalcTotalDueRemaining(),
	}
}

func convertToStatementPaidStatus(status model.StatementPaidStatus) v1Stmt.PaidStatus {
	switch status {
	case model.StatementPaidStatusPaid:
		return v1Stmt.PaidStatus_PAID_STATUS_PAID
	case model.StatementPaidStatusUnpaid:
		return v1Stmt.PaidStatus_PAID_STATUS_UNPAID
	case model.StatementPaidStatusPartiallyPaid:
		return v1Stmt.PaidStatus_PAID_STATUS_PARTIALLY_PAID
	default:
		return v1Stmt.PaidStatus_PAID_STATUS_UNSPECIFIED
	}
}

func convertToStatementInstallments(installments []*model.StatementInstallment) []*v1Stmt.StatementInstallment {
	var result []*v1Stmt.StatementInstallment
	for _, installment := range installments {
		result = append(result, &v1Stmt.StatementInstallment{
			Id:                installment.ID,
			ZpTransId:         installment.RefZPTransID,
			InstallmentId:     installment.RefOriInstID,
			StatementId:       installment.StatementID,
			TransactionDesc:   installment.TransactionRemark,
			OutstandingAmount: installment.GetOutstandingTotal(),
			OutstandingDetails: &v1Stmt.StatementInstallment_OutstandingDetails{
				TotalDueAmount:     installment.GetOutstandingDue(),
				TotalOverdueAmount: installment.GetOutstandingOverdue(),
				TotalPenaltyAmount: installment.GetOutstandingPenalty(),
			},
		})
	}
	return result
}

func convertToInstallmentBase(inst *model.InstallmentInfo) *v1Inst.InstallmentBase {
	if inst == nil {
		return nil
	}
	return &v1Inst.InstallmentBase{
		Id:              inst.ID,
		ZpTransId:       inst.ZPTransID,
		Tenor:           inst.Tenure,
		PartnerInstId:   inst.PartnerInstID,
		PartnerCode:     inst.PartnerCode.String(),
		InterestRate:    inst.SimulationInfo.InterestRate,
		PrincipalAmount: inst.SimulationInfo.PrincipalAmount,
	}
}

func convertToInstallmentData(inst *model.Installment) (*v1Inst.InstallmentData, error) {
	if inst == nil || inst.Info == nil || inst.Repays == nil {
		return nil, fmt.Errorf("invalid installment data, value=%+v", inst)
	}

	return &v1Inst.InstallmentData{
		Id:                   inst.Info.GetInstID(),
		Tenure:               inst.Info.GetTenure(),
		Status:               convertToInstallmentStatus(inst.Info.Status),
		PrincipalAmount:      inst.Info.GetPrincipalAmount(),
		InterestAmount:       inst.Info.GetInterestAmount(),
		PenaltyAmount:        inst.Repays.CalcPenaltyAmount(),
		TotalAmountDue:       inst.Repays.CalcTotalAmountDue(),
		TotalPaidAmount:      inst.Repays.CalcTotalPaidAmount(),
		TotalRemainingAmount: inst.Repays.CalcTotalOutsAmount(),
	}, nil
}

func convertToInstallmentStatus(status model.InstallmentStatus) v1Inst.InstallmentStatus {
	statusMap := map[model.InstallmentStatus]v1Inst.InstallmentStatus{
		model.InstallmentStatusOpen:    v1Inst.InstallmentStatus_INSTALLMENT_STATUS_OPEN,
		model.InstallmentStatusInit:    v1Inst.InstallmentStatus_INSTALLMENT_STATUS_INIT,
		model.InstallmentStatusClosed:  v1Inst.InstallmentStatus_INSTALLMENT_STATUS_CLOSED,
		model.InstallmentStatusUnknown: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED,
	}
	value, ok := statusMap[status]
	if !ok {
		return v1Inst.InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED
	}
	return value
}

func convertToInstRepaySchedules(repaySchedules []*model.InstallmentRepay) []*v1Inst.RepaymentSchedule {
	result := make([]*v1Inst.RepaymentSchedule, 0, len(repaySchedules))
	for _, repay := range repaySchedules {
		result = append(result, &v1Inst.RepaymentSchedule{
			SeqNo:                repay.SeqNo,
			Status:               convertToRepaymentStatus(repay.Status, repay.GraceDueDate),
			DueDate:              repay.RepayDueDate.Format("2006-01-02"),
			DueAmount:            repay.GetEmiAmount(),
			PenaltyAmount:        repay.GetTotalPenalty(),
			TotalDueAmount:       repay.GetTotalAmountDue(),
			TotalPaidAmount:      repay.GetTotalPaidAmount(),
			TotalRemainingAmount: repay.GetTotalOutsAmount(),
		})
	}
	return result
}

func convertToRepaymentStatus(status model.RepayScheduleStatus, dueDate time.Time) v1Inst.RepayStatus {
	switch status {
	case model.RepayScheduleStatusDue:
		nowDateStart := zutils.TruncateDate(time.Now())
		dueDateStart := zutils.TruncateDate(dueDate)
		if nowDateStart.After(dueDateStart) {
			return v1Inst.RepayStatus_REPAY_STATUS_OVERDUE
		}
		return v1Inst.RepayStatus_REPAY_STATUS_DUE
	case model.RepayScheduleStatusPaid:
		return v1Inst.RepayStatus_REPAY_STATUS_PAID
	case model.RepayScheduleStatusInit:
		return v1Inst.RepayStatus_REPAY_STATUS_PENDING
	default:
		return v1Inst.RepayStatus_REPAY_STATUS_UNSPECIFIED
	}
}

// Re-opened it when needed, but should be update some logic to match implementation of convertToEarlyDischarge
// func convertToEarlyDischargeByInst(inst *model.InstallmentInfo) *v1Inst.EarlyDischarge {
// 	if inst == nil {
// 		return nil
// 	}
// 	result := &v1Inst.EarlyDischarge{
// 		Info: &v1Inst.EarlyDischargeBase{
// 			Kind:    convertToEarlyDischargeKind(inst.GetEarlyDischargeKind()),
// 			Status:  convertToEarlyDischargeStatus(inst.DischargeStatus),
// 			Allowed: zutils.Ptr(inst.IsEarlyDischargeAllowed()),
// 		},
// 	}
// 	if inst.GetEarlyDischarge().GetDetails() != nil {
// 		result.Detail = &v1Inst.EarlyDischargeDetail{
// 			TotalDischargeAmount: inst.GetTotalEarlyDischargeAmount(),
// 			EarlyDischargeAmount: inst.GetAdjustEarlyDischargeAmount(),
// 			EarlyDischargeFee:    inst.GetEarlyDischarge().GetDetails().EarlyDischargeFee,
// 			OutstandingPrincipal: inst.GetEarlyDischarge().GetDetails().OutstandingPrincipal,
// 			OutstandingInterest:  inst.GetEarlyDischarge().GetDetails().OutstandingInterest,
// 			OutstandingPenalty:   inst.GetEarlyDischarge().GetDetails().OutstandingPenalty,
// 		}
// 	}
// 	return result
// }

func convertToEarlyDischarge(ed *dto.EarlyDischargeNormalize) *v1Inst.EarlyDischarge {
	if ed == nil {
		return nil
	}

	inst := ed.RefInstInfo
	if inst != nil && (inst.IsClosed() || inst.IsDischargeDone()) {
		return &v1Inst.EarlyDischarge{Info: buildCompleteEarlyDischargeInfo()}
	}
	return &v1Inst.EarlyDischarge{
		Info:   convertToEarlyDischargeBase(ed),
		Detail: convertToEarlyDischargeDetail(ed),
	}
}

func buildCompleteEarlyDischargeInfo() *v1Inst.EarlyDischargeBase {
	return &v1Inst.EarlyDischargeBase{
		Kind:    v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED,
		Status:  v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED,
		Allowed: zutils.Ptr(false),
	}
}

func convertToEarlyDischargeBase(ed *dto.EarlyDischargeNormalize) *v1Inst.EarlyDischargeBase {
	if ed == nil {
		return nil
	}

	earlyDischargeKind := convertToEarlyDischargeKind(ed.EarlyDischargeKind)
	earlyDischargeStatus := convertToEarlyDischargeStatus(ed.DischargeStatus)

	// Create the base response
	result := &v1Inst.EarlyDischargeBase{
		Kind:      earlyDischargeKind,
		Status:    earlyDischargeStatus,
		Allowed:   zutils.Ptr(ed.EarlyDischargeAllowed),
		InSession: zutils.Ptr(ed.InSession),
	}

	if ed.SessionInfo != nil {
		result.SessionInfo = &v1Inst.Session{
			StartTime: ed.SessionInfo.StartTime.Format("15:04"),
			EndTime:   ed.SessionInfo.EndTime.Format("15:04"),
		}
	}

	return result
}

func convertToEarlyDischargeDetail(ed *dto.EarlyDischargeNormalize) *v1Inst.EarlyDischargeDetail {
	instInfo := ed.RefInstInfo
	if instInfo == nil {
		return nil
	}
	detailData := instInfo.GetEarlyDischarge().GetDetails()
	if detailData == nil {
		return nil
	}
	return &v1Inst.EarlyDischargeDetail{
		TotalDischargeAmount:       ed.TotalDischargeAmount,
		EarlyDischargeAmount:       ed.AdjustDischargeAmount,
		EarlyDischargeFee:          ed.EarlyDischargeFee,
		TotalOutstandingAmount:     ed.EarlyDischargeOuts,
		OutstandingPenaltyAmount:   detailData.OutstandingPenalty,
		OutstandingInterestAmount:  detailData.OutstandingInterest,
		OutstandingPrincipalAmount: detailData.OutstandingPrincipal,
	}
}

func convertToEarlyDischargeForRefund(ed *dto.EarlyDischargeNormalize) *v1Inst.EarlyDischargeForRefund {
	if ed == nil {
		return nil
	}
	totalDischargeAmount := ed.TotalDischargeAmount
	earlyDischargeStatus := convertToEarlyDischargeStatus(ed.DischargeStatus)

	result := &v1Inst.EarlyDischargeForRefund{
		Status:           earlyDischargeStatus,
		DischargeAmount:  totalDischargeAmount,
		SessionAvailable: ed.InSession,
	}

	return result
}

func convertToEarlyDischargeKind(status model.EarlyDischargeKind) v1Inst.EarlyDischargeKind {
	statusMap := map[model.EarlyDischargeKind]v1Inst.EarlyDischargeKind{
		model.EarlyDischargeKindUnknown: v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED,
		model.EarlyDischargeKindNormal:  v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_NORMAL,
		model.EarlyDischargeKindRefund:  v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_REFUND,
	}
	value, ok := statusMap[status]
	if !ok {
		return v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED
	}
	return value
}

func convertToEarlyDischargeStatus(status model.DischargeStatus) v1Inst.EarlyDischargeStatus {
	statusMap := map[model.DischargeStatus]v1Inst.EarlyDischargeStatus{
		model.DischargeStatusUnknown:    v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED,
		model.DischargeStatusPending:    v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_ELIGIBLE,
		model.DischargeStatusProcessing: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_PROCESSING,
		model.DischargeStatusComplete:   v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED,
	}
	value, ok := statusMap[status]
	if !ok {
		return v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED
	}
	return value
}

func buildPaymentPlanMetadata(moduleFed *model.ModuleFederation) (*structpb.Struct, error) {
	metadata := map[string]any{}

	if moduleFed != nil {
		metadata["plan_detail_mf"] = map[string]any{
			"scope":        moduleFed.Scope,
			"module":       moduleFed.Module,
			"props":        moduleFed.Props,
			"remote_entry": moduleFed.Entry,
		}
	}

	result, err := structpb.NewStruct(metadata)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func buildPaymentPlanInteraction(
	action *model.Action,
	message *model.Message,
	notice *model.Message) *v1Inst.InteractionGuide {
	return &v1Inst.InteractionGuide{
		Action:  convertToAction(action),
		Message: convertToMessage(message),
		Notice:  convertToMessage(notice),
	}
}

func convertToTransportError(err error) *errors.Error {
	var errApp *errors.Error
	var errInfo *errorkit.ErrorInfo
	if errors.As(err, &errApp) {
		return errApp
	}
	if errors.As(err, &errInfo) {
		return convertErrorInfoToTransportError(errInfo)
	}
	return errors.InternalServer(errorkit.CodeInternalError.String(), err.Error())
}

func convertErrorInfoToTransportError(err *errorkit.ErrorInfo) *errors.Error {
	var errorCodeMapping = map[errorkit.Kind]errorkts.ErrorFunc{
		errorkit.TypeUnexpected:   errors.InternalServer,
		errorkit.TypeBadRequest:   errors.BadRequest,
		errorkit.TypeInvalidArg:   errors.BadRequest,
		errorkit.TypeNotFound:     errors.NotFound,
		errorkit.TypeConversion:   errors.InternalServer,
		errorkit.TypeRemoteCall:   errors.InternalServer,
		errorkit.TypeRepository:   errors.InternalServer,
		errorkit.TypeConflict:     errors.Conflict,
		errorkit.TypeUnauthorized: errors.Unauthorized,
		errorkit.TypeForbidden:    errors.Forbidden,
		errorkit.TypeValidation:   errorkts.UnprocessableEntity,
		errorkit.TypePrecondition: errorkts.PreconditionsFailed,
	}
	utilFunc, found := errorCodeMapping[err.Kind]
	if !found {
		utilFunc = errors.InternalServer
	}

	metadata := cast.ToStringMapString(err.Metadata)
	return utilFunc(err.Code.String(), err.Error()).WithMetadata(metadata)
}
