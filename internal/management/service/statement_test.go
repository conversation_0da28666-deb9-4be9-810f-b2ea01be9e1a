package service

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/testsuite"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repo_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	tx_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/statement"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	keygen_mock "gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocking"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type StatementServiceTestSuite struct {
	suite.Suite
	testsuite.WorkflowTestSuite

	workflowEnv        *testsuite.TestWorkflowEnvironment
	activityEnv        *testsuite.TestActivityEnvironment
	ctrl               *gomock.Controller
	mockTxn            *tx_mocks.MockTransaction
	mockDistLock       *adapter_mocks.MockDistributedLock
	mockJobTaskMgmt    *adapter_mocks.MockJobTaskMgmt
	mockCIMB           *adapter_mocks.MockCIMBService
	mockAcctSvc        *adapter_mocks.MockAccountService
	mockStmtRepo       *repo_mocks.MockStatementRepo
	mockInstRepo       *repo_mocks.MockInstallmentRepo
	mockStmtSyncerRepo *repo_mocks.MockStatementSyncerRepo
	mockRedisKeyGen    *keygen_mock.MockRedisKeyGenerator
	stmtUc             *statement.Usecase
	service            *StatementService
	ctx                context.Context
	logger             log.Logger
}

func TestStatementServiceSuite(t *testing.T) {
	suite.Run(t, new(StatementServiceTestSuite))
}

func (s *StatementServiceTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())

	s.mockTxn = tx_mocks.NewMockTransaction(s.ctrl)
	s.mockDistLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.mockJobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.mockCIMB = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockAcctSvc = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockStmtRepo = repo_mocks.NewMockStatementRepo(s.ctrl)
	s.mockInstRepo = repo_mocks.NewMockInstallmentRepo(s.ctrl)
	s.mockStmtSyncerRepo = repo_mocks.NewMockStatementSyncerRepo(s.ctrl)
	s.mockRedisKeyGen = keygen_mock.NewMockRedisKeyGenerator(s.ctrl)
	s.logger = log.NewStdLogger(os.Stdout)

	testMgmtConfig := &config.Management{
		StatementSync: &config.Management_StatementSync{
			PenaltyAllBufferDays: 7,
		},
	}

	s.stmtUc = statement.NewUsecase(
		s.logger,
		testMgmtConfig,
		s.mockTxn,
		&mocking.Mocking{},
		s.mockDistLock,
		s.mockJobTaskMgmt,
		s.mockRedisKeyGen,
		s.mockCIMB,
		s.mockAcctSvc,
		s.mockStmtRepo,
		s.mockInstRepo,
		s.mockStmtSyncerRepo,
	)

	s.service = NewStatementService(
		s.logger,
		testMgmtConfig,
		s.stmtUc,
	)

	s.ctx = context.Background()

	s.workflowEnv = s.WorkflowTestSuite.NewTestWorkflowEnvironment()
	s.activityEnv = s.WorkflowTestSuite.NewTestActivityEnvironment()

	s.workflowEnv.RegisterWorkflow(s.service.SyncLatestStatementWorkflow)
	s.workflowEnv.RegisterActivity(s.service.SyncLatestStatementActivity)
	s.activityEnv.RegisterActivity(s.service.SyncLatestStatementActivity)
}

func (s *StatementServiceTestSuite) TearDownTest() {
	s.workflowEnv.AssertExpectations(s.T())
	s.ctrl.Finish()
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementWorkflow_Success() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	expectedStatement := createTestStatement()

	s.workflowEnv.OnActivity(
		s.service.SyncLatestStatementActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncLatestStatementParams"),
	).Return(expectedStatement, nil)

	s.workflowEnv.ExecuteWorkflow(s.service.SyncLatestStatementWorkflow, params)

	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.NoError(s.workflowEnv.GetWorkflowError())

	var result *model.Statement
	err := s.workflowEnv.GetWorkflowResult(&result)
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedStatement.ID, result.ID)
	s.Equal(expectedStatement.ZalopayID, result.ZalopayID)
	s.Equal(expectedStatement.AccountID, result.AccountID)
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementWorkflow_ActivityFailure() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	activityError := temporal.NewApplicationError("sync failed", "")
	s.workflowEnv.OnActivity(
		s.service.SyncLatestStatementActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncLatestStatementParams"),
	).Return(nil, activityError)

	s.workflowEnv.ExecuteWorkflow(s.service.SyncLatestStatementWorkflow, params)

	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.Error(s.workflowEnv.GetWorkflowError())
	s.Contains(s.workflowEnv.GetWorkflowError().Error(), "sync failed")
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_Success() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()
	updatedStmt := createTestStatement()
	updatedStmt.OutstandingAmount = 500000

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-lock-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(gomock.Any(), "test-lock-key").Return(nil)
	s.mockDistLock.EXPECT().Release(gomock.Any(), "test-lock-key").Return(nil)
	s.mockAcctSvc.EXPECT().GetActiveAccountByID(gomock.Any(), params.ZalopayID, params.AccountID).Return(&model.Account{
		ID:          params.AccountID,
		ZalopayID:   params.ZalopayID,
		PartnerCode: partner.PartnerCIMB,
	}, nil)
	s.mockCIMB.EXPECT().GetStatementByIncurredDate(gomock.Any(), gomock.Any(), latestStmt.IncurredDate).Return(&model.StatementResult{
		StatementDetail: updatedStmt,
		StatementInsts:  []*model.StatementInstallment{},
	}, nil)
	s.mockCIMB.EXPECT().GetStatementDueOutstanding(gomock.Any(), gomock.Any()).Return(&model.StatementOutstanding{
		OutstandingBalance: 500000,
	}, nil)
	s.mockStmtRepo.EXPECT().UpdateStatementOutstandingAndPenaltyByID(gomock.Any(), updatedStmt).Return(nil)

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.NoError(err)
	s.NotNil(result)
	s.Equal(updatedStmt.ID, result.ID)
	s.Equal(updatedStmt.OutstandingAmount, result.OutstandingAmount)
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_GetLatestStatementError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(nil, model.ErrStatementNotFound)

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to get latest statement")
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_SyncUpdateError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-lock-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(gomock.Any(), "test-lock-key").Return(nil)
	s.mockDistLock.EXPECT().Release(gomock.Any(), "test-lock-key").Return(nil)
	s.mockAcctSvc.EXPECT().GetActiveAccountByID(gomock.Any(), params.ZalopayID, params.AccountID).Return(&model.Account{
		ID:          params.AccountID,
		ZalopayID:   params.ZalopayID,
		PartnerCode: partner.PartnerCIMB,
	}, nil)
	s.mockCIMB.EXPECT().GetStatementByIncurredDate(gomock.Any(), gomock.Any(), latestStmt.IncurredDate).Return(nil, errorkit.NewError(errorkit.CodeCallCIMBFailed, "CIMB service error"))

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to sync update statement")
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_ValidationError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   0, // Invalid zalopay ID
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	// Mock the repository call that will happen before validation fails
	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(nil, model.ErrStatementNotFound)

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_LockFailure() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-lock-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(gomock.Any(), "test-lock-key").Return(errorkit.NewError(errorkit.CodeResourceLockedForProcessing, "lock failed"))

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to sync update statement")
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementWorkflow_WithRetryPolicy() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	expectedStatement := createTestStatement()

	s.workflowEnv.OnActivity(
		s.service.SyncLatestStatementActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncLatestStatementParams"),
	).Return(expectedStatement, nil)

	s.workflowEnv.ExecuteWorkflow(s.service.SyncLatestStatementWorkflow, params)

	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.NoError(s.workflowEnv.GetWorkflowError())

	var result *model.Statement
	err := s.workflowEnv.GetWorkflowResult(&result)
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedStatement.ID, result.ID)
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_AccountServiceError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-lock-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(gomock.Any(), "test-lock-key").Return(nil)
	s.mockDistLock.EXPECT().Release(gomock.Any(), "test-lock-key").Return(nil)
	s.mockAcctSvc.EXPECT().GetActiveAccountByID(gomock.Any(), params.ZalopayID, params.AccountID).Return(nil, errorkit.NewError(errorkit.CodeAccountNotFound, "account not found"))

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to sync update statement")
}

func createTestStatement() *model.Statement {
	now := time.Now()
	return &model.Statement{
		ID:                1,
		Period:            1,
		AccountID:         789,
		ZalopayID:         123456,
		PartnerCode:       partner.PartnerCIMB,
		DueDate:           now.Add(30 * 24 * time.Hour),
		IncurredDate:      now.Add(-10 * 24 * time.Hour),
		PenaltyAmount:     0,
		OutstandingAmount: 1000000,
		OutstandingRepaid: 0,
		CreatedAt:         now.Add(-10 * 24 * time.Hour),
		UpdatedAt:         now,
	}
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_RepositoryUpdateError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()
	updatedStmt := createTestStatement()
	updatedStmt.OutstandingAmount = 500000

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("test-lock-key", nil)
	s.mockDistLock.EXPECT().AcquireStatementSyncing(gomock.Any(), "test-lock-key").Return(nil)
	s.mockDistLock.EXPECT().Release(gomock.Any(), "test-lock-key").Return(nil)
	s.mockAcctSvc.EXPECT().GetActiveAccountByID(gomock.Any(), params.ZalopayID, params.AccountID).Return(&model.Account{
		ID:          params.AccountID,
		ZalopayID:   params.ZalopayID,
		PartnerCode: partner.PartnerCIMB,
	}, nil)
	s.mockCIMB.EXPECT().GetStatementByIncurredDate(gomock.Any(), gomock.Any(), latestStmt.IncurredDate).Return(&model.StatementResult{
		StatementDetail: updatedStmt,
		StatementInsts:  []*model.StatementInstallment{},
	}, nil)
	s.mockCIMB.EXPECT().GetStatementDueOutstanding(gomock.Any(), gomock.Any()).Return(&model.StatementOutstanding{
		OutstandingBalance: 500000,
	}, nil)
	s.mockStmtRepo.EXPECT().UpdateStatementOutstandingAndPenaltyByID(gomock.Any(), updatedStmt).Return(errorkit.NewError(errorkit.CodeRepositoryError, "database error"))

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to sync update statement")
}

func (s *StatementServiceTestSuite) TestSyncLatestStatementActivity_KeyGenerationError() {
	params := &dto.SyncLatestStatementParams{
		ZalopayID:   123456,
		AccountID:   789,
		PartnerCode: partner.PartnerCIMB,
	}

	latestStmt := createTestStatement()

	s.mockStmtRepo.EXPECT().QueryLatestStatement(gomock.Any(), params.ZalopayID, params.AccountID).Return(latestStmt, nil)
	s.mockRedisKeyGen.EXPECT().Generate(gomock.Any()).Return("", errorkit.NewError(errorkit.CodeInternalError, "key generation failed"))

	result, err := s.service.SyncLatestStatementActivity(s.ctx, params)

	s.Error(err)
	s.Nil(result)
	s.IsType(&temporal.ApplicationError{}, err)
	s.Contains(err.Error(), "failed to sync update statement")
}

func createTestStatementWithOutstanding(outstanding int64) *model.Statement {
	stmt := createTestStatement()
	stmt.OutstandingAmount = outstanding
	return stmt
}
