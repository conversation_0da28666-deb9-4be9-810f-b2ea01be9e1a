package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *InstallmentService) ListClientEligiblePlans(ctx context.Context,
	req *v1.ListClientEligiblePlansRequest) (*v1.ListClientEligiblePlansResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.GetPartnerCode())
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	resp, err := s.planUc.ListPlan(ctx, dto.ListPlanParams{
		ZalopayID:   zalopayID,
		AppID:       req.GetAppId(),
		AppTransID:  req.GetAppTransId(),
		OrderAmount: req.GetChargeAmount(),
		PartnerCode: partnerCode,
	})
	if err != nil {
		return nil, convertToTransportError(err)
	}

	planOpts := convertToListPlanOptions(resp.PlanOptions)

	result := &v1.ListClientEligiblePlansResponse{
		PlanOptions:  planOpts,
		PlanSelected: resp.PlanKeySelected,
	}

	return result, nil
}

func (s *InstallmentService) GetClientPlanDetail(ctx context.Context,
	req *v1.GetClientPlanDetailRequest) (*v1.GetClientPlanDetailResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.GetPlanParams{
		PlanKey:   req.GetPlanKey(),
		ZalopayID: zalopayID,
	}
	resp, err := s.planUc.GetPlanDetailByPlanKey(ctx, params)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	planRes := resp.Plan
	result := &v1.GetClientPlanDetailResponse{
		PlanInfo:      convertToPlanInfo(planRes),
		CostInfo:      convertToCostInfo(planRes),
		PlanDetailUrl: planRes.GetPlanDetailUrl().CommonUrl,
	}
	return result, nil
}

func (s *InstallmentService) GetPaymentPlanDetail(ctx context.Context,
	req *v1.GetPaymentPlanDetailRequest) (*v1.GetPaymentPlanDetailResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.GetPlanParams{
		PlanKey:   req.GetPlanKey(),
		ZalopayID: req.GetZalopayId(),
		OrderInfo: &model.OrderRequest{
			AppID:        req.GetOrderInfo().GetAppId(),
			AppTransID:   req.GetOrderInfo().GetAppTransId(),
			ChargeAmount: req.GetOrderInfo().GetChargeAmount(),
		},
	}

	resp, err := s.planUc.GetPlanDetailInPayment(ctx, params)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	planResult := resp.Plan
	interaction := buildPaymentPlanInteraction(resp.Action, resp.Message, resp.Notice)
	metadata, err := buildPaymentPlanMetadata(resp.ModuleFed)
	if err != nil {
		s.logger.WithContext(ctx).Warnf("buildPaymentPlanMetadata fail: %v", err)
	}

	result := &v1.GetPaymentPlanDetailResponse{
		PlanInfo:         convertToPlanInfo(planResult),
		FsChargeInfo:     planResult.BuildFSChargeInfo(),
		PlanDetailUrl:    planResult.GetPlanDetailUrl().CommonUrl,
		Metadata:         metadata,
		InteractionGuide: interaction,
	}
	return result, nil
}

func (s *InstallmentService) AcceptClientPlan(ctx context.Context,
	req *v1.AcceptClientPlanRequest) (*v1.AcceptClientPlanResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.AcceptPlanParams{
		PlanKey:   req.GetPlanKey(),
		ZalopayID: zalopayID,
		OrderInfo: &model.OrderRequest{
			AppID:      req.GetAppId(),
			AppTransID: req.GetAppTransId(),
		},
	}

	if err := s.planUc.AcceptPlan(ctx, params); err != nil {
		return nil, convertToTransportError(err)
	}

	result := &v1.AcceptClientPlanResponse{
		PlanKey: req.PlanKey,
	}
	return result, nil
}
