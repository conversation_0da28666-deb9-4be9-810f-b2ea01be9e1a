package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *ManagementService) GetOutstandingInfo(ctx context.Context,
	req *v1.GetOutstandingInfoRequest) (*v1.GetOutstandingInfoResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	// Get outstanding info
	outstanding, err := s.outsUc.GetLoanOutstanding(ctx, req.GetZalopayId(), req.GetAccountId())
	if err != nil {
		s.logger.WithContext(ctx).Errorf("GetLoanOutstanding has error: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.GetOutstandingInfoResponse{
		TotalOutstanding: outstanding.TotalAmount,
		TotalDueAmount:   outstanding.DueDetails.DueAmount,
		TotalDueRepaid:   outstanding.DueDetails.DueRepaid,
		TotalDuePenalty:  outstanding.DueDetails.DuePenalty,
		DueCreatedAt:     timestamppb.New(outstanding.DueDetails.CreatedAt),
		DueUpdatedAt:     timestamppb.New(outstanding.DueDetails.UpdatedAt),
	}, nil
}
