package service

import (
	"context"
	"errors"

	"github.com/stretchr/testify/mock"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/temporal"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
)

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoWorkflow_Success() {
	// Arrange
	workflowData := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	expectedInstallment := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: 12345,
		Status:    model.InstallmentStatusOpen,
	}

	// Mock the activity execution using Temporal test environment
	s.workflowEnv.OnActivity(
		s.service.SyncInstallmentInfoActivity,             // activity function
		mock.Anything,                                     // any context
		mock.AnythingOfType("*dto.SyncInstallmentParams"), // any params of the right type
	).Return(expectedInstallment, nil)

	// Act
	s.workflowEnv.ExecuteWorkflow(s.service.SyncInstallmentInfoWorkflow, workflowData)

	// Assert
	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.NoError(s.workflowEnv.GetWorkflowError())

	var result model.InstallmentInfo
	s.NoError(s.workflowEnv.GetWorkflowResult(&result))
	s.Equal(expectedInstallment.ID, result.ID)
	s.Equal(expectedInstallment.ZPTransID, result.ZPTransID)
	s.Equal(expectedInstallment.Status, result.Status)
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoWorkflow_Error() {
	// Arrange
	workflowData := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	// Mock workflow execution with error
	s.workflowEnv.OnActivity(
		s.service.SyncInstallmentInfoActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncInstallmentParams"),
	).Return(nil, errors.New("activity error"))

	// Act
	s.workflowEnv.ExecuteWorkflow(s.service.SyncInstallmentInfoWorkflow, workflowData)

	// Assert
	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.Error(s.workflowEnv.GetWorkflowError())
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoActivity_Success() {
	// Arrange
	params := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	expectedInstallment := &model.InstallmentInfo{
		ID:            1,
		ZPTransID:     12345,
		Status:        model.InstallmentStatusOpen,
		ZalopayID:     123,
		PartnerInstID: "cimb456",
	}

	partnerInstInfo := &model.PartnerCIMBInst{
		Status: "OPEN",
	}

	// Mock transaction methods
	s.mockTxn.EXPECT().IsTxActive(gomock.Any()).Return(false).AnyTimes()
	s.mockTxn.EXPECT().BeginOrReuseTx(gomock.Any()).Return(context.Background(), nil).AnyTimes()
	s.mockTxn.EXPECT().RollbackTx(gomock.Any()).Return(nil).AnyTimes()
	s.mockTxn.EXPECT().CommitTx(gomock.Any()).Return(nil).AnyTimes()

	// Mock repository calls
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), params.ZPTransID).Return(expectedInstallment, nil)
	s.mockRepo.EXPECT().GetInstallmentByIDForUpdate(gomock.Any(), expectedInstallment.ID).Return(expectedInstallment, nil).AnyTimes()
	s.mockRepo.EXPECT().UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	// Mock CIMB service call
	s.mockCIMB.EXPECT().QueryInstallmentByID(gomock.Any(), expectedInstallment.ZalopayID, expectedInstallment.PartnerInstID).Return(partnerInstInfo, nil)

	// Act
	result, err := s.service.SyncInstallmentInfoActivity(context.Background(), params)

	// Assert
	s.NoError(err)
	s.Equal(expectedInstallment.ID, result.ID)
	s.Equal(expectedInstallment.ZPTransID, result.ZPTransID)
	s.Equal(expectedInstallment.Status, result.Status)
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoActivity_InstallmentNotFound() {
	// Arrange
	params := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	// Mock repository call - installment not found
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), params.ZPTransID).Return(nil, model.ErrInstallmentNotFound)

	// Act
	result, err := s.service.SyncInstallmentInfoActivity(context.Background(), params)

	// Assert
	s.Error(err)
	s.Nil(result)
	// Check error message instead of exact error instance
	s.Contains(err.Error(), "installment not found")
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoActivity_SyncInstallmentError() {
	// Arrange
	params := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	existingInstallment := &model.InstallmentInfo{
		ID:            1,
		ZPTransID:     12345,
		Status:        model.InstallmentStatusOpen,
		ZalopayID:     123,
		PartnerInstID: "cimb456",
	}

	// Mock repository call
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), params.ZPTransID).Return(existingInstallment, nil)

	// Mock CIMB call - sync failed
	s.mockCIMB.EXPECT().QueryInstallmentByID(gomock.Any(), existingInstallment.ZalopayID, existingInstallment.PartnerInstID).Return(nil, errors.New("sync failed"))

	// Act
	result, err := s.service.SyncInstallmentInfoActivity(context.Background(), params)

	// Assert
	s.Error(err)
	s.Nil(result)
	// Check for partial error message since the error might be wrapped
	s.Contains(err.Error(), "failed to get installment from partner")
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoWorkflow_ActivityTimeout() {
	// Arrange
	workflowData := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	// Mock workflow execution with timeout error
	s.workflowEnv.OnActivity(
		s.service.SyncInstallmentInfoActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncInstallmentParams"),
	).Return(nil, temporal.NewTimeoutError(enums.TIMEOUT_TYPE_SCHEDULE_TO_CLOSE, errors.New("activity timeout")))

	// Act
	s.workflowEnv.ExecuteWorkflow(s.service.SyncInstallmentInfoWorkflow, workflowData)

	// Assert
	s.True(s.workflowEnv.IsWorkflowCompleted())
	s.Error(s.workflowEnv.GetWorkflowError())

	// Get the workflow error
	err := s.workflowEnv.GetWorkflowError()
	s.Error(err)

	var timeoutErr *temporal.TimeoutError
	s.True(errors.As(err, &timeoutErr))
	s.Equal(enums.TIMEOUT_TYPE_SCHEDULE_TO_CLOSE, timeoutErr.TimeoutType())
}

func (s *InstallmentServiceTestSuite) TestSyncInstallmentInfoWorkflow_RetryPolicy() {
	// Arrange
	workflowData := &dto.SyncInstallmentParams{
		ZPTransID: 12345,
	}

	expectedInstallment := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: 12345,
		Status:    model.InstallmentStatusOpen,
	}

	// Mock activity execution to fail first, then succeed
	s.workflowEnv.OnActivity(
		s.service.SyncInstallmentInfoActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncInstallmentParams"),
	).Return(nil, errors.New("temporary error")).
		Times(1)

	s.workflowEnv.OnActivity(
		s.service.SyncInstallmentInfoActivity,
		mock.Anything,
		mock.AnythingOfType("*dto.SyncInstallmentParams"),
	).Return(expectedInstallment, nil).
		Times(1)

	// Configure workflow to retry the activity

	// Act
	s.workflowEnv.ExecuteWorkflow(s.service.SyncInstallmentInfoWorkflow, workflowData)
	s.NoError(s.workflowEnv.GetWorkflowError())

	var result model.InstallmentInfo
	s.NoError(s.workflowEnv.GetWorkflowResult(&result))
	s.Equal(expectedInstallment.ID, result.ID)
}
