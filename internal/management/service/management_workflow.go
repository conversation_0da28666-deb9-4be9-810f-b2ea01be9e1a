package service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
)

const (
	ErrTypePollingStatementDone               = "POLLING_STATEMENT_DONE"
	ErrTypePollingStatementSuccess            = "POLLING_STATEMENT_SUCCESS"
	ErrTypeSyncingStatementInstallmentSuccess = "SYNCING_STATEMENT_INSTALLMENT_SUCCESS"
)

func (s *ManagementService) SyncLoanRepaymentWorkflow(ctx workflow.Context,
	workflowData *dto.SyncLoanRepaymentWorkflow) error {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process polling statement and installment after repayment workflow", "data", workflowData)

	/**
	 * Sleep 5 seconds to wait for the statement to be updated
	 */
	if err := workflow.Sleep(ctx, time.Second*5); err != nil {
		return err
	}

	var currStmtResult *model.StatementResult
	var latestStmtInfo *model.Statement

	// Set up activity options
	actGetStmtOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Minute,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval: time.Second * 5,
			MaximumInterval: time.Second * 5,
			MaximumAttempts: 3,
		},
	}
	actGetStmtCtx := workflow.WithActivityOptions(ctx, actGetStmtOpts)
	err := workflow.
		ExecuteActivity(actGetStmtCtx, s.GetStatementByIDActivity, workflowData.StatementID).
		Get(actGetStmtCtx, &currStmtResult)
	if err != nil {
		logger.Error("failed to get current statement", "error", err)
		return err
	}

	actPollStmtOpts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: time.Minute * 20,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 5,
			MaximumInterval:    time.Second * 30,
			BackoffCoefficient: 1.5,
		},
	}
	actPollStmtCtx := workflow.WithActivityOptions(ctx, actPollStmtOpts)
	actPollStmtFut := workflow.ExecuteActivity(actPollStmtCtx, s.PollingStatementOutstandingActivity, currStmtResult)

	if err = workflow.Sleep(ctx, time.Minute*5); err != nil {
		return err
	}

	actSyncInstOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Minute * 15,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 5,
			MaximumInterval:    time.Second * 45,
			BackoffCoefficient: 2,
		},
	}
	actSyncInstCtx := workflow.WithActivityOptions(ctx, actSyncInstOpts)
	actSyncInstFut := workflow.ExecuteActivity(actSyncInstCtx, s.SyncInstallmentsBelongToStatementActivity, currStmtResult.StatementInsts)

	if err = actPollStmtFut.Get(actPollStmtCtx, &latestStmtInfo); err != nil {
		var appErr *temporal.ApplicationError
		if !errors.As(err, &appErr) {
			logger.Error("Failed to polling statement info", "error", err)
		}
		if appErr != nil && appErr.Type() == ErrTypePollingStatementDone {
			logger.Info("Polling statement info activity done or timeout")
		}
		if appErr != nil && appErr.Type() == ErrTypePollingStatementSuccess {
			logger.Info("Polling statement info success")
		}
	}

	if err = actSyncInstFut.Get(actSyncInstCtx, nil); err != nil {
		var appErr *temporal.ApplicationError
		if !errors.As(err, &appErr) {
			logger.Error("failed to sync installments belong to statement", "error", err)
		}
		if appErr != nil && appErr.Type() == ErrTypeSyncingStatementInstallmentSuccess {
			logger.Info("sync installments belong to statement success")
		}
	}
	return nil
}

func (s *ManagementService) GetStatementByIDActivity(ctx context.Context, stmtID int64) (*model.StatementResult, error) {
	logger := s.logger.WithContext(ctx)

	stmtDetail, err := s.stmtUc.GetStatementByID(ctx, stmtID)
	if err != nil {
		logger.Errorf("failed to get current statement: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get statement", "", err)
	}

	stmtInsts, err := s.stmtUc.GetInstallmentsByStatementID(ctx, stmtID)
	if err != nil {
		logger.Errorf("failed to get statement installments: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get statement installments", "", err)
	}

	stmtResult := &model.StatementResult{
		StatementDetail: stmtDetail,
		StatementInsts:  stmtInsts,
	}
	logger.Infof("Get statement by ID activity success: %+v", stmtResult)
	return stmtResult, nil
}

func (s *ManagementService) GetStatementByIncurDateActivity(ctx context.Context,
	zalopayID, accountID int64, stmtDate time.Time) (*model.StatementResult, error) {
	logger := s.logger.WithContext(ctx)

	params := &dto.QueryStatementParams{
		ZalopayID:     zalopayID,
		AccountID:     accountID,
		StatementDate: stmtDate,
	}
	stmtDetail, err := s.stmtUc.GetStatementByUserAndIncurDate(ctx, params)
	if err != nil {
		logger.Errorf("failed to get current statement: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get statement", "", err)
	}

	stmtInsts, err := s.stmtUc.GetInstallmentsByStatementID(ctx, stmtDetail.ID)
	if err != nil {
		logger.Errorf("failed to get statement installments: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get statement installments", "", err)
	}

	stmtResult := &model.StatementResult{
		StatementDetail: stmtDetail,
		StatementInsts:  stmtInsts,
	}
	return stmtResult, nil
}

func (s *ManagementService) PollingStatementOutstandingActivity(ctx context.Context,
	currStmt *model.StatementResult) (*model.Statement, error) {
	gfCtx := context.WithoutCancel(ctx)
	logger := s.logger.WithContext(ctx)

	currStmtInfo := currStmt.StatementDetail
	zalopayID := currStmtInfo.ZalopayID
	accountID := currStmtInfo.AccountID
	incurredDate := currStmtInfo.IncurredDate

	for {
		select {
		case <-ctx.Done():
			var latestStmtInfo *model.Statement
			if err := activity.GetHeartbeatDetails(gfCtx, &latestStmtInfo); err != nil {
				logger.Errorf("failed to get heartbeat details: %v", err)
				return nil, temporal.NewApplicationErrorWithCause("failed to get heartbeat details", ErrTypePollingStatementDone, err)
			}
			logger.Infow("msg", "Polling statement activity done or timeout", "statement", latestStmtInfo)
			return nil, temporal.NewApplicationErrorWithCause("polling statement activity canceled", ErrTypePollingStatementDone, ctx.Err(), latestStmtInfo)
		default:
			latestStmtInfo, err := s.stmtUc.SyncUpdateAccountStatementByIncrDate(ctx, zalopayID, accountID, incurredDate)
			if err != nil {
				logger.Errorf("failed to update statement and installment: %v", err)
				return nil, temporal.NewApplicationErrorWithCause("failed to update statement and installment", "", err)
			}
			activity.RecordHeartbeat(ctx, latestStmtInfo)
			logger.Infow("msg", "Polling statement info activity success", "statement", latestStmtInfo)
			return nil, temporal.NewApplicationError("polling statement info success", ErrTypePollingStatementSuccess, latestStmtInfo)
		}
	}
}

func (s *ManagementService) SyncInstallmentsBelongToStatementActivity(ctx context.Context,
	stmtInsts []*model.StatementInstallment) error {
	logger := s.logger.WithContext(ctx)

	if len(stmtInsts) == 0 {
		logger.Info("No installment to sync")
		return nil
	}

	partnerInstIDs := model.StatementInstallments(stmtInsts).GetListPartnerInstID()
	partnerInsts, err := s.instUc.SyncInstallmentsByPartnerInstIDs(ctx, partnerInstIDs)
	if err != nil {
		logger.Errorf("failed to sync stmt installments: %v", err)
		return temporal.NewApplicationErrorWithCause("failed to sync stmt installments", "", err, partnerInstIDs)
	}
	activity.RecordHeartbeat(ctx, partnerInsts)
	logger.Infow("msg", "Sync stmt installments activity success", "partnerInstIDs", partnerInstIDs)
	return temporal.NewApplicationError("sync stmt installments success", ErrTypeSyncingStatementInstallmentSuccess, partnerInsts)
}
