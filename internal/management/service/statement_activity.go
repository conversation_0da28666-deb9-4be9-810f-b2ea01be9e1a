package service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/retry"
	"go.temporal.io/sdk/temporal"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *StatementService) RetrieveLatestStatementIncurredActivity(ctx context.Context) (*model.StatementPeriod, error) {
	logger := s.logger.WithContext(ctx)

	stmtPeriod, eligible, err := s.stmtUc.CheckLatestStatementIncurred(ctx)
	if err != nil {
		logger.Errorf("failed to check latest statement incurred: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to check latest statement incurred", "", err)
	}
	if !eligible {
		logger.Infow("msg", "latest statement is not eligible for sync", "period", stmtPeriod)
		return nil, temporal.NewApplicationError("latest statement is not eligible for sync", "")
	}
	return stmtPeriod, nil
}

func (s *StatementService) ClassifyAndCheckSyncStatusActivity(ctx context.Context,
	stmtPeriod *model.StatementPeriod) (*model.StatementSyncSummary, error) {
	logger := s.logger.WithContext(ctx)

	syncInfos, err := s.stmtUc.GetStatementSyncBatchInfoByPeriod(ctx, stmtPeriod)
	if err != nil {
		logger.Errorf("failed to get statement sync info by period: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get statement sync info by period", "", err)
	}
	syncSummary := s.stmtUc.SummarizeStatementSyncBatches(syncInfos)
	return syncSummary, nil
}

func (s *StatementService) InitStatementSyncBatchesActivity(ctx context.Context,
	stmtPeriod *model.StatementPeriod) ([]*model.StatementSyncBatch, error) {
	logger := s.logger.WithContext(ctx)
	accounts, err := s.stmtUc.ListAccountsForStatementSync(ctx, stmtPeriod)
	if err != nil {
		logger.Errorf("failed to list users for statement sync: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to list users for statement sync", "", err)
	}
	if len(accounts) == 0 {
		logger.Info("no users found for statement sync")
		return nil, temporal.NewApplicationError("no users found for statement sync", "")
	}

	logger.Infow("msg", "Init statement sync batches activity", "period", stmtPeriod, "total_accounts", len(accounts))

	batches := s.stmtUc.SplitAccountsIntoBatches(stmtPeriod, accounts)
	if err = s.stmtUc.StoreStatementSyncBatches(ctx, batches); err != nil {
		logger.Errorf("failed to store statement sync batches: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to store statement sync batches", "", err)
	}

	return batches, nil
}

func (s *StatementService) InitSyncBatchesFromExistInfoActivity(ctx context.Context,
	syncInfos []*model.StatementSyncInfo) ([]*model.StatementSyncBatch, error) {
	logger := s.logger.WithContext(ctx)
	batches := s.stmtUc.ListStatementSyncBatchesFromInfos(syncInfos)
	batches, err := s.stmtUc.FulfillAccountsForSyncIntoBatches(ctx, batches)
	if err != nil {
		logger.Errorf("failed to fulfill accounts for statement sync batches: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to fulfill accounts for statement sync batches", "", err)
	}
	return batches, nil
}

func (s *StatementService) ProcessAccountStatementActivity(ctx context.Context,
	account *model.Account, stmtDate time.Time) (*model.Statement, error) {
	logger := s.logger.WithContext(ctx)
	retryer := retry.NewRetry(3, 5*time.Second, nil)

	var stmtResult *model.Statement
	err := retryer.Execute(ctx, func() error {
		stmtData, err := s.stmtUc.SyncCreateAccountStatement(ctx, account, stmtDate)
		if err != nil {
			logger.Errorf("failed to sync user statement: %v", err)
			return err
		}
		stmtResult = stmtData
		return nil
	})

	if err != nil {
		logger.Errorw(
			"msg", "failed to sync user statement", "error", err,
			"partner_code", account.PartnerCode, "account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync user statement", "", err)
	}
	if stmtResult == nil {
		logger.Warnw(
			"msg", "statement data is nil",
			"stmt_date", stmtDate, "partner_code", account.PartnerCode,
			"account_id", account.ID, "zalopay_id", account.ZalopayID,
		)
		return nil, temporal.NewApplicationError("statement data is nil", "")
	}

	logger.Infow(
		"msg", "Sync statement activity success",
		"statement", stmtResult, "partner_code", account.PartnerCode,
		"account_id", account.ID, "zalopay_id", account.ZalopayID,
	)

	if stmtResult.OutstandingAmount <= 0 {
		return stmtResult, nil
	}

	// Trigger sync account balance if there is outstanding amount
	// Still continue returning statement data even if failed to trigger sync account balance
	err = s.stmtUc.TriggerSyncAccountBalanceAfterStatement(ctx, account.ZalopayID, account.ID, stmtDate)
	if err != nil {
		logger.Errorf("failed to trigger sync account balance after statement: %v", err)
		return stmtResult, nil
	}

	logger.Infow("msg", "trigger sync account balance after statement success",
		"partner_code", account.PartnerCode, "account_id", account.ID,
		"zalopay_id", account.ZalopayID, "stmt_date", stmtDate,
	)

	return stmtResult, nil
}

func (s *StatementService) UpdateStatementSyncInfoActivity(ctx context.Context,
	syncInfo *model.StatementSyncInfo) error {
	logger := s.logger.WithContext(ctx)
	err := s.stmtUc.UpdateSyncInfoStatusAndStats(ctx, syncInfo)
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodeInvalidArgument)) {
		logger.Error("sync info ID is required")
		return temporal.NewNonRetryableApplicationError("sync info ID is required", "", err)
	}
	if err != nil {
		logger.Errorf("failed to update statement sync info: %v", err)
		return temporal.NewApplicationErrorWithCause("failed to update statement sync info", "", err)
	}
	return nil
}

func (s *StatementService) MarkSyncBatchToRunningActivity(ctx context.Context,
	batch *model.StatementSyncBatch) error {
	logger := s.logger.WithContext(ctx)
	syncInfo := &model.StatementSyncInfo{
		ID:     batch.RefSyncID,
		ExecID: batch.RefExecID,
		Status: model.StatementSyncStatusRunning,
	}
	err := s.stmtUc.UpdateSyncInfoStatus(ctx, syncInfo)
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodeInvalidArgument)) {
		logger.Error("sync info ID is required")
		return temporal.NewNonRetryableApplicationError("sync info ID is required", "", err)
	}
	if err != nil {
		logger.Errorf("failed to update statement sync batch status to processing: %v", err)
		return temporal.NewApplicationErrorWithCause("failed to update statement sync batch status to processing", "", err)
	}
	return nil
}

func (s *StatementService) SyncStatementsPenaltyActivity(ctx context.Context) (*dto.StatementSyncStats, error) {
	logger := s.logger.WithContext(ctx)

	syncStats, err := s.stmtUc.SyncStatementsPenalty(ctx)
	if err != nil {
		logger.Errorf("failed to sync statements penalty: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync statements penalty", "", err)
	}

	logger.Infof("Sync statement penalty activity completed, stats=%+v", syncStats)
	return syncStats, nil
}

func (s *StatementService) SyncLatestStatementActivity(ctx context.Context, params *dto.SyncLatestStatementParams) (*model.Statement, error) {
	logger := s.logger.WithContext(ctx)
	logger.Infow("Starting sync latest statement activity", "params", params)

	// Get the latest statement for the user
	stmtParams := &dto.QueryStatementParams{
		ZalopayID: params.ZalopayID,
		AccountID: params.AccountID,
	}

	latestStmt, err := s.stmtUc.GetLatestStatementByUser(ctx, stmtParams)
	if err != nil {
		logger.Errorf("failed to get latest statement: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to get latest statement", "", err)
	}

	// Sync and update the statement data
	updatedStmt, err := s.stmtUc.SyncUpdateFullStatementByInfo(ctx, latestStmt)
	if err != nil {
		logger.Errorf("failed to sync update statement: %v", err)
		return nil, temporal.NewApplicationErrorWithCause("failed to sync update statement", "", err)
	}

	logger.Infow("statement sync completed successfully",
		"statement_id", updatedStmt.ID,
		"zalopay_id", updatedStmt.ZalopayID,
		"account_id", updatedStmt.AccountID)

	return updatedStmt, nil
}
