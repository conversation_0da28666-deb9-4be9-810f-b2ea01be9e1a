package service

import (
	"errors"
	"os"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/payment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/installment"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repo_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	tx_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/outstanding"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/statement"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
)

type ManagementEventTestSuite struct {
	suite.Suite
	ctrl               *gomock.Controller
	mockTxn            *tx_mocks.MockTransaction
	mockDistLock       *adapter_mocks.MockDistributedLock
	mockInstRepo       *repo_mocks.MockInstallmentRepo
	mockStmtRepo       *repo_mocks.MockStatementRepo
	mockJobTaskMgmt    *adapter_mocks.MockJobTaskMgmt
	mockCIMB           *adapter_mocks.MockCIMBService
	mockAcctSvc        *adapter_mocks.MockAccountService
	mockPaymentEvtPub  *adapter_mocks.MockPaymentEventPublisher
	mockRedisKeyGen    *mock.MockRedisKeyGenerator
	mockStmtSyncerRepo *repo_mocks.MockStatementSyncerRepo
	instUc             *installment.Usecase
	stmtUc             *statement.Usecase
	outsUc             *outstanding.Usecase
	service            *ManagementService
	logger             log.Logger
}

func TestManagementEventSuite(t *testing.T) {
	suite.Run(t, new(ManagementEventTestSuite))
}

func (s *ManagementEventTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.logger = log.NewStdLogger(os.Stdout)

	// Initialize mocks
	s.mockTxn = tx_mocks.NewMockTransaction(s.ctrl)
	s.mockDistLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.mockInstRepo = repo_mocks.NewMockInstallmentRepo(s.ctrl)
	s.mockStmtRepo = repo_mocks.NewMockStatementRepo(s.ctrl)
	s.mockJobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.mockCIMB = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockAcctSvc = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockPaymentEvtPub = adapter_mocks.NewMockPaymentEventPublisher(s.ctrl)
	s.mockRedisKeyGen = mock.NewMockRedisKeyGenerator(s.ctrl)
	s.mockStmtSyncerRepo = repo_mocks.NewMockStatementSyncerRepo(s.ctrl)

	// Create usecases
	s.instUc = installment.NewUsecase(
		s.mockTxn,
		s.mockDistLock,
		s.mockInstRepo,
		s.mockJobTaskMgmt,
		s.mockCIMB,
		s.mockAcctSvc,
		s.mockPaymentEvtPub,
		s.mockRedisKeyGen,
		nil, // config not needed for these tests
		s.logger,
	)

	s.stmtUc = statement.NewUsecase(
		s.logger,
		nil, // config not needed for these tests
		s.mockTxn,
		nil, // mocking not needed for these tests
		s.mockDistLock,
		s.mockJobTaskMgmt,
		s.mockRedisKeyGen,
		s.mockCIMB,
		s.mockAcctSvc,
		s.mockStmtRepo,
		s.mockInstRepo,
		s.mockStmtSyncerRepo,
	)

	s.outsUc = &outstanding.Usecase{} // Simple mock for outstanding usecase

	// Create the service
	s.service = NewManagementService(
		s.logger,
		s.stmtUc,
		s.instUc,
		s.outsUc,
	)
}

func (s *ManagementEventTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_EmptyMessage() {
	// Test with empty message value
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: []byte{},
	}

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_InvalidJSON() {
	// Test with invalid JSON
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: []byte("invalid json"),
	}

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.Error(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_NoStandardSettle() {
	// Test with event that has no standard settle
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
		RefZpTransId: 12345,
		// No StandardSettle or ExpiredSettle
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_RequestType_Success() {
	// Test successful REQUEST event type
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	testInst := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: zpTransID,
		ZalopayID: 123456,
		AccountID: 789,
	}

	// Setup mock expectations for MarkEarlyDischargeProcessing
	// First it calls GetInstallmentByZPTransID, then UpdateEarlyDischargeStatus
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeStatus(gomock.Any(), testInst.ID, model.DischargeStatusProcessing).Return(nil)

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_RequestType_Error() {
	// Test REQUEST event type with error
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	// Setup mock expectations with error - GetInstallmentByZPTransID fails
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(nil, errors.New("installment not found"))

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.Error(err)
	s.Contains(err.Error(), "installment not found")
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_ResponseType_Success() {
	// Test successful RESPONSE event type with success status
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransStatus: v1.TransStatus_TRANS_STATUS_SUCCESS,
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	testInst := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: zpTransID,
		ZalopayID: 123456,
		AccountID: 789,
	}

	// Setup mock expectations for HandleEarlyDischargeByRefundSettleResult
	// First it calls GetInstallmentByZPTransID, then UpdateEarlyDischargeStatus
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeStatus(gomock.Any(), testInst.ID, model.DischargeStatusComplete).Return(nil)
	// Then the service calls GetInstallmentByZPTransID again for sync operations
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	// The sync operations run in goroutines and use the trigger methods, which internally use JobTaskMgmt
	// We'll mock the Execute methods that are called by the trigger methods
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncInstallmentTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncLatestStatementTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncAccountBalanceAfterDischargeTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_ResponseType_Failed() {
	// Test RESPONSE event type with failed status
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransStatus: v1.TransStatus_TRANS_STATUS_FAILED,
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	testInst := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: zpTransID,
		ZalopayID: 123456,
		AccountID: 789,
	}

	// Setup mock expectations for HandleEarlyDischargeByRefundSettleResult (failed status -> pending)
	// First it calls GetInstallmentByZPTransID, then UpdateEarlyDischargeStatus
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeStatus(gomock.Any(), testInst.ID, model.DischargeStatusPending).Return(nil)
	// Then the service calls GetInstallmentByZPTransID again for sync operations
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	// The sync operations run in goroutines
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncInstallmentTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncLatestStatementTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncAccountBalanceAfterDischargeTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_ResponseType_GetInstallmentError() {
	// Test RESPONSE event type with error getting installment
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransStatus: v1.TransStatus_TRANS_STATUS_SUCCESS,
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	// Setup mock expectations with error in HandleEarlyDischargeByRefundSettleResult
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(nil, errors.New("installment not found"))

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.Error(err)
	s.Contains(err.Error(), "installment not found")
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_ResponseType_SyncTaskErrors() {
	// Test RESPONSE event type with sync task errors
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
		SettleResult: &v1.RefundSettleEvent_RefundSettleResult{
			TransStatus: v1.TransStatus_TRANS_STATUS_SUCCESS,
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	testInst := &model.InstallmentInfo{
		ID:        1,
		ZPTransID: zpTransID,
		ZalopayID: 123456,
		AccountID: 789,
	}

	// Setup mock expectations with sync task errors
	// First HandleEarlyDischargeByRefundSettleResult calls
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeStatus(gomock.Any(), testInst.ID, model.DischargeStatusComplete).Return(nil)
	// Then the service calls GetInstallmentByZPTransID again for sync operations
	s.mockInstRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(testInst, nil)
	// The sync operations run in goroutines and may fail, but the main handler should still succeed
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncInstallmentTask(gomock.Any(), gomock.Any()).Return(errors.New("sync installment failed")).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncLatestStatementTask(gomock.Any(), gomock.Any()).Return(errors.New("sync statement failed")).AnyTimes()
	s.mockJobTaskMgmt.EXPECT().ExecuteSyncAccountBalanceAfterDischargeTask(gomock.Any(), gomock.Any()).Return(errors.New("sync balance failed")).AnyTimes()

	err := s.service.HandleLoanRefundSettleEvent(message)

	// The method should still return nil even if sync tasks fail (they just log errors)
	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_ExpiredSettle() {
	// Test with expired settle context - should return early without processing
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST,
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_ExpiredSettle{
			ExpiredSettle: &v1.RefundExpiredSettle{
				RefundId: 999,
			},
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	// No mock expectations needed - the code returns early when GetStandardSettle() == nil

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err)
}

func (s *ManagementEventTestSuite) TestHandleLoanRefundSettleEvent_UnknownEventType() {
	// Test with unknown event type
	const zpTransID int64 = 12345
	settleEvent := &v1.RefundSettleEvent{
		EventId:      "test-event-123",
		EventType:    v1.RefundSettleEventType_REFUND_SETTLE_EVENT_UNKNOWN, // Unknown type
		RefZpTransId: zpTransID,
		SettleContext: &v1.RefundSettleEvent_StandardSettle{
			StandardSettle: &v1.RefundStandardSettle{
				SettleId: 999,
			},
		},
	}

	eventData, _ := protojson.Marshal(settleEvent)
	message := kafka.Message{
		Key:   []byte("test-key"),
		Value: eventData,
	}

	err := s.service.HandleLoanRefundSettleEvent(message)

	s.NoError(err) // Should handle gracefully
}

// Note: Removed problematic test cases for unknown status and nil result
// These cases result in DischargeStatusUnknown which causes HandleEarlyDischargeByRefundSettleResult to return early with error
// The coverage for fromRefundSettleResultToDischargeStatus function is already good with the existing test cases
