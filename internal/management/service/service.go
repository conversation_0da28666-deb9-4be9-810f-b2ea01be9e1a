package service

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	v1Inst "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	v1Stmt "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/statement/v1"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/installment"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/outstanding"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/plan"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/statement"
)

var ProviderSet = wire.NewSet(
	NewStatementService,
	NewInstallmentService,
	NewManagementService,
	NewOperationService,
)

type StatementService struct {
	v1Stmt.UnimplementedStatementServer
	logger *log.Helper
	config *config.Management
	stmtUc *statement.Usecase
}

func NewStatementService(
	logger log.Logger,
	config *config.Management,
	stmtUc *statement.Usecase) *StatementService {
	logging := log.With(logger, "service", "statement")
	return &StatementService{
		logger: log.NewHelper(logging),
		stmtUc: stmtUc,
		config: config,
	}
}

type InstallmentService struct {
	v1Inst.UnimplementedInstallmentServer
	logger *log.Helper
	config *config.Management
	planUc *plan.Usecase
	instUc *installment.Usecase
}

func NewInstallmentService(
	logger log.Logger,
	config *config.Management,
	planUc *plan.Usecase,
	instUc *installment.Usecase) *InstallmentService {
	logging := log.With(logger, "service", "installment")
	return &InstallmentService{
		logger: log.NewHelper(logging),
		planUc: planUc,
		instUc: instUc,
		config: config,
	}
}

type OperationService struct {
	v1.UnimplementedOperationServer
	logger  *log.Helper
	config  *config.Management
	stmtUc  *statement.Usecase
	instUc  *installment.Usecase
	maintUc *maintenance.Usecase
}

func NewOperationService(
	logger log.Logger,
	config *config.Management,
	stmtUc *statement.Usecase,
	instUc *installment.Usecase,
	maintUc *maintenance.Usecase) *OperationService {
	logging := log.With(logger, "service", "operation")
	return &OperationService{
		config:  config,
		logger:  log.NewHelper(logging),
		stmtUc:  stmtUc,
		instUc:  instUc,
		maintUc: maintUc,
	}
}

type ManagementService struct {
	v1.UnimplementedManagementServer
	logger *log.Helper
	stmtUc *statement.Usecase
	instUc *installment.Usecase
	outsUc *outstanding.Usecase
}

func NewManagementService(
	logger log.Logger,
	stmtUc *statement.Usecase,
	instUc *installment.Usecase,
	outsUc *outstanding.Usecase) *ManagementService {
	logging := log.With(logger, "service", "management")
	return &ManagementService{
		logger: log.NewHelper(logging),
		stmtUc: stmtUc,
		instUc: instUc,
		outsUc: outsUc,
	}
}
