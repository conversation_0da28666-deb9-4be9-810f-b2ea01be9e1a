package service

import (
	"github.com/segmentio/kafka-go"
	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/payment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/kafkara"
	"google.golang.org/protobuf/encoding/protojson"
)

func (s *ManagementService) HandleLoanRefundSettleEvent(message kafka.Message) error {
	ctx := kafkara.ExtractB3FromHeaders(message.Headers)
	logger := s.logger.WithContext(ctx)

	if len(message.Value) == 0 {
		logger.Error("Received empty fundback order event")
		return nil
	}

	var settleEvent v1.RefundSettleEvent
	var unmarshaller = protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := unmarshaller.Unmarshal(message.Value, &settleEvent); err != nil {
		logger.Error("Failed to unmarshal refund settle event", "error", err)
		return err
	}

	if settleEvent.GetStandardSettle() == nil {
		logger.Error("Received refund settle event without standard settle")
		return nil
	}

	logger.Infow(
		"msg", "Received refund settle event",
		"event_data", settleEvent.String(),
		"event_key", string(message.Key),
		"event_header", message.Headers,
	)

	var err error

	switch settleEvent.EventType {
	case v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST:
		err = s.instUc.MarkEarlyDischargeProcessing(ctx, settleEvent.RefZpTransId)
	case v1.RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE:
		stt := fromRefundSettleResultToDischargeStatus(settleEvent.SettleResult)
		err = s.instUc.HandleEarlyDischargeByRefundSettleResult(ctx, settleEvent.RefZpTransId, stt)
	default:
		logger.Infow("msg", "Unknown event type", "event_type", settleEvent.EventType)
		return nil
	}

	if err != nil {
		logger.Error("Failed to process refund settle event", "error", err)
		return err
	}
	// if the event type is REFUND_SETTLE_EVENT_REQUEST, we don't need to trigger sync
	// because the installment info is already updated in the previous step
	if settleEvent.EventType == v1.RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST {
		return nil
	}

	inst, err := s.instUc.GetInstallmentByZPTransID(ctx, settleEvent.RefZpTransId)
	if err != nil {
		logger.Error("Failed to get installment by ZP transaction ID", "error", err)
		return err
	}

	go s.instUc.TriggerSyncInstallmentInfo(ctx, inst)
	go s.stmtUc.TriggerSyncLatestStatement(ctx, inst.ZalopayID, inst.AccountID)
	go s.instUc.TriggerSyncAccountBalanceAfterDischarged(ctx, inst.ZalopayID, inst.AccountID)

	return nil
}

func fromRefundSettleResultToDischargeStatus(result *v1.RefundSettleEvent_RefundSettleResult) model.DischargeStatus {
	switch {
	case result == nil:
		return model.DischargeStatusUnknown
	case result.TransStatus == v1.TransStatus_TRANS_STATUS_SUCCESS:
		return model.DischargeStatusComplete
	case result.TransStatus == v1.TransStatus_TRANS_STATUS_FAILED:
		return model.DischargeStatusPending
	default:
		return model.DischargeStatusUnknown
	}
}
