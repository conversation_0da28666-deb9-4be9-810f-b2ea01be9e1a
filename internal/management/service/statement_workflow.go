package service

import (
	"errors"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
)

const (
	workerNumber = 20
)

func (s *StatementService) SyncLatestStatementWorkflow(ctx workflow.Context, params *dto.SyncLatestStatementParams) (*model.Statement, error) {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync latest statement workflow", "params", params)

	actOpts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute * 5,
		ScheduleToCloseTimeout: time.Minute * 15,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second * 15,
			MaximumInterval:    time.Minute,
			BackoffCoefficient: 2.0,
		},
	}
	actCtx := workflow.WithActivityOptions(ctx, actOpts)

	var statement *model.Statement
	err := workflow.ExecuteActivity(actCtx, s.SyncLatestStatementActivity, params).Get(actCtx, &statement)
	if err != nil {
		logger.Error("Sync latest statement activity failed", "error", err)
		return nil, err
	}

	logger.Info("Sync latest statement workflow completed")

	return statement, nil
}

func (s *StatementService) SyncStatementsPeriodicWorkflow(ctx workflow.Context) ([]*model.StatementSyncInfo, error) {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync cimb statements workflow")

	opts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 48,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Minute,
			MaximumInterval:    time.Minute * 5,
			BackoffCoefficient: 2.0,
		},
	}

	var stmtPeriod *model.StatementPeriod
	err := workflow.ExecuteActivity(
		workflow.WithActivityOptions(ctx, opts),
		s.RetrieveLatestStatementIncurredActivity).Get(ctx, &stmtPeriod)
	if err != nil {
		logger.Error("Retrieve latest statement incurred activity failed", "error", err)
		return nil, err
	}

	var syncBatches []*model.StatementSyncBatch
	var syncSummary *model.StatementSyncSummary
	err = workflow.ExecuteActivity(
		workflow.WithActivityOptions(ctx, opts),
		s.ClassifyAndCheckSyncStatusActivity, stmtPeriod).Get(ctx, &syncSummary)
	if err != nil {
		logger.Error("Classify and check sync status activity failed", "error", err)
		return nil, err
	}
	if syncSummary.AllSuccess {
		logger.Info("Statement sync is up-to-date")
		return nil, nil
	}

	if syncSummary.StillHaveIncompleteSyncs() {
		// Recover only incomplete syncs (failed|pending) when run second time
		err = workflow.ExecuteActivity(
			workflow.WithActivityOptions(ctx, opts),
			s.InitSyncBatchesFromExistInfoActivity, syncSummary.CollectIncompleteSyncs()).Get(ctx, &syncBatches)
	} else {
		// Default case: init all batches
		err = workflow.ExecuteActivity(
			workflow.WithActivityOptions(ctx, opts),
			s.InitStatementSyncBatchesActivity, stmtPeriod).Get(ctx, &syncBatches)
	}

	if err != nil {
		logger.Error("Init statement sync batches info activity failed", "error", err)
		return nil, err
	}

	var futures []workflow.Future
	var syncInfos []*model.StatementSyncInfo
	for _, batch := range syncBatches {
		batchConf := s.config.GetSchedulers().GetSyncStatementsBatchProc()
		batchWorkflowID := buildBatchExecID(batch)
		batchWorkflowOptions := workflow.ChildWorkflowOptions{
			WorkflowID:               batchWorkflowID,
			TaskQueue:                batchConf.GetQueueName(),
			WorkflowExecutionTimeout: 2 * time.Hour,
		}
		cCtx := workflow.WithChildOptions(ctx, batchWorkflowOptions)
		future := workflow.ExecuteChildWorkflow(cCtx, s.StatementSyncBatchProcessingWorkflow, batch)
		futures = append(futures, future)
	}

	for _, future := range futures {
		var syncInfo *model.StatementSyncInfo
		err = future.Get(ctx, &syncInfo)
		if temporal.IsWorkflowExecutionAlreadyStartedError(err) {
			logger.Warn("Statement sync batch processing workflow already started")
			continue
		}
		if err != nil {
			logger.Error("Statement sync batch processing workflow failed", "error", err)
			return nil, err
		}
		syncInfos = append(syncInfos, syncInfo)
	}

	return syncInfos, nil
}

func (s *StatementService) StatementSyncBatchProcessingWorkflow(
	ctx workflow.Context, batch *model.StatementSyncBatch) (*model.StatementSyncInfo, error) {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync cimb statements by batch workflow", "batch", batch)

	ao := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 30,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Minute,
			MaximumInterval:    time.Minute * 5,
			BackoffCoefficient: 2.0,
		},
	}

	err := workflow.ExecuteActivity(
		workflow.WithActivityOptions(ctx, ao),
		s.MarkSyncBatchToRunningActivity, batch).Get(ctx, nil)
	if err != nil {
		logger.Error("Mark sync batch to running activity failed", "error", err)
		return nil, err
	}

	waitGroup := workflow.NewWaitGroup(ctx)
	waitGroup.Add(workerNumber)

	inChannel := workflow.NewBufferedChannel(ctx, workerNumber)
	outChannel := workflow.NewBufferedChannel(ctx, len(batch.Accounts))

	workflow.Go(ctx, func(ctx workflow.Context) {
		for _, account := range batch.Accounts {
			inChannel.Send(ctx, account)
		}
		inChannel.Close()
	})

	for i := 0; i < workerNumber; i++ {
		workflow.Go(ctx, s.executeSyncAccountStatement(waitGroup, inChannel, outChannel, batch.StmtPeriod))
	}

	waitGroup.Wait(ctx)
	outChannel.Close()

	syncStats := s.handleAndBuildSyncStatsFromBatchResult(ctx, batch, outChannel)
	syncInfo := s.stmtUc.BuildStatementSyncInfoFromBatch(batch, syncStats)

	err = workflow.ExecuteActivity(
		workflow.WithActivityOptions(ctx, ao),
		s.UpdateStatementSyncInfoActivity, syncInfo).Get(ctx, nil)
	if err != nil {
		logger.Error("Update statement sync info activity failed", "error", err)
		return nil, err
	}

	logger.Info(
		"Batch processing completed",
		"total_items", syncStats.TotalItems,
		"total_synced", syncStats.TotalSynced,
		"total_failed", syncStats.TotalFailed)

	return syncInfo, nil
}

func (s *StatementService) SyncStatementsPenaltyWorkflow(ctx workflow.Context) error {
	logger := workflow.GetLogger(ctx)

	logger.Info("Start process sync statement penalty workflow")

	actOpts := workflow.ActivityOptions{
		ScheduleToCloseTimeout: time.Hour * 6,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval: time.Minute,
			MaximumAttempts: 3,
		},
	}
	actCtx := workflow.WithActivityOptions(ctx, actOpts)
	err := workflow.
		ExecuteActivity(actCtx, s.SyncStatementsPenaltyActivity).
		Get(actCtx, nil)
	if err != nil {
		logger.Error("Sync statement penalty activity failed", "error", err)
		return err
	}

	logger.Info("Sync statement penalty workflow completed")

	return nil
}

func (s *StatementService) executeSyncAccountStatement(
	waitGroup workflow.WaitGroup,
	inChannel workflow.Channel,
	outChannel workflow.Channel,
	stmtPeriod *model.StatementPeriod) func(ctx workflow.Context) {
	return func(ctx workflow.Context) {
		defer waitGroup.Done()
		logger := workflow.GetLogger(ctx)
		stmtDate := stmtPeriod.IncurredDate

		var account *model.Account
		for inChannel.Receive(ctx, &account) {
			ao := workflow.ActivityOptions{
				StartToCloseTimeout: time.Minute * 5,
				RetryPolicy: &temporal.RetryPolicy{
					MaximumAttempts: 1, // DO NOT retry here, because we already have retry in ProcessAccountStatementActivity
				},
			}

			var stmt *model.Statement
			err := workflow.ExecuteActivity(
				workflow.WithActivityOptions(ctx, ao),
				s.ProcessAccountStatementActivity, account, stmtDate).Get(ctx, &stmt)
			if err != nil {
				logger.Error("Sync statement activity failed", "error", err)
				outChannel.Send(ctx, err)
				continue
			}
			if stmt == nil {
				logger.Warn("Statement data is nil")
				outChannel.Send(ctx, errors.New("statement data is nil"))
				continue
			}
			outChannel.Send(ctx, nil)
		}
	}
}

func (s *StatementService) handleAndBuildSyncStatsFromBatchResult(
	ctx workflow.Context,
	batch *model.StatementSyncBatch,
	outChannel workflow.Channel) *model.StatementSyncStats {
	totalSynced := 0
	totalFailed := 0

	var result error
	for outChannel.Receive(ctx, &result) {
		if result != nil {
			totalFailed++
			continue
		}
		totalSynced++
	}

	return &model.StatementSyncStats{
		// Assign again query offset, total items to prevent mismatch when storage
		QueryOffset: cast.ToString(batch.QueryOffset),
		TotalItems:  batch.TotalItems,
		TotalSynced: totalSynced,
		TotalFailed: totalFailed,
		FirstFailed: "",
	}
}

func buildBatchExecID(batch *model.StatementSyncBatch) string {
	batchNo := batch.BatchNo
	stmtDate := batch.StmtPeriod.IncurredDate
	return fmt.Sprintf(utils.WfIDSyncStatementsBatch.String(), stmtDate.Format("20060102"), batchNo)
}
