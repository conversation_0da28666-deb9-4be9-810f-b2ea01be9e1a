package service

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.temporal.io/sdk/testsuite"
	"go.uber.org/mock/gomock"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/installment"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repo_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	tx_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/plan"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
)

// ptrBool is a helper function to get a pointer to a boolean value
func ptrBool(b bool) *bool {
	return &b
}

type InstallmentServiceTestSuite struct {
	suite.Suite
	testsuite.WorkflowTestSuite

	workflowEnv       *testsuite.TestWorkflowEnvironment
	activityEnv       *testsuite.TestActivityEnvironment
	ctrl              *gomock.Controller
	mockTxn           *tx_mocks.MockTransaction
	mockDistLock      *adapter_mocks.MockDistributedLock
	mockRepo          *repo_mocks.MockInstallmentRepo
	mockJobTaskMgmt   *adapter_mocks.MockJobTaskMgmt
	mockCIMB          *adapter_mocks.MockCIMBService
	mockAcctSvc       *adapter_mocks.MockAccountService
	mockPaymentEvtPub *adapter_mocks.MockPaymentEventPublisher
	mockRedisKeyGen   *mock.MockRedisKeyGenerator // Using real or simple mock if needed, let's use a simple one for now
	mockPlanUc        *plan.Usecase               // This seems to be a different Usecase, assuming it's mocked or a simple version is fine
	instUc            *installment.Usecase        // Real Usecase
	service           *InstallmentService
	ctx               context.Context
	logger            log.Logger
}

func TestInstallmentServiceSuite(t *testing.T) {
	suite.Run(t, new(InstallmentServiceTestSuite))
}

func (s *InstallmentServiceTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())

	// Initialize mocks for installment.Usecase dependencies
	s.mockTxn = tx_mocks.NewMockTransaction(s.ctrl)
	s.mockDistLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.mockRepo = repo_mocks.NewMockInstallmentRepo(s.ctrl)
	s.mockJobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.mockCIMB = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockAcctSvc = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockPaymentEvtPub = adapter_mocks.NewMockPaymentEventPublisher(s.ctrl)
	s.mockRedisKeyGen = mock.NewMockRedisKeyGenerator(s.ctrl)
	s.logger = log.NewStdLogger(os.Stdout)
	s.mockPlanUc = &plan.Usecase{}

	// Initialize a minimal config for the Usecase
	testMgmtConfig := &config.Management{
		EarlyDischarge: &config.Management_EarlyDischarge{
			Refund: &config.Management_EarlyDischarge_BaseScenario{
				Enabled: true,
				TimeWindow: &config.Management_EarlyDischarge_TimeWindow{
					StartTime: &config.TimeOfDay{Hours: 9, Minutes: 0},
					EndTime:   &config.TimeOfDay{Hours: 17, Minutes: 0},
				},
			},
		},
	}

	// Initialize real usecase
	s.instUc = installment.NewUsecase(
		s.mockTxn,
		s.mockDistLock,
		s.mockRepo,
		s.mockJobTaskMgmt,
		s.mockCIMB,
		s.mockAcctSvc,
		s.mockPaymentEvtPub,
		s.mockRedisKeyGen,
		testMgmtConfig,
		s.logger,
	)

	// Create the service with the usecase
	s.service = NewInstallmentService(
		s.logger,
		testMgmtConfig, // Pass the same config or a relevant one for the service
		s.mockPlanUc,
		s.instUc, // Pass the usecase instance
	)

	// Create context with auth session for client tests
	s.ctx = auth.NewContext(context.Background(), &sessionv1.Session{
		ZalopayId: "123456",
	})

	// Setup workflow test environment
	s.workflowEnv = s.WorkflowTestSuite.NewTestWorkflowEnvironment()
	s.activityEnv = s.WorkflowTestSuite.NewTestActivityEnvironment()

	// Register activity with the test environments
	s.workflowEnv.RegisterActivity(s.service.SyncInstallmentInfoActivity)
	s.activityEnv.RegisterActivity(s.service.SyncInstallmentInfoActivity)
}

func (s *InstallmentServiceTestSuite) TearDownTest() {
	s.workflowEnv.AssertExpectations(s.T())
	s.ctrl.Finish()
}

// TestGetClientInstallment_Success tests the successful case for GetClientInstallment
func (s *InstallmentServiceTestSuite) TestGetClientInstallment_Success() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetClientInstallmentRequest{
		ZpTransId: zpTransID,
	}

	// Create test models
	instModel := createTestInstallment()
	// earlyDischarge := createTestEarlyDischargeNormalize() // Removed, res.EarlyDischarge is populated by service

	// Use the context with auth session that's already set up in SetupTest
	ctx := s.ctx

	// Setup mock behaviors on s.instUc's dependencies (e.g., s.mockRepo)
	// The GetFullInstallmentByZalopayAndZPTransID is a method of installment.Usecase which calls repo methods.
	// It calls s.mockRepo.GetFullInstallmentByUserIDAndZPTransID()
	s.mockRepo.EXPECT().GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), int64(123456), zpTransID).Return(instModel, nil)

	// For buildNormalizeEarlyDischargeFunc, this is a method of Usecase itself.
	// If it has external dependencies that are mocked, set expectations for them.
	// For now, we assume it's pure logic or its deps are covered by other mocks if called.
	// If BuildNormalizeEarlyDischarge is complex and calls other mocked usecase dependencies, set expectations here.
	// Example: if it calls cimb service:
	// s.mockCIMB.EXPECT().SomeMethod(gomock.Any(), gomock.Any()).Return(nil, nil)

	// Call the method under test
	res, err := s.service.GetClientInstallment(ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
	s.Equal(instModel.Info.ZPTransID, res.Installment.Id)
	s.Equal(v1.InstallmentStatus_INSTALLMENT_STATUS_OPEN, res.Installment.Status)
	s.NotNil(res.EarlyDischarge)
	s.Len(res.RepaymentSchedules, len(instModel.Repays))
}

// TestGetClientInstallment_ValidationError tests validation error for GetClientInstallment
func (s *InstallmentServiceTestSuite) TestGetClientInstallment_ValidationError() {
	// Setup test data with invalid ZP transaction ID
	req := &v1.GetClientInstallmentRequest{
		ZpTransId: 0,
	}

	// No mock setup needed since validation should fail before reaching usecase

	// Call the method under test
	res, err := s.service.GetClientInstallment(s.ctx, req)

	// Assertions for validation error
	s.Error(err)
	s.Nil(res)
	s.Contains(err.Error(), errorkit.CodeBadRequest.String())
}

// TestGetClientInstallment_UsecaseError tests usecase error for GetClientInstallment
func (s *InstallmentServiceTestSuite) TestGetClientInstallment_UsecaseError() {
	const zpTransID int64 = 12345
	req := &v1.GetClientInstallmentRequest{
		ZpTransId: zpTransID,
	}

	// Setup mock behavior to return an error
	s.mockRepo.EXPECT().GetFullInstallmentByUserIDAndZPTransID(gomock.Any(), int64(123456), zpTransID).Return(nil, errors.NotFound(errorkit.CodeDataNotFound.String(), "installment not found"))

	// Call the method under test
	resp, err := s.service.GetClientInstallment(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(resp)
	s.Contains(err.Error(), "not found")
}

// TestGetInstallmentStatus_Success tests the successful case for GetInstallmentStatus
func (s *InstallmentServiceTestSuite) TestGetInstallmentStatus_Success() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetInstallmentStatusRequest{
		ZpTransId:   zpTransID,
		ForceLatest: false,
	}

	// Create test model
	instModel := &model.Installment{
		Info: &model.InstallmentInfo{
			ZPTransID: zpTransID,
			Status:    model.InstallmentStatusOpen,
		},
		Repays: []*model.InstallmentRepay{},
	}

	// Setup mock behavior
	// Assuming GetFullInstallmentByZPTransID on usecase calls repo's GetFullInstallmentByZPTransID
	s.mockRepo.EXPECT().GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).Return(instModel, nil)

	// Call the method under test
	res, err := s.service.GetInstallmentStatus(s.ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
	s.Equal(v1.InstallmentStatus_INSTALLMENT_STATUS_OPEN, res.Status)
}

// TestGetInstallmentStatus_ForceLatestSync tests the case when ForceLatest=true
func (s *InstallmentServiceTestSuite) TestGetInstallmentStatus_ForceLatestSync() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetInstallmentStatusRequest{
		ZpTransId:   zpTransID,
		ForceLatest: true,
	}

	// Create test models
	origInst := &model.Installment{
		Info: &model.InstallmentInfo{
			ZPTransID: zpTransID,
			Status:    model.InstallmentStatusOpen,
		},
	}

	// Setup mock behavior
	s.mockRepo.EXPECT().GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).Return(origInst, nil)
	// SyncInstallmentData checks if transaction is active
	s.mockTxn.EXPECT().IsTxActive(gomock.Any()).Return(false)
	// SyncInstallmentData begins or reuses transaction
	s.mockTxn.EXPECT().BeginOrReuseTx(gomock.Any()).Return(context.Background(), nil)
	// SyncInstallmentData is a usecase method that calls CIMB service to get latest data
	s.mockCIMB.EXPECT().QueryInstallmentByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.PartnerCIMBInst{
		ID:                  "inst-123",
		Status:              model.PartnerCIMBInstStatusClosed,
		Tenure:              3,
		StartDate:           time.Now().Add(-30 * 24 * time.Hour),
		EndDate:             time.Now(),
		DueDate:             time.Now(),
		DaysPastDue:         0,
		InterestRate:        0.1,
		DisburseAmount:      1000000,
		OutstandingOriginal: 0,
		RepaymentSchedules: []*model.PartnerCIMBInstRepaySchedule{
			{
				SeqNo:           1,
				RepayDueDate:    time.Now().Add(-20 * 24 * time.Hour),
				EmiAmount:       333333,
				PrincipalAmount: 300000,
				InterestAmount:  33333,
			},
			{
				SeqNo:           2,
				RepayDueDate:    time.Now().Add(-10 * 24 * time.Hour),
				EmiAmount:       333333,
				PrincipalAmount: 300000,
				InterestAmount:  33333,
			},
			{
				SeqNo:           3,
				RepayDueDate:    time.Now(),
				EmiAmount:       333334,
				PrincipalAmount: 300000,
				InterestAmount:  33334,
			},
		},
	}, nil)
	// SyncInstallmentData also needs these additional mocks
	s.mockRepo.EXPECT().GetInstallmentByIDForUpdate(gomock.Any(), origInst.Info.ID).Return(origInst.Info, nil)
	s.mockTxn.EXPECT().RollbackTx(gomock.Any()).Return(nil) // defer rollback
	// Mock the repay schedule updates for each repayment schedule
	s.mockRepo.EXPECT().UpdateRepayScheduleAfterSync(gomock.Any(), gomock.Any()).Return(nil).Times(3) // 3 repay schedules
	s.mockTxn.EXPECT().CommitTx(gomock.Any()).Return(nil)
	// And then updates the repo:
	s.mockRepo.EXPECT().UpdateInstallmentAfterSync(gomock.Any(), gomock.Any()).Return(nil)

	// Call the method under test
	res, err := s.service.GetInstallmentStatus(s.ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
}

func (s *InstallmentServiceTestSuite) TestGetInstallmentStatus_ValidationError() {
	// Setup test data with invalid ZP transaction ID (0 is invalid)
	req := &v1.GetInstallmentStatusRequest{
		ZpTransId: 0,
	}

	// No mock setup needed since validation should fail before reaching usecase

	// Call the method under test
	res, err := s.service.GetInstallmentStatus(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(res)
	s.Contains(err.Error(), errorkit.CodeBadRequest.String())
}

// TestGetInstallmentStatus_SyncError tests sync error for GetInstallmentStatus
func (s *InstallmentServiceTestSuite) TestGetInstallmentStatus_SyncError() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetInstallmentStatusRequest{
		ZpTransId:   zpTransID,
		ForceLatest: true,
	}

	// Setup expectations with sync error
	// Assuming getFullInstallmentByZPTransID is called first
	s.mockRepo.EXPECT().GetFullInstallmentByZPTransID(gomock.Any(), zpTransID).Return(&model.Installment{Info: &model.InstallmentInfo{ZPTransID: zpTransID}}, nil)
	// Then SyncInstallmentData is called, and it errors (e.g. CIMB call fails)
	s.mockCIMB.EXPECT().QueryInstallmentByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.InternalServer(errorkit.CodeInternalError.String(), "sync error"))

	// Call the method under test
	resp, err := s.service.GetInstallmentStatus(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(resp)
	s.Contains(err.Error(), "sync error")
}

// TestGetEarlyDischargeRefund_Success tests success case for GetEarlyDischargeRefund
func (s *InstallmentServiceTestSuite) TestGetEarlyDischargeRefund_Success() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetEarlyDischargeRefundRequest{
		ZpTransId:   zpTransID,
		ForceLatest: ptrBool(false),
	}

	// Create test data - make sure early discharge is not mismatched
	instInfo := &model.InstallmentInfo{
		ID:              1,
		ZPTransID:       zpTransID,
		Status:          model.InstallmentStatusClosed, // Closed status to match discharge status
		DischargeStatus: model.DischargeStatusComplete,
		EarlyDischarge: &model.InstallmentEarlyDischarge{
			Details: &model.EarlyDischargeDetails{
				TotalDischargeAmount: 1000000,
				EarlyDischargeFee:    50000,
			},
			SyncedAt: time.Now(), // Recent sync time to avoid being outdated
		},
	}

	// Setup mock behavior for GetLatestEarlyDischarge usecase method
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(instInfo, nil)
	// Since ForceLatest is false and EarlyDischarge is not mismatched, no CIMB call should be made

	// Call the method under test
	res, err := s.service.GetEarlyDischargeRefund(s.ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
	s.NotNil(res.DischargeInfo)
	// Verify that the discharge amount is correctly mapped
	s.Equal(instInfo.EarlyDischarge.Details.TotalDischargeAmount, res.DischargeInfo.DischargeAmount)
}

// TestGetEarlyDischargeRefund_ValidationError tests validation error for GetEarlyDischargeRefund
func (s *InstallmentServiceTestSuite) TestGetEarlyDischargeRefund_ValidationError() {
	// Setup invalid request
	req := &v1.GetEarlyDischargeRefundRequest{
		ZpTransId: 0, // Invalid value
	}

	// Call the method under test
	resp, err := s.service.GetEarlyDischargeRefund(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(resp)
	s.Contains(err.Error(), errorkit.CodeBadRequest.String())
}

// TestNotifyInstallmentRefund_ValidationError tests validation error for NotifyInstallmentRefund
func (s *InstallmentServiceTestSuite) TestNotifyInstallmentRefund_ValidationError() {
	// Setup test data with invalid ZP transaction ID
	req := &v1.InstallmentRefund{
		ZpTransId: 0, // Invalid ZpTransId
	}

	// No mock setup needed since validation should fail before reaching usecase

	// Call the method under test
	res, err := s.service.NotifyInstallmentRefund(s.ctx, req)

	// Assertions for validation error
	s.Error(err)
	s.Nil(res)
	s.Contains(err.Error(), errorkit.CodeBadRequest.String())
}

// TestNotifyInstallmentRefund_Success tests success case for NotifyInstallmentRefund
func (s *InstallmentServiceTestSuite) TestNotifyInstallmentRefund_Success() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.InstallmentRefund{
		ZpTransId:         zpTransID,
		Version:           1,
		NetRefundAmount:   500000,
		TotalRefundAmount: 550000,
		UserTopupAmount:   0,
		UserTopupRequired: false,
	}

	// Setup mock behavior for the repository
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(&model.InstallmentInfo{ZPTransID: zpTransID}, nil)
	s.mockRepo.EXPECT().UpdateInstallmentRefund(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	// Call the method under test
	res, err := s.service.NotifyInstallmentRefund(s.ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
}

// TestNotifyInstallmentRefund_HandleError tests error from HandleRefundEventNotified
func (s *InstallmentServiceTestSuite) TestNotifyInstallmentRefund_HandleError() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.InstallmentRefund{
		ZpTransId:         zpTransID,
		Version:           1,
		NetRefundAmount:   500000,
		TotalRefundAmount: 550000,
		UserTopupAmount:   0,
		UserTopupRequired: false,
	}

	// Setup mock behavior for the repository with error
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(&model.InstallmentInfo{ZPTransID: zpTransID}, nil)
	s.mockRepo.EXPECT().UpdateInstallmentRefund(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.InternalServer(errorkit.CodeInternalError.String(), "failed to handle refund notification"))

	// Call the method under test
	res, err := s.service.NotifyInstallmentRefund(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(res)
	s.Contains(err.Error(), "failed to handle refund notification")
}

// Helper method to create a common installment model for tests
func createTestInstallment() *model.Installment {
	now := time.Now()
	installmentInfo := &model.InstallmentInfo{
		ID:              12345, // Use ZPTransID as ID for the assertion to pass
		ZPTransID:       12345,
		ZalopayID:       123456,
		Status:          model.InstallmentStatusOpen,
		PaidTenure:      2,
		DischargeStatus: model.DischargeStatusComplete,
		CreatedAt:       now.Add(-30 * 24 * time.Hour),
		// Add early discharge information
		EarlyDischarge: &model.InstallmentEarlyDischarge{
			Details: &model.EarlyDischargeDetails{
				TotalDischargeAmount: 1000000,
				EarlyDischargeFee:    50000,
			},
			SyncedAt: now.Add(-1 * time.Hour), // Set a recent sync time
		},
	}

	repays := []*model.InstallmentRepay{
		{
			SeqNo:           1,
			RepayDueDate:    now.Add(-10 * 24 * time.Hour),
			EmiAmount:       100000,
			PrincipalAmount: 80000,
			InterestAmount:  20000,
			Status:          model.RepayScheduleStatusPaid,
			OutstandingDetails: &model.InstallmentRepayOutstanding{
				OutstandingAmount:    0,
				OutstandingInterest:  0,
				OutstandingPenalty:   0,
				OutstandingPrincipal: 0,
			},
		},
		{
			SeqNo:           2,
			RepayDueDate:    now.Add(-5 * 24 * time.Hour),
			EmiAmount:       100000,
			PrincipalAmount: 80000,
			InterestAmount:  20000,
			Status:          model.RepayScheduleStatusPaid,
			OutstandingDetails: &model.InstallmentRepayOutstanding{
				OutstandingAmount:    0,
				OutstandingInterest:  0,
				OutstandingPenalty:   0,
				OutstandingPrincipal: 0,
			},
		},
		{
			SeqNo:           3,
			RepayDueDate:    now.Add(25 * 24 * time.Hour),
			EmiAmount:       100000,
			PrincipalAmount: 80000,
			InterestAmount:  20000,
			Status:          model.RepayScheduleStatusDue,
			OutstandingDetails: &model.InstallmentRepayOutstanding{
				OutstandingAmount:    100000,
				OutstandingInterest:  20000,
				OutstandingPenalty:   0,
				OutstandingPrincipal: 80000,
			},
		},
	}

	return &model.Installment{
		Info:   installmentInfo,
		Repays: repays,
	}
}

// TestGetClientEarlyDischarge_Success tests success case for GetClientEarlyDischarge
func (s *InstallmentServiceTestSuite) TestGetClientEarlyDischarge_Success() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetClientEarlyDischargeRequest{
		ZpTransId: zpTransID,
	}

	// Setup mock behavior
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(&model.InstallmentInfo{
		ZPTransID:       zpTransID,
		ZalopayID:       123456,
		Status:          model.InstallmentStatusClosed,
		DischargeStatus: model.DischargeStatusComplete,
		EarlyDischarge: &model.InstallmentEarlyDischarge{
			Details: &model.EarlyDischargeDetails{
				TotalDischargeAmount: 1000000,
				EarlyDischargeFee:    50000,
			},
			SyncedAt: time.Now(),
		},
	}, nil)

	// Call the method under test
	res, err := s.service.GetClientEarlyDischarge(s.ctx, req)

	// Assertions
	s.NoError(err)
	s.NotNil(res)
	s.NotNil(res.EarlyDischarge)
}

// TestGetClientEarlyDischarge_ValidationError tests validation error for GetClientEarlyDischarge
func (s *InstallmentServiceTestSuite) TestGetClientEarlyDischarge_ValidationError() {
	// Setup invalid request
	req := &v1.GetClientEarlyDischargeRequest{
		ZpTransId: 0, // Invalid value
	}

	// Call the method under test
	resp, err := s.service.GetClientEarlyDischarge(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(resp)
	s.Contains(err.Error(), errorkit.CodeBadRequest.String())
}

// TestGetClientEarlyDischarge_NotFound tests not found case for GetClientEarlyDischarge
func (s *InstallmentServiceTestSuite) TestGetClientEarlyDischarge_NotFound() {
	// Setup test data
	const zpTransID int64 = 12345
	req := &v1.GetClientEarlyDischargeRequest{
		ZpTransId: zpTransID,
	}

	// Setup mock behavior to return nil (not found)
	s.mockRepo.EXPECT().GetInstallmentByZPTransID(gomock.Any(), zpTransID).Return(&model.InstallmentInfo{
		ZPTransID:       zpTransID,
		ZalopayID:       999999, // Different zalopay ID to trigger not found
		Status:          model.InstallmentStatusClosed,
		DischargeStatus: model.DischargeStatusComplete,
		EarlyDischarge: &model.InstallmentEarlyDischarge{
			Details: &model.EarlyDischargeDetails{
				TotalDischargeAmount: 1000000,
				EarlyDischargeFee:    50000,
			},
			SyncedAt: time.Now(),
		},
	}, nil)

	// Call the method under test
	resp, err := s.service.GetClientEarlyDischarge(s.ctx, req)

	// Assertions
	s.Error(err)
	s.Nil(resp)
	s.Contains(err.Error(), "early discharge not found")
}

// Helper method to create a normalized early discharge object
func createTestEarlyDischargeNormalize() *dto.EarlyDischargeNormalize {
	return &dto.EarlyDischargeNormalize{
		RefInstID:             1,     // Example, adjust as needed
		RefZPTransID:          12345, // Example, adjust as needed
		InSession:             true,
		DischargeStatus:       model.DischargeStatusComplete,
		EarlyDischargeKind:    model.EarlyDischargeKindRefund, // Example
		EarlyDischargeFee:     50000,
		EarlyDischargeOuts:    950000, // Example: TotalDischargeAmount - EarlyDischargeFee
		TotalDischargeAmount:  1000000,
		EarlyDischargeAllowed: true,
		AdjustDischargeAmount: 0,
		// SessionInfo and RefInstInfo can be nil or populated if specific tests need them
	}
}
