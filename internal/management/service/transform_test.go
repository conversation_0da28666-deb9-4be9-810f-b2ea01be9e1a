package service

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	v1Inst "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	v1Stmt "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/statement/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func TestConvertToListPlanOptions(t *testing.T) {
	tests := []struct {
		name     string
		plans    []*model.Plan
		expected int
	}{
		{
			name:     "empty plans",
			plans:    []*model.Plan{},
			expected: 0,
		},
		{
			name: "single plan",
			plans: []*model.Plan{
				createTestPlan("plan1", 3, model.PlanStatusActive),
			},
			expected: 1,
		},
		{
			name: "multiple plans",
			plans: []*model.Plan{
				createTestPlan("plan1", 3, model.PlanStatusActive),
				createTestPlan("plan2", 6, model.PlanStatusInactive),
			},
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToListPlanOptions(tt.plans)
			assert.Len(t, result, tt.expected)

			if len(tt.plans) > 0 {
				assert.Equal(t, tt.plans[0].PlanKey, result[0].PlanKey)
				assert.NotNil(t, result[0].PlanInfo)
				assert.NotNil(t, result[0].CostInfo)
			}
		})
	}
}

func TestConvertToPlanRepaySchedules(t *testing.T) {
	tests := []struct {
		name       string
		repayments []*model.PlanRepayment
		expected   int
	}{
		{
			name:       "empty repayments",
			repayments: []*model.PlanRepayment{},
			expected:   0,
		},
		{
			name: "single repayment",
			repayments: []*model.PlanRepayment{
				{Amount: 100000, DueDate: "2024-01-15", InstNum: 1},
			},
			expected: 1,
		},
		{
			name: "multiple repayments",
			repayments: []*model.PlanRepayment{
				{Amount: 100000, DueDate: "2024-01-15", InstNum: 1},
				{Amount: 100000, DueDate: "2024-02-15", InstNum: 2},
			},
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPlanRepaySchedules(tt.repayments)
			assert.Len(t, result, tt.expected)

			if len(tt.repayments) > 0 {
				assert.Equal(t, tt.repayments[0].Amount, result[0].Amount)
				assert.Equal(t, tt.repayments[0].DueDate, result[0].DueDate)
				assert.Equal(t, tt.repayments[0].InstNum, result[0].InstallmentNumber)
			}
		})
	}
}

func TestConvertToPlanInfo(t *testing.T) {
	plan := createTestPlan("test-plan", 3, model.PlanStatusActive)

	result := convertToPlanInfo(plan)

	assert.NotNil(t, result)
	assert.Equal(t, plan.PlanKey, *result.PlanKey)
	assert.Equal(t, plan.Tenure, result.TenorNumber)
	assert.Equal(t, plan.PlanInfo.EmiAmount, result.EmiAmount)
	assert.Equal(t, plan.PlanInfo.TotalAmount, result.TotalAmount)
	assert.Equal(t, plan.PlanInfo.PrincipalAmount, result.PrincipalAmount)
	assert.Equal(t, v1Inst.PlanStatus_PLAN_STATUS_ACTIVE, result.Status)
}

func TestConvertToPlanStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   model.PlanStatus
		expected v1Inst.PlanStatus
	}{
		{
			name:     "active status",
			status:   model.PlanStatusActive,
			expected: v1Inst.PlanStatus_PLAN_STATUS_ACTIVE,
		},
		{
			name:     "inactive status",
			status:   model.PlanStatusInactive,
			expected: v1Inst.PlanStatus_PLAN_STATUS_INACTIVE,
		},
		{
			name:     "invalid status",
			status:   model.PlanStatusInvalid,
			expected: v1Inst.PlanStatus_PLAN_STATUS_INVALID,
		},
		{
			name:     "unknown status",
			status:   model.PlanStatusUnknown,
			expected: v1Inst.PlanStatus_PLAN_STATUS_UNSPECIFIED,
		},
		{
			name:     "unmapped status defaults to unspecified",
			status:   model.PlanStatus(999),
			expected: v1Inst.PlanStatus_PLAN_STATUS_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPlanStatus(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToCostInfo(t *testing.T) {
	plan := createTestPlan("test-plan", 3, model.PlanStatusActive)

	result := convertToCostInfo(plan)

	assert.NotNil(t, result)
	assert.Equal(t, plan.CostInfo.TotalCostAmount, result.TotalCostAmount)
	assert.Equal(t, plan.CostInfo.TotalFeeAmount, result.TotalFeeAmount)
	assert.Equal(t, plan.CostInfo.InterestAmount, result.InterestAmount)
	assert.Equal(t, plan.CostInfo.PlatformFeeAmount, result.PlatformFeeAmount)
	assert.Equal(t, plan.CostInfo.ConversionFeeAmount, result.ConversionFeeAmount)
	assert.NotNil(t, result.ListFeeInfo)
}

func TestConvertToFeeList(t *testing.T) {
	fees := []model.FeeDetail{
		{Type: model.PlatformFee, Amount: 10000, Explain: "Platform fee"},
		{Type: model.ConversionFee, Amount: 5000, Explain: "Conversion fee"},
		{Type: model.UnknownFee, Amount: 1000, Explain: "Unknown fee"},
	}

	result := convertToFeeList(fees)

	assert.Len(t, result, 3)
	assert.Equal(t, v1Inst.FeeType_FEE_TYPE_PLATFORM, result[0].FeeType)
	assert.Equal(t, int64(10000), result[0].FeeAmount)
	assert.Equal(t, "Platform fee", result[0].Message)

	assert.Equal(t, v1Inst.FeeType_FEE_TYPE_CONVERSION, result[1].FeeType)
	assert.Equal(t, v1Inst.FeeType_FEE_TYPE_INVALID, result[2].FeeType)
}

func TestConvertToFeeType(t *testing.T) {
	tests := []struct {
		name     string
		feeType  model.FeeType
		expected v1Inst.FeeType
	}{
		{
			name:     "platform fee",
			feeType:  model.PlatformFee,
			expected: v1Inst.FeeType_FEE_TYPE_PLATFORM,
		},
		{
			name:     "conversion fee",
			feeType:  model.ConversionFee,
			expected: v1Inst.FeeType_FEE_TYPE_CONVERSION,
		},
		{
			name:     "unknown fee",
			feeType:  model.UnknownFee,
			expected: v1Inst.FeeType_FEE_TYPE_INVALID,
		},
		{
			name:     "unmapped fee defaults to invalid",
			feeType:  model.FeeType("unmapped"),
			expected: v1Inst.FeeType_FEE_TYPE_INVALID,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToFeeType(tt.feeType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToAction(t *testing.T) {
	tests := []struct {
		name     string
		action   *model.Action
		expected *v1Inst.Action
	}{
		{
			name:     "nil action",
			action:   nil,
			expected: nil,
		},
		{
			name: "valid action",
			action: &model.Action{
				Type:   model.CTATypePlanSelection,
				Title:  "Select Plan",
				ZpiUrl: "zpi://test",
				ZpaUrl: "zpa://test",
			},
			expected: &v1Inst.Action{
				Type:   v1Inst.ActionType_ACTION_TYPE_PLAN_SELECTION,
				Text:   "Select Plan",
				ZpiUrl: "zpi://test",
				ZpaUrl: "zpa://test",
			},
		},
		{
			name: "unknown action type",
			action: &model.Action{
				Type:  model.CTAType(999),
				Title: "Unknown",
			},
			expected: &v1Inst.Action{
				Type: v1Inst.ActionType_ACTION_TYPE_UNSPECIFIED,
				Text: "Unknown",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToAction(tt.action)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.expected.Type, result.Type)
				assert.Equal(t, tt.expected.Text, result.Text)
				assert.Equal(t, tt.expected.ZpiUrl, result.ZpiUrl)
				assert.Equal(t, tt.expected.ZpaUrl, result.ZpaUrl)
			}
		})
	}
}

func TestConvertToMessage(t *testing.T) {
	tests := []struct {
		name     string
		message  *model.Message
		expected *v1Inst.Message
	}{
		{
			name:     "nil message",
			message:  nil,
			expected: nil,
		},
		{
			name: "valid message",
			message: &model.Message{
				Text:  "Test message",
				Color: "#FF0000",
			},
			expected: &v1Inst.Message{
				Text:  "Test message",
				Color: "#FF0000",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToMessage(tt.message)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.expected.Text, result.Text)
				assert.Equal(t, tt.expected.Color, result.Color)
			}
		})
	}
}

func TestConvertToStatementInfo(t *testing.T) {
	now := time.Now()
	statement := &model.Statement{
		ID:                   1,
		AccountID:            123,
		DueDate:              now,
		IncurredDate:         now.AddDate(0, 0, -1),
		PenaltyAmount:        5000,
		OutstandingAmount:    100000,
		OutstandingRepaid:    50000,
		InstallmentFeeAmount: 10000,
		InstallmentFeeRepaid: 5000,
	}

	result := convertToStatementInfo(statement)

	assert.NotNil(t, result)
	assert.Equal(t, statement.ID, result.Id)
	assert.Equal(t, statement.AccountID, result.AccountId)
	assert.Equal(t, now.Format("2006-01-02"), result.DueDate)
	assert.Equal(t, now.AddDate(0, 0, -1).Format("2006-01-02"), result.IncurredDate)
	assert.Equal(t, statement.PenaltyAmount, result.PenaltyAmount)
	assert.Equal(t, statement.OutstandingAmount, result.OutstandingAmount)
	assert.Equal(t, statement.OutstandingRepaid, result.OutstandingRepaid)
	assert.Equal(t, statement.InstallmentFeeAmount, result.InstallmentFeeAmount)
	assert.Equal(t, statement.InstallmentFeeRepaid, result.InstallmentFeeRepaid)
	assert.Equal(t, statement.CalcTotalDueAmount(), result.TotalDueAmount)
	assert.Equal(t, statement.CalcTotalDueRepaid(), result.TotalDueRepaid)
	assert.Equal(t, statement.CalcTotalDueRemaining(), result.TotalDueRemaining)
}

func TestConvertToStatementList(t *testing.T) {
	now := time.Now()
	statements := []*model.Statement{
		{
			ID:                1,
			AccountID:         123,
			DueDate:           now,
			IncurredDate:      now.AddDate(0, 0, -1),
			OutstandingAmount: 100000,
			OutstandingRepaid: 50000,
		},
		{
			ID:                2,
			AccountID:         123,
			DueDate:           now.AddDate(0, 1, 0),
			IncurredDate:      now,
			OutstandingAmount: 80000,
			OutstandingRepaid: 20000,
		},
	}

	result := convertToStatementList(statements)

	assert.Len(t, result, 2)
	assert.Equal(t, statements[0].ID, result[0].Id)
	assert.Equal(t, statements[1].ID, result[1].Id)
}

func TestConvertToStatementListItem(t *testing.T) {
	now := time.Now()
	statement := &model.Statement{
		ID:                1,
		AccountID:         123,
		DueDate:           now,
		IncurredDate:      now.AddDate(0, 0, -1),
		OutstandingAmount: 100000,
		OutstandingRepaid: 50000,
	}

	result := convertToStatementListItem(statement)

	assert.NotNil(t, result)
	assert.Equal(t, statement.ID, result.Id)
	assert.Equal(t, statement.AccountID, result.AccountId)
	assert.Equal(t, now.Format("2006-01-02"), result.DueDate)
	assert.Equal(t, now.AddDate(0, 0, -1).Format("2006-01-02"), result.IncurredDate)
	assert.Equal(t, statement.CalcTotalDueAmount(), result.TotalDueAmount)
	assert.Equal(t, statement.CalcTotalDueRepaid(), result.TotalDueRepaid)
	assert.Equal(t, statement.CalcTotalDueRemaining(), result.TotalDueRemaining)
}

func TestConvertToStatementPaidStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   model.StatementPaidStatus
		expected v1Stmt.PaidStatus
	}{
		{
			name:     "paid status",
			status:   model.StatementPaidStatusPaid,
			expected: v1Stmt.PaidStatus_PAID_STATUS_PAID,
		},
		{
			name:     "unpaid status",
			status:   model.StatementPaidStatusUnpaid,
			expected: v1Stmt.PaidStatus_PAID_STATUS_UNPAID,
		},
		{
			name:     "partially paid status",
			status:   model.StatementPaidStatusPartiallyPaid,
			expected: v1Stmt.PaidStatus_PAID_STATUS_PARTIALLY_PAID,
		},
		{
			name:     "unknown status defaults to unspecified",
			status:   model.StatementPaidStatusUnknown,
			expected: v1Stmt.PaidStatus_PAID_STATUS_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToStatementPaidStatus(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToStatementInstallments(t *testing.T) {
	installments := []*model.StatementInstallment{
		{
			ID:                1,
			RefZPTransID:      12345,
			RefOriInstID:      67890,
			StatementID:       1,
			TransactionRemark: "Test transaction 1",
			OutstandingData: &model.StatementInstallmentOuts{
				TotalOutstanding:        100000,
				OutstandingDuePrincipal: 80000,
				OutstandingDueInterest:  15000,
				OutstandingDuePenalty:   5000,
			},
		},
		{
			ID:                2,
			RefZPTransID:      12346,
			RefOriInstID:      67891,
			StatementID:       1,
			TransactionRemark: "Test transaction 2",
			OutstandingData: &model.StatementInstallmentOuts{
				TotalOutstanding:        50000,
				OutstandingDuePrincipal: 40000,
				OutstandingDueInterest:  8000,
				OutstandingDuePenalty:   2000,
			},
		},
	}

	result := convertToStatementInstallments(installments)

	assert.Len(t, result, 2)
	assert.Equal(t, installments[0].ID, result[0].Id)
	assert.Equal(t, installments[0].RefZPTransID, result[0].ZpTransId)
	assert.Equal(t, installments[0].RefOriInstID, result[0].InstallmentId)
	assert.Equal(t, installments[0].StatementID, result[0].StatementId)
	assert.Equal(t, installments[0].TransactionRemark, result[0].TransactionDesc)
	assert.NotNil(t, result[0].OutstandingDetails)
}

// Helper function to create test plan
func createTestPlan(planKey string, tenure int64, status model.PlanStatus) *model.Plan {
	return &model.Plan{
		PlanKey: planKey,
		Tenure:  tenure,
		Status:  status,
		PlanInfo: model.PlanInfo{
			EmiAmount:       100000,
			TotalAmount:     300000,
			PrincipalAmount: 250000,
		},
		CostInfo: model.CostInfo{
			TotalCostAmount:     50000,
			TotalFeeAmount:      30000,
			InterestAmount:      20000,
			PlatformFeeAmount:   15000,
			ConversionFeeAmount: 15000,
			ListFeeDetails: []model.FeeDetail{
				{Type: model.PlatformFee, Amount: 15000, Explain: "Platform fee"},
			},
		},
		OtherInfo: model.OtherInfo{
			IsPopular: true,
			PlanDetailUrl: model.DeepLinkData{
				ZPAUrl: "zpa://plan-detail",
			},
		},
		RepaySchedule: model.PlanRepaySchedule{
			Repayments: []*model.PlanRepayment{
				{Amount: 100000, DueDate: "2024-01-15", InstNum: 1},
			},
		},
	}
}

func TestConvertToInstallmentBase(t *testing.T) {
	tests := []struct {
		name string
		inst *model.InstallmentInfo
		want *v1Inst.InstallmentBase
	}{
		{
			name: "nil installment",
			inst: nil,
			want: nil,
		},
		{
			name: "valid installment",
			inst: &model.InstallmentInfo{
				ID:            123,
				ZPTransID:     456,
				Tenure:        3,
				PartnerInstID: "CIMB123",
				PartnerCode:   partner.PartnerCIMB,
				SimulationInfo: model.InstallmentSimulation{
					InterestRate:    0.15,
					PrincipalAmount: 1000000,
				},
			},
			want: &v1Inst.InstallmentBase{
				Id:              123,
				ZpTransId:       456,
				Tenor:           3,
				PartnerInstId:   "CIMB123",
				PartnerCode:     "CIMB",
				InterestRate:    0.15,
				PrincipalAmount: 1000000,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToInstallmentBase(tt.inst)
			if tt.want == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.want.Id, result.Id)
				assert.Equal(t, tt.want.ZpTransId, result.ZpTransId)
				assert.Equal(t, tt.want.Tenor, result.Tenor)
				assert.Equal(t, tt.want.PartnerInstId, result.PartnerInstId)
				assert.Equal(t, tt.want.PartnerCode, result.PartnerCode)
				assert.Equal(t, tt.want.InterestRate, result.InterestRate)
				assert.Equal(t, tt.want.PrincipalAmount, result.PrincipalAmount)
			}
		})
	}
}

func TestConvertToInstallmentData(t *testing.T) {
	tests := []struct {
		name    string
		inst    *model.Installment
		wantErr bool
	}{
		{
			name:    "nil installment",
			inst:    nil,
			wantErr: true,
		},
		{
			name: "installment with nil info",
			inst: &model.Installment{
				Info:   nil,
				Repays: []*model.InstallmentRepay{},
			},
			wantErr: true,
		},
		{
			name: "installment with nil repays",
			inst: &model.Installment{
				Info:   &model.InstallmentInfo{ID: 123},
				Repays: nil,
			},
			wantErr: true,
		},
		{
			name:    "valid installment",
			inst:    createTestInstallmentForTransform(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertToInstallmentData(tt.inst)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.inst.Info.GetInstID(), result.Id)
				assert.Equal(t, tt.inst.Info.GetTenure(), result.Tenure)
				assert.Equal(t, convertToInstallmentStatus(tt.inst.Info.Status), result.Status)
			}
		})
	}
}

func TestConvertToInstallmentStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   model.InstallmentStatus
		expected v1Inst.InstallmentStatus
	}{
		{
			name:     "open status",
			status:   model.InstallmentStatusOpen,
			expected: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_OPEN,
		},
		{
			name:     "init status",
			status:   model.InstallmentStatusInit,
			expected: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_INIT,
		},
		{
			name:     "closed status",
			status:   model.InstallmentStatusClosed,
			expected: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_CLOSED,
		},
		{
			name:     "unknown status",
			status:   model.InstallmentStatusUnknown,
			expected: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED,
		},
		{
			name:     "unmapped status defaults to unspecified",
			status:   model.InstallmentStatus("unmapped"),
			expected: v1Inst.InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToInstallmentStatus(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToInstRepaySchedules(t *testing.T) {
	now := time.Now()
	repaySchedules := []*model.InstallmentRepay{
		{
			SeqNo:        1,
			Status:       model.RepayScheduleStatusDue,
			RepayDueDate: now,
			GraceDueDate: now.AddDate(0, 0, 7),
			EmiAmount:    100000,
			PenaltyDetails: &model.InstallmentRepayPenalty{
				TotalPenalty: 5000,
			},
			OutstandingDetails: &model.InstallmentRepayOutstanding{
				OutstandingAmount: 50000,
			},
		},
		{
			SeqNo:        2,
			Status:       model.RepayScheduleStatusPaid,
			RepayDueDate: now.AddDate(0, 1, 0),
			GraceDueDate: now.AddDate(0, 1, 7),
			EmiAmount:    100000,
			PenaltyDetails: &model.InstallmentRepayPenalty{
				TotalPenalty: 0,
			},
			OutstandingDetails: &model.InstallmentRepayOutstanding{
				OutstandingAmount: 0,
			},
		},
	}

	result := convertToInstRepaySchedules(repaySchedules)

	assert.Len(t, result, 2)
	assert.Equal(t, repaySchedules[0].SeqNo, result[0].SeqNo)
	assert.Equal(t, now.Format("2006-01-02"), result[0].DueDate)
	assert.Equal(t, repaySchedules[0].EmiAmount, result[0].DueAmount)
	assert.Equal(t, repaySchedules[0].PenaltyDetails.TotalPenalty, result[0].PenaltyAmount)
}

func TestConvertToRepaymentStatus(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name     string
		status   model.RepayScheduleStatus
		dueDate  time.Time
		expected v1Inst.RepayStatus
	}{
		{
			name:     "due status - not overdue",
			status:   model.RepayScheduleStatusDue,
			dueDate:  now.AddDate(0, 0, 1), // tomorrow
			expected: v1Inst.RepayStatus_REPAY_STATUS_DUE,
		},
		{
			name:     "due status - overdue",
			status:   model.RepayScheduleStatusDue,
			dueDate:  now.AddDate(0, 0, -1), // yesterday
			expected: v1Inst.RepayStatus_REPAY_STATUS_OVERDUE,
		},
		{
			name:     "paid status",
			status:   model.RepayScheduleStatusPaid,
			dueDate:  now,
			expected: v1Inst.RepayStatus_REPAY_STATUS_PAID,
		},
		{
			name:     "init status",
			status:   model.RepayScheduleStatusInit,
			dueDate:  now,
			expected: v1Inst.RepayStatus_REPAY_STATUS_PENDING,
		},
		{
			name:     "unknown status",
			status:   model.RepayScheduleStatusUnknown,
			dueDate:  now,
			expected: v1Inst.RepayStatus_REPAY_STATUS_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToRepaymentStatus(tt.status, tt.dueDate)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToEarlyDischarge(t *testing.T) {
	tests := []struct {
		name string
		ed   *dto.EarlyDischargeNormalize
		want *v1Inst.EarlyDischarge
	}{
		{
			name: "nil early discharge",
			ed:   nil,
			want: nil,
		},
		{
			name: "closed installment",
			ed: &dto.EarlyDischargeNormalize{
				RefInstInfo: &model.InstallmentInfo{
					Status:          model.InstallmentStatusClosed,
					DischargeStatus: model.DischargeStatusComplete,
				},
			},
			want: &v1Inst.EarlyDischarge{
				Info: &v1Inst.EarlyDischargeBase{
					Kind:    v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED,
					Status:  v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED,
					Allowed: &[]bool{false}[0],
				},
			},
		},
		{
			name: "normal early discharge",
			ed: &dto.EarlyDischargeNormalize{
				InSession:             true,
				EarlyDischargeKind:    model.EarlyDischargeKindNormal,
				DischargeStatus:       model.DischargeStatusPending,
				EarlyDischargeAllowed: true,
				TotalDischargeAmount:  1000000,
				AdjustDischargeAmount: 950000,
				EarlyDischargeFee:     50000,
				RefInstInfo: &model.InstallmentInfo{
					Status:          model.InstallmentStatusOpen,
					DischargeStatus: model.DischargeStatusPending,
					EarlyDischarge: &model.InstallmentEarlyDischarge{
						Details: &model.EarlyDischargeDetails{
							TotalDischargeAmount: 1000000,
							EarlyDischargeFee:    50000,
							OutstandingPrincipal: &[]int64{800000}[0],
							OutstandingInterest:  &[]int64{100000}[0],
							OutstandingPenalty:   &[]int64{50000}[0],
						},
					},
				},
			},
			want: &v1Inst.EarlyDischarge{
				Info:   &v1Inst.EarlyDischargeBase{},
				Detail: &v1Inst.EarlyDischargeDetail{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischarge(tt.ed)
			if tt.want == nil {
				assert.Nil(t, result)
			} else if tt.want.Info != nil && tt.want.Info.Status == v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED {
				// For closed installment case
				assert.NotNil(t, result)
				assert.NotNil(t, result.Info)
				assert.Equal(t, tt.want.Info.Status, result.Info.Status)
				assert.Equal(t, *tt.want.Info.Allowed, *result.Info.Allowed)
			} else {
				// For normal case
				assert.NotNil(t, result)
				assert.NotNil(t, result.Info)
				assert.NotNil(t, result.Detail)
			}
		})
	}
}

func TestBuildCompleteEarlyDischargeInfo(t *testing.T) {
	result := buildCompleteEarlyDischargeInfo()

	assert.NotNil(t, result)
	assert.Equal(t, v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED, result.Kind)
	assert.Equal(t, v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED, result.Status)
	assert.NotNil(t, result.Allowed)
	assert.False(t, *result.Allowed)
}

// Helper function to create test installment for transform tests
func createTestInstallmentForTransform() *model.Installment {
	return &model.Installment{
		Info: &model.InstallmentInfo{
			ID:        123,
			ZPTransID: 456,
			Tenure:    3,
			Status:    model.InstallmentStatusOpen,
			SimulationInfo: model.InstallmentSimulation{
				InterestRate:    0.15,
				PrincipalAmount: 1000000,
				InterestAmount:  150000,
			},
		},
		Repays: []*model.InstallmentRepay{
			{
				SeqNo:     1,
				EmiAmount: 100000,
				PenaltyDetails: &model.InstallmentRepayPenalty{
					TotalPenalty: 5000,
				},
				OutstandingDetails: &model.InstallmentRepayOutstanding{
					OutstandingAmount: 50000,
				},
			},
		},
	}
}

func TestConvertToEarlyDischargeBase(t *testing.T) {
	sessionTime := time.Now()
	tests := []struct {
		name string
		ed   *dto.EarlyDischargeNormalize
		want *v1Inst.EarlyDischargeBase
	}{
		{
			name: "nil early discharge",
			ed:   nil,
			want: nil,
		},
		{
			name: "early discharge with session",
			ed: &dto.EarlyDischargeNormalize{
				EarlyDischargeKind:    model.EarlyDischargeKindNormal,
				DischargeStatus:       model.DischargeStatusPending,
				EarlyDischargeAllowed: true,
				InSession:             true,
				SessionInfo: &dto.Session{
					StartTime: &sessionTime,
					EndTime:   &sessionTime,
				},
			},
			want: &v1Inst.EarlyDischargeBase{},
		},
		{
			name: "early discharge without session",
			ed: &dto.EarlyDischargeNormalize{
				EarlyDischargeKind:    model.EarlyDischargeKindRefund,
				DischargeStatus:       model.DischargeStatusProcessing,
				EarlyDischargeAllowed: false,
				InSession:             false,
			},
			want: &v1Inst.EarlyDischargeBase{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischargeBase(tt.ed)
			if tt.want == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, convertToEarlyDischargeKind(tt.ed.EarlyDischargeKind), result.Kind)
				assert.Equal(t, convertToEarlyDischargeStatus(tt.ed.DischargeStatus), result.Status)
				assert.Equal(t, tt.ed.EarlyDischargeAllowed, *result.Allowed)
				assert.Equal(t, tt.ed.InSession, *result.InSession)
			}
		})
	}
}

func TestConvertToEarlyDischargeDetail(t *testing.T) {
	tests := []struct {
		name string
		ed   *dto.EarlyDischargeNormalize
		want *v1Inst.EarlyDischargeDetail
	}{
		{
			name: "nil installment info",
			ed: &dto.EarlyDischargeNormalize{
				RefInstInfo: nil,
			},
			want: nil,
		},
		{
			name: "installment with nil early discharge",
			ed: &dto.EarlyDischargeNormalize{
				RefInstInfo: &model.InstallmentInfo{
					EarlyDischarge: nil,
				},
			},
			want: nil,
		},
		{
			name: "installment with nil early discharge details",
			ed: &dto.EarlyDischargeNormalize{
				RefInstInfo: &model.InstallmentInfo{
					EarlyDischarge: &model.InstallmentEarlyDischarge{
						Details: nil,
					},
				},
			},
			want: nil,
		},
		{
			name: "valid early discharge detail",
			ed: &dto.EarlyDischargeNormalize{
				TotalDischargeAmount:  1000000,
				AdjustDischargeAmount: 950000,
				EarlyDischargeFee:     50000,
				EarlyDischargeOuts:    900000,
				RefInstInfo: &model.InstallmentInfo{
					EarlyDischarge: &model.InstallmentEarlyDischarge{
						Details: &model.EarlyDischargeDetails{
							TotalDischargeAmount: 1000000,
							EarlyDischargeFee:    50000,
							OutstandingPrincipal: &[]int64{800000}[0],
							OutstandingInterest:  &[]int64{100000}[0],
							OutstandingPenalty:   &[]int64{50000}[0],
						},
					},
				},
			},
			want: &v1Inst.EarlyDischargeDetail{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischargeDetail(tt.ed)
			if tt.want == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.ed.TotalDischargeAmount, result.TotalDischargeAmount)
				assert.Equal(t, tt.ed.AdjustDischargeAmount, result.EarlyDischargeAmount)
				assert.Equal(t, tt.ed.EarlyDischargeFee, result.EarlyDischargeFee)
				assert.Equal(t, tt.ed.EarlyDischargeOuts, result.TotalOutstandingAmount)
			}
		})
	}
}

func TestConvertToEarlyDischargeForRefund(t *testing.T) {
	tests := []struct {
		name string
		ed   *dto.EarlyDischargeNormalize
		want *v1Inst.EarlyDischargeForRefund
	}{
		{
			name: "nil early discharge",
			ed:   nil,
			want: nil,
		},
		{
			name: "valid early discharge for refund",
			ed: &dto.EarlyDischargeNormalize{
				DischargeStatus:      model.DischargeStatusPending,
				TotalDischargeAmount: 1000000,
				InSession:            true,
			},
			want: &v1Inst.EarlyDischargeForRefund{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischargeForRefund(tt.ed)
			if tt.want == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, convertToEarlyDischargeStatus(tt.ed.DischargeStatus), result.Status)
				assert.Equal(t, tt.ed.TotalDischargeAmount, result.DischargeAmount)
				assert.Equal(t, tt.ed.InSession, result.SessionAvailable)
			}
		})
	}
}

func TestConvertToEarlyDischargeKind(t *testing.T) {
	tests := []struct {
		name     string
		kind     model.EarlyDischargeKind
		expected v1Inst.EarlyDischargeKind
	}{
		{
			name:     "unknown kind",
			kind:     model.EarlyDischargeKindUnknown,
			expected: v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED,
		},
		{
			name:     "normal kind",
			kind:     model.EarlyDischargeKindNormal,
			expected: v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_NORMAL,
		},
		{
			name:     "refund kind",
			kind:     model.EarlyDischargeKindRefund,
			expected: v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_REFUND,
		},
		{
			name:     "unmapped kind defaults to unspecified",
			kind:     model.EarlyDischargeKind("unmapped"),
			expected: v1Inst.EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischargeKind(tt.kind)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertToEarlyDischargeStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   model.DischargeStatus
		expected v1Inst.EarlyDischargeStatus
	}{
		{
			name:     "unknown status",
			status:   model.DischargeStatusUnknown,
			expected: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED,
		},
		{
			name:     "pending status",
			status:   model.DischargeStatusPending,
			expected: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_ELIGIBLE,
		},
		{
			name:     "processing status",
			status:   model.DischargeStatusProcessing,
			expected: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_PROCESSING,
		},
		{
			name:     "complete status",
			status:   model.DischargeStatusComplete,
			expected: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED,
		},
		{
			name:     "unmapped status defaults to unspecified",
			status:   model.DischargeStatus("unmapped"),
			expected: v1Inst.EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToEarlyDischargeStatus(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBuildPaymentPlanMetadata(t *testing.T) {
	tests := []struct {
		name      string
		moduleFed *model.ModuleFederation
		wantErr   bool
	}{
		{
			name:      "nil module federation",
			moduleFed: nil,
			wantErr:   false,
		},
		{
			name: "valid module federation",
			moduleFed: &model.ModuleFederation{
				Scope:  "test-scope",
				Module: "test-module",
				Entry:  "https://example.com/entry",
				Props:  map[string]any{"key": "value"},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := buildPaymentPlanMetadata(tt.moduleFed)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.moduleFed != nil {
					fields := result.GetFields()
					assert.Contains(t, fields, "plan_detail_mf")
				}
			}
		})
	}
}

func TestBuildPaymentPlanInteraction(t *testing.T) {
	action := &model.Action{
		Type:   model.CTATypePlanSelection,
		Title:  "Select Plan",
		ZpiUrl: "zpi://test",
		ZpaUrl: "zpa://test",
	}
	message := &model.Message{
		Text:  "Test message",
		Color: "#FF0000",
	}
	notice := &model.Message{
		Text:  "Test notice",
		Color: "#00FF00",
	}

	result := buildPaymentPlanInteraction(action, message, notice)

	assert.NotNil(t, result)
	assert.NotNil(t, result.Action)
	assert.Equal(t, action.Title, result.Action.Text)
	assert.NotNil(t, result.Message)
	assert.Equal(t, message.Text, result.Message.Text)
	assert.NotNil(t, result.Notice)
	assert.Equal(t, notice.Text, result.Notice.Text)
}

func TestConvertToTransportError(t *testing.T) {
	tests := []struct {
		name string
		err  error
	}{
		{
			name: "kratos error",
			err:  fmt.Errorf("test error"),
		},
		{
			name: "errorkit error",
			err: &errorkit.ErrorInfo{
				Kind:     errorkit.TypeBadRequest,
				Code:     errorkit.CodeBadRequest,
				Message:  "bad request",
				Metadata: map[string]any{"field": "value"},
			},
		},
		{
			name: "generic error",
			err:  fmt.Errorf("generic error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToTransportError(tt.err)
			assert.NotNil(t, result)
			assert.Contains(t, result.Error(), tt.err.Error())
		})
	}
}

func TestConvertErrorInfoToTransportError(t *testing.T) {
	tests := []struct {
		name     string
		errInfo  *errorkit.ErrorInfo
		expected string
	}{
		{
			name: "bad request error",
			errInfo: &errorkit.ErrorInfo{
				Kind:     errorkit.TypeBadRequest,
				Code:     errorkit.CodeBadRequest,
				Message:  "bad request",
				Metadata: map[string]any{"field": "value"},
			},
			expected: "BAD_REQUEST",
		},
		{
			name: "not found error",
			errInfo: &errorkit.ErrorInfo{
				Kind:    errorkit.TypeNotFound,
				Code:    errorkit.CodeDataNotFound,
				Message: "not found",
			},
			expected: "NOT_FOUND",
		},
		{
			name: "unmapped error type",
			errInfo: &errorkit.ErrorInfo{
				Kind:    errorkit.Kind("unmapped"),
				Code:    errorkit.CodeInternalError,
				Message: "unmapped error",
			},
			expected: "INTERNAL_ERROR",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertErrorInfoToTransportError(tt.errInfo)
			assert.NotNil(t, result)
			assert.Contains(t, result.Reason, tt.expected)
			assert.Contains(t, result.Message, tt.errInfo.Message)
		})
	}
}
