package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/statement/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (s *StatementService) GetClientStatement(ctx context.Context,
	req *v1.GetClientStatementRequest) (*v1.GetClientStatementResponse, error) {
	logger := s.logger.WithContext(ctx)
	session, _ := auth.FromContext(ctx)

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	var err error
	var stmtInfo *model.Statement
	accountID := req.AccountId
	zalopayID := cast.ToInt64(session.GetZalopayId())

	switch req.QueryBy.(type) {
	case *v1.GetClientStatementRequest_StatementId:
		stmtID := req.GetStatementId()
		stmtInfo, err = s.stmtUc.GetStatementByID(ctx, stmtID)
	case *v1.GetClientStatementRequest_StatementDate:
		stmtDate, pErr := time.Parse("2006-01-02", req.GetStatementDate())
		if pErr != nil {
			return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), pErr.Error())
		}
		stmtInfo, err = s.stmtUc.GetStatementByUserAndIncurDate(ctx, &dto.QueryStatementParams{
			ZalopayID:     zalopayID,
			AccountID:     accountID,
			StatementDate: stmtDate,
		})
	default:
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "query_by must be provided")
	}

	if err != nil {
		logger.Errorf("failed to get statement: %v", err)
		return nil, convertToTransportError(err)
	}

	if stmtInfo.ZalopayID != zalopayID || stmtInfo.AccountID != accountID {
		logger.Errorw("msg", "statement not match with user cred",
			"zalopayIDReq", zalopayID, "accountIDReq", accountID,
			"zalopayIDStmt", stmtInfo.ZalopayID, "accountIDStmt", stmtInfo.AccountID)
		return nil, errors.Forbidden(errorkit.CodeStatementNotFound.String(), "statement not match with user info")
	}

	return &v1.GetClientStatementResponse{
		Statement: convertToStatementInfo(stmtInfo),
	}, nil
}

func (s *StatementService) GetClientLatestStatement(ctx context.Context,
	req *v1.GetClientLatestStatementRequest) (*v1.GetClientLatestStatementResponse, error) {
	session, _ := auth.FromContext(ctx)

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	accountID := req.AccountId
	zalopayID := cast.ToInt64(session.GetZalopayId())

	params := &dto.QueryStatementParams{
		ZalopayID: zalopayID,
		AccountID: accountID,
	}
	stmtInfo, err := s.stmtUc.GetLatestStatementByUser(ctx, params)
	if errors.Is(err, errorkit.NewWithCode(errorkit.CodeStatementNotFound)) {
		return &v1.GetClientLatestStatementResponse{}, nil
	}
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.GetClientLatestStatementResponse{
		Statement: convertToStatementInfo(stmtInfo),
	}, nil
}

func (s *StatementService) ListClientStatements(ctx context.Context,
	req *v1.ListClientStatementsRequest) (*v1.ListClientStatementsResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	accountID := req.AccountId

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	fromTime := req.FromTime.AsTime()
	toTime := req.ToTime.AsTime()
	if fromTime.IsZero() || toTime.IsZero() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "from_time and to_time must be provided")
	}
	if fromTime.After(toTime) {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "from_time must be before to_time")
	}
	if toTime.Sub(fromTime) > time.Hour*24*365*6 {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "date range must be less than 6 year")
	}

	listStmt, err := s.stmtUc.ListStatementsByUserAndIncurDateRange(ctx, &dto.QueryListStatementsParams{
		ZalopayID:         zalopayID,
		AccountID:         accountID,
		StatementDateFrom: fromTime,
		StatementDateTo:   toTime,
	})
	if err != nil {
		s.logger.Errorf("failed to get user statements: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.ListClientStatementsResponse{
		Statements: convertToStatementList(listStmt),
	}, nil
}

func (s *StatementService) ListClientInstallments(ctx context.Context,
	req *v1.ListClientInstallmentsRequest) (*v1.ListClientInstallmentsResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.QueryStatementInstallmentsParams{
		ZalopayID:   zalopayID,
		AccountID:   req.AccountId,
		StatementID: req.StatementId,
	}
	listInst, err := s.stmtUc.GetInstallmentsByUserAndStatementID(ctx, params)
	if err != nil {
		s.logger.Errorf("failed to get statement installments: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.ListClientInstallmentsResponse{
		Installments: convertToStatementInstallments(listInst),
	}, nil
}
