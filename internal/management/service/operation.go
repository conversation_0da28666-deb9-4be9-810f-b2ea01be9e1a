package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/errors"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *OperationService) TriggerSyncInstallments(ctx context.Context,
	_ *v1.TriggerSyncInstallmentsRequest) (*v1.TriggerSyncInstallmentsResponse, error) {
	if err := s.instUc.TriggerSyncInstallmentDaily(ctx); err != nil {
		return nil, convertToTransportError(err)
	}
	return &v1.TriggerSyncInstallmentsResponse{}, nil
}

func (s *OperationService) MockStatementPeriod(_ context.Context,
	req *v1.MockStatementPeriodRequest) (*v1.MockStatementPeriodResponse, error) {
	stmtDate, err := time.Parse("2006-01-02", req.GetStatementDate())
	if err != nil {
		return nil, convertToTransportError(err)
	}

	if err = s.stmtUc.MockStatementDate(stmtDate); err != nil {
		return nil, convertToTransportError(err)
	}

	stmtDateOverdue, err := time.Parse("2006-01-02", req.GetStatementDateOverdue())
	if err != nil {
		return nil, convertToTransportError(err)
	}
	if err = s.stmtUc.MockStatementDateOverdue(stmtDateOverdue); err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.MockStatementPeriodResponse{}, nil
}

func (s *OperationService) SetMaintenanceState(ctx context.Context,
	req *v1.SetMaintenanceStateRequest) (*v1.SetMaintenanceStateResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.PartnerCode)
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	mData := dto.MaintenanceState{
		All:        req.State.All,
		Onboarding: req.State.Onboarding,
		Purchase:   req.State.Purchase,
		Repayment:  req.State.Repayment,
	}
	params := &dto.MaintenanceData{
		PartnerCode:      partnerCode,
		MaintenanceState: mData,
	}
	if err := s.maintUc.SetMaintenance(ctx, params); err != nil {
		return nil, convertToTransportError(err)
	}
	return &v1.SetMaintenanceStateResponse{}, nil
}

func (s *OperationService) GetMaintenanceState(ctx context.Context,
	req *v1.GetMaintenanceStateRequest) (*v1.GetMaintenanceStateResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	partnerCode := partner.PartnerCode(req.PartnerCode)
	if !partnerCode.IsValid() {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid partner code")
	}

	resp, err := s.maintUc.GetMaintenance(ctx, partnerCode)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	mState := resp.MaintenanceState
	mPartner := resp.PartnerCode.String()
	respData := &v1.GetMaintenanceStateResponse{
		PartnerCode: mPartner,
		State: &v1.MaintenanceState{
			All:        mState.All,
			Onboarding: mState.Onboarding,
			Purchase:   mState.Purchase,
			Repayment:  mState.Repayment,
		},
	}
	return respData, nil
}
