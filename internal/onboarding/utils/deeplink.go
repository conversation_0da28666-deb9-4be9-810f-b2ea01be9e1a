package utils

import (
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
)

//go:generate mockgen --destination=./mocks/deeplink.go --package=utils_mocks . DeepLinkCraft
type DeepLinkCraft interface {
	GetDoingKycLink() model.DeepLinkData
	GetUpdateKycLink() model.DeepLinkData
	GetUpdateNfcLink() model.DeepLinkData
}

type DeepLinks map[model.DeepLinkKey]model.DeepLinkData

type deepLinkDeps struct {
	deeplinks *config.Onboarding_Deeplinks
}

func NewDeepLinkCraft(config *config.Onboarding) DeepLinkCraft {
	deepLinksConf := config.GetResources().GetDeeplinks()
	return &deepLinkDeps{deeplinks: deepLinksConf}
}

func (d deepLinkDeps) GetDoingKycLink() model.DeepLinkData {
	return model.DeepLinkData{
		ZPAUrl:    d.deeplinks.GetKycUpdate().GetZpaUrl(),
		ZPIUrl:    d.deeplinks.GetKycUpdate().GetZpiUrl(),
		CommonUrl: d.deeplinks.GetKycUpdate().GetCommonUrl(),
	}
}

func (d deepLinkDeps) GetUpdateKycLink() model.DeepLinkData {
	return model.DeepLinkData{
		ZPAUrl:    d.deeplinks.GetKycUpdate().GetZpaUrl(),
		ZPIUrl:    d.deeplinks.GetKycUpdate().GetZpiUrl(),
		CommonUrl: d.deeplinks.GetKycUpdate().GetCommonUrl(),
	}
}

func (d deepLinkDeps) GetUpdateNfcLink() model.DeepLinkData {
	return model.DeepLinkData{
		ZPAUrl:    d.deeplinks.GetNfcUpdate().GetZpaUrl(),
		ZPIUrl:    d.deeplinks.GetNfcUpdate().GetZpiUrl(),
		CommonUrl: d.deeplinks.GetNfcUpdate().GetCommonUrl(),
	}
}
