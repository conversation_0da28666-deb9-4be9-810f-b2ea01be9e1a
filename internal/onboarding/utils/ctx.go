package utils

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type ctxKey struct {
	name string
}

var ctxKeyClientSession = &ctxKey{}

func AttachClientSessionToCtx(ctx context.Context, location *model.ClientSession) context.Context {
	return context.WithValue(ctx, ctxKeyClientSession, &model.ClientSession{
		DeviceID:  location.DeviceID,
		Latitude:  location.Latitude,
		Longitude: location.Longitude,
		RequestIP: location.RequestIP,
	})
}

func ClientSessionFromCtx(ctx context.Context) *model.ClientSession {
	location, ok := ctx.Value(ctxKeyClientSession).(*model.ClientSession)
	if !ok {
		return &model.ClientSession{}
	}
	return location
}
