package utils

import (
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type ctaDeps struct {
	deeplinkDraft DeepLinkCraft
}

//go:generate mockgen --destination=./mocks/cta.go --package=utils_mocks . CTACrafter
type CTACrafter interface {
	GetCTARetry() model.NoticeAction
	GetCTADoKyc() model.NoticeAction
	GetCTAContactCS() model.NoticeAction
	GetCTAUpdateKyc() model.NoticeAction
	GetCTAUpdateNfc() model.NoticeAction
	GetCTAResetNfc() model.NoticeAction
	GetCTALinkAccount() model.NoticeAction
	GetCTAUpdateKycNfc() model.NoticeAction
	GetCTADetermineKyc(missingNfc bool) model.NoticeAction
	GetCTACloseNotice(opts ...CTAOption) model.NoticeAction
	GetCTADiscoverMore(opts ...CTAOption) model.NoticeAction
	GetCTAContactPartner(opts ...CTAOption) model.NoticeAction
	GetCTARegisterOnboarding() model.NoticeAction
	GetCTAUpdatePhoneNumber() model.NoticeAction
	GetCTAUnderstand() model.NoticeAction
	GetCTAReRegisterOnboarding() model.NoticeAction

	// Options
	WithPrimaryBtn() CTAOption
	WithOutlinedBtn() CTAOption
}

func NewCTACraft(deeplinkDraft DeepLinkCraft) CTACrafter {
	return &ctaDeps{deeplinkDraft: deeplinkDraft}
}

type CTAOption func(action model.NoticeAction) model.NoticeAction

func (c *ctaDeps) WithPrimaryBtn() CTAOption {
	return func(action model.NoticeAction) model.NoticeAction {
		action.Variant = model.CTAVariantPrimary
		return action
	}
}

func (c *ctaDeps) WithOutlinedBtn() CTAOption {
	return func(action model.NoticeAction) model.NoticeAction {
		action.Variant = model.CTAVariantOutlined
		return action
	}
}

func (c *ctaDeps) GetCTAContactCS() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeContactCS,
		Title:   model.CTATitleContactCS,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTAContactPartner(opts ...CTAOption) model.NoticeAction {
	result := model.NoticeAction{
		Code:    model.CTACodeContactPartner,
		Title:   model.CTATitleContactPartnerCIMB,
		Variant: model.CTAVariantPrimary,
	}
	for _, opt := range opts {
		result = opt(result)
	}
	return result
}

func (c *ctaDeps) GetCTADiscoverMore(opts ...CTAOption) model.NoticeAction {
	result := model.NoticeAction{
		Code:    model.CTACodeNavigateToHome,
		Title:   model.CTATitleDiscoverMore,
		Variant: model.CTAVariantOutlined,
	}
	for _, opt := range opts {
		result = opt(result)
	}
	return result
}

func (c *ctaDeps) GetCTADoKyc() model.NoticeAction {
	return model.NoticeAction{
		Code:         model.CTACodeDoKyc,
		Title:        model.CTATitleDoKyc,
		Variant:      model.CTAVariantPrimary,
		ZpaActionURL: c.deeplinkDraft.GetDoingKycLink().ZPAUrl,
		ZpiActionURL: c.deeplinkDraft.GetDoingKycLink().ZPIUrl,
	}
}

func (c *ctaDeps) GetCTAUpdateKyc() model.NoticeAction {
	return model.NoticeAction{
		Code:         model.CTACodeUpdateKyc,
		Title:        model.CTATitleUpdateKyc,
		Variant:      model.CTAVariantPrimary,
		ZpaActionURL: c.deeplinkDraft.GetUpdateKycLink().ZPAUrl,
		ZpiActionURL: c.deeplinkDraft.GetUpdateKycLink().ZPIUrl,
	}
}

func (c *ctaDeps) GetCTAResetNfc() model.NoticeAction {
	return model.NoticeAction{
		Code:         model.CTACodeResetNfc,
		Title:        model.CTATitleScanIdentityCard,
		Variant:      model.CTAVariantPrimary,
		ZpaActionURL: c.deeplinkDraft.GetUpdateNfcLink().ZPAUrl,
		ZpiActionURL: c.deeplinkDraft.GetUpdateNfcLink().ZPIUrl,
	}
}

func (c *ctaDeps) GetCTAUpdateNfc() model.NoticeAction {
	return model.NoticeAction{
		Code:         model.CTACodeUpdateNfc,
		Title:        model.CTATitleScanIdentityCard,
		Variant:      model.CTAVariantPrimary,
		ZpaActionURL: c.deeplinkDraft.GetUpdateNfcLink().ZPAUrl,
		ZpiActionURL: c.deeplinkDraft.GetUpdateNfcLink().ZPIUrl,
	}
}

func (c *ctaDeps) GetCTAUpdateKycNfc() model.NoticeAction {
	return model.NoticeAction{
		Code:         model.CTACodeUpdateKycNfc,
		Title:        model.CTATitleUpdateKyc,
		Variant:      model.CTAVariantPrimary,
		ZpaActionURL: c.deeplinkDraft.GetUpdateKycLink().ZPAUrl,
		ZpiActionURL: c.deeplinkDraft.GetUpdateKycLink().ZPIUrl,
	}
}

func (c *ctaDeps) GetCTADetermineKyc(missingNfc bool) model.NoticeAction {
	if missingNfc {
		return c.GetCTAUpdateKycNfc()
	}
	return c.GetCTAUpdateKyc()
}

func (c *ctaDeps) GetCTACloseNotice(opts ...CTAOption) model.NoticeAction {
	result := model.NoticeAction{
		Code:    model.CTACodeCloseNotice,
		Title:   model.CTATitleClose,
		Variant: model.CTAVariantPrimary,
	}
	for _, opt := range opts {
		result = opt(result)
	}
	return result
}

func (c *ctaDeps) GetCTAUpdatePhoneNumber() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeUpdatePhone,
		Title:   model.CTATitleUpdatePhone,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTARegisterOnboarding() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeRegister,
		Title:   model.CTATitleRegister,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTARetry() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeRetry,
		Title:   model.CTATitleRetry,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTAUnderstand() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeCloseNotice,
		Title:   model.CTATitleUnderstand,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTAReRegisterOnboarding() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeReRegister,
		Title:   model.CTATitleReRegister,
		Variant: model.CTAVariantPrimary,
	}
}

func (c *ctaDeps) GetCTALinkAccount() model.NoticeAction {
	return model.NoticeAction{
		Code:    model.CTACodeLinkAccount,
		Title:   model.CTATitleLinkAccount,
		Variant: model.CTAVariantPrimary,
	}
}
