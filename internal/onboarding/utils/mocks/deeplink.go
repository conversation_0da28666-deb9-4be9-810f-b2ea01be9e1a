// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils (interfaces: DeepLinkCraft)
//
// Generated by this command:
//
//	mockgen --destination=./mocks/deeplink.go --package=utils_mocks . DeepLinkCraft
//

// Package utils_mocks is a generated GoMock package.
package utils_mocks

import (
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/model"
	gomock "go.uber.org/mock/gomock"
)

// MockDeepLinkCraft is a mock of DeepLinkCraft interface.
type MockDeepLinkCraft struct {
	ctrl     *gomock.Controller
	recorder *MockDeepLinkCraftMockRecorder
	isgomock struct{}
}

// MockDeepLinkCraftMockRecorder is the mock recorder for MockDeepLinkCraft.
type MockDeepLinkCraftMockRecorder struct {
	mock *MockDeepLinkCraft
}

// NewMockDeepLinkCraft creates a new mock instance.
func NewMockDeepLinkCraft(ctrl *gomock.Controller) *MockDeepLinkCraft {
	mock := &MockDeepLinkCraft{ctrl: ctrl}
	mock.recorder = &MockDeepLinkCraftMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeepLinkCraft) EXPECT() *MockDeepLinkCraftMockRecorder {
	return m.recorder
}

// GetDoingKycLink mocks base method.
func (m *MockDeepLinkCraft) GetDoingKycLink() model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDoingKycLink")
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetDoingKycLink indicates an expected call of GetDoingKycLink.
func (mr *MockDeepLinkCraftMockRecorder) GetDoingKycLink() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDoingKycLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetDoingKycLink))
}

// GetUpdateKycLink mocks base method.
func (m *MockDeepLinkCraft) GetUpdateKycLink() model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdateKycLink")
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetUpdateKycLink indicates an expected call of GetUpdateKycLink.
func (mr *MockDeepLinkCraftMockRecorder) GetUpdateKycLink() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdateKycLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetUpdateKycLink))
}

// GetUpdateNfcLink mocks base method.
func (m *MockDeepLinkCraft) GetUpdateNfcLink() model.DeepLinkData {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpdateNfcLink")
	ret0, _ := ret[0].(model.DeepLinkData)
	return ret0
}

// GetUpdateNfcLink indicates an expected call of GetUpdateNfcLink.
func (mr *MockDeepLinkCraftMockRecorder) GetUpdateNfcLink() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpdateNfcLink", reflect.TypeOf((*MockDeepLinkCraft)(nil).GetUpdateNfcLink))
}
