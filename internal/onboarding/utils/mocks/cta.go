// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils (interfaces: CTACrafter)
//
// Generated by this command:
//
//	mockgen --destination=./mocks/cta.go --package=utils_mocks . CTACrafter
//

// Package utils_mocks is a generated GoMock package.
package utils_mocks

import (
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	utils "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	gomock "go.uber.org/mock/gomock"
)

// MockCTACrafter is a mock of CTACrafter interface.
type MockCTACrafter struct {
	ctrl     *gomock.Controller
	recorder *MockCTACrafterMockRecorder
	isgomock struct{}
}

// MockCTACrafterMockRecorder is the mock recorder for MockCTACrafter.
type MockCTACrafterMockRecorder struct {
	mock *MockCTACrafter
}

// NewMockCTACrafter creates a new mock instance.
func NewMockCTACrafter(ctrl *gomock.Controller) *MockCTACrafter {
	mock := &MockCTACrafter{ctrl: ctrl}
	mock.recorder = &MockCTACrafterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCTACrafter) EXPECT() *MockCTACrafterMockRecorder {
	return m.recorder
}

// GetCTACloseNotice mocks base method.
func (m *MockCTACrafter) GetCTACloseNotice(opts ...utils.CTAOption) model.NoticeAction {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCTACloseNotice", varargs...)
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTACloseNotice indicates an expected call of GetCTACloseNotice.
func (mr *MockCTACrafterMockRecorder) GetCTACloseNotice(opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTACloseNotice", reflect.TypeOf((*MockCTACrafter)(nil).GetCTACloseNotice), opts...)
}

// GetCTAContactCS mocks base method.
func (m *MockCTACrafter) GetCTAContactCS() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAContactCS")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAContactCS indicates an expected call of GetCTAContactCS.
func (mr *MockCTACrafterMockRecorder) GetCTAContactCS() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAContactCS", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAContactCS))
}

// GetCTAContactPartner mocks base method.
func (m *MockCTACrafter) GetCTAContactPartner(opts ...utils.CTAOption) model.NoticeAction {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCTAContactPartner", varargs...)
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAContactPartner indicates an expected call of GetCTAContactPartner.
func (mr *MockCTACrafterMockRecorder) GetCTAContactPartner(opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAContactPartner", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAContactPartner), opts...)
}

// GetCTADetermineKyc mocks base method.
func (m *MockCTACrafter) GetCTADetermineKyc(missingNfc bool) model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTADetermineKyc", missingNfc)
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTADetermineKyc indicates an expected call of GetCTADetermineKyc.
func (mr *MockCTACrafterMockRecorder) GetCTADetermineKyc(missingNfc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTADetermineKyc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTADetermineKyc), missingNfc)
}

// GetCTADiscoverMore mocks base method.
func (m *MockCTACrafter) GetCTADiscoverMore(opts ...utils.CTAOption) model.NoticeAction {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCTADiscoverMore", varargs...)
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTADiscoverMore indicates an expected call of GetCTADiscoverMore.
func (mr *MockCTACrafterMockRecorder) GetCTADiscoverMore(opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTADiscoverMore", reflect.TypeOf((*MockCTACrafter)(nil).GetCTADiscoverMore), opts...)
}

// GetCTADoKyc mocks base method.
func (m *MockCTACrafter) GetCTADoKyc() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTADoKyc")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTADoKyc indicates an expected call of GetCTADoKyc.
func (mr *MockCTACrafterMockRecorder) GetCTADoKyc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTADoKyc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTADoKyc))
}

// GetCTALinkAccount mocks base method.
func (m *MockCTACrafter) GetCTALinkAccount() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTALinkAccount")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTALinkAccount indicates an expected call of GetCTALinkAccount.
func (mr *MockCTACrafterMockRecorder) GetCTALinkAccount() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTALinkAccount", reflect.TypeOf((*MockCTACrafter)(nil).GetCTALinkAccount))
}

// GetCTAReRegisterOnboarding mocks base method.
func (m *MockCTACrafter) GetCTAReRegisterOnboarding() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAReRegisterOnboarding")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAReRegisterOnboarding indicates an expected call of GetCTAReRegisterOnboarding.
func (mr *MockCTACrafterMockRecorder) GetCTAReRegisterOnboarding() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAReRegisterOnboarding", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAReRegisterOnboarding))
}

// GetCTARegisterOnboarding mocks base method.
func (m *MockCTACrafter) GetCTARegisterOnboarding() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTARegisterOnboarding")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTARegisterOnboarding indicates an expected call of GetCTARegisterOnboarding.
func (mr *MockCTACrafterMockRecorder) GetCTARegisterOnboarding() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTARegisterOnboarding", reflect.TypeOf((*MockCTACrafter)(nil).GetCTARegisterOnboarding))
}

// GetCTAResetNfc mocks base method.
func (m *MockCTACrafter) GetCTAResetNfc() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAResetNfc")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAResetNfc indicates an expected call of GetCTAResetNfc.
func (mr *MockCTACrafterMockRecorder) GetCTAResetNfc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAResetNfc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAResetNfc))
}

// GetCTARetry mocks base method.
func (m *MockCTACrafter) GetCTARetry() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTARetry")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTARetry indicates an expected call of GetCTARetry.
func (mr *MockCTACrafterMockRecorder) GetCTARetry() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTARetry", reflect.TypeOf((*MockCTACrafter)(nil).GetCTARetry))
}

// GetCTAUnderstand mocks base method.
func (m *MockCTACrafter) GetCTAUnderstand() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAUnderstand")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAUnderstand indicates an expected call of GetCTAUnderstand.
func (mr *MockCTACrafterMockRecorder) GetCTAUnderstand() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAUnderstand", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAUnderstand))
}

// GetCTAUpdateKyc mocks base method.
func (m *MockCTACrafter) GetCTAUpdateKyc() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAUpdateKyc")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAUpdateKyc indicates an expected call of GetCTAUpdateKyc.
func (mr *MockCTACrafterMockRecorder) GetCTAUpdateKyc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAUpdateKyc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAUpdateKyc))
}

// GetCTAUpdateKycNfc mocks base method.
func (m *MockCTACrafter) GetCTAUpdateKycNfc() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAUpdateKycNfc")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAUpdateKycNfc indicates an expected call of GetCTAUpdateKycNfc.
func (mr *MockCTACrafterMockRecorder) GetCTAUpdateKycNfc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAUpdateKycNfc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAUpdateKycNfc))
}

// GetCTAUpdateNfc mocks base method.
func (m *MockCTACrafter) GetCTAUpdateNfc() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAUpdateNfc")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAUpdateNfc indicates an expected call of GetCTAUpdateNfc.
func (mr *MockCTACrafterMockRecorder) GetCTAUpdateNfc() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAUpdateNfc", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAUpdateNfc))
}

// GetCTAUpdatePhoneNumber mocks base method.
func (m *MockCTACrafter) GetCTAUpdatePhoneNumber() model.NoticeAction {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCTAUpdatePhoneNumber")
	ret0, _ := ret[0].(model.NoticeAction)
	return ret0
}

// GetCTAUpdatePhoneNumber indicates an expected call of GetCTAUpdatePhoneNumber.
func (mr *MockCTACrafterMockRecorder) GetCTAUpdatePhoneNumber() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCTAUpdatePhoneNumber", reflect.TypeOf((*MockCTACrafter)(nil).GetCTAUpdatePhoneNumber))
}

// WithOutlinedBtn mocks base method.
func (m *MockCTACrafter) WithOutlinedBtn() utils.CTAOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithOutlinedBtn")
	ret0, _ := ret[0].(utils.CTAOption)
	return ret0
}

// WithOutlinedBtn indicates an expected call of WithOutlinedBtn.
func (mr *MockCTACrafterMockRecorder) WithOutlinedBtn() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithOutlinedBtn", reflect.TypeOf((*MockCTACrafter)(nil).WithOutlinedBtn))
}

// WithPrimaryBtn mocks base method.
func (m *MockCTACrafter) WithPrimaryBtn() utils.CTAOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPrimaryBtn")
	ret0, _ := ret[0].(utils.CTAOption)
	return ret0
}

// WithPrimaryBtn indicates an expected call of WithPrimaryBtn.
func (mr *MockCTACrafterMockRecorder) WithPrimaryBtn() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPrimaryBtn", reflect.TypeOf((*MockCTACrafter)(nil).WithPrimaryBtn))
}
