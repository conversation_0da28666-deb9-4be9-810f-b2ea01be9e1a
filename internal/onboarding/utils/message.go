package utils

import (
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type MessageBuilder struct {
}

//type MessageBuilder interface {
//	GetExceptionMessage() model.NoticeContent
//	GetKycStatusProcessingMessage() model.NoticeContent
//	GetKycStatusInvalidMessage() model.NoticeContent
//	GetKycStatusRejectedMessage() model.NoticeContent
//	GetProfileIssueAgeInvalidMessage() model.NoticeContent
//	GetProfileIssueAgeNotInRangeMessage(minAge, maxAge int) model.NoticeContent
//	GetProfileIssueInfoLackMessage() model.NoticeContent
//	GetProfileIssueIDExpiredMessage() model.NoticeContent
//	GetProfileIssueIDTypeNotAllowedMessage() model.NoticeContent
//	GetProfileIssueKycNfcMissingMessage() model.NoticeContent
//	GetProfileIssueShouldBeUpdatedMessage() model.NoticeContent
//	GetOnboardingRejectICNumberUsedMessage() model.NoticeContent
//	GetOnboardingRejectEmailOrPhoneUsedMessage() model.NoticeContent
//	GetOnboardingRejectOnboardingInfoUsedMessage() model.NoticeContent
//	GetOnboardingRejectOnboardingNotAllowedMessage() model.NoticeContent
//	GetOnboardingRejectIdentityInfoMismatchedMessage() model.NoticeContent
//	GetOnboardingIsProcessingMessage() model.NoticeContent
//	GetOnboardingRejectPhoneUsedMessage() model.NoticeContent
//	GetOnboardingNotProvideMessage() model.NoticeContent
//	GetOnboardingRejectCancelledMessage() model.NoticeContent
//	GetOnboardingRejectPhoneInvalidMessage() model.NoticeContent
//	GetOnboardingRejectSelfieInvalidMessage() model.NoticeContent
//	GetOnboardingRejectIdentityInvalidMessage() model.NoticeContent
//	GetOnboardingRejectNotMatchRuleMessage() model.NoticeContent
//	GetOnboardingRejectDuplicationAppMessage() model.NoticeContent
//	GetOnboardingRejectVerificationStuckMessage() model.NoticeContent
//}

func NewMessageBuilder() MessageBuilder {
	return MessageBuilder{}
}

func (m *MessageBuilder) GetExceptionMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleException,
		Message: MessageException,
	}
}

func (m *MessageBuilder) GetUserNonWhitelistMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingNotProvide,
		Message: MessageUserNonWhitelist,
	}
}

func (m *MessageBuilder) GetKycStatusProcessingMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleKycStatusProcessing,
		Message: MessageKycStatusProcessing,
	}
}

func (m *MessageBuilder) GetKycStatusInvalidMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleKycStatusInvalid,
		Message: MessageKycStatusInvalid,
	}
}

func (m *MessageBuilder) GetKycStatusRejectedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleKycStatusRejected,
		Message: MessageKycStatusRejected,
	}
}

func (m *MessageBuilder) GetProfileIssueAgeNotInRangeMessage(minAge, maxAge int) model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueAgeNotInRange,
		Message: fmt.Sprintf(MessageProfileIssueAgeNotInRange, minAge, maxAge),
	}
}

func (m *MessageBuilder) GetProfileIssueInfoLackMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueInfoLack,
		Message: MessageProfileIssueInfoLack,
	}
}

func (m *MessageBuilder) GetProfileIssueIDExpiredMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueIDExpired,
		Message: MessageProfileIssueIDExpired,
	}
}

func (m *MessageBuilder) GetProfileIssueIDTypeNotAllowedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueIDTypeNotAllowed,
		Message: MessageProfileIssueIDTypeNotAllowed,
	}
}

func (m *MessageBuilder) GetProfileIssueKycNfcMissingMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueKycNfcMissing,
		Message: MessageProfileIssueKycNfcMissing,
	}
}

func (m *MessageBuilder) GetProfileIssueShouldBeUpdatedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueShouldBeUpdated,
		Message: MessageProfileIssueShouldBeUpdated,
	}
}

func (m *MessageBuilder) GetProfileIssueICImageEmptyMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueICImageEmpty,
		Message: MessageProfileIssueICImageEmpty,
	}
}

func (m *MessageBuilder) GetProfileIssueAgeInvalidMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueAgeInvalid,
		Message: MessageProfileIssueAgeInvalid,
	}
}

func (m *MessageBuilder) GetProfileIssueNfcNeedsResetMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueKycNfcNeedsReset,
		Message: MessageProfileIssueKycNfcNeedsReset,
	}
}

func (m *MessageBuilder) GetProfileHasFraudRiskMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleProfileIssueHasFraudRisk,
		Message: MessageProfileIssueHasFraudRisk,
	}
}

func (m *MessageBuilder) GetOnboardingIsProcessingMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingIsProcessing,
		Message: MessageOnboardingIsProcessing,
	}
}

func (m *MessageBuilder) GetOnboardingRejectICNumberUsedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectICNumberUsed,
		Message: MessageOnboardingRejectICNumberUsed,
	}
}

func (m *MessageBuilder) GetOnboardingRejectEmailOrPhoneUsedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectEmailOrPhoneUsed,
		Message: MessageOnboardingRejectEmailOrPhoneUsed,
	}
}

func (m *MessageBuilder) GetOnboardingRejectOnboardingInfoUsedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectOnboardingInfoUsed,
		Message: MessageOnboardingRejectOnboardingInfoUsed,
	}
}

func (m *MessageBuilder) GetOnboardingRejectOnboardingNotAllowedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectOnboardingNotAllowed,
		Message: MessageOnboardingRejectOnboardingNotAllowed,
	}
}

func (m *MessageBuilder) GetOnboardingRejectIdentityInfoMismatchedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectIdentityInfoMismatched,
		Message: MessageOnboardingRejectIdentityInfoMismatched,
	}
}

func (m *MessageBuilder) GetOnboardingRejectPhoneUsedMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRejectPhoneUsed,
		Message: MessageOnboardingRejectPhoneUsed,
	}
}

func (m *MessageBuilder) GetOnboardingNotProvideMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingNotProvide,
		Message: MessageOnboardingNotProvide,
	}
}

func (m *MessageBuilder) GetOnboardingRejectCancelledMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRequestCancelled,
		Message: MessageOnboardingRejectAprCustomerCancelled,
	}
}

func (m *MessageBuilder) GetOnboardingRejectPhoneInvalidMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprPhoneInvalid,
	}
}

func (m *MessageBuilder) GetOnboardingRejectCustomerExistMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprCustomerExist,
	}
}

func (m *MessageBuilder) GetOnboardingRejectSelfieInvalidMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprSelfieInvalid,
	}
}

func (m *MessageBuilder) GetOnboardingRejectIdentityInvalidMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprIdentityInvalid,
	}
}

func (m *MessageBuilder) GetOnboardingRejectNotMatchRuleMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprNotMatchPartnerRule,
	}
}

func (m *MessageBuilder) GetOnboardingRejectDuplicationAppMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprDuplicationApp,
	}
}

func (m *MessageBuilder) GetOnboardingRejectVerificationStuckMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingRejectAprVerificationStuck,
	}
}

func (m *MessageBuilder) GetOnboardingRejectAgeNotInRangeMessage(minAge, maxAge int) model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: fmt.Sprintf(MessageProfileIssueAgeNotInRange, minAge, maxAge),
	}
}

func (m *MessageBuilder) GetOnboardingNotAllowUpdateMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingNotAllowUpdate,
		Message: MessageOnboardingNotAllowUpdate,
	}
}

func (m *MessageBuilder) GetOnboardingSystemHasErrorMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingRegisterNotSuccess,
		Message: MessageOnboardingSystemHasError,
	}
}

func (m *MessageBuilder) GetOnboardingLinkingAccountMessage() model.NoticeContent {
	return model.NoticeContent{
		Title:   TitleOnboardingLinkAccount,
		Message: MessageOnboardingLinkingAccount,
	}
}

const (
	// Common
	TitleException     = "Hệ thống gặp lỗi trong quá trình xử lý"
	MessageException   = "Bạn vui lòng thử lại hoặc liên hệ tổng đài CSKH của Zalopay để được hỗ trợ nhé!"
	MessageContactCIMB = `Bạn vui lòng liên hệ CSKH của CIMB theo số điện thoại "1900 58 58 85" để được hỗ trợ nhé!`

	// User
	MessageUserNonWhitelist = "Zalopay sẽ gửi thông báo cho bạn ngay khi bạn thoả điều kiện để tiếp cận dịch vụ."

	// KYC status
	TitleKycStatusInvalid    = "Thực hiện định danh tài khoản đi nè!"
	TitleKycStatusProcessing = "Định danh của bạn đang được duyệt!"
	TitleKycStatusRejected   = "Định danh của bạn bị từ chối rồi!"

	MessageKycStatusInvalid    = "Bạn cần cập nhật thông tin định danh để có thể đăng ký mua hàng trả góp nhé."
	MessageKycStatusProcessing = "Bạn cần đợi thông tin định danh duyệt thành công để có thể đăng ký mua hàng trả góp nhé."
	MessageKycStatusRejected   = "Bạn cần cập nhật lại thông tin định danh để có thể đăng ký mua hàng trả góp nhé."

	// Profile issue
	TitleProfileIssueAgeInvalid         = "Giá trị tuổi của bạn không hợp lệ!"
	TitleProfileIssueAgeNotInRange      = "Bạn chưa nằm trong độ tuổi để đăng ký!"
	TitleProfileIssueInfoLack           = "Thông tin của bạn chưa đầy đủ rồi!"
	TitleProfileIssueIDExpired          = "Thông tin của bạn đã hết hạn rồi!"
	TitleProfileIssueIDTypeNotAllowed   = "Loại giấy tờ tuỳ thân chưa phù hợp rồi!"
	TitleProfileIssueKycNfcMissing      = "Bạn quét NFC trên CCCD gắn chip nhé!"
	TitleProfileIssueShouldBeUpdated    = "Thông tin của bạn cần cập nhật mới!"
	TitleProfileIssueKycNfcNeedsReset   = "Bạn cần quét lại CCCD gắn chíp!"
	TitleProfileIssueHasFraudRisk       = "Zalopay chưa thể cung cấp tính năng trả góp cho bạn!"
	TitleProfileIssueICImageEmpty       = "Thiếu thông tin CCCD!"
	MessageProfileIssueAgeNotInRange    = "Bạn cần nằm trong độ tuổi từ %v đến %v để đăng ký mua hàng trả góp nhé."
	MessageProfileIssueAgeInvalid       = "Bạn cần cập nhật lại thông tin định danh để có thể đăng ký mua hàng trả góp nhé."
	MessageProfileIssueInfoLack         = "Bạn cần cập nhật lại thông tin định danh để có thể đăng ký mua hàng trả góp nhé."
	MessageProfileIssueIDExpired        = "Bạn cần cập nhật lại thông tin định danh để có thể đăng ký mua hàng trả góp nhé."
	MessageProfileIssueIDTypeNotAllowed = "Bạn cần định danh bằng CCCD gắn chíp để có thể đăng ký mua hàng trả góp nhé."
	MessageProfileIssueKycNfcMissing    = "Bạn cần quét trên CCCD gắn chíp để bổ sung hồ sơ đăng ký nhé!"
	MessageProfileIssueShouldBeUpdated  = "Bạn cập nhật lại thông tin định danh mới nhất để hồ sơ được phê duyệt nhanh chóng nhé."
	MessageProfileIssueKycNfcNeedsReset = "Bạn cần quét lại CCCD gắn chíp để tiếp tục thực hiện đăng ký tính năng trả góp nhé."
	MessageProfileIssueHasFraudRisk     = "Zalopay chưa thể cung cấp tính năng trả góp cho bạn ở thời điểm hiện tại."
	MessageProfileIssueICImageEmpty     = "Bạn cần cập nhật lại thông tin định danh để có thể đăng ký mua hàng trả góp nhé."
	// Onboarding submit/reject
	TitleOnboardingLinkAccount                  = "Bạn đã có tài khoản tại CIMB"
	TitleOnboardingNotProvide                   = "Zalopay chưa thể cung cấp tính năng trả góp cho bạn"
	TitleOnboardingNotAllowUpdate               = "Hệ thống gặp lỗi trong quá trình xử lý"
	TitleOnboardingIsProcessing                 = "Thông tin đăng ký đang được xử lý"
	TitleOnboardingRejectPhoneUsed              = "Số điện thoại không hợp lệ"
	TitleOnboardingRejectICNumberUsed           = "Số điện thoại không hợp lệ"
	TitleOnboardingRejectEmailOrPhoneUsed       = "Số điện thoại không hợp lệ"
	TitleOnboardingRejectOnboardingInfoUsed     = "Thông tin đăng ký không hợp lệ"
	TitleOnboardingRejectOnboardingNotAllowed   = "Zalopay chưa thể cung cấp tính năng trả góp cho bạn"
	TitleOnboardingRejectIdentityInfoMismatched = "Thông tin đăng ký không hợp lệ"
	TitleOnboardingRequestCancelled             = "Yêu cầu đăng ký trả góp đã bị huỷ"
	TitleOnboardingRegisterNotSuccess           = "Đăng ký trả góp không thành công"

	MessageOnboardingNotProvide                   = "Hiện Zalopay chưa cung cấp được dịch vụ trả góp cho người dùng đã có tài khoản tại ngân hàng CIMB. Zalopay sẽ nâng cấp tính năng trong thời gian sớm nhất và thông báo cho bạn ngay khi sẵn sàng."
	MessageOnboardingNotAllowUpdate               = `Bạn vui lòng liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ nhé!`
	MessageOnboardingLinkingAccount               = "Liên kết tài khoản tại ngân hàng CIMB của bạn với Zalopay để tiếp tục đăng ký trả góp nhé!"
	MessageOnboardingIsProcessing                 = "Yêu cầu đăng ký mở tính năng trả góp của bạn đã được gửi trước đó và đang được xử lý. Bạn chờ tí nhé!"
	MessageOnboardingRejectPhoneUsed              = `Bạn cần cập nhật lại số điện thoại mới nhất hoặc liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ.`
	MessageOnboardingRejectICNumberUsed           = `Số điện thoại bạn đang dùng để đăng ký trả góp không trùng khớp với tài khoản trả sau/gửi tiết kiệm đã đăng ký trên Zalopay trước đó. Vui lòng liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ.`
	MessageOnboardingRejectEmailOrPhoneUsed       = `Bạn cần cập nhật lại số điện thoại mới nhất hoặc liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ.`
	MessageOnboardingRejectOnboardingInfoUsed     = `Bạn vui lòng liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ nhé!`
	MessageOnboardingRejectOnboardingNotAllowed   = `Bấm "Khám phá" để Zalopay bật mí cho bạn bí kíp để có thể sử dụng tính năng trả góp nhé!`
	MessageOnboardingRejectIdentityInfoMismatched = `Bạn vui lòng liên hệ CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ nhé!`
	MessageOnboardingRejectAprCustomerCancelled   = "Yêu cầu đăng ký tính năng trả góp của bạn đã bị huỷ bỏ. Bạn có thể gửi lại yêu cầu đăng ký bằng cách bấm \"Đăng ký lại\" ngay nhé!"
	MessageOnboardingRejectAprPhoneInvalid        = `Số điện thoại bạn đang dùng để đăng ký trả góp không hợp lệ. Bạn cần cập nhật lại số điện thoại mới nhất hoặc liên hệ tổng đài CSKH của CIMB theo số điện thoại "1900 969696" để được hỗ trợ.`
	MessageOnboardingRejectAprCustomerExist       = "Số điện thoại bạn đang dùng để đăng ký trả góp không hợp lệ. Bạn cần cập nhật lại số điện thoại mới nhất hoặc liên hệ tổng đài CSKH của Zalopay để được hỗ trợ."
	MessageOnboardingRejectAprSelfieInvalid       = "Ảnh chụp chân dung của bạn cần được ngân hàng xác thực lại. Bạn có thể gửi lại yêu cầu đăng ký bằng cách bấm \"Đăng ký lại\" ngay nhé!"
	MessageOnboardingRejectAprIdentityInvalid     = "Thông tin cá nhân bạn cung cấp để đăng ký trả góp cần được ngân hàng CIMB xác thực lại. Bạn có thể gửi lại yêu cầu đăng ký bằng cách bấm \"Đăng ký lại\" ngay nhé!"
	MessageOnboardingRejectAprNotMatchPartnerRule = "Zalopay chưa thể cung cấp tính năng trả góp cho bạn ở thời điểm hiện tại."
	MessageOnboardingRejectAprDuplicationApp      = "Hiện tại bạn đang có yêu cầu đăng ký mở tài khoản khác với ngân hàng CIMB đang chờ xử lý. Bạn vui lòng chờ cho đến khi có kết quả của yêu cầu đăng ký trước đó và thử lại sau nhé!"
	MessageOnboardingRejectAprVerificationStuck   = "Thông tin bạn cung cấp để đăng ký trả góp cần được ngân hàng CIMB xác thực lại. Bạn có thể gửi lại yêu cầu đăng ký bằng cách bấm \"Đăng ký lại\" ngay nhé!"
	MessageOnboardingSystemHasError               = "Hệ thống xảy ra vấn đề khi thực hiện yêu cầu mở tính năng trả góp cho bạn. Bạn có thể gửi lại yêu cầu đăng ký bằng cách bấm \"Đăng ký lại\" ngay nhé!"
)
