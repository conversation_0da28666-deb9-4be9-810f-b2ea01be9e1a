package usecase

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"go.uber.org/mock/gomock"
)

func (s *OnboardingUsecaseTestSuite) TestSyncOnboardingApproval_Success() {
	// Arrange
	ctx := context.Background()
	userOb := &model.Onboarding{
		ID:          int64(123),
		ZalopayID:   int64(456),
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepWaitingApproval,
		PartnerCode: partner.PartnerCIMB.String(),
		UserInfo:    &cimb.UserOnboardingInfo{},
		PartnerData: &cimb.PartnerOnboardingData{
			BankRequestID: "123",
		},
	}

	s.onboardRepo.EXPECT().
		GetOnboardingByIDAndUserID(ctx, userOb.ZalopayID, userOb.ID).
		Return(userOb, nil)

	cimbStatus := &cimb.OnboardingStatus{
		ODStatus:   cimb.ODCompleted,
		CASAStatus: cimb.CASACompleted,
	}

	s.cimbService.EXPECT().
		QueryOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID).
		Return(cimbStatus, nil)

	s.transaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.transaction.EXPECT().CommitTx(ctx).Return(nil)
	s.transaction.EXPECT().RollbackTx(ctx).Return(nil)

	userFinalOb := &model.Onboarding{
		ID:          userOb.ID,
		ZalopayID:   userOb.ZalopayID,
		UserInfo:    userOb.UserInfo,
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepApproved,
		PartnerCode: userOb.PartnerCode,
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:   cimb.ODCompleted,
			CasaStatus: cimb.CASACompleted,
		},
	}

	s.onboardRepo.EXPECT().
		UpdateOnboardingApproval(ctx, userOb.ZalopayID, userOb.ID, gomock.Any()).
		Return(userFinalOb, nil)

	s.accountService.EXPECT().
		CreateAccount(ctx, &dto.CreateAccountParams{
			ZalopayID:    userFinalOb.ZalopayID,
			OnboardingID: userFinalOb.ID,
			PartnerCode:  partner.PartnerCIMB,
		}).
		Return(int64(789), nil)

	s.onboardRepo.EXPECT().
		UpdateAccountID(ctx, userFinalOb.ZalopayID, userFinalOb.ID, int64(789)).
		Return(nil)

	// Act
	result, err := s.usecase.SyncOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.OnboardingStepApproved, result.CurrentStep)
	s.Equal(model.OnboardingStatusActive, result.Status)
}

func (s *OnboardingUsecaseTestSuite) TestSyncOnboardingApproval_UnsupportedPartner() {
	// Arrange
	ctx := context.Background()
	userOb := &model.Onboarding{
		ID:          int64(123),
		ZalopayID:   int64(456),
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepWaitingApproval,
		PartnerCode: "UNSUPPORTED",
		PartnerData: &cimb.PartnerOnboardingData{
			BankRequestID: "123",
		},
	}

	s.onboardRepo.EXPECT().
		GetOnboardingByIDAndUserID(ctx, userOb.ZalopayID, userOb.ID).
		Return(userOb, nil)

	// Act
	_, err := s.usecase.SyncOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodePartnerNotSupported))
}

func (s *OnboardingUsecaseTestSuite) TestSyncCIMBOnboardingApproval_QueryStatusError() {
	// Arrange
	ctx := context.Background()
	userOb := &model.Onboarding{
		ID:          int64(123),
		ZalopayID:   int64(456),
		PartnerCode: partner.PartnerCIMB.String(),
		PartnerData: &cimb.PartnerOnboardingData{
			BankRequestID: "123",
		},
	}

	s.onboardRepo.EXPECT().
		GetOnboardingByIDAndUserID(ctx, userOb.ZalopayID, userOb.ID).
		Return(userOb, nil)

	s.cimbService.EXPECT().
		QueryOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID).
		Return(nil, errorkit.NewError(errorkit.CodeCallCIMBFailed, "query failed"))

	// Act
	_, err := s.usecase.SyncOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallCIMBFailed))
}

func (s *OnboardingUsecaseTestSuite) TestProcessOnboardingAfterSynced_Success() {
	// Arrange
	ctx := context.Background()
	userOb := &model.Onboarding{
		ID:          123,
		ZalopayID:   456,
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepApproved,
		PartnerCode: partner.PartnerCIMB.String(),
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:   cimb.ODCompleted,
			CasaStatus: cimb.CASACompleted,
		},
	}

	accountID := int64(789)
	s.accountService.EXPECT().
		CreateAccount(ctx, &dto.CreateAccountParams{
			ZalopayID:    userOb.ZalopayID,
			OnboardingID: userOb.ID,
			PartnerCode:  partner.PartnerCIMB,
		}).
		Return(accountID, nil)

	s.onboardRepo.EXPECT().
		UpdateAccountID(ctx, userOb.ZalopayID, userOb.ID, accountID).
		Return(nil)

	// Act
	err := s.usecase.processOnboardingAfterSynced(ctx, userOb)

	// Assert
	s.NoError(err)
}

func (s *OnboardingUsecaseTestSuite) TestTriggerQueryOnboardingStatusJob_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	userOb := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepWaitingApproval,
		PartnerCode: partner.PartnerCIMB.String(),
		PartnerData: &cimb.PartnerOnboardingData{
			BankRequestID: "123",
		},
	}

	s.onboardRepo.EXPECT().
		GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).
		Return(userOb, nil)

	s.jobTaskMgmt.EXPECT().
		CanTriggerQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
			ZalopayID:    "123",
			OnboardingID: "456",
		}).
		Return(true, nil)

	s.jobTaskMgmt.EXPECT().
		RegisterQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
			ZalopayID:    "123",
			OnboardingID: "456",
			PartnerReqID: "123",
			CurrentStep:  model.OnboardingStepWaitingApproval.String(),
		}).
		Return(nil)

	// Act
	err := s.usecase.TriggerQueryOnboardingStatusJob(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
}

func (s *OnboardingUsecaseTestSuite) TestTriggerQueryOnboardingStatusJob_AlreadyRunning() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.jobTaskMgmt.EXPECT().
		CanTriggerQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
			ZalopayID:    "123",
			OnboardingID: "456",
		}).
		Return(false, nil)

	// Act
	err := s.usecase.TriggerQueryOnboardingStatusJob(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err) // Should return nil when job is already running
}

func (s *OnboardingUsecaseTestSuite) TestTriggerQueryOnboardingStatusJob_CheckError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.jobTaskMgmt.EXPECT().
		CanTriggerQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
			ZalopayID:    "123",
			OnboardingID: "456",
		}).
		Return(false, errorkit.NewError(errorkit.CodeInternalError, "check failed"))

	// Act
	err := s.usecase.TriggerQueryOnboardingStatusJob(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeInternalError))
}
