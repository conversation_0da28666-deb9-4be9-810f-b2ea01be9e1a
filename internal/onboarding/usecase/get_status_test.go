package usecase

import (
	"context"

	"github.com/stretchr/testify/assert"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboarding_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	expectedOnboardings := []*model.Onboarding{{
		ID:          456,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
	}}

	s.onboardRepo.EXPECT().ListActiveOnboarding(ctx, zalopayID).Return(expectedOnboardings, nil)

	// Act
	result, err := s.usecase.ListActiveOnboarding(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.Equal(expectedOnboardings, result)
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboarding_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)

	s.onboardRepo.EXPECT().ListActiveOnboarding(ctx, zalopayID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.ListActiveOnboarding(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingListInvalid))
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboarding_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)

	s.onboardRepo.EXPECT().ListActiveOnboarding(ctx, zalopayID).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.ListActiveOnboarding(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboardingByUserIDs_Success() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}
	expectedOnboardings := []*model.Onboarding{{
		ID:          789,
		ZalopayID:   123,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
	}}

	s.onboardRepo.EXPECT().ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs).Return(expectedOnboardings, nil)

	// Act
	result, err := s.usecase.ListActiveOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.NoError(err)
	s.Equal(expectedOnboardings, result)
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboardingByUserIDs_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}

	s.onboardRepo.EXPECT().ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.ListActiveOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingListInvalid))
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboardingByUserIDs_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayIDs := []int64{123, 456}

	s.onboardRepo.EXPECT().ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.ListActiveOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestGetActiveOnboardingStatus_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	partnerCode := partner.PartnerCIMB.String()
	expectedOnboarding := &model.Onboarding{
		ID:          456,
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode,
		Status:      model.OnboardingStatusActive,
	}

	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partnerCode).Return(expectedOnboarding, nil)

	// Act
	result, err := s.usecase.GetActiveOnboardingStatus(ctx, zalopayID, partnerCode)

	// Assert
	s.NoError(err)
	s.Equal(expectedOnboarding, result)
}

func (s *OnboardingUsecaseTestSuite) TestGetActiveOnboardingStatus_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	partnerCode := partner.PartnerCIMB.String()

	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partnerCode).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.GetActiveOnboardingStatus(ctx, zalopayID, partnerCode)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.NewUnregisterOnboarding(zalopayID, partnerCode), result)
}

func (s *OnboardingUsecaseTestSuite) TestGetActiveOnboardingStatus_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	partnerCode := partner.PartnerCIMB.String()

	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partnerCode).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.GetActiveOnboardingStatus(ctx, zalopayID, partnerCode)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingInfoByID_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	expectedOnboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(expectedOnboarding, nil)

	// Act
	result, err := s.usecase.GetOnboardingInfoByID(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.Equal(expectedOnboarding, result)
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingInfoByID_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.GetOnboardingInfoByID(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingInfoByID_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.GetOnboardingInfoByID(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		PartnerData: &cimb.PartnerOnboardingData{},
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().QueryOnboardingStatus(ctx, zalopayID, onboardingID).Return(&cimb.OnboardingStatus{}, nil)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_QueryStatusError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		PartnerData: &cimb.PartnerOnboardingData{},
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().QueryOnboardingStatus(ctx, zalopayID, onboardingID).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallCIMBFailed))
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_InvalidPartnerData() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().QueryOnboardingStatus(ctx, zalopayID, onboardingID).Return(&cimb.OnboardingStatus{}, nil)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeConversionError))
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_NilPartnerData() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		PartnerData: nil, // Nil partner data
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().QueryOnboardingStatus(ctx, zalopayID, onboardingID).Return(&cimb.OnboardingStatus{}, nil)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeConversionError))
}

// Helper type for testing invalid partner data
type InvalidPartnerData struct{}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboarding_EmptyResult() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	expectedOnboardings := []*model.Onboarding{}

	s.onboardRepo.EXPECT().ListActiveOnboarding(ctx, zalopayID).Return(expectedOnboardings, nil)

	// Act
	result, err := s.usecase.ListActiveOnboarding(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *OnboardingUsecaseTestSuite) TestListActiveOnboardingByUserIDs_EmptyInput() {
	// Arrange
	ctx := context.Background()
	var zalopayIDs []int64

	s.onboardRepo.EXPECT().ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs).Return([]*model.Onboarding{}, nil)

	// Act
	result, err := s.usecase.ListActiveOnboardingByUserIDs(ctx, zalopayIDs)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *OnboardingUsecaseTestSuite) TestRetrievePartnerOnboarding_SuccessWithDataSync() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	partnerData := &cimb.PartnerOnboardingData{
		ODStatus:   cimb.ODInProgress,
		CasaStatus: cimb.CASAInProgress,
	}
	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		PartnerData: partnerData,
	}
	partnerStatus := &cimb.OnboardingStatus{
		ODStatus:   cimb.ODCompleted,
		CASAStatus: cimb.CASACompleted,
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().QueryOnboardingStatus(ctx, zalopayID, onboardingID).Return(partnerStatus, nil)

	// Act
	result, err := s.usecase.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(partnerStatus.ODStatus, partnerData.ODStatus)
	s.Equal(partnerStatus.CASAStatus, partnerData.CasaStatus)
}
