package usecase

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *ProfileUsecaseTestSuite) TestGetResources_Success() {
	// Given
	ctx := context.Background()
	resourceTypes := []string{"occupation", model.ResourceTypeIncome}
	mockResources := []model.Resource{
		{
			Type: "occupation",
			Data: []model.ResourceData{{Code: "001", Vietnamese: "Engineer"}},
		},
	}

	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(nil, errors.New("cache miss"))
	s.cimbService.EXPECT().
		FetchResources(gomock.Any(), []string{"occupation"}).
		Return(mockResources, nil)
	s.usersRepo.EXPECT().
		StoreUserDemographics(gomock.Any(), partner.PartnerCIMB, gomock.Any()).
		Return(nil)

	// When
	result, err := s.usecase.GetResources(ctx, resourceTypes)

	// Then
	s.NoError(err)
	s.Len(result, 2) // occupation + income
	s.Equal("occupation", result[0].Type)
	s.Equal(model.ResourceTypeIncome, result[1].Type)
}

func (s *ProfileUsecaseTestSuite) TestGetResources_CIMBError() {
	// Given
	ctx := context.Background()
	resourceTypes := []string{"occupation"}
	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(nil, errors.New("cache miss"))
	s.cimbService.EXPECT().
		FetchResources(gomock.Any(), resourceTypes).
		Return(nil, errors.New("CIMB error"))

	// When
	result, err := s.usecase.GetResources(ctx, resourceTypes)

	// Then
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallCIMBFailed))
}

func (s *ProfileUsecaseTestSuite) TestGetResourceByTypeAndCode_Success() {
	// Given
	ctx := context.Background()
	resourceType := "occupation"
	code := "001"
	mockResources := []model.Resource{
		{
			Type: "occupation",
			Data: []model.ResourceData{{Code: "001", Vietnamese: "Engineer"}},
		},
	}

	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(nil, errors.New("cache miss"))
	s.cimbService.EXPECT().
		FetchResources(gomock.Any(), []string{"occupation"}).
		Return(mockResources, nil)
	s.usersRepo.EXPECT().
		StoreUserDemographics(gomock.Any(), partner.PartnerCIMB, gomock.Any()).
		Return(nil)

	// When
	result, err := s.usecase.GetResourceByTypeAndCode(ctx, resourceType, code)

	// Then
	s.NoError(err)
	s.Equal(code, result.Code)
	s.Equal("Engineer", result.Vietnamese)
}

func (s *ProfileUsecaseTestSuite) TestGetResourceByTypeAndCode_NotFound() {
	// Given
	ctx := context.Background()
	resourceType := "occupation"
	code := "999"
	mockResources := []model.Resource{
		{
			Type: "occupation",
			Data: []model.ResourceData{{Code: "001", Vietnamese: "Engineer"}},
		},
	}

	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(nil, errors.New("cache miss"))
	s.cimbService.EXPECT().
		FetchResources(gomock.Any(), []string{"occupation"}).
		Return(mockResources, nil)
	s.usersRepo.EXPECT().
		StoreUserDemographics(gomock.Any(), partner.PartnerCIMB, gomock.Any()).
		Return(nil)

	// When
	result, err := s.usecase.GetResourceByTypeAndCode(ctx, resourceType, code)

	// Then
	s.Error(err)
	s.Empty(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeDataNotFound))
}

func (s *ProfileUsecaseTestSuite) TestGetResourcesFromCache_Success() {
	// Given
	ctx := context.Background()
	resourceTypes := []string{"occupation"}
	mockResources := []model.Resource{
		{
			Type: "occupation",
			Data: []model.ResourceData{{Code: "001", Vietnamese: "Engineer"}},
		},
	}

	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(mockResources, nil)

	// When
	result, err := s.usecase.getResourcesFromCache(ctx, partner.PartnerCIMB, resourceTypes)

	// Then
	s.NoError(err)
	s.Len(result, 1)
	s.Equal("occupation", result[0].Type)
}

func (s *ProfileUsecaseTestSuite) TestGetResourcesFromCache_Error() {
	// Given
	ctx := context.Background()
	resourceTypes := []string{"occupation"}
	s.usersRepo.EXPECT().
		GetUserDemographics(gomock.Any(), partner.PartnerCIMB).
		Return(nil, errors.New("cache error"))

	// When
	result, err := s.usecase.getResourcesFromCache(ctx, partner.PartnerCIMB, resourceTypes)

	// Then
	s.Error(err)
	s.Nil(result)
}

func (s *ProfileUsecaseTestSuite) TestInitIncomeResource() {
	// When
	result := initIncomeResource()

	// Then
	s.Equal(model.ResourceTypeIncome, result.Type)
	s.NotEmpty(result.Data)
	s.Equal("3000000", result.Data[0].Code)
	s.Equal("27000000", result.Data[len(result.Data)-1].Code)
}

func (s *ProfileUsecaseTestSuite) TestInitFundPurposeResource() {
	// When
	result := initFundPurposeResource()

	// Then
	s.Equal(model.ResourceTypeFundPurpose, result.Type)
	s.Len(result.Data, 1)
	s.Equal("01", result.Data[0].Code)
}

func (s *ProfileUsecaseTestSuite) TestFilterResources() {
	// Given
	resources := []model.Resource{
		{Type: "type1", Data: []model.ResourceData{{Code: "1"}}},
		{Type: "type2", Data: []model.ResourceData{{Code: "2"}}},
		{Type: "type3", Data: []model.ResourceData{{Code: "3"}}},
	}

	// When
	result := filterResources(resources, []string{"type1", "type3"})

	// Then
	s.Len(result, 2)
	s.Equal("type1", result[0].Type)
	s.Equal("type3", result[1].Type)
}

func (s *ProfileUsecaseTestSuite) TestBuildRequestResourceType() {
	// Given
	resourceTypes := []string{
		model.ResourceTypeIncome,
		"occupation",
		model.ResourceTypeFundPurpose,
		"education",
	}

	// When
	result := buildRequestResourceType(resourceTypes)

	// Then
	s.Len(result, 2)
	s.Contains(result, "occupation")
	s.Contains(result, "education")
	s.NotContains(result, model.ResourceTypeIncome)
	s.NotContains(result, model.ResourceTypeFundPurpose)
}
