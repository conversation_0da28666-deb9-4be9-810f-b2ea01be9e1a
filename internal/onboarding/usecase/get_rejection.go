package usecase

import (
	"context"
	"errors"
	"strings"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (uc *OnboardingUsecase) GetOnboardingRejection(ctx context.Context,
	zalopayID, onboardingID int64) (*dto.OnboardingRejectionResult, error) {
	onboarding, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get onboarding from db fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if !onboarding.IsRejected() {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotRejectedYet, "onboarding not rejected yet").
			WithCause(err).WithKind(errorkit.TypePrecondition)
	}

	minAgeAllowed, maxAgeAllowed := 0, 0
	gender := onboarding.GetUserInfo().GetGenderStr()
	pRejectInfo := onboarding.PartnerData.BuildRejection()
	partnerCode := partner.CodeFromString(onboarding.PartnerCode)
	// RejectCodeFmt is the combination of the main domain reject code and the partner reject code
	rejectCodeFmt := strings.Join([]string{onboarding.RejectCode, pRejectInfo.RejectCode}, ":")

	switch gender {
	case zutils.GenderMale:
		minAgeAllowed = model.MinMaleAge
		maxAgeAllowed = model.MaxMaleAge
	case zutils.GenderFemale:
		minAgeAllowed = model.MinFemaleAge
		maxAgeAllowed = model.MaxFemaleAge
	}

	return &dto.OnboardingRejectionResult{
		PartnerCode:   partnerCode,
		RejectCodeFmt: rejectCodeFmt,
		RejectCodeRaw: pRejectInfo.RejectCode,
		CanResubmit:   pRejectInfo.CanResubmit,
		NeedUpdKyc:    pRejectInfo.NeedUpdKyc,
		MinAgeAllowed: minAgeAllowed,
		MaxAgeAllowed: maxAgeAllowed,
	}, nil
}
