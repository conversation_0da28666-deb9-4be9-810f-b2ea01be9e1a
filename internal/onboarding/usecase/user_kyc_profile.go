package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

const (
	resetKycNfcLockTTL             = time.Second * 5
	resetKycNfcLockKeyPattern      = "ekyc_nfc:reset:%d"
	resetKycNfcRateLimitKeyPattern = "ekyc_nfc:reset:rate_limit:%d"
)

func (uc *ProfileUsecase) GetUserEKYCProfile(ctx context.Context,
	params *dto.UserKycProfileParams) (*dto.UserKycProfileResult, error) {
	logger := uc.logger.WithContext(ctx)

	// Fetch user ekyc profile
	profileInfo, err := uc.getZPUserKycProfile(ctx, params.ZalopayID)
	if err != nil {
		logger.Errorf("fail to get user profile from UM, err %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "fail to get user profile from UM").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	if !params.NeedVerify {
		return &dto.UserKycProfileResult{ProfileInfo: profileInfo}, nil
	}

	result := &dto.UserKycProfileResult{
		ProfileInfo:   profileInfo,
		ProfileIssues: []*model.UserProfileIssue{},
	}

	// Validate user profile required rule
	profileIssues := uc.validateUserKycProfile(profileInfo, []validator.KycProfileValidationRule{
		validator.RuleKycProfileInfoLack(),
		validator.RuleKycProfileAge(),
		validator.RuleKycProfileIDType(),
		validator.RuleKycProfileIDExpired(),
		validator.RuleICImageNotEmpty(),
		validator.RuleKycProfileHasValidNfc(),
		validator.RuleKycProfileShouldUpdate(),
	})
	if len(profileIssues) > 0 {
		result.ProfileIssues = profileIssues
		result.MissingKycNFC = uc.isMissingKycNfc(profileIssues)
		return result, nil
	}

	return result, nil
}

func (uc *ProfileUsecase) ResetUserKycNfc(ctx context.Context, zalopayID int64) error {
	logger := uc.logger.WithContext(ctx)

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID))
	if err != nil {
		logger.Errorf("fail to generate lock key, err %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "fail to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLocker.Acquire(ctx, lockKey, resetKycNfcLockTTL); err != nil {
		logger.Errorf("acquire lock fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "reset process already in progress").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLocker.Release(ctx, lockKey)

	limitKey := fmt.Sprintf(resetKycNfcRateLimitKeyPattern, zalopayID)
	allow, err := uc.rateLimiter.AllowResetKycNfc(ctx, limitKey)
	if err != nil {
		logger.Errorf("fail to check rate limit, err %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "fail to check rate limit").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if !allow {
		logger.Infof("rate limit exceeded, key=%s", lockKey)
		return errorkit.
			NewError(errorkit.CodeRateLimitExceeded, "rate limit exceeded").
			WithMetadataField("key", limitKey).WithKind(errorkit.TypeRateLimit)

	}

	if err = uc.userProfile.ResetKycNfcData(ctx, zalopayID); err != nil {
		logger.Errorf("fail to reset user kyc nfc data, err %v", err)
		return errorkit.
			NewError(errorkit.CodeCallUMFailed, "fail to reset user kyc nfc data").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	return nil
}

func (uc *ProfileUsecase) isMissingKycNfc(profileIssues []*model.UserProfileIssue) bool {
	for _, issue := range profileIssues {
		if issue.ViolateCode == model.ViolationProfileKycNfcMissing {
			return true
		}
	}
	return false
}

func (uc *ProfileUsecase) getZPUserKycProfile(ctx context.Context, zalopayID int64) (*model.UserProfile, error) {
	profile, err := uc.userProfile.GetProfile(ctx, dto.ZPUserProfileParams{
		ZalopayID:       zalopayID,
		KycProfile:      true,
		BasicProfile:    true,
		IdentityProfile: true,
	})
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("Fail to get user profile from UM, err %v", err)
		return nil, errors.Wrap(err, "Fail to get user profile from UM")
	}
	return profile, nil
}

func (uc *ProfileUsecase) evaluateZPUserRisk(ctx context.Context, zalopayID int64) (model.UserRiskAssessment, error) {
	logger := uc.logger.WithContext(ctx)
	cliSession := utils.ClientSessionFromCtx(ctx)
	userFraud, err := uc.fraudService.EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestIP:   cliSession.RequestIP,
		DeviceID:    cliSession.DeviceID,
		RequestTime: uc.timeNow().UnixMilli(),
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("failed to check user fraud from risk system, err %v", err)
		return model.UserRiskAssessment{}, errors.Wrap(err, "failed to check user fraud from risk system")
	}
	if userFraud.HasFraud {
		logger.WithContext(ctx).Warnw("msg", "user has fraud", "zalopayID", zalopayID, "fraudData", userFraud)
	}
	return model.UserRiskAssessment{
		HasFraud: userFraud.HasFraud,
	}, nil
}

func (uc *ProfileUsecase) getZPUserUnderwriting(ctx context.Context, zalopayID int64) model.UserRiskUnderwriting {
	logger := uc.logger.WithContext(ctx)
	data, err := uc.fraudService.GetUnderwriting(ctx, &dto.UserRiskUnderwritingParams{
		ZalopayID:   zalopayID,
		RequestTime: uc.timeNow().UnixMilli(),
	})
	if err != nil {
		logger.Errorf("failed to get user underwriting profile, err %v", err)
		return model.UserRiskUnderwriting{Error: err, HasError: true}
	}
	if len(data) == 0 {
		logger.Warn("user underwriting data is empty")
		return model.UserRiskUnderwriting{HasData: false}
	}
	return model.UserRiskUnderwriting{HasData: true, Data: data}
}

func (uc *ProfileUsecase) validateUserKycProfile(
	profile *model.UserProfile,
	rules []validator.KycProfileValidationRule) []*model.UserProfileIssue {
	validationRes := uc.profileValidator.ValidateProfile(profile, rules)
	profileIssues := uc.profileValidationToProfileIssues(validationRes)
	return profileIssues
}

func (uc *ProfileUsecase) validateUserKycNfcInfo(
	nfcInfo *model.UserKycNfc,
	rules []validator.KycNfcValidationRule) []*model.UserProfileIssue {
	validationRes := uc.nfcInfoValidator.ValidateNfc(nfcInfo, rules)
	profileIssues := uc.profileValidationToProfileIssues(validationRes)
	return profileIssues
}

func (uc *ProfileUsecase) profileValidationToProfileIssues(
	validationRes []model.ValidateResult) []*model.UserProfileIssue {
	profileIssues := make([]*model.UserProfileIssue, 0, len(validationRes))
	for _, res := range validationRes {
		if res.Status == model.ValidateResultApproved {
			continue
		}
		profileIssues = append(profileIssues, &model.UserProfileIssue{
			Code:        res.Code.String(),
			ViolateCode: res.Code,
			ViolateData: res.Metadata,
		})
	}
	return profileIssues
}
