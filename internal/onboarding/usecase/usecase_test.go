package usecase

import (
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/mocks/repository"
	transaction_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
	"go.uber.org/mock/gomock"
)

type ProfileUsecaseTestSuite struct {
	suite.Suite
	ctrl             *gomock.Controller
	usecase          *ProfileUsecase
	logger           log.Logger
	timeNow          func() time.Time
	transaction      *transaction_mocks.MockTransaction
	usersRepo        *repository_mocks.MockUsersRepo
	onboardRepo      *repository_mocks.MockOnboardingRepo
	fraudService     *adapter_mocks.MockFraudService
	cimbService      *adapter_mocks.MockCIMBService
	minioUploader    *adapter_mocks.MockMinioUploader
	jobTaskMgmt      *adapter_mocks.MockJobTaskMgmt
	rateLimiter      *adapter_mocks.MockRateLimiter
	distLocker       *adapter_mocks.MockDistributedLock
	userProfile      *adapter_mocks.MockUserProfileService
	whitelistSys     *adapter_mocks.MockWhitelistSys
	userChallenge    *adapter_mocks.MockUserChallengeService
	redisKeyGen      keygen.RedisKeyGenerator
	profileValidator *validator.ProfileValidator
	nfcInfoValidator *validator.NfcInfoValidator
}

func (s *ProfileUsecaseTestSuite) SetupSuite() {
	s.ctrl = gomock.NewController(s.T())

	s.logger = log.DefaultLogger
	s.transaction = transaction_mocks.NewMockTransaction(s.ctrl)
	s.usersRepo = repository_mocks.NewMockUsersRepo(s.ctrl)
	s.onboardRepo = repository_mocks.NewMockOnboardingRepo(s.ctrl)
	s.fraudService = adapter_mocks.NewMockFraudService(s.ctrl)
	s.cimbService = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.minioUploader = adapter_mocks.NewMockMinioUploader(s.ctrl)
	s.jobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.rateLimiter = adapter_mocks.NewMockRateLimiter(s.ctrl)
	s.distLocker = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.userProfile = adapter_mocks.NewMockUserProfileService(s.ctrl)
	s.userChallenge = adapter_mocks.NewMockUserChallengeService(s.ctrl)
	s.whitelistSys = adapter_mocks.NewMockWhitelistSys(s.ctrl)
	s.userChallenge = adapter_mocks.NewMockUserChallengeService(s.ctrl)
	s.redisKeyGen, _ = keygen.NewRedisKeyGenerator("dev", "onboarding")
	s.profileValidator = validator.NewProfileValidator()
	s.nfcInfoValidator = validator.NewNfcInfoValidator()
	s.timeNow = func() time.Time {
		return time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	}

	s.usecase = NewProfileUsecase(
		s.logger,
		s.usersRepo,
		s.onboardRepo,
		s.transaction,
		s.jobTaskMgmt,
		s.redisKeyGen,
		s.rateLimiter,
		s.distLocker,
		s.fraudService,
		s.cimbService,
		s.userProfile,
		s.whitelistSys,
		s.userChallenge,
		s.minioUploader,
		s.profileValidator,
		s.nfcInfoValidator,
	)
	s.usecase.timeNow = s.timeNow
}

func (s *ProfileUsecaseTestSuite) TearDownSuite() {
	s.ctrl.Finish()
}

func TestProfileUsecaseTestSuite(t *testing.T) {
	suite.Run(t, new(ProfileUsecaseTestSuite))
}

// OnboardingUsecaseTestSuite defines the test suite for OnboardingUsecase
type OnboardingUsecaseTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	usecase        *OnboardingUsecase
	logger         log.Logger
	timeNow        func() time.Time
	transaction    *transaction_mocks.MockTransaction
	onboardRepo    *repository_mocks.MockOnboardingRepo
	jobTaskMgmt    *adapter_mocks.MockJobTaskMgmt
	cimbService    *adapter_mocks.MockCIMBService
	accountService *adapter_mocks.MockAccountService
}

// SetupSuite initializes the test suite
func (s *OnboardingUsecaseTestSuite) SetupSuite() {
	s.ctrl = gomock.NewController(s.T())

	s.logger = log.DefaultLogger
	s.transaction = transaction_mocks.NewMockTransaction(s.ctrl)
	s.onboardRepo = repository_mocks.NewMockOnboardingRepo(s.ctrl)
	s.jobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.cimbService = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.accountService = adapter_mocks.NewMockAccountService(s.ctrl)
	s.timeNow = func() time.Time {
		return time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	}

	s.usecase = NewOnboardingUsecase(
		s.logger,
		s.onboardRepo,
		s.transaction,
		s.jobTaskMgmt,
		s.cimbService,
		s.accountService,
	)
}

// TearDownSuite cleans up after the test suite
func (s *OnboardingUsecaseTestSuite) TearDownSuite() {
	s.ctrl.Finish()
}

// TestOnboardingUsecaseTestSuite runs the test suite
func TestOnboardingUsecaseTestSuite(t *testing.T) {
	suite.Run(t, new(OnboardingUsecaseTestSuite))
}
