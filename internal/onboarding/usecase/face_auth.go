package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (uc *FaceAuthUsecase) SubmitFaceChallenge(ctx context.Context,
	params dto.SubmitFaceChallengeParams) error {
	logger := uc.logger.WithContext(ctx)
	zalopayID := params.ZalopayID

	// Get the current user active onboarding
	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, params.OnboardingID)
	if err != nil {
		logger.Errorf("get onboarding in db failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if !userOb.CurrentStep.CanSubmitFaceChallenge() {
		logger.Warnf("onboarding step is not allowed to submit face challenge, current step=%v", userOb.CurrentStep)
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not allowed to submit fc").
			WithCause(err).WithKind(errorkit.TypePrecondition)
	}

	/**
	 * In step call to UM to check a face challenge, we maybe only need to call get
	 * face challenge data to check img valid or not, not need to call get face challenge status
	 */
	fcDataResult, err := uc.userChallenge.GetFaceChallengeData(ctx, params.ZalopayID, params.FaceChallengeID)
	if err != nil {
		logger.Errorf("get face challenge data failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeCallUMFailed, "get face challenge data failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if !fcDataResult.HasImage() {
		logger.Warn("face challenge image is empty")
		return errorkit.
			NewError(errorkit.CodeFaceChallengeInvalid, "face challenge image is empty").
			WithKind(errorkit.TypeValidation)
	}
	if fcDataResult.IsExpiredNow() {
		logger.Warn("face challenge expired")
		return errorkit.
			NewError(errorkit.CodeFaceChallengeInvalid, "face challenge expired").
			WithKind(errorkit.TypeValidation)
	}

	// Upload face challenge image to object storage
	fcImgPath := getFCImagePath(params.ZalopayID, params.OnboardingID, time.Now())
	fcImgResult, err := uc.minioUploader.FetchAndUploadFaceImage(ctx, &dto.FaceImgStoreParams{
		ZalopayID:    params.ZalopayID,
		OnboardingID: params.OnboardingID,
		ImageUri:     fcDataResult.ImageUrl,
		ImagePath:    fcImgPath,
	})
	if err != nil {
		logger.Errorf("upload face challenge image failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "upload face challenge image failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	fcFinalResult := &model.UserFaceChallenge{
		Approved:    fcDataResult.Approved,
		ImageUrl:    fcDataResult.ImageUrl,
		MatchScore:  fcDataResult.MatchScore,
		ValidateAt:  fcDataResult.ValidateAt,
		ChallengeID: fcDataResult.ChallengeID,
	}

	logger.Infow("msg", "face challenge result", "result", fcFinalResult)

	// Store face challenge data
	if err = uc.SaveUseFaceChallengeData(ctx, userOb, fcFinalResult, fcImgResult); err != nil {
		logger.Errorf("store face challenge data failed, error=%v", err)
		return err
	}

	return nil
}

func (uc *FaceAuthUsecase) SaveUseFaceChallengeData(
	ctx context.Context, onboarding *model.Onboarding,
	faceChallengeRes *model.UserFaceChallenge,
	faceChallengeImg *model.Image) error {
	logger := uc.logger.WithContext(ctx)
	faceChallengeID := faceChallengeRes.ChallengeID
	faceChallengeTime := faceChallengeRes.ValidateAt

	if faceChallengeID == "" {
		return errorkit.
			NewError(errorkit.CodeFaceChallengeInvalid, "face challenge id is empty").
			WithKind(errorkit.TypeInvalidArg)
	}
	if faceChallengeImg.IsEmpty() {
		return errorkit.
			NewError(errorkit.CodeFaceChallengeInvalid, "face challenge image is empty").
			WithKind(errorkit.TypeInvalidArg)
	}

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		return errorkit.NewError(errorkit.CodeInternalError, "begin transaction failed").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserIDForUpdate(ctx, onboarding.ZalopayID, onboarding.ID)
	if err != nil {
		logger.Errorf("get onboarding in db failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if userOb.IsCompleted() {
		logger.Warn("onboarding is completed, skip face challenge")
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is completed").
			WithKind(errorkit.TypePrecondition)
	}

	// Update a face challenge id
	userOb.SetCurrentStep(model.OnboardingStepFaceChallenged)
	userOb.UserInfo.WithFaceChallengeID(faceChallengeID)
	userOb.UserInfo.WithFaceChallengeImg(faceChallengeImg)
	userOb.UserInfo.WithFaceChallengeTime(faceChallengeTime)

	if err = uc.onboardRepo.UpdateStepAndExtraProfile(ctx, userOb); err != nil {
		logger.Errorf("update onboarding after face challenge failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding after face challenge failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if err = uc.txn.CommitTx(ctx); err != nil {
		return errorkit.NewError(errorkit.CodeInternalError, "commit transaction failed").WithCause(err)
	}

	return nil
}

func (uc *FaceAuthUsecase) GetFaceChallengeData(ctx context.Context,
	zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error) {
	faceResult, err := uc.userChallenge.GetFaceChallengeData(ctx, zalopayID, faceChallengeID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get face challenge data failed, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "get face challenge data failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	return faceResult, nil
}

func (uc *FaceAuthUsecase) GetAndVerifyFaceChallenge(ctx context.Context,
	zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error) {
	faceResult, err := uc.userChallenge.GetFaceChallengeData(ctx, zalopayID, faceChallengeID)
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("get face challenge data failed, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "get face challenge data failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	expiredIn := uc.challengeConf.GetFaceExpiredIn()
	faceResult.AssessExpiration(expiredIn.AsDuration())
	return faceResult, nil
}

func (uc *FaceAuthUsecase) ResetUserToFaceChallenge(ctx context.Context, zalopayID int64, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("begin transaction failed, error=%v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "begin transaction failed").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserIDForUpdate(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		logger.Errorf("get onboarding in db failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("get onboarding in db failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if userOb.CurrentStep.CanSubmitFaceChallenge() {
		logger.Info("onboarding step is allowed to submit face challenge, skip reset")
		return nil
	}
	if !userOb.CurrentStep.CanResetFaceChallenge() {
		logger.Errorf("onboarding step is not allowed to reset face challenge, current step=%v", userOb.CurrentStep)
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not allowed to reset fc").
			WithKind(errorkit.TypePrecondition)
	}

	step := model.OnboardingStepProfileSuccess
	if err = uc.onboardRepo.UpdateOnboardingStep(ctx, userOb.ZalopayID, userOb.ID, step); err != nil {
		logger.Errorf("reset user to face challenge failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "reset user to face challenge failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("commit transaction failed, error=%v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "commit transaction failed").WithCause(err)

	}

	logger.Infow(
		"msg", "reset user to face challenge successfully",
		"zalopay_id", zalopayID, "onboarding_id", onboardingID)

	return nil
}

func (uc *FaceAuthUsecase) ResetUserToContractSigning(ctx context.Context, zalopayID int64, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("begin transaction failed, error=%v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "begin transaction failed").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserIDForUpdate(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get onboarding in db failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}

	if userOb.CurrentStep.CanContractOTPConfirmation() {
		logger.Info("onboarding step is allowed to signing contract by otp, skip reset")
		return nil
	}
	if !userOb.CurrentStep.CanResetOTPConfirmation() {
		logger.Errorf("onboarding step is not allowed to reset contract otp singing, current step=%v", userOb.CurrentStep)
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not allowed to reset contract otp singing").
			WithKind(errorkit.TypePrecondition)
	}

	step := model.OnboardingStepFaceChallenged
	if err = uc.onboardRepo.UpdateOnboardingStep(ctx, userOb.ZalopayID, userOb.ID, step); err != nil {
		logger.Errorf("update onboarding for reseting user to contract otp singing failed, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "reset user to contract signing failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("commit transaction failed, error=%v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "commit transaction failed").WithCause(err)
	}

	logger.Infow(
		"msg", "reset user to contract otp singing successfully",
		"zalopay_id", zalopayID, "onboarding_id", onboardingID)

	return nil
}

func (uc *FaceAuthUsecase) IsFaceChallengeExpired(faceChallengeTime time.Time) bool {
	expiredIn := uc.challengeConf.GetFaceExpiredIn().AsDuration()
	faceChallenge := &model.UserFaceChallenge{ValidateAt: faceChallengeTime}
	return faceChallenge.AssessExpiration(expiredIn)
}

func getFCImagePath(userID, onboardingID int64, time time.Time) string {
	const pattern = "onboarding/%d/%d/images/%s_face_challenge"
	timestamp := time.Format("20060102_1504")
	filePath := fmt.Sprintf(pattern, userID, onboardingID, timestamp)
	return filePath
}
