package usecase

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userOb := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		CurrentStep: model.OnboardingStepRejected,
		Status:      model.OnboardingStatusActive,
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:    "REJECTED",
			CasaStatus:  "REJECTED",
			ErrorDetail: "R15",
		},
	}

	// Mock expectations
	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(userOb, nil)
	s.onboardRepo.EXPECT().MarkOnboardingInactive(ctx, zalopayID, onboardingID).Return(nil)

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_LockAcquisitionFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(fmt.Errorf("lock error"))

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeResourceLockedForProcessing))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_OnboardingNotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_NotRejectedOrInactive() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userOb := &model.Onboarding{
		ID:        onboardingID,
		ZalopayID: zalopayID,
		Status:    model.OnboardingStatusInactive, // Not rejected status
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(userOb, nil)

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotAllowToResubmit))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_NotEligibleToResubmit() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userOb := &model.Onboarding{
		ID:        onboardingID,
		ZalopayID: zalopayID,
		Status:    model.OnboardingStatusActive,
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(userOb, nil)

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotAllowToResubmit))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ReinitiateOnboarding_MarkInactiveError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	lockKey := fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userOb := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepRejected,
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:    "REJECTED",
			CasaStatus:  "REJECTED",
			ErrorDetail: "R15",
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resubmitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(userOb, nil)
	s.onboardRepo.EXPECT().MarkOnboardingInactive(ctx, zalopayID, onboardingID).Return(fmt.Errorf("db error"))

	// Act
	err := s.usecase.ReinitiateOnboarding(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}
