package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	registerOBLockTTL        = 10 * time.Second
	registerOBLockKeyPattern = "register:%s:%d"
)

func (uc *ProfileUsecase) RegisterOnboarding(ctx context.Context,
	params *dto.OnboardingRegisterParams) (*dto.OnboardingRegisterResult, error) {
	params.SetDefaultPartnerCodeIfEmpty()

	userID := params.ZalopayID
	partnerCode := params.PartnerCode
	logging := log.With(uc.kLogger, "user_id", userID, "partner", partnerCode)
	logger := log.NewHelper(logging).WithContext(ctx)

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(registerOBLockKeyPattern, partnerCode, userID))
	if err != nil {
		logger.Errorf("Generate lock key failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "Failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLocker.Acquire(ctx, lockKey, registerOBLockTTL); err != nil {
		logger.Errorf("Acquire lock failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "Registration process already in progress").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLocker.Release(ctx, lockKey)

	// Fetch user whitelist status
	userWhitelisted, err := uc.whitelistSys.IsOnboardWhitelisted(ctx, userID)
	if err != nil {
		logger.Errorf("Fail to get user whitelist status, err %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallWhitelistFailed, "Failed to check user whitelisted").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if !userWhitelisted {
		logger.Errorf("User not whitelisted, zalopayID=%d, partnerCode=%s", params.ZalopayID, params.PartnerCode)
		return nil, errorkit.
			NewError(errorkit.CodeUserNonWhitelist, "User not whitelisted").
			WithKind(errorkit.TypePrecondition)
	}

	// Check user has active onboarding or not
	userOb, err := uc.onboardRepo.GetActiveOnboarding(ctx, userID, partnerCode.String())
	if err != nil && !errors.Is(err, model.ErrOnboardingNotFound) {
		logger.Errorf("GetActiveOnboarding failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	// Handle idempotence
	if userOb != nil && userOb.CurrentStep.IsRegistered() {
		return &dto.OnboardingRegisterResult{OnboardingID: userOb.ID}, nil
	}
	if userOb != nil && !userOb.CurrentStep.IsUnregistered() {
		logger.Warnf("ob step is not valid for register, current_step: %v", userOb.CurrentStep)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not valid for new register").
			WithKind(errorkit.TypePrecondition)
	}

	userRisk, err := uc.evaluateZPUserRisk(ctx, params.ZalopayID)
	if err != nil {
		logger.Errorf("check user fraud fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallRiskFailed, "check user fraud fail").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if userRisk.HasFraud {
		logger.Errorf("user has fraud, zalopayID=%d", params.ZalopayID)
		return nil, errorkit.
			NewError(errorkit.CodeUserFraud, "user has fraud").
			WithKind(errorkit.TypePrecondition)
	}

	userProfile, err := uc.getZPUserKycProfile(ctx, params.ZalopayID)
	if err != nil {
		logger.Errorf("GetUserProfile failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeGetKycProfileFailed, "get user kyc profile failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	profileIssues := uc.validateUserKycProfile(userProfile, []validator.KycProfileValidationRule{
		validator.RuleKycProfileInfoLack(),
		validator.RuleKycProfileAge(),
		validator.RuleKycProfileIDType(),
		validator.RuleKycProfileIDExpired(),
		validator.RuleICImageNotEmpty(),
		validator.RuleKycProfileHasValidNfc(),
	})
	if len(profileIssues) != 0 {
		logger.Errorf(
			"Profile validation failed, issues length: %v, first issue: %v",
			len(profileIssues), profileIssues[0],
		)
		return nil, errorkit.
			NewError(errorkit.CodeProfileProblems, "user profile validation failed").
			WithCause(err).WithKind(errorkit.TypeValidation).
			WithMetadataField(errorkit.OnboardProfileIssueField, profileIssues[0])
	}

	kycNfcInfo, err := uc.userProfile.GetKycNfcData(ctx, params.ZalopayID)
	if err != nil {
		logger.Errorf("get user kyc nfc info from UM fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "get user kyc nfc status from UM fail").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	kycNfcIssue := uc.validateUserKycNfcInfo(kycNfcInfo, []validator.KycNfcValidationRule{
		validator.RuleKycNfcRawEmpty(),
		validator.RuleKycNfcIdNotEmpty(),
	})
	if len(kycNfcIssue) != 0 {
		logger.Errorf("KycNfc validation failed, issues length: %v", len(kycNfcIssue))
		return nil, errorkit.
			NewError(errorkit.CodeProfileProblems, "user kyc nfc validation failed").
			WithCause(err).WithKind(errorkit.TypeValidation).
			WithMetadataField(errorkit.OnboardProfileIssueField, kycNfcIssue[0])
	}

	checkPermParams := &dto.OnboardingPermissionParams{
		ZalopayID:   params.ZalopayID,
		IDNumber:    userProfile.IdNumber,
		PhoneNumber: userProfile.PhoneNumber,
	}
	onboardingPerm, err := uc.cimbService.CheckOnboardingPermission(ctx, checkPermParams)
	if err != nil {
		logger.Errorf("CheckOnboardingPermission from partner failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "check onboarding permission failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// Init new onboarding if not exist
	if userOb == nil {
		userOb = model.NewOnboarding(params.ZalopayID, partnerCode.String())
	}

	onboardingInf := fulfillNewOnboarding(userOb, userProfile, onboardingPerm)
	onboardingID, err := uc.upsertNewOnboarding(ctx, onboardingInf)
	if err != nil {
		logger.Errorf("registerOnboarding failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "register onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if onboardingID == model.InitOnboardID {
		logger.Errorf("registerOnboarding failed: onboardingID is invalid")
		return nil, errorkit.
			NewError(errorkit.CodeInvalidArgument, "onboardingID is invalid").
			WithKind(errorkit.TypeInvalidArg)
	}
	if !onboardingPerm.Allowed && !onboardingPerm.IsWarning {
		logger.Errorf("User not eligible to register onboarding, permission=%+v", onboardingPerm)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotEligibleToRegister, "user info not eligible for register").
			WithKind(errorkit.TypePrecondition).WithMetadataField(errorkit.OnboardPermCodeField, onboardingPerm.RejectCode)
	}

	return &dto.OnboardingRegisterResult{
		PartnerCode:  partnerCode,
		OnboardingID: onboardingID,
	}, nil
}

func (uc *ProfileUsecase) upsertNewOnboarding(ctx context.Context, onboarding *model.Onboarding) (int64, error) {
	// Update onboarding info for existing onboarding
	if onboarding.HasExisted() {
		err := uc.onboardRepo.UpdateBasicOnboarding(ctx, onboarding)
		if err != nil {
			return -1, err
		}
		return onboarding.ID, nil
	}

	// Insert new onboarding
	onboardingID, err := uc.onboardRepo.CreateUserOnboarding(ctx, onboarding)
	if err != nil {
		return -1, err
	}
	return onboardingID, nil
}

func fulfillNewOnboarding(
	userOnboard *model.Onboarding,
	userProfile *model.UserProfile,
	onboardingPerm *model.OnboardingPermission) *model.Onboarding {
	basicProfile := model.UserProfile{
		Gender:      userProfile.Gender,
		FullName:    userProfile.FullName,
		IdNumber:    userProfile.IdNumber,
		PhoneNumber: userProfile.PhoneNumber,
	}
	if userOnboard.PartnerCode == partner.PartnerCIMB.String() {
		userOnboard.UserInfo = &cimb.UserOnboardingInfo{UserProfile: basicProfile}
	}

	userOnboard.RejectCode = ""
	userOnboard.CurrentStep = model.OnboardingStepEligible
	if !onboardingPerm.Allowed && !onboardingPerm.IsWarning {
		userOnboard.CurrentStep = model.OnboardingStepIneligible
		userOnboard.RejectCode = onboardingPerm.GetRejectCodeFmt()
	}
	return userOnboard
}
