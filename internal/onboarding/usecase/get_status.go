package usecase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (uc *OnboardingUsecase) ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error) {
	onboardings, err := uc.onboardRepo.ListActiveOnboarding(ctx, zalopayID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingListInvalid, "No active onboarding found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("ListActiveOnboarding fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "ListActiveOnboarding from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return onboardings, nil
}

func (uc *OnboardingUsecase) ListActiveOnboardingByUserIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Onboarding, error) {
	onboardings, err := uc.onboardRepo.ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingListInvalid, "No active onboarding found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("ListActiveOnboardingByUserIDs fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "ListActiveOnboardingByUserIDs from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return onboardings, nil
}

func (uc *OnboardingUsecase) GetActiveOnboardingStatus(ctx context.Context,
	zalopayID int64, partnerCode string) (*model.Onboarding, error) {
	onboarding, err := uc.onboardRepo.GetActiveOnboarding(ctx, zalopayID, partnerCode)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return model.NewUnregisterOnboarding(zalopayID, partnerCode), nil
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("GetActiveOnboardingStatus fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "GetActiveOnboardingStatus from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return onboarding, nil
}

func (uc *OnboardingUsecase) GetOnboardingInfoByID(ctx context.Context,
	zalopayID int64, onboardingID int64) (*model.Onboarding, error) {
	onboarding, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "Onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("GetOnboardingByIDAndUserID fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "GetOnboardingByIDAndUserID from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return onboarding, nil
}

func (uc *OnboardingUsecase) RetrievePartnerOnboarding(ctx context.Context,
	zalopayID int64, onboardingID int64) (*model.Onboarding, error) {
	onboarding, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "Onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		uc.logger.WithContext(ctx).Errorf("GetOnboardingInfo fail, error %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "GetOnboardingInfo from db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	cimbObStatus, err := uc.cimbService.QueryOnboardingStatus(ctx, onboarding.ZalopayID, onboarding.ID)
	if err != nil {
		uc.logger.Errorf("QueryOnboardingStatus failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "query onboarding status from partner failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	cimbObData, ok := cimb.AsPartnerOnboardingData(onboarding.PartnerData)
	if !ok {
		return nil, errorkit.
			NewError(errorkit.CodeConversionError, "failed to convert partner data").
			WithCause(err).WithKind(errorkit.TypeConversion)
	}

	cimbObData.SetApprovalResult(cimbObStatus)
	onboarding.SyncPartnerApproval(cimbObData)

	return onboarding, nil
}
