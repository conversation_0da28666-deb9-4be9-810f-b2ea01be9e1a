package usecase

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	linkingRequestID := "req-123"

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingEmpty,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return(linkingRequestID, nil)
	s.onboardRepo.EXPECT().UpdatePartnerData(ctx, gomock.Any()).Return(nil)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_LockAcquisitionError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(errors.New("lock error"))

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeResourceLockedForProcessing))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_OnboardingNotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_InvalidStep() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepContractSinging, // Invalid step for linking
		PartnerData: &cimb.PartnerOnboardingData{},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingStepInvalidForExec))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_AlreadyLinked() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingSuccess,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeBadRequest))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_CIMBServiceError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingEmpty,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return("", errors.New("CIMB service error"))

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallCIMBFailed))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_UpdatePartnerDataError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	linkingRequestID := "req-123"

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingEmpty,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return(linkingRequestID, nil)
	s.onboardRepo.EXPECT().UpdatePartnerData(ctx, gomock.Any()).Return(errors.New("update error"))

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, errors.New("db error"))

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_LinkingFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	linkingRequestID := "req-123"

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingInit,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return(linkingRequestID, nil)
	s.onboardRepo.EXPECT().UpdatePartnerData(ctx, gomock.Any()).Return(nil)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_EmptyLinkingStatus() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	linkingRequestID := "req-123"

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingEmpty,
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return(linkingRequestID, nil)
	s.onboardRepo.EXPECT().UpdatePartnerData(ctx, gomock.Any()).Return(nil)

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
}

func (s *ProfileUsecaseTestSuite) TestLinkingAccount_VerifyUpdatePartnerData() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	linkingRequestID := "req-123"

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		PartnerData: &cimb.PartnerOnboardingData{
			LinkingStatus: cimb.LinkingEmpty,
		},
	}

	var capturedParams *model.UpdatePartnerDataParams
	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), linkingLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.cimbService.EXPECT().RequestLinkingAccount(ctx, onboarding).Return(linkingRequestID, nil)
	s.onboardRepo.EXPECT().UpdatePartnerData(ctx, gomock.Any()).
		DoAndReturn(func(_ context.Context, params *model.UpdatePartnerDataParams) error {
			capturedParams = params
			return nil
		})

	// Act
	err := s.usecase.LinkingAccount(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.NotNil(capturedParams)
	s.Equal(zalopayID, capturedParams.ZalopayID)
	s.Equal(onboardingID, capturedParams.OnboardingID)
	s.Equal(partner.PartnerCIMB, capturedParams.PartnerCode)

	// Verify partner data update
	partnerData, ok := capturedParams.PartnerData.(*cimb.PartnerOnboardingData)
	s.True(ok)
	s.Equal(cimb.LinkingInit, partnerData.LinkingStatus)
	s.Equal(linkingRequestID, partnerData.BankLinkingRequestID)
}
