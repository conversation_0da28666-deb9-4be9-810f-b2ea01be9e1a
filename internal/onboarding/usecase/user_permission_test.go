package usecase

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserPermissions_BoundUser() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.UserPermissionsParams{ZalopayID: zalopayID}

	onboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		PartnerCode: string(partner.PartnerCIMB),
		CurrentStep: "APPROVED",
		Status:      model.OnboardingStatusActive,
	}
	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{onboarding}, nil)

	// Act
	result, err := s.usecase.GetUserPermissions(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.BindStatusBound, result.BindInfo.Status)
	s.Equal(onboarding.ID, result.BindInfo.OnboardingID)
	s.Equal(partner.PartnerCIMB, result.BindInfo.PartnerCode)
	s.True(result.IsWhitelisted)
	s.Equal(model.KycStatusByPass, result.KycProgress.Status)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserPermissions_OnboardingUser() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.UserPermissionsParams{ZalopayID: zalopayID}

	onboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		PartnerCode: string(partner.PartnerCIMB),
		CurrentStep: "EKYC",
		Status:      model.OnboardingStatusActive,
	}
	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{onboarding}, nil)

	// Act
	result, err := s.usecase.GetUserPermissions(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.BindStatusOnboarding, result.BindInfo.Status)
	s.True(result.IsWhitelisted)
	s.Equal(model.KycStatusByPass, result.KycProgress.Status)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserPermissions_UnboundUser() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.UserPermissionsParams{ZalopayID: zalopayID}

	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{}, nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.userProfile.EXPECT().GetKycProgStatus(ctx, zalopayID).Return(&model.UserKycProgress{
		Status: model.KycStatusProcessing,
	}, nil)

	// Act
	result, err := s.usecase.GetUserPermissions(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.BindStatusUnbound, result.BindInfo.Status)
	s.True(result.IsWhitelisted)
	s.Equal(model.KycStatusProcessing, result.KycProgress.Status)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserPermissions_WhitelistError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.UserPermissionsParams{ZalopayID: zalopayID}

	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{}, nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(false, fmt.Errorf("whitelist error"))

	// Act
	result, err := s.usecase.GetUserPermissions(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallWhitelistFailed))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserPermissions_KycError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.UserPermissionsParams{ZalopayID: zalopayID}

	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{}, nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.userProfile.EXPECT().GetKycProgStatus(ctx, zalopayID).Return(nil, fmt.Errorf("kyc error"))

	// Act
	result, err := s.usecase.GetUserPermissions(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallUMFailed))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetAppBindingInfo_InactiveOnboarding() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)

	onboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		PartnerCode: string(partner.PartnerCIMB),
		CurrentStep: "APPROVED",
		Status:      model.OnboardingStatusInactive,
	}
	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{onboarding}, nil)

	// Act
	result, err := s.usecase.GetAppBindingInfo(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.BindStatusUnbound, result.Status)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetAppBindingInfo_IneligibleOnboarding() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)

	onboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		PartnerCode: string(partner.PartnerCIMB),
		CurrentStep: model.OnboardingStepIneligible,
		Status:      model.OnboardingStatusActive,
	}
	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return([]*model.Onboarding{onboarding}, nil)

	// Act
	result, err := s.usecase.GetAppBindingInfo(ctx, zalopayID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.BindStatusUnbound, result.Status)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetAppBindingInfo_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	expectedErr := fmt.Errorf("repository error")

	s.onboardRepo.EXPECT().ListOnboardingByUserID(ctx, zalopayID).Return(nil, expectedErr)

	// Act
	result, err := s.usecase.GetAppBindingInfo(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedErr, err)
}
