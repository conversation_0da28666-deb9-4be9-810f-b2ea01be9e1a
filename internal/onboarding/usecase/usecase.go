package usecase

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
)

var ProviderSet = wire.NewSet(
	NewProfileUsecase, NewContractUsecase,
	NewOnboardingUsecase, NewFaceAuthUsecase,
	validator.ProviderSet,
)

// ProfileUsecase ...
type ProfileUsecase struct {
	txn              _interface.Transaction
	logger           *log.Helper
	kLogger          log.Logger
	timeNow          func() time.Time
	usersRepo        _interface.UsersRepo
	onboardRepo      _interface.OnboardingRepo
	whitelistSys     _interface.WhitelistSys
	fraudService     _interface.FraudService
	cimbService      _interface.CIMBService
	minioUploader    _interface.MinioUploader
	jobTaskMgmt      _interface.JobTaskMgmt
	rateLimiter      _interface.RateLimiter
	redisKeyGen      keygen.RedisKeyGenerator
	distLocker       _interface.DistributedLock
	userProfile      _interface.UserProfileService
	userChallenge    _interface.UserChallengeService
	nfcInfoValidator *validator.NfcInfoValidator
	profileValidator *validator.ProfileValidator
}

// ContractUsecase ...
type ContractUsecase struct {
	txn           _interface.Transaction
	logger        *log.Helper
	kLogger       log.Logger
	onboardRepo   _interface.OnboardingRepo
	jobTaskMgmt   _interface.JobTaskMgmt
	cimbService   _interface.CIMBService
	minioUploader _interface.MinioUploader
	imgFetcher    _interface.ImageFetcher
	imgValidator  *validator.ImageValidator
}

// OnboardingUsecase ...
type OnboardingUsecase struct {
	txn            _interface.Transaction
	logger         *log.Helper
	kLogger        log.Logger
	onboardRepo    _interface.OnboardingRepo
	jobTaskMgmt    _interface.JobTaskMgmt
	cimbService    _interface.CIMBService
	accountService _interface.AccountService
}

// FaceAuthUsecase ...
type FaceAuthUsecase struct {
	txn           _interface.Transaction
	logger        *log.Helper
	kLogger       log.Logger
	onboardRepo   _interface.OnboardingRepo
	minioUploader _interface.MinioUploader
	userChallenge _interface.UserChallengeService
	challengeConf *config.Onboarding_UserChallenge
}

func NewProfileUsecase(
	kLogger log.Logger,
	usersRepo _interface.UsersRepo,
	onboardRepo _interface.OnboardingRepo,
	transaction _interface.Transaction,
	jobTaskMgmt _interface.JobTaskMgmt,
	redisKeyGen keygen.RedisKeyGenerator,
	rateLimiter _interface.RateLimiter,
	distLocker _interface.DistributedLock,
	fraudService _interface.FraudService,
	cimbService _interface.CIMBService,
	userProfile _interface.UserProfileService,
	whitelistSys _interface.WhitelistSys,
	userChallenge _interface.UserChallengeService,
	minioUploader _interface.MinioUploader,
	profileValidator *validator.ProfileValidator,
	nfcInfoValidator *validator.NfcInfoValidator,
) *ProfileUsecase {
	logger := log.NewHelper(kLogger)
	return &ProfileUsecase{
		txn:              transaction,
		logger:           logger,
		timeNow:          time.Now,
		kLogger:          kLogger,
		usersRepo:        usersRepo,
		onboardRepo:      onboardRepo,
		fraudService:     fraudService,
		redisKeyGen:      redisKeyGen,
		rateLimiter:      rateLimiter,
		distLocker:       distLocker,
		userProfile:      userProfile,
		userChallenge:    userChallenge,
		whitelistSys:     whitelistSys,
		cimbService:      cimbService,
		jobTaskMgmt:      jobTaskMgmt,
		minioUploader:    minioUploader,
		profileValidator: profileValidator,
		nfcInfoValidator: nfcInfoValidator,
	}
}

func NewContractUsecase(
	kLogger log.Logger,
	onboardRepo _interface.OnboardingRepo,
	jobTaskMgmt _interface.JobTaskMgmt,
	transaction _interface.Transaction,
	cimbService _interface.CIMBService,
	imgFetcher _interface.ImageFetcher,
	imgValidator *validator.ImageValidator,
	minioUploader _interface.MinioUploader,
) *ContractUsecase {
	logger := log.NewHelper(kLogger)
	return &ContractUsecase{
		logger:        logger,
		kLogger:       kLogger,
		txn:           transaction,
		onboardRepo:   onboardRepo,
		jobTaskMgmt:   jobTaskMgmt,
		cimbService:   cimbService,
		minioUploader: minioUploader,
		imgFetcher:    imgFetcher,
		imgValidator:  imgValidator,
	}
}

func NewOnboardingUsecase(
	kLogger log.Logger,
	onboardRepo _interface.OnboardingRepo,
	transaction _interface.Transaction,
	jobTaskMgmt _interface.JobTaskMgmt,
	cimbService _interface.CIMBService,
	accountService _interface.AccountService,
) *OnboardingUsecase {
	logger := log.NewHelper(kLogger)
	return &OnboardingUsecase{
		logger:         logger,
		kLogger:        kLogger,
		txn:            transaction,
		jobTaskMgmt:    jobTaskMgmt,
		onboardRepo:    onboardRepo,
		cimbService:    cimbService,
		accountService: accountService,
	}
}

func NewFaceAuthUsecase(
	kLogger log.Logger,
	onboardRepo _interface.OnboardingRepo,
	transaction _interface.Transaction,
	minioUploader _interface.MinioUploader,
	userChallenge _interface.UserChallengeService,
	onboardingConf *config.Onboarding,
) *FaceAuthUsecase {
	logger := log.NewHelper(kLogger)
	return &FaceAuthUsecase{
		logger:        logger,
		kLogger:       kLogger,
		txn:           transaction,
		onboardRepo:   onboardRepo,
		minioUploader: minioUploader,
		userChallenge: userChallenge,
		challengeConf: onboardingConf.GetUserChallenge(),
	}
}
