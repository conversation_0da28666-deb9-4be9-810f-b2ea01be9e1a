package usecase

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (uc *ProfileUsecase) GetUserPermissions(ctx context.Context,
	params *dto.UserPermissionsParams) (*model.UserPermissions, error) {
	logger := uc.logger.WithContext(ctx)
	zalopayID := params.ZalopayID

	userWhitelisted := true
	userKycProgress := model.UserKycProgress{
		Status: model.KycStatusByPass,
	}

	// Check user binding status with application
	userBindData, err := uc.GetAppBindingInfo(ctx, zalopayID)
	if err != nil {
		logger.Errorf("Fail to get user binding status, err %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "Fail to get user binding status").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	if userBindData.Status == model.BindStatusBound {
		return &model.UserPermissions{
			BindInfo:      userBindData,
			KycProgress:   userKycProgress,
			IsWhitelisted: userWhitelisted,
		}, nil
	}

	if userBindData.Status == model.BindStatusOnboarding {
		return &model.UserPermissions{
			BindInfo:      userBindData,
			KycProgress:   userKycProgress,
			IsWhitelisted: userWhitelisted,
		}, nil
	}

	// Fetch user whitelist status
	userWhitelisted, err = uc.whitelistSys.IsOnboardWhitelisted(ctx, zalopayID)
	if err != nil {
		logger.Errorf("Fail to get user whitelist status, err %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallWhitelistFailed, "Failed to check user whitelisted").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// Fetch user ekyc status
	ekycProg, err := uc.userProfile.GetKycProgStatus(ctx, zalopayID)
	if err != nil {
		logger.Errorf("Fail to get user ekyc status, err %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "Fail to get user ekyc status").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if ekycProg != nil {
		userKycProgress.Status = ekycProg.Status
	}

	return &model.UserPermissions{
		BindInfo:      userBindData,
		KycProgress:   userKycProgress,
		IsWhitelisted: userWhitelisted,
	}, nil
}

// GetAppBindingInfo returns the application-binding info of the user.
func (uc *ProfileUsecase) GetAppBindingInfo(ctx context.Context, zalopayID int64) (*model.BindingInfo, error) {
	onboardings, err := uc.onboardRepo.ListOnboardingByUserID(ctx, zalopayID)
	if err != nil {
		return nil, err
	}

	if len(onboardings) == 0 {
		return &model.BindingInfo{Status: model.BindStatusUnbound}, nil
	}

	if onboardings = filterActiveOnboarding(onboardings); len(onboardings) == 0 {
		return &model.BindingInfo{Status: model.BindStatusUnbound}, nil
	}

	var approvedOnboarding *model.Onboarding
	var eligibleOnboarding *model.Onboarding

	for _, onboarding := range onboardings {
		onboardStep := onboarding.CurrentStep
		switch {
		case onboardStep == model.OnboardingStepApproved:
			approvedOnboarding = onboarding
		case onboardStep != model.OnboardingStepIneligible:
			eligibleOnboarding = onboarding
		}
	}

	// If a user has approved onboarding, return the binding info
	if approvedOnboarding != nil {
		return &model.BindingInfo{
			Status:       model.BindStatusBound,
			AccountID:    approvedOnboarding.AccountID,
			OnboardingID: approvedOnboarding.ID,
			PartnerCode:  partner.CodeFromString(approvedOnboarding.PartnerCode),
		}, nil
	}

	// If a user has at least one onboarding in progress, return the binding status onboarding
	if eligibleOnboarding != nil {
		return &model.BindingInfo{Status: model.BindStatusOnboarding}, nil
	}

	return &model.BindingInfo{Status: model.BindStatusUnbound}, nil
}

func filterActiveOnboarding(onboardings []*model.Onboarding) []*model.Onboarding {
	onboardingsActs := make([]*model.Onboarding, 0)
	for _, onboarding := range onboardings {
		if onboarding.Status == model.OnboardingStatusActive {
			onboardingsActs = append(onboardingsActs, onboarding)
		}
	}
	return onboardingsActs
}
