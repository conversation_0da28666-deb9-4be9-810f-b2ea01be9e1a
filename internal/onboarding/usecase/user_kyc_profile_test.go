package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserEKYCProfile_Success() {
	// Arrange
	ctx := context.Background()
	request := &dto.UserKycProfileParams{ZalopayID: 123, NeedVerify: true}
	profileData := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: 123, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(profileData, nil)

	// Act
	result, err := s.usecase.GetUserEKYCProfile(ctx, request)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(&dto.UserKycProfileResult{
		ProfileInfo:   profileData,
		ProfileIssues: []*model.UserProfileIssue{},
		MissingKycNFC: false,
	}, result)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserEKYCProfile_GetUserProfile_Fail() {
	// Arrange
	ctx := context.Background()
	request := &dto.UserKycProfileParams{ZalopayID: 123, NeedVerify: true}
	profileParams := dto.ZPUserProfileParams{ZalopayID: 123, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(nil, errors.New("Get profile failed by bad request"))

	// Act
	result, err := s.usecase.GetUserEKYCProfile(ctx, request)

	// Assert
	s.Nil(result)
	s.NotNil(err)
	s.EqualError(err, "code: CALL_UM_FAILED, message: fail to get user profile from UM")
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserEKYCProfile_NoVerificationNeeded() {
	// Arrange
	ctx := context.Background()
	request := &dto.UserKycProfileParams{ZalopayID: 123, NeedVerify: false}
	profileData := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: 123, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(profileData, nil)

	// Act
	result, err := s.usecase.GetUserEKYCProfile(ctx, request)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(&dto.UserKycProfileResult{ProfileInfo: profileData}, result)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserEKYCProfile_WithProfileIssues() {
	// Arrange
	ctx := context.Background()
	request := &dto.UserKycProfileParams{ZalopayID: 123, NeedVerify: true}
	profileData := &model.UserProfile{
		FullName:     "Test User",
		IdNumber:     "",               // Missing ID number
		IdType:       model.IDTypeCCCD, // Updated to a valid IDType
		Birthday:     "",               // Missing birthday
		PhoneNumber:  "0906658696",
		KycNfcStatus: model.KycNfcStatusInvalid, // Updated to a defined KycNfcStatus
	}
	profileParams := dto.ZPUserProfileParams{ZalopayID: 123, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(profileData, nil)

	// Act
	result, err := s.usecase.GetUserEKYCProfile(ctx, request)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotEmpty(result.ProfileIssues)
	s.True(result.MissingKycNFC)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_GetUserEKYCProfile_UnderAge() {
	// Arrange
	ctx := context.Background()
	request := &dto.UserKycProfileParams{ZalopayID: 123, NeedVerify: true}
	profileData := s.buildUserProfile()
	profileData.Birthday = time.Now().AddDate(-17, 0, 0).Format("2006-01-02") // 17 years old
	profileParams := dto.ZPUserProfileParams{ZalopayID: 123, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(profileData, nil)

	// Act
	result, err := s.usecase.GetUserEKYCProfile(ctx, request)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotEmpty(result.ProfileIssues)
	hasAgeIssue := false
	for _, issue := range result.ProfileIssues {
		if issue.ViolateCode == model.ViolationAgeNotInRange {
			hasAgeIssue = true
			break
		}
	}
	s.True(hasAgeIssue)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ResetUserKycNfc_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	lockKey := fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)
	limitKey := fmt.Sprintf(resetKycNfcRateLimitKeyPattern, zalopayID)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resetKycNfcLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.rateLimiter.EXPECT().AllowResetKycNfc(ctx, limitKey).Return(true, nil)
	s.userProfile.EXPECT().ResetKycNfcData(ctx, zalopayID).Return(nil).Times(1)

	// Act
	err := s.usecase.ResetUserKycNfc(ctx, zalopayID)

	// Assert
	s.NoError(err)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ResetUserKycNfc_LockAcquisitionFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	lockKey := fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)
	expectedErr := errors.New("lock acquisition failed")

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resetKycNfcLockTTL).Return(expectedErr)

	// Act
	err := s.usecase.ResetUserKycNfc(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "reset process already in progress")
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ResetUserKycNfc_RateLimitCheckFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	lockKey := fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)
	limitKey := fmt.Sprintf(resetKycNfcRateLimitKeyPattern, zalopayID)
	expectedErr := errors.New("rate limit check failed")

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resetKycNfcLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.rateLimiter.EXPECT().AllowResetKycNfc(ctx, limitKey).Return(false, expectedErr)

	// Act
	err := s.usecase.ResetUserKycNfc(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "fail to check rate limit")
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ResetUserKycNfc_RateLimitExceeded() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	lockKey := fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)
	limitKey := fmt.Sprintf(resetKycNfcRateLimitKeyPattern, zalopayID)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resetKycNfcLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.rateLimiter.EXPECT().AllowResetKycNfc(ctx, limitKey).Return(false, nil)

	// Act
	err := s.usecase.ResetUserKycNfc(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "rate limit exceeded")
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_ResetUserKycNfc_ResetFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	lockKey := fmt.Sprintf(resetKycNfcLockKeyPattern, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)
	limitKey := fmt.Sprintf(resetKycNfcRateLimitKeyPattern, zalopayID)
	expectedErr := errors.New("reset failed")

	s.distLocker.EXPECT().Acquire(ctx, lockKey, resetKycNfcLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.rateLimiter.EXPECT().AllowResetKycNfc(ctx, limitKey).Return(true, nil)
	s.userProfile.EXPECT().ResetKycNfcData(ctx, zalopayID).Return(expectedErr).Times(1)

	// Act
	err := s.usecase.ResetUserKycNfc(ctx, zalopayID)

	// Assert
	s.Error(err)
	s.Contains(err.Error(), "fail to reset user kyc nfc data")
}

func (s *ProfileUsecaseTestSuite) buildUserProfile() *model.UserProfile {
	return &model.UserProfile{
		FullName:         "Bùi Thuý Hằng",
		IdNumber:         "051193019414",
		IdType:           model.IDTypeCCCDChip,
		Birthday:         "1993-11-05",
		PhoneNumber:      "0906658696",
		Email:            "",
		Gender:           2,
		IdIssueDate:      "2023-11-05",
		IdExpireDate:     "2033-10-23",
		IdIssuePlace:     "CỤC TRƯỞNG CỤC CẢNH SÁT QUẢN LÝ HÀNH CHÍNH VỀ TRẬT TỰ XÃ HỘI",
		PermanentAddress: "THÔN 6, ĐỨC CHÁNH, MỘ ĐỨC, QUẢNG NGÃI",
		ProfileLevel:     3,
		KycLevel:         3,
		KycUpdatedDate:   "2022-11-05T00:00:00Z",
		KycNfcStatus:     model.KycNfcStatusExisted,
		ProfileImage: model.ProfileImage{
			FrontICUri: "front-uri",
			BackICUri:  "back-uri",
		},
	}
}
