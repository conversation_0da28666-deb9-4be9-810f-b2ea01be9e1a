package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

const (
	resubmitOBLockTTL        = 5 * time.Second
	resubmitOBLockKeyPattern = "resubmit:%d:%d"
)

// ReinitiateOnboarding only close the onboarding profile and set it to inactive status
func (uc *ProfileUsecase) ReinitiateOnboarding(ctx context.Context, zalopayID, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	// Acquire lock to prevent multiple request
	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(resubmitOBLockKeyPattern, zalopayID, onboardingID))
	if err != nil {
		logger.Errorf("generate lock key fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLocker.Acquire(ctx, lockKey, resubmitOBLockTTL); err != nil {
		logger.Errorf("acquire lock fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "reinitiate onboarding already processing").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLocker.Release(ctx, lockKey)

	// Get user onboarding info
	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		logger.Errorf("user's onboarding not found in db, err=%v", err)
		return errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}

	// Get onboarding rejection info
	if !userOb.IsActiveStatus() || !userOb.IsRejected() {
		logger.Errorf("onboarding is not rejected or active, onboardingID=%d", onboardingID)
		return errorkit.
			NewError(errorkit.CodeOnboardingNotAllowToResubmit, "onboarding is not rejected or inactive").
			WithKind(errorkit.TypePrecondition)
	}

	if rejection := userOb.GetRejection(); !rejection.CanResubmit {
		logger.Errorf("onboarding not eligible to reset,  info=%+v", rejection)
		return errorkit.
			NewError(errorkit.CodeOnboardingNotAllowToResubmit, "onboarding not eligible to reset").
			WithKind(errorkit.TypePrecondition)
	}

	if err = uc.onboardRepo.MarkOnboardingInactive(ctx, zalopayID, onboardingID); err != nil {
		logger.Errorf("failed to mark onboarding inactive, err=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "failed to mark onboarding inactive").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infof("reinitiate onboarding success, zalopayID=%d, onboardingID=%d", zalopayID, onboardingID)
	return nil
}
