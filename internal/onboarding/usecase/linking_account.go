package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	linkingLockTTL        = 30 * time.Second
	linkingLockKeyPattern = "liking:%d:%d"
)

func (uc *ProfileUsecase) LinkingAccount(ctx context.Context, zalopayID, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(linkingLockKeyPattern, zalopayID, onboardingID))
	if err != nil {
		logger.Errorf("generate lock key fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLocker.Acquire(ctx, lockKey, linkingLockTTL); err != nil {
		logger.Errorf("acquire lock fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "linking account already in progress").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLocker.Release(ctx, lockKey)

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("get active onboarding fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if !obd.CurrentStep.CanLinkingAccount() {
		logger.Errorf("onboarding step is not allowed to linking account, zalopayID=%v, onboardingID=%d", zalopayID, onboardingID)
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not allowed to linking account").
			WithKind(errorkit.TypePrecondition)
	}
	if obd.PartnerData != nil && obd.PartnerData.IsLinkingSuccess() {
		logger.Warnw("msg", "linking account already success, no need to process",
			"onboardingID", onboardingID, "zalopayID", zalopayID)
		return errorkit.
			NewError(errorkit.CodeBadRequest, "linking account already success").
			WithKind(errorkit.TypeValidation)
	}

	linkingRequestID, err := uc.cimbService.RequestLinkingAccount(ctx, obd)
	if err != nil {
		logger.Errorf("request linking account to cimb fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "request linking account to cimb fail").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// Init account link data with id and status
	// NOTE: Currently support for cimb partner only, if process with other partner, need to update this part
	// by switch-case for each partner or implement more generic way in partner data interface
	partnerCode := partner.CodeFromString(obd.PartnerCode)
	updateFunc := func(pod *cimb.PartnerOnboardingData) *cimb.PartnerOnboardingData {
		pod.LinkingStatus = cimb.LinkingInit
		pod.BankLinkingRequestID = linkingRequestID
		return pod
	}
	obd.PartnerData = cimb.UpdatePartnerOnboardingData(obd.PartnerData, updateFunc)

	updateParams := &model.UpdatePartnerDataParams{
		ZalopayID:    zalopayID,
		OnboardingID: onboardingID,
		PartnerCode:  partnerCode,
		PartnerData:  obd.PartnerData,
	}
	if err = uc.onboardRepo.UpdatePartnerData(ctx, updateParams); err != nil {
		logger.Errorf("update partner data for linking into db fail, error=%v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update partner data for linking into db fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return nil
}
