package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/parallel"
)

const (
	submitOBLockTTL        = 10 * time.Second
	submitOBLockKeyPattern = "submit:%d:%d"
)

func (uc *ProfileUsecase) SubmitApplication(
	ctx context.Context, zalopayID, onboardingID int64,
	userExtra *model.UserAdditionalInfo,
) (*model.Onboarding, error) {
	logger := uc.logger.WithContext(ctx)

	lockKey, err := uc.redisKeyGen.Generate(fmt.Sprintf(submitOBLockKeyPattern, zalopayID, onboardingID))
	if err != nil {
		logger.Errorf("generate lock key fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "failed to generate lock key").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.distLocker.Acquire(ctx, lockKey, submitOBLockTTL); err != nil {
		logger.Errorf("acquire lock fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeResourceLockedForProcessing, "submit process already in progress").
			WithCause(err).WithKind(errorkit.TypeConflict)
	}
	defer uc.distLocker.Release(ctx, lockKey)

	if userExtra, err = uc.enrichUserExtra(userExtra); err != nil {
		logger.Errorf("enrich user extra fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "enrich user extra fail").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if err = uc.validateUserExtra(ctx, userExtra); err != nil {
		logger.Errorf("validate resources fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeConditionFailed, "validate resources fail").
			WithCause(err).WithKind(errorkit.TypeInvalidArg)
	}

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if errors.Is(err, model.ErrOnboardingNotFound) {
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingNotFound, "onboarding not found").
			WithCause(err).WithKind(errorkit.TypeNotFound)
	}
	if err != nil {
		logger.Errorf("get active onboarding fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infow(
		"msg", "get active onboarding success",
		"onboarding_id", obd.ID,
		"zalopay_id", obd.ZalopayID,
	)

	if !obd.CurrentStep.IsRegistered() {
		logger.Warnf("onboarding is not available submit, current_step=%s", obd.CurrentStep)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available submit").
			WithKind(errorkit.TypePrecondition)
	}

	userRisk, err := uc.evaluateZPUserRisk(ctx, zalopayID)
	if err != nil {
		logger.Errorf("check user fraud fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallRiskFailed, "check user fraud fail").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if userRisk.HasFraud {
		logger.Warnf("user has fraud, zalopayID=%d", zalopayID)
		return nil, errorkit.
			NewError(errorkit.CodeUserFraud, "user has fraud").
			WithKind(errorkit.TypePrecondition)
	}

	userProfile, err := uc.getZPUserKycProfile(ctx, zalopayID)
	if err != nil {
		logger.Errorf("get user profile from UM fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "get user profile from UM fail").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	profileIssues, err := uc.validateUserProfileAndNfcVerified(ctx, zalopayID, userProfile)
	if err != nil {
		logger.Errorf("validateUserProfileAndVerifyNfc failed, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "validate user profile or nfc verified failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if len(profileIssues) > 0 {
		firstIssue := profileIssues[0]
		logger.Errorw("msg", "profile validate invalid and has issue", "issue", firstIssue)
		return nil, errorkit.
			NewError(errorkit.CodeProfileProblems, "profile invalid").
			WithKind(errorkit.TypeValidation).WithMetadataField(errorkit.OnboardProfileIssueField, firstIssue)
	}

	kycNfcInfo, err := uc.userProfile.GetKycNfcData(ctx, zalopayID)
	if err != nil {
		logger.Errorf("get user kyc nfc info from UM fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallUMFailed, "get user kyc nfc status from UM fail").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if kycNfcInfo == nil {
		logger.Error("user kyc nfc info not found")
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "user kyc nfc info not found").
			WithKind(errorkit.TypeNotFound)
	}

	nfcInfoIssue := uc.validateUserKycNfcInfo(kycNfcInfo, []validator.KycNfcValidationRule{
		validator.RuleKycNfcRawEmpty(),
		validator.RuleKycNfcDataRequired(),
		validator.RuleKycNfcGenderValid(),
	})
	if len(nfcInfoIssue) > 0 {
		logger.Errorw("msg", "nfc info invalid and has issue", "issue", nfcInfoIssue[0])
		return nil, errorkit.
			NewError(errorkit.CodeProfileProblems, "nfc info invalid").
			WithKind(errorkit.TypeValidation).WithMetadataField(errorkit.OnboardProfileIssueField, nfcInfoIssue[0])
	}

	underwriting := uc.getZPUserUnderwriting(ctx, zalopayID)

	logger.Infow(
		"msg", "user underwriting data info",
		"error", underwriting.Error,
		"has_data", underwriting.HasData,
		"has_error", underwriting.HasError,
		"zalopay_id", zalopayID, "partner_code", obd.PartnerCode,
	)

	icImages, err := uc.storeUserKYCImages(ctx, zalopayID, onboardingID, userProfile.ProfileImage, kycNfcInfo)
	if err != nil {
		logger.Errorf("store user kyc images fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeInternalError, "store user kyc images fail").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	obd.SetICImagesSet(icImages)
	obd.UserInfo.WithBasicProfile(userProfile)
	obd.UserInfo.WithExtraProfile(userExtra)
	obd.UserInfo.WithIdentityNfcInfo(kycNfcInfo)
	obd.UserInfo.WithIdentityProfile(userProfile)
	obd.UserInfo.WithUnderwritingData(underwriting)

	logger.Infow("msg", "start submit application to partner",
		"zalopay_id", zalopayID,
		"onboarding_id", obd.ID,
		"partner_code", obd.PartnerCode,
		"whitelist_method", obd.UserInfo.GetRiskWhitelistMethod(),
	)

	// Carefull: onboarding domain data must include all required info before submit to partner
	partnerRequestID, err := uc.cimbService.SubmitCasaOnboarding(ctx, obd, false)

	var pErr *model.PartnerError
	if err != nil && !errors.As(err, &pErr) {
		logger.Errorf("submit casa onboarding to cimb partner fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "submit casa onboarding to cimb partner fail").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if pErr != nil {
		partnerRequestID, err = uc.handleSubmitToCIMBPartnerError(ctx, obd, pErr)
	}
	if err != nil {
		logger.Errorf("submit casa onboarding to cimb partner fail, error=%v", err)
		return nil, err
	}

	updateObFunc := func(partnerOb *cimb.PartnerOnboardingData) *cimb.PartnerOnboardingData {
		partnerOb.BankRequestID = partnerRequestID
		return partnerOb
	}

	// update onboarding data after submit to partner
	obd.CurrentStep = model.OnboardingStepProfileSuccess
	obd.PartnerData = cimb.UpdatePartnerOnboardingData(obd.PartnerData, updateObFunc)

	// update onboarding to DB
	if err = uc.onboardRepo.UpdateOnboardingInfo(ctx, obd); err != nil {
		logger.Errorf("update onboarding into storage fail, error=%v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding into storage fail").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	go uc.RegisterPollingOnboardingStatusJob(ctx, obd)

	return obd, nil
}

func (uc *ProfileUsecase) RegisterPollingOnboardingStatusJob(
	ctx context.Context, onboarding *model.Onboarding,
) {
	maxRetry := 5
	delayTime := 5 * time.Second
	ctx = context.WithoutCancel(ctx)
	logger := uc.logger.WithContext(ctx)

	err := retry.NewRetry(maxRetry, delayTime, nil).Execute(ctx, func() error {
		err := uc.jobTaskMgmt.RegisterQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
			ZalopayID:    cast.ToString(onboarding.ZalopayID),
			OnboardingID: cast.ToString(onboarding.ID),
			PartnerReqID: onboarding.PartnerData.GetPartnerRequestID(),
			CurrentStep:  onboarding.CurrentStep.String(),
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("register query onboarding status task fail, error=%v", err)
	}
}

func (uc *ProfileUsecase) validateUserProfileAndNfcVerified(
	ctx context.Context, zalopayID int64,
	userProfile *model.UserProfile) ([]*model.UserProfileIssue, error) {
	logger := uc.logger.WithContext(ctx)

	profileIssues := uc.validateUserKycProfile(userProfile, []validator.KycProfileValidationRule{
		validator.RuleKycProfileAge(),
		validator.RuleKycProfileInfoLack(),
		validator.RuleKycProfileIDExpired(),
		validator.RuleICImageNotEmpty(),
		validator.RuleKycProfilePassport(),
		validator.RuleKycProfileHasValidNfc(),
	})
	if len(profileIssues) > 0 {
		logger.Errorw("msg", "profile invalid", "first_issue", profileIssues[0])
		return profileIssues, nil
	}
	if userProfile.IsNfcVerifyBCA() {
		return nil, nil
	}
	if err := uc.userProfile.VerifyKycNfcBCA(ctx, zalopayID); err != nil {
		logger.Errorf("verify user kyc nfc data fail, error=%v", err)
		return nil, errors.Wrap(err, "verify user kyc nfc data fail")
	}
	return nil, nil
}

func (uc *ProfileUsecase) handleSubmitToCIMBPartnerError(
	ctx context.Context, obd *model.Onboarding,
	pErr *model.PartnerError,
) (string, error) {
	logger := uc.logger.WithContext(ctx)

	if pErr.SysCode != cimb.ErrorCodeDuplicateReqID.String() {
		return "", errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "submit onboarding data failed").
			WithCause(pErr).WithKind(errorkit.TypeRemoteCall)
	}

	partnerObd, err := uc.cimbService.QueryOnboardingStatus(ctx, obd.ZalopayID, obd.ID)
	if err != nil {
		logger.Errorf("query onboarding status fail, error=%v", err)
		return "", errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "query onboarding status failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	return partnerObd.RequestID, nil
}

func (uc *ProfileUsecase) storeUserKYCImages(
	ctx context.Context, zalopayID, onboardID int64,
	profileImage model.ProfileImage, kycInfo *model.UserKycNfc) (icImages *model.ICImage, err error) {
	uploadFunc := func(imageUri string, imagePath string, needBuffer bool) parallel.ExecFunc {
		return func(ctx context.Context) (any, error) {
			data, uErr := uc.minioUploader.FetchAndUploadKYCImages(ctx, &dto.UserKycImageStoreParams{
				ZalopayID:    zalopayID,
				OnboardingID: onboardID,
				ImageUri:     imageUri,
				ImagePath:    imagePath,
				NeedBuffer:   needBuffer,
			})
			if uErr != nil {
				return nil, errors.Errorf("upload image fail, imgPath=%s, error=%v", imageUri, uErr)
			}
			return data, nil
		}
	}

	const (
		icSelfieImgRes = iota
		icBackImgRes
		icFrontImgRes
	)

	selfieImgPath, icFrontImgPath, icBackImgPath := getKycImagesPath(zalopayID, onboardID, time.Now())

	results, err := parallel.PromiseAll(ctx, []parallel.ExecFunc{
		uploadFunc(kycInfo.NfcDataDG2, selfieImgPath, true),
		uploadFunc(profileImage.BackICUri, icBackImgPath, false),
		uploadFunc(profileImage.FrontICUri, icFrontImgPath, false),
	}, 10*time.Second)
	if err != nil {
		return nil, errors.Errorf("upload images fail, error=%v", err)
	}

	var icSelfie, icFront, icBack *model.Image
	if err = results.CastAt(icSelfieImgRes, &icSelfie); err != nil {
		return nil, errors.Errorf("cast ic selfie image fail, error=%v", err)
	}
	if err = results.CastAt(icFrontImgRes, &icFront); err != nil {
		return nil, errors.Errorf("cast ic front image fail, error=%v", err)
	}
	if err = results.CastAt(icBackImgRes, &icBack); err != nil {
		return nil, errors.Errorf("cast ic back image fail, error=%v", err)
	}
	if icSelfie == nil || icFront == nil || icBack == nil {
		return nil, errors.Errorf("upload images fail, icSelfie=%v, icFront=%v, icBack=%v", icSelfie, icFront, icBack)
	}
	return &model.ICImage{
		SelfieIC: icSelfie,
		FrontIC:  icFront,
		BackIC:   icBack,
	}, nil
}

func (uc *ProfileUsecase) enrichUserExtra(request *model.UserAdditionalInfo) (*model.UserAdditionalInfo, error) {
	if request == nil {
		return request, nil
	}

	if strings.TrimSpace(request.JobTitle) == "" {
		request.JobTitle = model.DefaultJobTitle
	}

	if strings.TrimSpace(request.LivingCity) == "" {
		request.LivingCity = model.DefaultLivingCity
	}

	if strings.TrimSpace(request.Occupation) == "" {
		request.Occupation = model.DefaultOccupation
	}

	if strings.TrimSpace(request.SourceOfFund) == "" {
		request.SourceOfFund = model.DefaultSourceOfFund
	}

	if strings.TrimSpace(request.LoanPurpose) == "" {
		request.LoanPurpose = model.DefaultLoanPurpose
	}

	if strings.TrimSpace(request.Education) == "" {
		request.Education = model.DefaultEducationLevel
	}

	if strings.TrimSpace(request.EmploymentStatus) == "" {
		request.EmploymentStatus = model.DefaultEmploymentStatus
	}

	if strings.TrimSpace(request.MonthlyIncome) == "" {
		request.MonthlyIncome = model.DefaultMonthlyIncome
	}

	if strings.TrimSpace(request.FundPurpose) == "" {
		// NOTE: Currently we set fund purpose description as default value
		// but in future it should be a code of fund purpose and we will lookup description from resources
		// and set it to request.FundPurposeDesc
		request.FundPurpose = model.DefaultFundPurposeDesc
	}

	if request.OverdraftLimit == 0 {
		request.OverdraftLimit = model.DefaultOverdraftLimit
	}

	return request, nil
}

func (uc *ProfileUsecase) validateUserExtra(ctx context.Context, request *model.UserAdditionalInfo) error {
	resources, err := uc.GetResources(ctx, []string{
		model.ResourceTypeCity,
		model.ResourceTypeIncome,
		model.ResourceTypeJobTitle,
		model.ResourceTypeEducation,
		model.ResourceTypeOccupation,
		model.ResourceTypeSourceOfFund,
		model.ResourceTypeEmploymentStt,
	})
	if err != nil {
		return err
	}

	rValidator := validator.ResourceValidator{
		Resources: resources,
	}
	err = rValidator.Validate([]model.ResourceCheck{
		{Type: model.ResourceTypeCity, Code: request.LivingCity},
		{Type: model.ResourceTypeJobTitle, Code: request.JobTitle},
		{Type: model.ResourceTypeEducation, Code: request.Education},
		{Type: model.ResourceTypeIncome, Code: request.MonthlyIncome},
		{Type: model.ResourceTypeOccupation, Code: request.Occupation},
		{Type: model.ResourceTypeSourceOfFund, Code: request.SourceOfFund},
		{Type: model.ResourceTypeEmploymentStt, Code: request.EmploymentStatus},
	})
	if err != nil {
		return err
	}

	return nil
}

func getKycImagesPath(userID, onboardID int64, submitTime time.Time) (selfie, icFront, icBack string) {
	const selfiePattern = "onboarding/%d/%d/%s_selfie"
	const icBackPattern = "onboarding/%d/%d/%s_ic_back"
	const icFrontPattern = "onboarding/%d/%d/%s_ic_front"
	timestamp := submitTime.Format("20060102_1504")
	selfie = fmt.Sprintf(selfiePattern, userID, onboardID, timestamp)
	icBack = fmt.Sprintf(icBackPattern, userID, onboardID, timestamp)
	icFront = fmt.Sprintf(icFrontPattern, userID, onboardID, timestamp)
	return
}
