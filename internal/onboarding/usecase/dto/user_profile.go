package dto

import "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"

type UserKycProfileParams struct {
	ZalopayID  int64
	NeedVerify bool
}

type UserKycProfileResult struct {
	ProfileInfo   *model.UserProfile
	ProfileIssues []*model.UserProfileIssue
	MissingKycNFC bool
}

type UserKycImageStoreParams struct {
	ZalopayID    int64
	OnboardingID int64
	ImageUri     string
	ImagePath    string
	NeedBuffer   bool
}

type SubmitFaceChallengeParams struct {
	ZalopayID       int64
	OnboardingID    int64
	FaceChallengeID string
}

type ZPUserProfileParams struct {
	ZalopayID       int64
	KycProfile      bool
	BasicProfile    bool
	IdentityProfile bool
}
