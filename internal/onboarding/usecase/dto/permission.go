package dto

type UserPermissionsParams struct {
	ZalopayID int64
	DeviceID  string
	UserIP    string
}

type UserRiskEvaluationParams struct {
	ZalopayID   int64
	RequestIP   string
	DeviceID    string
	RequestTime int64
}

type UserRiskEvaluationResult struct {
	HasF<PERSON>ud   bool
	RiskCode   int32
	InfoCode   int32
	ActionCode int32
}

type UserRiskUnderwritingParams struct {
	ZalopayID   int64
	RequestTime int64
}

type UserRiskUnderwritingResult struct {
	Data map[string]any
}
