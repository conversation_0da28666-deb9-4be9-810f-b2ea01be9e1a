package dto

import (
	"slices"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type OnboardingRegisterParams struct {
	ZalopayID   int64
	PartnerCode partner.PartnerCode
}

type OnboardingRegisterResult struct {
	PartnerCode  partner.PartnerCode
	OnboardingID int64
}

func (o *OnboardingRegisterParams) SetDefaultPartnerCode() {
	o.PartnerCode = partner.PartnerCIMB
}

func (o *OnboardingRegisterParams) SetDefaultPartnerCodeIfEmpty() {
	if o.PartnerCode == "" {
		o.SetDefaultPartnerCode()
	}
}

type OnboardingRejectionResult struct {
	CanResubmit   bool
	NeedUpdKyc    bool
	PartnerCode   partner.PartnerCode
	RejectCodeRaw string
	RejectCodeFmt string
	MinAgeAllowed int
	MaxAgeAllowed int
}

type WetSignSubmitParams struct {
	ZalopayID    string
	OnboardingID int64
	ImageData    string
	ImageMime    model.ImageMime
}

type WetSignSubmitResult struct {
	OnboardingID int64
}

type WetSignUploadParams struct {
	ZalopayID    int64
	OnboardingID int64
	ImagePath    string
	ImageData    []byte
	ContentType  string
}

func (p *WetSignSubmitParams) ValidateImageMime() bool {
	imageMimes := []model.ImageMime{model.ImageMimePNG, model.ImageMimeJPG, model.ImageMimeJPEG}
	return slices.Contains(imageMimes, p.ImageMime)
}

type ContractSingingTask struct {
	ZalopayID    string
	OnboardingID string
}

type FaceImgSubmitTask struct {
	ZalopayID    string
	OnboardingID string
}

type FaceImgSubmitCheck ContractSingingTask

type FaceImgStoreParams struct {
	ZalopayID    int64
	OnboardingID int64
	ImageUri     string
	ImagePath    string
}

type SelfieUploadParams struct {
	ImageUrl            string
	ZalopayID           int64
	OnboardingID        int64
	PartnerRequestID    string
	PartnerOTPRequestId string
}

type SelfieUploadUrlParams struct {
	ZalopayID      int64
	PartnerReqID   string
	PartnerTransID string
	ContentType    string
}

type OnboardingPermissionParams struct {
	ZalopayID   int64
	IDNumber    string
	PhoneNumber string
}

type SubmitCasaOnboardingResult struct {
	RequestID string
}

type OnboardingPollingResult struct {
	ZalopayID    int64                  `json:"zalopay_id"`
	AccountID    int64                  `json:"account_id"`
	OnboardingID int64                  `json:"onboarding_id"`
	Status       model.OnboardingStatus `json:"status"`
	CurrentStep  model.OnboardingStep   `json:"current_step"`
	PartnerCode  string                 `json:"partner_code"`
	RejectCode   string                 `json:"reject_code"`
}
