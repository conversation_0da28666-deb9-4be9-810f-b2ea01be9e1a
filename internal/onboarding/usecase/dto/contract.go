package dto

import "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"

type RequestOTPParams struct {
	Type             model.OTPType
	ZalopayID        int64
	OnboardingID     int64
	PartnerRequestID string
}

type RequestOTPResult struct {
	WaitTime     int64
	ResendTime   int64
	Status       bool
	ErrorMessage string
	PhoneNumber  string
	OtpRequestId string
	IsSendSms    bool
}

type VerifyOTPParams struct {
	Type                model.OTPType
	ZalopayID           int64
	OnboardingID        int64
	PartnerOTPCode      string
	PartnerRequestID    string
	PartnerOTPRequestId string
}

type GetContractParams struct {
	ZalopayID        int64
	OnboardingID     int64
	ContractSigned   bool
	PartnerRequestID string
}

type InitContractResult struct {
	AuthMethod    string
	TransactionID string
}
