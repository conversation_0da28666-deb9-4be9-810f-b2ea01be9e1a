package _interface

import (
	"context"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

//go:generate mockgen --destination=../mocks/transaction/transaction.go --package=transaction_mocks . Transaction
type Transaction interface {
	WithTx(ctx context.Context, exec func(ctx context.Context) error) error
	BeginTx(ctx context.Context) (context.Context, error)
	CommitTx(ctx context.Context) error
	RollbackTx(ctx context.Context) error
}

//go:generate mockgen --destination=../mocks/repository/onboarding.go --package=repository_mocks . OnboardingRepo
type OnboardingRepo interface {
	ListOnboardingByUserID(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error)
	GetActiveOnboarding(ctx context.Context, zalopayID int64, partnerCode string) (*model.Onboarding, error)
	ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error)
	ListActiveOnboardingByZalopayIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Onboarding, error)
	UpdateOnboardingInfo(ctx context.Context, onboarding *model.Onboarding) error
	CreateUserOnboarding(ctx context.Context, onboarding *model.Onboarding) (int64, error)
	UpdateBasicOnboarding(ctx context.Context, onboarding *model.Onboarding) error
	UpdateStepAndRejectCode(ctx context.Context, onboardingID int64, step model.OnboardingStep, rejectCode string) error
	UpdateStepAndExtraProfile(ctx context.Context, onboarding *model.Onboarding) error
	UpdateStepAndPartnerData(ctx context.Context, onboardingID int64, step model.OnboardingStep, partnerData model.PartnerOnboardingData) error
	GetOnboardingByIDAndUserID(ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error)
	GetOnboardingByIDAndUserIDForUpdate(ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error)
	UpdatePartnerData(ctx context.Context, params *model.UpdatePartnerDataParams) error
	UpdateOnboardingStep(ctx context.Context, zalopayID, onboardingID int64, step model.OnboardingStep) error
	MarkOnboardingInactive(ctx context.Context, zalopayID, onboardingID int64) error
	UpdateOnboardingApproval(
		ctx context.Context, zalopayID, onboardingID int64,
		updFunc model.UpdateOnboardingFunc) (*model.Onboarding, error)
	UpdateOnboardingApprovalWithTx(
		ctx context.Context, zalopayID, onboardingID int64,
		updFunc model.UpdateOnboardingFunc) (*model.Onboarding, error)
	UpdateAccountID(ctx context.Context, zalopayID, onboardingID, accountID int64) error
	UpdateAccountIDAfterBound(ctx context.Context, zalopayID, onboardingID, accountID int64) error
}

//go:generate mockgen --destination=../mocks/repository/user.go --package=repository_mocks . UsersRepo
type UsersRepo interface {
	StoreUserDemographics(ctx context.Context, partner partner.PartnerCode, resources []model.Resource) error
	GetUserDemographics(ctx context.Context, partner partner.PartnerCode) ([]model.Resource, error)
}

//go:generate mockgen --destination=../mocks/adapter/job_task.go --package=adapter_mocks . JobTaskMgmt
type JobTaskMgmt interface {
	RegisterSubmitFaceImageTask(ctx context.Context, params *dto.FaceImgSubmitTask) error
	RegisterContractSigningTask(ctx context.Context, params *dto.ContractSingingTask) error
	RegisterQueryOnboardingStatusTask(ctx context.Context, params *model.OnboardingStatusTask) error
	CanTriggerQueryOnboardingStatusTask(ctx context.Context, params *model.OnboardingStatusTask) (bool, error)
}

//go:generate mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
type CIMBService interface {
	FetchResources(ctx context.Context, resourceTypes []string) ([]model.Resource, error)
	CheckOnboardingPermission(ctx context.Context, params *dto.OnboardingPermissionParams) (*model.OnboardingPermission, error)
	SubmitCasaOnboarding(ctx context.Context, onboarding *model.Onboarding, isResubmitted bool) (string, error)
	RequestOTP(ctx context.Context, req *dto.RequestOTPParams) (*dto.RequestOTPResult, error)
	VerifyOTP(ctx context.Context, req *dto.VerifyOTPParams) (bool, error)
	GetContractData(ctx context.Context, req *dto.GetContractParams) (*model.ContractData, error)
	UploadSelfieImage(ctx context.Context, req *dto.SelfieUploadParams) (txnExp bool, err error)
	RequestSelfieUploadUrl(ctx context.Context, req *dto.SelfieUploadUrlParams) (url string, txnExp bool, err error)
	UploadSelfieImageByURL(ctx context.Context, uploadUrl string, imageData *model.Image) error
	InitContractProcess(ctx context.Context, zalopayID int64, partnerReqID string) (contractTxnID string, err error)
	MarkContractComplete(ctx context.Context, zalopayID int64, faceReqID, partnerReqID, partnerTransID string) (signed bool, err error)
	QueryOnboardingStatus(ctx context.Context, zalopayID int64, onboardingID int64) (*cimb.OnboardingStatus, error)
	RequestLinkingAccount(ctx context.Context, onboarding *model.Onboarding) (string, error)
}

//go:generate mockgen --destination=../mocks/adapter/account_service.go --package=adapter_mocks . AccountService
type AccountService interface {
	CreateAccount(ctx context.Context, in *dto.CreateAccountParams) (accountID int64, err error)
}

//go:generate mockgen --destination=../mocks/adapter/minio_uploader.go --package=adapter_mocks . MinioUploader
type MinioUploader interface {
	GetFaceImageUrl(ctx context.Context, zalopayID int64, imagePath string) (string, error)
	UploadSignatureImage(ctx context.Context, params *dto.WetSignUploadParams) error
	FetchAndUploadFaceImage(ctx context.Context, params *dto.FaceImgStoreParams) (*model.Image, error)
	FetchAndUploadKYCImages(ctx context.Context, params *dto.UserKycImageStoreParams) (*model.Image, error)
}

//go:generate mockgen --destination=../mocks/adapter/user_profile_service.go --package=adapter_mocks . UserProfileService
type UserProfileService interface {
	GetProfile(ctx context.Context, request dto.ZPUserProfileParams) (*model.UserProfile, error)
	GetKycNfcData(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error)
	GetKycNfcStatus(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error)
	GetKycProgStatus(ctx context.Context, zalopayID int64) (*model.UserKycProgress, error)
	ResetKycNfcData(ctx context.Context, zalopayID int64) error
	VerifyKycNfcBCA(ctx context.Context, zalopayID int64) error
}

//go:generate mockgen --destination=../mocks/adapter/user_challenge_service.go --package=adapter_mocks . UserChallengeService
type UserChallengeService interface {
	GetFaceChallengeStatus(ctx context.Context, zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error)
	GetFaceChallengeData(ctx context.Context, zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error)
}

//go:generate mockgen --destination=../mocks/adapter/image_fetcher.go --package=adapter_mocks . ImageFetcher
type ImageFetcher interface {
	// GetKycImageData fetches kyc image data from the given url;
	// In context zalopay user, all scenarios about user image are related to KYC domain
	GetKycImageData(ctx context.Context, url string) (*model.Image, error)
}

//go:generate mockgen --destination=../mocks/adapter/dist_lock.go --package=adapter_mocks . DistributedLock
type DistributedLock interface {
	// Acquire attempts to acquire the lock for the specified resource.
	// It returns an error if the lock could not be acquired.
	Acquire(ctx context.Context, resource string, ttl time.Duration) error

	// Release releases the lock for the specified resource.
	// It returns an error if the lock could not be released.
	Release(ctx context.Context, resource string) error
}

//go:generate mockgen --destination=../mocks/adapter/rate_limiter.go --package=adapter_mocks . RateLimiter
type RateLimiter interface {
	AllowResetKycNfc(ctx context.Context, key string) (bool, error)
}

//go:generate mockgen --destination=../mocks/adapter/fraud_service.go --package=adapter_mocks . FraudService
type FraudService interface {
	EvaluateRisk(ctx context.Context, data *dto.UserRiskEvaluationParams) (*dto.UserRiskEvaluationResult, error)
	GetUnderwriting(ctx context.Context, data *dto.UserRiskUnderwritingParams) (map[string]any, error)
}

//go:generate mockgen --destination=../mocks/adapter/whitelist_service.go --package=adapter_mocks . WhitelistSys
type WhitelistSys interface {
	IsOnboardWhitelisted(ctx context.Context, zalopayID int64) (bool, error)
}
