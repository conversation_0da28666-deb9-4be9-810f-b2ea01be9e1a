package usecase

import (
	"context"
	"fmt"
	"testing"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"go.uber.org/goleak"
	"go.uber.org/mock/gomock"
)

func TestMain(m *testing.M) {
	goleak.VerifyTestMain(m)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{
		NfcStatus:     model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}
	onboardingPerm := &model.OnboardingPermission{Allowed: true}

	// Mock expectations
	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, &dto.OnboardingPermissionParams{
		ZalopayID:   zalopayID,
		IDNumber:    userProfile.IdNumber,
		PhoneNumber: userProfile.PhoneNumber,
	}).Return(onboardingPerm, nil)
	s.onboardRepo.EXPECT().CreateUserOnboarding(ctx, &model.Onboarding{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			Gender:      userProfile.Gender,
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
		}},
	}).Return(int64(1), nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(1), result.OnboardingID)
	s.Equal(partner.PartnerCIMB, result.PartnerCode)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_CreateOnboardingError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{NfcStatus: model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}
	onboardingPerm := &model.OnboardingPermission{Allowed: true}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, gomock.Any()).Return(onboardingPerm, nil)
	s.onboardRepo.EXPECT().CreateUserOnboarding(ctx, &model.Onboarding{
		ID:          model.InitOnboardID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
			Gender:      userProfile.Gender,
		}},
	}).Return(model.InitOnboardID, nil).Times(1)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeInvalidArgument))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_LockAcquisitionFailed() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(fmt.Errorf("lock error"))

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeResourceLockedForProcessing))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_UserNotWhitelisted() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(false, nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeUserNonWhitelist))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_ExistingActiveOnboarding() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	existingOnboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		CurrentStep: model.OnboardingStepEligible,
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(existingOnboarding, nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(1), result.OnboardingID)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_InvalidKycProfile() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	invalidProfile := s.buildUserProfile()
	invalidProfile.IdNumber = "" // Invalid profile with missing ID
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(invalidProfile, nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeProfileProblems))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_InvalidKycNfcData() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	invalidKycNfcData := &model.UserKycNfc{
		NfcStatus:  model.KycNfcStatusVerified,
		NfcDataRaw: model.KycNfcDataRaw{},
	} // Empty NFC data

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(invalidKycNfcData, nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeProfileProblems))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_PartnerPermissionError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{
		NfcStatus:     model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, gomock.Any()).Return(nil, fmt.Errorf("partner error"))

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeCallCIMBFailed))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_NotEligible() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{
		NfcStatus:     model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}
	onboardingPerm := &model.OnboardingPermission{
		Allowed:    false,
		IsWarning:  false,
		RejectCode: "NOT_ELIGIBLE",
	}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(nil, model.ErrOnboardingNotFound)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, gomock.Any()).Return(onboardingPerm, nil)
	s.onboardRepo.EXPECT().CreateUserOnboarding(ctx, &model.Onboarding{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepIneligible,
		RejectCode:  "PERM:NOT_ELIGIBLE",
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			Gender:      userProfile.Gender,
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
		}},
	}).Return(int64(1), nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotEligibleToRegister))
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_UpdateExistingOnboarding() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	existingOnboarding := &model.Onboarding{
		ID:          int64(1),
		ZalopayID:   zalopayID,
		RejectCode:  "PERM:NOT_ELIGIBLE",
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepIneligible,
	}

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{NfcStatus: model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}
	onboardingPerm := &model.OnboardingPermission{Allowed: true}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(existingOnboarding, nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil).Times(1)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, gomock.Any()).Return(onboardingPerm, nil)
	s.onboardRepo.EXPECT().UpdateBasicOnboarding(ctx, &model.Onboarding{
		ID:          int64(1),
		ZalopayID:   zalopayID,
		RejectCode:  "",
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
			Gender:      userProfile.Gender,
		}},
	}).Return(nil)

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(1), result.OnboardingID)
}

func (s *ProfileUsecaseTestSuite) TestProfileUsecase_RegisterOnboarding_UpdateOnboardingError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB,
	}

	lockKey := fmt.Sprintf(registerOBLockKeyPattern, partner.PartnerCIMB, zalopayID)
	lockKey, _ = s.redisKeyGen.Generate(lockKey)

	userProfile := s.buildUserProfile()
	profileParams := dto.ZPUserProfileParams{ZalopayID: zalopayID, BasicProfile: true, IdentityProfile: true, KycProfile: true}
	kycNfcData := &model.UserKycNfc{
		NfcStatus:     model.KycNfcStatusVerified,
		CollectSource: "jth",
		NfcId:         "1234567890",
		NfcDataRaw: model.KycNfcDataRaw{
			Sod: "1234567890",
		},
	}
	onboardingPerm := &model.OnboardingPermission{Allowed: true}

	s.distLocker.EXPECT().Acquire(ctx, lockKey, registerOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, lockKey).Return(nil)
	s.whitelistSys.EXPECT().IsOnboardWhitelisted(ctx, zalopayID).Return(true, nil)
	s.onboardRepo.EXPECT().GetActiveOnboarding(ctx, zalopayID, partner.PartnerCIMB.String()).Return(&model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepIneligible,
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
			Gender:      userProfile.Gender,
		}},
	}, nil)
	s.fraudService.EXPECT().EvaluateRisk(ctx, &dto.UserRiskEvaluationParams{
		ZalopayID:   zalopayID,
		RequestTime: s.usecase.timeNow().UnixMilli(),
	}).Return(&dto.UserRiskEvaluationResult{HasFraud: false}, nil)
	s.userProfile.EXPECT().GetProfile(ctx, profileParams).Return(userProfile, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcData, nil)
	s.cimbService.EXPECT().CheckOnboardingPermission(ctx, gomock.Any()).Return(onboardingPerm, nil)

	expectedOnboarding := &model.Onboarding{
		ID:          1,
		ZalopayID:   zalopayID,
		RejectCode:  "",
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		UserInfo: &cimb.UserOnboardingInfo{UserProfile: model.UserProfile{
			FullName:    userProfile.FullName,
			IdNumber:    userProfile.IdNumber,
			PhoneNumber: userProfile.PhoneNumber,
			Gender:      userProfile.Gender,
		}},
	}

	s.onboardRepo.EXPECT().UpdateBasicOnboarding(ctx, expectedOnboarding).Return(fmt.Errorf("update error"))

	// Act
	result, err := s.usecase.RegisterOnboarding(ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}
