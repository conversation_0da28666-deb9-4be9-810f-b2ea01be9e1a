package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (uc *ContractUsecase) GetContract(ctx context.Context,
	userID int64, onboardID int64) (model.ContractData, error) {
	logging := log.With(uc.kLogger, "user_id", userID, "onboarding_id", onboardID)
	logger := log.NewHelper(logging).WithContext(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, userID, onboardID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserID failed: %v", err)
		return model.ContractData{}, errorkit.NewError(errorkit.CodeRepositoryError, "get onboarding from db failed")
	}
	if !userOb.PartnerData.IsContractReady() {
		logger.Warn("contract is not available")
		return model.ContractData{}, nil
	}

	partnerReqID := userOb.PartnerData.GetPartnerRequestID()
	contractSigned := userOb.PartnerData.IsContractSigned()
	contractData, err := uc.cimbService.GetContractData(ctx, &dto.GetContractParams{
		ZalopayID:        userID,
		OnboardingID:     onboardID,
		ContractSigned:   contractSigned,
		PartnerRequestID: partnerReqID,
	})
	if err != nil {
		logger.Errorf("GetContractData failed: %v", err)
		return model.ContractData{}, nil
	}
	if contractData == nil {
		logger.Warn("GetContractData return nil")
		return model.ContractData{}, nil
	}
	return model.ContractData{
		SignedContract:   contractData.SignedContract,
		UnsignedContract: contractData.UnsignedContract,
	}, nil
}

func (uc *ContractUsecase) RequestContractOTP(ctx context.Context, zalopayID, onboardingID int64) (*dto.RequestOTPResult, error) {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "RequestContractOTP", "zalopayID", zalopayID, "onboardingID", onboardingID)

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get active onboarding fail: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if !obd.CurrentStep.CanContractOTPConfirmation() {
		logger.Warnf("onboarding is not available request contract otp, current_step: %s", obd.CurrentStep)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available for request contract otp").
			WithKind(errorkit.TypePrecondition)
	}

	resp, err := uc.cimbService.RequestOTP(ctx, &dto.RequestOTPParams{
		Type:             model.OTPContract,
		ZalopayID:        obd.ZalopayID,
		OnboardingID:     obd.ID,
		PartnerRequestID: obd.PartnerData.GetPartnerRequestID(),
	})
	if err != nil {
		logger.Errorf("RequestContractOTP failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "request otp failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	updateFunc := func(pod *cimb.PartnerOnboardingData) *cimb.PartnerOnboardingData {
		pod.SignOTPRequestID = resp.OtpRequestId
		return pod
	}

	obd.PartnerData = cimb.UpdatePartnerOnboardingData(obd.PartnerData, updateFunc)

	if err = uc.onboardRepo.UpdateOnboardingInfo(ctx, obd); err != nil {
		logger.Errorf("update onboarding info failed after request contract otp: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding info failed after request contract otp").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return resp, nil
}

func (uc *ContractUsecase) RequestLinkingOTP(ctx context.Context, zalopayID, onboardingID int64) (*dto.RequestOTPResult, error) {
	logger := uc.logger.WithContext(ctx)

	logger.Infow("msg", "RequestLinkingOTP", "zalopayID", zalopayID, "onboardingID", onboardingID)

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get active onboarding fail: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if !obd.PartnerData.IsLinkingInit() || !obd.CurrentStep.CanLinkingOTPConfirmation() {
		logger.Warnf("onboarding step is not available request linking otp, current_step: %s", obd.CurrentStep)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available for request linking otp").
			WithKind(errorkit.TypePrecondition)
	}

	resp, err := uc.cimbService.RequestOTP(ctx, &dto.RequestOTPParams{
		Type:             model.OTPLinking,
		ZalopayID:        obd.ZalopayID,
		OnboardingID:     obd.ID,
		PartnerRequestID: obd.PartnerData.GetLinkingRequestID(),
	})
	if err != nil {
		logger.Errorf("RequestLinkingOTP failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "request linking otp failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// NOTE: Currently support for cimb partner only, if process with other partner, need to update this part
	// by switch-case for each partner or implement more generic way in partner data interface
	partnerCode := partner.CodeFromString(obd.PartnerCode)
	updateFunc := func(pod *cimb.PartnerOnboardingData) *cimb.PartnerOnboardingData {
		pod.LinkOTPRequestID = resp.OtpRequestId
		return pod
	}
	obd.PartnerData = cimb.UpdatePartnerOnboardingData(obd.PartnerData, updateFunc)

	updateParams := &model.UpdatePartnerDataParams{
		ZalopayID:    zalopayID,
		OnboardingID: onboardingID,
		PartnerCode:  partnerCode,
		PartnerData:  obd.PartnerData,
	}
	if err = uc.onboardRepo.UpdatePartnerData(ctx, updateParams); err != nil {
		logger.Errorf("update onboarding info failed after request linking otp: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding info failed after request linking otp").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return resp, nil
}

func (uc *ContractUsecase) VerifyContractOTP(ctx context.Context,
	zalopayID, onboardingID int64, otpCode string) (bool, error) {
	logger := uc.logger.WithContext(ctx)
	logger.Infow("msg", "VerifyContractOTP", "zalopayID", zalopayID, "onboardingID", onboardingID)

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get active onboarding fail: %v", err)
		return false, errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	//CanContractOTPConfirmation
	if !obd.CurrentStep.CanContractOTPConfirmation() {
		logger.Warnf("onboarding is not available signing, current_step: %s", obd.CurrentStep)
		return false, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available for verify otp").
			WithKind(errorkit.TypePrecondition)
	}

	partnerData := obd.PartnerData
	if partnerData.GetPartnerRequestID() == "" || partnerData.GetPartnerTransactionID() == "" {
		logger.Errorf("partner_request_id|partner_transaction_id is empty")
		return false, errorkit.
			NewError(errorkit.CodeConditionFailed, "partner_request_id|partner_transaction_id is empty").
			WithKind(errorkit.TypeInvalidArg)
	}

	status, err := uc.cimbService.VerifyOTP(ctx, &dto.VerifyOTPParams{
		Type:                model.OTPContract,
		ZalopayID:           obd.ZalopayID,
		OnboardingID:        obd.ID,
		PartnerOTPCode:      otpCode,
		PartnerRequestID:    partnerData.GetPartnerRequestID(),
		PartnerOTPRequestId: partnerData.GetPartnerTransactionID(),
	})
	if err != nil {
		logger.Errorf("VerifyContractOTP failed: %v", err)
		return false, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "verify otp failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if !status {
		logger.Warnf("otp verification failed")
		return false, nil
	}

	// Recheck and update an onboarding step to otp verified
	if err = uc.updateStepWithApprovalCheck(
		ctx, obd.ZalopayID, obd.ID,
		model.OnboardingStepOTPVerified); err != nil {
		return false, err
	}

	ctx = context.WithoutCancel(ctx)
	go uc.RegisterSubmitFaceImageJob(ctx, zalopayID, onboardingID)

	return status, nil
}

func (uc *ContractUsecase) VerifyLinkingOTP(ctx context.Context,
	zalopayID, onboardingID int64, otpCode string) (bool, error) {
	logger := uc.logger.WithContext(ctx)
	logger.Infow("msg", "VerifyLinkingOTP", "zalopayID", zalopayID, "onboardingID", onboardingID)

	obd, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get active onboarding fail: %v", err)
		return false, errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	if !obd.PartnerData.IsLinkingInit() || !obd.CurrentStep.CanLinkingOTPConfirmation() {
		logger.Warnf("onboarding is not available for verify linking otp, current_step: %s", obd.CurrentStep)
		return false, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available for verify linking otp").
			WithKind(errorkit.TypePrecondition)
	}

	partnerData := obd.PartnerData
	if partnerData.GetLinkingRequestID() == "" || partnerData.GetLinkingTransactionID() == "" {
		logger.Errorf("linking_request_id|linking_transaction_id is empty")
		return false, errorkit.
			NewError(errorkit.CodeConditionFailed, "linking_request_id|linking_transaction_id is empty").
			WithKind(errorkit.TypeInvalidArg)
	}

	status, err := uc.cimbService.VerifyOTP(ctx, &dto.VerifyOTPParams{
		Type:                model.OTPLinking,
		ZalopayID:           obd.ZalopayID,
		OnboardingID:        obd.ID,
		PartnerOTPCode:      otpCode,
		PartnerRequestID:    partnerData.GetLinkingRequestID(),
		PartnerOTPRequestId: partnerData.GetLinkingTransactionID(),
	})
	if err != nil {
		logger.Errorf("VerifyLinkingOTP failed: %v", err)
		return false, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "verify linking otp failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}
	if !status {
		logger.Warnf("otp verification failed")
		return false, nil
	}

	// Mark linking account success
	// NOTE: Currently support for cimb partner only, if process with other partner, need to update this part
	// by switch-case for each partner or implement more generic way in partner data interface
	partnerCode := partner.CodeFromString(obd.PartnerCode)
	updateFunc := func(pod *cimb.PartnerOnboardingData) *cimb.PartnerOnboardingData {
		pod.LinkingStatus = cimb.LinkingSuccess
		return pod
	}
	obd.PartnerData = cimb.UpdatePartnerOnboardingData(obd.PartnerData, updateFunc)

	updateParams := &model.UpdatePartnerDataParams{
		ZalopayID:    zalopayID,
		OnboardingID: onboardingID,
		PartnerCode:  partnerCode,
		PartnerData:  obd.PartnerData,
	}
	if err = uc.onboardRepo.UpdatePartnerData(ctx, updateParams); err != nil {
		logger.Errorf("update onboarding info failed after verify linking otp: %v", err)
		return false, errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding info failed after verify linking otp").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	return status, nil
}

func (uc *ContractUsecase) SubmitWetSign(ctx context.Context,
	params *dto.WetSignSubmitParams) (*dto.WetSignSubmitResult, error) {
	userID := cast.ToInt64(params.ZalopayID)
	onboardID := cast.ToInt64(params.OnboardingID)
	imgBase64 := params.ImageData
	imgMimeType := params.ImageMime.String()
	logger := uc.logger.WithContext(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, userID, onboardID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserID failed: %v", err)
		return nil, errorkit.NewError(errorkit.CodeRepositoryError, "get onboarding from db failed")
	}
	if !userOb.CurrentStep.CanSubmitSignature() {
		logger.Errorf("ob step is not valid for submit signature, status: %v", userOb.Status)
		return nil, errorkit.NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is not valid for submit signature")
	}

	// convert to image object and validate image data
	imgObject, err := zutils.Base64DataToImage(imgBase64, imgMimeType)
	if err != nil {
		logger.Errorf("Base64DataToImage failed: %v", err)
		return nil, errorkit.NewError(errorkit.CodeImageDataCorrupted, "convert base64 encoded data to image failed")
	}
	imgVerifyRes := uc.imgValidator.ValidateImage(imgObject, []validator.ImageValidationRule{
		validator.RuleImageSizeHD(),
		validator.RuleImageSingleColorOrEmpty(),
	})
	if model.ValidateResultSlice(imgVerifyRes).HasAbnormalRes() {
		logger.Errorf("image data is invalid: %v", imgVerifyRes)
		return nil, errorkit.NewError(errorkit.CodeImageDataInvalid, "image data is invalid")
	}

	// upload image to native object storage
	signPath := getESignImagePath(userID, onboardID, time.Now())
	signByte, err := zutils.Base64DataToBinary(imgBase64, imgMimeType)
	if err != nil {
		logger.Errorf("Base64DataToBinary failed: %v", err)
		return nil, errorkit.NewError(errorkit.CodeImageDataCorrupted, "convert base64 encoded data to binary failed")
	}

	signFileData := &dto.WetSignUploadParams{
		ZalopayID:    userID,
		OnboardingID: onboardID,
		ImagePath:    signPath,
		ImageData:    signByte,
		ContentType:  imgMimeType,
	}
	if err = uc.minioUploader.UploadSignatureImage(ctx, signFileData); err != nil {
		logger.Errorf("UploadSignImage failed: %v", err)
		return nil, errorkit.NewError(errorkit.CodeInternalError, "upload image to storage failed")
	}

	partnerData := userOb.PartnerData
	partnerData.SetSignatureImageUri(signPath, imgMimeType)

	err = uc.onboardRepo.UpdatePartnerData(ctx, &model.UpdatePartnerDataParams{
		ZalopayID:    userID,
		OnboardingID: onboardID,
		PartnerCode:  partner.PartnerCIMB,
		PartnerData:  partnerData,
	})
	if err != nil {
		logger.Errorf("UpdatePartnerData failed: %v", err)
		return nil, errorkit.NewError(errorkit.CodeRepositoryError, "update partner data failed")
	}

	return &dto.WetSignSubmitResult{
		OnboardingID: params.OnboardingID,
	}, nil
}

// TriggerContractSigningJob trigger a job to process contract for partner
// It will check onboarding status and current step to ensure that user can submit face image
// If user can submit face image, it will register a job to submit face image to partner
// Often use for operation flow
func (uc *ContractUsecase) TriggerContractSigningJob(ctx context.Context, zalopayID, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	// Get onboarding info to double check that zalopayID and onboardingID are valid of user
	onboarding, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("get active onboarding fail: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get active onboarding failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if !onboarding.CurrentStep.CanSigningContract() {
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is not available for process contract signing").
			WithKind(errorkit.TypePrecondition)
	}

	if err = uc.RegisterContractSigningJob(ctx, zalopayID, onboardingID); err != nil {
		logger.Errorf("register contract signing job fail: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "register contract signing job failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	return nil
}

// Deprecated: please not use this method anymore
// RegisterSubmitFaceImageJob register a job to submit face image to partner
// It not validate onboarding status and current step, so caller must ensure that onboarding status is valid before call this function
func (uc *ContractUsecase) RegisterSubmitFaceImageJob(ctx context.Context, zalopayID, onboardingID int64) error {
	maxRetry := 5
	delayTime := 5 * time.Second
	logger := uc.logger.WithContext(ctx)

	err := retry.NewRetry(maxRetry, delayTime, nil).Execute(ctx, func() error {
		err := uc.jobTaskMgmt.RegisterSubmitFaceImageTask(ctx, &dto.FaceImgSubmitTask{
			ZalopayID:    cast.ToString(zalopayID),
			OnboardingID: cast.ToString(onboardingID),
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("register upload face image job fail, error=%v", err)
		return err
	}
	return nil
}

// RegisterContractSigningJob register a job for process contract signing
// It not validate onboarding status and current step, so caller must ensure that onboarding status is valid before call this function
func (uc *ContractUsecase) RegisterContractSigningJob(ctx context.Context, zalopayID, onboardingID int64) error {
	maxRetry := 5
	delayTime := 5 * time.Second
	logger := uc.logger.WithContext(ctx)

	err := retry.NewRetry(maxRetry, delayTime, nil).Execute(ctx, func() error {
		err := uc.jobTaskMgmt.RegisterContractSigningTask(ctx, &dto.ContractSingingTask{
			ZalopayID:    cast.ToString(zalopayID),
			OnboardingID: cast.ToString(onboardingID),
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Errorf("register contract signing job fail, error=%v", err)
		return err
	}
	return nil
}

func (uc *ContractUsecase) SubmitFaceImageToPartner(ctx context.Context, onboarding *model.Onboarding) error {
	logger := uc.logger.WithContext(ctx)

	// Get full face image url from object storage
	fcImagePath := onboarding.UserInfo.GetFaceChallengeImg().GetPath()
	if fcImagePath == "" {
		logger.Errorf("face challenge image path is empty")
		return errorkit.NewError(errorkit.CodeFaceChallengeInvalid, "face challenge image path is empty")
	}

	fcImageUrl, err := uc.minioUploader.GetFaceImageUrl(ctx, onboarding.ZalopayID, fcImagePath)
	if err != nil {
		logger.Errorf("get face image url failed: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "get face image url failed")
	}

	logger.Infow(
		"msg", "get face image url success",
		"zalopay_id", onboarding.ZalopayID,
		"face_image_url", fcImageUrl)

	// Request cimb partner upload face image
	txnExpired, err := uc.cimbService.UploadSelfieImage(ctx, &dto.SelfieUploadParams{
		ImageUrl:            fcImageUrl,
		ZalopayID:           onboarding.ZalopayID,
		OnboardingID:        onboarding.ID,
		PartnerRequestID:    onboarding.PartnerData.GetPartnerRequestID(),
		PartnerOTPRequestId: onboarding.PartnerData.GetPartnerTransactionID(),
	})
	if err != nil {
		logger.Errorf("UploadSelfieImage failed: %v", err)
		return errorkit.NewError(errorkit.CodeCallCIMBFailed, "upload selfie image failed")
	}
	if txnExpired {
		logger.Warn("contract transaction expired")
		return errorkit.NewError(errorkit.CodeContractTransactionExpired, "contract transaction expired")
	}
	return nil
}

func (uc *ContractUsecase) InitContractSigning(ctx context.Context, onboarding *model.Onboarding) (*model.Onboarding, error) {
	logger := uc.logger.WithContext(ctx)

	transactionID, err := uc.cimbService.InitContractProcess(ctx, onboarding.ZalopayID, onboarding.PartnerData.GetPartnerRequestID())
	if errors.Is(err, model.ErrContractHasBeenSigned) {
		logger.Warn("contract has been signed")
		return nil, errorkit.
			NewError(errorkit.CodeContractHasBeenSigned, "contract has been signed").
			WithKind(errorkit.TypePrecondition)
	}
	if err != nil {
		logger.Errorf("InitContractProcess failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "init contract process failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// NOTE: Currently support for cimb partner only, if process with other partner, need to update this part
	// by switch-case for each partner or implement more generic way in partner data interface
	partnerData := onboarding.PartnerData
	currentStep := model.OnboardingStepContractSinging
	partnerData.SetPartnerTransactionID(transactionID)

	if err = uc.updateStepAndPartnerWithApprovalCheck(ctx, onboarding.ZalopayID, onboarding.ID, currentStep, partnerData); err != nil {
		logger.Errorf("update onboarding failed after init contract singing: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding failed after init contract signing").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	onboarding.PartnerData = partnerData
	return onboarding, nil
}

func (uc *ContractUsecase) MarkContractComplete(ctx context.Context, onboarding *model.Onboarding) error {
	logger := uc.logger.WithContext(ctx)

	completed, err := uc.cimbService.MarkContractComplete(ctx,
		onboarding.ZalopayID,
		onboarding.UserInfo.GetFaceChallengeID(),
		onboarding.PartnerData.GetPartnerRequestID(),
		onboarding.PartnerData.GetPartnerTransactionID(),
	)
	if err != nil {
		logger.Errorf("MarkContractComplete failed: %v", err)
		return errorkit.NewError(errorkit.CodeCallCIMBFailed, "confirm contract completion failed")
	}
	if !completed {
		logger.Warn("contract is not fully signed")
		return errorkit.NewError(errorkit.CodeInternalError, "contract is not fully signed")
	}
	return nil
}

// This function is only wrapper for UpdateStepWithApprovalCheck to update onboarding step to waiting approval
func (uc *ContractUsecase) UpdateStepWhenContractSigned(
	ctx context.Context, userID, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	err := uc.updateStepWithApprovalCheck(
		ctx, userID, onboardingID,
		model.OnboardingStepWaitingApproval,
	)
	if err != nil {
		logger.Errorf("UpdateStepWithApprovalCheck failed: %v", err)
		return err
	}
	return nil
}

func (uc *ContractUsecase) updateStepWithApprovalCheck(
	ctx context.Context,
	userID, onboardingID int64,
	newStep model.OnboardingStep) error {
	logger := uc.logger.WithContext(ctx)

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("BeginTx failed: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "begin transaction failed").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserIDForUpdate(ctx, userID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserIDForUpdate failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if userOb.IsCompleted() {
		logger.Warn("onboarding is completed, skip step update")
		return errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding is completed").
			WithKind(errorkit.TypePrecondition)
	}

	if err = uc.onboardRepo.UpdateOnboardingStep(ctx, userID, onboardingID, newStep); err != nil {
		logger.Errorf("UpdateOnboardingStep failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding step failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("CommitTx failed: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "commit transaction failed").WithCause(err)
	}

	return nil
}

func (uc *ContractUsecase) updateStepAndPartnerWithApprovalCheck(
	ctx context.Context,
	userID, onboardingID int64,
	newStep model.OnboardingStep,
	partnerData model.PartnerOnboardingData) error {
	logger := uc.logger.WithContext(ctx)

	ctx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("BeginTx failed: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "begin transaction failed").WithCause(err)
	}
	defer uc.txn.RollbackTx(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserIDForUpdate(ctx, userID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserIDForUpdate failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if userOb.IsCompleted() {
		logger.Warn("onboarding is completed, skip step update")
		return errorkit.
			NewError(errorkit.CodeOnboardingCompleted, "onboarding is completed").
			WithKind(errorkit.TypePrecondition)
	}

	if err = uc.onboardRepo.UpdateStepAndPartnerData(ctx, onboardingID, newStep, partnerData); err != nil {
		logger.Errorf("UpdateStepAndPartnerData failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update onboarding step and partner data failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if err = uc.txn.CommitTx(ctx); err != nil {
		logger.Errorf("CommitTx failed: %v", err)
		return errorkit.NewError(errorkit.CodeInternalError, "commit transaction failed").WithCause(err)
	}

	return nil
}

func getESignImagePath(userID, onboardingID int64, time time.Time) string {
	const pattern = "onboarding/%d/%d/images/%s_esign"
	timestamp := time.Format("20060102_1504")
	filePath := fmt.Sprintf(pattern, userID, onboardingID, timestamp)
	return filePath
}
