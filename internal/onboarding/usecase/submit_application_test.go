package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *ProfileUsecaseTestSuite) TestSubmitApplication_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	partnerRequestID := "req-123"
	userExtra := &model.UserAdditionalInfo{
		JobTitle:         model.DefaultJobTitle,
		LivingCity:       model.DefaultLivingCity,
		Occupation:       model.DefaultOccupation,
		SourceOfFund:     model.DefaultSourceOfFund,
		LoanPurpose:      model.DefaultLoanPurpose,
		Education:        model.DefaultEducationLevel,
		EmploymentStatus: model.DefaultEmploymentStatus,
		MonthlyIncome:    model.DefaultMonthlyIncome,
		FundPurpose:      model.DefaultFundPurposeDesc,
		OverdraftLimit:   model.DefaultOverdraftLimit,
	}

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		CurrentStep: model.OnboardingStepEligible,
		UserInfo: &cimb.UserOnboardingInfo{
			UserUnderwritingData: map[string]any{},
		},
		PartnerData: &cimb.PartnerOnboardingData{},
	}

	userProfile := s.buildUserProfile()

	userProfileParams := dto.ZPUserProfileParams{
		ZalopayID:       zalopayID,
		KycProfile:      true,
		BasicProfile:    true,
		IdentityProfile: true,
	}

	kycNfcInfo := &model.UserKycNfc{
		NfcStatus:  model.KycNfcStatusExisted,
		NfcDataDG2: "nfc-data",
		NfcDataDG13: model.KycNfcDataDG13{
			EIDNumber:      "123",
			FullName:       "John Doe",
			Gender:         2,
			Birthday:       "1990-01-01",
			IssueDate:      "2022-11-05",
			PlaceResidence: "Hanoi",
		},
		NfcDataRaw: model.KycNfcDataRaw{
			Com: "123",
		},
	}

	fraudCheckResult := &dto.UserRiskEvaluationResult{
		HasFraud: false,
	}

	// Mock expectations
	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), submitOBLockTTL).Return(nil).Times(1)
	s.distLocker.EXPECT().Release(ctx, gomock.Any()).Times(1)
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.userProfile.EXPECT().GetKycNfcData(ctx, zalopayID).Return(kycNfcInfo, nil)
	s.userProfile.EXPECT().GetProfile(ctx, userProfileParams).Return(userProfile, nil)
	s.cimbService.EXPECT().SubmitCasaOnboarding(ctx, onboarding, false).Return(partnerRequestID, nil)
	s.fraudService.EXPECT().EvaluateRisk(ctx, gomock.Any()).Return(fraudCheckResult, nil)
	s.fraudService.EXPECT().GetUnderwriting(ctx, gomock.Any()).Return(map[string]any{}, nil)
	s.onboardRepo.EXPECT().UpdateOnboardingInfo(ctx, onboarding).Return(nil)
	s.userProfile.EXPECT().VerifyKycNfcBCA(ctx, gomock.Any()).Return(nil)
	s.minioUploader.EXPECT().FetchAndUploadKYCImages(gomock.Any(), gomock.Any()).Return(&model.Image{
		URL:         "url",
		Path:        "path",
		Checksum:    "123123aa",
		DataBuffer:  []byte("data"),
		ContentType: "image/jpeg",
	}, nil).Times(3)
	s.jobTaskMgmt.EXPECT().RegisterQueryOnboardingStatusTask(gomock.Any(), &model.OnboardingStatusTask{
		ZalopayID:    cast.ToString(zalopayID),
		OnboardingID: cast.ToString(onboardingID),
		PartnerReqID: partnerRequestID,
		CurrentStep:  model.OnboardingStepProfileSuccess.String(),
	}).Return(nil)

	// Mock resource validation
	s.mockGetResourcesSuccess()

	// Act
	result, err := s.usecase.SubmitApplication(ctx, zalopayID, onboardingID, userExtra)

	time.Sleep(1 * time.Second)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.OnboardingStepProfileSuccess, result.CurrentStep)
	s.Equal(partnerRequestID, result.PartnerData.GetPartnerRequestID())
}

func (s *ProfileUsecaseTestSuite) TestSubmitApplication_LockAcquisitionError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	userExtra := &model.UserAdditionalInfo{}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), submitOBLockTTL).Return(errors.New("lock error"))

	// Act
	result, err := s.usecase.SubmitApplication(ctx, zalopayID, onboardingID, userExtra)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeResourceLockedForProcessing))
}

func (s *ProfileUsecaseTestSuite) TestSubmitApplication_OnboardingNotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	userExtra := &model.UserAdditionalInfo{}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), submitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)
	s.mockGetResourcesSuccess()

	// Act
	result, err := s.usecase.SubmitApplication(ctx, zalopayID, onboardingID, userExtra)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *ProfileUsecaseTestSuite) TestSubmitApplication_InvalidStep() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	userExtra := &model.UserAdditionalInfo{}

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		CurrentStep: model.OnboardingStepIneligible,
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), submitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)
	s.mockGetResourcesSuccess()

	// Act
	result, err := s.usecase.SubmitApplication(ctx, zalopayID, onboardingID, userExtra)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingStepInvalidForExec))
}

func (s *ProfileUsecaseTestSuite) TestSubmitApplication_ValidationError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)
	userExtra := &model.UserAdditionalInfo{
		JobTitle: "invalid-job-title",
	}

	s.distLocker.EXPECT().Acquire(ctx, gomock.Any(), submitOBLockTTL).Return(nil)
	s.distLocker.EXPECT().Release(ctx, gomock.Any())
	s.mockGetResourcesSuccess()

	// Act
	result, err := s.usecase.SubmitApplication(ctx, zalopayID, onboardingID, userExtra)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeConditionFailed))
}

// Helper function to mock GetResources
func (s *ProfileUsecaseTestSuite) mockGetResourcesSuccess() {
	s.usersRepo.EXPECT().GetUserDemographics(gomock.Any(), gomock.Any()).Return([]model.Resource{
		{
			Type: model.ResourceTypeCity,
			Data: []model.ResourceData{
				{Code: model.DefaultLivingCity},
			},
		},
		{
			Type: model.ResourceTypeJobTitle,
			Data: []model.ResourceData{
				{Code: model.DefaultJobTitle},
			},
		},
		{
			Type: model.ResourceTypeEducation,
			Data: []model.ResourceData{
				{Code: model.DefaultEducationLevel},
			},
		},
		{
			Type: model.ResourceTypeIncome,
			Data: []model.ResourceData{
				{Code: model.DefaultMonthlyIncome},
			},
		},
		{
			Type: model.ResourceTypeOccupation,
			Data: []model.ResourceData{
				{Code: model.DefaultOccupation},
			},
		},
		{
			Type: model.ResourceTypeSourceOfFund,
			Data: []model.ResourceData{
				{Code: model.DefaultSourceOfFund},
			},
		},
		{
			Type: model.ResourceTypeEmploymentStt,
			Data: []model.ResourceData{
				{Code: model.DefaultEmploymentStatus},
			},
		},
	}, nil)
}

func (s *ProfileUsecaseTestSuite) TestEnrichUserExtra_Success() {
	// Arrange
	request := &model.UserAdditionalInfo{
		JobTitle: "", // Should be filled with default
	}

	// Act
	result, err := s.usecase.enrichUserExtra(request)

	// Assert
	s.NoError(err)
	s.Equal(model.DefaultJobTitle, result.JobTitle)
	s.Equal(model.DefaultLivingCity, result.LivingCity)
	s.Equal(model.DefaultOccupation, result.Occupation)
	s.Equal(model.DefaultSourceOfFund, result.SourceOfFund)
	s.Equal(model.DefaultLoanPurpose, result.LoanPurpose)
	s.Equal(model.DefaultEducationLevel, result.Education)
	s.Equal(model.DefaultEmploymentStatus, result.EmploymentStatus)
	s.Equal(model.DefaultMonthlyIncome, result.MonthlyIncome)
	s.Equal(model.DefaultFundPurposeDesc, result.FundPurpose)
	s.Equal(int64(model.DefaultOverdraftLimit), result.OverdraftLimit)
}

func (s *ProfileUsecaseTestSuite) TestGetKycImagesPath() {
	// Arrange
	userID := int64(123)
	onboardID := int64(456)
	submitTime := time.Date(2024, 3, 15, 14, 30, 0, 0, time.UTC)

	// Act
	selfie, icFront, icBack := getKycImagesPath(userID, onboardID, submitTime)

	// Assert
	expectedTimestamp := "20240315_1430"
	s.Equal(fmt.Sprintf("onboarding/%d/%d/%s_selfie", userID, onboardID, expectedTimestamp), selfie)
	s.Equal(fmt.Sprintf("onboarding/%d/%d/%s_ic_front", userID, onboardID, expectedTimestamp), icFront)
	s.Equal(fmt.Sprintf("onboarding/%d/%d/%s_ic_back", userID, onboardID, expectedTimestamp), icBack)
}
