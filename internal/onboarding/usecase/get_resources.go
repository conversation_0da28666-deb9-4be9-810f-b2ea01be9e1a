package usecase

import (
	"context"
	"fmt"
	"slices"
	"strconv"

	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (uc *ProfileUsecase) GetResources(ctx context.Context, resourceTypes []string) ([]model.Resource, error) {
	if result, err := uc.getResourcesFromCache(ctx, partner.PartnerCIMB, resourceTypes); err == nil {
		return filterResources(result, resourceTypes), nil
	}

	// Fetch resources from CIMB
	req := buildRequestResourceType(resourceTypes)
	resp, err := uc.cimbService.FetchResources(ctx, req)
	if err != nil {
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "fail to fetch resources from CIMB").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	// Add money income resource, then cache resources
	result := append(resp,
		initIncomeResource(),
		initFundPurposeResource(),
	)
	result = filterResources(result, resourceTypes)
	go uc.usersRepo.StoreUserDemographics(ctx, partner.PartnerCIMB, result)

	return result, nil
}

func (uc *ProfileUsecase) GetResourceByTypeAndCode(ctx context.Context, resourceType, code string) (model.ResourceData, error) {
	resources, err := uc.GetResources(ctx, []string{resourceType})
	errResNotFound := errors.Errorf("resource not found, type: %s, code: %s", resourceType, code)
	if err != nil {
		return model.ResourceData{}, err
	}
	if len(resources) == 0 {
		return model.ResourceData{}, errorkit.
			NewError(errorkit.CodeDataNotFound, errorkit.Message(errResNotFound.Error())).
			WithKind(errorkit.TypeNotFound)
	}

	for _, data := range resources[0].Data {
		if data.Code == code {
			return data, nil
		}
	}

	return model.ResourceData{}, errorkit.
		NewError(errorkit.CodeDataNotFound, errorkit.Message(errResNotFound.Error())).
		WithKind(errorkit.TypeNotFound)
}

func (uc *ProfileUsecase) getResourcesFromCache(
	ctx context.Context, partnerCode partner.PartnerCode,
	resourceTypes []string,
) ([]model.Resource, error) {
	logger := uc.logger.WithContext(ctx)
	resources, err := uc.usersRepo.GetUserDemographics(ctx, partnerCode)
	if err != nil {
		logger.Warnf("Fail to get user demographics, err %v", err)
		return nil, errors.Wrap(err, fmt.Sprintf("no user demographics found for partner %s", partnerCode))
	}
	resourcesMap := zutils.KeyBy(resources, func(r model.Resource) string {
		return r.Type
	})
	for _, rType := range resourceTypes {
		_, found := resourcesMap[rType]
		if found {
			continue
		}
		logger.Warnf("Resource type not found in cache, resource type: %s", rType)
		return nil, errors.Errorf("may be some resource type not found in cache, resource type: %s", rType)
	}
	return filterResources(resources, resourceTypes), nil
}

func initIncomeResource() model.Resource {
	const (
		StepIncome = 2000000
		MinIncome  = 3000000
		MaxIncome  = 27000000
	)

	var data []model.ResourceData

	for i := MinIncome; i <= MaxIncome; i += StepIncome {
		value := strconv.Itoa(i)
		data = append(data, model.ResourceData{
			Code:       value,
			Vietnamese: value,
			English:    value,
		})
	}
	return model.Resource{
		Type: model.ResourceTypeIncome,
		Data: data,
	}
}

func initFundPurposeResource() model.Resource {
	data := []model.ResourceData{{
		Code:       "01",
		Vietnamese: "Tiêu dùng cá nhân, mua sắm hàng hoá dịch vụ",
		English:    "Personal finance",
	}}
	return model.Resource{
		Type: model.ResourceTypeFundPurpose,
		Data: data,
	}
}

func filterResources(resources []model.Resource, resourceTypes []string) []model.Resource {
	if len(resourceTypes) == 0 {
		return resources
	}
	resourcesPairs := zutils.KeyBy(resources, func(r model.Resource) string {
		return r.Type
	})
	filteredResources := make([]model.Resource, 0)
	for _, rType := range resourceTypes {
		if r, ok := resourcesPairs[rType]; ok {
			filteredResources = append(filteredResources, r)
		}
	}
	return filteredResources
}

func buildRequestResourceType(resourceTypes []string) []string {
	result := make([]string, 0, len(resourceTypes))
	internalResourceTypes := []string{
		model.ResourceTypeIncome,
		model.ResourceTypeFundPurpose,
	}
	for _, rType := range resourceTypes {
		if slices.Contains(internalResourceTypes, rType) {
			continue
		}
		result = append(result, rType)
	}
	return result
}
