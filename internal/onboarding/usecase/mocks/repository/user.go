// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: UsersRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/user.go --package=repository_mocks . UsersRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	partner "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	gomock "go.uber.org/mock/gomock"
)

// MockUsersRepo is a mock of UsersRepo interface.
type MockUsersRepo struct {
	ctrl     *gomock.Controller
	recorder *MockUsersRepoMockRecorder
	isgomock struct{}
}

// MockUsersRepoMockRecorder is the mock recorder for MockUsersRepo.
type MockUsersRepoMockRecorder struct {
	mock *MockUsersRepo
}

// NewMockUsersRepo creates a new mock instance.
func NewMockUsersRepo(ctrl *gomock.Controller) *MockUsersRepo {
	mock := &MockUsersRepo{ctrl: ctrl}
	mock.recorder = &MockUsersRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUsersRepo) EXPECT() *MockUsersRepoMockRecorder {
	return m.recorder
}

// GetUserDemographics mocks base method.
func (m *MockUsersRepo) GetUserDemographics(ctx context.Context, partner partner.PartnerCode) ([]model.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDemographics", ctx, partner)
	ret0, _ := ret[0].([]model.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDemographics indicates an expected call of GetUserDemographics.
func (mr *MockUsersRepoMockRecorder) GetUserDemographics(ctx, partner any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDemographics", reflect.TypeOf((*MockUsersRepo)(nil).GetUserDemographics), ctx, partner)
}

// StoreUserDemographics mocks base method.
func (m *MockUsersRepo) StoreUserDemographics(ctx context.Context, partner partner.PartnerCode, resources []model.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreUserDemographics", ctx, partner, resources)
	ret0, _ := ret[0].(error)
	return ret0
}

// StoreUserDemographics indicates an expected call of StoreUserDemographics.
func (mr *MockUsersRepoMockRecorder) StoreUserDemographics(ctx, partner, resources any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreUserDemographics", reflect.TypeOf((*MockUsersRepo)(nil).StoreUserDemographics), ctx, partner, resources)
}
