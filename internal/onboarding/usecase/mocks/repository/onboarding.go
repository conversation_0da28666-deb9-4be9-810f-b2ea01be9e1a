// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: OnboardingRepo)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/repository/onboarding.go --package=repository_mocks . OnboardingRepo
//

// Package repository_mocks is a generated GoMock package.
package repository_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	gomock "go.uber.org/mock/gomock"
)

// MockOnboardingRepo is a mock of OnboardingRepo interface.
type MockOnboardingRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingRepoMockRecorder
	isgomock struct{}
}

// MockOnboardingRepoMockRecorder is the mock recorder for MockOnboardingRepo.
type MockOnboardingRepoMockRecorder struct {
	mock *MockOnboardingRepo
}

// NewMockOnboardingRepo creates a new mock instance.
func NewMockOnboardingRepo(ctrl *gomock.Controller) *MockOnboardingRepo {
	mock := &MockOnboardingRepo{ctrl: ctrl}
	mock.recorder = &MockOnboardingRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingRepo) EXPECT() *MockOnboardingRepoMockRecorder {
	return m.recorder
}

// CreateUserOnboarding mocks base method.
func (m *MockOnboardingRepo) CreateUserOnboarding(ctx context.Context, onboarding *model.Onboarding) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserOnboarding", ctx, onboarding)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUserOnboarding indicates an expected call of CreateUserOnboarding.
func (mr *MockOnboardingRepoMockRecorder) CreateUserOnboarding(ctx, onboarding any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserOnboarding", reflect.TypeOf((*MockOnboardingRepo)(nil).CreateUserOnboarding), ctx, onboarding)
}

// GetActiveOnboarding mocks base method.
func (m *MockOnboardingRepo) GetActiveOnboarding(ctx context.Context, zalopayID int64, partnerCode string) (*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOnboarding", ctx, zalopayID, partnerCode)
	ret0, _ := ret[0].(*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOnboarding indicates an expected call of GetActiveOnboarding.
func (mr *MockOnboardingRepoMockRecorder) GetActiveOnboarding(ctx, zalopayID, partnerCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOnboarding", reflect.TypeOf((*MockOnboardingRepo)(nil).GetActiveOnboarding), ctx, zalopayID, partnerCode)
}

// GetOnboardingByIDAndUserID mocks base method.
func (m *MockOnboardingRepo) GetOnboardingByIDAndUserID(ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnboardingByIDAndUserID", ctx, zalopayID, onboardingID)
	ret0, _ := ret[0].(*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnboardingByIDAndUserID indicates an expected call of GetOnboardingByIDAndUserID.
func (mr *MockOnboardingRepoMockRecorder) GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnboardingByIDAndUserID", reflect.TypeOf((*MockOnboardingRepo)(nil).GetOnboardingByIDAndUserID), ctx, zalopayID, onboardingID)
}

// GetOnboardingByIDAndUserIDForUpdate mocks base method.
func (m *MockOnboardingRepo) GetOnboardingByIDAndUserIDForUpdate(ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnboardingByIDAndUserIDForUpdate", ctx, zalopayID, onboardingID)
	ret0, _ := ret[0].(*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnboardingByIDAndUserIDForUpdate indicates an expected call of GetOnboardingByIDAndUserIDForUpdate.
func (mr *MockOnboardingRepoMockRecorder) GetOnboardingByIDAndUserIDForUpdate(ctx, zalopayID, onboardingID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnboardingByIDAndUserIDForUpdate", reflect.TypeOf((*MockOnboardingRepo)(nil).GetOnboardingByIDAndUserIDForUpdate), ctx, zalopayID, onboardingID)
}

// ListActiveOnboarding mocks base method.
func (m *MockOnboardingRepo) ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListActiveOnboarding", ctx, zalopayID)
	ret0, _ := ret[0].([]*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListActiveOnboarding indicates an expected call of ListActiveOnboarding.
func (mr *MockOnboardingRepoMockRecorder) ListActiveOnboarding(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActiveOnboarding", reflect.TypeOf((*MockOnboardingRepo)(nil).ListActiveOnboarding), ctx, zalopayID)
}

// ListActiveOnboardingByZalopayIDs mocks base method.
func (m *MockOnboardingRepo) ListActiveOnboardingByZalopayIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListActiveOnboardingByZalopayIDs", ctx, zalopayIDs)
	ret0, _ := ret[0].([]*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListActiveOnboardingByZalopayIDs indicates an expected call of ListActiveOnboardingByZalopayIDs.
func (mr *MockOnboardingRepoMockRecorder) ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActiveOnboardingByZalopayIDs", reflect.TypeOf((*MockOnboardingRepo)(nil).ListActiveOnboardingByZalopayIDs), ctx, zalopayIDs)
}

// ListOnboardingByUserID mocks base method.
func (m *MockOnboardingRepo) ListOnboardingByUserID(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnboardingByUserID", ctx, zalopayID)
	ret0, _ := ret[0].([]*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnboardingByUserID indicates an expected call of ListOnboardingByUserID.
func (mr *MockOnboardingRepoMockRecorder) ListOnboardingByUserID(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnboardingByUserID", reflect.TypeOf((*MockOnboardingRepo)(nil).ListOnboardingByUserID), ctx, zalopayID)
}

// MarkOnboardingInactive mocks base method.
func (m *MockOnboardingRepo) MarkOnboardingInactive(ctx context.Context, zalopayID, onboardingID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkOnboardingInactive", ctx, zalopayID, onboardingID)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkOnboardingInactive indicates an expected call of MarkOnboardingInactive.
func (mr *MockOnboardingRepoMockRecorder) MarkOnboardingInactive(ctx, zalopayID, onboardingID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOnboardingInactive", reflect.TypeOf((*MockOnboardingRepo)(nil).MarkOnboardingInactive), ctx, zalopayID, onboardingID)
}

// UpdateAccountID mocks base method.
func (m *MockOnboardingRepo) UpdateAccountID(ctx context.Context, zalopayID, onboardingID, accountID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountID", ctx, zalopayID, onboardingID, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountID indicates an expected call of UpdateAccountID.
func (mr *MockOnboardingRepoMockRecorder) UpdateAccountID(ctx, zalopayID, onboardingID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountID", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateAccountID), ctx, zalopayID, onboardingID, accountID)
}

// UpdateAccountIDAfterBound mocks base method.
func (m *MockOnboardingRepo) UpdateAccountIDAfterBound(ctx context.Context, zalopayID, onboardingID, accountID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountIDAfterBound", ctx, zalopayID, onboardingID, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountIDAfterBound indicates an expected call of UpdateAccountIDAfterBound.
func (mr *MockOnboardingRepoMockRecorder) UpdateAccountIDAfterBound(ctx, zalopayID, onboardingID, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountIDAfterBound", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateAccountIDAfterBound), ctx, zalopayID, onboardingID, accountID)
}

// UpdateBasicOnboarding mocks base method.
func (m *MockOnboardingRepo) UpdateBasicOnboarding(ctx context.Context, onboarding *model.Onboarding) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBasicOnboarding", ctx, onboarding)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBasicOnboarding indicates an expected call of UpdateBasicOnboarding.
func (mr *MockOnboardingRepoMockRecorder) UpdateBasicOnboarding(ctx, onboarding any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBasicOnboarding", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateBasicOnboarding), ctx, onboarding)
}

// UpdateOnboardingApproval mocks base method.
func (m *MockOnboardingRepo) UpdateOnboardingApproval(ctx context.Context, zalopayID, onboardingID int64, updFunc model.UpdateOnboardingFunc) (*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOnboardingApproval", ctx, zalopayID, onboardingID, updFunc)
	ret0, _ := ret[0].(*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOnboardingApproval indicates an expected call of UpdateOnboardingApproval.
func (mr *MockOnboardingRepoMockRecorder) UpdateOnboardingApproval(ctx, zalopayID, onboardingID, updFunc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOnboardingApproval", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateOnboardingApproval), ctx, zalopayID, onboardingID, updFunc)
}

// UpdateOnboardingApprovalWithTx mocks base method.
func (m *MockOnboardingRepo) UpdateOnboardingApprovalWithTx(ctx context.Context, zalopayID, onboardingID int64, updFunc model.UpdateOnboardingFunc) (*model.Onboarding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOnboardingApprovalWithTx", ctx, zalopayID, onboardingID, updFunc)
	ret0, _ := ret[0].(*model.Onboarding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOnboardingApprovalWithTx indicates an expected call of UpdateOnboardingApprovalWithTx.
func (mr *MockOnboardingRepoMockRecorder) UpdateOnboardingApprovalWithTx(ctx, zalopayID, onboardingID, updFunc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOnboardingApprovalWithTx", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateOnboardingApprovalWithTx), ctx, zalopayID, onboardingID, updFunc)
}

// UpdateOnboardingInfo mocks base method.
func (m *MockOnboardingRepo) UpdateOnboardingInfo(ctx context.Context, onboarding *model.Onboarding) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOnboardingInfo", ctx, onboarding)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOnboardingInfo indicates an expected call of UpdateOnboardingInfo.
func (mr *MockOnboardingRepoMockRecorder) UpdateOnboardingInfo(ctx, onboarding any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOnboardingInfo", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateOnboardingInfo), ctx, onboarding)
}

// UpdateOnboardingStep mocks base method.
func (m *MockOnboardingRepo) UpdateOnboardingStep(ctx context.Context, zalopayID, onboardingID int64, step model.OnboardingStep) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOnboardingStep", ctx, zalopayID, onboardingID, step)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOnboardingStep indicates an expected call of UpdateOnboardingStep.
func (mr *MockOnboardingRepoMockRecorder) UpdateOnboardingStep(ctx, zalopayID, onboardingID, step any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOnboardingStep", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateOnboardingStep), ctx, zalopayID, onboardingID, step)
}

// UpdatePartnerData mocks base method.
func (m *MockOnboardingRepo) UpdatePartnerData(ctx context.Context, params *model.UpdatePartnerDataParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartnerData", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartnerData indicates an expected call of UpdatePartnerData.
func (mr *MockOnboardingRepoMockRecorder) UpdatePartnerData(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartnerData", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdatePartnerData), ctx, params)
}

// UpdateStepAndExtraProfile mocks base method.
func (m *MockOnboardingRepo) UpdateStepAndExtraProfile(ctx context.Context, onboarding *model.Onboarding) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStepAndExtraProfile", ctx, onboarding)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStepAndExtraProfile indicates an expected call of UpdateStepAndExtraProfile.
func (mr *MockOnboardingRepoMockRecorder) UpdateStepAndExtraProfile(ctx, onboarding any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStepAndExtraProfile", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateStepAndExtraProfile), ctx, onboarding)
}

// UpdateStepAndPartnerData mocks base method.
func (m *MockOnboardingRepo) UpdateStepAndPartnerData(ctx context.Context, onboardingID int64, step model.OnboardingStep, partnerData model.PartnerOnboardingData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStepAndPartnerData", ctx, onboardingID, step, partnerData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStepAndPartnerData indicates an expected call of UpdateStepAndPartnerData.
func (mr *MockOnboardingRepoMockRecorder) UpdateStepAndPartnerData(ctx, onboardingID, step, partnerData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStepAndPartnerData", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateStepAndPartnerData), ctx, onboardingID, step, partnerData)
}

// UpdateStepAndRejectCode mocks base method.
func (m *MockOnboardingRepo) UpdateStepAndRejectCode(ctx context.Context, onboardingID int64, step model.OnboardingStep, rejectCode string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStepAndRejectCode", ctx, onboardingID, step, rejectCode)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStepAndRejectCode indicates an expected call of UpdateStepAndRejectCode.
func (mr *MockOnboardingRepoMockRecorder) UpdateStepAndRejectCode(ctx, onboardingID, step, rejectCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStepAndRejectCode", reflect.TypeOf((*MockOnboardingRepo)(nil).UpdateStepAndRejectCode), ctx, onboardingID, step, rejectCode)
}
