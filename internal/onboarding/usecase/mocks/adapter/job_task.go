// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: JobTaskMgmt)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/job_task.go --package=adapter_mocks . JobTaskMgmt
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockJobTaskMgmt is a mock of JobTaskMgmt interface.
type MockJobTaskMgmt struct {
	ctrl     *gomock.Controller
	recorder *MockJobTaskMgmtMockRecorder
	isgomock struct{}
}

// MockJobTaskMgmtMockRecorder is the mock recorder for MockJobTaskMgmt.
type MockJobTaskMgmtMockRecorder struct {
	mock *MockJobTaskMgmt
}

// NewMockJobTaskMgmt creates a new mock instance.
func NewMockJobTaskMgmt(ctrl *gomock.Controller) *MockJobTaskMgmt {
	mock := &MockJobTaskMgmt{ctrl: ctrl}
	mock.recorder = &MockJobTaskMgmtMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJobTaskMgmt) EXPECT() *MockJobTaskMgmtMockRecorder {
	return m.recorder
}

// CanTriggerQueryOnboardingStatusTask mocks base method.
func (m *MockJobTaskMgmt) CanTriggerQueryOnboardingStatusTask(ctx context.Context, params *model.OnboardingStatusTask) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CanTriggerQueryOnboardingStatusTask", ctx, params)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CanTriggerQueryOnboardingStatusTask indicates an expected call of CanTriggerQueryOnboardingStatusTask.
func (mr *MockJobTaskMgmtMockRecorder) CanTriggerQueryOnboardingStatusTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanTriggerQueryOnboardingStatusTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).CanTriggerQueryOnboardingStatusTask), ctx, params)
}

// RegisterContractSigningTask mocks base method.
func (m *MockJobTaskMgmt) RegisterContractSigningTask(ctx context.Context, params *dto.ContractSingingTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterContractSigningTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// RegisterContractSigningTask indicates an expected call of RegisterContractSigningTask.
func (mr *MockJobTaskMgmtMockRecorder) RegisterContractSigningTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterContractSigningTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).RegisterContractSigningTask), ctx, params)
}

// RegisterQueryOnboardingStatusTask mocks base method.
func (m *MockJobTaskMgmt) RegisterQueryOnboardingStatusTask(ctx context.Context, params *model.OnboardingStatusTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterQueryOnboardingStatusTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// RegisterQueryOnboardingStatusTask indicates an expected call of RegisterQueryOnboardingStatusTask.
func (mr *MockJobTaskMgmtMockRecorder) RegisterQueryOnboardingStatusTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterQueryOnboardingStatusTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).RegisterQueryOnboardingStatusTask), ctx, params)
}

// RegisterSubmitFaceImageTask mocks base method.
func (m *MockJobTaskMgmt) RegisterSubmitFaceImageTask(ctx context.Context, params *dto.FaceImgSubmitTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterSubmitFaceImageTask", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// RegisterSubmitFaceImageTask indicates an expected call of RegisterSubmitFaceImageTask.
func (mr *MockJobTaskMgmtMockRecorder) RegisterSubmitFaceImageTask(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterSubmitFaceImageTask", reflect.TypeOf((*MockJobTaskMgmt)(nil).RegisterSubmitFaceImageTask), ctx, params)
}
