// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: UserChallengeService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/user_challenge_service.go --package=adapter_mocks . UserChallengeService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	gomock "go.uber.org/mock/gomock"
)

// MockUserChallengeService is a mock of UserChallengeService interface.
type MockUserChallengeService struct {
	ctrl     *gomock.Controller
	recorder *MockUserChallengeServiceMockRecorder
	isgomock struct{}
}

// MockUserChallengeServiceMockRecorder is the mock recorder for MockUserChallengeService.
type MockUserChallengeServiceMockRecorder struct {
	mock *MockUserChallengeService
}

// NewMockUserChallengeService creates a new mock instance.
func NewMockUserChallengeService(ctrl *gomock.Controller) *MockUserChallengeService {
	mock := &MockUserChallengeService{ctrl: ctrl}
	mock.recorder = &MockUserChallengeServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserChallengeService) EXPECT() *MockUserChallengeServiceMockRecorder {
	return m.recorder
}

// GetFaceChallengeData mocks base method.
func (m *MockUserChallengeService) GetFaceChallengeData(ctx context.Context, zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFaceChallengeData", ctx, zalopayID, faceChallengeID)
	ret0, _ := ret[0].(*model.UserFaceChallenge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceChallengeData indicates an expected call of GetFaceChallengeData.
func (mr *MockUserChallengeServiceMockRecorder) GetFaceChallengeData(ctx, zalopayID, faceChallengeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceChallengeData", reflect.TypeOf((*MockUserChallengeService)(nil).GetFaceChallengeData), ctx, zalopayID, faceChallengeID)
}

// GetFaceChallengeStatus mocks base method.
func (m *MockUserChallengeService) GetFaceChallengeStatus(ctx context.Context, zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFaceChallengeStatus", ctx, zalopayID, faceChallengeID)
	ret0, _ := ret[0].(*model.UserFaceChallenge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceChallengeStatus indicates an expected call of GetFaceChallengeStatus.
func (mr *MockUserChallengeServiceMockRecorder) GetFaceChallengeStatus(ctx, zalopayID, faceChallengeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceChallengeStatus", reflect.TypeOf((*MockUserChallengeService)(nil).GetFaceChallengeStatus), ctx, zalopayID, faceChallengeID)
}
