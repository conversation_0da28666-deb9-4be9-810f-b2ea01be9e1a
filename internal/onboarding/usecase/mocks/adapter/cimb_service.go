// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: CIMBService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/cimb_service.go --package=adapter_mocks . CIMBService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	cimb "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockCIMBService is a mock of CIMBService interface.
type MockCIMBService struct {
	ctrl     *gomock.Controller
	recorder *MockCIMBServiceMockRecorder
	isgomock struct{}
}

// MockCIMBServiceMockRecorder is the mock recorder for MockCIMBService.
type MockCIMBServiceMockRecorder struct {
	mock *MockCIMBService
}

// NewMockCIMBService creates a new mock instance.
func NewMockCIMBService(ctrl *gomock.Controller) *MockCIMBService {
	mock := &MockCIMBService{ctrl: ctrl}
	mock.recorder = &MockCIMBServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCIMBService) EXPECT() *MockCIMBServiceMockRecorder {
	return m.recorder
}

// CheckOnboardingPermission mocks base method.
func (m *MockCIMBService) CheckOnboardingPermission(ctx context.Context, params *dto.OnboardingPermissionParams) (*model.OnboardingPermission, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOnboardingPermission", ctx, params)
	ret0, _ := ret[0].(*model.OnboardingPermission)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOnboardingPermission indicates an expected call of CheckOnboardingPermission.
func (mr *MockCIMBServiceMockRecorder) CheckOnboardingPermission(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOnboardingPermission", reflect.TypeOf((*MockCIMBService)(nil).CheckOnboardingPermission), ctx, params)
}

// FetchResources mocks base method.
func (m *MockCIMBService) FetchResources(ctx context.Context, resourceTypes []string) ([]model.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchResources", ctx, resourceTypes)
	ret0, _ := ret[0].([]model.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchResources indicates an expected call of FetchResources.
func (mr *MockCIMBServiceMockRecorder) FetchResources(ctx, resourceTypes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchResources", reflect.TypeOf((*MockCIMBService)(nil).FetchResources), ctx, resourceTypes)
}

// GetContractData mocks base method.
func (m *MockCIMBService) GetContractData(ctx context.Context, req *dto.GetContractParams) (*model.ContractData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractData", ctx, req)
	ret0, _ := ret[0].(*model.ContractData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractData indicates an expected call of GetContractData.
func (mr *MockCIMBServiceMockRecorder) GetContractData(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractData", reflect.TypeOf((*MockCIMBService)(nil).GetContractData), ctx, req)
}

// InitContractProcess mocks base method.
func (m *MockCIMBService) InitContractProcess(ctx context.Context, zalopayID int64, partnerReqID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitContractProcess", ctx, zalopayID, partnerReqID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitContractProcess indicates an expected call of InitContractProcess.
func (mr *MockCIMBServiceMockRecorder) InitContractProcess(ctx, zalopayID, partnerReqID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitContractProcess", reflect.TypeOf((*MockCIMBService)(nil).InitContractProcess), ctx, zalopayID, partnerReqID)
}

// MarkContractComplete mocks base method.
func (m *MockCIMBService) MarkContractComplete(ctx context.Context, zalopayID int64, faceReqID, partnerReqID, partnerTransID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkContractComplete", ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkContractComplete indicates an expected call of MarkContractComplete.
func (mr *MockCIMBServiceMockRecorder) MarkContractComplete(ctx, zalopayID, faceReqID, partnerReqID, partnerTransID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkContractComplete", reflect.TypeOf((*MockCIMBService)(nil).MarkContractComplete), ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)
}

// QueryOnboardingStatus mocks base method.
func (m *MockCIMBService) QueryOnboardingStatus(ctx context.Context, zalopayID, onboardingID int64) (*cimb.OnboardingStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryOnboardingStatus", ctx, zalopayID, onboardingID)
	ret0, _ := ret[0].(*cimb.OnboardingStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOnboardingStatus indicates an expected call of QueryOnboardingStatus.
func (mr *MockCIMBServiceMockRecorder) QueryOnboardingStatus(ctx, zalopayID, onboardingID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOnboardingStatus", reflect.TypeOf((*MockCIMBService)(nil).QueryOnboardingStatus), ctx, zalopayID, onboardingID)
}

// RequestLinkingAccount mocks base method.
func (m *MockCIMBService) RequestLinkingAccount(ctx context.Context, onboarding *model.Onboarding) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLinkingAccount", ctx, onboarding)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLinkingAccount indicates an expected call of RequestLinkingAccount.
func (mr *MockCIMBServiceMockRecorder) RequestLinkingAccount(ctx, onboarding any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLinkingAccount", reflect.TypeOf((*MockCIMBService)(nil).RequestLinkingAccount), ctx, onboarding)
}

// RequestOTP mocks base method.
func (m *MockCIMBService) RequestOTP(ctx context.Context, req *dto.RequestOTPParams) (*dto.RequestOTPResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestOTP", ctx, req)
	ret0, _ := ret[0].(*dto.RequestOTPResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestOTP indicates an expected call of RequestOTP.
func (mr *MockCIMBServiceMockRecorder) RequestOTP(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestOTP", reflect.TypeOf((*MockCIMBService)(nil).RequestOTP), ctx, req)
}

// RequestSelfieUploadUrl mocks base method.
func (m *MockCIMBService) RequestSelfieUploadUrl(ctx context.Context, req *dto.SelfieUploadUrlParams) (string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestSelfieUploadUrl", ctx, req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RequestSelfieUploadUrl indicates an expected call of RequestSelfieUploadUrl.
func (mr *MockCIMBServiceMockRecorder) RequestSelfieUploadUrl(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestSelfieUploadUrl", reflect.TypeOf((*MockCIMBService)(nil).RequestSelfieUploadUrl), ctx, req)
}

// SubmitCasaOnboarding mocks base method.
func (m *MockCIMBService) SubmitCasaOnboarding(ctx context.Context, onboarding *model.Onboarding, isResubmitted bool) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitCasaOnboarding", ctx, onboarding, isResubmitted)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCasaOnboarding indicates an expected call of SubmitCasaOnboarding.
func (mr *MockCIMBServiceMockRecorder) SubmitCasaOnboarding(ctx, onboarding, isResubmitted any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCasaOnboarding", reflect.TypeOf((*MockCIMBService)(nil).SubmitCasaOnboarding), ctx, onboarding, isResubmitted)
}

// UploadSelfieImage mocks base method.
func (m *MockCIMBService) UploadSelfieImage(ctx context.Context, req *dto.SelfieUploadParams) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadSelfieImage", ctx, req)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadSelfieImage indicates an expected call of UploadSelfieImage.
func (mr *MockCIMBServiceMockRecorder) UploadSelfieImage(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadSelfieImage", reflect.TypeOf((*MockCIMBService)(nil).UploadSelfieImage), ctx, req)
}

// UploadSelfieImageByURL mocks base method.
func (m *MockCIMBService) UploadSelfieImageByURL(ctx context.Context, uploadUrl string, imageData *model.Image) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadSelfieImageByURL", ctx, uploadUrl, imageData)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadSelfieImageByURL indicates an expected call of UploadSelfieImageByURL.
func (mr *MockCIMBServiceMockRecorder) UploadSelfieImageByURL(ctx, uploadUrl, imageData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadSelfieImageByURL", reflect.TypeOf((*MockCIMBService)(nil).UploadSelfieImageByURL), ctx, uploadUrl, imageData)
}

// VerifyOTP mocks base method.
func (m *MockCIMBService) VerifyOTP(ctx context.Context, req *dto.VerifyOTPParams) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyOTP", ctx, req)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOTP indicates an expected call of VerifyOTP.
func (mr *MockCIMBServiceMockRecorder) VerifyOTP(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOTP", reflect.TypeOf((*MockCIMBService)(nil).VerifyOTP), ctx, req)
}
