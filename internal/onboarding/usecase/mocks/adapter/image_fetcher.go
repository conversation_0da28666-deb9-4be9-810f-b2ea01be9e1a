// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: ImageFetcher)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/image_fetcher.go --package=adapter_mocks . ImageFetcher
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	gomock "go.uber.org/mock/gomock"
)

// MockImageFetcher is a mock of ImageFetcher interface.
type MockImageFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockImageFetcherMockRecorder
	isgomock struct{}
}

// MockImageFetcherMockRecorder is the mock recorder for MockImageFetcher.
type MockImageFetcherMockRecorder struct {
	mock *MockImageFetcher
}

// NewMockImageFetcher creates a new mock instance.
func NewMockImageFetcher(ctrl *gomock.Controller) *MockImageFetcher {
	mock := &MockImageFetcher{ctrl: ctrl}
	mock.recorder = &MockImageFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockImageFetcher) EXPECT() *MockImageFetcherMockRecorder {
	return m.recorder
}

// GetKycImageData mocks base method.
func (m *MockImageFetcher) GetKycImageData(ctx context.Context, url string) (*model.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKycImageData", ctx, url)
	ret0, _ := ret[0].(*model.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycImageData indicates an expected call of GetKycImageData.
func (mr *MockImageFetcherMockRecorder) GetKycImageData(ctx, url any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycImageData", reflect.TypeOf((*MockImageFetcher)(nil).GetKycImageData), ctx, url)
}
