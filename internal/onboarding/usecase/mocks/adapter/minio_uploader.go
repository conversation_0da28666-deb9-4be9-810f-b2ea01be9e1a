// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: MinioUploader)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/minio_uploader.go --package=adapter_mocks . MinioUploader
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockMinioUploader is a mock of MinioUploader interface.
type MockMinioUploader struct {
	ctrl     *gomock.Controller
	recorder *MockMinioUploaderMockRecorder
	isgomock struct{}
}

// MockMinioUploaderMockRecorder is the mock recorder for MockMinioUploader.
type MockMinioUploaderMockRecorder struct {
	mock *MockMinioUploader
}

// NewMockMinioUploader creates a new mock instance.
func NewMockMinioUploader(ctrl *gomock.Controller) *MockMinioUploader {
	mock := &MockMinioUploader{ctrl: ctrl}
	mock.recorder = &MockMinioUploaderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMinioUploader) EXPECT() *MockMinioUploaderMockRecorder {
	return m.recorder
}

// FetchAndUploadFaceImage mocks base method.
func (m *MockMinioUploader) FetchAndUploadFaceImage(ctx context.Context, params *dto.FaceImgStoreParams) (*model.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAndUploadFaceImage", ctx, params)
	ret0, _ := ret[0].(*model.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAndUploadFaceImage indicates an expected call of FetchAndUploadFaceImage.
func (mr *MockMinioUploaderMockRecorder) FetchAndUploadFaceImage(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAndUploadFaceImage", reflect.TypeOf((*MockMinioUploader)(nil).FetchAndUploadFaceImage), ctx, params)
}

// FetchAndUploadKYCImages mocks base method.
func (m *MockMinioUploader) FetchAndUploadKYCImages(ctx context.Context, params *dto.UserKycImageStoreParams) (*model.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAndUploadKYCImages", ctx, params)
	ret0, _ := ret[0].(*model.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAndUploadKYCImages indicates an expected call of FetchAndUploadKYCImages.
func (mr *MockMinioUploaderMockRecorder) FetchAndUploadKYCImages(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAndUploadKYCImages", reflect.TypeOf((*MockMinioUploader)(nil).FetchAndUploadKYCImages), ctx, params)
}

// GetFaceImageUrl mocks base method.
func (m *MockMinioUploader) GetFaceImageUrl(ctx context.Context, zalopayID int64, imagePath string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFaceImageUrl", ctx, zalopayID, imagePath)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFaceImageUrl indicates an expected call of GetFaceImageUrl.
func (mr *MockMinioUploaderMockRecorder) GetFaceImageUrl(ctx, zalopayID, imagePath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFaceImageUrl", reflect.TypeOf((*MockMinioUploader)(nil).GetFaceImageUrl), ctx, zalopayID, imagePath)
}

// UploadSignatureImage mocks base method.
func (m *MockMinioUploader) UploadSignatureImage(ctx context.Context, params *dto.WetSignUploadParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadSignatureImage", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UploadSignatureImage indicates an expected call of UploadSignatureImage.
func (mr *MockMinioUploaderMockRecorder) UploadSignatureImage(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadSignatureImage", reflect.TypeOf((*MockMinioUploader)(nil).UploadSignatureImage), ctx, params)
}
