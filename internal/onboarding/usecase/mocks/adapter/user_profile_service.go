// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: UserProfileService)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/user_profile_service.go --package=adapter_mocks . UserProfileService
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	model "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	dto "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockUserProfileService is a mock of UserProfileService interface.
type MockUserProfileService struct {
	ctrl     *gomock.Controller
	recorder *MockUserProfileServiceMockRecorder
	isgomock struct{}
}

// MockUserProfileServiceMockRecorder is the mock recorder for MockUserProfileService.
type MockUserProfileServiceMockRecorder struct {
	mock *MockUserProfileService
}

// NewMockUserProfileService creates a new mock instance.
func NewMockUserProfileService(ctrl *gomock.Controller) *MockUserProfileService {
	mock := &MockUserProfileService{ctrl: ctrl}
	mock.recorder = &MockUserProfileServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserProfileService) EXPECT() *MockUserProfileServiceMockRecorder {
	return m.recorder
}

// GetKycNfcData mocks base method.
func (m *MockUserProfileService) GetKycNfcData(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKycNfcData", ctx, zalopayID)
	ret0, _ := ret[0].(*model.UserKycNfc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycNfcData indicates an expected call of GetKycNfcData.
func (mr *MockUserProfileServiceMockRecorder) GetKycNfcData(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycNfcData", reflect.TypeOf((*MockUserProfileService)(nil).GetKycNfcData), ctx, zalopayID)
}

// GetKycNfcStatus mocks base method.
func (m *MockUserProfileService) GetKycNfcStatus(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKycNfcStatus", ctx, zalopayID)
	ret0, _ := ret[0].(*model.UserKycNfc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycNfcStatus indicates an expected call of GetKycNfcStatus.
func (mr *MockUserProfileServiceMockRecorder) GetKycNfcStatus(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycNfcStatus", reflect.TypeOf((*MockUserProfileService)(nil).GetKycNfcStatus), ctx, zalopayID)
}

// GetKycProgStatus mocks base method.
func (m *MockUserProfileService) GetKycProgStatus(ctx context.Context, zalopayID int64) (*model.UserKycProgress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKycProgStatus", ctx, zalopayID)
	ret0, _ := ret[0].(*model.UserKycProgress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKycProgStatus indicates an expected call of GetKycProgStatus.
func (mr *MockUserProfileServiceMockRecorder) GetKycProgStatus(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKycProgStatus", reflect.TypeOf((*MockUserProfileService)(nil).GetKycProgStatus), ctx, zalopayID)
}

// GetProfile mocks base method.
func (m *MockUserProfileService) GetProfile(ctx context.Context, request dto.ZPUserProfileParams) (*model.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfile", ctx, request)
	ret0, _ := ret[0].(*model.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProfile indicates an expected call of GetProfile.
func (mr *MockUserProfileServiceMockRecorder) GetProfile(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfile", reflect.TypeOf((*MockUserProfileService)(nil).GetProfile), ctx, request)
}

// ResetKycNfcData mocks base method.
func (m *MockUserProfileService) ResetKycNfcData(ctx context.Context, zalopayID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetKycNfcData", ctx, zalopayID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetKycNfcData indicates an expected call of ResetKycNfcData.
func (mr *MockUserProfileServiceMockRecorder) ResetKycNfcData(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetKycNfcData", reflect.TypeOf((*MockUserProfileService)(nil).ResetKycNfcData), ctx, zalopayID)
}

// VerifyKycNfcBCA mocks base method.
func (m *MockUserProfileService) VerifyKycNfcBCA(ctx context.Context, zalopayID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyKycNfcBCA", ctx, zalopayID)
	ret0, _ := ret[0].(error)
	return ret0
}

// VerifyKycNfcBCA indicates an expected call of VerifyKycNfcBCA.
func (mr *MockUserProfileServiceMockRecorder) VerifyKycNfcBCA(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyKycNfcBCA", reflect.TypeOf((*MockUserProfileService)(nil).VerifyKycNfcBCA), ctx, zalopayID)
}
