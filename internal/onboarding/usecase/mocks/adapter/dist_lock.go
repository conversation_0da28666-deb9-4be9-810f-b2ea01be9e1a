// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: DistributedLock)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/dist_lock.go --package=adapter_mocks . DistributedLock
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockDistributedLock is a mock of DistributedLock interface.
type MockDistributedLock struct {
	ctrl     *gomock.Controller
	recorder *MockDistributedLockMockRecorder
	isgomock struct{}
}

// MockDistributedLockMockRecorder is the mock recorder for MockDistributedLock.
type MockDistributedLockMockRecorder struct {
	mock *MockDistributedLock
}

// NewMockDistributedLock creates a new mock instance.
func NewMockDistributedLock(ctrl *gomock.Controller) *MockDistributedLock {
	mock := &MockDistributedLock{ctrl: ctrl}
	mock.recorder = &MockDistributedLockMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributedLock) EXPECT() *MockDistributedLockMockRecorder {
	return m.recorder
}

// Acquire mocks base method.
func (m *MockDistributedLock) Acquire(ctx context.Context, resource string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Acquire", ctx, resource, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// Acquire indicates an expected call of Acquire.
func (mr *MockDistributedLockMockRecorder) Acquire(ctx, resource, ttl any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Acquire", reflect.TypeOf((*MockDistributedLock)(nil).Acquire), ctx, resource, ttl)
}

// Release mocks base method.
func (m *MockDistributedLock) Release(ctx context.Context, resource string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", ctx, resource)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockDistributedLockMockRecorder) Release(ctx, resource any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockDistributedLock)(nil).Release), ctx, resource)
}
