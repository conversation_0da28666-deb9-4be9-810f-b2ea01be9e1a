// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface (interfaces: WhitelistSys)
//
// Generated by this command:
//
//	mockgen --destination=../mocks/adapter/whitelist_service.go --package=adapter_mocks . WhitelistSys
//

// Package adapter_mocks is a generated GoMock package.
package adapter_mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockWhitelistSys is a mock of WhitelistSys interface.
type MockWhitelistSys struct {
	ctrl     *gomock.Controller
	recorder *MockWhitelistSysMockRecorder
	isgomock struct{}
}

// MockWhitelistSysMockRecorder is the mock recorder for MockWhitelistSys.
type MockWhitelistSysMockRecorder struct {
	mock *MockWhitelistSys
}

// NewMockWhitelistSys creates a new mock instance.
func NewMockWhitelistSys(ctrl *gomock.Controller) *MockWhitelistSys {
	mock := &MockWhitelistSys{ctrl: ctrl}
	mock.recorder = &MockWhitelistSysMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhitelistSys) EXPECT() *MockWhitelistSysMockRecorder {
	return m.recorder
}

// IsOnboardWhitelisted mocks base method.
func (m *MockWhitelistSys) IsOnboardWhitelisted(ctx context.Context, zalopayID int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsOnboardWhitelisted", ctx, zalopayID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsOnboardWhitelisted indicates an expected call of IsOnboardWhitelisted.
func (mr *MockWhitelistSysMockRecorder) IsOnboardWhitelisted(ctx, zalopayID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsOnboardWhitelisted", reflect.TypeOf((*MockWhitelistSys)(nil).IsOnboardWhitelisted), ctx, zalopayID)
}
