package usecase

import (
	"context"

	"github.com/stretchr/testify/assert"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingRejection_Success() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepRejected,
		RejectCode:  "CASA:REJECTED",
		UserInfo: &cimb.UserOnboardingInfo{
			UserProfile: model.UserProfile{
				Gender: 1,
			},
		},
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:    cimb.ODInRejected,
			CasaStatus:  cimb.CASARejected,
			ErrorDetail: "R01",
		},
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)

	// Act
	result, err := s.usecase.GetOnboardingRejection(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(partner.PartnerCIMB, result.PartnerCode)
	s.Equal("CASA:REJECTED:R01", result.RejectCodeFmt)
	s.Equal("R01", result.RejectCodeRaw)
	s.False(result.CanResubmit)
	s.False(result.NeedUpdKyc)
	s.Equal(model.MinMaleAge, result.MinAgeAllowed)
	s.Equal(model.MaxMaleAge, result.MaxAgeAllowed)
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingRejection_NotFound() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, model.ErrOnboardingNotFound)

	// Act
	result, err := s.usecase.GetOnboardingRejection(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound))
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingRejection_RepositoryError() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(nil, assert.AnError)

	// Act
	result, err := s.usecase.GetOnboardingRejection(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeRepositoryError))
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingRejection_NotRejected() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)

	// Act
	result, err := s.usecase.GetOnboardingRejection(ctx, zalopayID, onboardingID)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.True(errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotRejectedYet))
}

func (s *OnboardingUsecaseTestSuite) TestGetOnboardingRejection_FemaleGender() {
	// Arrange
	ctx := context.Background()
	zalopayID := int64(123)
	onboardingID := int64(456)

	onboarding := &model.Onboarding{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		PartnerCode: partner.PartnerCIMB.String(),
		Status:      model.OnboardingStatusActive,
		CurrentStep: model.OnboardingStepRejected,
		RejectCode:  "CASA:REJECTED",
		UserInfo: &cimb.UserOnboardingInfo{
			UserProfile: model.UserProfile{
				Gender: 2,
			},
		},
		PartnerData: &cimb.PartnerOnboardingData{
			ODStatus:    cimb.ODInRejected,
			CasaStatus:  cimb.CASARejected,
			ErrorDetail: "R01",
		},
	}

	s.onboardRepo.EXPECT().GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID).Return(onboarding, nil)

	// Act
	result, err := s.usecase.GetOnboardingRejection(ctx, zalopayID, onboardingID)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.MinFemaleAge, result.MinAgeAllowed)
	s.Equal(model.MaxFemaleAge, result.MaxAgeAllowed)
}
