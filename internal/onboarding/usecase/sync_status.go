package usecase

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/retry"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (uc *OnboardingUsecase) TriggerQueryOnboardingStatusJob(ctx context.Context, zalopayID, onboardingID int64) error {
	logger := uc.logger.WithContext(ctx)

	logger.Infow(
		"msg", "Start TriggerSyncOnboardingStatusJob",
		"zalopay<PERSON>", zalopay<PERSON>, "onboardingID", onboardingID,
	)

	// Pre-check can trigger a job
	allowed, err := uc.jobTaskMgmt.CanTriggerQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
		ZalopayID:    cast.ToString(zalopayID),
		OnboardingID: cast.ToString(onboardingID),
	})
	if err != nil {
		logger.Errorf("CanTriggerQueryOnboardingStatusTask failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "check condition re-run query onboarding status task failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}
	if !allowed {
		logger.Info("Job is current running or completed, skip trigger")
		return nil
	}

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserID failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if userOb.IsCompleted() {
		logger.Warn("Onboarding is already completed, step=%v", userOb.CurrentStep)
		return nil
	}

	// Register a job
	err = uc.jobTaskMgmt.RegisterQueryOnboardingStatusTask(ctx, &model.OnboardingStatusTask{
		ZalopayID:    cast.ToString(zalopayID),
		OnboardingID: cast.ToString(onboardingID),
		PartnerReqID: userOb.PartnerData.GetPartnerRequestID(),
		CurrentStep:  userOb.CurrentStep.String(),
	})
	if err != nil {
		logger.Errorf("RegisterQueryOnboardingStatusTask failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeInternalError, "register query onboarding status task failed").
			WithCause(err).WithKind(errorkit.TypeUnexpected)
	}

	logger.Infow(
		"msg", "TriggerSyncOnboardingStatusJob success",
		"zalopayID", zalopayID, "onboardingID", onboardingID,
	)

	return nil
}

func (uc *OnboardingUsecase) SyncOnboardingStatus(ctx context.Context,
	zalopayID int64, onboardingID int64) (*model.Onboarding, error) {
	logger := uc.logger.WithContext(ctx)

	userOb, err := uc.onboardRepo.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserID failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "get onboarding from db failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	if !userOb.PartnerData.CanQueryApprovalStatus() {
		logger.Warn("Current info not eligible to query approval info, step=%v", userOb.CurrentStep)
		return nil, errorkit.
			NewError(errorkit.CodeOnboardingStepInvalidForExec, "onboarding step is invalid for execution").
			WithKind(errorkit.TypePrecondition)
	}

	switch partner.PartnerCode(userOb.PartnerCode) {
	case partner.PartnerCIMB:
		userOb, err = uc.syncCIMBOnboardingApproval(ctx, userOb)
	default:
		logger.Errorf("partner not supported: %v", userOb.PartnerCode)
		return nil, errorkit.
			NewError(errorkit.CodePartnerNotSupported, "partner not supported").
			WithKind(errorkit.TypeInvalidArg)
	}

	if err != nil {
		logger.Errorf("sync onboarding approval failed: %v", err)
		return nil, err
	}

	return userOb, nil
}

func (uc *OnboardingUsecase) syncCIMBOnboardingApproval(
	ctx context.Context, userOb *model.Onboarding) (*model.Onboarding, error) {
	logger := uc.logger.WithContext(ctx)

	cimbObs, err := uc.cimbService.QueryOnboardingStatus(ctx, userOb.ZalopayID, userOb.ID)
	if err != nil {
		logger.Errorf("QueryOnboardingStatus failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeCallCIMBFailed, "query onboarding status from partner failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	logger.Infof("current cimb onboarding status: %v", cimbObs)

	tCtx, err := uc.txn.BeginTx(ctx)
	if err != nil {
		logger.Errorf("BeginTx failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "begin transaction failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}
	defer uc.txn.RollbackTx(tCtx)

	userOb, err = uc.handleCIMBApprovalStatus(tCtx, userOb, cimbObs)
	if err != nil {
		logger.Errorf("handleCIMBApprovalStatus failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "handle cimb approval status failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infof("sync onboarding approval success")

	if err = uc.processOnboardingAfterSynced(tCtx, userOb); err != nil {
		logger.Errorf("processOnboardingAfterSynced failed: %v", err)
		return nil, err
	}

	if err = uc.txn.CommitTx(tCtx); err != nil {
		logger.Errorf("CommitTx failed: %v", err)
		return nil, errorkit.
			NewError(errorkit.CodeRepositoryError, "commit transaction failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	return userOb, nil
}

func (uc *OnboardingUsecase) handleCIMBApprovalStatus(ctx context.Context,
	userOb *model.Onboarding, cimbObs *cimb.OnboardingStatus) (*model.Onboarding, error) {
	logger := uc.logger.WithContext(ctx)

	updateFunc := func(ctx context.Context, currentOb *model.Onboarding) (*model.Onboarding, error) {
		if currentOb.IsCompleted() {
			logger.Warn("Onboarding is already completed, step=%v", currentOb.CurrentStep)
			return currentOb, nil
		}

		partnerOb, valid := cimb.AsPartnerOnboardingData(currentOb.PartnerData)
		if !valid {
			logger.Errorf("AsPartnerOnboardingData failed")
			return nil, errors.New("data type conversion failed")
		}

		partnerOb.SetApprovalResult(cimbObs)
		currentOb.SyncPartnerApproval(partnerOb)

		logger.Infof("cimb partner data after sync onboarding approval: %v", partnerOb)

		return currentOb, nil
	}

	userOb, err := uc.onboardRepo.UpdateOnboardingApproval(ctx, userOb.ZalopayID, userOb.ID, updateFunc)
	if err != nil {
		return nil, errors.Wrap(err, "update onboarding approval failed")
	}
	return userOb, nil
}

func (uc *OnboardingUsecase) processOnboardingAfterSynced(ctx context.Context, userOb *model.Onboarding) error {
	logger := uc.logger.WithContext(ctx)

	if !userOb.IsApproved() {
		return nil
	}

	var accountID int64

	const (
		maxRetry  = 3
		delayTime = time.Second
	)

	// Do not worry about a retry call creation account multiple times, because CreateAccount API support idempotent
	err := retry.NewRetry(maxRetry, delayTime, nil).Execute(ctx, func() error {
		accID, cErr := uc.accountService.CreateAccount(ctx, &dto.CreateAccountParams{
			ZalopayID:    userOb.ZalopayID,
			OnboardingID: userOb.ID,
			PartnerCode:  partner.CodeFromString(userOb.PartnerCode),
		})
		if cErr != nil {
			logger.Errorf("CreateAccount failed: %v", cErr)
			return cErr
		}
		accountID = accID
		return nil
	})
	if err != nil {
		logger.Errorf("CreateAccount failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeCallAccountFailed, "create account failed").
			WithCause(err).WithKind(errorkit.TypeRemoteCall)
	}

	if err = uc.onboardRepo.UpdateAccountID(ctx, userOb.ZalopayID, userOb.ID, accountID); err != nil {
		logger.Errorf("UpdateAccountIDAfterBound failed: %v", err)
		return errorkit.
			NewError(errorkit.CodeRepositoryError, "update account id after bound failed").
			WithCause(err).WithKind(errorkit.TypeRepository)
	}

	logger.Infof("CreateAccount success, accountID: %v", accountID)

	return nil
}
