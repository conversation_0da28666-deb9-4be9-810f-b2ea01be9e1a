package validator

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

func TestResourceValidator_Validate(t *testing.T) {
	tests := []struct {
		name          string
		resources     []model.Resource
		resourceCheck []model.ResourceCheck
		wantErr       bool
		expectedErr   string
	}{
		{
			name: "valid resources",
			resources: []model.Resource{
				{
					Type: "type1",
					Data: []model.ResourceData{
						{Code: "code1"},
						{Code: "code2"},
					},
				},
				{
					Type: "type2",
					Data: []model.ResourceData{
						{Code: "code3"},
					},
				},
			},
			resourceCheck: []model.ResourceCheck{
				{Type: "type1", Code: "code1"},
				{Type: "type2", Code: "code3"},
			},
			wantErr: false,
		},
		{
			name: "invalid resource type",
			resources: []model.Resource{
				{
					Type: "type1",
					Data: []model.ResourceData{
						{Code: "code1"},
					},
				},
			},
			resourceCheck: []model.ResourceCheck{
				{Type: "type2", Code: "code1"},
			},
			wantErr:     true,
			expectedErr: "resource invalid, type: type2",
		},
		{
			name: "invalid resource code",
			resources: []model.Resource{
				{
					Type: "type1",
					Data: []model.ResourceData{
						{Code: "code1"},
					},
				},
			},
			resourceCheck: []model.ResourceCheck{
				{Type: "type1", Code: "code2"},
			},
			wantErr:     true,
			expectedErr: "resource invalid, type: type1",
		},
		{
			name:      "empty resources",
			resources: []model.Resource{},
			resourceCheck: []model.ResourceCheck{
				{Type: "type1", Code: "code1"},
			},
			wantErr:     true,
			expectedErr: "resource invalid, type: type1",
		},
		{
			name: "empty resource check",
			resources: []model.Resource{
				{
					Type: "type1",
					Data: []model.ResourceData{
						{Code: "code1"},
					},
				},
			},
			resourceCheck: []model.ResourceCheck{},
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := ResourceValidator{
				Resources: tt.resources,
			}

			err := validator.Validate(tt.resourceCheck)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
