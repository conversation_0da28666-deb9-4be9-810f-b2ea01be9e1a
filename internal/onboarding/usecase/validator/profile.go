package validator

import (
	"slices"
	"time"
	"unicode"

	"gitlab.zalopay.vn/fin/platform/common/utils"
	"golang.org/x/text/runes"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type ProfileValidator struct {
}

func NewProfileValidator() *ProfileValidator {
	return &ProfileValidator{}
}

type KycProfileValidationRule func(profile *model.UserProfile) model.ValidateResult

func (kv ProfileValidator) ValidateProfile(profile *model.UserProfile,
	rules []KycProfileValidationRule) []model.ValidateResult {
	if len(rules) == 0 {
		return []model.ValidateResult{}
	}

	validationResult := make([]model.ValidateResult, 0, len(rules))
	for _, rule := range rules {
		result := rule(profile)
		validationResult = append(validationResult, result)
	}
	return validationResult
}

func (kv ProfileValidator) HasViolation(results []*model.ValidateResult, code model.ViolationCode) bool {
	for _, result := range results {
		if result.Code == code {
			return true
		}
	}
	return false

}

func RuleKycProfileAge() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		userGender := zutils.GenderConvertToString(profile.Gender)
		userDob, err := time.Parse(utils.LayoutDashYYYYMMDD, profile.Birthday)
		if err != nil {
			result.Code = model.ViolationAgeInvalid
			result.Status = model.ValidateResultUnknown
			return result
		}
		userAge := utils.Age(userDob)
		if userGender == zutils.GenderMale &&
			(userAge < model.MinMaleAge || userAge > model.MaxMaleAge) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationAgeNotInRange
			result.Metadata = map[string]any{
				"min_age": model.MinMaleAge,
				"max_age": model.MaxMaleAge,
			}
		}
		if userGender == zutils.GenderFemale &&
			(userAge < model.MinFemaleAge || userAge > model.MaxFemaleAge) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationAgeNotInRange
			result.Metadata = map[string]any{
				"min_age": model.MinFemaleAge,
				"max_age": model.MaxFemaleAge,
			}
		}
		return result
	}
}

func RuleKycProfileInfoLack() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if profile.Birthday == "" ||
			profile.IdNumber == "" ||
			profile.IdIssueDate == "" ||
			profile.IdIssuePlace == "" ||
			profile.PermanentAddress == "" {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileInfoLack
		}
		return result
	}
}

func RuleKycProfilePassport() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if profile.IdType == model.IDTypePassport {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileIDTypeNotAllowed
		}
		return result
	}
}

func RuleKycProfileIDType() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		allowedIDTypes := []model.IDType{
			model.IDTypeCCCDChip,
			model.IDTypeCCCDNew,
		}
		if !slices.Contains(allowedIDTypes, profile.IdType) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileIDTypeNotAllowed
		}
		return result
	}
}

func RuleKycTimeOutdated() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		const kycValidDate = "2021-01-01"
		kycValidTime, _ := time.Parse(utils.LayoutDashYYYYMMDD, kycValidDate)
		kycUpdatedTime, _ := time.Parse(time.RFC3339, profile.KycUpdatedDate)
		if kycUpdatedTime.Before(kycValidTime) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycTimeOutdated
		}
		return result
	}
}

func RuleKycNameNonAccent() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		if profile.FullName == removeAccents(profile.FullName) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileNameNonAccent
		}
		return result
	}
}

func RuleKycProfileShouldUpdate() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		rules := []KycProfileValidationRule{
			RuleKycTimeOutdated(),
			RuleKycNameNonAccent(),
		}

		for _, rule := range rules {
			if result = rule(profile); result.IsRejected() {
				result.Status = model.ValidateResultRejected
				result.Code = model.ViolationProfileShouldBeUpdated
				return result
			}
		}
		return result
	}
}

func RuleKycProfileIDExpired() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		if profile == nil {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileIDExpired
			return result
		}

		nowTime := startOfDate(time.Now())
		idExpiredTime := calcIDExpiredDate(profile)
		if nowTime.After(startOfDate(idExpiredTime)) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileIDExpired
		}
		return result
	}
}

func RuleICImageNotEmpty() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if profile == nil || profile.ProfileImage.FrontICUri == "" || profile.ProfileImage.BackICUri == "" {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileICImageEmpty
		}
		return result
	}
}

// This rule is use the flag nfc status belong to user kyc profile, not user nfc data
func RuleKycProfileHasValidNfc() KycProfileValidationRule {
	return func(profile *model.UserProfile) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeKyc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		invalidStatus := []model.KycNfcStatus{
			model.KycNfcStatusUnknown,
			model.KycNfcStatusInvalid,
		}
		if profile == nil || slices.Contains(invalidStatus, profile.KycNfcStatus) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcMissing
		}
		return result
	}
}

func calcIDExpiredDate(profile *model.UserProfile) time.Time {
	idType := profile.IdType
	dobTime, _ := time.Parse(utils.LayoutDashYYYYMMDD, profile.Birthday)
	idIssueTime, _ := time.Parse(utils.LayoutDashYYYYMMDD, profile.IdIssueDate)
	idExpiredTime, _ := time.Parse(utils.LayoutDashYYYYMMDD, profile.IdIssueDate)

	if idType == model.IDTypeCMND {
		return calcTimeFromYears(idIssueTime, 15)
	}

	birthday23rd := calcTimeFromYears(dobTime, 23)
	birthday38rd := calcTimeFromYears(dobTime, 38)
	birthday58rd := calcTimeFromYears(dobTime, 58)
	switch {
	case idIssueTime.Before(birthday23rd):
		return calcTimeFromYears(dobTime, 25)
	case idIssueTime.Before(birthday38rd):
		return calcTimeFromYears(dobTime, 40)
	case idIssueTime.Before(birthday58rd):
		return calcTimeFromYears(dobTime, 60)
	case idIssueTime.Equal(birthday58rd) || idIssueTime.After(birthday58rd):
		return time.Date(9999, 12, 31, 0, 0, 0, 0, time.Local)
	default:
		return idExpiredTime
	}
}

func startOfDate(value time.Time) time.Time {
	return time.Date(value.Year(), value.Month(), value.Day(), 0, 0, 0, 0, time.Local)
}

func calcTimeFromYears(timeValue time.Time, numYears int) time.Time {
	result := timeValue.AddDate(numYears, 0, 0)
	if timeValue.Month() != time.February || timeValue.Day() != 29 || result.Year()%4 == 0 {
		return result
	}
	return result.AddDate(0, 0, -1)
}

func removeAccents(value string) string {
	t := transform.Chain(norm.NFD, runes.Remove(runes.In(unicode.Mn)), norm.NFC)
	result, _, _ := transform.String(t, value)
	return result
}
