package validator

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type ProfileValidatorTestSuite struct {
	suite.Suite
	validator *ProfileValidator
}

func (s *ProfileValidatorTestSuite) SetupTest() {
	s.validator = NewProfileValidator()
}

func TestProfileValidator(t *testing.T) {
	suite.Run(t, new(ProfileValidatorTestSuite))
}

// ... existing code ...

func (s *ProfileValidatorTestSuite) TestRuleKycProfileAge() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid male age",
			profile: &model.UserProfile{
				Gender:   1, // Male
				Birthday: "1990-01-01",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "invalid male age - too young",
			profile: &model.UserProfile{
				Gender:   1,
				Birthday: "2010-01-01",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationAgeNotInRange,
				Metadata: map[string]any{
					"min_age": model.MinMaleAge,
					"max_age": model.MaxMaleAge,
				},
			},
		},
		{
			name: "invalid female age - too old",
			profile: &model.UserProfile{
				Gender:   2, // Female
				Birthday: "1940-01-01",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationAgeNotInRange,
				Metadata: map[string]any{
					"min_age": model.MinFemaleAge,
					"max_age": model.MaxFemaleAge,
				},
			},
		},
		{
			name: "invalid date format",
			profile: &model.UserProfile{
				Gender:   1,
				Birthday: "invalid-date",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultUnknown,
				Code:   model.ViolationAgeInvalid,
			},
		},
	}

	rule := RuleKycProfileAge()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfileInfoLack() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "complete profile",
			profile: &model.UserProfile{
				Birthday:         "1990-01-01",
				IdNumber:         "*********",
				IdIssueDate:      "2020-01-01",
				IdIssuePlace:     "Ha Noi",
				PermanentAddress: "123 Street",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "missing birthday",
			profile: &model.UserProfile{
				IdNumber:         "*********",
				IdIssueDate:      "2020-01-01",
				IdIssuePlace:     "Ha Noi",
				PermanentAddress: "123 Street",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileInfoLack,
			},
		},
	}

	rule := RuleKycProfileInfoLack()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfilePassport() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "non-passport ID",
			profile: &model.UserProfile{
				IdType: model.IDTypeCCCDChip,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "passport ID",
			profile: &model.UserProfile{
				IdType: model.IDTypePassport,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileIDTypeNotAllowed,
			},
		},
	}

	rule := RuleKycProfilePassport()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfileIDType() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid CCCD chip",
			profile: &model.UserProfile{
				IdType: model.IDTypeCCCDChip,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "invalid ID type",
			profile: &model.UserProfile{
				IdType: model.IDTypeCMND,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileIDTypeNotAllowed,
			},
		},
	}

	rule := RuleKycProfileIDType()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycTimeOutdated() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid KYC date",
			profile: &model.UserProfile{
				KycUpdatedDate: "2022-01-01T00:00:00Z",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "outdated KYC",
			profile: &model.UserProfile{
				KycUpdatedDate: "2020-01-01T00:00:00Z",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycTimeOutdated,
			},
		},
	}

	rule := RuleKycTimeOutdated()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycNameNonAccent() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid accented name",
			profile: &model.UserProfile{
				FullName: "Nguyễn Văn A",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "non-accented name",
			profile: &model.UserProfile{
				FullName: "Nguyen Van A",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileNameNonAccent,
			},
		},
	}

	rule := RuleKycNameNonAccent()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfileHasValidNfc() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid NFC status",
			profile: &model.UserProfile{
				KycNfcStatus: model.KycNfcStatusVerified,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "invalid NFC status",
			profile: &model.UserProfile{
				KycNfcStatus: model.KycNfcStatusInvalid,
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcMissing,
			},
		},
		{
			name:    "nil profile",
			profile: nil,
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcMissing,
			},
		},
	}

	rule := RuleKycProfileHasValidNfc()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

// Helper function tests
func (s *ProfileValidatorTestSuite) TestCalcIDExpiredDate() {
	tests := []struct {
		name           string
		profile        *model.UserProfile
		expectedResult time.Time
	}{
		{
			name: "CMND type",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCMND,
				Birthday:    "1990-01-01",
				IdIssueDate: "2010-01-01",
			},
			expectedResult: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			name: "CCCD before 23",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCCCDChip,
				Birthday:    "2000-01-01",
				IdIssueDate: "2020-01-01",
			},
			expectedResult: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
		},
		// Add more test cases for different age ranges
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := calcIDExpiredDate(tt.profile)
			s.Equal(tt.expectedResult, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfileShouldUpdate() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "profile does not need update",
			profile: &model.UserProfile{
				FullName:       "Nguyễn Văn A",
				KycUpdatedDate: "2022-01-01T00:00:00Z",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "profile needs update - outdated KYC",
			profile: &model.UserProfile{
				FullName:       "Nguyễn Văn A",
				KycUpdatedDate: "2020-01-01T00:00:00Z",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileShouldBeUpdated,
			},
		},
		{
			name: "profile needs update - non-accented name",
			profile: &model.UserProfile{
				FullName:       "Nguyen Van A",
				KycUpdatedDate: "2022-01-01T00:00:00Z",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileShouldBeUpdated,
			},
		},
	}

	rule := RuleKycProfileShouldUpdate()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleKycProfileIDExpired() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid ID not expired",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCCCDChip,
				Birthday:    "2000-01-01",
				IdIssueDate: "2023-01-01",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "expired ID - CMND type",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCMND,
				Birthday:    "1990-01-01",
				IdIssueDate: "2000-01-01", // Expired after 15 years (2015)
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileIDExpired,
			},
		},
		{
			name: "expired ID - CCCD before 23",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCCCDChip,
				Birthday:    "1965-01-01",
				IdIssueDate: "2024-01-01", // Issue date after 2021-01-01 to make it not expired
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "invalid issue date format",
			profile: &model.UserProfile{
				IdType:      model.IDTypeCCCDChip,
				Birthday:    "1990-01-01",
				IdIssueDate: "invalid-date",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileIDExpired,
			},
		},
		{
			name:    "nil profile",
			profile: nil,
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileIDExpired,
			},
		},
	}

	rule := RuleKycProfileIDExpired()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestRuleICImageNotEmpty() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		expected model.ValidateResult
	}{
		{
			name: "valid IC images",
			profile: &model.UserProfile{
				ProfileImage: model.ProfileImage{
					FrontICUri: "http://example.com/front.jpg",
					BackICUri:  "http://example.com/back.jpg",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "empty front IC image",
			profile: &model.UserProfile{
				ProfileImage: model.ProfileImage{
					FrontICUri: "",
					BackICUri:  "http://example.com/back.jpg",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileICImageEmpty,
			},
		},
		{
			name: "empty back IC image",
			profile: &model.UserProfile{
				ProfileImage: model.ProfileImage{
					FrontICUri: "http://example.com/front.jpg",
					BackICUri:  "",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileICImageEmpty,
			},
		},
		{
			name: "both IC images empty",
			profile: &model.UserProfile{
				ProfileImage: model.ProfileImage{
					FrontICUri: "",
					BackICUri:  "",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileICImageEmpty,
			},
		},
		{
			name:    "nil profile",
			profile: nil,
			expected: model.ValidateResult{
				Type:   model.ValidateTypeKyc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileICImageEmpty,
			},
		},
	}

	rule := RuleICImageNotEmpty()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.profile)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *ProfileValidatorTestSuite) TestValidateProfile() {
	tests := []struct {
		name     string
		profile  *model.UserProfile
		rules    []KycProfileValidationRule
		expected []model.ValidateResult
	}{
		{
			name: "no rules",
			profile: &model.UserProfile{
				FullName: "Test User",
			},
			rules:    []KycProfileValidationRule{},
			expected: []model.ValidateResult{},
		},
		{
			name: "single rule - passing",
			profile: &model.UserProfile{
				FullName:       "Nguyễn Văn A",
				KycUpdatedDate: "2022-01-01T00:00:00Z",
			},
			rules: []KycProfileValidationRule{
				RuleKycNameNonAccent(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeKyc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
			},
		},
		{
			name: "multiple rules - mixed results",
			profile: &model.UserProfile{
				FullName:       "Nguyen Van A",         // Should fail non-accent check
				KycUpdatedDate: "2022-01-01T00:00:00Z", // Should pass time check
			},
			rules: []KycProfileValidationRule{
				RuleKycNameNonAccent(),
				RuleKycTimeOutdated(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeKyc,
					Status: model.ValidateResultRejected,
					Code:   model.ViolationProfileNameNonAccent,
				},
				{
					Type:   model.ValidateTypeKyc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			validator := NewProfileValidator()
			results := validator.ValidateProfile(tt.profile, tt.rules)
			s.Equal(tt.expected, results)
		})
	}
}
