package validator

import (
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type NfcInfoValidator struct{}

func NewNfcInfoValidator() *NfcInfoValidator {
	return &NfcInfoValidator{}
}

type KycNfcValidationRule func(nfcData *model.UserKycNfc) model.ValidateResult

func (kv NfcInfoValidator) ValidateNfc(nfcData *model.UserKycNfc,
	rules []KycNfcValidationRule) []model.ValidateResult {
	if len(rules) == 0 {
		return []model.ValidateResult{}
	}

	validationResult := make([]model.ValidateResult, 0, len(rules))
	for _, rule := range rules {
		result := rule(nfcData)
		validationResult = append(validationResult, result)
	}
	return validationResult
}

func RuleKycNfcRawEmpty() KycNfcValidationRule {
	return func(nfcData *model.UserKycNfc) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeNfc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if nfcData.NfcDataRaw.IsEmpty() {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcRawEmpty
		}
		return result
	}
}

func RuleKycNfcSourceAllowed() KycNfcValidationRule {
	return func(nfcData *model.UserKycNfc) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeNfc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if nfcData.CollectSource != "jth" {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcSourceNotAllowed
		}
		return result
	}
}

func RuleKycNfcIdNotEmpty() KycNfcValidationRule {
	return func(nfcData *model.UserKycNfc) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeNfc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if nfcData.NfcId == "" && nfcData.CollectSource == "vneid" {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcIdEmpty
		}
		return result
	}
}
func RuleKycNfcGenderValid() KycNfcValidationRule {
	return func(nfcData *model.UserKycNfc) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeNfc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		gender := zutils.GenderConvertToString(nfcData.NfcDataDG13.Gender)
		if !zutils.IsGenderStringValid(gender) {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcGenderInvalid
			result.Metadata = map[string]any{"gender_invalid": gender}
		}
		return result
	}
}

func RuleKycNfcDataRequired() KycNfcValidationRule {
	return func(nfcData *model.UserKycNfc) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeNfc,
			Status: model.ValidateResultApproved,
			Code:   "",
		}
		if nfcData.NfcDataDG13.IsEmpty() {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcDg13Emtpy
		}
		if err := nfcData.NfcDataDG13.Validate(); err != nil {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationProfileKycNfcDg13Invalid
			result.Metadata = map[string]any{"nfc_validate_error": err.Error()}
		}
		return result
	}
}
