package validator

import (
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type ResourceValidator struct {
	Resources []model.Resource
}

func (r ResourceValidator) Validate(resourceChecks []model.ResourceCheck) error {
	checkMaps := make(map[string]bool)
	for _, resource := range r.Resources {
		for _, datum := range resource.Data {
			checkMaps[fmt.Sprintf("%s:%s", resource.Type, datum.Code)] = true
		}
	}

	for _, check := range resourceChecks {
		val, ok := checkMaps[fmt.Sprintf("%s:%s", check.Type, check.Code)]
		if !ok || !val {
			return fmt.Errorf("resource invalid, type: %s", check.Type)
		}
	}

	return nil
}
