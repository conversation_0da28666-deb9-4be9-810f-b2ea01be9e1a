package validator

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type NfcValidatorTestSuite struct {
	suite.Suite
	validator *NfcInfoValidator
}

func (s *NfcValidatorTestSuite) SetupTest() {
	s.validator = NewNfcInfoValidator()
}

func TestNfcValidator(t *testing.T) {
	suite.Run(t, new(NfcValidatorTestSuite))
}

func (s *NfcValidatorTestSuite) TestValidateNfc() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		rules    []KycNfcValidationRule
		expected []model.ValidateResult
	}{
		{
			name:     "no rules",
			nfcData:  &model.UserKycNfc{},
			rules:    []KycNfcValidationRule{},
			expected: []model.ValidateResult{},
		},
		{
			name:    "single rule - passing",
			nfcData: &model.UserKycNfc{NfcDataRaw: model.KycNfcDataRaw{Com: "raw data"}},
			rules: []KycNfcValidationRule{
				RuleKycNfcRawEmpty(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
			},
		},
		{
			name:    "multiple rules - mixed results",
			nfcData: &model.UserKycNfc{},
			rules: []KycNfcValidationRule{
				RuleKycNfcRawEmpty(),
				RuleKycNfcGenderValid(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultRejected,
					Code:   model.ViolationProfileKycNfcRawEmpty,
				},
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultRejected,
					Code:   model.ViolationProfileKycNfcGenderInvalid,
					Metadata: map[string]any{
						"gender_invalid": "UNKNOWN",
					},
				},
			},
		},
		{
			name: "multiple rules including source validation - mixed results",
			nfcData: &model.UserKycNfc{
				NfcDataRaw:    model.KycNfcDataRaw{Com: "raw data"},
				CollectSource: "other", // Not allowed source
			},
			rules: []KycNfcValidationRule{
				RuleKycNfcRawEmpty(),
				RuleKycNfcSourceAllowed(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultRejected,
					Code:   model.ViolationProfileKycNfcSourceNotAllowed,
				},
			},
		},
		{
			name: "multiple rules including source validation - all passing",
			nfcData: &model.UserKycNfc{
				NfcDataRaw:    model.KycNfcDataRaw{Com: "raw data"},
				CollectSource: "jth", // Allowed source
			},
			rules: []KycNfcValidationRule{
				RuleKycNfcRawEmpty(),
				RuleKycNfcSourceAllowed(),
			},
			expected: []model.ValidateResult{
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
				{
					Type:   model.ValidateTypeNfc,
					Status: model.ValidateResultApproved,
					Code:   "",
				},
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			results := s.validator.ValidateNfc(tt.nfcData, tt.rules)
			s.Equal(tt.expected, results)
		})
	}
}

func (s *NfcValidatorTestSuite) TestRuleKycNfcRawEmpty() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		expected model.ValidateResult
	}{
		{
			name: "valid raw data",
			nfcData: &model.UserKycNfc{
				NfcDataRaw: model.KycNfcDataRaw{
					Com: "raw data",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name:    "empty raw data",
			nfcData: &model.UserKycNfc{},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcRawEmpty,
			},
		},
	}

	rule := RuleKycNfcRawEmpty()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.nfcData)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *NfcValidatorTestSuite) TestRuleKycNfcGenderValid() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		expected model.ValidateResult
	}{
		{
			name: "valid gender",
			nfcData: &model.UserKycNfc{
				NfcDataDG13: model.KycNfcDataDG13{Gender: 1}, // Assuming 1 is valid gender
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "invalid gender",
			nfcData: &model.UserKycNfc{
				NfcDataDG13: model.KycNfcDataDG13{Gender: 999}, // Invalid gender
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcGenderInvalid,
				Metadata: map[string]any{
					"gender_invalid": "UNKNOWN",
				},
			},
		},
	}

	rule := RuleKycNfcGenderValid()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.nfcData)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *NfcValidatorTestSuite) TestRuleKycNfcDataRequired() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		expected model.ValidateResult
	}{
		{
			name: "valid DG13 data",
			nfcData: &model.UserKycNfc{
				NfcDataDG13: model.KycNfcDataDG13{
					FullName:       "Test User",
					Gender:         1,
					EIDNumber:      "1234567890",
					Birthday:       "2000-01-01",
					IssueDate:      "2020-01-01",
					PlaceOrigin:    "Hanoi",
					PlaceResidence: "Hanoi",
					OldIDNumber:    "1234567890",
				},
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name:    "empty DG13 data",
			nfcData: &model.UserKycNfc{},
			expected: model.ValidateResult{
				Type:     model.ValidateTypeNfc,
				Status:   model.ValidateResultRejected,
				Code:     model.ViolationProfileKycNfcDg13Invalid,
				Metadata: map[string]any{"nfc_validate_error": `birthday: cannot be blank; eid_number: cannot be blank; full_name: cannot be blank; gender: cannot be blank.`},
			},
		},
	}

	rule := RuleKycNfcDataRequired()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.nfcData)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *NfcValidatorTestSuite) TestRuleKycNfcSourceAllowed() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		expected model.ValidateResult
	}{
		{
			name: "allowed source - jth",
			nfcData: &model.UserKycNfc{
				CollectSource: "jth",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "not allowed source - jth_nfc",
			nfcData: &model.UserKycNfc{
				CollectSource: "jth_nfc",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcSourceNotAllowed,
			},
		},
		{
			name: "not allowed source",
			nfcData: &model.UserKycNfc{
				CollectSource: "other",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcSourceNotAllowed,
			},
		},
		{
			name: "empty source",
			nfcData: &model.UserKycNfc{
				CollectSource: "",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcSourceNotAllowed,
			},
		},
	}

	rule := RuleKycNfcSourceAllowed()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.nfcData)
			s.Equal(tt.expected, result)
		})
	}
}

func (s *NfcValidatorTestSuite) TestRuleKycNfcIdNotEmpty() {
	tests := []struct {
		name     string
		nfcData  *model.UserKycNfc
		expected model.ValidateResult
	}{
		{
			name: "valid nfc id",
			nfcData: &model.UserKycNfc{
				NfcId: "123456789",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultApproved,
				Code:   "",
			},
		},
		{
			name: "empty nfc id",
			nfcData: &model.UserKycNfc{
				NfcId:         "",
				CollectSource: "vneid",
			},
			expected: model.ValidateResult{
				Type:   model.ValidateTypeNfc,
				Status: model.ValidateResultRejected,
				Code:   model.ViolationProfileKycNfcIdEmpty,
			},
		},
	}

	rule := RuleKycNfcIdNotEmpty()
	for _, tt := range tests {
		s.Run(tt.name, func() {
			result := rule(tt.nfcData)
			s.Equal(tt.expected, result)
		})
	}
}
