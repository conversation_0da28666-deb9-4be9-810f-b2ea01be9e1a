package validator

import (
	"image"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

type ImageSize struct {
	Width  int
	Height int
}

var (
	imageSizeHD = ImageSize{
		Width:  1280,
		Height: 720,
	}
)

const (
	minRgbValue = uint32(0)
	maxRgbValue = uint32(65535)
)

type ImageValidator struct {
}

func NewImageValidator() *ImageValidator {
	return &ImageValidator{}
}

type ImageValidationRule func(img image.Image) model.ValidateResult

func (iv ImageValidator) ValidateImage(img image.Image, rules []ImageValidationRule) []model.ValidateResult {
	if len(rules) == 0 {
		return []model.ValidateResult{}
	}

	validationResult := make([]model.ValidateResult, 0, len(rules))
	for _, rule := range rules {
		result := rule(img)
		validationResult = append(validationResult, result)
	}
	return validationResult

}

func RuleImageSizeLessThan(width, height int) ImageValidationRule {
	return func(img image.Image) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeImage,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		bounds := img.Bounds()
		imageWidth := bounds.Max.X
		imageHeight := bounds.Max.Y
		if imageWidth > width || imageHeight > height {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationImageSizeTooBig
		}
		return result
	}
}

func RuleImageSizeHD() ImageValidationRule {
	return RuleImageSizeLessThan(imageSizeHD.Width, imageSizeHD.Height)
}

func RuleImageSingleColorOrEmpty() ImageValidationRule {
	return func(img image.Image) model.ValidateResult {
		result := model.ValidateResult{
			Type:   model.ValidateTypeImage,
			Status: model.ValidateResultApproved,
			Code:   "",
		}

		bounds := img.Bounds()
		notHasGrayscale := false
		hasWhite, hasBlack := false, false

		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
				color := img.At(x, y)
				r, g, b, a := color.RGBA()
				if !isGrayscale(r, g, b) {
					notHasGrayscale = true
					break
				}
				if !hasWhite && isColorWhite(r) {
					hasWhite = true
				}
				if !hasBlack && isColorBlack(r, a) {
					hasBlack = true
				}
				if hasWhite && hasBlack {
					break
				}
			}
		}

		if notHasGrayscale {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationImageNotGrayscale
		} else if !hasWhite || !hasBlack {
			result.Status = model.ValidateResultRejected
			result.Code = model.ViolationImageHasSolidColor
		}
		return result
	}
}

func isGrayscale(r, g, b uint32) bool {
	// check if image color is in grayscale
	// for an image color to be in grayscale, at any pixel, the value at three color channels must be the same
	// which mean r = g = b
	return r == g && g == b
}

func isColorBlack(value, alpha uint32) bool {
	// alpha is the color intensity
	// with zero value will ignore the rgb value and look like white so must evaluate to max value
	// for a color in RGBA to be black, r, g, b channel value must be equal
	return value == minRgbValue && alpha == maxRgbValue
}

func isColorWhite(value uint32) bool {
	// could ignore alpha from with max value, on or off will look just like white
	return value == maxRgbValue
}
