package service

import (
	"context"

	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) ResetClientEkycNfc(ctx context.Context,
	_ *v1.ResetClientEkycNfcRequest) (*v1.ResetClientEkycNfcResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := o.profileUc.ResetUserKycNfc(ctx, zalopayID); err != nil {
		o.logger.WithContext(ctx).Errorf("fail to exec reset user kyc nfc, err %v", err)
		return nil, convertToTransportError(err)
	}
	return &v1.ResetClientEkycNfcResponse{}, nil
}
