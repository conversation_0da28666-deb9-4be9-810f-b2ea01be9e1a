package service

import (
	"context"
	"sync"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

type OperationService struct {
	v1.UnimplementedOperationServer
	logger     *log.Helper
	contractUc *usecase.ContractUsecase
	onboardUc  *usecase.OnboardingUsecase
}

// NewOnboardingService new an onboarding service.
func NewOperationService(
	contractUc *usecase.ContractUsecase,
	onboardUc *usecase.OnboardingUsecase,
	kLogger log.Logger) *OperationService {
	logging := log.With(kLogger, "service", "operation")
	return &OperationService{
		onboardUc:  onboardUc,
		contractUc: contractUc,
		logger:     log.NewHelper(logging),
	}
}

func (s *OperationService) TriggerSyncOnboarding(ctx context.Context,
	req *v1.TriggerSyncOnboardingRequest) (*v1.TriggerSyncOnboardingResponse, error) {
	logger := s.logger.WithContext(ctx)

	if err := req.ValidateAll(); err != nil {
		logger.Errorf("TriggerSyncOnboardingRequest validation failed: %v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid request")
	}

	go s.onboardUc.SyncOnboardingStatus(context.WithoutCancel(ctx), req.ZalopayId, req.OnboardingId)

	return &v1.TriggerSyncOnboardingResponse{}, nil
}

func (s *OperationService) TriggerSyncOnboardingJob(ctx context.Context,
	req *v1.TriggerSyncOnboardingJobRequest) (*v1.TriggerSyncOnboardingJobResponse, error) {
	logger := s.logger.WithContext(ctx)

	if err := req.ValidateAll(); err != nil {
		logger.Errorf("TriggerSyncOnboardingRequest validation failed: %v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid request")
	}

	go s.onboardUc.TriggerQueryOnboardingStatusJob(context.WithoutCancel(ctx), req.ZalopayId, req.OnboardingId)

	return &v1.TriggerSyncOnboardingJobResponse{}, nil
}

func (s *OperationService) TriggerContractSigningJob(ctx context.Context,
	req *v1.TriggerContractSigningJobRequest) (*v1.TriggerContractSigningJobResponse, error) {
	logger := s.logger.WithContext(ctx)

	if err := req.ValidateAll(); err != nil {
		logger.Errorf("TriggerSyncOnboardingRequest validation failed: %v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid request")
	}

	ctx = context.WithoutCancel(ctx)
	go s.contractUc.TriggerContractSigningJob(ctx, req.ZalopayId, req.OnboardingId)

	return &v1.TriggerContractSigningJobResponse{}, nil
}

// Deprecated: please not use this method anymore
func (s *OperationService) TriggerSubmitSelfieJob(ctx context.Context,
	req *v1.TriggerSubmitSelfieJobRequest) (*v1.TriggerSubmitSelfieJobResponse, error) {
	return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "this method is deprecated")
}

func (s *OperationService) TriggerSyncOnboardingsJob(ctx context.Context,
	req *v1.TriggerSyncOnboardingsJobRequest) (*v1.TriggerSyncOnboardingsJobResponse, error) {

	logger := s.logger.WithContext(ctx)

	if err := req.ValidateAll(); err != nil {
		logger.Errorf("TriggerSyncOnboardingsJobRequest validation failed: %v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "invalid request")
	}

	handler := func(ctx context.Context, waitGroup *sync.WaitGroup, listChan chan *v1.OnboardingOp) {
		for v := range listChan {
			zalopayID := v.ZalopayId
			onboardingID := v.OnboardingId
			s.onboardUc.TriggerQueryOnboardingStatusJob(ctx, zalopayID, onboardingID)
		}
		waitGroup.Done()
	}

	go func() {
		workers := 10
		waitGp := &sync.WaitGroup{}
		newCtx := context.WithoutCancel(ctx)
		listChan := make(chan *v1.OnboardingOp, len(req.GetData()))

		for _, data := range req.GetData() {
			listChan <- data
		}
		close(listChan)

		for i := 0; i < workers; i++ {
			waitGp.Add(1)
			go handler(newCtx, waitGp, listChan)
		}

		waitGp.Wait()
	}()

	return &v1.TriggerSyncOnboardingsJobResponse{}, nil
}
