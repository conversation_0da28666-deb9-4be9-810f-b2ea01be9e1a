package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	pkgerror "github.com/pkg/errors"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

func (o *OnboardingService) GetClientResources(ctx context.Context,
	req *v1.GetClientResourcesRequest) (*v1.GetClientResourcesResponse, error) {
	logger := o.logger.WithContext(ctx)

	if err := req.Validate(); err != nil {
		logger.WithContext(ctx).Errorf("validate request fail, error=%v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resourceTypes, err := convertToUCResourceTypes(req.GetResourceTypes())
	if err != nil {
		logger.WithContext(ctx).Errorf("convert resource types fail, error=%v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resp, err := o.profileUc.GetResources(ctx, resourceTypes)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to fetch resource, err=%v", err)
		return nil, convertToTransportError(err)
	}
	if len(resp) == 0 {
		o.logger.WithContext(ctx).Errorf("Fail to fetch resource, err %v", err)
		return nil, errors.BadRequest("NO_RESOURCES_FOUND", "no resources found")
	}

	resources := make([]*v1.CIMBResource, len(resp))
	for i, rs := range resp {
		resources[i] = convertToOnboardingResource(rs)
	}

	return &v1.GetClientResourcesResponse{
		Resources: resources,
	}, nil
}

func convertToUCResourceTypes(resourceTypes []v1.ResourceType) ([]string, error) {
	var result []string
	var mapper = map[v1.ResourceType]string{
		v1.ResourceType_CITY:              model.ResourceTypeCity,
		v1.ResourceType_INCOME:            model.ResourceTypeIncome,
		v1.ResourceType_EDUCATION:         model.ResourceTypeEducation,
		v1.ResourceType_JOB_TITLE:         model.ResourceTypeJobTitle,
		v1.ResourceType_OCCUPATION:        model.ResourceTypeOccupation,
		v1.ResourceType_FUND_PURPOSE:      model.ResourceTypeFundPurpose,
		v1.ResourceType_SOURCE_OF_FUND:    model.ResourceTypeSourceOfFund,
		v1.ResourceType_EMPLOYMENT_STATUS: model.ResourceTypeEmploymentStt,
	}
	for _, r := range resourceTypes {
		if t, ok := mapper[r]; ok {
			result = append(result, t)
			continue
		}
		return nil, pkgerror.Errorf("unsupported resource type %v", r)
	}
	return result, nil
}
