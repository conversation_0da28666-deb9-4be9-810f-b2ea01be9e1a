package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func (o *OnboardingService) SubmitClientFaceChallenge(ctx context.Context,
	req *v1.SubmitClientFaceChallengeRequest) (*v1.SubmitClientFaceChallengeResponse, error) {
	logger := o.logger.WithContext(ctx)
	session, _ := auth.FromContext(ctx)
	zaloPayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := req.GetOnboardingId()

	if err := req.Validate(); err != nil {
		logger.WithContext(ctx).Errorf("validate request fail, error=%v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	type FaceChallengeIDData struct {
		Token string `json:"token"`
	}
	var faceChallengeIDData FaceChallengeIDData
	if err := zutils.Base64DataToStruct(req.GetFaceRequestId(), &faceChallengeIDData); err != nil {
		logger.WithContext(ctx).Errorf("Base64DataToStruct fail, error=%v", err)
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := dto.SubmitFaceChallengeParams{
		ZalopayID:       zaloPayID,
		OnboardingID:    onboardingID,
		FaceChallengeID: faceChallengeIDData.Token,
	}
	if err := o.faceAuthUc.SubmitFaceChallenge(ctx, params); err != nil {
		logger.WithContext(ctx).Errorf("SubmitFaceChallenge fail, error=%v", err)
		return nil, convertToTransportError(err)
	}

	// Register and trigger submit face image job for contract flow
	ctx = context.WithoutCancel(ctx)
	go o.contractUc.RegisterContractSigningJob(ctx, zaloPayID, onboardingID)

	return &v1.SubmitClientFaceChallengeResponse{OnboardingId: onboardingID}, nil
}
