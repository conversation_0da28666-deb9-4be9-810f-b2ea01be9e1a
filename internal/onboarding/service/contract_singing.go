package service

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) RequestClientOTP(ctx context.Context,
	req *v1.RequestClientOTPRequest) (*v1.RequestClientOTPResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := req.GetOnboardingId()

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	var err error
	var resp *dto.RequestOTPResult
	newCtx := fromSessionToClientSessionCtx(ctx, session)

	switch req.GetOtpType() {
	case v1.OTPType_CONTRACT_SIGNING:
		err = fmt.Errorf("otp for contract singing is deprecated")
	case v1.OTPType_LINK_TYPE_3:
		resp, err = o.contractUc.RequestLinkingOTP(newCtx, zalopayID, onboardingID)
	default:
		err = fmt.Errorf("otp type %s not supported", req.GetOtpType().String())
	}

	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to request otp, err %v", err)
		return nil, convertToTransportError(err)
	}
	if resp == nil {
		o.logger.WithContext(ctx).Errorf("Fail to request otp, empty response")
		return nil, errors.InternalServer(errorkit.CodeInternalError.String(), "empty request otp response")
	}

	return &v1.RequestClientOTPResponse{
		WaitTime:     resp.WaitTime,
		ResendTime:   resp.ResendTime,
		Status:       resp.Status,
		ErrorMessage: resp.ErrorMessage,
		PhoneNumber:  resp.PhoneNumber,
		OtpRequestId: resp.OtpRequestId,
		IsSendSms:    resp.IsSendSms,
	}, nil
}

func (o *OnboardingService) VerifyClientOTP(ctx context.Context,
	req *v1.VerifyClientOTPRequest) (*v1.VerifyClientOTPResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := req.GetOnboardingId()

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	var err error
	var verifyStatus bool
	newCtx := fromSessionToClientSessionCtx(ctx, session)

	switch req.GetOtpType() {
	case v1.OTPType_CONTRACT_SIGNING:
		err = fmt.Errorf("otp for contract singing is deprecated")
	case v1.OTPType_LINK_TYPE_3:
		verifyStatus, err = o.contractUc.VerifyLinkingOTP(newCtx, zalopayID, onboardingID, req.GetOtpCode())
	default:
		err = fmt.Errorf("otp type %s not supported", req.GetOtpType().String())
	}

	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to verify otp, err %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.VerifyClientOTPResponse{
		Status: verifyStatus,
	}, nil
}

func (o *OnboardingService) SubmitClientWetSign(ctx context.Context,
	req *v1.SubmitClientWetSignRequest) (*v1.SubmitClientWetSignResponse, error) {
	session, _ := auth.FromContext(ctx)
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.WetSignSubmitParams{
		ZalopayID:    session.GetZalopayId(),
		OnboardingID: req.GetOnboardingId(),
		ImageData:    req.GetImageData().GetImageData(),
		ImageMime:    model.ImageMime(req.GetImageData().GetImageType()),
	}
	if !params.ValidateImageMime() {
		err := fmt.Errorf("image format not supported, mime type: %s", params.ImageMime)
		return nil, errors.BadRequest(errorkit.CodeImageFormatNotSupported.String(), err.Error())
	}

	resp, err := o.contractUc.SubmitWetSign(ctx, params)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to submit wet sign, err %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.SubmitClientWetSignResponse{
		OnboardingId: resp.OnboardingID,
	}, nil
}

func (o *OnboardingService) GetClientContract(ctx context.Context,
	req *v1.GetClientContractRequest) (*v1.GetClientContractResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resp, err := o.contractUc.GetContract(ctx, zalopayID, req.OnboardingId)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to get contract, err %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.GetClientContractResponse{
		UnsignedContractUrl: resp.UnsignedContract,
		SignedContractUrl:   resp.SignedContract,
	}, nil
}
