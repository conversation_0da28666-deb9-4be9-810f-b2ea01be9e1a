package service

import (
	"context"

	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) GetClientPermissions(ctx context.Context,
	_ *v1.GetClientPermissionsRequest) (*v1.GetClientPermissionsResponse, error) {
	session, _ := auth.FromContext(ctx)
	zaloPayID := cast.ToInt64(session.GetZalopayId())

	params := &dto.UserPermissionsParams{
		ZalopayID: zaloPayID,
		DeviceID:  session.GetMeta().GetDeviceId(),
		UserIP:    session.GetMeta().GetUserIp(),
	}
	permissions, err := o.profileUc.GetUserPermissions(ctx, params)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to get user permissions, err %v", err)
		return nil, convertToTransportError(err)
	}

	kycStatus := permissions.KycProgress.Status
	kycStatusNotice := o.buildNoticeFromKycStatus(kycStatus)

	return &v1.GetClientPermissionsResponse{
		IsWhitelisted: permissions.IsWhitelisted,
		BindInfo:      ucBindInfoToBindInfo(permissions.BindInfo),
		KycStatus:     ucKycProgressToKycStatus(kycStatus, kycStatusNotice),
		BindStatus:    ucBindStatusToBindStatus(permissions.BindInfo.Status),
	}, nil
}
