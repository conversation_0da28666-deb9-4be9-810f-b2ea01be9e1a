package scheduler

type FaceImageSubmitCondResult struct {
	Eligible        bool   `json:"eligible"`
	FaceChallengeID string `json:"face_challenge_id"`
}

type SubmitFaceImageResult struct {
	IsSuccess      bool `json:"success"`
	TxnExpired     bool `json:"txn_expired"`
	FaceImgExpired bool `json:"face_img_expired"`
}

func (r *SubmitFaceImageResult) IsSubmitSuccess() bool {
	return r != nil && r.IsSuccess
}

func (r *SubmitFaceImageResult) IsTxnExpired() bool {
	return r != nil && r.TxnExpired
}

func (r *SubmitFaceImageResult) IsFCImgExpired() bool {
	return r != nil && r.FaceImgExpired
}

type ContractSingingResult struct {
	IsSuccess      bool `json:"success"`
	FaceImgExpired bool `json:"face_img_expired"`
}

func (r *ContractSingingResult) IsSignSuccess() bool {
	return r != nil && r.IsSuccess
}

func (r *ContractSingingResult) IsFCImgExpired() bool {
	return r != nil && r.FaceImgExpired
}
