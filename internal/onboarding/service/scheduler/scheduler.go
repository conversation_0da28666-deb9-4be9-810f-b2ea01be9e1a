package scheduler

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	tempCli "go.temporal.io/sdk/client"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

type SchedulerService interface {
	PollingOnboardingStatusWorkflow(ctx workflow.Context, workflowData *model.OnboardingStatusTask) error
	PollingOnboardingStatusActivity(ctx context.Context, workflowData *model.OnboardingStatusTask) (*dto.OnboardingPollingResult, error)
	ForceUserFaceChallengeActivity(ctx context.Context, zalopayID string, onboardingID string) error
	ForceUserContractSigningActivity(ctx context.Context, zalopayID string, onboardingID string) error
	TriggerOnboardingStatusJobActivity(ctx context.Context, zalopayID, onboardingID string) error

	SubmitFaceImageWorkflow(ctx workflow.Context, workflowData *dto.FaceImgSubmitTask) error
	ContractSigningWorkflow(ctx workflow.Context, workflowData *dto.ContractSingingTask) error
	SubmitFaceImageActivity(ctx context.Context, workflowData *dto.FaceImgSubmitTask) (*SubmitFaceImageResult, error)
	MarkContractCompleteActivity(ctx context.Context, workflowData *dto.FaceImgSubmitTask) error
	ProcessContractSigningActivity(ctx context.Context, workflowData *dto.ContractSingingTask) (*ContractSingingResult, error)
	CheckFaceImageUploadCondActivity(ctx context.Context, workflowData *dto.FaceImgSubmitCheck) (*FaceImageSubmitCondResult, error)
	GetAndVerifyFaceImageDataActivity(ctx context.Context, workflowData *dto.FaceImgSubmitTask, faceChallengeID string) (*model.UserFaceChallenge, error)
	UpdateAfterContractSignedActivity(ctx context.Context, workflowData *dto.ContractSingingTask) error
}

type schedulerService struct {
	logger     *log.Helper
	kLogger    log.Logger
	temporal   tempCli.Client
	maintMgr   maintenance.Handler
	config     *config.Onboarding_Schedulers
	onboardUc  *usecase.OnboardingUsecase
	contractUc *usecase.ContractUsecase
	faceAuthUc *usecase.FaceAuthUsecase
}

func NewWorkflowService(
	conf *config.Onboarding, temporal tempCli.Client,
	maintMgr maintenance.Handler, kLogger log.Logger, contractUc *usecase.ContractUsecase,
	faceAuthUc *usecase.FaceAuthUsecase, onboardUc *usecase.OnboardingUsecase) SchedulerService {
	logging := log.With(kLogger, "service", "scheduler")
	return &schedulerService{
		temporal:   temporal,
		maintMgr:   maintMgr,
		kLogger:    logging,
		logger:     log.NewHelper(logging),
		config:     conf.GetSchedulers(),
		onboardUc:  onboardUc,
		contractUc: contractUc,
		faceAuthUc: faceAuthUc,
	}
}
