package scheduler

import (
	"go.temporal.io/sdk/temporal"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
)

func buildActivityRetryPolicyByTaskConfig(taskConf *config.TemporalTask) *temporal.RetryPolicy {
	interval := taskConf.GetTimeInterval().AsDuration()
	maxInterval := taskConf.GetMaxTimeInterval().AsDuration()
	return &temporal.RetryPolicy{
		BackoffCoefficient:     1.5,
		InitialInterval:        interval,
		MaximumInterval:        maxInterval,
		NonRetryableErrorTypes: []string{},
	}
}
