package scheduler

import (
	"fmt"
	"slices"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/api/enums/v1"
	tLog "go.temporal.io/sdk/log"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

func (s *schedulerService) PollingOnboardingStatusWorkflow(
	ctx workflow.Context, workflowData *model.OnboardingStatusTask) error {
	const actIDInitialPattern = "initial_poll:%v"
	const actIDExtendedPattern = "extend_poll:%v"

	logger := workflow.GetLogger(ctx)
	logger = tLog.With(logger,
		"zalopay_id", workflowData.ZalopayID,
		"onboarding_id", workflowData.OnboardingID,
	)

	logger.Info("Start process query onboarding status workflow", "data", workflowData)

	var result *dto.OnboardingPollingResult

	// First phase - initial phase onboarding status check
	opts := workflow.ActivityOptions{
		ActivityID:             fmt.Sprintf(actIDInitialPattern, workflowData.OnboardingID),
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: time.Hour * 24 * 7, // 1 week
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Minute,
			MaximumInterval:    time.Minute * 5,
			BackoffCoefficient: 1.5,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, opts)
	err := workflow.ExecuteActivity(ctx, s.PollingOnboardingStatusActivity, workflowData).Get(ctx, &result)
	if result != nil && err == nil {
		logger.Info("Query onboarding status activity has completed", "result", result)
		return nil
	}

	var activityErr *temporal.ActivityError
	statesCanExtend := []enums.RetryState{
		enums.RETRY_STATE_TIMEOUT,
		enums.RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED,
	}
	switch {
	case temporal.IsTimeoutError(err):
		logger.Info("Query onboarding status activity timeout, continue extend polling", "error", err)
	case errors.As(err, &activityErr) && slices.Contains(statesCanExtend, activityErr.RetryState()):
		logger.Error("Query onboarding status activity reach max attempt, continue extend polling", "error", err)
	default:
		logger.Error("Query onboarding status activity failed", "error", err)
		return err
	}

	// Second phase - extended phase onboarding status check
	opts = workflow.ActivityOptions{
		ActivityID:             fmt.Sprintf(actIDExtendedPattern, workflowData.OnboardingID),
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: time.Hour * 24 * 23, // next 23 days after 1 week
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Hour * 12,
			MaximumInterval:    time.Hour * 12,
			BackoffCoefficient: 1.0,
		},
	}
	ctx = workflow.WithActivityOptions(ctx, opts)
	err = workflow.ExecuteActivity(ctx, s.PollingOnboardingStatusActivity, workflowData).Get(ctx, &result)
	if err != nil {
		logger.Error("Query onboarding status activity failed", "error", err)
		return err
	}

	logger.Info("Query onboarding status workflow has completed", "result", result)

	return nil
}

// Deprecated: Use ContractSigningWorkflow instead
func (s *schedulerService) SubmitFaceImageWorkflow(
	ctx workflow.Context, workflowData *dto.FaceImgSubmitTask) error {
	logger := workflow.GetLogger(ctx)
	logger = tLog.With(logger,
		"zalopay_id", workflowData.ZalopayID,
		"onboarding_id", workflowData.OnboardingID,
	)

	logger.Info("Start process submit face image workflow", "data", workflowData)

	conf := s.config.GetSubmitFaceImage()
	// Default all activity has same max execution timeout and workflow exec timeout, default config is 1 day
	opts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: conf.GetMaxTimeRetry().AsDuration(), // same as workflow exec timeout, config is 1 day
		RetryPolicy:            buildActivityRetryPolicyByTaskConfig(conf),
	}
	ctx = workflow.WithActivityOptions(ctx, opts)

	var faceCheckResult *FaceImageSubmitCondResult
	err := workflow.ExecuteActivity(ctx, s.CheckFaceImageUploadCondActivity, workflowData).Get(ctx, &faceCheckResult)
	if err != nil {
		logger.Error("Execute activity check onboarding info for face image upload has failed", "error", err)
		return buildBaseWorkflowError(err)
	}

	var submitFcImgResult *SubmitFaceImageResult
	submitFcImgCtx := workflow.WithStartToCloseTimeout(ctx, time.Minute*2)
	err = workflow.ExecuteActivity(
		submitFcImgCtx, s.SubmitFaceImageActivity,
		workflowData).Get(submitFcImgCtx, &submitFcImgResult)
	if err != nil {
		logger.Error("Execute activity upload face image to partner has failed", "error", err)
		return buildBaseWorkflowError(err)
	}
	if submitFcImgResult.IsFCImgExpired() {
		onboardQueue := s.config.GetOnboardingStatus().GetQueueName()
		resetFaceCtx := workflow.WithTaskQueue(ctx, onboardQueue)
		if err = workflow.ExecuteActivity(
			resetFaceCtx, s.ForceUserFaceChallengeActivity,
			workflowData.ZalopayID, workflowData.OnboardingID).
			Get(resetFaceCtx, nil); err != nil {
			return err
		}
		return nil
	}
	if submitFcImgResult.IsTxnExpired() {
		onboardQueue := s.config.GetOnboardingStatus().GetQueueName()
		resetSingingCtx := workflow.WithTaskQueue(ctx, onboardQueue)
		if err = workflow.ExecuteActivity(
			resetSingingCtx, s.ForceUserContractSigningActivity,
			workflowData.ZalopayID, workflowData.OnboardingID).
			Get(resetSingingCtx, nil); err != nil {
			return err
		}
		return nil
	}

	err = workflow.ExecuteActivity(ctx, s.MarkContractCompleteActivity, workflowData).Get(ctx, nil)
	if err != nil {
		logger.Error("Execute activity mark contract complete has failed", "error", err)
		return buildBaseWorkflowError(err)
	}

	onboardQueue := s.config.GetOnboardingStatus().GetQueueName()
	obStatusCtx := workflow.WithTaskQueue(ctx, onboardQueue)
	obStatusCtx = workflow.WithRetryPolicy(obStatusCtx, temporal.RetryPolicy{})
	err = workflow.ExecuteActivity(
		obStatusCtx, s.TriggerOnboardingStatusJobActivity,
		workflowData.ZalopayID, workflowData.OnboardingID).
		Get(obStatusCtx, nil)
	if err != nil {
		logger.Error("Execute activity trigger onboarding status job has failed", "error", err)
	}

	logger.Info("Submit face image workflow has completed", "data", workflowData)

	return nil
}

func (s *schedulerService) ContractSigningWorkflow(
	ctx workflow.Context, workflowData *dto.ContractSingingTask) error {
	zalopayID := workflowData.ZalopayID
	onboardingID := workflowData.OnboardingID
	logger := workflow.GetLogger(ctx)
	logger = tLog.With(logger, "zalopay_id", zalopayID, "onboarding_id", onboardingID)

	logger.Info("Start process contract singing workflow", "data", workflowData)

	conf := s.config.GetContractSigning()
	// Default all activity has same max execution timeout and workflow exec timeout, default config is 1 day
	opts := workflow.ActivityOptions{
		StartToCloseTimeout:    time.Minute,
		ScheduleToCloseTimeout: conf.GetMaxTimeRetry().AsDuration(), // same as workflow exec timeout, config is 1 day
		RetryPolicy:            buildActivityRetryPolicyByTaskConfig(conf),
	}

	var faceCheckResult *FaceImageSubmitCondResult
	ctx = workflow.WithActivityOptions(ctx, opts)
	err := workflow.
		ExecuteActivity(ctx, s.CheckFaceImageUploadCondActivity, workflowData).
		Get(ctx, &faceCheckResult)
	if err != nil {
		logger.Error("Execute activity check onboarding info for face image upload has failed", "error", err)
		return err
	}

	var contractResult *ContractSingingResult
	ctx = workflow.WithStartToCloseTimeout(ctx, time.Minute*2)
	err = workflow.
		ExecuteActivity(ctx, s.ProcessContractSigningActivity, workflowData).
		Get(ctx, &contractResult)
	if err != nil {
		logger.Error("Execute activity upload face image to partner has failed", "error", err)
		return err
	}
	if contractResult.IsFCImgExpired() {
		queue := s.config.GetOnboardingStatus().GetQueueName()
		rfCtx := workflow.WithTaskQueue(ctx, queue)
		err = workflow.
			ExecuteActivity(rfCtx, s.ForceUserFaceChallengeActivity, zalopayID, onboardingID).
			Get(rfCtx, nil)
		if err != nil {
			return err
		}
		return nil
	}

	err = workflow.
		ExecuteActivity(ctx, s.UpdateAfterContractSignedActivity, workflowData).
		Get(ctx, nil)
	if err != nil {
		logger.Error("Execute activity update after contract signed has failed", "error", err)
		return err
	}

	queue := s.config.GetOnboardingStatus().GetQueueName()
	obCtx := workflow.WithTaskQueue(ctx, queue)
	obCtx = workflow.WithRetryPolicy(obCtx, temporal.RetryPolicy{})
	err = workflow.
		ExecuteActivity(obCtx, s.TriggerOnboardingStatusJobActivity, zalopayID, onboardingID).
		Get(obCtx, nil)
	if err != nil {
		logger.Error("Execute activity trigger onboarding status job has failed", "error", err)
	}

	logger.Info("Submit face image workflow has completed", "data", workflowData)

	return nil
}

func buildBaseWorkflowError(err error) error {
	var appErr *temporal.ApplicationError
	if !errors.As(err, &appErr) {
		return err
	}
	if appErr.NonRetryable() {
		var firstDetails, secondDetails, thirdDetails any
		_ = appErr.Details(&firstDetails, &secondDetails, &thirdDetails)
		return temporal.NewNonRetryableApplicationError(
			appErr.Message(), appErr.Type(), appErr.Unwrap(),
			firstDetails, secondDetails, thirdDetails,
		)
	}
	return err
}
