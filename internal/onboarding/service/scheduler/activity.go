package scheduler

import (
	"context"

	"github.com/spf13/cast"
	"go.temporal.io/sdk/temporal"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	maintMgr "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	ErrTypeFaceImageExpired       = "FACE_IMAGE_EXPIRED"
	ErrTypeOnboardingNotFound     = "ONBOARDING_NOT_FOUND"
	ErrTypeOnboardingCompleted    = "ONBOARDING_COMPLETED"
	ErrTypeSystemUnderMaintenance = "RESOURCE_UNDER_MAINTENANCE"
)

func (s *schedulerService) PollingOnboardingStatusActivity(
	ctx context.Context, workflowData *model.OnboardingStatusTask) (*dto.OnboardingPollingResult, error) {
	logger := s.logger.WithContext(ctx)
	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return nil, buildUnderMaintenanceError()
	}

	userOb, err := s.onboardUc.SyncOnboardingStatus(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("SyncOnboardingStatus failed: %v", err)
		return nil, temporal.NewApplicationError("sync onboarding status failed", "", err)
	}
	if !userOb.IsCompleted() {
		logger.Infow(
			"msg", "Onboarding is not completed, continue processing",
			"onboardingID", userOb.ID, "zalopayID", userOb.ZalopayID,
			"currentStep", userOb.CurrentStep, "partnerData", userOb.PartnerData,
		)
		return nil, temporal.NewApplicationError("onboarding is not completed", "", userOb.CurrentStep, userOb.PartnerData)
	}

	result := &dto.OnboardingPollingResult{
		Status:       userOb.Status,
		ZalopayID:    userOb.ZalopayID,
		AccountID:    userOb.AccountID,
		OnboardingID: userOb.ID,
		PartnerCode:  userOb.PartnerCode,
		CurrentStep:  userOb.CurrentStep,
		RejectCode:   userOb.RejectCode,
	}

	logger.Infow(
		"msg", "Onboarding has final approval result",
		"result", result, "partner_data", userOb.PartnerData,
	)

	return result, nil
}

func (s *schedulerService) TriggerOnboardingStatusJobActivity(
	ctx context.Context, zalopayID, onboardingID string) error {
	logger := s.logger.WithContext(ctx)

	if err := s.onboardUc.TriggerQueryOnboardingStatusJob(ctx,
		cast.ToInt64(zalopayID), cast.ToInt64(onboardingID)); err != nil {
		logger.Errorf("TriggerQueryOnboardingStatusJob failed: %v", err)
		return temporal.NewNonRetryableApplicationError("trigger job polling onboarding status failed", "", err)
	}
	return nil
}

// CheckFaceImageUploadCondActivity is an activity function to get and check onboarding info for face image upload
func (s *schedulerService) CheckFaceImageUploadCondActivity(
	ctx context.Context, workflowData *dto.FaceImgSubmitCheck) (*FaceImageSubmitCondResult, error) {
	logger := s.logger.WithContext(ctx)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return nil, buildUnderMaintenanceError()
	}

	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)
	userOb, err := s.onboardUc.RetrievePartnerOnboarding(ctx, zalopayID, onboardingID)
	if errorkit.IsErrorCode(err, errorkit.CodeOnboardingNotFound) {
		return nil, temporal.NewNonRetryableApplicationError("onboarding not found", ErrTypeOnboardingNotFound, err, workflowData)
	}
	if err != nil {
		logger.Errorf("RetrievePartnerOnboarding failed: %v", err)
		return nil, temporal.NewApplicationError("retrieve latest onboarding status failed", "", err)
	}
	if userOb.IsCompleted() {
		logger.Warnw(
			"msg", "Onboarding has final approval result, skip processing",
			"currentStep", userOb.CurrentStep, "partnerData", userOb.PartnerData,
		)
		return nil, temporal.NewNonRetryableApplicationError("onboarding has final approval result", ErrTypeOnboardingCompleted, err, userOb.CurrentStep, userOb.PartnerData)
	}
	if userOb.CurrentStep.CanSigningContract() && userOb.PartnerData.IsContractReady() {
		logger.Info("Onboarding data is ready for face image upload")
		return &FaceImageSubmitCondResult{Eligible: true, FaceChallengeID: userOb.UserInfo.GetFaceChallengeID()}, nil
	}

	logger.Warnw(
		"msg", "Onboarding is not ready for face image upload",
		"currentStep", userOb.CurrentStep, "partnerData", userOb.PartnerData,
	)
	return nil, temporal.NewApplicationError("onboarding is not ready for face image upload", "", userOb.CurrentStep, userOb.PartnerData)
}

// Note: This function is not used in the current codebase
// GetAndVerifyFaceImageDataActivity is an activity function to get and verify face image data
func (s *schedulerService) GetAndVerifyFaceImageDataActivity(
	ctx context.Context, workflowData *dto.FaceImgSubmitTask,
	faceChallengeID string) (*model.UserFaceChallenge, error) {
	logger := s.logger.WithContext(ctx)

	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	faceImage, err := s.faceAuthUc.GetAndVerifyFaceChallenge(ctx, zalopayID, faceChallengeID)
	if err != nil {
		logger.Errorf("GetAndVerifyFaceChallenge failed: %v", err)
		return nil, temporal.NewApplicationError("get face image data failed", "", err)
	}
	return faceImage, nil
}

// ForceUserFaceChallengeActivity is an activity function to force a user face challenge again
func (s *schedulerService) ForceUserFaceChallengeActivity(
	ctx context.Context, zalopayID string, onboardingID string) error {
	logger := s.logger.WithContext(ctx)

	if err := s.faceAuthUc.ResetUserToFaceChallenge(ctx,
		cast.ToInt64(zalopayID), cast.ToInt64(onboardingID)); err != nil {
		logger.Errorf("ResetUserToFaceChallenge failed: %v", err)
		return temporal.NewApplicationError("reset user to face challenge failed", "", err)
	}
	return nil
}

// ForceUserFaceChallengeActivity is an activity function to force a user face challenge again
func (s *schedulerService) ForceUserContractSigningActivity(
	ctx context.Context, zalopayID string, onboardingID string) error {
	logger := s.logger.WithContext(ctx)

	if err := s.faceAuthUc.ResetUserToContractSigning(ctx,
		cast.ToInt64(zalopayID), cast.ToInt64(onboardingID)); err != nil {
		logger.Errorf("ResetUserToContractSigning failed: %v", err)
		return temporal.NewApplicationError("reset user to contract otp singing failed", "", err)
	}
	return nil
}

// Deprecated: This function is not used in the current codebase
// SubmitFaceImageActivity is an activity function to submit face image to partner
func (s *schedulerService) SubmitFaceImageActivity(ctx context.Context,
	workflowData *dto.FaceImgSubmitTask) (*SubmitFaceImageResult, error) {
	logger := s.logger.WithContext(ctx)
	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return nil, buildUnderMaintenanceError()
	}

	onboarding, err := s.getAndCheckOnboardingApproval(ctx, zalopayID, onboardingID)
	if err != nil {
		return nil, err
	}

	fcImageData := onboarding.UserInfo.GetFaceChallengeImg()
	fcSubmitTime := onboarding.UserInfo.GetFaceChallengeTime()
	if s.faceAuthUc.IsFaceChallengeExpired(fcSubmitTime) {
		logger.Warnf("Face challenge image is expired, face challenge time=%v", fcSubmitTime)
		return &SubmitFaceImageResult{FaceImgExpired: true}, nil
	}
	if fcImageData.IsEmpty() {
		logger.Warnw(
			"msg", "Face challenge image is empty, skip processing",
			"fc_image_path", fcImageData.Path, "fc_image_url", fcImageData.URL,
		)
		return &SubmitFaceImageResult{FaceImgExpired: true}, nil
	}

	err = s.contractUc.SubmitFaceImageToPartner(ctx, onboarding)
	if errorkit.IsErrorCode(err, errorkit.CodeContractTransactionExpired) {
		logger.Warn("Contract transaction is expired, skip processing")
		return &SubmitFaceImageResult{TxnExpired: true}, nil
	}
	if err != nil {
		logger.Errorf("SubmitFaceImageToPartner failed: %v", err)
		return nil, temporal.NewApplicationError("submit face image to partner failed", "", err)
	}
	return &SubmitFaceImageResult{IsSuccess: true}, nil
}

// Deprecated: This function is not used in the current codebase
// MarkContractCompleteActivity is an activity function to confirm contract completion
func (s *schedulerService) MarkContractCompleteActivity(
	ctx context.Context, workflowData *dto.FaceImgSubmitTask) error {
	logger := s.logger.WithContext(ctx)
	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return buildUnderMaintenanceError()
	}

	onboarding, err := s.getAndCheckOnboardingApproval(ctx, zalopayID, onboardingID)
	if err != nil {
		return err
	}

	if err = s.contractUc.MarkContractComplete(ctx, onboarding); err == nil {
		logger.Info("Contract completion is confirmed")
		return nil
	}

	logger.Errorf("MarkContractComplete failed: %v", err)
	logger.Info("Continue to retrieve latest onboarding status to verify signed contract status")

	// In-case mark contract complete has error, retrieve the latest onboarding status to verify singed contract status
	// Because some time partner already received the confirm request but the response is not received yet or timeout
	onboarding, oErr := s.onboardUc.RetrievePartnerOnboarding(ctx,
		cast.ToInt64(zalopayID),
		cast.ToInt64(onboardingID),
	)
	if oErr != nil {
		logger.Errorf("RetrievePartnerOnboarding for verify contract sign status failed: %v", oErr)
		return temporal.NewApplicationError("retrieve latest onboarding for verify contract sign status failed", "", oErr)
	}
	if !onboarding.PartnerData.IsContractSigned() {
		logger.Warn("Contract is not signed, still return error from MarkContractComplete call")
		return temporal.NewApplicationError("confirm contract completion failed", "", err)
	}

	// Update an onboarding step to waiting approval
	err = s.contractUc.UpdateStepWhenContractSigned(ctx,
		cast.ToInt64(zalopayID),
		cast.ToInt64(onboardingID),
	)
	if err != nil {
		logger.Errorf("UpdateOnboardingStepByContractSigned failed: %v", err)
		return temporal.NewApplicationError("update onboarding step by signed contract failed", "", err)
	}
	return nil
}

func (s *schedulerService) ProcessContractSigningActivity(
	ctx context.Context, workflowData *dto.ContractSingingTask) (*ContractSingingResult, error) {
	logger := s.logger.WithContext(ctx)
	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return nil, buildUnderMaintenanceError()
	}

	onboarding, err := s.getAndCheckOnboardingApproval(ctx, zalopayID, onboardingID)
	if err != nil {
		return nil, err
	}

	onboarding, err = s.contractUc.InitContractSigning(ctx, onboarding)
	if errorkit.IsErrorCode(err, errorkit.CodeContractHasBeenSigned) {
		logger.Warn("Contract has been signed, skip processing")
		return &ContractSingingResult{IsSuccess: true}, nil
	}
	if err != nil {
		logger.Errorf("InitContractSigning failed: %v", err)
		return nil, temporal.NewApplicationError("init contract process failed", "", err)
	}

	fcImageData := onboarding.UserInfo.GetFaceChallengeImg()
	fcSubmitTime := onboarding.UserInfo.GetFaceChallengeTime()
	if s.faceAuthUc.IsFaceChallengeExpired(fcSubmitTime) {
		logger.Warnf("Face challenge image is expired, face challenge time=%v", fcSubmitTime)
		return &ContractSingingResult{FaceImgExpired: true}, nil
	}
	if fcImageData.IsEmpty() {
		logger.Warnw(
			"msg", "Face challenge image is empty, skip processing and reset to face challenge",
			"fc_image_path", fcImageData.Path, "fc_image_url", fcImageData.URL,
		)
		return &ContractSingingResult{FaceImgExpired: true}, nil
	}

	err = s.contractUc.SubmitFaceImageToPartner(ctx, onboarding)
	if errorkit.IsErrorCode(err, errorkit.CodeContractTransactionExpired) {
		logger.Warn("Contract transaction is expired, retry all process")
		return nil, temporal.NewApplicationError("contract transaction is expired", "", err)
	}
	if err != nil {
		logger.Errorf("SubmitFaceImageToPartner failed: %v", err)
		return nil, temporal.NewApplicationError("submit face image to partner failed", "", err)
	}

	err = s.contractUc.MarkContractComplete(ctx, onboarding)
	if errorkit.IsErrorCode(err, errorkit.CodeContractTransactionExpired) {
		logger.Warn("Contract transaction is expired, retry all process")
		return nil, temporal.NewApplicationError("contract transaction is expired", "", err)
	}
	if errorkit.IsErrorCode(err, errorkit.CodeContractHasBeenSigned) {
		logger.Warn("Contract has been signed, return success")
		return &ContractSingingResult{IsSuccess: true}, nil
	}
	if err != nil {
		logger.Errorf("ProcessContractSigning failed: %v", err)
		return nil, temporal.NewApplicationError("process contract signing failed", "", err)
	}

	logger.Infow("msg", "Contract signing process is completed", "onboardingID", onboardingID, "zalopayID", zalopayID)

	return &ContractSingingResult{IsSuccess: true}, nil
}

func (s *schedulerService) UpdateAfterContractSignedActivity(
	ctx context.Context, workflowData *dto.ContractSingingTask) error {
	logger := s.logger.WithContext(ctx)
	zalopayID := cast.ToInt64(workflowData.ZalopayID)
	onboardingID := cast.ToInt64(workflowData.OnboardingID)

	if s.isUnderMaintenance(ctx, partner.PartnerCIMB) {
		return buildUnderMaintenanceError()
	}

	_, err := s.getAndCheckOnboardingApproval(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("GetAndCheckOnboardingApproval failed: %v", err)
		return err
	}

	err = s.contractUc.UpdateStepWhenContractSigned(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("UpdateStepWhenContractSigned failed: %v", err)
		return temporal.NewApplicationError("update onboarding step when contract signed failed", "", err)
	}
	return nil
}

func (s *schedulerService) getAndCheckOnboardingApproval(
	ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error) {
	logger := s.logger.WithContext(ctx)

	onboarding, err := s.onboardUc.GetOnboardingInfoByID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingInfoByID failed: %v", err)
		return nil, temporal.NewApplicationError("get onboarding info by id failed", "", err)
	}
	if onboarding.IsCompleted() {
		logger.Warnw(
			"msg", "Onboarding has final approval result, skip processing",
			"currentStep", onboarding.CurrentStep, "partnerData", onboarding.PartnerData,
		)
		return nil, temporal.NewNonRetryableApplicationError("onboarding has final approval result", ErrTypeOnboardingCompleted, nil)
	}
	return onboarding, nil
}

func (s *schedulerService) isUnderMaintenance(ctx context.Context, partnerCode partner.PartnerCode) bool {
	underMaintain, err := s.maintMgr.CheckFeaturesMaintenance(ctx, partnerCode, []maintMgr.Feat{maintMgr.FeatOnboarding})
	return err == nil && underMaintain
}

func buildUnderMaintenanceError() error {
	return temporal.NewApplicationError("system is under maintenance", ErrTypeSystemUnderMaintenance)
}
