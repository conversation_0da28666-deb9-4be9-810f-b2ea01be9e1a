package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) SubmitClientApplication(ctx context.Context,
	req *v1.SubmitClientApplicationRequest,
) (*v1.SubmitClientApplicationResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := cast.ToInt64(req.GetOnboardingId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	newCtx := fromSessionToClientSessionCtx(ctx, session)
	_, err := o.profileUc.SubmitApplication(newCtx, zalopayID, onboardingID, &model.UserAdditionalInfo{
		JobTitle:             req.GetJobTitle(),
		Occupation:           req.GetOccupation(),
		LivingCity:           req.GetLivingCity(),
		Education:            req.GetEducation(),
		SourceOfFund:         req.GetSourceOfFund(),
		MonthlyIncome:        req.GetMonthlyIncome(),
		EmploymentStatus:     req.GetEmploymentStatus(),
		TempResidenceAddress: req.GetTempResidenceAddress(),
	})
	if err == nil {
		return &v1.SubmitClientApplicationResponse{ApplicationRejected: false, RequireLinkAccount: false}, nil
	}

	var errorInfo *errorkit.ErrorInfo
	var pErrorInfo *model.PartnerError
	if !errors.As(err, &errorInfo) {
		o.logger.WithContext(ctx).Errorf("Fail to submit application, error=%v", err)
		return nil, convertToTransportError(err)
	}

	if errorInfo.GetCode() == errorkit.CodeUserFraud {
		noticeData := o.buildNoticeFromFraudDetect()
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		return nil, convertToTransportError(errorInfo)
	}
	if errorInfo.GetCode() == errorkit.CodeProfileProblems {
		profileIssue := errorInfo.GetMetadataField(errorkit.OnboardProfileIssueField).(*model.UserProfileIssue)
		noticeData := o.buildNoticeFromProfileIssue(profileIssue, false)
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		errorInfo = errorInfo.WithMetadataField(errorkit.DetailCodeField, profileIssue.Code)
		return nil, convertToTransportError(errorInfo)
	}
	if errorInfo.GetCode() == errorkit.CodeCallCIMBFailed && errors.As(errorInfo.Cause, &pErrorInfo) {
		noticeData := o.buildNoticeFromPartnerErrorCode(pErrorInfo.SysCode)
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		errorInfo = errorInfo.WithMetadataField(errorkit.DetailCodeField, pErrorInfo.SysCode)
		return nil, convertToTransportError(errorInfo)
	}

	o.logger.WithContext(ctx).Errorf("Fail to submit application, err=%v", errorInfo)
	return nil, convertToTransportError(errorInfo)
}
