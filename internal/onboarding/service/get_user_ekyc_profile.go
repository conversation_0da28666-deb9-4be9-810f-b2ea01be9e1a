package service

import (
	"context"

	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) GetClientEkycProfile(ctx context.Context,
	req *v1.GetClientEkycProfileRequest) (*v1.GetClientEkycProfileResponse, error) {
	session, _ := auth.FromContext(ctx)

	params := &dto.UserKycProfileParams{
		ZalopayID:  cast.ToInt64(session.GetZalopayId()),
		NeedVerify: req.GetNeedVerifyProfile(),
	}
	ekycProfile, err := o.profileUc.GetUserEKYCProfile(ctx, params)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("Fail to get user ekyc profile, err %v", err)
		return nil, convertToTransportError(err)
	}
	return &v1.GetClientEkycProfileResponse{
		ProfileInfo:   convertToUserProfileInfo(ekycProfile.ProfileInfo),
		ProfileIssues: o.convertToUserProfileIssues(ekycProfile.ProfileIssues, ekycProfile.MissingKycNFC),
	}, nil
}
