package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/timestamppb"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) ListClientOnboarding(ctx context.Context,
	_ *v1.ListClientOnboardingRequest) (*v1.ListClientOnboardingResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	obdList, err := o.onboardUc.ListActiveOnboarding(ctx, zalopayID)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	var list []*v1.ListClientOnboardingResponse_Onboarding
	for _, obd := range obdList {
		list = append(list, &v1.ListClientOnboardingResponse_Onboarding{
			Id:          obd.ID,
			PartnerCode: obd.PartnerCode,
			Status:      obd.Status.String(),
			CurrentStep: obd.CurrentStep.String(),
			CreatedAt:   timestamppb.New(obd.CreatedAt),
			UpdatedAt:   timestamppb.New(obd.UpdatedAt),
			AllStep:     obd.GetAllStages().ToStrings(),
			NextStep:    obd.GetNextStage().UnifyInfo(),
		})
	}

	return &v1.ListClientOnboardingResponse{
		Onboardings: list,
	}, nil
}

func (o *OnboardingService) GetClientOnboarding(ctx context.Context,
	req *v1.GetClientOnboardingRequest) (*v1.GetClientOnboardingResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := req.GetOnboardingId()

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	obd, err := o.onboardUc.GetOnboardingInfoByID(ctx, zalopayID, onboardingID)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.GetClientOnboardingResponse{
		Status:               obd.Status.String(),
		PartnerCode:          obd.PartnerCode,
		CurrentStep:          obd.CurrentStep.String(),
		FullName:             obd.UserInfo.GetFullName(),
		Gender:               obd.UserInfo.GetGenderStr(),
		PhoneNumber:          obd.UserInfo.GetPhoneNumber(),
		IdNumber:             obd.UserInfo.GetIdNumber(),
		IdIssueDate:          obd.UserInfo.GetIdIssueDate(),
		IdIssuePlace:         obd.UserInfo.GetIdIssuePlace(),
		DateOfBirth:          obd.UserInfo.GetBirthday(),
		PermanentAddress:     obd.UserInfo.GetPermanentAddress(),
		TempResidenceAddress: obd.UserInfo.GetTempResidenceAddress(),
	}, nil
}

func (o *OnboardingService) QueryOnboardingStatus(ctx context.Context, req *v1.QueryOnboardingStatusRequest) (*v1.QueryOnboardingStatusResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	obd, err := o.onboardUc.GetActiveOnboardingStatus(ctx, req.GetZalopayId(), req.GetPartnerCode())
	if err != nil {
		return nil, convertToTransportError(err)
	}

	return &v1.QueryOnboardingStatusResponse{
		OnboardingId: obd.ID,
		PartnerCode:  obd.PartnerCode,
		CurrentStep:  obd.CurrentStep.String(),
		Flags: &v1.QueryOnboardingStatusResponse_Flags{
			IsUnregister: obd.IsUnregister(),
			IsOnboarding: obd.IsOnboarding(),
			IsRejected:   obd.IsRejected(),
		},
	}, nil
}

func (o *OnboardingService) ListOnboardingByUserIDs(ctx context.Context,
	req *v1.ListOnboardingByUserIDsRequest) (*v1.ListOnboardingByUserIDsResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	obdList, err := o.onboardUc.ListActiveOnboardingByUserIDs(ctx, req.GetZalopayIds())
	if err != nil {
		return nil, convertToTransportError(err)
	}

	obdResp := make([]*v1.OnboardingData, len(obdList))
	for i, obd := range obdList {
		obdResp[i] = convertToOnboardingData(obd)
	}
	return &v1.ListOnboardingByUserIDsResponse{Onboardings: obdResp}, nil
}
