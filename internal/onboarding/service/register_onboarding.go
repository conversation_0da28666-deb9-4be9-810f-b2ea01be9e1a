package service

import (
	"context"
	"errors"

	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (o *OnboardingService) RegisterClientOnboarding(ctx context.Context,
	req *v1.RegisterClientOnboardingRequest) (*v1.RegisterClientOnboardingResponse, error) {
	session, _ := auth.FromContext(ctx)
	params := &dto.OnboardingRegisterParams{
		ZalopayID:   cast.ToInt64(session.GetZalopayId()),
		PartnerCode: partner.CodeFromString(req.GetPartnerCode()),
	}

	resp, err := o.profileUc.RegisterOnboarding(ctx, params)
	if err == nil {
		o.logger.WithContext(ctx).Infof("Register onboarding successfully, onboardingID %v", resp.OnboardingID)
		return &v1.RegisterClientOnboardingResponse{OnboardingId: resp.OnboardingID}, nil
	}

	var errorInfo *errorkit.ErrorInfo
	if !errors.As(err, &errorInfo) {
		o.logger.WithContext(ctx).Errorf("Fail to register onboarding, err %v", err)
		errorInfo = errorkit.NewError(errorkit.CodeInternalError, "register onboarding failed").WithCause(err)
		errorInfo = errorInfo.WithKind(errorkit.TypeUnexpected).WithMetadataField(errorkit.NoticeField, o.buildDefaultNotice())
		return nil, convertToTransportError(err)
	}

	// Set default notice
	errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, o.buildDefaultNotice())
	if errorInfo.GetCode() == errorkit.CodeUserFraud {
		noticeData := o.buildNoticeFromFraudDetect()
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		return nil, convertToTransportError(errorInfo)
	}
	if errorInfo.GetCode() == errorkit.CodeUserNonWhitelist {
		noticeData := o.buildNoticeFromNonWhitelist()
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		return nil, convertToTransportError(errorInfo)
	}
	if errorInfo.GetCode() == errorkit.CodeProfileProblems {
		profileIssue := errorInfo.GetMetadataField(errorkit.OnboardProfileIssueField).(*model.UserProfileIssue)
		noticeData := o.buildNoticeFromProfileIssue(profileIssue, false)
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		errorInfo = errorInfo.WithMetadataField(errorkit.DetailCodeField, profileIssue.Code)
		return nil, convertToTransportError(errorInfo)
	}
	if errorInfo.GetCode() == errorkit.CodeOnboardingNotEligibleToRegister {
		permCode := errorInfo.GetMetadataField(errorkit.OnboardPermCodeField).(string)
		noticeData := o.buildNoticeFromOnboardPermCode(params.PartnerCode, permCode)
		errorInfo = errorInfo.WithMetadataField(errorkit.NoticeField, noticeData)
		errorInfo = errorInfo.WithMetadataField(errorkit.DetailCodeField, permCode)
		return nil, convertToTransportError(errorInfo)
	}

	o.logger.WithContext(ctx).Errorf("Fail to register onboarding, err %v", errorInfo)
	return nil, convertToTransportError(errorInfo)
}
