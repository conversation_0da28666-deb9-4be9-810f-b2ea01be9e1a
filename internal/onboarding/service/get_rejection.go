package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) GetClientRejection(ctx context.Context,
	req *v1.GetClientRejectionRequest) (*v1.GetClientRejectionResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	obRejection, err := o.onboardUc.GetOnboardingRejection(ctx, zalopayID, req.GetOnboardingId())
	if err != nil {
		return nil, convertToTransportError(err)
	}

	rejectNoticeRes := o.buildNoticeFromRejection(obRejection)
	rejectNoticeResp := convertToNoticeData(rejectNoticeRes)

	return &v1.GetClientRejectionResponse{
		Code:    obRejection.RejectCodeFmt,
		Content: rejectNoticeResp.Content,
		Actions: rejectNoticeResp.Actions,
	}, nil
}
