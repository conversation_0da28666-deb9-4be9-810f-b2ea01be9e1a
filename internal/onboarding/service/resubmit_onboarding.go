package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) ReinitClientOnboarding(ctx context.Context,
	req *v1.ReinitClientOnboardingRequest) (*v1.ReinitClientOnboardingResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := cast.ToInt64(req.GetOnboardingId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	err := o.profileUc.ReinitiateOnboarding(ctx, zalopayID, onboardingID)
	if err != nil {
		return nil, convertToTransportError(err)
	}
	return &v1.ReinitClientOnboardingResponse{}, nil
}
