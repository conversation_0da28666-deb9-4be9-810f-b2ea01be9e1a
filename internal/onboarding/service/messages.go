package service

import (
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func (o *OnboardingService) buildDefaultNotice() *model.NoticeInfo {
	return &model.NoticeInfo{
		Content: o.messBuilder.GetExceptionMessage(),
		Action:  []model.NoticeAction{o.ctaCrafter.GetCTARetry()},
	}
}

func (o *OnboardingService) buildNoticeFromFraudDetect() *model.NoticeInfo {
	return &model.NoticeInfo{
		Content: o.messBuilder.GetProfileHasFraudRiskMessage(),
		Action:  []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore()},
	}
}

func (o *OnboardingService) buildNoticeFromNonWhitelist() *model.NoticeInfo {
	return &model.NoticeInfo{
		Content: o.messBuilder.GetUserNonWhitelistMessage(),
		Action:  []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore()},
	}
}

func (o *OnboardingService) buildNoticeFromKycStatus(status model.KycStatus) *model.NoticeInfo {
	noticeMap := map[model.KycStatus]model.NoticeInfo{
		model.KycStatusInvalid: {
			Content: o.messBuilder.GetKycStatusInvalidMessage(),
			Action:  []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore(), o.ctaCrafter.GetCTADoKyc()},
		},
		model.KycStatusProcessing: {
			Content: o.messBuilder.GetKycStatusProcessingMessage(),
			Action:  []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore()},
		},
		model.KycStatusRejected: {
			Content: o.messBuilder.GetKycStatusRejectedMessage(),
			Action:  []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore(), o.ctaCrafter.GetCTAUpdateKyc()},
		},
	}
	noticeVal, ok := noticeMap[status]
	if !ok {
		return nil
	}
	return &noticeVal
}

func (o *OnboardingService) buildNoticeFromProfileIssue(
	profileIssue *model.UserProfileIssue,
	missingKycNfc bool) *model.NoticeInfo {
	if profileIssue == nil {
		return nil
	}

	ctaDiscoverMore := o.ctaCrafter.GetCTADiscoverMore()
	ctaDetermineKyc := o.ctaCrafter.GetCTADetermineKyc(missingKycNfc)

	result := &model.NoticeInfo{}
	switch profileIssue.ViolateCode {
	case model.ViolationAgeInvalid:
		result.Content = o.messBuilder.GetProfileIssueAgeInvalidMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, ctaDetermineKyc}
	case model.ViolationAgeNotInRange:
		minAge := profileIssue.ViolateData["min_age"].(int)
		maxAge := profileIssue.ViolateData["max_age"].(int)
		result.Content = o.messBuilder.GetProfileIssueAgeNotInRangeMessage(minAge, maxAge)
		result.Action = []model.NoticeAction{ctaDiscoverMore}
	case model.ViolationProfileInfoLack:
		result.Content = o.messBuilder.GetProfileIssueInfoLackMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, ctaDetermineKyc}
	case model.ViolationProfileIDExpired:
		result.Content = o.messBuilder.GetProfileIssueIDExpiredMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, ctaDetermineKyc}
	case model.ViolationProfileIDTypeNotAllowed:
		result.Content = o.messBuilder.GetProfileIssueIDTypeNotAllowedMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, ctaDetermineKyc}
	case model.ViolationProfileKycNfcMissing:
		result.Content = o.messBuilder.GetProfileIssueKycNfcMissingMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, o.ctaCrafter.GetCTAUpdateNfc()}
	case model.ViolationProfileKycNfcRawEmpty, model.ViolationProfileKycNfcSourceNotAllowed, model.ViolationProfileKycNfcIdEmpty:
		result.Content = o.messBuilder.GetProfileIssueNfcNeedsResetMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAResetNfc()}
	case model.ViolationProfileShouldBeUpdated:
		result.Content = o.messBuilder.GetProfileIssueShouldBeUpdatedMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, o.ctaCrafter.GetCTARegisterOnboarding()}
	case model.ViolationProfileICImageEmpty:
		result.Content = o.messBuilder.GetProfileIssueICImageEmptyMessage()
		result.Action = []model.NoticeAction{ctaDiscoverMore, ctaDetermineKyc}
	default:
		return nil
	}
	return result
}

func (o *OnboardingService) buildNoticeFromOnboardPermCode(
	partner partner.PartnerCode, permCode string) *model.NoticeInfo {
	cimbRejectCode := cimb.RejectCodeFromString(permCode)

	result := &model.NoticeInfo{}
	switch cimbRejectCode {
	case cimb.RejectCodeICNumberUsed:
		result.Content = o.messBuilder.GetOnboardingRejectICNumberUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	case cimb.RejectCodeOnboardingInfoUsed:
		result.Content = o.messBuilder.GetOnboardingRejectOnboardingInfoUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	case cimb.RejectCodeOnboardingNotAllowed:
		result.Content = o.messBuilder.GetOnboardingRejectOnboardingNotAllowedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore(o.ctaCrafter.WithPrimaryBtn())}
	case cimb.RejectCodeIdentityInfoMismatched:
		result.Content = o.messBuilder.GetOnboardingRejectIdentityInfoMismatchedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	case cimb.RejectCodeEmailOrPhoneUsed:
		result.Content = o.messBuilder.GetOnboardingRejectEmailOrPhoneUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAUpdatePhoneNumber(), o.ctaCrafter.GetCTAContactPartner(o.ctaCrafter.WithOutlinedBtn())}
	default:
		result.Content = o.messBuilder.GetExceptionMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTARetry()}
	}
	return result
}

func (o *OnboardingService) buildNoticeFromPartnerErrorCode(code string) *model.NoticeInfo {
	cimbErrCode := cimb.ErrorCode(code)

	result := &model.NoticeInfo{}
	switch cimbErrCode {
	case cimb.ErrorCodeOnboardingInfoUsed, cimb.ErrorCodeCurrIdentityNotMatch:
		result.Content = o.messBuilder.GetOnboardingRejectOnboardingInfoUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	case cimb.ErrorCodeOnboardingProcessing:
		result.Content = o.messBuilder.GetOnboardingIsProcessingMessage()
	case cimb.ErrorCodeOnboardingNotAllowed:
		result.Content = o.messBuilder.GetOnboardingRejectOnboardingNotAllowedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTADiscoverMore()}
	case cimb.ErrorCodePhoneNumberUsed:
		result.Content = o.messBuilder.GetOnboardingRejectPhoneUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAUpdatePhoneNumber(), o.ctaCrafter.GetCTAContactPartner(o.ctaCrafter.WithOutlinedBtn())}
	case cimb.ErrorCodeICNumberUsed:
		result.Content = o.messBuilder.GetOnboardingRejectICNumberUsedMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	case cimb.ErrorCodeCustomerExisting:
		result.Content = o.messBuilder.GetOnboardingLinkingAccountMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTALinkAccount()}
	case cimb.ErrorCodeNotAllowUpdate:
		result.Content = o.messBuilder.GetOnboardingNotAllowUpdateMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAContactPartner()}
	default:
		result.Content = o.messBuilder.GetExceptionMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTARetry()}
	}
	return result
}

func (o *OnboardingService) buildNoticeFromRejection(rejection *dto.OnboardingRejectionResult) *model.NoticeInfo {
	result := &model.NoticeInfo{}
	switch cimb.RejectCodeFromString(rejection.RejectCodeRaw) {
	case cimb.RejectCodeR01AgeNotInRange:
		minAge := rejection.MinAgeAllowed
		maxAge := rejection.MaxAgeAllowed
		result.Content = o.messBuilder.GetOnboardingRejectAgeNotInRangeMessage(minAge, maxAge)
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTACloseNotice()}
	case cimb.RejectCodeR27ExistingCustomer:
		result.Content = o.messBuilder.GetOnboardingRejectCustomerExistMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAUpdatePhoneNumber()}
	case
		cimb.RejectCodeR02CustomerCancelled, cimb.RejectCodeR23CustomerRefused,
		cimb.RejectCodeR28CustomerNotApplyPrd, cimb.RejectCodeC5AgentRequest:
		result.Content = o.messBuilder.GetOnboardingRejectCancelledMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAReRegisterOnboarding()}
	case cimb.RejectCodeR26InvalidMobileNumber, cimb.RejectCodeR29CustomerNotOwnPhone:
		result.Content = o.messBuilder.GetOnboardingRejectPhoneInvalidMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAUpdatePhoneNumber(), o.ctaCrafter.GetCTAContactPartner(o.ctaCrafter.WithOutlinedBtn())}
	case
		cimb.RejectCodeR05ICDamagedOrFaded, cimb.RejectCodeR06ICAndSelfieInvalid,
		cimb.RejectCodeR07ICExpired, cimb.RejectCodeR08ICLivenessFail,
		cimb.RejectCodeR09ICCutCorner, cimb.RejectCodeR10ICBlur,
		cimb.RejectCodeR12ICTampering, cimb.RejectCodeR13Uncontactable,
		cimb.RejectCodeR14WrongICNumber, cimb.RejectCodeR15WrongName,
		cimb.RejectCodeR16WrongIC, cimb.RejectCodeR17WrongDOB,
		cimb.RejectCodeR18WrongDOI, cimb.RejectCodeR19WrongCurrentAddr,
		cimb.RejectCodeR20WrongCertifyPlace, cimb.RejectCodeR24PartialID,
		cimb.RejectCodeR25LackFrontOrBackIC, cimb.RejectCodeR30CustomerNotConfirm,
		cimb.RejectCodeR34IDAndNFCNotMatch, cimb.RejectCodeR35IDAndSelfieNotMatch,
		cimb.RejectCodeR36IDPhotosIsCovered, cimb.RejectCodeR37IDPhotosInvalid,
		cimb.RejectCodeR38CustomerHasNewID, cimb.RejectCodeR39WrongPhotosOrder,
		cimb.RejectCodeC10InvalidIdentity, cimb.RejectCodeC12InvalidDocument,
		cimb.RejectCodeC13BlurIdentityCard:
		result.Content = o.messBuilder.GetOnboardingRejectIdentityInvalidMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAReRegisterOnboarding()}
	case
		cimb.RejectCodeR11ICFaceNotMatch, cimb.RejectCodeR21SelfieLivenessFail,
		cimb.RejectCodeR22SelfieInvalid, cimb.RejectCodeR40SelfieIsBlurry,
		cimb.RejectCodeR41SelfieIsCovered, cimb.RejectCodeR42SelfieIsDummy,
		cimb.RejectCodeR43SelfieEyeClosed, cimb.RejectCodeR44SelfieEyeNotDirect,
		cimb.RejectCodeR45SelfieIsGlared, cimb.RejectCodeR46SelfieLowLight,
		cimb.RejectCodeR47SelfieHasMultiFace, cimb.RejectCodeR48SelfieNotLiveness,
		cimb.RejectCodeR49SelfieNotFullFace, cimb.RejectCodeR50SelfieTakenByOther,
		cimb.RejectCodeR51SelfieWearingGlasses, cimb.RejectCodeR52SelfieWearingMask,
		cimb.RejectCodeC9InvalidSelfie:
		result.Content = o.messBuilder.GetOnboardingRejectSelfieInvalidMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAReRegisterOnboarding()}
	case
		cimb.RejectCodeR03HitWatchList, cimb.RejectCodeR04HighRisk,
		cimb.RejectCodeR31AbnormalBehavior, cimb.RejectCodeR32AbnormalIDData,
		cimb.RejectCodeR33OtherFraudReason, cimb.RejectCodeR54CustomerNFCInvalid,
		cimb.RejectCodeC0Other:
		result.Content = o.messBuilder.GetOnboardingRejectNotMatchRuleMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTACloseNotice()}
	case cimb.RejectCodeC1DuplicateApp:
		result.Content = o.messBuilder.GetOnboardingRejectDuplicationAppMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTACloseNotice()}
	case
		cimb.RejectCodeC3InsufficientDoc, cimb.RejectCodeC4InCompleteVerify,
		cimb.RejectCodeC6OverPermitted:
		result.Content = o.messBuilder.GetOnboardingRejectVerificationStuckMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAReRegisterOnboarding()}
	case cimb.RejectCodeR53SystemHasError:
		result.Content = o.messBuilder.GetOnboardingSystemHasErrorMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTAReRegisterOnboarding()}
	default:
		result.Content = o.messBuilder.GetExceptionMessage()
		result.Action = []model.NoticeAction{o.ctaCrafter.GetCTACloseNotice()}
	}
	return result
}
