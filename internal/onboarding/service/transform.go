package service

import (
	"context"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkts"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

func fromSessionToClientSessionCtx(ctx context.Context, session *sessionv1.Session) context.Context {
	result := &model.ClientSession{
		DeviceID:  session.GetMeta().GetDeviceId(),
		RequestIP: session.GetMeta().GetUserIp(),
	}
	return utils.AttachClientSessionToCtx(ctx, result)
}

func convertToOnboardingResource(resource model.Resource) *v1.CIMBResource {
	data := make([]*v1.CIMBResourceData, 0)
	for _, d := range resource.Data {
		data = append(data, &v1.CIMBResourceData{
			Code:       d.Code,
			Vietnamese: d.Vietnamese,
			English:    d.English,
		})
	}
	return &v1.CIMBResource{
		Type: resource.Type,
		Data: data,
	}
}

func ucBindInfoToBindInfo(data *model.BindingInfo) *v1.BindInfo {
	if data == nil {
		return nil
	}
	if data.Status != model.BindStatusBound {
		return nil
	}
	return &v1.BindInfo{
		OnboardingId: data.OnboardingID,
		AccountId:    data.AccountID,
		PartnerCode:  data.PartnerCode.String(),
	}
}

func ucBindStatusToBindStatus(status model.BindingStatus) v1.BindStatus {
	var mapping = map[model.BindingStatus]v1.BindStatus{
		model.BindStatusBound:      v1.BindStatus_BIND_STATUS_BOUND,
		model.BindStatusOnboarding: v1.BindStatus_BIND_STATUS_ONBOARDING,
		model.BindStatusUnbound:    v1.BindStatus_BIND_STATUS_UNBOUND,
		model.BindStatusUnknown:    v1.BindStatus_BIND_STATUS_UNKNOWN,
	}
	value, found := mapping[status]
	if !found {
		return v1.BindStatus_BIND_STATUS_UNKNOWN
	}
	return value
}

func ucKycProgressToKycStatus(status model.KycStatus, notice *model.NoticeInfo) *v1.KycStatusData {
	return &v1.KycStatusData{
		Status: ucKycStatusToKycStatusCode(status),
		Notice: convertToNoticeData(notice),
	}
}

func ucKycStatusToKycStatusCode(status model.KycStatus) v1.KycStatus {
	var ekycStatus = v1.KycStatus_KYC_STATUS_UNKNOWN
	var statusMapping = map[model.KycStatus]v1.KycStatus{
		model.KycStatusInvalid:    v1.KycStatus_KYC_STATUS_INVALID,
		model.KycStatusByPass:     v1.KycStatus_KYC_STATUS_BYPASS,
		model.KycStatusProcessing: v1.KycStatus_KYC_STATUS_PROCESSING,
		model.KycStatusApproved:   v1.KycStatus_KYC_STATUS_APPROVE,
		model.KycStatusRejected:   v1.KycStatus_KYC_STATUS_REJECT,
	}

	value, found := statusMapping[status]
	if found {
		ekycStatus = value
	}
	return ekycStatus
}

func convertToNoticeData(notice *model.NoticeInfo) *v1.Notice {
	if notice == nil {
		return nil
	}

	actions := make([]*v1.Action, len(notice.Action))
	for index, action := range notice.Action {
		actions[index] = &v1.Action{
			Code:    action.Code.String(),
			Title:   action.Title.String(),
			Variant: action.Variant.String(),
		}
	}

	return &v1.Notice{
		Content: &v1.Content{
			Title:   notice.Content.Title,
			Message: notice.Content.Message,
		},
		Actions: actions,
	}
}

func convertToUserProfileInfo(profile *model.UserProfile) *v1.UserProfile {
	if profile == nil {
		return nil
	}

	return &v1.UserProfile{
		KycLevel:         profile.KycLevel,
		ProfileLevel:     profile.ProfileLevel,
		FullName:         profile.FullName,
		Gender:           v1.Gender(profile.Gender),
		Email:            profile.Email,
		DateOfBirth:      profile.Birthday,
		PhoneNumber:      profile.PhoneNumber,
		PermanentAddress: profile.PermanentAddress,
		IdType:           int32(profile.IdType),
		IdNumber:         profile.IdNumber,
		IdIssueDate:      profile.IdIssueDate,
		IdIssuePlace:     profile.IdIssuePlace,
	}
}

func (o *OnboardingService) convertToUserProfileIssues(issues []*model.UserProfileIssue, nfcMissing bool) []*v1.ProfileIssue {
	if issues == nil {
		return nil
	}

	profileIssues := make([]*v1.ProfileIssue, len(issues))
	for index, issue := range issues {
		issueNotice := o.buildNoticeFromProfileIssue(issue, nfcMissing)
		noticeData := convertToNoticeData(issueNotice)
		profileIssues[index] = &v1.ProfileIssue{
			Code:   issue.Code,
			Notice: noticeData,
		}
	}
	return profileIssues
}

func convertToOnboardingData(data *model.Onboarding) *v1.OnboardingData {
	if data == nil {
		return nil
	}

	return &v1.OnboardingData{
		Id:          data.ID,
		ZalopayId:   data.ZalopayID,
		Status:      data.Status.String(),
		PartnerCode: data.PartnerCode,
		CurrentStep: data.CurrentStep.String(),
		RejectCode:  data.RejectCode,
		ProfileInfo: convertToOnboardingProfile(data.UserInfo),
		PartnerData: convertToPartnerOnboarding(data.PartnerCode, data.PartnerData),
		StatusFlags: convertToStatusFlags(data),
	}
}

func convertToOnboardingProfile(info model.UserOnboardingInfo) *v1.OnboardingProfile {
	return &v1.OnboardingProfile{
		FullName:             info.GetFullName(),
		Gender:               info.GetGenderStr(),
		PhoneNumber:          info.GetPhoneNumber(),
		IdNumber:             info.GetIdNumber(),
		IdIssueDate:          info.GetIdIssueDate(),
		IdIssuePlace:         info.GetIdIssuePlace(),
		DateOfBirth:          info.GetBirthday(),
		PermanentAddress:     info.GetPermanentAddress(),
		TempResidenceAddress: info.GetTempResidenceAddress(),
	}
}

func convertToPartnerOnboarding(partnerCode string, data model.PartnerOnboardingData) *v1.PartnerOnboarding {
	if data == nil {
		return nil
	}

	partnerData := &v1.PartnerOnboarding{}
	switch partner.PartnerCode(partnerCode) {
	case partner.PartnerCIMB:
		partnerData.CimbData = convertToCIMBOnboardingData(data)
	default:
		return nil
	}
	return partnerData
}

func convertToCIMBOnboardingData(data model.PartnerOnboardingData) *v1.CIMBOnboarding {
	if data == nil {
		return nil
	}
	cimbData, ok := cimb.AsPartnerOnboardingData(data)
	if !ok {
		return nil
	}
	return &v1.CIMBOnboarding{
		OdStatus:             cimbData.ODStatus.String(),
		CasaStatus:           cimbData.CasaStatus.String(),
		SignStatus:           cimbData.SigningStatus.String(),
		ErrorDetail:          cimbData.ErrorDetail,
		ManualApprovalReason: strings.Join(cimbData.ManualApprovalReasons, ","),
	}
}

func convertToStatusFlags(data *model.Onboarding) *v1.OnboardingFlags {
	return &v1.OnboardingFlags{
		IsUnregister: data.IsUnregister(),
		IsOnboarding: data.IsOnboarding(),
		IsRejected:   data.IsRejected(),
	}
}

func convertToTransportError(err error) *errors.Error {
	var errApp *errors.Error
	var errInfo *errorkit.ErrorInfo
	if errors.As(err, &errApp) {
		return errApp
	}
	if errors.As(err, &errInfo) {
		return convertErrorInfoToTransportError(errInfo)
	}
	return errors.InternalServer("UNKNOWN_ERROR", err.Error())
}

func convertErrorInfoToTransportError(err *errorkit.ErrorInfo) *errors.Error {
	var errorCodeMapping = map[errorkit.Kind]errorkts.ErrorFunc{
		errorkit.TypeUnexpected:   errors.InternalServer,
		errorkit.TypeBadRequest:   errors.BadRequest,
		errorkit.TypeInvalidArg:   errors.BadRequest,
		errorkit.TypeNotFound:     errors.NotFound,
		errorkit.TypeConversion:   errors.InternalServer,
		errorkit.TypeRemoteCall:   errors.InternalServer,
		errorkit.TypeRepository:   errors.InternalServer,
		errorkit.TypeConflict:     errors.Conflict,
		errorkit.TypeUnauthorized: errors.Unauthorized,
		errorkit.TypeForbidden:    errors.Forbidden,
		errorkit.TypeValidation:   errorkts.UnprocessableEntity,
		errorkit.TypePrecondition: errorkts.PreconditionsFailed,
	}
	utilFunc, found := errorCodeMapping[err.Kind]
	if !found {
		utilFunc = errors.InternalServer
	}

	metadata := cast.ToStringMapString(err.Metadata)
	return utilFunc(err.Code.String(), err.Error()).WithMetadata(metadata)
}
