package service

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service/scheduler"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	NewOperationService,
	NewOnboardingService,
	scheduler.NewWorkflowService)

type OnboardingService struct {
	v1.UnimplementedOnboardingServer
	logger      *log.Helper
	profileUc   *usecase.ProfileUsecase
	faceAuthUc  *usecase.FaceAuthUsecase
	contractUc  *usecase.ContractUsecase
	onboardUc   *usecase.OnboardingUsecase
	ctaCrafter  utils.CTACrafter
	messBuilder utils.MessageBuilder
}

// NewOnboardingService new an onboarding service.
func NewOnboardingService(
	profileUc *usecase.ProfileUsecase,
	faceAuthUc *usecase.FaceAuthUsecase,
	contractUc *usecase.ContractUsecase,
	onboardUc *usecase.OnboardingUsecase,
	ctaCrafter utils.CTACrafter,
	messBuilder utils.MessageBuilder,
	kLogger log.Logger) *OnboardingService {
	logging := log.With(kLogger, "service", "onboarding")
	return &OnboardingService{
		onboardUc:   onboardUc,
		profileUc:   profileUc,
		faceAuthUc:  faceAuthUc,
		contractUc:  contractUc,
		ctaCrafter:  ctaCrafter,
		messBuilder: messBuilder,
		logger:      log.NewHelper(logging),
	}
}
