package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (o *OnboardingService) LinkingClientAccount(ctx context.Context,
	req *v1.LinkingClientAccountRequest) (*v1.LinkingClientAccountResponse, error) {
	session, _ := auth.FromContext(ctx)
	zaloPayID := cast.ToInt64(session.GetZalopayId())
	onboardingID := req.GetOnboardingId()

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	err := o.profileUc.LinkingAccount(ctx, zaloPayID, onboardingID)
	if err != nil {
		o.logger.WithContext(ctx).Errorf("linking client account failed: %v", err)
		return nil, convertToTransportError(err)
	}
	return &v1.LinkingClientAccountResponse{}, nil
}
