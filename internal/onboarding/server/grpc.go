package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"go.opentelemetry.io/otel"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	maintMgr "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	imetrics "gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/maintenance"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(
	c *config.Onboarding, logger log.Logger,
	operatorService *service.OperationService,
	onboardingService *service.OnboardingService,
	maintManager maintMgr.Handler) *grpc.Server {
	codec := codec.NewRequestLogCodec(getSensitiveCensorship())
	metric := imetrics.NewOtelRequestMetrics(imetrics.Options{
		Namespace: imetrics.MetricOnboardingMeter,
		Module:    imetrics.MetricModuleGrpcServer,
	})

	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			metrics.Server(
				metrics.WithRequests(metric.HandledCounter),
				metrics.WithSeconds(metric.HandledHistogram),
			),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			logging.Server(logger, codec),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().GetGrpc().GetNetwork() != "" {
		opts = append(opts, grpc.Network(c.GetServer().GetGrpc().GetNetwork()))
	}
	if c.GetServer().GetGrpc().GetAddr() != "" {
		opts = append(opts, grpc.Address(c.GetServer().GetGrpc().GetAddr()))
	}
	if c.GetServer().GetGrpc().GetTimeout() != nil {
		opts = append(opts, grpc.Timeout(c.GetServer().GetGrpc().GetTimeout().AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterOperationServer(srv, operatorService)
	v1.RegisterOnboardingServer(srv, onboardingService)
	return srv
}
