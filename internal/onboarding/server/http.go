package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.opentelemetry.io/otel"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	maintMgr "gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
	imetrics "gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/logging"
	maintMw "gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/maintenance"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	c *config.Onboarding, logger log.Logger,
	onboardingService *service.OnboardingService,
	authenticator auth.Authenticator,
	maintManager maintMgr.Handler) *http.Server {
	codec := codec.NewRequestLogCodec(getSensitiveCensorship())
	metric := imetrics.NewOtelRequestMetrics(imetrics.Options{
		Namespace: imetrics.MetricOnboardingMeter,
		Module:    imetrics.MetricModuleHttpServer,
	})

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			metrics.Server(
				metrics.WithRequests(metric.HandledCounter),
				metrics.WithSeconds(metric.HandledHistogram),
			),
			tracing.Server(
				tracing.WithTracerProvider(otel.GetTracerProvider()),
				tracing.WithPropagator(otel.GetTextMapPropagator()),
				tracing.WithTracerName(c.GetApp().GetName()),
			),
			logging.Server(logger, codec),
			auth.Server(authenticator),
			maintMw.Server(maintManager, MaintenanceElements),
		),
	}
	if c.GetServer().GetHttp().GetNetwork() != "" {
		opts = append(opts, http.Network(c.GetServer().GetHttp().GetNetwork()))
	}
	if c.GetServer().GetHttp().GetAddr() != "" {
		opts = append(opts, http.Address(c.GetServer().GetHttp().GetAddr()))
	}
	if c.GetServer().GetHttp().GetTimeout() != nil {
		opts = append(opts, http.Timeout(c.GetServer().GetHttp().GetTimeout().AsDuration()))
	}
	srv := http.NewServer(opts...)
	srv.Handle("/metrics", promhttp.Handler())
	v1.RegisterOnboardingHTTPServer(srv, onboardingService)
	return srv
}

type WorkerHTTPServer struct {
	*http.Server
}

func NewWorkerHTTPServer(c *config.Onboarding, logger log.Logger) *WorkerHTTPServer {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			logging.Server(logger, nil),
		),
	}
	if c.GetServer().GetHttp().GetNetwork() != "" {
		opts = append(opts, http.Network(c.GetServer().GetHttp().GetNetwork()))
	}
	if c.GetServer().GetHttp().GetAddr() != "" {
		opts = append(opts, http.Address(c.GetServer().GetHttp().GetAddr()))
	}
	if c.GetServer().GetHttp().GetTimeout() != nil {
		opts = append(opts, http.Timeout(c.GetServer().GetHttp().GetTimeout().AsDuration()))
	}
	srv := http.NewServer(opts...)
	srv.Handle("/metrics", promhttp.Handler())
	return &WorkerHTTPServer{Server: srv}
}
