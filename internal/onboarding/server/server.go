package server

import (
	"github.com/google/wire"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/onboarding_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer, NewTemporalWorker, NewWorkerHTTPServer)

var MaintenanceElements = []maintenance.Element{
	{
		Operation: v1.Onboarding_GetClientPermissions_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatAll},
	},
	{
		Operation: v1.Onboarding_RegisterClientOnboarding_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_SubmitClientApplication_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_SubmitClientWetSign_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_SubmitClientFaceChallenge_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_RequestClientOTP_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_VerifyClientOTP_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
	{
		Operation: v1.Onboarding_ReinitClientOnboarding_FullMethodName,
		Features:  []maintenance.Feat{maintenance.FeatOnboarding},
	},
}

func getSensitiveCensorship() (sensitiveMethods, sensitiveFields []string) {
	sensitiveMethods = []string{
		v1.Onboarding_GetClientEkycProfile_FullMethodName,
		v1.Onboarding_SubmitClientApplication_FullMethodName,
		v1.Onboarding_GetClientOnboarding_FullMethodName,
		v1.Onboarding_GetClientPermissions_FullMethodName,
		v1.Onboarding_ListOnboardingByUserIDs_FullMethodName,
	}
	sensitiveFields = []string{
		"email",
		"full_name",
		"monthly_income",
		"phone_number",
		"id_number",
		"id_issue_date",
		"id_issue_place",
		"date_of_birth",
		"permanent_address",
		"temp_residence_address",
		"profile_info.full_name",
		"profile_info.phone_number",
		"profile_info.id_number",
		"profile_info.id_issue_date",
		"profile_info.id_issue_place",
		"profile_info.date_of_birth",
		"profile_info.permanent_address",
		"profile_info.temp_residence_address",
	}
	return
}
