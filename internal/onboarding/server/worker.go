package server

import (
	"context"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	tempCli "go.temporal.io/sdk/client"
	tempWorker "go.temporal.io/sdk/worker"
	"go.temporal.io/sdk/workflow"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service/scheduler"
)

type TemporalWorker struct {
	transport.Server
	logger       *log.Helper
	onceExec     *sync.Once
	temporal     tempCli.Client
	workerList   []*TemporalWorkerData
	schedulerSvc scheduler.SchedulerService
}

type TemporalWorkerData struct {
	worker  tempWorker.Worker
	wfNames []string
}

func NewTemporalWorker(
	conf *config.Onboarding, kLogger log.Logger,
	schedulerSvc scheduler.SchedulerService,
	temporal tempCli.Client) *TemporalWorker {
	result := &TemporalWorker{
		temporal:     temporal,
		onceExec:     &sync.Once{},
		schedulerSvc: schedulerSvc,
	}

	// init logger
	logging := log.With(kLogger, "module", "temporal-worker")
	result.logger = log.NewHelper(logging)

	// init worker
	onboardStatusWorker := result.RegisterOnboardingStatusWorkflow(conf.GetSchedulers().GetOnboardingStatus())
	faceImgSubmitWorker := result.RegisterContractSigningWorkflows(
		conf.GetSchedulers().GetSubmitFaceImage(),
		conf.GetSchedulers().GetContractSigning(),
	)
	result.workerList = append([]*TemporalWorkerData{}, onboardStatusWorker, faceImgSubmitWorker)
	return result
}

func (t TemporalWorker) RegisterOnboardingStatusWorkflow(taskConfig *config.TemporalTask) *TemporalWorkerData {
	queueName := taskConfig.GetQueueName()
	workflowType := taskConfig.GetWorkflowType()
	workerRun := tempWorker.New(t.temporal, queueName, tempWorker.Options{})

	// Register Workflow
	options := workflow.RegisterOptions{Name: workflowType}
	workerRun.RegisterWorkflowWithOptions(t.schedulerSvc.PollingOnboardingStatusWorkflow, options)

	// Register list activity
	workerRun.RegisterActivity(t.schedulerSvc.PollingOnboardingStatusActivity)
	workerRun.RegisterActivity(t.schedulerSvc.ForceUserFaceChallengeActivity)
	workerRun.RegisterActivity(t.schedulerSvc.ForceUserContractSigningActivity)
	workerRun.RegisterActivity(t.schedulerSvc.TriggerOnboardingStatusJobActivity)

	return &TemporalWorkerData{
		worker:  workerRun,
		wfNames: []string{workflowType},
	}
}

func (t TemporalWorker) RegisterContractSigningWorkflows(
	submitFaceTaskConfig *config.TemporalTask,
	signContractTaskConfig *config.TemporalTask) *TemporalWorkerData {
	queueName := signContractTaskConfig.GetQueueName()
	workerRun := tempWorker.New(t.temporal, queueName, tempWorker.Options{})

	// Register Workflow
	submitFaceWorkflow := submitFaceTaskConfig.GetWorkflowType()
	signContractWorkflow := signContractTaskConfig.GetWorkflowType()
	workerRun.RegisterWorkflowWithOptions(t.schedulerSvc.SubmitFaceImageWorkflow, workflow.RegisterOptions{Name: submitFaceWorkflow})
	workerRun.RegisterWorkflowWithOptions(t.schedulerSvc.ContractSigningWorkflow, workflow.RegisterOptions{Name: signContractWorkflow})

	// Register list activity
	workerRun.RegisterActivity(t.schedulerSvc.SubmitFaceImageActivity)
	workerRun.RegisterActivity(t.schedulerSvc.MarkContractCompleteActivity)
	workerRun.RegisterActivity(t.schedulerSvc.ProcessContractSigningActivity)
	workerRun.RegisterActivity(t.schedulerSvc.CheckFaceImageUploadCondActivity)
	workerRun.RegisterActivity(t.schedulerSvc.GetAndVerifyFaceImageDataActivity)
	workerRun.RegisterActivity(t.schedulerSvc.UpdateAfterContractSignedActivity)

	return &TemporalWorkerData{
		worker:  workerRun,
		wfNames: []string{submitFaceWorkflow, signContractWorkflow},
	}
}

func (t TemporalWorker) Start(ctx context.Context) error {
	var wg sync.WaitGroup

	handleStart := func(worker *TemporalWorkerData) {
		defer wg.Done()
		wfNames := strings.Join(worker.wfNames, ",")
		runErr := worker.worker.Run(tempWorker.InterruptCh())
		if runErr == nil {
			return
		}
		t.logger.Errorf("failed to start worker for workflow: %s, error: %v", wfNames, runErr)
	}

	for _, worker := range t.workerList {
		wg.Add(1)
		cloned := worker
		go handleStart(cloned)
	}

	wg.Wait()
	return nil
}

func (t TemporalWorker) Stop(ctx context.Context) error {
	handle := func() {
		for _, w := range t.workerList {
			w.worker.Stop()
		}
	}

	t.onceExec.Do(handle)
	return nil
}
