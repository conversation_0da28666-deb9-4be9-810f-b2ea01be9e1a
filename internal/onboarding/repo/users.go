package repo

import (
	"context"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type usersRepo struct {
	repos          *Repository
	logger         *log.Helper
	userAttrsCache map[string]model.Resource
	userAttrsMutex *sync.RWMutex
}

// NewUsersRepo .
func NewUsersRepo(repos *Repository, logger log.Logger) _interface.UsersRepo {
	return &usersRepo{
		repos:          repos,
		logger:         log.NewHelper(log.With(logger, "module", "users-repo")),
		userAttrsCache: make(map[string]model.Resource),
		userAttrsMutex: &sync.RWMutex{},
	}
}

func (u usersRepo) StoreUserDemographics(ctx context.Context,
	partner partner.PartnerCode, resources []model.Resource) error {
	defer u.repos.metrics.MonitoredTime(usersRepoName)("StoreUserDemographics")

	// Store user demographics
	u.userAttrsMutex.Lock()
	defer u.userAttrsMutex.Unlock()
	for _, r := range resources {
		u.userAttrsCache[r.Type] = r
	}
	u.logger.WithContext(ctx).Infof("Store user demographics for partner %s", partner)
	return nil
}

func (u usersRepo) GetUserDemographics(ctx context.Context, partner partner.PartnerCode) ([]model.Resource, error) {
	defer u.repos.metrics.MonitoredTime(usersRepoName)("GetUserDemographics")

	u.userAttrsMutex.RLock()
	defer u.userAttrsMutex.RUnlock()
	var resources []model.Resource
	for _, r := range u.userAttrsCache {
		resources = append(resources, r)
	}
	if len(resources) == 0 {
		return nil, errors.Errorf("no user demographics found for partner %s", partner)
	}
	u.logger.WithContext(ctx).Infof("Get user demographics for partner %s", partner)
	return resources, nil
}
