// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/entity"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

type BindingsStatus string

const (
	BindingsStatusActive   BindingsStatus = "active"
	BindingsStatusInactive BindingsStatus = "inactive"
)

func (e *BindingsStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = BindingsStatus(s)
	case string:
		*e = BindingsStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for BindingsStatus: %T", src)
	}
	return nil
}

type NullBindingsStatus struct {
	BindingsStatus BindingsStatus `json:"bindings_status"`
	Valid          bool           `json:"valid"` // Valid is true if BindingsStatus is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullBindingsStatus) Scan(value interface{}) error {
	if value == nil {
		ns.BindingsStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.BindingsStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullBindingsStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.BindingsStatus), nil
}

type PartnersStatus string

const (
	PartnersStatusEnabled  PartnersStatus = "enabled"
	PartnersStatusDisabled PartnersStatus = "disabled"
)

func (e *PartnersStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = PartnersStatus(s)
	case string:
		*e = PartnersStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for PartnersStatus: %T", src)
	}
	return nil
}

type NullPartnersStatus struct {
	PartnersStatus PartnersStatus `json:"partners_status"`
	Valid          bool           `json:"valid"` // Valid is true if PartnersStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullPartnersStatus) Scan(value interface{}) error {
	if value == nil {
		ns.PartnersStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.PartnersStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullPartnersStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.PartnersStatus), nil
}

type Bindings struct {
	ID                   int64           `json:"id"`
	ZalopayID            int64           `json:"zalopay_id"`
	AccountID            sql.NullInt64   `json:"account_id"`
	PartnerCode          string          `json:"partner_code"`
	FullName             string          `json:"full_name"`
	PhoneNumber          string          `json:"phone_number"`
	IDNumber             string          `json:"id_number"`
	IDIssuedDate         sql.NullString  `json:"id_issued_date"`
	IDIssuedLocation     sql.NullString  `json:"id_issued_location"`
	IcImages             entity.ICImages `json:"ic_images"`
	PermanentAddress     sql.NullString  `json:"permanent_address"`
	TempResidenceAddress sql.NullString  `json:"temp_residence_address"`
	Birthday             sql.NullString  `json:"birthday"`
	Gender               sql.NullString  `json:"gender"`
	Status               BindingsStatus  `json:"status"`
	CurrentStep          string          `json:"current_step"`
	RejectCode           string          `json:"reject_code"`
	ExtraProfile         json.RawMessage `json:"extra_profile"`
	PartnerData          json.RawMessage `json:"partner_data"`
	CreatedAt            time.Time       `json:"created_at"`
	UpdatedAt            time.Time       `json:"updated_at"`
}

type Partners struct {
	// Partner id in installment system
	ID int64 `json:"id"`
	// Partner name
	Name string `json:"name"`
	// Partner code
	Code      partner.PartnerCode `json:"code"`
	Status    NullPartnersStatus  `json:"status"`
	CreatedAt time.Time           `json:"created_at"`
	UpdatedAt time.Time           `json:"updated_at"`
}
