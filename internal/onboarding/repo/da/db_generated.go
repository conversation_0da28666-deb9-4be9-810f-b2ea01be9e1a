// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.createOnboardingStmt, err = db.PrepareContext(ctx, createOnboarding); err != nil {
		return nil, fmt.Errorf("error preparing query CreateOnboarding: %w", err)
	}
	if q.getActiveOnboardingStmt, err = db.PrepareContext(ctx, getActiveOnboarding); err != nil {
		return nil, fmt.Errorf("error preparing query GetActiveOnboarding: %w", err)
	}
	if q.getOnboardingByIDAndZalopayIDStmt, err = db.PrepareContext(ctx, getOnboardingByIDAndZalopayID); err != nil {
		return nil, fmt.Errorf("error preparing query GetOnboardingByIDAndZalopayID: %w", err)
	}
	if q.getOnboardingByIDAndZalopayIDForUpdateStmt, err = db.PrepareContext(ctx, getOnboardingByIDAndZalopayIDForUpdate); err != nil {
		return nil, fmt.Errorf("error preparing query GetOnboardingByIDAndZalopayIDForUpdate: %w", err)
	}
	if q.listActiveOnboardingStmt, err = db.PrepareContext(ctx, listActiveOnboarding); err != nil {
		return nil, fmt.Errorf("error preparing query ListActiveOnboarding: %w", err)
	}
	if q.listActiveOnboardingByZalopayIDsStmt, err = db.PrepareContext(ctx, listActiveOnboardingByZalopayIDs); err != nil {
		return nil, fmt.Errorf("error preparing query ListActiveOnboardingByZalopayIDs: %w", err)
	}
	if q.listOnboardingByZalopayIDStmt, err = db.PrepareContext(ctx, listOnboardingByZalopayID); err != nil {
		return nil, fmt.Errorf("error preparing query ListOnboardingByZalopayID: %w", err)
	}
	if q.updateAccountIDStmt, err = db.PrepareContext(ctx, updateAccountID); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateAccountID: %w", err)
	}
	if q.updateApprovalDataStmt, err = db.PrepareContext(ctx, updateApprovalData); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateApprovalData: %w", err)
	}
	if q.updateBasicOnboardingStmt, err = db.PrepareContext(ctx, updateBasicOnboarding); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateBasicOnboarding: %w", err)
	}
	if q.updateExtraProfileStmt, err = db.PrepareContext(ctx, updateExtraProfile); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateExtraProfile: %w", err)
	}
	if q.updateOnboardingStmt, err = db.PrepareContext(ctx, updateOnboarding); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateOnboarding: %w", err)
	}
	if q.updatePartnerDataStmt, err = db.PrepareContext(ctx, updatePartnerData); err != nil {
		return nil, fmt.Errorf("error preparing query UpdatePartnerData: %w", err)
	}
	if q.updateStatusStmt, err = db.PrepareContext(ctx, updateStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStatus: %w", err)
	}
	if q.updateStepStmt, err = db.PrepareContext(ctx, updateStep); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStep: %w", err)
	}
	if q.updateStepAndExtraProfileStmt, err = db.PrepareContext(ctx, updateStepAndExtraProfile); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStepAndExtraProfile: %w", err)
	}
	if q.updateStepAndPartnerDataStmt, err = db.PrepareContext(ctx, updateStepAndPartnerData); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStepAndPartnerData: %w", err)
	}
	if q.updateStepAndRejectCodeStmt, err = db.PrepareContext(ctx, updateStepAndRejectCode); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateStepAndRejectCode: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.createOnboardingStmt != nil {
		if cerr := q.createOnboardingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createOnboardingStmt: %w", cerr)
		}
	}
	if q.getActiveOnboardingStmt != nil {
		if cerr := q.getActiveOnboardingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getActiveOnboardingStmt: %w", cerr)
		}
	}
	if q.getOnboardingByIDAndZalopayIDStmt != nil {
		if cerr := q.getOnboardingByIDAndZalopayIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOnboardingByIDAndZalopayIDStmt: %w", cerr)
		}
	}
	if q.getOnboardingByIDAndZalopayIDForUpdateStmt != nil {
		if cerr := q.getOnboardingByIDAndZalopayIDForUpdateStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getOnboardingByIDAndZalopayIDForUpdateStmt: %w", cerr)
		}
	}
	if q.listActiveOnboardingStmt != nil {
		if cerr := q.listActiveOnboardingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listActiveOnboardingStmt: %w", cerr)
		}
	}
	if q.listActiveOnboardingByZalopayIDsStmt != nil {
		if cerr := q.listActiveOnboardingByZalopayIDsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listActiveOnboardingByZalopayIDsStmt: %w", cerr)
		}
	}
	if q.listOnboardingByZalopayIDStmt != nil {
		if cerr := q.listOnboardingByZalopayIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing listOnboardingByZalopayIDStmt: %w", cerr)
		}
	}
	if q.updateAccountIDStmt != nil {
		if cerr := q.updateAccountIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateAccountIDStmt: %w", cerr)
		}
	}
	if q.updateApprovalDataStmt != nil {
		if cerr := q.updateApprovalDataStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateApprovalDataStmt: %w", cerr)
		}
	}
	if q.updateBasicOnboardingStmt != nil {
		if cerr := q.updateBasicOnboardingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateBasicOnboardingStmt: %w", cerr)
		}
	}
	if q.updateExtraProfileStmt != nil {
		if cerr := q.updateExtraProfileStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateExtraProfileStmt: %w", cerr)
		}
	}
	if q.updateOnboardingStmt != nil {
		if cerr := q.updateOnboardingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateOnboardingStmt: %w", cerr)
		}
	}
	if q.updatePartnerDataStmt != nil {
		if cerr := q.updatePartnerDataStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updatePartnerDataStmt: %w", cerr)
		}
	}
	if q.updateStatusStmt != nil {
		if cerr := q.updateStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStatusStmt: %w", cerr)
		}
	}
	if q.updateStepStmt != nil {
		if cerr := q.updateStepStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStepStmt: %w", cerr)
		}
	}
	if q.updateStepAndExtraProfileStmt != nil {
		if cerr := q.updateStepAndExtraProfileStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStepAndExtraProfileStmt: %w", cerr)
		}
	}
	if q.updateStepAndPartnerDataStmt != nil {
		if cerr := q.updateStepAndPartnerDataStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStepAndPartnerDataStmt: %w", cerr)
		}
	}
	if q.updateStepAndRejectCodeStmt != nil {
		if cerr := q.updateStepAndRejectCodeStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateStepAndRejectCodeStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                                         DBTX
	tx                                         *sql.Tx
	createOnboardingStmt                       *sql.Stmt
	getActiveOnboardingStmt                    *sql.Stmt
	getOnboardingByIDAndZalopayIDStmt          *sql.Stmt
	getOnboardingByIDAndZalopayIDForUpdateStmt *sql.Stmt
	listActiveOnboardingStmt                   *sql.Stmt
	listActiveOnboardingByZalopayIDsStmt       *sql.Stmt
	listOnboardingByZalopayIDStmt              *sql.Stmt
	updateAccountIDStmt                        *sql.Stmt
	updateApprovalDataStmt                     *sql.Stmt
	updateBasicOnboardingStmt                  *sql.Stmt
	updateExtraProfileStmt                     *sql.Stmt
	updateOnboardingStmt                       *sql.Stmt
	updatePartnerDataStmt                      *sql.Stmt
	updateStatusStmt                           *sql.Stmt
	updateStepStmt                             *sql.Stmt
	updateStepAndExtraProfileStmt              *sql.Stmt
	updateStepAndPartnerDataStmt               *sql.Stmt
	updateStepAndRejectCodeStmt                *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                                tx,
		tx:                                tx,
		createOnboardingStmt:              q.createOnboardingStmt,
		getActiveOnboardingStmt:           q.getActiveOnboardingStmt,
		getOnboardingByIDAndZalopayIDStmt: q.getOnboardingByIDAndZalopayIDStmt,
		getOnboardingByIDAndZalopayIDForUpdateStmt: q.getOnboardingByIDAndZalopayIDForUpdateStmt,
		listActiveOnboardingStmt:                   q.listActiveOnboardingStmt,
		listActiveOnboardingByZalopayIDsStmt:       q.listActiveOnboardingByZalopayIDsStmt,
		listOnboardingByZalopayIDStmt:              q.listOnboardingByZalopayIDStmt,
		updateAccountIDStmt:                        q.updateAccountIDStmt,
		updateApprovalDataStmt:                     q.updateApprovalDataStmt,
		updateBasicOnboardingStmt:                  q.updateBasicOnboardingStmt,
		updateExtraProfileStmt:                     q.updateExtraProfileStmt,
		updateOnboardingStmt:                       q.updateOnboardingStmt,
		updatePartnerDataStmt:                      q.updatePartnerDataStmt,
		updateStatusStmt:                           q.updateStatusStmt,
		updateStepStmt:                             q.updateStepStmt,
		updateStepAndExtraProfileStmt:              q.updateStepAndExtraProfileStmt,
		updateStepAndPartnerDataStmt:               q.updateStepAndPartnerDataStmt,
		updateStepAndRejectCodeStmt:                q.updateStepAndRejectCodeStmt,
	}
}
