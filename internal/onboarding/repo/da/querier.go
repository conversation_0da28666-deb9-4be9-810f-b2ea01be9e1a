// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package da

import (
	"context"
	"database/sql"
)

type Querier interface {
	CreateOnboarding(ctx context.Context, arg *CreateOnboardingParams) (sql.Result, error)
	GetActiveOnboarding(ctx context.Context, arg *GetActiveOnboardingParams) (*Bindings, error)
	GetOnboardingByIDAndZalopayID(ctx context.Context, arg *GetOnboardingByIDAndZalopayIDParams) (*Bindings, error)
	GetOnboardingByIDAndZalopayIDForUpdate(ctx context.Context, arg *GetOnboardingByIDAndZalopayIDForUpdateParams) (*Bindings, error)
	ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*Bindings, error)
	ListActiveOnboardingByZalopayIDs(ctx context.Context, zalopayIds []int64) ([]*Bindings, error)
	ListOnboardingByZalopayID(ctx context.Context, zalopayID int64) ([]*Bindings, error)
	UpdateAccountID(ctx context.Context, arg *UpdateAccountIDParams) error
	UpdateApprovalData(ctx context.Context, arg *UpdateApprovalDataParams) error
	UpdateBasicOnboarding(ctx context.Context, arg *UpdateBasicOnboardingParams) error
	UpdateExtraProfile(ctx context.Context, arg *UpdateExtraProfileParams) error
	UpdateOnboarding(ctx context.Context, arg *UpdateOnboardingParams) error
	UpdatePartnerData(ctx context.Context, arg *UpdatePartnerDataParams) error
	UpdateStatus(ctx context.Context, arg *UpdateStatusParams) error
	UpdateStep(ctx context.Context, arg *UpdateStepParams) error
	UpdateStepAndExtraProfile(ctx context.Context, arg *UpdateStepAndExtraProfileParams) error
	UpdateStepAndPartnerData(ctx context.Context, arg *UpdateStepAndPartnerDataParams) error
	UpdateStepAndRejectCode(ctx context.Context, arg *UpdateStepAndRejectCodeParams) error
}

var _ Querier = (*Queries)(nil)
