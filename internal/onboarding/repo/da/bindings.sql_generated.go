// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0
// source: bindings.sql

package da

import (
	"context"
	"database/sql"
	"encoding/json"
	"strings"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/entity"
)

const createOnboarding = `-- name: CreateOnboarding :execresult
INSERT INTO bindings (
  zalopay_id,
  partner_code, full_name,
  phone_number, id_number,
  id_issued_date, id_issued_location,
  ic_images, permanent_address,
  temp_residence_address, birthday, gender,
  status, current_step, reject_code)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateOnboardingParams struct {
	ZalopayID            int64           `json:"zalopay_id"`
	PartnerCode          string          `json:"partner_code"`
	FullName             string          `json:"full_name"`
	PhoneNumber          string          `json:"phone_number"`
	IDNumber             string          `json:"id_number"`
	IDIssuedDate         sql.NullString  `json:"id_issued_date"`
	IDIssuedLocation     sql.NullString  `json:"id_issued_location"`
	IcImages             entity.ICImages `json:"ic_images"`
	PermanentAddress     sql.NullString  `json:"permanent_address"`
	TempResidenceAddress sql.NullString  `json:"temp_residence_address"`
	Birthday             sql.NullString  `json:"birthday"`
	Gender               sql.NullString  `json:"gender"`
	Status               BindingsStatus  `json:"status"`
	CurrentStep          string          `json:"current_step"`
	RejectCode           string          `json:"reject_code"`
}

func (q *Queries) CreateOnboarding(ctx context.Context, arg *CreateOnboardingParams) (sql.Result, error) {
	return q.exec(ctx, q.createOnboardingStmt, createOnboarding,
		arg.ZalopayID,
		arg.PartnerCode,
		arg.FullName,
		arg.PhoneNumber,
		arg.IDNumber,
		arg.IDIssuedDate,
		arg.IDIssuedLocation,
		arg.IcImages,
		arg.PermanentAddress,
		arg.TempResidenceAddress,
		arg.Birthday,
		arg.Gender,
		arg.Status,
		arg.CurrentStep,
		arg.RejectCode,
	)
}

const getActiveOnboarding = `-- name: GetActiveOnboarding :one
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE zalopay_id = ? AND partner_code = ? AND status = 'active'
`

type GetActiveOnboardingParams struct {
	ZalopayID   int64  `json:"zalopay_id"`
	PartnerCode string `json:"partner_code"`
}

func (q *Queries) GetActiveOnboarding(ctx context.Context, arg *GetActiveOnboardingParams) (*Bindings, error) {
	row := q.queryRow(ctx, q.getActiveOnboardingStmt, getActiveOnboarding, arg.ZalopayID, arg.PartnerCode)
	var i Bindings
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.FullName,
		&i.PhoneNumber,
		&i.IDNumber,
		&i.IDIssuedDate,
		&i.IDIssuedLocation,
		&i.IcImages,
		&i.PermanentAddress,
		&i.TempResidenceAddress,
		&i.Birthday,
		&i.Gender,
		&i.Status,
		&i.CurrentStep,
		&i.RejectCode,
		&i.ExtraProfile,
		&i.PartnerData,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getOnboardingByIDAndZalopayID = `-- name: GetOnboardingByIDAndZalopayID :one
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE id = ? AND zalopay_id = ?
`

type GetOnboardingByIDAndZalopayIDParams struct {
	ID        int64 `json:"id"`
	ZalopayID int64 `json:"zalopay_id"`
}

func (q *Queries) GetOnboardingByIDAndZalopayID(ctx context.Context, arg *GetOnboardingByIDAndZalopayIDParams) (*Bindings, error) {
	row := q.queryRow(ctx, q.getOnboardingByIDAndZalopayIDStmt, getOnboardingByIDAndZalopayID, arg.ID, arg.ZalopayID)
	var i Bindings
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.FullName,
		&i.PhoneNumber,
		&i.IDNumber,
		&i.IDIssuedDate,
		&i.IDIssuedLocation,
		&i.IcImages,
		&i.PermanentAddress,
		&i.TempResidenceAddress,
		&i.Birthday,
		&i.Gender,
		&i.Status,
		&i.CurrentStep,
		&i.RejectCode,
		&i.ExtraProfile,
		&i.PartnerData,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const getOnboardingByIDAndZalopayIDForUpdate = `-- name: GetOnboardingByIDAndZalopayIDForUpdate :one
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE id = ? AND zalopay_id = ? FOR UPDATE
`

type GetOnboardingByIDAndZalopayIDForUpdateParams struct {
	ID        int64 `json:"id"`
	ZalopayID int64 `json:"zalopay_id"`
}

func (q *Queries) GetOnboardingByIDAndZalopayIDForUpdate(ctx context.Context, arg *GetOnboardingByIDAndZalopayIDForUpdateParams) (*Bindings, error) {
	row := q.queryRow(ctx, q.getOnboardingByIDAndZalopayIDForUpdateStmt, getOnboardingByIDAndZalopayIDForUpdate, arg.ID, arg.ZalopayID)
	var i Bindings
	err := row.Scan(
		&i.ID,
		&i.ZalopayID,
		&i.AccountID,
		&i.PartnerCode,
		&i.FullName,
		&i.PhoneNumber,
		&i.IDNumber,
		&i.IDIssuedDate,
		&i.IDIssuedLocation,
		&i.IcImages,
		&i.PermanentAddress,
		&i.TempResidenceAddress,
		&i.Birthday,
		&i.Gender,
		&i.Status,
		&i.CurrentStep,
		&i.RejectCode,
		&i.ExtraProfile,
		&i.PartnerData,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const listActiveOnboarding = `-- name: ListActiveOnboarding :many
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE zalopay_id = ? AND status = 'active'
`

func (q *Queries) ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*Bindings, error) {
	rows, err := q.query(ctx, q.listActiveOnboardingStmt, listActiveOnboarding, zalopayID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Bindings
	for rows.Next() {
		var i Bindings
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.FullName,
			&i.PhoneNumber,
			&i.IDNumber,
			&i.IDIssuedDate,
			&i.IDIssuedLocation,
			&i.IcImages,
			&i.PermanentAddress,
			&i.TempResidenceAddress,
			&i.Birthday,
			&i.Gender,
			&i.Status,
			&i.CurrentStep,
			&i.RejectCode,
			&i.ExtraProfile,
			&i.PartnerData,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listActiveOnboardingByZalopayIDs = `-- name: ListActiveOnboardingByZalopayIDs :many
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE zalopay_id IN (/*SLICE:zalopay_ids*/?) and status = 'active'
`

func (q *Queries) ListActiveOnboardingByZalopayIDs(ctx context.Context, zalopayIds []int64) ([]*Bindings, error) {
	query := listActiveOnboardingByZalopayIDs
	var queryParams []interface{}
	if len(zalopayIds) > 0 {
		for _, v := range zalopayIds {
			queryParams = append(queryParams, v)
		}
		query = strings.Replace(query, "/*SLICE:zalopay_ids*/?", strings.Repeat(",?", len(zalopayIds))[1:], 1)
	} else {
		query = strings.Replace(query, "/*SLICE:zalopay_ids*/?", "NULL", 1)
	}
	rows, err := q.query(ctx, nil, query, queryParams...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Bindings
	for rows.Next() {
		var i Bindings
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.FullName,
			&i.PhoneNumber,
			&i.IDNumber,
			&i.IDIssuedDate,
			&i.IDIssuedLocation,
			&i.IcImages,
			&i.PermanentAddress,
			&i.TempResidenceAddress,
			&i.Birthday,
			&i.Gender,
			&i.Status,
			&i.CurrentStep,
			&i.RejectCode,
			&i.ExtraProfile,
			&i.PartnerData,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOnboardingByZalopayID = `-- name: ListOnboardingByZalopayID :many
SELECT id, zalopay_id, account_id, partner_code, full_name, phone_number, id_number, id_issued_date, id_issued_location, ic_images, permanent_address, temp_residence_address, birthday, gender, status, current_step, reject_code, extra_profile, partner_data, created_at, updated_at
FROM bindings
WHERE zalopay_id = ?
`

func (q *Queries) ListOnboardingByZalopayID(ctx context.Context, zalopayID int64) ([]*Bindings, error) {
	rows, err := q.query(ctx, q.listOnboardingByZalopayIDStmt, listOnboardingByZalopayID, zalopayID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []*Bindings
	for rows.Next() {
		var i Bindings
		if err := rows.Scan(
			&i.ID,
			&i.ZalopayID,
			&i.AccountID,
			&i.PartnerCode,
			&i.FullName,
			&i.PhoneNumber,
			&i.IDNumber,
			&i.IDIssuedDate,
			&i.IDIssuedLocation,
			&i.IcImages,
			&i.PermanentAddress,
			&i.TempResidenceAddress,
			&i.Birthday,
			&i.Gender,
			&i.Status,
			&i.CurrentStep,
			&i.RejectCode,
			&i.ExtraProfile,
			&i.PartnerData,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateAccountID = `-- name: UpdateAccountID :exec
UPDATE bindings
SET ` + "`" + `account_id` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateAccountIDParams struct {
	AccountID sql.NullInt64 `json:"account_id"`
	ID        int64         `json:"id"`
	ZalopayID int64         `json:"zalopay_id"`
}

func (q *Queries) UpdateAccountID(ctx context.Context, arg *UpdateAccountIDParams) error {
	_, err := q.exec(ctx, q.updateAccountIDStmt, updateAccountID, arg.AccountID, arg.ID, arg.ZalopayID)
	return err
}

const updateApprovalData = `-- name: UpdateApprovalData :exec
UPDATE bindings
SET ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `partner_data` + "`" + ` = ?,
    ` + "`" + `reject_code` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateApprovalDataParams struct {
	CurrentStep string          `json:"current_step"`
	PartnerData json.RawMessage `json:"partner_data"`
	RejectCode  string          `json:"reject_code"`
	ID          int64           `json:"id"`
	ZalopayID   int64           `json:"zalopay_id"`
}

func (q *Queries) UpdateApprovalData(ctx context.Context, arg *UpdateApprovalDataParams) error {
	_, err := q.exec(ctx, q.updateApprovalDataStmt, updateApprovalData,
		arg.CurrentStep,
		arg.PartnerData,
		arg.RejectCode,
		arg.ID,
		arg.ZalopayID,
	)
	return err
}

const updateBasicOnboarding = `-- name: UpdateBasicOnboarding :exec
UPDATE bindings
SET ` + "`" + `full_name` + "`" + ` = ?,
    ` + "`" + `phone_number` + "`" + ` = ?,
    ` + "`" + `id_number` + "`" + ` = ?,
    ` + "`" + `gender` + "`" + ` = ?,
    ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `reject_code` + "`" + ` = ?
WHERE id = ?
`

type UpdateBasicOnboardingParams struct {
	FullName    string         `json:"full_name"`
	PhoneNumber string         `json:"phone_number"`
	IDNumber    string         `json:"id_number"`
	Gender      sql.NullString `json:"gender"`
	CurrentStep string         `json:"current_step"`
	RejectCode  string         `json:"reject_code"`
	ID          int64          `json:"id"`
}

func (q *Queries) UpdateBasicOnboarding(ctx context.Context, arg *UpdateBasicOnboardingParams) error {
	_, err := q.exec(ctx, q.updateBasicOnboardingStmt, updateBasicOnboarding,
		arg.FullName,
		arg.PhoneNumber,
		arg.IDNumber,
		arg.Gender,
		arg.CurrentStep,
		arg.RejectCode,
		arg.ID,
	)
	return err
}

const updateExtraProfile = `-- name: UpdateExtraProfile :exec
UPDATE bindings
SET ` + "`" + `extra_profile` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateExtraProfileParams struct {
	ExtraProfile json.RawMessage `json:"extra_profile"`
	ID           int64           `json:"id"`
	ZalopayID    int64           `json:"zalopay_id"`
}

func (q *Queries) UpdateExtraProfile(ctx context.Context, arg *UpdateExtraProfileParams) error {
	_, err := q.exec(ctx, q.updateExtraProfileStmt, updateExtraProfile, arg.ExtraProfile, arg.ID, arg.ZalopayID)
	return err
}

const updateOnboarding = `-- name: UpdateOnboarding :exec
UPDATE bindings
SET ` + "`" + `full_name` + "`" + ` = ?,
    ` + "`" + `phone_number` + "`" + ` = ?,
    ` + "`" + `id_number` + "`" + ` = ?,
    ` + "`" + `id_issued_date` + "`" + ` = ?,
    ` + "`" + `id_issued_location` + "`" + ` = ?,
    ` + "`" + `ic_images` + "`" + ` = ?,
    ` + "`" + `permanent_address` + "`" + ` = ?,
    ` + "`" + `temp_residence_address` + "`" + ` = ?,
    ` + "`" + `birthday` + "`" + ` = ?,
    ` + "`" + `gender` + "`" + ` = ?,
    ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `reject_code` + "`" + ` = ?,
    ` + "`" + `extra_profile` + "`" + ` = ?,
    ` + "`" + `partner_data` + "`" + ` = ?,
    ` + "`" + `updated_at` + "`" + `=CURRENT_TIMESTAMP
WHERE id = ?
`

type UpdateOnboardingParams struct {
	FullName             string          `json:"full_name"`
	PhoneNumber          string          `json:"phone_number"`
	IDNumber             string          `json:"id_number"`
	IDIssuedDate         sql.NullString  `json:"id_issued_date"`
	IDIssuedLocation     sql.NullString  `json:"id_issued_location"`
	IcImages             entity.ICImages `json:"ic_images"`
	PermanentAddress     sql.NullString  `json:"permanent_address"`
	TempResidenceAddress sql.NullString  `json:"temp_residence_address"`
	Birthday             sql.NullString  `json:"birthday"`
	Gender               sql.NullString  `json:"gender"`
	CurrentStep          string          `json:"current_step"`
	RejectCode           string          `json:"reject_code"`
	ExtraProfile         json.RawMessage `json:"extra_profile"`
	PartnerData          json.RawMessage `json:"partner_data"`
	ID                   int64           `json:"id"`
}

func (q *Queries) UpdateOnboarding(ctx context.Context, arg *UpdateOnboardingParams) error {
	_, err := q.exec(ctx, q.updateOnboardingStmt, updateOnboarding,
		arg.FullName,
		arg.PhoneNumber,
		arg.IDNumber,
		arg.IDIssuedDate,
		arg.IDIssuedLocation,
		arg.IcImages,
		arg.PermanentAddress,
		arg.TempResidenceAddress,
		arg.Birthday,
		arg.Gender,
		arg.CurrentStep,
		arg.RejectCode,
		arg.ExtraProfile,
		arg.PartnerData,
		arg.ID,
	)
	return err
}

const updatePartnerData = `-- name: UpdatePartnerData :exec
UPDATE bindings
SET ` + "`" + `partner_data` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdatePartnerDataParams struct {
	PartnerData json.RawMessage `json:"partner_data"`
	ID          int64           `json:"id"`
	ZalopayID   int64           `json:"zalopay_id"`
}

func (q *Queries) UpdatePartnerData(ctx context.Context, arg *UpdatePartnerDataParams) error {
	_, err := q.exec(ctx, q.updatePartnerDataStmt, updatePartnerData, arg.PartnerData, arg.ID, arg.ZalopayID)
	return err
}

const updateStatus = `-- name: UpdateStatus :exec
UPDATE bindings
SET ` + "`" + `status` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateStatusParams struct {
	Status    BindingsStatus `json:"status"`
	ID        int64          `json:"id"`
	ZalopayID int64          `json:"zalopay_id"`
}

func (q *Queries) UpdateStatus(ctx context.Context, arg *UpdateStatusParams) error {
	_, err := q.exec(ctx, q.updateStatusStmt, updateStatus, arg.Status, arg.ID, arg.ZalopayID)
	return err
}

const updateStep = `-- name: UpdateStep :exec
UPDATE bindings
SET ` + "`" + `current_step` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateStepParams struct {
	CurrentStep string `json:"current_step"`
	ID          int64  `json:"id"`
	ZalopayID   int64  `json:"zalopay_id"`
}

func (q *Queries) UpdateStep(ctx context.Context, arg *UpdateStepParams) error {
	_, err := q.exec(ctx, q.updateStepStmt, updateStep, arg.CurrentStep, arg.ID, arg.ZalopayID)
	return err
}

const updateStepAndExtraProfile = `-- name: UpdateStepAndExtraProfile :exec
UPDATE bindings
SET ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `extra_profile` + "`" + ` = ?
WHERE id = ? AND zalopay_id = ?
`

type UpdateStepAndExtraProfileParams struct {
	CurrentStep  string          `json:"current_step"`
	ExtraProfile json.RawMessage `json:"extra_profile"`
	ID           int64           `json:"id"`
	ZalopayID    int64           `json:"zalopay_id"`
}

func (q *Queries) UpdateStepAndExtraProfile(ctx context.Context, arg *UpdateStepAndExtraProfileParams) error {
	_, err := q.exec(ctx, q.updateStepAndExtraProfileStmt, updateStepAndExtraProfile,
		arg.CurrentStep,
		arg.ExtraProfile,
		arg.ID,
		arg.ZalopayID,
	)
	return err
}

const updateStepAndPartnerData = `-- name: UpdateStepAndPartnerData :exec
UPDATE bindings
SET ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `partner_data` + "`" + ` = ?
WHERE id = ?
`

type UpdateStepAndPartnerDataParams struct {
	CurrentStep string          `json:"current_step"`
	PartnerData json.RawMessage `json:"partner_data"`
	ID          int64           `json:"id"`
}

func (q *Queries) UpdateStepAndPartnerData(ctx context.Context, arg *UpdateStepAndPartnerDataParams) error {
	_, err := q.exec(ctx, q.updateStepAndPartnerDataStmt, updateStepAndPartnerData, arg.CurrentStep, arg.PartnerData, arg.ID)
	return err
}

const updateStepAndRejectCode = `-- name: UpdateStepAndRejectCode :exec
UPDATE bindings
SET ` + "`" + `current_step` + "`" + ` = ?,
    ` + "`" + `reject_code` + "`" + ` = ?
WHERE id = ?
`

type UpdateStepAndRejectCodeParams struct {
	CurrentStep string `json:"current_step"`
	RejectCode  string `json:"reject_code"`
	ID          int64  `json:"id"`
}

func (q *Queries) UpdateStepAndRejectCode(ctx context.Context, arg *UpdateStepAndRejectCodeParams) error {
	_, err := q.exec(ctx, q.updateStepAndRejectCodeStmt, updateStepAndRejectCode, arg.CurrentStep, arg.RejectCode, arg.ID)
	return err
}
