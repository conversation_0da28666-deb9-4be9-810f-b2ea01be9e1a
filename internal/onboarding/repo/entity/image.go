package entity

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type Image struct {
	URL         string `json:"url,omitempty"`
	Path        string `json:"path,omitempty"`
	ContentType string `json:"content_type,omitempty"`
	Checksum    string `json:"checksum,omitempty"`
}

func (i Image) IsEmpty() bool {
	return i == Image{}
}

type ICImages struct {
	Avatar   Image `json:"avatar,omitempty"`
	FrontIC  Image `json:"front_ic,omitempty"`
	BackIC   Image `json:"back_ic,omitempty"`
	SelfieIC Image `json:"selfie_ic,omitempty"`
}

//nolint:gocritic
func (i ICImages) IsEmpty() bool {
	return i.Avatar.IsEmpty() &&
		i.FrontIC.IsEmpty() &&
		i.BackIC.IsEmpty() &&
		i.SelfieIC.IsEmpty()
}

func (i ICImages) Value() (driver.Value, error) {
	j, err := json.Marshal(i)
	if err != nil {
		return nil, err
	}
	return driver.Value(j), nil
}

func (i *ICImages) Scan(src any) error {
	if src == nil {
		*i = ICImages{}
		return nil
	}

	var _i ICImages
	var source []byte
	switch t := src.(type) {
	case []uint8:
		source = t
	case nil:
		return nil
	default:
		return errors.New("incompatible type for ICImage")
	}

	err := json.Unmarshal(source, &_i)
	if err != nil {
		return err
	}
	*i = _i
	return nil
}

type SignImage struct {
	URL         string `json:"url"`
	ContentType string `json:"content_type"`
}

func (s *SignImage) UpdateSignImage(url, contentType string) {
	s.URL = url
	s.ContentType = contentType
}

func (s SignImage) IsEmpty() bool {
	return s.URL == "" || s.ContentType == ""
}
