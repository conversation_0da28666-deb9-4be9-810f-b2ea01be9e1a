package repo

import (
	"context"
	"database/sql"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/entity"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type onboardingRepo struct {
	repos  *Repository
	logger *log.Helper
}

// NewOnboardingRepo .
func NewOnboardingRepo(repos *Repository, logger log.Logger) _interface.OnboardingRepo {
	return &onboardingRepo{
		repos:  repos,
		logger: log.NewHelper(log.With(logger, "module", "onboarding-repo")),
	}
}

func (r *onboardingRepo) GetActiveOnboarding(ctx context.Context, zalopayID int64, partnerCode string) (*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("GetActiveOnboarding")

	binding, err := r.repos.Queries(ctx).GetActiveOnboarding(ctx, &da.GetActiveOnboardingParams{
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode,
	})
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrOnboardingNotFound
	}
	if err != nil {
		r.logger.WithContext(ctx).Errorf("get active onboarding failed: %v", err)
		return nil, errors.Wrapf(err, "get active onboarding failed")
	}

	obd, err := unmarshalOnboarding(binding)
	if err != nil {
		r.logger.WithContext(ctx).Errorf("unmarshalOnboarding failed: %v", err)
		return nil, err
	}

	return obd, nil
}

func (r *onboardingRepo) UpdateOnboardingInfo(ctx context.Context, onboarding *model.Onboarding) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateOnboardingInfo")
	logger := r.logger.WithContext(ctx)

	if onboarding == nil {
		logger.Errorf("onboarding is empty")
		return errors.New("onboarding is empty")
	}

	userInfo, parseOk := cimb.AsUserOnboardingInfo(onboarding.UserInfo)
	if !parseOk {
		logger.Errorf("failed to parse user onboarding info")
		return errors.New("failed to parse user onboarding info")
	}
	userExtraBin, err := userInfo.UserOnboardingExtra.MarshalBinary()
	if err != nil {
		logger.Errorf("failed to marshal extra profile")
		return errors.Wrap(err, "failed to marshal extra profile")
	}

	partnerData, parseOk := cimb.AsPartnerOnboardingData(onboarding.PartnerData)
	if !parseOk {
		logger.Errorf("failed to parse partner onboarding data")
		return errors.New("failed to parse partner onboarding data")
	}
	partnerDataBin, err := partnerData.MarshalBinary()
	if err != nil {
		logger.Errorf("failed to marshal partner data")
		return errors.Wrap(err, "failed to marshal partner data")
	}

	icImages := icImagesToEntityICImages(onboarding.IcImages)

	params := &da.UpdateOnboardingParams{
		ID:                   onboarding.ID,
		FullName:             userInfo.FullName,
		PhoneNumber:          userInfo.PhoneNumber,
		IcImages:             icImages,
		ExtraProfile:         userExtraBin,
		PartnerData:          partnerDataBin,
		IDNumber:             userInfo.IdNumber,
		Birthday:             zutils.NewNullString(userInfo.Birthday),
		IDIssuedDate:         zutils.NewNullString(userInfo.IdIssueDate),
		IDIssuedLocation:     zutils.NewNullString(userInfo.IdIssuePlace),
		PermanentAddress:     zutils.NewNullString(userInfo.PermanentAddress),
		TempResidenceAddress: zutils.NewNullString(userInfo.TempResidenceAddress),
		Gender:               zutils.NewNullString(zutils.GenderConvertToString(userInfo.Gender)),
		RejectCode:           onboarding.RejectCode,
		CurrentStep:          onboarding.CurrentStep.String(),
	}

	err = r.repos.Queries(ctx).UpdateOnboarding(ctx, params)
	if err != nil {
		logger.Errorf("UpdateOnboardingInfo into db failed: %v", err)
		return errors.Wrap(err, "UpdateOnboardingInfo into db failed")
	}
	return nil
}

func (r *onboardingRepo) CreateUserOnboarding(ctx context.Context, onboarding *model.Onboarding) (int64, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("CreateUserOnboarding")
	logger := r.logger.WithContext(ctx)

	params := &da.CreateOnboardingParams{
		ZalopayID:            onboarding.ZalopayID,
		PartnerCode:          onboarding.PartnerCode,
		FullName:             onboarding.UserInfo.GetFullName(),
		PhoneNumber:          onboarding.UserInfo.GetPhoneNumber(),
		IDNumber:             onboarding.UserInfo.GetIdNumber(),
		IDIssuedDate:         sql.NullString{},
		IDIssuedLocation:     sql.NullString{},
		IcImages:             entity.ICImages{},
		PermanentAddress:     sql.NullString{},
		TempResidenceAddress: sql.NullString{},
		Birthday:             sql.NullString{},
		Gender:               zutils.NewNullString(onboarding.UserInfo.GetGenderStr()),
		Status:               da.BindingsStatusActive,
		CurrentStep:          onboarding.CurrentStep.String(),
		RejectCode:           onboarding.RejectCode,
	}
	result, err := r.repos.Queries(ctx).CreateOnboarding(ctx, params)
	if err != nil {
		logger.Errorf("CreateOnboarding into db failed: %v", err)
		return -1, errors.Wrap(err, "CreateOnboarding into db failed")
	}
	onboardingID, err := result.LastInsertId()
	if err != nil {
		logger.Errorf("Get last insert id failed: %v", err)
		return -1, errors.Wrap(err, "Get last insert id failed")
	}
	return onboardingID, nil
}

func (r *onboardingRepo) UpdateBasicOnboarding(ctx context.Context, onboarding *model.Onboarding) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateBasicOnboarding")
	logger := r.logger.WithContext(ctx)

	params := &da.UpdateBasicOnboardingParams{
		ID:          onboarding.ID,
		FullName:    onboarding.UserInfo.GetFullName(),
		PhoneNumber: onboarding.UserInfo.GetPhoneNumber(),
		IDNumber:    onboarding.UserInfo.GetIdNumber(),
		Gender:      zutils.NewNullString(onboarding.UserInfo.GetGenderStr()),
		CurrentStep: onboarding.CurrentStep.String(),
		RejectCode:  onboarding.RejectCode,
	}
	err := r.repos.Queries(ctx).UpdateBasicOnboarding(ctx, params)
	if err != nil {
		logger.Errorf("UpdateBasicOnboarding into db failed: %+v", err)
		return errors.Wrap(err, "UpdateBasicOnboarding into db failed")
	}
	return nil
}

func (r *onboardingRepo) ListOnboardingByUserID(ctx context.Context, userID int64) ([]*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("ListOnboardingByUserID")

	onboards, err := r.repos.Queries(ctx).ListOnboardingByZalopayID(ctx, userID)
	if err != nil {
		r.logger.WithContext(ctx).Errorf("ListOnboardingByZalopayID failed: %v", err)
		return nil, errors.Wrap(err, "ListOnboardingByZalopayID failed")
	}

	result := make([]*model.Onboarding, 0, len(onboards))
	for _, onboard := range onboards {
		obd, uErr := unmarshalOnboarding(onboard)
		if uErr != nil {
			r.logger.WithContext(ctx).Errorf("unmarshalOnboarding failed: %v", uErr)
			continue
		}
		result = append(result, obd)
	}
	return result, nil
}

func (r *onboardingRepo) UpdateStepAndRejectCode(ctx context.Context,
	onboardingID int64, step model.OnboardingStep, rejectCode string) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateStepAndRejectCode")

	params := &da.UpdateStepAndRejectCodeParams{
		ID:          onboardingID,
		CurrentStep: step.String(),
		RejectCode:  rejectCode,
	}
	err := r.repos.Queries(ctx).UpdateStepAndRejectCode(ctx, params)
	if err != nil {
		r.logger.WithContext(ctx).Errorf("UpdateStepAndRejectCode failed: %v", err)
		return errors.Wrap(err, "UpdateStepAndRejectCode failed")
	}
	return nil
}

func (r *onboardingRepo) UpdateStepAndExtraProfile(ctx context.Context, onboarding *model.Onboarding) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateStepAndExtraProfile")
	logger := r.logger.WithContext(ctx)

	if onboarding == nil {
		logger.Errorf("onboarding is empty")
		return errors.New("onboarding is empty")
	}

	userInfo, parseOk := cimb.AsUserOnboardingInfo(onboarding.UserInfo)
	if !parseOk {
		logger.Errorf("failed to parse user onboarding info")
		return errors.New("failed to parse user onboarding info")
	}
	userExtraBin, err := userInfo.UserOnboardingExtra.MarshalBinary()
	if err != nil {
		logger.Errorf("failed to marshal extra profile")
		return errors.Wrap(err, "failed to marshal extra profile")
	}

	params := &da.UpdateStepAndExtraProfileParams{
		ID:           onboarding.ID,
		ZalopayID:    onboarding.ZalopayID,
		CurrentStep:  onboarding.CurrentStep.String(),
		ExtraProfile: userExtraBin,
	}
	if err = r.repos.Queries(ctx).UpdateStepAndExtraProfile(ctx, params); err != nil {
		logger.Errorf("UpdateStepAndExtraProfile into db failed: %v", err)
		return errors.Wrap(err, "UpdateStepAndExtraProfile into db failed")
	}
	return nil
}

func (r *onboardingRepo) UpdateStepAndPartnerData(ctx context.Context, onboardingID int64,
	step model.OnboardingStep, partnerData model.PartnerOnboardingData) error {
	logger := r.logger.WithContext(ctx)

	partnerDataBin, err := partnerData.MarshalBinary()
	if err != nil {
		logger.Errorf("failed to marshal partner data")
		return errors.Wrap(err, "failed to marshal partner data")
	}

	params := &da.UpdateStepAndPartnerDataParams{
		ID:          onboardingID,
		CurrentStep: step.String(),
		PartnerData: partnerDataBin,
	}
	if err = r.repos.Queries(ctx).UpdateStepAndPartnerData(ctx, params); err != nil {
		logger.Errorf("UpdateStepAndPartnerData into db failed: %v", err)
		return errors.Wrap(err, "UpdateStepAndPartnerData into db failed")
	}
	return nil
}

func (r *onboardingRepo) UpdateOnboardingStep(ctx context.Context,
	zalopayID, onboardingID int64, step model.OnboardingStep) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateOnboardingStep")
	logger := r.logger.WithContext(ctx)

	if zalopayID == 0 || onboardingID == 0 {
		logger.Errorf("onboarding is empty")
		return errors.New("onboarding is empty")
	}

	params := &da.UpdateStepParams{
		ID:          onboardingID,
		ZalopayID:   zalopayID,
		CurrentStep: step.String(),
	}
	if err := r.repos.Queries(ctx).UpdateStep(ctx, params); err != nil {
		logger.Errorf("UpdateStep into db failed: %v", err)
		return errors.Wrap(err, "UpdateStep into db failed")
	}
	return nil
}

func (r *onboardingRepo) GetOnboardingByIDAndUserID(
	ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("GetOnboardingByIDAndUserID")
	logger := r.logger.WithContext(ctx)

	params := &da.GetOnboardingByIDAndZalopayIDParams{
		ID:        onboardingID,
		ZalopayID: zalopayID,
	}
	binding, err := r.repos.Queries(ctx).GetOnboardingByIDAndZalopayID(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrOnboardingNotFound
	}
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndZalopayID failed: %v", err)
		return nil, errors.Wrap(err, "GetOnboardingByIDAndZalopayID failed")
	}
	obd, err := unmarshalOnboarding(binding)
	if err != nil {
		logger.Errorf("unmarshalOnboarding failed: %v", err)
		return nil, errors.Wrap(err, "unmarshalOnboarding failed")
	}
	return obd, nil
}

func (r *onboardingRepo) GetOnboardingByIDAndUserIDForUpdate(
	ctx context.Context, zalopayID, onboardingID int64) (*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("GetOnboardingByIDAndUserIDForUpdate")
	logger := r.logger.WithContext(ctx)

	params := &da.GetOnboardingByIDAndZalopayIDForUpdateParams{
		ID:        onboardingID,
		ZalopayID: zalopayID,
	}
	binding, err := r.repos.Queries(ctx).GetOnboardingByIDAndZalopayIDForUpdate(ctx, params)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrOnboardingNotFound
	}
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndZalopayIDForUpdate failed: %v", err)
		return nil, errors.Wrap(err, "GetOnboardingByIDAndZalopayIDForUpdate failed")
	}
	obd, err := unmarshalOnboarding(binding)
	if err != nil {
		logger.Errorf("unmarshalOnboarding failed: %v", err)
		return nil, errors.Wrap(err, "unmarshalOnboarding failed")
	}
	return obd, nil
}

func (r *onboardingRepo) UpdatePartnerData(ctx context.Context, params *model.UpdatePartnerDataParams) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdatePartnerData")

	var err error
	var partnerDataRaw []byte

	switch params.PartnerCode {
	case partner.PartnerCIMB:
		partnerDataRaw, err = jsoniter.Marshal(params.PartnerData)
	}
	if err != nil {
		r.logger.WithContext(ctx).Errorf("Marshal partner data failed: %v", err)
		return errors.Wrap(err, "Marshal partner data failed")
	}

	err = r.repos.Queries(ctx).UpdatePartnerData(ctx, &da.UpdatePartnerDataParams{
		ID:          params.OnboardingID,
		ZalopayID:   params.ZalopayID,
		PartnerData: partnerDataRaw,
	})
	if err != nil {
		r.logger.WithContext(ctx).Errorf("UpdatePartnerData failed: %v", err)
		return errors.Wrap(err, "UpdatePartnerData into db failed")
	}
	return nil
}

func (r *onboardingRepo) ListActiveOnboarding(ctx context.Context, zalopayID int64) ([]*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("ListActiveOnboarding")
	logger := r.logger.WithContext(ctx)

	bindings, err := r.repos.Queries(ctx).ListActiveOnboarding(ctx, zalopayID)
	if errors.Is(err, sql.ErrNoRows) || len(bindings) == 0 {
		return nil, model.ErrOnboardingNotFound
	}
	if err != nil {
		logger.Errorf("ListActiveOnboarding failed: %v", err)
		return nil, errors.Wrap(err, "ListActiveOnboarding failed")
	}

	result := make([]*model.Onboarding, 0, len(bindings))
	for _, binding := range bindings {
		obd, uErr := unmarshalOnboarding(binding)
		if uErr != nil {
			logger.Errorf("unmarshalOnboarding failed: %v", uErr)
			continue
		}
		result = append(result, obd)
	}

	return result, nil
}

func (r *onboardingRepo) ListActiveOnboardingByZalopayIDs(ctx context.Context, zalopayIDs []int64) ([]*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("ListActiveOnboardingByZalopayIDs")
	logger := r.logger.WithContext(ctx)

	onboarding, err := r.repos.Queries(ctx).ListActiveOnboardingByZalopayIDs(ctx, zalopayIDs)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, model.ErrOnboardingNotFound
	}
	if err != nil {
		logger.Errorf("ListActiveOnboardingByZalopayIDs failed: %v", err)
		return nil, errors.Wrap(err, "ListActiveOnboardingByZalopayIDs failed")
	}

	result := make([]*model.Onboarding, 0, len(onboarding))
	for _, binding := range onboarding {
		obd, uErr := unmarshalOnboarding(binding)
		if uErr != nil {
			logger.Errorf("unmarshalOnboarding failed: %v", uErr)
			continue
		}
		result = append(result, obd)
	}
	return result, nil
}

func (r *onboardingRepo) UpdateOnboardingApproval(
	ctx context.Context, zalopayID int64, onboardingID int64,
	execFunc model.UpdateOnboardingFunc) (*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateOnboardingApproval")
	logger := r.logger.WithContext(ctx)

	// Note: GetOnboardingByIDAndUserIDForUpdate is used to lock the row,
	// so the context should include the transaction.
	onboarding, err := r.GetOnboardingByIDAndUserIDForUpdate(ctx, zalopayID, onboardingID)
	if err != nil {
		r.logger.Errorf("GetOnboardingByIDAndUserIDForUpdate failed: %v", err)
		return nil, errors.Wrap(err, "GetOnboardingByIDAndUserIDForUpdate failed")
	}

	onboarding, err = execFunc(ctx, onboarding)
	if err != nil {
		logger.Errorf("execFunc failed: %v", err)
		return nil, errors.Wrap(err, "execFunc failed")
	}
	if onboarding == nil {
		logger.Errorf("execFunc return nil")
		return nil, errors.Errorf("execFunc return nil")
	}

	partnerData, err := onboarding.PartnerData.MarshalBinary()
	if err != nil {
		logger.Errorf("Marshal partner data failed: %v", err)
		return nil, errors.Wrap(err, "Marshal partner data failed")
	}

	err = r.repos.Queries(ctx).UpdateApprovalData(ctx, &da.UpdateApprovalDataParams{
		ID:          onboarding.ID,
		ZalopayID:   onboarding.ZalopayID,
		RejectCode:  onboarding.RejectCode,
		CurrentStep: onboarding.CurrentStep.String(),
		PartnerData: partnerData,
	})
	if err != nil {
		logger.Errorf("UpdateOnboardingApproval failed: %v", err)
		return nil, errors.Wrap(err, "UpdateOnboardingApproval failed")
	}
	return onboarding, nil
}

func (r *onboardingRepo) UpdateOnboardingApprovalWithTx(
	ctx context.Context, zalopayID int64, onboardingID int64,
	execFunc model.UpdateOnboardingFunc) (*model.Onboarding, error) {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateOnboardingApprovalWithTx")
	logger := r.logger.WithContext(ctx)

	tCtx, err := r.repos.BeginTx(ctx)
	if err != nil {
		logger.Errorf("BeginTx failed: %v", err)
		return nil, errors.Wrap(err, "BeginTx failed")
	}
	defer r.repos.RollbackTx(tCtx)

	result, err := r.UpdateOnboardingApproval(tCtx, zalopayID, onboardingID, execFunc)
	if err != nil {
		logger.Errorf("UpdateOnboardingApproval failed: %v", err)
		return nil, errors.Wrap(err, "UpdateOnboardingApproval failed")
	}

	if err = r.repos.CommitTx(tCtx); err != nil {
		logger.Errorf("CommitTx failed: %v", err)
		return nil, errors.Wrap(err, "CommitTx failed")
	}
	return result, nil
}

func (r *onboardingRepo) UpdateAccountID(
	ctx context.Context, zalopayID int64,
	onboardingID int64, accountID int64) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateAccountID")

	err := r.repos.Queries(ctx).UpdateAccountID(ctx, &da.UpdateAccountIDParams{
		ID:        onboardingID,
		ZalopayID: zalopayID,
		AccountID: sql.NullInt64{Int64: accountID, Valid: true},
	})
	if err != nil {
		r.logger.WithContext(ctx).Errorf("UpdateAccountID failed: %v", err)
		return errors.Wrap(err, "UpdateAccountID failed")
	}
	return nil
}

func (r *onboardingRepo) UpdateAccountIDAfterBound(
	ctx context.Context, zalopayID int64,
	onboardingID int64, accountID int64) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("UpdateAccountIDAfterBound")
	logger := r.logger.WithContext(ctx)

	onboarding, err := r.GetOnboardingByIDAndUserID(ctx, zalopayID, onboardingID)
	if err != nil {
		logger.Errorf("GetOnboardingByIDAndUserID failed: %v", err)
		return errors.Wrap(err, "GetOnboardingByIDAndUserID failed")
	}
	if !onboarding.IsApproved() {
		logger.Errorf("onboarding is not approved")
		return errors.New("onboarding is not approved")
	}

	err = r.repos.Queries(ctx).UpdateAccountID(ctx, &da.UpdateAccountIDParams{
		ID:        onboarding.ID,
		ZalopayID: onboarding.ZalopayID,
		AccountID: sql.NullInt64{Int64: accountID, Valid: true},
	})
	if err != nil {
		logger.Errorf("UpdateAccountID failed: %v", err)
		return errors.Wrap(err, "UpdateAccountID into db failed")
	}
	return nil
}

func (r *onboardingRepo) MarkOnboardingInactive(ctx context.Context, zalopayID, onboardingID int64) error {
	defer r.repos.metrics.MonitoredTime(onboardingRepoName)("MarkOnboardingInactive")

	err := r.repos.Queries(ctx).UpdateStatus(ctx, &da.UpdateStatusParams{
		ID:        onboardingID,
		ZalopayID: zalopayID,
		Status:    da.BindingsStatusInactive,
	})
	if err != nil {
		r.logger.WithContext(ctx).Errorf("UpdateStatus failed: %v", err)
		return errors.Wrap(err, "UpdateStatus failed")
	}
	return nil
}

// ========== HELPER FUNCTIONS ==========
func unmarshalOnboarding(binding *da.Bindings) (*model.Onboarding, error) {
	switch binding.PartnerCode {
	case partner.PartnerCIMB.String():
		return unmarshalCIMBOnboarding(binding)
	default:
		return nil, errors.Errorf("invalid partner")
	}
}

func unmarshalCIMBOnboarding(binding *da.Bindings) (*model.Onboarding, error) {
	cimbPartnerData, err := unmarshalCIMBPartnerData(binding.PartnerData)
	if err != nil {
		return nil, err
	}
	cimbExtraProfile, err := unmarshalCIMBUserExtraProfile(binding.ExtraProfile)
	if err != nil {
		return nil, err
	}

	icImages := icImagesFromEntityICImages(binding.IcImages)
	genderNum := zutils.GenderConvertToInt(binding.Gender.String)
	kycUpdatedTime := time.UnixMilli(cimbExtraProfile.KycUpdatedTimeMs)
	kycUpdatedTimeStr := kycUpdatedTime.Format(time.RFC3339)

	refAccountID := int64(-1)
	if binding.AccountID.Valid {
		refAccountID = binding.AccountID.Int64
	}

	onboardingStep, err := model.StepFromString(binding.CurrentStep)
	if err != nil {
		return nil, err
	}

	onboardingStatus, err := statusFromEntity(binding.Status)
	if err != nil {
		return nil, err
	}

	userInfo := &cimb.UserOnboardingInfo{
		UserProfile: model.UserProfile{
			Gender:               genderNum,
			FullName:             binding.FullName,
			Birthday:             binding.Birthday.String,
			PhoneNumber:          binding.PhoneNumber,
			IdNumber:             binding.IDNumber,
			IdIssueDate:          binding.IDIssuedDate.String,
			IdIssuePlace:         binding.IDIssuedLocation.String,
			KycUpdatedDate:       kycUpdatedTimeStr,
			PermanentAddress:     binding.PermanentAddress.String,
			TempResidenceAddress: binding.TempResidenceAddress.String,
		},
		UserOnboardingExtra: cimb.UserOnboardingExtra{
			City:                cimbExtraProfile.City,
			Education:           cimbExtraProfile.Education,
			Occupation:          cimbExtraProfile.Occupation,
			JobTitle:            cimbExtraProfile.JobTitle,
			EmploymentStatus:    cimbExtraProfile.EmploymentStatus,
			SourceOfFund:        cimbExtraProfile.SourceOfFund,
			MonthlyIncome:       cimbExtraProfile.MonthlyIncome,
			LoanPurpose:         cimbExtraProfile.LoanPurpose,
			OverdraftLimit:      cimbExtraProfile.OverdraftLimit,
			KycUpdatedTimeMs:    cimbExtraProfile.KycUpdatedTimeMs,
			FaceChallengeID:     cimbExtraProfile.FaceChallengeID,
			FaceChallengeImg:    cimbExtraProfile.FaceChallengeImg,
			FaceChallengeTimeMs: cimbExtraProfile.FaceChallengeTimeMs,
			RiskWhitelistMethod: cimbExtraProfile.RiskWhitelistMethod,
			NFCSource:           cimbExtraProfile.NFCSource,
		},
	}

	partnerData := &cimb.PartnerOnboardingData{
		BankRequestID:         cimbPartnerData.BankRequestID,
		BankLinkingRequestID:  cimbPartnerData.BankLinkingRequestID,
		SignOTPRequestID:      cimbPartnerData.SignOTPRequestID,
		LinkOTPRequestID:      cimbPartnerData.LinkOTPRequestID,
		ODStatus:              cimbPartnerData.ODStatus,
		CasaStatus:            cimbPartnerData.CasaStatus,
		SigningStatus:         cimbPartnerData.SigningStatus,
		SigningImage:          cimbPartnerData.SigningImage,
		LinkingStatus:         cimbPartnerData.LinkingStatus,
		ErrorDetail:           cimbPartnerData.ErrorDetail,
		ManualApprovalReasons: cimbPartnerData.ManualApprovalReasons,
	}

	return &model.Onboarding{
		ID:          binding.ID,
		ZalopayID:   binding.ZalopayID,
		AccountID:   refAccountID,
		Status:      onboardingStatus,
		PartnerCode: binding.PartnerCode,
		IcImages:    icImages,
		UserInfo:    userInfo,
		PartnerData: partnerData,
		CurrentStep: onboardingStep,
		RejectCode:  binding.RejectCode,
		PartnerName: binding.PartnerCode,
		CreatedAt:   binding.CreatedAt,
		UpdatedAt:   binding.UpdatedAt,
	}, nil
}

func unmarshalCIMBPartnerData(data []byte) (*cimb.PartnerOnboardingData, error) {
	var result cimb.PartnerOnboardingData
	err := jsoniter.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func unmarshalCIMBUserExtraProfile(data []byte) (*cimb.UserOnboardingExtra, error) {
	var result cimb.UserOnboardingExtra
	err := jsoniter.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func icImagesToEntityICImages(icImages model.ICImage) entity.ICImages {
	if icImages.IsEmpty() {
		return entity.ICImages{}
	}
	return entity.ICImages{
		BackIC:   imageToEntity(icImages.BackIC),
		FrontIC:  imageToEntity(icImages.FrontIC),
		SelfieIC: imageToEntity(icImages.SelfieIC),
	}
}

func icImagesFromEntityICImages(icImages entity.ICImages) model.ICImage {
	if icImages.IsEmpty() {
		return model.ICImage{}
	}
	return model.ICImage{
		SelfieIC: imageFromEntity(icImages.Avatar),
		FrontIC:  imageFromEntity(icImages.FrontIC),
		BackIC:   imageFromEntity(icImages.BackIC),
	}

}

func imageFromEntity(image entity.Image) *model.Image {
	if image.IsEmpty() {
		return nil
	}
	return &model.Image{
		Path:        image.Path,
		Checksum:    image.Checksum,
		ContentType: image.ContentType,
	}
}

func imageToEntity(image *model.Image) entity.Image {
	if image == nil {
		return entity.Image{}
	}
	return entity.Image{
		Path:        image.Path,
		Checksum:    image.Checksum,
		ContentType: image.ContentType,
	}
}

func statusFromEntity(status da.BindingsStatus) (model.OnboardingStatus, error) {
	switch status {
	case da.BindingsStatusActive:
		return model.OnboardingStatusActive, nil
	case da.BindingsStatusInactive:
		return model.OnboardingStatusInactive, nil
	default:
		return model.OnboardingStatusUnknown, errors.Errorf("invalid status")
	}
}
