package repo

import (
	"context"
	"database/sql"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.zalopay.vn/fin/platform/common/s3storage"
	tempCli "go.temporal.io/sdk/client"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo/da"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
)

var ProviderSet = wire.NewSet(
	NewRepository,
	NewTransaction,
	NewUsersRepo,
	NewOnboardingRepo,
)

type Repository struct {
	sqlDB    *sql.DB
	logger   *log.Helper
	metrics  *metrics.RepositoryMetrics
	objStore s3storage.Client
	temporal tempCli.Client
}

type ctxTxKey struct{}

const (
	usersRepoName      = "users"
	parnerRepoName     = "partners"
	onboardingRepoName = "onboarding"
)

func NewRepository(sqlDB *sql.DB, objStore s3storage.Client,
	temporal tempCli.Client, kLogger log.Logger) *Repository {
	logger := log.NewHelper(log.With(kLogger, "module", "repository"))
	return &Repository{
		sqlDB:    sqlDB,
		logger:   logger,
		objStore: objStore,
		temporal: temporal,
		metrics:  metrics.NewRepositoryMetrics(metrics.Options{Module: metrics.MetricModuleRepository}),
	}
}

func NewTransaction(d *Repository) _interface.Transaction {
	return d
}

func (r *Repository) BeginTx(ctx context.Context) (context.Context, error) {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return nil, err
	}
	return context.WithValue(ctx, ctxTxKey{}, tx), nil
}

func (r *Repository) CommitTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Commit()
}

func (r *Repository) RollbackTx(ctx context.Context) error {
	tx := getTxFromCtx(ctx)
	if tx == nil {
		return nil
	}
	return tx.Rollback()
}

func (r *Repository) WithTx(ctx context.Context, exec func(ctx context.Context) error) error {
	tx, err := r.sqlDB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	err = exec(context.WithValue(ctx, ctxTxKey{}, tx))
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func getTxFromCtx(ctx context.Context) *sql.Tx {
	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if !ok {
		return nil
	}
	return tx
}

func (r *Repository) Queries(ctx context.Context) *da.Queries {
	tx, ok := ctx.Value(ctxTxKey{}).(*sql.Tx)
	if ok {
		return da.New(tx)
	}
	return da.New(r.sqlDB)
}
