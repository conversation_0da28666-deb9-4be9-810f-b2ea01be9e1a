// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package worker

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/image_fetcher"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/job_task"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/minio_storage"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_challenge"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service/scheduler"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(onboarding *config.Onboarding, logger log.Logger) (*kratos.App, func(), error) {
	client, cleanup, err := base.InitTemporalClient(onboarding)
	if err != nil {
		return nil, nil, err
	}
	cacheNoCaller, cleanup2, err := base.InitRedisCache(onboarding)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	handler := maintenance.NewMaintenance(cacheNoCaller, logger)
	db, cleanup3, err := base.InitSQLDatabase(onboarding, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	s3storageClient := base.InitMinioStorage(onboarding)
	repository := repo.NewRepository(db, s3storageClient, client, logger)
	onboardingRepo := repo.NewOnboardingRepo(repository, logger)
	jobTaskMgmt := job_task.NewJobTaskMgmt(onboarding, client, logger)
	transaction := repo.NewTransaction(repository)
	cimbConnectorClient, cleanup4, err := base.InitCIMBConnectorClient(onboarding, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	commonHttpClient := base.InitCommonHttpClient(onboarding)
	cimbService := cimb_service.NewService(onboarding, cimbConnectorClient, commonHttpClient, logger)
	kycImageHttpClient := base.InitKYCImageHttpClient(onboarding)
	imageFetcher := image_fetcher.NewImageFetcher(kycImageHttpClient)
	imageValidator := validator.NewImageValidator()
	minioUploader := minio_storage.NewMinioUpload(s3storageClient, kycImageHttpClient)
	contractUsecase := usecase.NewContractUsecase(logger, onboardingRepo, jobTaskMgmt, transaction, cimbService, imageFetcher, imageValidator, minioUploader)
	userFaceAuthenServiceClient, cleanup5, err := base.InitFaceAuthClient(onboarding, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userChallengeService := user_challenge.NewClient(userFaceAuthenServiceClient, onboarding, logger)
	faceAuthUsecase := usecase.NewFaceAuthUsecase(logger, onboardingRepo, transaction, minioUploader, userChallengeService, onboarding)
	accountClient, cleanup6, err := base.InitAccountServiceClient(onboarding, logger)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountService := account_service.NewClient(accountClient, logger)
	onboardingUsecase := usecase.NewOnboardingUsecase(logger, onboardingRepo, transaction, jobTaskMgmt, cimbService, accountService)
	schedulerService := scheduler.NewWorkflowService(onboarding, client, handler, logger, contractUsecase, faceAuthUsecase, onboardingUsecase)
	temporalWorker := server.NewTemporalWorker(onboarding, logger, schedulerService, client)
	workerHTTPServer := server.NewWorkerHTTPServer(onboarding, logger)
	app := newApp(logger, temporalWorker, workerHTTPServer)
	return app, func() {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
