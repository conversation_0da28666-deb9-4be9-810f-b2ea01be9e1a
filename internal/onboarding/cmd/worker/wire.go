//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package worker

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// wireApp init kratos application.
func wireApp(*config.Onboarding, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		base.ProviderSet,
		repo.ProviderSet,
		adapters.ProviderSet,
		usecase.ProviderSet,
		service.ProviderSet,
		server.ProviderSet,
		maintenance.ProviderSet,
		newApp))
}
