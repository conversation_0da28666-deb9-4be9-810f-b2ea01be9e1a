package cmd

import (
	"github.com/spf13/cobra"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/onboarding"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/worker"
)

var rootCmd = &cobra.Command{
	Use:              "onboarding",
	Short:            "onboarding service",
	RunE:             runCmd,
	TraverseChildren: true,
}

func NewCmd() *cobra.Command {
	rootCmd.AddCommand(worker.NewCmd())
	rootCmd.AddCommand(onboarding.NewCmd())
	return rootCmd
}

func runCmd(cmd *cobra.Command, args []string) error {
	defaultCmd := onboarding.NewCmd()
	return defaultCmd.RunE(cmd, args)
}
