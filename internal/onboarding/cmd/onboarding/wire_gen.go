// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package onboarding

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/image_fetcher"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/job_task"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/minio_storage"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/rate_limiter"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/risk_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_challenge"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_profile"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/whitelist"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/repo"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/server"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/validator"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/maintenance"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(onboarding *config.Onboarding, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := base.InitSQLDatabase(onboarding, logger)
	if err != nil {
		return nil, nil, err
	}
	client := base.InitMinioStorage(onboarding)
	clientClient, cleanup2, err := base.InitTemporalClient(onboarding)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	repository := repo.NewRepository(db, client, clientClient, logger)
	onboardingRepo := repo.NewOnboardingRepo(repository, logger)
	jobTaskMgmt := job_task.NewJobTaskMgmt(onboarding, clientClient, logger)
	transaction := repo.NewTransaction(repository)
	cimbConnectorClient, cleanup3, err := base.InitCIMBConnectorClient(onboarding, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	commonHttpClient := base.InitCommonHttpClient(onboarding)
	cimbService := cimb_service.NewService(onboarding, cimbConnectorClient, commonHttpClient, logger)
	kycImageHttpClient := base.InitKYCImageHttpClient(onboarding)
	imageFetcher := image_fetcher.NewImageFetcher(kycImageHttpClient)
	imageValidator := validator.NewImageValidator()
	minioUploader := minio_storage.NewMinioUpload(client, kycImageHttpClient)
	contractUsecase := usecase.NewContractUsecase(logger, onboardingRepo, jobTaskMgmt, transaction, cimbService, imageFetcher, imageValidator, minioUploader)
	accountClient, cleanup4, err := base.InitAccountServiceClient(onboarding, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	accountService := account_service.NewClient(accountClient, logger)
	onboardingUsecase := usecase.NewOnboardingUsecase(logger, onboardingRepo, transaction, jobTaskMgmt, cimbService, accountService)
	operationService := service.NewOperationService(contractUsecase, onboardingUsecase, logger)
	usersRepo := repo.NewUsersRepo(repository, logger)
	redisKeyGenerator, cleanup5, err := base.InitRedisKeyGenerator(onboarding)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	cacheNoCaller, cleanup6, err := base.InitRedisCache(onboarding)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	rateLimiter, err := rate_limiter.NewClient(cacheNoCaller, logger)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	distributedLock := dist_lock.NewDistLock(cacheNoCaller, logger)
	fraudSystem, cleanup7, err := base.InitRiskSystemClient(onboarding, logger)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	creditSystem, cleanup8, err := base.InitCreditScoreClient(onboarding, logger)
	if err != nil {
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	fraudService := risk_system.NewClient(onboarding, fraudSystem, creditSystem, logger)
	userProfileClient, cleanup9, err := base.InitUserProfileClient(onboarding, logger)
	if err != nil {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userEkycNfcClient, cleanup10, err := base.InitUserKycNfcClient(onboarding, logger)
	if err != nil {
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	eKycCenterServiceClient, cleanup11, err := base.InitEkycCenterGrpcConn(onboarding, logger)
	if err != nil {
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userProfileService := user_profile.NewClient(onboarding, userProfileClient, userEkycNfcClient, eKycCenterServiceClient, logger)
	abPublicServiceClient, cleanup12, err := base.InitAbPlatformServiceClient(onboarding, logger)
	if err != nil {
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	whitelistSys := whitelist.NewClient(abPublicServiceClient, onboarding, logger)
	userFaceAuthenServiceClient, cleanup13, err := base.InitFaceAuthClient(onboarding, logger)
	if err != nil {
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	userChallengeService := user_challenge.NewClient(userFaceAuthenServiceClient, onboarding, logger)
	profileValidator := validator.NewProfileValidator()
	nfcInfoValidator := validator.NewNfcInfoValidator()
	profileUsecase := usecase.NewProfileUsecase(logger, usersRepo, onboardingRepo, transaction, jobTaskMgmt, redisKeyGenerator, rateLimiter, distributedLock, fraudService, cimbService, userProfileService, whitelistSys, userChallengeService, minioUploader, profileValidator, nfcInfoValidator)
	faceAuthUsecase := usecase.NewFaceAuthUsecase(logger, onboardingRepo, transaction, minioUploader, userChallengeService, onboarding)
	deepLinkCraft := utils.NewDeepLinkCraft(onboarding)
	ctaCrafter := utils.NewCTACraft(deepLinkCraft)
	messageBuilder := utils.NewMessageBuilder()
	onboardingService := service.NewOnboardingService(profileUsecase, faceAuthUsecase, contractUsecase, onboardingUsecase, ctaCrafter, messageBuilder, logger)
	handler := maintenance.NewMaintenance(cacheNoCaller, logger)
	grpcServer := server.NewGRPCServer(onboarding, logger, operationService, onboardingService, handler)
	sessionServiceClient, cleanup14, err := base.InitSessionGrpcConn(onboarding, logger)
	if err != nil {
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	authenticator := auth_service.NewClient(sessionServiceClient, logger)
	httpServer := server.NewHTTPServer(onboarding, logger, onboardingService, authenticator, handler)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
