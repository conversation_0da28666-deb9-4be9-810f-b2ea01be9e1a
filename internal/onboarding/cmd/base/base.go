package base

import (
	"database/sql"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/extra/redisotel/v8"
	_ "github.com/go-sql-driver/mysql"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"
	"gitlab.zalopay.vn/fin/platform/common/redis"
	"gitlab.zalopay.vn/fin/platform/common/s3storage"
	"gitlab.zalopay.vn/fin/platform/common/workflowengine/temporalclient"
	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
	"gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1"
	"gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	tempCli "go.temporal.io/sdk/client"
	"go.temporal.io/sdk/contrib/opentelemetry"
	"google.golang.org/grpc"

	"github.com/XSAM/otelsql"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	abplatform "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ab_platform"
	ekyccenterv2 "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc"
	userProfile "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile"
	zpBase "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/zp_base"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/risk_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/bootstrap"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/codec"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/metrics"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/uberfx"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

var ProviderSet = wire.NewSet(
	InitSessionGrpcConn,
	InitFaceAuthClient,
	InitUserKycNfcClient,
	InitUserProfileClient,
	InitEkycCenterGrpcConn,
	InitCIMBConnectorClient,
	InitAccountServiceClient,
	InitAbPlatformServiceClient,
	InitRiskSystemClient,
	InitCreditScoreClient,
	InitMinioStorage,
	InitCommonHttpClient,
	InitKYCImageHttpClient,
	InitSQLDatabase,
	InitTemporalClient,
	InitRedisCache,
	InitRedisKeyGenerator,
)

type CommonHttpClient httpclient.Client

type KycImageHttpClient httpclient.Client

func InitSessionGrpcConn(config *config.Onboarding, logger log.Logger) (sessionv1.SessionServiceClient, func(), error) {
	sessConfig := config.GetAdapters().GetSession()
	log.Info("InitSessionGrpcConn connecting..., target: ", sessConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: sessConfig.GetAddress(),
			Timeout: sessConfig.GetTimeout().AsDuration(),
			Secured: sessConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitSessionGrpcConn, err: ", err, ", target: ", config.GetAdapters().GetSession().GetAddress())
		return nil, nil, err
	}

	log.Info("InitSessionGrpcConn success, target: ", config.GetAdapters().GetSession().GetAddress())

	return sessionv1.NewSessionServiceClient(conn), func() { _ = conn.Close() }, nil
}

func InitUserProfileClient(config *config.Onboarding, logger log.Logger) (userProfile.UserProfileClient, func(), error) {
	upConfig := config.GetAdapters().GetUserProfile()
	log.Info("InitUserProfileClient is starting..., target: ", upConfig.GetAddress())

	codec := codec.NewRequestLogCodec(
		[]string{userProfile.UserProfile_QueryByUserId_FullMethodName},
		[]string{"basic_profile", "identity_profile"},
	)
	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: upConfig.GetAddress(),
			Timeout: upConfig.GetTimeout().AsDuration(),
			Secured: upConfig.GetSecured(),
			Codec:   codec,
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliCredsInterceptor(upConfig.GetClientId(), upConfig.GetClientKey()),
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitUserProfileClient error", err)
		return nil, nil, err
	}

	log.Info("InitUserProfileClient success, target: ", upConfig.GetAddress())

	return userProfile.NewUserProfileClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitUserKycNfcClient(config *config.Onboarding, logger log.Logger) (userekycnfcv1.UserEkycNfcClient, func(), error) {
	ukConfig := config.GetAdapters().GetEkycNfc()
	log.Info("InitUserKycNfcClient is starting..., target: ", ukConfig.GetAddress())

	codec := codec.NewRequestLogCodec(
		[]string{userekycnfcv1.UserEkycNfc_GetNFC_FullMethodName},
		[]string{"dg13", "ds_cert", "nfc_raw"},
	)
	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: ukConfig.GetAddress(),
			Timeout: ukConfig.GetTimeout().AsDuration(),
			Secured: ukConfig.GetSecured(),
			Logger:  logger,
			Codec:   codec,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliCredsInterceptor(ukConfig.GetClientId(), ukConfig.GetClientKey()),
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitUserKycNfcClient error", err)
		return nil, nil, err
	}

	log.Info("InitUserKycNfcClient success, target: ", ukConfig.GetAddress())

	return userekycnfcv1.NewUserEkycNfcClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitFaceAuthClient(config *config.Onboarding, logger log.Logger) (userfaceauthenv1.UserFaceAuthenServiceClient, func(), error) {
	faceAuthConfig := config.GetAdapters().GetFaceAuth()
	log.Info("InitFaceAuthClient is starting..., target: ", faceAuthConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: faceAuthConfig.GetAddress(),
			Timeout: faceAuthConfig.GetTimeout().AsDuration(),
			Secured: faceAuthConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliCredsInterceptor(faceAuthConfig.GetClientId(), faceAuthConfig.GetClientKey()),
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitFaceAuthClient error", err)
		return nil, nil, err
	}

	log.Info("InitFaceAuthClient success, target: ", faceAuthConfig.GetAddress())

	return userfaceauthenv1.NewUserFaceAuthenServiceClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitEkycCenterGrpcConn(config *config.Onboarding, logger log.Logger) (ekyccenterv2.EKycCenterServiceClient, func(), error) {
	ekycConfig := config.GetAdapters().GetEkycCenter()
	log.Info("InitEkycCenterGrpcConn is starting..., target: ", ekycConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: ekycConfig.GetAddress(),
			Timeout: ekycConfig.GetTimeout().AsDuration(),
			Secured: ekycConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitEkycCenterGrpcConn error", err)
		return nil, nil, err
	}

	log.Info("InitEkycCenterGrpcConn success, target: ", ekycConfig.GetAddress())

	return ekyccenterv2.NewEKycCenterServiceClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitCIMBConnectorClient(config *config.Onboarding, logger log.Logger) (connector.CIMBConnectorClient, func(), error) {
	cimbConfig := config.GetAdapters().GetCimbConnector()
	log.Info("InitCIMBConnectorClient is starting..., target: ", cimbConfig.GetAddress())

	codec := codec.NewRequestLogCodec(
		[]string{
			connector.CIMBConnector_CheckOnboardingPermission_FullMethodName,
			connector.CIMBConnector_SubmitCASAOnboardingV2_FullMethodName,
			connector.CIMBConnector_SubmitCASAOnboardingV3_FullMethodName,
			connector.CIMBConnector_UploadSelfie_FullMethodName,
		},
		[]string{"id_number", "phone_number", "ic_nfc_chip", "documents", "location_data", "device_info", "url"},
	)
	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: cimbConfig.GetAddress(),
			Timeout: cimbConfig.GetTimeout().AsDuration(),
			Secured: cimbConfig.GetSecured(),
			Logger:  logger,
			Codec:   codec,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliCredsInterceptor(cimbConfig.GetClientId(), cimbConfig.GetClientKey()),
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
			bootstrap.WithMetadataAppendInterceptor(
				common.MetadataKey_METADATA_PRODUCT_LINE.String(),
				common.ProductLine_PRODUCT_INSTALLMENT.String(),
			),
		),
	)

	if err != nil {
		log.Error("InitCIMBConnectorClient error", err)
		return nil, nil, err
	}

	log.Info("InitCIMBConnectorClient success, target: ", cimbConfig.GetAddress())

	return connector.NewCIMBConnectorClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitRiskSystemClient(config *config.Onboarding, logger log.Logger) (risk_system.FraudSystem, func(), error) {
	riskConfig := config.GetAdapters().GetRiskSystem()
	log.Info("InitRiskSystemClient is starting..., target: ", riskConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: riskConfig.GetAddress(),
			Timeout: riskConfig.GetTimeout().AsDuration(),
			Secured: riskConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitRiskSystemClient error", err)
		return nil, nil, err
	}

	log.Info("InitRiskSystemClient success, target: ", riskConfig.GetAddress())

	return zpBase.NewZPBaseClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitCreditScoreClient(config *config.Onboarding, logger log.Logger) (risk_system.CreditSystem, func(), error) {
	creditConfig := config.GetAdapters().GetCreditScore()
	log.Info("InitCreditScoreClient is starting..., target: ", creditConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: creditConfig.GetAddress(),
			Timeout: creditConfig.GetTimeout().AsDuration(),
			Secured: creditConfig.GetSecured(),
			Logger:  logger,
		},
		grpc.WithChainUnaryInterceptor(
			bootstrap.WithCliTraceFromCtxInterceptor(zutils.TraceIDFromCtx),
		),
	)

	if err != nil {
		log.Error("InitCreditScoreClient error", err)
		return nil, nil, err
	}

	log.Info("InitCreditScoreClient success, target: ", creditConfig.GetAddress())

	return zpBase.NewZPBaseClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitMinioStorage(config *config.Onboarding) s3storage.Client {
	log.Infow("msg", "InitMinioStorage", "s3_config", config.GetData().GetS3Storage())

	s3Client := s3storage.NewMinioStorage(&s3storage.Config{
		Host:       config.GetData().GetS3Storage().GetHost(),
		Region:     config.GetData().GetS3Storage().GetRegion(),
		BucketName: config.GetData().GetS3Storage().GetBucketName(),
		AccessKey:  config.GetData().GetS3Storage().GetAccessKey(),
		SecretKey:  config.GetData().GetS3Storage().GetSecretKey(),
		UseProxy:   config.GetData().GetS3Storage().GetUseProxy(),
		ProxyURL:   config.GetData().GetS3Storage().GetProxyUrl(),
		EnableSSL:  config.GetData().GetS3Storage().GetEnableSsl(),
	}, &metrics.ClientNoop{})

	return s3Client
}

func InitCommonHttpClient(config *config.Onboarding) CommonHttpClient {
	return httpclient.NewClient(&httpclient.Config{
		BaseURL:  config.GetAdapters().GetHttpCommon().BaseUrl,
		ProxyURL: config.GetAdapters().GetHttpCommon().GetProxyUrl(),
		Timeout:  time.Second * 30,
	}, &metrics.ClientNoop{})
}

func InitKYCImageHttpClient(config *config.Onboarding) KycImageHttpClient {
	return httpclient.NewClient(&httpclient.Config{
		BaseURL: config.GetAdapters().GetKycImage().GetBaseUrl(),
		Timeout: config.GetAdapters().GetKycImage().GetTimeout().AsDuration(),
		Retries: int(config.GetAdapters().GetKycImage().GetRetries()),
	}, &metrics.ClientNoop{})
}

func InitSQLDatabase(conf *config.Onboarding, kLogger log.Logger) (*sql.DB, func(), error) {
	logger := log.NewHelper(kLogger)
	dbConf := conf.GetData().GetDatabase()
	dbSource := zutils.FormatDBSource(dbConf.GetSource())

	db, err := otelsql.Open(
		dbConf.GetDriver(), dbSource,
		otelsql.WithSQLCommenter(true),
		otelsql.WithAttributes(semconv.DBSystemMySQL))
	if err != nil {
		logger.Error("Open database error", err)
		return nil, nil, err
	}

	err = otelsql.RegisterDBStatsMetrics(db, otelsql.WithAttributes(semconv.DBSystemMySQL))
	if err != nil {
		logger.Error("RegisterDBStatsMetrics error", err)
		return nil, nil, err
	}

	db.SetMaxOpenConns(int(dbConf.GetMaxOpenConn()))
	db.SetMaxIdleConns(int(dbConf.GetMaxIdleConn()))
	db.SetConnMaxLifetime(dbConf.GetConnMaxLifetime().AsDuration())

	cleanup := func() {
		_ = db.Close()
	}
	return db, cleanup, nil
}

func InitTemporalClient(config *config.Onboarding) (tempClient tempCli.Client, cleanup func(), err error) {
	tempConf := &temporalclient.Config{
		HostPort:  config.GetAdapters().GetTemporal().GetAddress(),
		Namespace: config.GetAdapters().GetTemporal().GetNamespace(),
		EnableTls: true,
	}

	otelOption := func(options tempCli.Options) tempCli.Options {
		tracerOpts := opentelemetry.TracerOptions{}
		otelIntercept, tErr := opentelemetry.NewTracingInterceptor(tracerOpts)
		if tErr != nil {
			return options
		}

		interceptors := options.Interceptors
		interceptors = append(interceptors, otelIntercept)
		options.Interceptors = interceptors
		return options
	}

	client, err := temporalclient.NewClient(tempConf, otelOption)
	if err != nil {
		return nil, nil, err
	}

	return client, client.Close, nil
}

func InitAccountServiceClient(config *config.Onboarding, logger log.Logger) (v1.AccountClient, func(), error) {
	accountConfig := config.GetAdapters().GetAccountService()
	log.Info("InitAccountServiceClient is starting..., target: ", accountConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: accountConfig.GetAddress(),
			Timeout: accountConfig.GetTimeout().AsDuration(),
			Secured: accountConfig.GetSecured(),
			Logger:  logger,
		},
	)

	if err != nil {
		log.Error("InitAccountServiceClient error", err)
		return nil, nil, err
	}

	log.Info("InitAccountServiceClient success, target: ", accountConfig.GetAddress())

	return v1.NewAccountClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitAbPlatformServiceClient(config *config.Onboarding, logger log.Logger) (abplatform.ABPublicServiceClient, func(), error) {
	abConfig := config.GetAdapters().GetAbPlatform()
	log.Info("InitAbPlatformSystemClient is starting..., target: ", abConfig.GetAddress())

	conn, err := bootstrap.InitGrpcConn(
		&bootstrap.GrpcConfig{
			Address: abConfig.GetAddress(),
			Timeout: abConfig.GetTimeout().AsDuration(),
			Secured: abConfig.GetSecured(),
			Logger:  logger,
		},
	)

	if err != nil {
		log.Error("InitAbPlatformSystemClient error", err)
		return nil, nil, err
	}

	log.Info("InitAbPlatformSystemClient success, target: ", abConfig.GetAddress())

	return abplatform.NewABPublicServiceClient(conn), func() {
		_ = conn.Close()
	}, nil
}

func InitRedisCache(config *config.Onboarding) (redis.CacheNoCaller, func(), error) {
	redisConfig := config.GetData().GetRedisCache()
	log.Info("InitRedisCache is starting..., target: ", redisConfig.GetAddress())

	cacheConf := &redis.Config{
		Addr:             []string{redisConfig.GetAddress()},
		PoolSize:         int(redisConfig.GetPoolSize()),
		MinIdleConn:      int(redisConfig.GetMinIdleConn()),
		MaxConnAge:       time.Minute * 5,
		MasterName:       redisConfig.GetMasterName(),
		Password:         redisConfig.GetPassword(),
		Username:         redisConfig.GetUsername(),
		SentinelUsername: redisConfig.GetUsername(),
		SentinelPassword: redisConfig.GetPassword(),
	}
	redisCache := redis.CreateRedisCache(&uberfx.NoopLifeCycle{}, cacheConf)
	redisClient := redisCache.GetRedisClient()
	redisClient.AddHook(redisotel.NewTracingHook())

	log.Info("InitRedisCache success, target: ", redisConfig.GetAddress())

	return redisCache, func() {
		if redisClient == nil {
			return
		}
		_ = redisClient.Close()
	}, nil
}

func InitRedisKeyGenerator(config *config.Onboarding) (keygen.RedisKeyGenerator, func(), error) {
	appConfig := config.GetApp()
	keyGenerator, err := keygen.NewRedisKeyGenerator(appConfig.GetEnv(), appConfig.GetName())
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create redis key generator")
	}
	return keyGenerator, func() {}, nil
}
