package model

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"
)

const (
	InitOnboardID = int64(0)
)

type Onboarding struct {
	ID          int64                 `json:"id"`
	ZalopayID   int64                 `json:"zalopay_id"`
	AccountID   int64                 `json:"account_id"` // Reference to account table
	Status      OnboardingStatus      `json:"status"`
	PartnerCode string                `json:"partner_code"`
	PartnerName string                `json:"partner_name"`
	IcImages    ICImage               `json:"ic_images"`
	UserInfo    UserOnboardingInfo    `json:"user_info"`
	PartnerData PartnerOnboardingData `json:"partner_data"`
	CurrentStep OnboardingStep        `json:"current_step"`
	RejectCode  string                `json:"reject_code"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
}

// RejectInfo is used to store the reject code and reject description
type RejectInfo struct {
	RejectCode  string `json:"reject_code"`
	RejectDesc  string `json:"reject_desc"`
	CanResubmit bool   `json:"can_resubmit"`
	NeedUpdKyc  bool   `json:"need_upd_kyc"`
}

type UpdateOnboardingFunc func(context.Context, *Onboarding) (*Onboarding, error)

type PartnerOnboardingData interface {
	GetStatus() string
	BuildRejection() *RejectInfo
	GetRejectDesc() string
	// GetRawRejectCode is used to get the reject code in the raw format
	GetRawRejectCode() string
	// GetDomainRejectCode is used to get the reject code in the main domain format
	GetDomainRejectCode() string
	GetPartnerStatus() string
	SetPartnerRequestID(string)
	GetPartnerRequestID() string
	GetLinkingRequestID() string
	SetPartnerTransactionID(string)
	GetPartnerTransactionID() string
	GetLinkingTransactionID() string
	SetSignatureImageUri(url, contentType string)
	CanQueryApprovalStatus() bool
	IsLinkingInit() bool
	IsLinkingSuccess() bool
	IsContractReady() bool
	IsContractSigned() bool
	IsApprovalRejected() bool
	IsApprovalApproved() bool
	IsApprovalProcessing() bool
	MarshalBinary() ([]byte, error)
}

/**
 * UserOnboardingInfo is an interface to define the user information for onboarding
 * Although WithBasicProfile, WithIdentityProfile is a same input, but it's different in the context
 * WithBasicProfile is used to set the basic information of user, such as phone number
 * WithIdentityProfile is used to set the identity information of user, such as full
 */
type UserOnboardingInfo interface {
	GetGender() int32
	GetGenderStr() string
	GetFullName() string
	GetPhoneNumber() string
	GetBirthday() string
	GetIdNumber() string
	GetIdIssueDate() string
	GetIdIssuePlace() string
	GetPermanentAddress() string
	GetTempResidenceAddress() string
	GetFaceChallengeID() string
	GetFaceChallengeImg() *Image
	GetFaceChallengeTime() time.Time
	GetRiskWhitelistMethod() string

	MarshalBinary() ([]byte, error)
	WithBasicProfile(profile *UserProfile) UserOnboardingInfo
	WithIdentityProfile(profile *UserProfile) UserOnboardingInfo
	WithIdentityNfcInfo(nfcInfo *UserKycNfc) UserOnboardingInfo
	WithExtraProfile(userInput *UserAdditionalInfo) UserOnboardingInfo
	WithUnderwritingData(data UserRiskUnderwriting) UserOnboardingInfo
	WithFaceChallenge(fcID string, fcImg *Image, fcTime time.Time) UserOnboardingInfo
	WithFaceChallengeID(faceChallengeID string) UserOnboardingInfo
	WithFaceChallengeImg(faceChallengeImg *Image) UserOnboardingInfo
	// WithFaceChallengeTime is used to set the time when the face challenge is validated/submitted
	WithFaceChallengeTime(faceChallengeTime time.Time) UserOnboardingInfo
}

type ICImage struct {
	SelfieIC *Image `json:"selfie_ic"`
	FrontIC  *Image `json:"front_ic"`
	BackIC   *Image `json:"back_ic"`
}

func (i ICImage) IsEmpty() bool {
	return reflect.DeepEqual(i, ICImage{})
}

type Image struct {
	URL         string `json:"url,omitempty"`
	Path        string `json:"path,omitempty"`
	Checksum    string `json:"checksum,omitempty"`
	DataBuffer  []byte `json:"-"`
	ContentType string `json:"content_type,omitempty"`
}

func (i *Image) IsEmpty() bool {
	return i == nil || (i.URL == "" && i.Path == "")
}

func (i *Image) GetURL() string {
	if i == nil {
		return ""
	}
	return i.URL
}

func (i *Image) GetPath() string {
	if i == nil {
		return ""
	}
	return i.Path
}

func (i *Image) GetContentType() string {
	if i == nil {
		return ""
	}
	return i.ContentType
}

func (i *Image) GetChecksum() string {
	if i == nil {
		return ""
	}
	return i.Checksum
}

func (i *Image) GetDataBuffer() []byte {
	if i == nil {
		return nil
	}
	return i.DataBuffer
}

func (o *Onboarding) GetUserInfo() UserOnboardingInfo {
	if o.UserInfo == nil {
		return nil
	}
	return o.UserInfo
}

func (o *Onboarding) GetRejection() *RejectInfo {
	return o.PartnerData.BuildRejection()
}

// HasExisted mean that the onboarding has been created in the database, also have positive ID
func (o *Onboarding) HasExisted() bool {
	return o.ID > 0
}

func (o *Onboarding) IsActiveStatus() bool {
	return o.Status == OnboardingStatusActive
}

func (o *Onboarding) IsUnregister() bool {
	return o.CurrentStep.IsUnregistered()
}

func (o *Onboarding) IsOnboarding() bool {
	return !o.IsUnregister() && !o.IsCompleted()
}

func (o *Onboarding) IsApproved() bool {
	return o.CurrentStep.IsApproved() && o.PartnerData.IsApprovalApproved()
}

func (o *Onboarding) IsRejected() bool {
	return o.CurrentStep.IsRejected() && o.PartnerData.IsApprovalRejected()
}

func (o *Onboarding) IsCompleted() bool {
	return o.CurrentStep.IsFinal()
}

func (o *Onboarding) IsWaitingApproval() bool {
	return o.CurrentStep.IsWaitingApproval()
}

func (o *Onboarding) SetCurrentStep(step OnboardingStep) {
	o.CurrentStep = step
}

func (o *Onboarding) SyncPartnerApproval(data PartnerOnboardingData) *Onboarding {
	o.PartnerData = data
	if o.PartnerData.IsApprovalRejected() {
		o.CurrentStep = OnboardingStepRejected
		o.RejectCode = o.PartnerData.GetDomainRejectCode()
	}
	if o.PartnerData.IsApprovalApproved() {
		o.CurrentStep = OnboardingStepApproved
		o.RejectCode = ""
	}
	return o
}

func (o *Onboarding) SetICImagesSet(images *ICImage) {
	o.IcImages = ICImage{
		SelfieIC: images.SelfieIC,
		FrontIC:  images.FrontIC,
		BackIC:   images.BackIC,
	}
}

func (o *Onboarding) SetICImagesParts(selfieIC, frontIC, backIC *Image) {
	o.IcImages = ICImage{
		SelfieIC: selfieIC,
		FrontIC:  frontIC,
		BackIC:   backIC,
	}
}

func (o *Onboarding) SetPartnerData(data PartnerOnboardingData) {
	o.PartnerData = data
}

func (o *Onboarding) GetAllStages() OnboardingStageEnums {
	return AllStages()
}

func (o *Onboarding) GetNextStage() OnboardingProgress {
	progressMap := map[OnboardingStep]OnboardingProgress{
		OnboardingStepInitial:         NewOnboardingProgress(OnboardingStageEnumPermission, OnboardingStageStatusWaiting),
		OnboardingStepNonWhitelist:    NewOnboardingProgress(OnboardingStageEnumPermission, OnboardingStageStatusRejected),
		OnboardingStepIneligible:      NewOnboardingProgress(OnboardingStageEnumPermission, OnboardingStageStatusRejected),
		OnboardingStepEligible:        NewOnboardingProgress(OnboardingStageEnumRegister, OnboardingStageStatusWaiting),
		OnboardingStepProfileSuccess:  NewOnboardingProgress(OnboardingStageEnumFaceChallenge, OnboardingStageStatusWaiting),
		OnboardingStepFaceChallenged:  NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusWaiting),
		OnboardingStepOTPVerified:     NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusWaiting),
		OnboardingStepContractSinging: NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusWaiting),
		OnboardingStepWaitingApproval: NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusWaiting),
		OnboardingStepApproved:        NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusSuccess),
		OnboardingStepRejected:        NewOnboardingProgress(OnboardingStageEnumApproval, OnboardingStageStatusRejected),
	}
	progress, ok := progressMap[o.CurrentStep]
	if !ok {
		return NewOnboardingProgress(OnboardingStageEnumUnknown, OnboardingStageStatusInvalid)
	}
	return progress
}

type BindingInfo struct {
	AccountID    int64
	OnboardingID int64
	Status       BindingStatus
	PartnerCode  partner.PartnerCode
}

type BindingStatus string

const (
	BindStatusUnknown    BindingStatus = "UNKNOWN"
	BindStatusUnbound    BindingStatus = "UNBOUND"
	BindStatusBound      BindingStatus = "BOUND"
	BindStatusOnboarding BindingStatus = "ONBOARDING"
)

func (s BindingStatus) String() string {
	return string(s)
}

type OnboardingPermission struct {
	Allowed    bool
	IsWarning  bool
	RejectCode string
	RejectDesc string
}

func (p OnboardingPermission) GetRejectCodeFmt() string {
	return fmt.Sprintf("PERM:%s", p.RejectCode)
}

type OnboardingStatusTask struct {
	ZalopayID    string
	OnboardingID string
	PartnerReqID string
	CurrentStep  string
}

type UpdatePartnerDataParams struct {
	ZalopayID    int64
	OnboardingID int64
	PartnerCode  partner.PartnerCode
	PartnerData  PartnerOnboardingData
}

func NewOnboarding(zalopayID int64, partnerCode string) *Onboarding {
	return &Onboarding{
		ID:          InitOnboardID,
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode,
	}
}

func NewUnregisterOnboarding(zalopayID int64, partnerCode string) *Onboarding {
	return &Onboarding{
		ID:          InitOnboardID,
		ZalopayID:   zalopayID,
		PartnerCode: partnerCode,
		CurrentStep: OnboardingStepInitial,
	}
}
