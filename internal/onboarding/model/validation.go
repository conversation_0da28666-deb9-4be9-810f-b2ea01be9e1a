package model

type ValidateType string

const (
	ValidateTypeKyc   ValidateType = "KYC"
	ValidateTypeNfc   ValidateType = "NFC"
	ValidateTypeImage ValidateType = "IMAGE"
)

func (t ValidateType) String() string {
	return string(t)
}

type ValidateStatus string

const (
	ValidateResultUnknown  ValidateStatus = "UNKNOWN"
	ValidateResultApproved ValidateStatus = "APPROVED"
	ValidateResultRejected ValidateStatus = "REJECTED"
)

func (s ValidateStatus) String() string {
	return string(s)
}

type ViolationCode string

const (
	ViolationException                     ViolationCode = "UNKNOWN_EXCEPTION"
	ViolationAgeInvalid                    ViolationCode = "AGE_INVALID"
	ViolationAgeNotInRange                 ViolationCode = "AGE_NOT_IN_RANGE"
	ViolationProfileInfoLack               ViolationCode = "PROFILE_INFO_LACK"
	ViolationProfileIDExpired              ViolationCode = "PROFILE_ID_EXPIRED"
	ViolationProfileIDTypeNotAllowed       ViolationCode = "PROFILE_ID_TYPE_NOT_ALLOWED"
	ViolationProfileKycTimeOutdated        ViolationCode = "PROFILE_KYC_TIME_OUTDATED"
	ViolationProfileNameNonAccent          ViolationCode = "PROFILE_NAME_NON_ACCENT"
	ViolationProfileShouldBeUpdated        ViolationCode = "PROFILE_SHOULD_BE_UPDATED"
	ViolationProfileKycNfcMissing          ViolationCode = "PROFILE_KYC_NFC_MISSING"
	ViolationProfileKycNfcRawEmpty         ViolationCode = "PROFILE_KYC_NFC_RAW_EMPTY"
	ViolationProfileKycNfcDg13Emtpy        ViolationCode = "PROFILE_KYC_NFC_DG13_EMPTY"
	ViolationProfileKycNfcDg13Invalid      ViolationCode = "PROFILE_KYC_NFC_DG13_INVALID"
	ViolationProfileKycNfcSourceNotAllowed ViolationCode = "PROFILE_KYC_NFC_SOURCE_NOT_ALLOWED"
	ViolationProfileKycNfcGenderInvalid    ViolationCode = "PROFILE_KYC_NFC_GENDER_INVALID"
	ViolationProfileKycNfcIdEmpty          ViolationCode = "PROFILE_KYC_NFC_ID_EMPTY"
	ViolationImageSizeTooBig               ViolationCode = "IMAGE_SIZE_TOO_BIG"
	ViolationImageNotGrayscale             ViolationCode = "IMAGE_NOT_GRAYSCALE"
	ViolationImageHasSolidColor            ViolationCode = "IMAGE_HAS_SOLID_COLOR"
	ViolationProfileICImageEmpty           ViolationCode = "PROFILE_IC_IMAGE_EMPTY"
)

func (c ViolationCode) String() string {
	return string(c)
}

type ValidateResult struct {
	Type     ValidateType
	Code     ViolationCode
	Status   ValidateStatus
	Required bool
	Metadata map[string]any
}

func (r ValidateResult) IsApproved() bool {
	return r.Status == ValidateResultApproved
}

func (r ValidateResult) IsRejected() bool {
	return r.Status == ValidateResultRejected
}

type ValidateResultSlice []ValidateResult

func (r ValidateResultSlice) HasAbnormalRes() bool {
	for _, res := range r {
		if res.IsRejected() {
			return true
		}
	}
	return false
}
