package model

import (
	"time"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

const (
	MinMaleAge   = 20
	MaxMaleAge   = 60
	MinFemaleAge = 20
	MaxFemaleAge = 55
)

/**
 * UserProfile represents the user profile base on ZaloPay's user profile
 * It includes all profile level info of the user
 * It is only specific to the installment's user profile
 */
type UserProfile struct {
	FullName             string       `json:"full_name"`
	Birthday             string       `json:"birthday"` // Birthday format is "YYYY-MM-DD"
	PhoneNumber          string       `json:"phone_number"`
	Email                string       `json:"email"`
	Gender               int32        `json:"gender"`
	IdType               IDType       `json:"id_type"`
	IdNumber             string       `json:"id_number"`
	IdIssueDate          string       `json:"id_issue_date"` // IssueDate format is "YYYY-MM-DD"
	IdIssuePlace         string       `json:"id_issue_place"`
	IdExpireDate         string       `json:"id_expire_date"` // ExpireDate format is "YYYY-MM-DD"
	ProfileLevel         int32        `json:"-"`
	ProfileImage         ProfileImage `json:"-"`
	KycLevel             int32        `json:"-"`
	KycNfcStatus         KycNfcStatus `json:"-"` // note: this field maybe replaces by UserKycNfc if needed
	KycUpdatedDate       string       `json:"-"`
	PermanentAddress     string       `json:"permanent_address"`
	TempResidenceAddress string       `json:"temp_residence_address"`
}

func (p *UserProfile) IsNfcVerifyBCA() bool {
	return p.KycNfcStatus == KycNfcStatusVerified
}

func (p *UserProfile) GetKycUpdatedTimeMs() int64 {
	t, err := time.Parse(time.RFC3339, p.KycUpdatedDate)
	if err != nil {
		return 0
	}
	return t.UnixMilli()
}

/**
 * UserAdditionalInfo represents the user additional info
 * FundPurpose is the purpose of the fund, eg: for cimb - reference to opening casa purpose
**/
type UserAdditionalInfo struct {
	Education            string
	JobTitle             string
	LivingCity           string
	Occupation           string
	FundPurpose          string
	LoanPurpose          string
	SourceOfFund         string
	MonthlyIncome        string
	OverdraftLimit       int64
	EmploymentStatus     string
	TempResidenceAddress string
}

type UserFaceChallenge struct {
	Approved    bool
	ImageUrl    string
	IsExpired   *bool
	MatchScore  float64
	ValidateAt  time.Time
	ChallengeID string
}

func (r *UserFaceChallenge) HasImage() bool {
	return r != nil && r.ImageUrl != ""
}

func (r *UserFaceChallenge) IsExpiredNow() bool {
	return r == nil || r.IsExpired != nil && *r.IsExpired
}

func (r *UserFaceChallenge) AssessExpiration(expiredIn time.Duration) bool {
	now := time.Now()
	r.IsExpired = new(bool)
	*r.IsExpired = now.Sub(r.ValidateAt) > expiredIn
	return *r.IsExpired
}

type UserKycProgress struct {
	Status KycStatus
}

/**
 * UserKycNfc represents the user eGov-kyc
 * It includes all info about government kyc of the user by id card nfc
 */
type UserKycNfc struct {
	NfcStatus       KycNfcStatus
	NfcDataRaw      KycNfcDataRaw
	NfcDataDG13     KycNfcDataDG13
	NfcDataDG2      string
	NfcSignature    string
	CollectSource   string
	VendorSignature string
	NfcId           string
}

type UserProfileIssue struct {
	Code        string
	ViolateCode ViolationCode
	ViolateData map[string]any
}

type ProfileImage struct {
	AvatarUri  string
	FrontICUri string
	BackICUri  string
}

type ProfileLevel int32

//nolint:stylecheck
const (
	ProfileLevelInvalid ProfileLevel = iota
	ProfileLevel1
	ProfileLevel2
	ProfileLevel3
)

type IDType int32

const (
	IDTypeCMND IDType = iota + 1
	IDTypePassport
	IDTypeCCCD
	IDTypeCMSQ
	IDTypeCCCDChip
	IDTypeCCCDNew
)

type KycStatus int

const (
	KycStatusInvalid KycStatus = iota
	KycStatusProcessing
	KycStatusApproved
	KycStatusRejected
	KycStatusByPass
)

type KycNfcStatus int32

const (
	KycNfcStatusUnknown KycNfcStatus = iota
	KycNfcStatusInvalid
	KycNfcStatusExisted
	KycNfcStatusVerified
)

type KycNfcDataRaw struct {
	Sod  string `json:"sod"`
	Com  string `json:"com"`
	Dg1  string `json:"dg1"`
	Dg2  string `json:"dg2"`
	Dg13 string `json:"dg13"`
	Dg14 string `json:"dg14"`
	Dg15 string `json:"dg15"`
}

func (p KycNfcDataRaw) IsEmpty() bool {
	return p == KycNfcDataRaw{}
}

// KycNfcDataDG13 represents the user eGov-kyc DG13 data, but we only define the fields that we need
type KycNfcDataDG13 struct {
	EIDNumber      string `json:"eid_number"`
	FullName       string `json:"full_name"`
	Birthday       string `json:"birthday"` // Birthday format is "YYYY-MM-DD"
	Gender         int32  `json:"gender"`
	IssueDate      string `json:"issue_date"` // IssueDate format is "YYYY-MM-DD"
	PlaceOrigin    string `json:"place_origin"`
	PlaceResidence string `json:"place_residence"`
	OldIDNumber    string `json:"old_id_number"`
}

func (p KycNfcDataDG13) IsEmpty() bool {
	return p == KycNfcDataDG13{}
}

func (p KycNfcDataDG13) Validate() error {
	return validation.ValidateStruct(&p,
		validation.Field(&p.EIDNumber, validation.Required),
		validation.Field(&p.FullName, validation.Required),
		validation.Field(&p.Gender, validation.Required),
		validation.Field(&p.Birthday, validation.Required),
	)
}
