package cimb

import (
	"time"

	jsoniter "github.com/json-iterator/go"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

var _ model.UserOnboardingInfo = (*UserOnboardingInfo)(nil)

type UserOnboardingInfo struct {
	model.UserProfile
	model.UserKycNfc
	UserOnboardingExtra
	UserUnderwritingData map[string]any
}

type UserOnboardingExtra struct {
	City             string `json:"city"`
	Education        string `json:"education"`
	Occupation       string `json:"occupation"`
	JobTitle         string `json:"job_title"`
	EmploymentStatus string `json:"employment_status"`
	SourceOfFund     string `json:"source_of_fund"`
	MonthlyIncome    string `json:"monthly_income"`
	FundPurpose      string `json:"-"` // only attach and use during submit application process, not need to store in db
	LoanPurpose      string `json:"loan_purpose"`
	OverdraftLimit   int64  `json:"overdraft_limit"`
	KycUpdatedTimeMs int64  `json:"kyc_updated_time_ms"` // it refers to KycUpdatedDate of UserProfile but in millisecond

	/**
	 * Note: May we should group these face challenge fields
	 * into a struct like FaceChallenge struct
	 */
	FaceChallengeID     string      `json:"face_challenge_id"`
	FaceChallengeImg    model.Image `json:"face_challenge_image"`
	FaceChallengeTimeMs int64       `json:"face_challenge_time_ms"`

	RiskWhitelistMethod model.RiskWhitelistMethod `json:"risk_whitelist_method,omitempty"`
	NFCSource           string                    `json:"nfc_source"`
}

func (u *UserOnboardingExtra) MarshalBinary() ([]byte, error) {
	return jsoniter.Marshal(u)
}

func (u *UserOnboardingInfo) WithBasicProfile(profile *model.UserProfile) model.UserOnboardingInfo {
	if profile == nil {
		return u
	}

	u.PhoneNumber = profile.PhoneNumber
	return u
}

func (u *UserOnboardingInfo) WithIdentityProfile(profile *model.UserProfile) model.UserOnboardingInfo {
	if profile == nil {
		return u
	}

	u.FullName = profile.FullName
	u.Gender = profile.Gender
	u.Birthday = profile.Birthday
	u.IdNumber = profile.IdNumber
	u.IdIssueDate = profile.IdIssueDate
	u.IdIssuePlace = profile.IdIssuePlace
	u.IdExpireDate = profile.IdExpireDate
	u.PermanentAddress = profile.PermanentAddress
	u.KycUpdatedDate = profile.KycUpdatedDate
	u.KycUpdatedTimeMs = profile.GetKycUpdatedTimeMs()
	return u
}

func (u *UserOnboardingInfo) WithIdentityNfcInfo(nfcInfo *model.UserKycNfc) model.UserOnboardingInfo {
	if nfcInfo == nil {
		return u
	}
	u.NfcStatus = nfcInfo.NfcStatus
	u.NfcDataRaw = nfcInfo.NfcDataRaw
	u.NfcDataDG2 = nfcInfo.NfcDataDG2
	u.NfcDataDG13 = nfcInfo.NfcDataDG13
	u.NfcSignature = nfcInfo.NfcSignature
	u.CollectSource = nfcInfo.CollectSource
	u.NfcId = nfcInfo.NfcId

	// asign nfc source to onbaording extra struct so that save it into database
	u.UserOnboardingExtra.NFCSource = nfcInfo.CollectSource
	return u
}

func (u *UserOnboardingInfo) WithUnderwritingData(data model.UserRiskUnderwriting) model.UserOnboardingInfo {
	if data.HasError {
		u.RiskWhitelistMethod = model.RiskWhitelistMethodError
		return u
	}
	if !data.HasData {
		u.RiskWhitelistMethod = model.RiskWhitelistMethodWhitelist
		return u
	}
	u.UserUnderwritingData = data.Data
	u.RiskWhitelistMethod = model.RiskWhitelistMethodRealtime
	return u
}

func (u *UserOnboardingInfo) WithExtraProfile(userInput *model.UserAdditionalInfo) model.UserOnboardingInfo {
	if userInput == nil {
		return u
	}
	if u.TempResidenceAddress == "" {
		u.TempResidenceAddress = u.PermanentAddress
	}
	if userInput.TempResidenceAddress != "" {
		u.TempResidenceAddress = userInput.TempResidenceAddress
	}

	u.City = userInput.LivingCity
	u.JobTitle = userInput.JobTitle
	u.Education = userInput.Education
	u.Occupation = userInput.Occupation
	u.FundPurpose = userInput.FundPurpose
	u.LoanPurpose = userInput.LoanPurpose
	u.SourceOfFund = userInput.SourceOfFund
	u.MonthlyIncome = userInput.MonthlyIncome
	u.OverdraftLimit = userInput.OverdraftLimit
	u.EmploymentStatus = userInput.EmploymentStatus
	return u
}

func (u *UserOnboardingInfo) WithFaceChallenge(fcID string, fcImg *model.Image, fcTime time.Time) model.UserOnboardingInfo {
	return u.
		WithFaceChallengeID(fcID).
		WithFaceChallengeImg(fcImg).
		WithFaceChallengeTime(fcTime)
}

func (u *UserOnboardingInfo) WithFaceChallengeID(faceChallengeID string) model.UserOnboardingInfo {
	u.FaceChallengeID = faceChallengeID
	return u
}

func (u *UserOnboardingInfo) WithFaceChallengeImg(faceChallengeImg *model.Image) model.UserOnboardingInfo {
	if faceChallengeImg == nil {
		return u
	}
	u.FaceChallengeImg = *faceChallengeImg
	return u
}

func (u *UserOnboardingInfo) WithFaceChallengeTime(faceChallengeTime time.Time) model.UserOnboardingInfo {
	u.FaceChallengeTimeMs = faceChallengeTime.UnixMilli()
	return u
}

func (u *UserOnboardingInfo) GetGender() int32 {
	if u == nil {
		return 0
	}
	return u.Gender
}

func (u *UserOnboardingInfo) GetGenderStr() string {
	genderValue := u.GetGender()
	return zutils.GenderConvertToString(genderValue)
}

func (u *UserOnboardingInfo) GetFullName() string {
	if u == nil {
		return ""
	}
	return u.FullName
}

func (u *UserOnboardingInfo) GetPhoneNumber() string {
	if u == nil {
		return ""
	}
	return u.PhoneNumber
}

func (u *UserOnboardingInfo) GetBirthday() string {
	if u == nil {
		return ""
	}
	return u.Birthday
}

func (u *UserOnboardingInfo) GetIdNumber() string {
	if u == nil {
		return ""
	}
	return u.IdNumber
}

func (u *UserOnboardingInfo) GetIdIssueDate() string {
	if u == nil {
		return ""
	}
	return u.IdIssueDate
}

func (u *UserOnboardingInfo) GetIdIssuePlace() string {
	if u == nil {
		return ""
	}
	return u.IdIssuePlace
}

func (u *UserOnboardingInfo) GetPermanentAddress() string {
	if u == nil {
		return ""
	}
	return u.PermanentAddress
}

func (u *UserOnboardingInfo) GetTempResidenceAddress() string {
	if u == nil {
		return ""
	}
	return u.TempResidenceAddress
}

func (u *UserOnboardingInfo) GetFaceChallengeID() string {
	return u.FaceChallengeID
}

func (u *UserOnboardingInfo) GetFaceChallengeImg() *model.Image {
	return &u.FaceChallengeImg
}

func (u *UserOnboardingInfo) GetFaceChallengeTime() time.Time {
	return time.UnixMilli(u.FaceChallengeTimeMs)
}

func (u *UserOnboardingInfo) GetRiskWhitelistMethod() string {
	return u.RiskWhitelistMethod.String()
}

func AsUserOnboardingInfo(info model.UserOnboardingInfo) (*UserOnboardingInfo, bool) {
	uoi, ok := info.(*UserOnboardingInfo)
	return uoi, ok
}
