package cimb

/**
 * ErrorCode is detail code return when make an infra call like SQL error, network error, third party error, etc
 * IMPORTANT: Prefix of ErrorCode must be ErrorCode<ExceptionCode>
 */
type ErrorCode string

const (
	// Partner Provider Connector (CIMB, etc...)
	ErrorCodeUnknown              ErrorCode = "UNKNOWN"
	ErrorCodeBadRequest           ErrorCode = "BAD_REQUEST"
	ErrorCodeAgeNotAllow          ErrorCode = "AGE_NOT_ALLOW"
	ErrorCodeRequestIDExist       ErrorCode = "PARTNER_REQUEST_ID_IS_EXISTING"
	ErrorCodeGenderInvalid        ErrorCode = "GENDER_INVALID"
	ErrorCodeICNumberUsed         ErrorCode = "IC_NUMBER_USED"
	ErrorCodePhoneNumberUsed      ErrorCode = "PHONE_NUMBER_USED"
	ErrorCodeEmailOrPhoneUsed     ErrorCode = "EMAIL_OR_PHONE_USED"
	ErrorCodeNotAllowUpdate       ErrorCode = "NOT_ALLOW_UPDATION"
	ErrorCodeDuplicateReqID       ErrorCode = "DUPLICATED_REQUEST_ID"
	ErrorCodeNfcChipInvalid       ErrorCode = "NFC_CHIP_RAW_DATA_INVALID"
	ErrorCodeCoreReturnFailed     ErrorCode = "ICORE_RETURN_FAIL"
	ErrorCodeSomethingWentWrong   ErrorCode = "SORRY_SOMETHING_WENT_WRONG"
	ErrorCodeOnboardingInfoUsed   ErrorCode = "ONBOARDING_INFORMATION_USED"
	ErrorCodeCurrIdentityNotMatch ErrorCode = "IDENTITY_INFORMATION_NOT_MATCH_WITH_CURRENT"
	ErrorCodeOnboardingProcessing ErrorCode = "ONBOARDING_PROCCED_ALREADY"
	ErrorCodeOnboardingNotAllowed ErrorCode = "ONBOARDING_PRODUCT_NOT_ALLOWED"
	ErrorCodeCustomerExisting     ErrorCode = "EXISTING_CIMB_CUSTOMER_FOUND"
)

func (p ErrorCode) String() string {
	return string(p)
}
