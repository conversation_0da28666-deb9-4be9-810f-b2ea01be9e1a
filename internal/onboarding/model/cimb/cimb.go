package cimb

import (
	"fmt"
	"regexp"
	"slices"
	"strings"

	jsoniter "github.com/json-iterator/go"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

var _ model.PartnerOnboardingData = (*PartnerOnboardingData)(nil)

type PartnerOnboardingData struct {
	BankRequestID         string        `json:"bank_request_id"`
	ContractTransID       string        `json:"contract_trans_id"`
	SignOTPRequestID      string        `json:"sign_otp_request_id"`
	LinkOTPRequestID      string        `json:"link_otp_request_id"`
	BankLinkingRequestID  string        `json:"bank_linking_request_id"`
	ODStatus              ODStatus      `json:"od_status"`
	CasaStatus            CASAStatus    `json:"casa_status"`
	SigningStatus         SigningStatus `json:"signing_status"`
	LinkingStatus         LinkingStatus `json:"linking_status"`
	SigningImage          SignImage     `json:"signing_image"`
	ErrorDetail           string        `json:"error_detail"`
	ManualApprovalReasons []string      `json:"manual_approval_reasons"`
}

func (p *PartnerOnboardingData) BuildRejection() *model.RejectInfo {
	rejectCode := p.GetRawRejectCode()
	rejectDesc := p.GetRejectDesc()
	rejectCodeE := RejectCodeFromString(rejectCode)
	needUpdKyc := p.IsApprovalRejected() && EvaluateNeedUpdKyc(rejectCodeE)
	canResubmit := p.IsApprovalRejected() && EvaluateCanResubmit(rejectCodeE)
	return &model.RejectInfo{
		RejectCode:  rejectCode,
		RejectDesc:  rejectDesc,
		NeedUpdKyc:  needUpdKyc,
		CanResubmit: canResubmit,
	}
}

type OnboardingStatus struct {
	RequestID             string        `json:"request_id"`
	ODStatus              ODStatus      `json:"od_status"`
	CASAStatus            CASAStatus    `json:"casa_status"`
	SigningStatus         SigningStatus `json:"signing_status"`
	ErrorDetail           string        `json:"error_detail"`
	ManualApprovalReasons []string      `json:"manual_approval_reasons"`
}

type OnboardingPermission struct {
	OnboardingAllowed      bool
	OnboardingRejectCode   RejectCode
	OnboardingRejectReason string
}

type SignImage struct {
	URL         string `json:"url"`
	ContentType string `json:"content_type"`
}

func (o *OnboardingStatus) MockApprovalApproved() *OnboardingStatus {
	o.ODStatus = ODCompleted
	o.CASAStatus = CASACompleted
	o.SigningStatus = SigningSigned
	o.ErrorDetail = ""
	o.ManualApprovalReasons = nil
	return o
}

func (o *OnboardingStatus) MockApprovalRejected() *OnboardingStatus {
	o.ODStatus = ODInRejected
	o.CASAStatus = CASARejected
	o.SigningStatus = SigningSigned
	o.ErrorDetail = "R18. Some info wrong"
	o.ManualApprovalReasons = nil
	return o
}

func (p *PartnerOnboardingData) MarshalBinary() ([]byte, error) {
	raw, err := jsoniter.Marshal(p)
	if err != nil {
		return nil, err
	}
	return raw, nil
}

func (p *PartnerOnboardingData) GetStatus() string {
	return ""
}

func (p *PartnerOnboardingData) GetPartnerStatus() string {
	return ""
}

func (p *PartnerOnboardingData) SetPartnerRequestID(prID string) {
	p.BankRequestID = prID
}

func (p *PartnerOnboardingData) GetPartnerRequestID() string {
	if p != nil {
		return p.BankRequestID
	}
	return ""
}

func (p *PartnerOnboardingData) GetLinkingRequestID() string {
	if p != nil {
		return p.BankLinkingRequestID
	}
	return ""
}

func (p *PartnerOnboardingData) SetPartnerTransactionID(transactionID string) {
	p.ContractTransID = transactionID
}

func (p *PartnerOnboardingData) GetPartnerTransactionID() string {
	if p != nil {
		return p.ContractTransID
	}
	return ""
}

func (p *PartnerOnboardingData) GetLinkingTransactionID() string {
	if p != nil {
		return p.LinkOTPRequestID
	}
	return ""
}

func (p *PartnerOnboardingData) SetSignatureImageUri(url, contentType string) {
	p.SigningImage = SignImage{
		URL:         url,
		ContentType: contentType,
	}
}

func (p *PartnerOnboardingData) SetApprovalResult(obStatus *OnboardingStatus) {
	p.ODStatus = obStatus.ODStatus
	p.CasaStatus = obStatus.CASAStatus
	p.SigningStatus = obStatus.SigningStatus
	p.ErrorDetail = obStatus.ErrorDetail
	p.ManualApprovalReasons = obStatus.ManualApprovalReasons
}

func (p *PartnerOnboardingData) GetApprovalResult() *OnboardingStatus {
	return &OnboardingStatus{
		ODStatus:              p.ODStatus,
		CASAStatus:            p.CasaStatus,
		SigningStatus:         p.SigningStatus,
		ErrorDetail:           p.ErrorDetail,
		ManualApprovalReasons: p.ManualApprovalReasons,
	}
}

func (p *PartnerOnboardingData) GetRawRejectCode() string {
	if !p.IsApprovalRejected() {
		return ""
	}
	if p.IsCasaOrODRejected() {
		return p.GenRejectCodeFromRejected()
	}
	if p.IsCasaOrODCancelled() {
		return p.GenRejectCodeFromCancelled()
	}
	return RejectCodeUnknown.String()
}

func (p *PartnerOnboardingData) GetDomainRejectCode() string {
	if p.IsCasaRejectedOrCancelled() {
		return fmt.Sprintf("CASA:%s", p.CasaStatus)
	}
	if p.IsODRejectedOrCancelled() {
		return fmt.Sprintf("OD:%s", p.ODStatus)
	}
	return ""
}

// Example: CASA:Rejected:R18
func (p *PartnerOnboardingData) GetFormatRejectCode() string {
	return fmt.Sprintf("%s:%s", p.GetDomainRejectCode(), p.GetRawRejectCode())
}

func (p *PartnerOnboardingData) GetRejectDesc() string {
	return p.ErrorDetail
}

func (p *PartnerOnboardingData) IsApprovalRejected() bool {
	return p.ODStatus == ODInRejected ||
		p.ODStatus == ODCancelled ||
		p.CasaStatus == CASARejected ||
		p.CasaStatus == CASACancelled
}

func (p *PartnerOnboardingData) IsApprovalApproved() bool {
	return p.ODStatus == ODCompleted && p.CasaStatus == CASACompleted
}

func (p *PartnerOnboardingData) IsApprovalProcessing() bool {
	return !p.IsApprovalRejected() && !p.IsApprovalApproved()
}

func (p *PartnerOnboardingData) CanQueryApprovalStatus() bool {
	return p.BankRequestID != ""
}

func (p *PartnerOnboardingData) IsContractSigned() bool {
	signStatuses := []SigningStatus{SigningSigned}
	return slices.Contains(signStatuses, p.SigningStatus)
}

func (p *PartnerOnboardingData) IsLinkingInit() bool {
	return p.BankLinkingRequestID != "" && p.LinkingStatus == LinkingInit
}

func (p *PartnerOnboardingData) IsLinkingSuccess() bool {
	return p.LinkingStatus == LinkingSuccess
}

func (p *PartnerOnboardingData) IsContractReady() bool {
	signStatuses := []SigningStatus{SigningSigned, SigningReadyToSign, SigningResign}
	return slices.Contains(signStatuses, p.SigningStatus)
}

func (p *PartnerOnboardingData) IsCasaRejectedOrCancelled() bool {
	return p.CasaStatus == CASARejected || p.CasaStatus == CASACancelled
}

func (p *PartnerOnboardingData) IsODRejectedOrCancelled() bool {
	return p.ODStatus == ODInRejected || p.ODStatus == ODCancelled
}

func (p *PartnerOnboardingData) IsCasaOrODRejected() bool {
	return p.CasaStatus == CASARejected || p.ODStatus == ODInRejected
}

func (p *PartnerOnboardingData) IsCasaOrODCancelled() bool {
	return p.CasaStatus == CASACancelled || p.ODStatus == ODCancelled
}

func (p *PartnerOnboardingData) GenRejectCodeFromRejected() string {
	components := strings.Split(p.ErrorDetail, ".")
	rejectCode := RejectCodeFromString(components[0])
	return rejectCode.String()
}

func (p *PartnerOnboardingData) GenRejectCodeFromCancelled() string {
	regex := regexp.MustCompile(`code\":\"(.*?)\",\"value\":\"(.*?)\"`)
	matches := regex.FindStringSubmatch(p.ErrorDetail)
	if len(matches) < 3 {
		return RejectCodeUnknown.String()
	}
	rejectCode := RejectCodeFromString(matches[1])
	return rejectCode.String()
}

func AsPartnerOnboardingData(data model.PartnerOnboardingData) (*PartnerOnboardingData, bool) {
	partnerData, ok := data.(*PartnerOnboardingData)
	return partnerData, ok
}

func UpdatePartnerOnboardingData(data model.PartnerOnboardingData, fx func(onboardingData *PartnerOnboardingData) *PartnerOnboardingData) model.PartnerOnboardingData {
	partnerData, ok := AsPartnerOnboardingData(data)
	if !ok {
		partnerData = &PartnerOnboardingData{}
	}
	partnerData = fx(partnerData)

	return partnerData
}
