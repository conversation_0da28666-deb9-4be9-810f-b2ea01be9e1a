package cimb

type SigningStatus string

const (
	SigningUnknown     SigningStatus = "UNKNOWN"
	SigningNotReady    SigningStatus = "NOT_READY"
	SigningReadyToSign SigningStatus = "READY_TO_SIGN"
	SigningSigned      SigningStatus = "SIGNED"
	SigningDataInvalid SigningStatus = "DATA_INVALID"
	SigningResign      SigningStatus = "RE_SIGN"
)

func (s SigningStatus) String() string {
	return string(s)
}

var SigningStatusMap = map[string]SigningStatus{
	string(SigningUnknown):     SigningUnknown,
	string(SigningNotReady):    SigningNotReady,
	string(SigningReadyToSign): SigningReadyToSign,
	string(SigningSigned):      SigningSigned,
	string(SigningDataInvalid): SigningDataInvalid,
}

func SigningStatusFromString(input string) SigningStatus {
	value, ok := SigningStatusMap[input]
	if !ok {
		return SigningUnknown
	}
	return value
}

type CASAStatus string

const (
	CASAUnknown     CASAStatus = "UNKNOWN"
	CASASubmitted   CASAStatus = "SUBMITTED"
	CASAInProgress  CASAStatus = "IN_PROGRESS"
	CASAPendingAIP  CASAStatus = "PENDING_AIP"
	CASAApprovedAIP CASAStatus = "APPROVED_AIP"
	CasaKYCVerified CASAStatus = "KYC_VERIFIED"
	// When generating contract, there could be mismatch in data, user need to resign so that CIMB can regenerate contract
	// This status indicate the resign contract case
	CASAApproved              CASAStatus = "APPROVED"
	CASACompleted             CASAStatus = "COMPLETED"
	CASARejected              CASAStatus = "REJECTED"
	CASACancelled             CASAStatus = "CANCELLED"
	CASADataError             CASAStatus = "ADDITIONAL_DATA_ERROR"
	CASAPendingManualApproval CASAStatus = "PENDING_MANUAL_APPROVAL"
)

var CASAStatusMap = map[string]CASAStatus{
	string(CASAUnknown):               CASAUnknown,
	string(CASASubmitted):             CASASubmitted,
	string(CASAInProgress):            CASAInProgress,
	string(CASAPendingAIP):            CASAPendingAIP,
	string(CASAApprovedAIP):           CASAApprovedAIP,
	string(CasaKYCVerified):           CasaKYCVerified,
	string(CASADataError):             CASADataError,
	string(CASAApproved):              CASAApproved,
	string(CASACompleted):             CASACompleted,
	string(CASARejected):              CASARejected,
	string(CASACancelled):             CASACancelled,
	string(CASAPendingManualApproval): CASAPendingManualApproval,
}

func (c CASAStatus) String() string {
	return string(c)
}

func CASAStatusFromString(input string) CASAStatus {
	value, ok := CASAStatusMap[input]
	if !ok {
		return CASAUnknown
	}
	return value
}

type ODStatus string

const (
	ODUnknown               ODStatus = "UNKNOWN"
	ODInProgress            ODStatus = "IN_PROGRESS"
	ODPendingAIP            ODStatus = "PENDING_AIP"
	ODPendingManualApproval ODStatus = "PENDING_MANUAL_APPROVAL"
	ODReadyToDisburse       ODStatus = "READY_FOR_DISBURSE"
	ODCompleted             ODStatus = "COMPLETED"
	ODInRejected            ODStatus = "REJECTED"
	ODCancelled             ODStatus = "CANCELLED"
)

func (o ODStatus) String() string {
	return string(o)
}

var ODStatusMap = map[string]ODStatus{
	string(ODUnknown):               ODUnknown,
	string(ODInProgress):            ODInProgress,
	string(ODPendingAIP):            ODPendingAIP,
	string(ODCancelled):             ODCancelled,
	string(ODCompleted):             ODCompleted,
	string(ODInRejected):            ODInRejected,
	string(ODReadyToDisburse):       ODReadyToDisburse,
	string(ODPendingManualApproval): ODPendingManualApproval,
}

func ODStatusFromString(input string) ODStatus {
	value, ok := ODStatusMap[input]
	if !ok {
		return ODUnknown
	}
	return value
}

type LinkingStatus string

const (
	LinkingEmpty   LinkingStatus = ""
	LinkingInit    LinkingStatus = "INIT"
	LinkingSuccess LinkingStatus = "SUCCESS"
)
