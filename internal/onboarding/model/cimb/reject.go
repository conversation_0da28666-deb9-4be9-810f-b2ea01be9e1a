package cimb

import "slices"

type RejectCode string

const (
	// Common
	RejectCodeUnknown       RejectCode = "UNKNOWN"
	RejectCodeODRejected    RejectCode = "OD:REJECTED"
	RejectCodeODCancelled   RejectCode = "OD:CANCELLED"
	RejectCodeCasaRejected  RejectCode = "CASA:REJECTED"
	RejectCodeCasaCancelled RejectCode = "CASA:CANCELLED"

	RejectCodeBadRequest             RejectCode = "BAD_REQUEST"
	RejectCodeICNumberUsed           RejectCode = "IC_NUMBER_USED"
	RejectCodePhoneNumberUsed        RejectCode = "PHONE_NUMBER_USED"
	RejectCodeEmailOrPhoneUsed       RejectCode = "EMAIL_OR_PHONE_USED"
	RejectCodeCustomerExisted        RejectCode = "EXISTING_CIMB_CUSTOMER_FOUND"
	RejectCodeOnboardingInfoUsed     RejectCode = "ONBOARDING_INFORMATION_USED"
	RejectCodeOnboardingNotAllowed   RejectCode = "ONBOARDING_PRODUCT_NOT_ALLOWED"
	RejectCodeIdentityInfoMismatched RejectCode = "IDENTITY_INFORMATION_NOT_MATCH_WITH_CURRENT"

	// Reject due to CIMB
	RejectCodeR01AgeNotInRange        RejectCode = "R01"
	RejectCodeR01MaleAgeNotInRange    RejectCode = "R01M"
	RejectCodeR01FemaleAgeNotInRange  RejectCode = "R01F"
	RejectCodeR02CustomerCancelled    RejectCode = "R02"
	RejectCodeR03HitWatchList         RejectCode = "R03"
	RejectCodeR04HighRisk             RejectCode = "R04"
	RejectCodeR05ICDamagedOrFaded     RejectCode = "R05"
	RejectCodeR06ICAndSelfieInvalid   RejectCode = "R06"
	RejectCodeR07ICExpired            RejectCode = "R07"
	RejectCodeR08ICLivenessFail       RejectCode = "R08"
	RejectCodeR09ICCutCorner          RejectCode = "R09"
	RejectCodeR10ICBlur               RejectCode = "R10"
	RejectCodeR11ICFaceNotMatch       RejectCode = "R11"
	RejectCodeR12ICTampering          RejectCode = "R12"
	RejectCodeR13Uncontactable        RejectCode = "R13"
	RejectCodeR14WrongICNumber        RejectCode = "R14"
	RejectCodeR15WrongName            RejectCode = "R15"
	RejectCodeR16WrongIC              RejectCode = "R16"
	RejectCodeR17WrongDOB             RejectCode = "R17"
	RejectCodeR18WrongDOI             RejectCode = "R18"
	RejectCodeR19WrongCurrentAddr     RejectCode = "R19"
	RejectCodeR20WrongCertifyPlace    RejectCode = "R20"
	RejectCodeR21SelfieLivenessFail   RejectCode = "R21"
	RejectCodeR22SelfieInvalid        RejectCode = "R22"
	RejectCodeR23CustomerRefused      RejectCode = "R23"
	RejectCodeR24PartialID            RejectCode = "R24"
	RejectCodeR25LackFrontOrBackIC    RejectCode = "R25"
	RejectCodeR26InvalidMobileNumber  RejectCode = "R26"
	RejectCodeR27ExistingCustomer     RejectCode = "R27"
	RejectCodeR28CustomerNotApplyPrd  RejectCode = "R28"
	RejectCodeR29CustomerNotOwnPhone  RejectCode = "R29"
	RejectCodeR30CustomerNotConfirm   RejectCode = "R30"
	RejectCodeR31AbnormalBehavior     RejectCode = "R31"
	RejectCodeR32AbnormalIDData       RejectCode = "R32"
	RejectCodeR33OtherFraudReason     RejectCode = "R33"
	RejectCodeR34IDAndNFCNotMatch     RejectCode = "R34"
	RejectCodeR35IDAndSelfieNotMatch  RejectCode = "R35"
	RejectCodeR36IDPhotosIsCovered    RejectCode = "R36"
	RejectCodeR37IDPhotosInvalid      RejectCode = "R37"
	RejectCodeR38CustomerHasNewID     RejectCode = "R38"
	RejectCodeR39WrongPhotosOrder     RejectCode = "R39"
	RejectCodeR40SelfieIsBlurry       RejectCode = "R40"
	RejectCodeR41SelfieIsCovered      RejectCode = "R41"
	RejectCodeR42SelfieIsDummy        RejectCode = "R42"
	RejectCodeR43SelfieEyeClosed      RejectCode = "R43"
	RejectCodeR44SelfieEyeNotDirect   RejectCode = "R44"
	RejectCodeR45SelfieIsGlared       RejectCode = "R45"
	RejectCodeR46SelfieLowLight       RejectCode = "R46"
	RejectCodeR47SelfieHasMultiFace   RejectCode = "R47"
	RejectCodeR48SelfieNotLiveness    RejectCode = "R48"
	RejectCodeR49SelfieNotFullFace    RejectCode = "R49"
	RejectCodeR50SelfieTakenByOther   RejectCode = "R50"
	RejectCodeR51SelfieWearingGlasses RejectCode = "R51"
	RejectCodeR52SelfieWearingMask    RejectCode = "R52"
	RejectCodeR53SystemHasError       RejectCode = "R53"
	RejectCodeR54CustomerNFCInvalid   RejectCode = "R54"

	// Cancelled due CIMB
	RejectCodeC0Other             RejectCode = "CO"
	RejectCodeC1DuplicateApp      RejectCode = "C1"
	RejectCodeC2                  RejectCode = "C2"
	RejectCodeC3InsufficientDoc   RejectCode = "C3"
	RejectCodeC4InCompleteVerify  RejectCode = "C4"
	RejectCodeC5AgentRequest      RejectCode = "C5"
	RejectCodeC6OverPermitted     RejectCode = "C6"
	RejectCodeC7                  RejectCode = "C7"
	RejectCodeC8                  RejectCode = "C8"
	RejectCodeC9InvalidSelfie     RejectCode = "C9"
	RejectCodeC10InvalidIdentity  RejectCode = "C10"
	RejectCodeC12InvalidDocument  RejectCode = "C12"
	RejectCodeC13BlurIdentityCard RejectCode = "C13"
	RejectCodeC16                 RejectCode = "C16"
	RejectCodeC17                 RejectCode = "C17"
)

var rejectStatusMap = map[string]RejectCode{
	RejectCodeBadRequest.String():             RejectCodeBadRequest,
	RejectCodeICNumberUsed.String():           RejectCodeICNumberUsed,
	RejectCodePhoneNumberUsed.String():        RejectCodePhoneNumberUsed,
	RejectCodeEmailOrPhoneUsed.String():       RejectCodeEmailOrPhoneUsed,
	RejectCodeCustomerExisted.String():        RejectCodeCustomerExisted,
	RejectCodeOnboardingInfoUsed.String():     RejectCodeOnboardingInfoUsed,
	RejectCodeOnboardingNotAllowed.String():   RejectCodeOnboardingNotAllowed,
	RejectCodeIdentityInfoMismatched.String(): RejectCodeIdentityInfoMismatched,

	// Rxx
	RejectCodeR01AgeNotInRange.String():        RejectCodeR01AgeNotInRange,
	RejectCodeR01MaleAgeNotInRange.String():    RejectCodeR01MaleAgeNotInRange,
	RejectCodeR01FemaleAgeNotInRange.String():  RejectCodeR01FemaleAgeNotInRange,
	RejectCodeR02CustomerCancelled.String():    RejectCodeR02CustomerCancelled,
	RejectCodeR03HitWatchList.String():         RejectCodeR03HitWatchList,
	RejectCodeR04HighRisk.String():             RejectCodeR04HighRisk,
	RejectCodeR05ICDamagedOrFaded.String():     RejectCodeR05ICDamagedOrFaded,
	RejectCodeR06ICAndSelfieInvalid.String():   RejectCodeR06ICAndSelfieInvalid,
	RejectCodeR07ICExpired.String():            RejectCodeR07ICExpired,
	RejectCodeR08ICLivenessFail.String():       RejectCodeR08ICLivenessFail,
	RejectCodeR09ICCutCorner.String():          RejectCodeR09ICCutCorner,
	RejectCodeR10ICBlur.String():               RejectCodeR10ICBlur,
	RejectCodeR11ICFaceNotMatch.String():       RejectCodeR11ICFaceNotMatch,
	RejectCodeR12ICTampering.String():          RejectCodeR12ICTampering,
	RejectCodeR13Uncontactable.String():        RejectCodeR13Uncontactable,
	RejectCodeR14WrongICNumber.String():        RejectCodeR14WrongICNumber,
	RejectCodeR15WrongName.String():            RejectCodeR15WrongName,
	RejectCodeR16WrongIC.String():              RejectCodeR16WrongIC,
	RejectCodeR17WrongDOB.String():             RejectCodeR17WrongDOB,
	RejectCodeR18WrongDOI.String():             RejectCodeR18WrongDOI,
	RejectCodeR19WrongCurrentAddr.String():     RejectCodeR19WrongCurrentAddr,
	RejectCodeR20WrongCertifyPlace.String():    RejectCodeR20WrongCertifyPlace,
	RejectCodeR21SelfieLivenessFail.String():   RejectCodeR21SelfieLivenessFail,
	RejectCodeR22SelfieInvalid.String():        RejectCodeR22SelfieInvalid,
	RejectCodeR23CustomerRefused.String():      RejectCodeR23CustomerRefused,
	RejectCodeR24PartialID.String():            RejectCodeR24PartialID,
	RejectCodeR25LackFrontOrBackIC.String():    RejectCodeR25LackFrontOrBackIC,
	RejectCodeR26InvalidMobileNumber.String():  RejectCodeR26InvalidMobileNumber,
	RejectCodeR27ExistingCustomer.String():     RejectCodeR27ExistingCustomer,
	RejectCodeR28CustomerNotApplyPrd.String():  RejectCodeR28CustomerNotApplyPrd,
	RejectCodeR29CustomerNotOwnPhone.String():  RejectCodeR29CustomerNotOwnPhone,
	RejectCodeR30CustomerNotConfirm.String():   RejectCodeR30CustomerNotConfirm,
	RejectCodeR31AbnormalBehavior.String():     RejectCodeR31AbnormalBehavior,
	RejectCodeR32AbnormalIDData.String():       RejectCodeR32AbnormalIDData,
	RejectCodeR33OtherFraudReason.String():     RejectCodeR33OtherFraudReason,
	RejectCodeR34IDAndNFCNotMatch.String():     RejectCodeR34IDAndNFCNotMatch,
	RejectCodeR35IDAndSelfieNotMatch.String():  RejectCodeR35IDAndSelfieNotMatch,
	RejectCodeR36IDPhotosIsCovered.String():    RejectCodeR36IDPhotosIsCovered,
	RejectCodeR37IDPhotosInvalid.String():      RejectCodeR37IDPhotosInvalid,
	RejectCodeR38CustomerHasNewID.String():     RejectCodeR38CustomerHasNewID,
	RejectCodeR39WrongPhotosOrder.String():     RejectCodeR39WrongPhotosOrder,
	RejectCodeR40SelfieIsBlurry.String():       RejectCodeR40SelfieIsBlurry,
	RejectCodeR41SelfieIsCovered.String():      RejectCodeR41SelfieIsCovered,
	RejectCodeR42SelfieIsDummy.String():        RejectCodeR42SelfieIsDummy,
	RejectCodeR43SelfieEyeClosed.String():      RejectCodeR43SelfieEyeClosed,
	RejectCodeR44SelfieEyeNotDirect.String():   RejectCodeR44SelfieEyeNotDirect,
	RejectCodeR45SelfieIsGlared.String():       RejectCodeR45SelfieIsGlared,
	RejectCodeR46SelfieLowLight.String():       RejectCodeR46SelfieLowLight,
	RejectCodeR47SelfieHasMultiFace.String():   RejectCodeR47SelfieHasMultiFace,
	RejectCodeR48SelfieNotLiveness.String():    RejectCodeR48SelfieNotLiveness,
	RejectCodeR49SelfieNotFullFace.String():    RejectCodeR49SelfieNotFullFace,
	RejectCodeR50SelfieTakenByOther.String():   RejectCodeR50SelfieTakenByOther,
	RejectCodeR51SelfieWearingGlasses.String(): RejectCodeR51SelfieWearingGlasses,
	RejectCodeR52SelfieWearingMask.String():    RejectCodeR52SelfieWearingMask,
	RejectCodeR53SystemHasError.String():       RejectCodeR53SystemHasError,
	RejectCodeR54CustomerNFCInvalid.String():   RejectCodeR54CustomerNFCInvalid,

	// Cxx
	RejectCodeC0Other.String():             RejectCodeC0Other,
	RejectCodeC1DuplicateApp.String():      RejectCodeC1DuplicateApp,
	RejectCodeC2.String():                  RejectCodeC2,
	RejectCodeC3InsufficientDoc.String():   RejectCodeC3InsufficientDoc,
	RejectCodeC4InCompleteVerify.String():  RejectCodeC4InCompleteVerify,
	RejectCodeC5AgentRequest.String():      RejectCodeC5AgentRequest,
	RejectCodeC6OverPermitted.String():     RejectCodeC6OverPermitted,
	RejectCodeC7.String():                  RejectCodeC7,
	RejectCodeC8.String():                  RejectCodeC8,
	RejectCodeC9InvalidSelfie.String():     RejectCodeC9InvalidSelfie,
	RejectCodeC10InvalidIdentity.String():  RejectCodeC10InvalidIdentity,
	RejectCodeC12InvalidDocument.String():  RejectCodeC12InvalidDocument,
	RejectCodeC13BlurIdentityCard.String(): RejectCodeC13BlurIdentityCard,
	RejectCodeC16.String():                 RejectCodeC16,
	RejectCodeC17.String():                 RejectCodeC17,
}

func RejectCodeFromString(s string) RejectCode {
	if code, ok := rejectStatusMap[s]; ok {
		return code
	}
	return RejectCodeUnknown
}

func (rc RejectCode) String() string {
	return string(rc)
}

func (rc RejectCode) IsLinkingError() bool {
	return rc == RejectCodeCustomerExisted
}

func (rc RejectCode) IsEmpty() bool {
	return rc == RejectCodeUnknown
}

func EvaluateCanResubmit(code RejectCode) bool {
	notAllowedCodes := []RejectCode{
		RejectCodeR01AgeNotInRange,
		RejectCodeR03HitWatchList,
		RejectCodeR04HighRisk,
		RejectCodeR26InvalidMobileNumber,
		RejectCodeR27ExistingCustomer,
		RejectCodeR29CustomerNotOwnPhone,
		RejectCodeR31AbnormalBehavior,
		RejectCodeR32AbnormalIDData,
		RejectCodeR33OtherFraudReason,
		RejectCodeR54CustomerNFCInvalid,

		RejectCodeC0Other,
		RejectCodeC1DuplicateApp,
		RejectCodeC3InsufficientDoc,
		RejectCodeC4InCompleteVerify,
		RejectCodeC6OverPermitted,
	}
	return !slices.Contains(notAllowedCodes, code)
}

func EvaluateNeedUpdKyc(code RejectCode) bool {
	matchCondCodes := []RejectCode{
		RejectCodeR05ICDamagedOrFaded,
		RejectCodeR06ICAndSelfieInvalid,
		RejectCodeR07ICExpired,
		RejectCodeR08ICLivenessFail,
		RejectCodeR09ICCutCorner,
		RejectCodeR10ICBlur,
		RejectCodeR11ICFaceNotMatch,
		RejectCodeR12ICTampering,
		RejectCodeR14WrongICNumber,
		RejectCodeR15WrongName,
		RejectCodeR16WrongIC,
		RejectCodeR17WrongDOB,
		RejectCodeR18WrongDOI,
		RejectCodeR19WrongCurrentAddr,
		RejectCodeR20WrongCertifyPlace,
		RejectCodeR21SelfieLivenessFail,
		RejectCodeR22SelfieInvalid,
		RejectCodeR24PartialID,
		RejectCodeR25LackFrontOrBackIC,
		RejectCodeR26InvalidMobileNumber,
	}
	return slices.Contains(matchCondCodes, code)
}
