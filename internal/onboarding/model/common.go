package model

import "encoding/json"

type ClientSession struct {
	DeviceID  string
	Latitude  float64
	Longitude float64
	RequestIP string
}

func (l *ClientSession) GetLat() float64 {
	if l == nil {
		return 0
	}
	return l.Latitude
}

func (l *ClientSession) GetLng() float64 {
	if l == nil {
		return 0
	}
	return l.Longitude
}

func (l *ClientSession) GetIP() string {
	if l == nil {
		return ""
	}
	return l.RequestIP
}

func (l *ClientSession) GetDeviceID() string {
	if l == nil {
		return ""
	}
	return l.DeviceID
}

type NoticeInfo struct {
	Content NoticeContent  `json:"content,omitempty"`
	Action  []NoticeAction `json:"action,omitempty"`
}

func (n *NoticeInfo) String() string {
	if n == nil {
		return ""
	}
	jsonString, _ := json.Marshal(n)
	return string(jsonString)
}

type NoticeContent struct {
	Title   string `json:"title,omitempty"`
	Message string `json:"message,omitempty"`
}

type NoticeAction struct {
	Code         CTACode                `json:"code,omitempty"`
	Title        CTATitle               `json:"title,omitempty"`
	Variant      CTAVariant             `json:"variant,omitempty"`
	ZpaActionURL string                 `json:"zpa_action_url,omitempty"`
	ZpiActionURL string                 `json:"zpi_action_url,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

type CTACode string

const (
	CTACodeUnknown        CTACode = "UNKNOWN"
	CTACodeRetry          CTACode = "RETRY"
	CTACodeDoKyc          CTACode = "DO_KYC"
	CTACodeUpdateKyc      CTACode = "UPDATE_KYC"
	CTACodeResetNfc       CTACode = "RESET_NFC"
	CTACodeUpdateNfc      CTACode = "UPDATE_NFC"
	CTACodeUpdateKycNfc   CTACode = "UPDATE_KYC_NFC"
	CTACodeContactCS      CTACode = "CONTACT_CS"
	CTACodeUpdatePhone    CTACode = "UPDATE_PHONE"
	CTACodeCloseNotice    CTACode = "CLOSE_NOTICE"
	CTACodeContactPartner CTACode = "CONTACT_PARTNER"
	CTACodeNavigateToHome CTACode = "NAVIGATE_TO_HOME"
	CTACodeRegister       CTACode = "REGISTER_ONBOARDING"
	CTACodeReRegister     CTACode = "RE_REGISTER_ONBOARDING"
	CTACodeLinkAccount    CTACode = "LINK_ACCOUNT"
)

func (c CTACode) String() string {
	return string(c)
}

type CTAVariant string

const (
	CTAVariantPrimary   CTAVariant = "PRIMARY"
	CTAVariantSecondary CTAVariant = "SECONDARY"
	CTAVariantOutlined  CTAVariant = "OUTLINED"
	CTAVariantDanger    CTAVariant = "DANGER"
	CTAVariantGhost     CTAVariant = "GHOST"
	CTAVariantLink      CTAVariant = "LINK"
	CTAVariantText      CTAVariant = "TEXT"
)

func (v CTAVariant) String() string {
	return string(v)
}

type CTATitle string

const (
	CTATitleClose              CTATitle = "Đóng"
	CTATitleContactCS          CTATitle = "Liên hệ CSKH"
	CTATitleDoKyc              CTATitle = "Thực hiện định danh"
	CTATitleUpdateKyc          CTATitle = "Cập nhật định danh"
	CTATitleUpdatePhone        CTATitle = "Cập nhật SĐT"
	CTATitleContactPartner     CTATitle = "Liên hệ đối tác"
	CTATitleContactPartnerCIMB CTATitle = "Liên hệ hotline CIMB"
	CTATitleDiscoverMore       CTATitle = "Khám phá thêm"
	CTATitleRegister           CTATitle = "Đăng ký ngay"
	CTATitleReRegister         CTATitle = "Đăng ký lại"
	CTATitleLinkAccount        CTATitle = "Liên kết ngay"
	CTATitleScanIdentityCard   CTATitle = "Quét CCCD"
	CTATitleLater              CTATitle = "Để sau"
	CTATitleUnderstand         CTATitle = "Đã hiểu"
	CTATitleRetry              CTATitle = "Thử lại"
)

func (c CTATitle) String() string {
	return string(c)
}
