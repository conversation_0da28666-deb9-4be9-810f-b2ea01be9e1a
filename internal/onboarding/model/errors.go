package model

import (
	"fmt"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
)

var (
	ErrOnboardingNotFound    = errorkit.NewError(errorkit.CodeOnboardingNotFound, "onboarding not found")
	ErrOnboardingCompleted   = errorkit.NewError(errorkit.CodeOnboardingCompleted, "onboarding is completed")
	ErrContractHasBeenSigned = errorkit.NewError(errorkit.CodeContractHasBeenSigned, "binding contract has been signed")
)

type PartnerError struct {
	Cause   error
	SysCode string
}

func NewPartnerError(code string, cause error) *PartnerError {
	return &PartnerError{
		Cause:   cause,
		SysCode: code,
	}
}

func (e *PartnerError) Error() string {
	return fmt.Sprintf("partner error: %s, err=%s", e.SysCode, e.Cause.Error())
}

func (e *PartnerError) Unwrap() error {
	return e.Cause
}
