package model

import (
	"fmt"
	"slices"
)

type OnboardingStep string

const (
	OnboardingStepUnknown         OnboardingStep = "UNKNOWN"
	OnboardingStepInitial         OnboardingStep = "INITIAL" //created status
	OnboardingStepNonWhitelist    OnboardingStep = "NON_WHITELIST"
	OnboardingStepEligible        OnboardingStep = "ONBOARDING_ELIGIBLE" //user is eligible for onboarding
	OnboardingStepIneligible      OnboardingStep = "ONBOARDING_INELIGIBLE"
	OnboardingStepProfileSuccess  OnboardingStep = "PROFILE_SUCCESS"
	OnboardingStepFaceChallenged  OnboardingStep = "FACE_CHALLENGED"
	OnboardingStepReadyToSign     OnboardingStep = "READY_TO_SIGN"
	OnboardingStepOTPVerified     OnboardingStep = "OTP_VERIFIED"     //user verified otp,
	OnboardingStepContractSinging OnboardingStep = "CONTRACT_SIGNING" //user contract signing flow
	OnboardingStepWaitingApproval OnboardingStep = "WAITING_APPROVAL" //onboarding profile approved by partner
	OnboardingStepApproved        OnboardingStep = "APPROVED"         //onboarding profile approved by partner
	OnboardingStepRejected        OnboardingStep = "REJECTED"         //onboarding profile is rejected by partner
)

func (s OnboardingStep) String() string {
	return string(s)
}

func (s OnboardingStep) IsUnregistered() bool {
	steps := []OnboardingStep{
		OnboardingStepInitial, OnboardingStepUnknown,
		OnboardingStepNonWhitelist, OnboardingStepIneligible,
	}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsRegistered() bool {
	return s == OnboardingStepEligible
}

func (s OnboardingStep) CanLinkingAccount() bool {
	steps := []OnboardingStep{OnboardingStepEligible}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) CanSubmitSignature() bool {
	return s == OnboardingStepProfileSuccess
}

func (s OnboardingStep) CanSubmitFaceChallenge() bool {
	return s == OnboardingStepProfileSuccess
}

func (s OnboardingStep) CanSigningContract() bool {
	steps := []OnboardingStep{OnboardingStepFaceChallenged, OnboardingStepContractSinging}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) CanResetFaceChallenge() bool {
	steps := []OnboardingStep{OnboardingStepFaceChallenged, OnboardingStepContractSinging, OnboardingStepWaitingApproval}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) CanContractOTPConfirmation() bool {
	steps := []OnboardingStep{OnboardingStepProfileSuccess, OnboardingStepFaceChallenged}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) CanLinkingOTPConfirmation() bool {
	steps := []OnboardingStep{OnboardingStepEligible}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) CanResetOTPConfirmation() bool {
	steps := []OnboardingStep{OnboardingStepOTPVerified}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsFinal() bool {
	// We only need to query status from partner when onboarding is in processing
	steps := []OnboardingStep{OnboardingStepApproved, OnboardingStepRejected}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsApproved() bool {
	steps := []OnboardingStep{OnboardingStepApproved}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsRejected() bool {
	steps := []OnboardingStep{OnboardingStepRejected}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsOtpVerified() bool {
	steps := []OnboardingStep{OnboardingStepOTPVerified}
	return slices.Contains(steps, s)
}

func (s OnboardingStep) IsWaitingApproval() bool {
	steps := []OnboardingStep{OnboardingStepWaitingApproval}
	return slices.Contains(steps, s)
}

func StepFromString(s string) (OnboardingStep, error) {
	stepMap := map[string]OnboardingStep{
		OnboardingStepUnknown.String():         OnboardingStepUnknown,
		OnboardingStepInitial.String():         OnboardingStepInitial,
		OnboardingStepNonWhitelist.String():    OnboardingStepNonWhitelist,
		OnboardingStepEligible.String():        OnboardingStepEligible,
		OnboardingStepIneligible.String():      OnboardingStepIneligible,
		OnboardingStepProfileSuccess.String():  OnboardingStepProfileSuccess,
		OnboardingStepFaceChallenged.String():  OnboardingStepFaceChallenged,
		OnboardingStepReadyToSign.String():     OnboardingStepReadyToSign,
		OnboardingStepOTPVerified.String():     OnboardingStepOTPVerified,
		OnboardingStepContractSinging.String(): OnboardingStepContractSinging,
		OnboardingStepWaitingApproval.String(): OnboardingStepWaitingApproval,
		OnboardingStepApproved.String():        OnboardingStepApproved,
		OnboardingStepRejected.String():        OnboardingStepRejected,
	}
	value, ok := stepMap[s]
	if !ok {
		return OnboardingStepUnknown, fmt.Errorf("invalid onboarding step: %s", s)
	}
	return value, nil
}

type OnboardingProgress struct {
	Stage  OnboardingStageEnum
	Status OnboardingStageStatus
}

func (o OnboardingProgress) UnifyInfo() string {
	return fmt.Sprintf("%s_%s", o.Stage, o.Status)
}

func NewOnboardingProgress(stage OnboardingStageEnum, status OnboardingStageStatus) OnboardingProgress {
	return OnboardingProgress{
		Stage:  stage,
		Status: status,
	}
}

type OnboardingStageStatus string

const (
	OnboardingStageStatusInvalid    OnboardingStageStatus = "invalid"
	OnboardingStageStatusWaiting    OnboardingStageStatus = "waiting"
	OnboardingStageStatusProcessing OnboardingStageStatus = "processing"
	OnboardingStageStatusSuccess    OnboardingStageStatus = "success"
	OnboardingStageStatusRejected   OnboardingStageStatus = "rejected"
)

type OnboardingStageEnum string

type OnboardingStageEnums []OnboardingStageEnum

const (
	OnboardingStageEnumUnknown       OnboardingStageEnum = "unknown"
	OnboardingStageEnumPermission    OnboardingStageEnum = "permission"
	OnboardingStageEnumRegister      OnboardingStageEnum = "register"
	OnboardingStageEnumFaceChallenge OnboardingStageEnum = "face_challenge"
	OnboardingStageEnumOtp           OnboardingStageEnum = "otp"
	OnboardingStageEnumApproval      OnboardingStageEnum = "approval"
)

func (s OnboardingStageEnum) String() string {
	return string(s)
}

func (s OnboardingStageEnums) ToStrings() []string {
	var result []string
	for _, stage := range s {
		result = append(result, stage.String())
	}
	return result
}

func AllStages() OnboardingStageEnums {
	return []OnboardingStageEnum{
		OnboardingStageEnumRegister,
		OnboardingStageEnumFaceChallenge,
		OnboardingStageEnumOtp,
		OnboardingStageEnumApproval,
	}
}

type OnboardingStatus string

const (
	OnboardingStatusUnknown  OnboardingStatus = "unknown"
	OnboardingStatusActive   OnboardingStatus = "active"
	OnboardingStatusInactive OnboardingStatus = "inactive"
)

func (s OnboardingStatus) String() string {
	return string(s)
}
