package model

import "gitlab.zalopay.vn/fin/installment/installment-service/pkg/partner"

type UserRiskAssessment struct {
	HasFraud   bool
	ScoresInfo map[partner.PartnerCode]CreditScore
}

type CreditScore struct {
	EstLimit    int64
	ScoreBand   int32
	PartnerCode partner.PartnerCode
}

type UserRiskUnderwriting struct {
	Data     map[string]any
	Error    error
	HasData  bool
	HasError bool
}

type RiskWhitelistMethod string

const (
	RiskWhitelistMethodError     RiskWhitelistMethod = "RISK_ERROR"
	RiskWhitelistMethodWhitelist RiskWhitelistMethod = "WHITELIST"
	RiskWhitelistMethodRealtime  RiskWhitelistMethod = "REALTIME"
)

func (u RiskWhitelistMethod) String() string {
	return string(u)
}

func (u RiskWhitelistMethod) IsEmpty() bool {
	return u == ""
}
