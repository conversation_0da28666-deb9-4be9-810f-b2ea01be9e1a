package user_profile

import (
	"context"
	"regexp"
	"slices"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/utils"
	uk "gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1"
	"google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	ec "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc"
	up "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type userProfileClient struct {
	env          string
	logger       *log.Helper
	ecConf       *Config
	upGrpcClient up.UserProfileClient
	ukGrpcClient uk.UserEkycNfcClient
	ecGrpcClient ec.EKycCenterServiceClient
}

var (
	noNfcReason = []string{uk.ReasonCode_REASON_CODE_NOT_EKYC.String()}
)

func NewClient(
	config *config.Onboarding,
	upGrpcClient up.UserProfileClient,
	ukGrpcClient uk.UserEkycNfcClient,
	ecGrpcClient ec.EKycCenterServiceClient,
	kLogger log.Logger) _interface.UserProfileService {
	env := config.GetApp().GetEnv()
	kLogger = log.NewFilter(kLogger, log.FilterKey("profile.basic_profile", "identity_profile", "kyc_profile"))
	logger := log.NewHelper(log.With(kLogger, "adapters", "user-profile"))
	ecConf := &Config{
		ClientID:  config.GetAdapters().GetEkycCenter().GetClientId(),
		ClientKey: config.GetAdapters().GetEkycCenter().GetClientKey(),
	}

	return &userProfileClient{
		env:          env,
		logger:       logger,
		ecConf:       ecConf,
		upGrpcClient: upGrpcClient,
		ukGrpcClient: ukGrpcClient,
		ecGrpcClient: ecGrpcClient,
	}
}

func (u userProfileClient) GetProfile(ctx context.Context,
	request dto.ZPUserProfileParams) (*model.UserProfile, error) {
	logger := u.logger.WithContext(ctx)

	params := &up.QueryByUserIdRequest{
		UserId:          cast.ToString(request.ZalopayID),
		KycProfile:      request.KycProfile,
		BasicProfile:    request.BasicProfile,
		IdentityProfile: request.IdentityProfile,
	}
	profile, err := u.upGrpcClient.QueryByUserId(ctx, params)
	if err != nil {
		logger.Errorf("get profile from UM failed, error=%v", err)
		return nil, errors.Errorf("get profile from UM failed, error=%v", err)
	}

	logger.Infow("msg", "get profile from UM success", "params", params)

	return buildUserProfileInfo(profile), nil

}

func (u userProfileClient) GetKycNfcData(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error) {
	logger := u.logger.WithContext(ctx)

	resp, err := u.ukGrpcClient.GetUserNFC(ctx, &uk.GetUserNFCRequest{
		ZalopayId:  cast.ToString(zalopayID),
		NeedNfcRaw: true,
	})
	if err == nil && resp != nil {
		return &model.UserKycNfc{
			NfcDataDG2:      resp.GetDg2(),
			NfcDataDG13:     buildNfcDG13(resp),
			NfcDataRaw:      buildNfcRaw(resp),
			NfcSignature:    getNfcSignature(resp, u.env),
			CollectSource:   resp.GetVendorData().GetCollectSource(),
			VendorSignature: resp.GetVendorData().GetVerifySignature(),
			NfcId:           resp.GetNfcId(),
		}, nil
	}

	stt, ok := status.FromError(err)
	if !ok || (stt.Code() != codes.OK && len(stt.Details()) == 0) {
		logger.Errorf("get kyc nfc data from UM failed, error=%v", err)
		return nil, errors.Errorf("get kyc nfc data from UM failed, error=%v", err)
	}

	detail := stt.Details()[0]
	errInfo, ok := detail.(*errdetails.ErrorInfo)
	if !ok || !slices.Contains(noNfcReason, errInfo.Reason) {
		logger.Errorf("get kyc nfc data from UM failed, error=%v", err)
		return nil, errors.Errorf("get kyc nfc data from UM failed, error=%v", err)
	}

	return &model.UserKycNfc{NfcDataRaw: model.KycNfcDataRaw{}}, nil
}

func (u userProfileClient) GetKycProgStatus(ctx context.Context, zalopayID int64) (*model.UserKycProgress, error) {
	params := &ec.GetUserEKycStatusRequest{
		UserId:    cast.ToString(zalopayID),
		ClientId:  u.ecConf.ClientID,
		ClientKey: u.ecConf.ClientKey,
	}

	ekyc, err := u.ecGrpcClient.GetUserEKycStatus(ctx, params)
	if err != nil {
		return nil, errors.Errorf("GetKycStatus from ekyc center fail: %v", err)
	}

	return &model.UserKycProgress{Status: model.KycStatus(ekyc.Status)}, nil
}

func (u userProfileClient) GetKycNfcStatus(ctx context.Context, zalopayID int64) (*model.UserKycNfc, error) {
	logger := u.logger.WithContext(ctx)

	resp, err := u.ukGrpcClient.GetStatus(ctx, &uk.GetStatusRequest{
		ZalopayId: cast.ToString(zalopayID),
	})
	// Fast return
	if err == nil {
		return &model.UserKycNfc{NfcStatus: model.KycNfcStatus(resp.GetStatus())}, nil
	}

	stt, ok := status.FromError(err)
	if !ok || len(stt.Details()) == 0 {
		logger.Errorf("get kyc nfc status from UM failed, error=%v", err)
		return nil, errors.Errorf("get kyc nfc status from UM failed, error=%v", err)
	}

	detail := stt.Details()[0]
	errInfo, ok := detail.(*errdetails.ErrorInfo)
	if !ok || !slices.Contains(noNfcReason, errInfo.Reason) {
		logger.Errorf("get kyc nfc status from UM failed, error=%v", err)
		return nil, errors.Errorf("get kyc nfc status from UM failed, error=%v", err)
	}

	return &model.UserKycNfc{NfcStatus: model.KycNfcStatusInvalid}, nil
}

func (u userProfileClient) ResetKycNfcData(ctx context.Context, zalopayID int64) error {
	logger := u.logger.WithContext(ctx)

	_, err := u.ukGrpcClient.ResetUserNFC(ctx, &uk.ResetUserNFCRequest{
		ZalopayId: cast.ToString(zalopayID),
	})
	if err != nil {
		logger.Errorf("reset kyc nfc data from UM failed, error=%v", err)
		return errors.Errorf("reset kyc nfc data from UM failed, error=%v", err)
	}
	return nil
}

func (u userProfileClient) VerifyKycNfcBCA(ctx context.Context, zalopayID int64) error {
	logger := u.logger.WithContext(ctx)

	_, err := u.ukGrpcClient.VerifyBCA(ctx, &uk.VerifyBCARequest{
		ZalopayId: cast.ToString(zalopayID),
	})
	if err != nil {
		logger.Errorf("verify kyc nfc data from UM failed, error=%v", err)
		return errors.Errorf("verify kyc nfc data from UM failed, error=%v", err)
	}
	return nil
}

func buildUserProfileInfo(umProfile *up.Profile) *model.UserProfile {
	return &model.UserProfile{
		KycLevel:             umProfile.GetKycProfile().GetLevel(),
		ProfileLevel:         getProfileLevel(umProfile.GetIdentityProfile()),
		FullName:             umProfile.GetIdentityProfile().GetFullName(),
		Email:                umProfile.GetBasicProfile().GetEmail(),
		Gender:               umProfile.GetIdentityProfile().GetGender(),
		Birthday:             getBirthday(umProfile.GetIdentityProfile()),
		PhoneNumber:          getPhoneNumber(umProfile.GetBasicProfile()),
		PermanentAddress:     umProfile.GetIdentityProfile().GetPermanentAddress(),
		IdNumber:             umProfile.GetIdentityProfile().GetIdNumber(),
		IdType:               model.IDType(umProfile.GetIdentityProfile().GetIdType()),
		IdIssueDate:          getIdIssueDate(umProfile.GetIdentityProfile()),
		IdIssuePlace:         umProfile.GetIdentityProfile().GetIssuePlaceText(),
		IdExpireDate:         getIdExpireDate(umProfile.GetIdentityProfile()),
		KycUpdatedDate:       getKycUpdateDate(umProfile.GetIdentityProfile()),
		ProfileImage:         getProfileImage(umProfile.GetIdentityProfile()),
		TempResidenceAddress: umProfile.GetIdentityProfile().GetPermanentAddress(), // Default value is same as permanent address
		KycNfcStatus:         toNfcStatus(umProfile.GetIdentityProfile().GetNfcStatus()),
	}

}

func getNfcSignature(resp *uk.GetUserNFCResponse, env string) string {
	envAllowMock := []string{"local", "dev", "qc"}
	if slices.Contains(envAllowMock, env) {
		return uuid.NewString()
	}
	return resp.GetBcaSignature()
}

func toNfcStatus(status up.IdentityProfile_NfcStatus) model.KycNfcStatus {
	switch status {
	case up.IdentityProfile_NFC_STATUS_DETECTED:
		return model.KycNfcStatusExisted
	case up.IdentityProfile_NFC_STATUS_VERIFIED:
		return model.KycNfcStatusVerified
	case up.IdentityProfile_NFC_STATUS_UNSPECIFIED:
		return model.KycNfcStatusInvalid
	default:
		return model.KycNfcStatusUnknown
	}
}

func getProfileLevel(idProfile *up.IdentityProfile) int32 {
	if idProfile.Approved {
		return int32(model.ProfileLevel3)
	}
	return int32(model.ProfileLevel2)
}

func getBirthday(idProfile *up.IdentityProfile) string {
	if idProfile.GetBirthday() == 0 {
		return ""
	}
	return utils.TimeFromSecond(idProfile.GetBirthday()).Format(utils.LayoutDashYYYYMMDD)
}

func getPhoneNumber(bsProfile *up.BasicProfile) string {
	phonePrefix := "^(?:\\+84|84)"
	phoneString := cast.ToString(bsProfile.GetPhoneNumber())
	if match, _ := regexp.MatchString(phonePrefix, phoneString); match {
		regex := regexp.MustCompile(phonePrefix)
		phoneString = regex.ReplaceAllString(phoneString, "0")
	}
	return phoneString
}

func getIdIssueDate(idProfile *up.IdentityProfile) string {
	if idProfile.GetIssueDate() == 0 {
		return ""
	}
	return utils.TimeFromSecond(idProfile.GetIssueDate()).Format(utils.LayoutDashYYYYMMDD)
}

func getIdExpireDate(idProfile *up.IdentityProfile) string {
	if idProfile.GetExpirationDate() == 0 {
		return ""
	}
	return utils.TimeFromSecond(idProfile.GetExpirationDate()).Format(utils.LayoutDashYYYYMMDD)
}

func getKycUpdateDate(idProfile *up.IdentityProfile) string {
	if idProfile.GetKycUpdateDate() == 0 {
		return ""
	}
	return utils.TimeFromSecond(idProfile.GetKycUpdateDate()).Format(time.RFC3339)
}

/**
 * Generate profile image from user identity profile
 */
func getProfileImage(idProfile *up.IdentityProfile) model.ProfileImage {
	return model.ProfileImage{
		AvatarUri:  idProfile.GetAvatarImageUri(),
		FrontICUri: idProfile.GetFrontImageUri(),
		BackICUri:  idProfile.GetBackImageUri(),
	}
}

func buildNfcRaw(nfcData *uk.GetUserNFCResponse) model.KycNfcDataRaw {
	return model.KycNfcDataRaw{
		Sod:  nfcData.GetNfcRaw().GetSod(),
		Com:  nfcData.GetNfcRaw().GetCom(),
		Dg1:  nfcData.GetNfcRaw().GetDg1(),
		Dg2:  nfcData.GetNfcRaw().GetDg2(),
		Dg13: nfcData.GetNfcRaw().GetDg13(),
		Dg14: nfcData.GetNfcRaw().GetDg14(),
		Dg15: nfcData.GetNfcRaw().GetDg15(),
	}
}

func buildNfcDG13(nfcData *uk.GetUserNFCResponse) model.KycNfcDataDG13 {
	dateOfBirth := ""
	idIssueDate := ""
	userGender := zutils.GenderNfcConvertToInt(nfcData.GetDg13().GetGender())
	dateOfBirthTime, _ := time.Parse(utils.LayoutSlashDDMMYYYY, nfcData.GetDg13().GetDob())
	idIssueDateTime, _ := time.Parse(utils.LayoutSlashDDMMYYYY, nfcData.GetDg13().GetIssueDate())
	if !dateOfBirthTime.IsZero() {
		dateOfBirth = dateOfBirthTime.Format(utils.LayoutDashYYYYMMDD)
	}
	if !idIssueDateTime.IsZero() {
		idIssueDate = idIssueDateTime.Format(utils.LayoutDashYYYYMMDD)
	}

	return model.KycNfcDataDG13{
		Gender:         userGender,
		Birthday:       dateOfBirth,
		IssueDate:      idIssueDate,
		EIDNumber:      nfcData.GetDg13().GetEidNumber(),
		FullName:       nfcData.GetDg13().GetName(),
		PlaceOrigin:    nfcData.GetDg13().GetPlaceOrigin(),
		PlaceResidence: nfcData.GetDg13().GetPlaceResidence(),
		OldIDNumber:    nfcData.GetDg13().GetCmnd(),
	}
}
