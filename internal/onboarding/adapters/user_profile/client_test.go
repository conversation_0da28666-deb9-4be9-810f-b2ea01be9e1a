package user_profile

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	ec "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc"
	up "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_profile/mocks"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	uk "gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1"
)

type UserProfileClientTestSuite struct {
	suite.Suite
	ctrl       *gomock.Controller
	client     *userProfileClient
	upMock     *mocks.MockUserProfileClient
	ukMock     *mocks.MockUserEkycNfcClient
	ecMock     *mocks.MockEKycCenterServiceClient
	mockConfig *config.Onboarding
}

func (s *UserProfileClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.upMock = mocks.NewMockUserProfileClient(s.ctrl)
	s.ukMock = mocks.NewMockUserEkycNfcClient(s.ctrl)
	s.ecMock = mocks.NewMockEKycCenterServiceClient(s.ctrl)

	// Setup mock config
	s.mockConfig = &config.Onboarding{}
	// Initialize client
	s.client = &userProfileClient{
		env:          "dev",
		logger:       log.NewHelper(log.DefaultLogger),
		ecConf:       &Config{ClientID: "test", ClientKey: "test"},
		upGrpcClient: s.upMock,
		ukGrpcClient: s.ukMock,
		ecGrpcClient: s.ecMock,
	}
}

func TestUserProfileClientSuite(t *testing.T) {
	suite.Run(t, new(UserProfileClientTestSuite))
}

// Main service method tests
func (s *UserProfileClientTestSuite) TestGetProfile_Success() {
	// Arrange
	req := dto.ZPUserProfileParams{
		ZalopayID:       123456,
		KycProfile:      true,
		BasicProfile:    true,
		IdentityProfile: true,
	}

	mockResp := &up.Profile{
		BasicProfile: &up.BasicProfile{
			Email:       "<EMAIL>",
			PhoneNumber: 123456789,
		},
		IdentityProfile: &up.IdentityProfile{
			FullName: "Test User",
			Gender:   1,
		},
		KycProfile: &up.KycProfile{
			Level: 2,
		},
	}

	s.upMock.EXPECT().QueryByUserId(gomock.Any(), gomock.Any()).Return(mockResp, nil)

	// Act
	result, err := s.client.GetProfile(context.Background(), req)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal("Test User", result.FullName)
	s.Equal("<EMAIL>", result.Email)
}

func (s *UserProfileClientTestSuite) TestGetProfile_Error() {
	// Arrange
	req := dto.ZPUserProfileParams{
		ZalopayID:       123456,
		KycProfile:      true,
		BasicProfile:    true,
		IdentityProfile: true,
	}

	s.upMock.EXPECT().QueryByUserId(gomock.Any(), gomock.Any()).Return(nil, errors.New("service unavailable"))

	// Act
	result, err := s.client.GetProfile(context.Background(), req)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "get profile from UM failed")
}

func (s *UserProfileClientTestSuite) TestGetKycNfcData_Success() {
	// Arrange
	mockResp := &uk.GetUserNFCResponse{
		Dg2:          "dg2data",
		BcaSignature: "signature",
		NfcRaw: &uk.NFCRaw{
			Sod:  "sod",
			Com:  "com",
			Dg1:  "dg1",
			Dg2:  "dg2",
			Dg13: "dg13",
			Dg14: "dg14",
			Dg15: "dg15",
		},
	}

	s.ukMock.EXPECT().GetUserNFC(gomock.Any(), gomock.Any()).Return(mockResp, nil)

	// Act
	result, err := s.client.GetKycNfcData(context.Background(), 123456)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal("dg2data", result.NfcDataDG2)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcData_NoNfcReason() {
	// Arrange
	st := status.New(codes.NotFound, "not found")
	st, _ = st.WithDetails(&errdetails.ErrorInfo{
		Reason: uk.ReasonCode_REASON_CODE_NOT_EKYC.String(),
	})

	s.ukMock.EXPECT().GetUserNFC(gomock.Any(), gomock.Any()).Return(nil, st.Err())

	// Act
	result, err := s.client.GetKycNfcData(context.Background(), 123456)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Empty(result.NfcDataRaw)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcData_Error() {
	// Arrange
	s.ukMock.EXPECT().GetUserNFC(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error"))

	// Act
	result, err := s.client.GetKycNfcData(context.Background(), 123456)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcData_InvalidErrorInfo() {
	// Arrange
	st := status.New(codes.NotFound, "not found")
	st, _ = st.WithDetails(&errdetails.ErrorInfo{
		Reason: "invalid_reason",
	})

	s.ukMock.EXPECT().GetUserNFC(gomock.Any(), gomock.Any()).Return(nil, st.Err())

	// Act
	result, err := s.client.GetKycNfcData(context.Background(), 123456)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *UserProfileClientTestSuite) TestGetKycProgStatus_Success() {
	// Arrange
	mockResp := &ec.GetUserEKycStatusResponse{
		Status: ec.GetUserEKycStatusResponse_APPROVE,
	}
	s.ecMock.EXPECT().GetUserEKycStatus(gomock.Any(), gomock.Any()).Return(mockResp, nil)

	// Act
	result, err := s.client.GetKycProgStatus(context.Background(), 123456)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.KycStatus(ec.GetUserEKycStatusResponse_APPROVE), result.Status)
}

func (s *UserProfileClientTestSuite) TestGetKycProgStatus_Error() {
	// Arrange
	s.ecMock.EXPECT().GetUserEKycStatus(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error"))

	// Act
	result, err := s.client.GetKycProgStatus(context.Background(), 123456)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcStatus_Success() {
	// Arrange
	mockResp := &uk.GetStatusResponse{
		Status: 3,
	}
	s.ukMock.EXPECT().GetStatus(gomock.Any(), gomock.Any()).Return(mockResp, nil)

	// Act
	result, err := s.client.GetKycNfcStatus(context.Background(), 123456)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.KycNfcStatusVerified, result.NfcStatus)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcStatus_NoNfcReason() {
	// Arrange
	st := status.New(codes.NotFound, "not found")
	st, _ = st.WithDetails(&errdetails.ErrorInfo{
		Reason: uk.ReasonCode_REASON_CODE_NOT_EKYC.String(),
	})

	s.ukMock.EXPECT().GetStatus(gomock.Any(), gomock.Any()).Return(nil, st.Err())

	// Act
	result, err := s.client.GetKycNfcStatus(context.Background(), 123456)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.KycNfcStatusInvalid, result.NfcStatus)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcStatus_Error() {
	// Arrange
	s.ukMock.EXPECT().GetStatus(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error"))

	// Act
	result, err := s.client.GetKycNfcStatus(context.Background(), 123456)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *UserProfileClientTestSuite) TestGetKycNfcStatus_InvalidErrorInfo() {
	// Arrange
	st := status.New(codes.NotFound, "not found")
	st, _ = st.WithDetails(&errdetails.ErrorInfo{
		Reason: "invalid_reason",
	})

	s.ukMock.EXPECT().GetStatus(gomock.Any(), gomock.Any()).Return(nil, st.Err())

	// Act
	result, err := s.client.GetKycNfcStatus(context.Background(), 123456)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *UserProfileClientTestSuite) TestResetKycNfcData_Success() {
	// Arrange
	s.ukMock.EXPECT().ResetUserNFC(gomock.Any(), gomock.Any()).Return(&uk.ResetUserNFCResponse{}, nil)

	// Act
	err := s.client.ResetKycNfcData(context.Background(), 123456)

	// Assert
	s.NoError(err)
}

func (s *UserProfileClientTestSuite) TestResetKycNfcData_Error() {
	// Arrange
	s.ukMock.EXPECT().ResetUserNFC(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error"))

	// Act
	err := s.client.ResetKycNfcData(context.Background(), 123456)

	// Assert
	s.Error(err)
}

func (s *UserProfileClientTestSuite) TestVerifyKycNfcBCA_Success() {
	// Arrange
	s.ukMock.EXPECT().VerifyBCA(gomock.Any(), gomock.Any()).Return(&uk.VerifyBCAResponse{}, nil)

	// Act
	err := s.client.VerifyKycNfcBCA(context.Background(), 123456)

	// Assert
	s.NoError(err)
}

func (s *UserProfileClientTestSuite) TestVerifyKycNfcBCA_Error() {
	// Arrange
	s.ukMock.EXPECT().VerifyBCA(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error"))

	// Act
	err := s.client.VerifyKycNfcBCA(context.Background(), 123456)

	// Assert
	s.Error(err)
}

// Tests specifically for selection code
func (s *UserProfileClientTestSuite) TestGetNfcSignature() {
	// Arrange
	resp := &uk.GetUserNFCResponse{
		BcaSignature: "original-signature",
	}

	// Act & Assert
	// Production environment should return original signature
	s.Equal("original-signature", getNfcSignature(resp, "prod"))
	s.Equal("original-signature", getNfcSignature(resp, "stg"))

	// Test environments should generate a new UUID
	devEnvSig := getNfcSignature(resp, "dev")
	localEnvSig := getNfcSignature(resp, "local")
	qcEnvSig := getNfcSignature(resp, "qc")

	s.NotEqual("", devEnvSig)
	s.NotEqual("", localEnvSig)
	s.NotEqual("", qcEnvSig)
	s.NotEqual("original-signature", devEnvSig)
	s.NotEqual("original-signature", localEnvSig)
	s.NotEqual("original-signature", qcEnvSig)
}

func (s *UserProfileClientTestSuite) TestToNfcStatus() {
	// Test all possible status conversions
	s.Equal(model.KycNfcStatusExisted, toNfcStatus(up.IdentityProfile_NFC_STATUS_DETECTED))
	s.Equal(model.KycNfcStatusVerified, toNfcStatus(up.IdentityProfile_NFC_STATUS_VERIFIED))
	s.Equal(model.KycNfcStatusInvalid, toNfcStatus(up.IdentityProfile_NFC_STATUS_UNSPECIFIED))

	// Test with numeric values out of defined range
	s.Equal(model.KycNfcStatusUnknown, toNfcStatus(99))
}

func (s *UserProfileClientTestSuite) TestGetProfileLevel() {
	// Test approved case
	approvedProfile := &up.IdentityProfile{Approved: true}
	s.Equal(int32(model.ProfileLevel3), getProfileLevel(approvedProfile))

	// Test not approved case
	notApprovedProfile := &up.IdentityProfile{Approved: false}
	s.Equal(int32(model.ProfileLevel2), getProfileLevel(notApprovedProfile))
}

func (s *UserProfileClientTestSuite) TestBuildNfcRaw() {
	// Test with complete data
	nfcData := &uk.GetUserNFCResponse{
		NfcRaw: &uk.NFCRaw{
			Sod:  "sod-data",
			Com:  "com-data",
			Dg1:  "dg1-data",
			Dg2:  "dg2-data",
			Dg13: "dg13-data",
			Dg14: "dg14-data",
			Dg15: "dg15-data",
		},
	}

	result := buildNfcRaw(nfcData)
	s.Equal("sod-data", result.Sod)
	s.Equal("com-data", result.Com)
	s.Equal("dg1-data", result.Dg1)
	s.Equal("dg2-data", result.Dg2)
	s.Equal("dg13-data", result.Dg13)
	s.Equal("dg14-data", result.Dg14)
	s.Equal("dg15-data", result.Dg15)

	// Test with nil data
	nilData := &uk.GetUserNFCResponse{}
	nilResult := buildNfcRaw(nilData)
	s.Equal("", nilResult.Sod)
	s.Equal("", nilResult.Com)
}

func (s *UserProfileClientTestSuite) TestBuildNfcDG13() {
	// Test with valid dates
	nfcData := &uk.GetUserNFCResponse{
		Dg13: &uk.NFCDG13{
			Name:           "Test User",
			Gender:         "Nam",
			Dob:            "01/01/1990",
			PlaceOrigin:    "Test Origin",
			PlaceResidence: "Test Residence",
			IssueDate:      "01/01/2020",
			EidNumber:      "123456789",
			Cmnd:           "987654321",
		},
	}

	result := buildNfcDG13(nfcData)
	s.Equal(int32(1), result.Gender) // "Nam" converted to 1
	s.Equal("1990-01-01", result.Birthday)
	s.Equal("2020-01-01", result.IssueDate)
	s.Equal("123456789", result.EIDNumber)
	s.Equal("Test User", result.FullName)
	s.Equal("Test Origin", result.PlaceOrigin)
	s.Equal("Test Residence", result.PlaceResidence)
	s.Equal("987654321", result.OldIDNumber)

	// Test with female gender
	femaleData := &uk.GetUserNFCResponse{
		Dg13: &uk.NFCDG13{
			Gender: "Nữ",
		},
	}
	s.Equal(int32(2), buildNfcDG13(femaleData).Gender)

	// Test with invalid date formats
	invalidDates := &uk.GetUserNFCResponse{
		Dg13: &uk.NFCDG13{
			Dob:       "invalid-date",
			IssueDate: "invalid-date",
		},
	}
	invalidResult := buildNfcDG13(invalidDates)
	s.Equal("", invalidResult.Birthday)
	s.Equal("", invalidResult.IssueDate)

	// Test with nil DG13
	nilData := &uk.GetUserNFCResponse{}
	nilResult := buildNfcDG13(nilData)
	s.Equal(int32(0), nilResult.Gender)
	s.Equal("", nilResult.Birthday)
	s.Equal("", nilResult.EIDNumber)
}
