// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile (interfaces: UserProfileClient)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/user_profile.go -package=mocks gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile UserProfileClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	user_profile_grpc_client "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/user_profile"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUserProfileClient is a mock of UserProfileClient interface.
type MockUserProfileClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserProfileClientMockRecorder
	isgomock struct{}
}

// MockUserProfileClientMockRecorder is the mock recorder for MockUserProfileClient.
type MockUserProfileClientMockRecorder struct {
	mock *MockUserProfileClient
}

// NewMockUserProfileClient creates a new mock instance.
func NewMockUserProfileClient(ctrl *gomock.Controller) *MockUserProfileClient {
	mock := &MockUserProfileClient{ctrl: ctrl}
	mock.recorder = &MockUserProfileClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserProfileClient) EXPECT() *MockUserProfileClientMockRecorder {
	return m.recorder
}

// QueryByPhone mocks base method.
func (m *MockUserProfileClient) QueryByPhone(ctx context.Context, in *user_profile_grpc_client.QueryByPhoneRequest, opts ...grpc.CallOption) (*user_profile_grpc_client.Profile, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByPhone", varargs...)
	ret0, _ := ret[0].(*user_profile_grpc_client.Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByPhone indicates an expected call of QueryByPhone.
func (mr *MockUserProfileClientMockRecorder) QueryByPhone(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByPhone", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByPhone), varargs...)
}

// QueryByUserId mocks base method.
func (m *MockUserProfileClient) QueryByUserId(ctx context.Context, in *user_profile_grpc_client.QueryByUserIdRequest, opts ...grpc.CallOption) (*user_profile_grpc_client.Profile, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByUserId", varargs...)
	ret0, _ := ret[0].(*user_profile_grpc_client.Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByUserId indicates an expected call of QueryByUserId.
func (mr *MockUserProfileClientMockRecorder) QueryByUserId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByUserId", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByUserId), varargs...)
}

// QueryByZaloId mocks base method.
func (m *MockUserProfileClient) QueryByZaloId(ctx context.Context, in *user_profile_grpc_client.QueryByZaloIdRequest, opts ...grpc.CallOption) (*user_profile_grpc_client.Profile, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByZaloId", varargs...)
	ret0, _ := ret[0].(*user_profile_grpc_client.Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByZaloId indicates an expected call of QueryByZaloId.
func (mr *MockUserProfileClientMockRecorder) QueryByZaloId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByZaloId", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByZaloId), varargs...)
}

// VerifyPin mocks base method.
func (m *MockUserProfileClient) VerifyPin(ctx context.Context, in *user_profile_grpc_client.VerifyPinRequest, opts ...grpc.CallOption) (*user_profile_grpc_client.VerifyPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyPin", varargs...)
	ret0, _ := ret[0].(*user_profile_grpc_client.VerifyPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPin indicates an expected call of VerifyPin.
func (mr *MockUserProfileClientMockRecorder) VerifyPin(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPin", reflect.TypeOf((*MockUserProfileClient)(nil).VerifyPin), varargs...)
}
