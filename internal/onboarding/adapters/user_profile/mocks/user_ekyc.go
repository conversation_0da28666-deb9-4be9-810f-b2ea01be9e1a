// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc (interfaces: EKycCenterServiceClient)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/user_ekyc.go -package=mocks gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc EKycCenterServiceClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	ekyccenterv2 "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ekyc"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEKycCenterServiceClient is a mock of EKycCenterServiceClient interface.
type MockEKycCenterServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockEKycCenterServiceClientMockRecorder
	isgomock struct{}
}

// MockEKycCenterServiceClientMockRecorder is the mock recorder for MockEKycCenterServiceClient.
type MockEKycCenterServiceClientMockRecorder struct {
	mock *MockEKycCenterServiceClient
}

// NewMockEKycCenterServiceClient creates a new mock instance.
func NewMockEKycCenterServiceClient(ctrl *gomock.Controller) *MockEKycCenterServiceClient {
	mock := &MockEKycCenterServiceClient{ctrl: ctrl}
	mock.recorder = &MockEKycCenterServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEKycCenterServiceClient) EXPECT() *MockEKycCenterServiceClientMockRecorder {
	return m.recorder
}

// GetUserEKycStatus mocks base method.
func (m *MockEKycCenterServiceClient) GetUserEKycStatus(ctx context.Context, in *ekyccenterv2.GetUserEKycStatusRequest, opts ...grpc.CallOption) (*ekyccenterv2.GetUserEKycStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserEKycStatus", varargs...)
	ret0, _ := ret[0].(*ekyccenterv2.GetUserEKycStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserEKycStatus indicates an expected call of GetUserEKycStatus.
func (mr *MockEKycCenterServiceClientMockRecorder) GetUserEKycStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEKycStatus", reflect.TypeOf((*MockEKycCenterServiceClient)(nil).GetUserEKycStatus), varargs...)
}
