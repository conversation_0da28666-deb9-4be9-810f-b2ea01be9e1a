// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1 (interfaces: UserEkycNfcClient)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/user_nfc.go -package=mocks gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1 UserEkycNfcClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	userekycnfcv1 "gitlab.zalopay.vn/grpc-specifications/user-ekyc-nfc/pkg/user/userekycnfcv1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUserEkycNfcClient is a mock of UserEkycNfcClient interface.
type MockUserEkycNfcClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserEkycNfcClientMockRecorder
	isgomock struct{}
}

// MockUserEkycNfcClientMockRecorder is the mock recorder for MockUserEkycNfcClient.
type MockUserEkycNfcClientMockRecorder struct {
	mock *MockUserEkycNfcClient
}

// NewMockUserEkycNfcClient creates a new mock instance.
func NewMockUserEkycNfcClient(ctrl *gomock.Controller) *MockUserEkycNfcClient {
	mock := &MockUserEkycNfcClient{ctrl: ctrl}
	mock.recorder = &MockUserEkycNfcClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserEkycNfcClient) EXPECT() *MockUserEkycNfcClientMockRecorder {
	return m.recorder
}

// GetListMessage mocks base method.
func (m *MockUserEkycNfcClient) GetListMessage(ctx context.Context, in *userekycnfcv1.GetListMessageRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetListMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetListMessage", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetListMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetListMessage indicates an expected call of GetListMessage.
func (mr *MockUserEkycNfcClientMockRecorder) GetListMessage(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListMessage", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetListMessage), varargs...)
}

// GetListSource mocks base method.
func (m *MockUserEkycNfcClient) GetListSource(ctx context.Context, in *userekycnfcv1.GetListSourceRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetListSourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetListSource", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetListSourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetListSource indicates an expected call of GetListSource.
func (mr *MockUserEkycNfcClientMockRecorder) GetListSource(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListSource", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetListSource), varargs...)
}

// GetListUserNFC mocks base method.
func (m *MockUserEkycNfcClient) GetListUserNFC(ctx context.Context, in *userekycnfcv1.GetListUserNFCRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetListUserNFCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetListUserNFC", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetListUserNFCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetListUserNFC indicates an expected call of GetListUserNFC.
func (mr *MockUserEkycNfcClientMockRecorder) GetListUserNFC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetListUserNFC", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetListUserNFC), varargs...)
}

// GetNFC mocks base method.
func (m *MockUserEkycNfcClient) GetNFC(ctx context.Context, in *userekycnfcv1.GetNFCRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetNFCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNFC", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetNFCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFC indicates an expected call of GetNFC.
func (mr *MockUserEkycNfcClientMockRecorder) GetNFC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFC", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetNFC), varargs...)
}

// GetStatus mocks base method.
func (m *MockUserEkycNfcClient) GetStatus(ctx context.Context, in *userekycnfcv1.GetStatusRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStatus", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatus indicates an expected call of GetStatus.
func (mr *MockUserEkycNfcClientMockRecorder) GetStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatus", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetStatus), varargs...)
}

// GetTicket mocks base method.
func (m *MockUserEkycNfcClient) GetTicket(ctx context.Context, in *userekycnfcv1.GetTicketRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetTicketResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicket", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicket indicates an expected call of GetTicket.
func (mr *MockUserEkycNfcClientMockRecorder) GetTicket(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicket", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetTicket), varargs...)
}

// GetTickets mocks base method.
func (m *MockUserEkycNfcClient) GetTickets(ctx context.Context, in *userekycnfcv1.GetTicketsRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTickets", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTickets indicates an expected call of GetTickets.
func (mr *MockUserEkycNfcClientMockRecorder) GetTickets(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTickets", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetTickets), varargs...)
}

// GetUserNFC mocks base method.
func (m *MockUserEkycNfcClient) GetUserNFC(ctx context.Context, in *userekycnfcv1.GetUserNFCRequest, opts ...grpc.CallOption) (*userekycnfcv1.GetUserNFCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserNFC", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.GetUserNFCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNFC indicates an expected call of GetUserNFC.
func (mr *MockUserEkycNfcClientMockRecorder) GetUserNFC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNFC", reflect.TypeOf((*MockUserEkycNfcClient)(nil).GetUserNFC), varargs...)
}

// MigrateBankMB mocks base method.
func (m *MockUserEkycNfcClient) MigrateBankMB(ctx context.Context, in *userekycnfcv1.MigrateBankMBRequest, opts ...grpc.CallOption) (*userekycnfcv1.MigrateBankMBResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MigrateBankMB", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.MigrateBankMBResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MigrateBankMB indicates an expected call of MigrateBankMB.
func (mr *MockUserEkycNfcClientMockRecorder) MigrateBankMB(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateBankMB", reflect.TypeOf((*MockUserEkycNfcClient)(nil).MigrateBankMB), varargs...)
}

// ProcessTicket mocks base method.
func (m *MockUserEkycNfcClient) ProcessTicket(ctx context.Context, in *userekycnfcv1.ProcessTicketRequest, opts ...grpc.CallOption) (*userekycnfcv1.ProcessTicketResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessTicket", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.ProcessTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTicket indicates an expected call of ProcessTicket.
func (mr *MockUserEkycNfcClientMockRecorder) ProcessTicket(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTicket", reflect.TypeOf((*MockUserEkycNfcClient)(nil).ProcessTicket), varargs...)
}

// ResetUserNFC mocks base method.
func (m *MockUserEkycNfcClient) ResetUserNFC(ctx context.Context, in *userekycnfcv1.ResetUserNFCRequest, opts ...grpc.CallOption) (*userekycnfcv1.ResetUserNFCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResetUserNFC", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.ResetUserNFCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetUserNFC indicates an expected call of ResetUserNFC.
func (mr *MockUserEkycNfcClientMockRecorder) ResetUserNFC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetUserNFC", reflect.TypeOf((*MockUserEkycNfcClient)(nil).ResetUserNFC), varargs...)
}

// SubmitNFC mocks base method.
func (m *MockUserEkycNfcClient) SubmitNFC(ctx context.Context, in *userekycnfcv1.SubmitNFCRequest, opts ...grpc.CallOption) (*userekycnfcv1.SubmitNFCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitNFC", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.SubmitNFCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitNFC indicates an expected call of SubmitNFC.
func (mr *MockUserEkycNfcClientMockRecorder) SubmitNFC(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitNFC", reflect.TypeOf((*MockUserEkycNfcClient)(nil).SubmitNFC), varargs...)
}

// SubmitVNEID mocks base method.
func (m *MockUserEkycNfcClient) SubmitVNEID(ctx context.Context, in *userekycnfcv1.SubmitVNEIDRequest, opts ...grpc.CallOption) (*userekycnfcv1.SubmitVNEIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitVNEID", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.SubmitVNEIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitVNEID indicates an expected call of SubmitVNEID.
func (mr *MockUserEkycNfcClientMockRecorder) SubmitVNEID(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitVNEID", reflect.TypeOf((*MockUserEkycNfcClient)(nil).SubmitVNEID), varargs...)
}

// VerifyBCA mocks base method.
func (m *MockUserEkycNfcClient) VerifyBCA(ctx context.Context, in *userekycnfcv1.VerifyBCARequest, opts ...grpc.CallOption) (*userekycnfcv1.VerifyBCAResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyBCA", varargs...)
	ret0, _ := ret[0].(*userekycnfcv1.VerifyBCAResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyBCA indicates an expected call of VerifyBCA.
func (mr *MockUserEkycNfcClientMockRecorder) VerifyBCA(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyBCA", reflect.TypeOf((*MockUserEkycNfcClient)(nil).VerifyBCA), varargs...)
}
