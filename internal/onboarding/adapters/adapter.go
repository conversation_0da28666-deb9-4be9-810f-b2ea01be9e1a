package adapters

import (
	"github.com/google/wire"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/account_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/auth_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/cimb_service"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/dist_lock"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/image_fetcher"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/job_task"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/minio_storage"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/rate_limiter"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/risk_system"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_challenge"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_profile"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/whitelist"
)

var ProviderSet = wire.NewSet(
	whitelist.NewClient,
	risk_system.NewClient,
	user_profile.NewClient,
	auth_service.NewClient,
	cimb_service.NewService,
	dist_lock.NewDistLock,
	job_task.NewJobTaskMgmt,
	user_challenge.NewClient,
	account_service.NewClient,
	minio_storage.NewMinioUpload,
	image_fetcher.NewImageFetcher,
	rate_limiter.NewClient,
)
