package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

func (s service) MarkContractComplete(ctx context.Context,
	zalopayID int64, faceReqID,
	partnerReqID, partnerTransID string) (signed bool, err error) {
	logger := s.logger.WithContext(ctx)
	cliSession := utils.ClientSessionFromCtx(ctx)

	confirmResp, err := s.connectorClient.ConfirmContractCompletion(ctx, &connector.ConfirmContractCompletionRequest{
		ZalopayId:        zalopayID,
		BankRequestId:    partnerReqID,
		BankOtpRequestId: partnerTransID,
		AuthenticationInfo: &connector.AuthenticationInfo{
			Id:   faceReqID,
			Type: connector.AuthenticationInfo_PARTNER_PIN_PASS,
		},
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
	})

	connErr, parseErr := parseConnectorError(err)

	if err != nil && parseErr != nil {
		logger.Errorf("ConfirmContractCompletion from cimb fail: %v", err)
		return false, errors.Errorf("ConfirmContractCompletion from cimb fail: %v", err)
	}
	if connErr != nil && connErr.SubCode == common.SubCode_CONTRACT_HAS_BEEN_SIGNED {
		logger.Warn("ConfirmContractCompletion from cimb warning: contract has been signed")
		return true, nil
	}
	if connErr != nil {
		logger.Errorf("ConfirmContractCompletion from cimb fail: %v", connErr)
		return false, errors.Errorf("ConfirmContractCompletion from cimb fail: %v", connErr)
	}
	if !confirmResp.GetIsSuccess() {
		logger.Warnf("contract confirm status is not done")
		return false, errors.Errorf("Contract confirm status is not done")
	}
	return true, nil
}
