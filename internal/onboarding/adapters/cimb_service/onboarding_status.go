package cimb_service

import (
	"context"
	"fmt"

	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
)

func (s service) QueryOnboardingStatus(ctx context.Context,
	zalopayID int64, onboardingID int64) (*cimb.OnboardingStatus, error) {
	zlpReqID, _ := s.generateRequestID(onboardingID)

	statusResp, err := s.connectorClient.QueryOnboardingStatus(ctx, &connector.OnboardingStatusRequest{
		ZalopayId:         zalopayID,
		ZalopayRequestIds: []string{zlpReqID},
	})
	if err != nil {
		return nil, err
	}
	if statusResp == nil || len(statusResp.GetOnboardingStatuses()) == 0 {
		return nil, fmt.Errorf("QueryOnboardingStatus has empty response")
	}

	statusRes := statusResp.GetOnboardingStatuses()[0]
	obStatus := &cimb.OnboardingStatus{
		RequestID:             statusRes.GetBankRequestId(),
		ODStatus:              cimb.ODStatusFromString(statusRes.GetOverdraftStatus()),
		CASAStatus:            cimb.CASAStatusFromString(statusRes.GetStatus()),
		SigningStatus:         cimb.SigningStatusFromString(statusRes.GetContractSigningStatus()),
		ErrorDetail:           statusRes.GetErrorDetail(),
		ManualApprovalReasons: statusRes.GetManualApprovalReasons(),
	}
	return obStatus, nil
}
