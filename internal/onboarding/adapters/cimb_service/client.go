package cimb_service

import (
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type service struct {
	env             string
	logger          *log.Helper
	timeNow         func() time.Time
	httpClient      base.CommonHttpClient
	connectorClient connector.CIMBConnectorClient
}

const (
	overdraftType           = "INSTALLMENT"
	requestIDPattern        = "installment-%d"
	requestIDWithEnvPattern = "installment:%s-%d"
	linkAccountRemark       = "Liên kết tài khoản ZaloPay với tài khoản CIMB để có thể sử dụng sản phẩm Tr<PERSON>"
)

func NewService(
	config *config.Onboarding,
	client connector.CIMBConnectorClient,
	httpClient base.CommonHttpClient,
	kLogger log.Logger) _interface.CIMBService {
	envCfg := config.GetApp().GetEnv()
	logger := log.NewHelper(log.With(kLogger, "adapters", "cimb-connector"))
	return &service{
		env:             envCfg,
		logger:          logger,
		timeNow:         time.Now,
		httpClient:      httpClient,
		connectorClient: client,
	}
}

// Example: installment:stg-1, installment-1
func (s service) generateRequestID(onboardingID int64) (string, error) {
	if onboardingID == 0 {
		return "", fmt.Errorf("onboardingID must be greater than 0")
	}
	switch s.env {
	case "prod":
		return fmt.Sprintf(requestIDPattern, onboardingID), nil
	default:
		return fmt.Sprintf(requestIDWithEnvPattern, s.env, onboardingID), nil
	}
}

func getFirstNonEmpty(values ...string) string {
	for _, v := range values {
		if v != "" {
			return v
		}
	}
	return ""
}
