package cimb_service

import (
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
)

type ConnectorErr struct {
	Code    codes.Code
	SubCode common.SubCode
}

func parseConnectorError(err error) (*ConnectorErr, error) {
	if err == nil {
		return nil, nil
	}
	sttErr, ok := status.FromError(err)
	if !ok {
		return nil, errors.New("fail to extract status code")
	}
	if len(sttErr.Details()) == 0 {
		return nil, errors.New("no error detail")
	}
	errorInfo, ok := sttErr.Details()[0].(*common.ErrorInfo)
	if !ok {
		return nil, errors.New("fail to extract detail")
	}
	return &ConnectorErr{
		Code:    sttErr.Code(),
		SubCode: errorInfo.SubCode,
	}, nil
}

func subCodeToPartnerCode(subCode common.SubCode) string {
	if errorCode, ok := subCodeMapping[subCode]; ok {
		return errorCode.String()
	}
	return cimb.ErrorCodeUnknown.String()
}

var subCodeMapping = map[common.SubCode]cimb.ErrorCode{
	common.SubCode_INTERNAL:                                    cimb.ErrorCodeUnknown,
	common.SubCode_BAD_REQUEST:                                 cimb.ErrorCodeBadRequest,
	common.SubCode_NOT_ALLOW_UPDATE:                            cimb.ErrorCodeNotAllowUpdate,
	common.SubCode_PHONE_USED:                                  cimb.ErrorCodePhoneNumberUsed,
	common.SubCode_ONBOARDING_PROCCED_ALREADY:                  cimb.ErrorCodeOnboardingProcessing,
	common.SubCode_ONBOARDING_PRODUCT_NOT_ALLOWED:              cimb.ErrorCodeOnboardingNotAllowed,
	common.SubCode_ONBOARDING_INFORMATION_USED:                 cimb.ErrorCodeOnboardingInfoUsed,
	common.SubCode_IC_NUMBER_USED:                              cimb.ErrorCodeICNumberUsed,
	common.SubCode_EMAIL_OR_PHONE_USED:                         cimb.ErrorCodeEmailOrPhoneUsed,
	common.SubCode_EXISTING_CIMB_CUSTOMER_FOUND:                cimb.ErrorCodeCustomerExisting,
	common.SubCode_DUPLICATED_REQUEST_ID:                       cimb.ErrorCodeDuplicateReqID,
	common.SubCode_CIMB_BAD_REQUEST:                            cimb.ErrorCodeBadRequest,
	common.SubCode_GENDER_INVALID:                              cimb.ErrorCodeGenderInvalid,
	common.SubCode_AGE_NOT_ALLOW:                               cimb.ErrorCodeAgeNotAllow,
	common.SubCode_PARTNER_REQUEST_ID_IS_EXISTING:              cimb.ErrorCodeRequestIDExist,
	common.SubCode_IDENTITY_INFORMATION_NOT_MATCH_WITH_CURRENT: cimb.ErrorCodeCurrIdentityNotMatch,
}
