package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
)

func (s service) InitContractProcess(ctx context.Context,
	zalopayID int64, partnerReqID string) (contractTxnID string, err error) {
	logger := s.logger.WithContext(ctx)

	resp, err := s.connectorClient.InitSignContract(ctx, &connector.InitSignContractRequest{
		ZalopayId:     zalopayID,
		BankRequestId: partnerReqID,
	})

	connErr, parseErr := parseConnectorError(err)
	if err != nil && parseErr != nil {
		logger.Errorf("InitSignContract from cimb fail: %v", err)
		return "", errors.Errorf("InitSignContract from cimb fail: %v", err)
	}
	if connErr != nil && connErr.SubCode == common.SubCode_CONTRACT_HAS_BEEN_SIGNED {
		logger.Warn("InitSignContract from cimb warning: contract has been signed")
		return "", model.ErrContractHasBeenSigned
	}
	if connErr != nil {
		logger.Errorf("InitSignContract from cimb fail: %v", connErr)
		return "", errors.Errorf("InitSignContract from cimb fail: %v", connErr)
	}
	return resp.GetTransactionId(), nil
}
