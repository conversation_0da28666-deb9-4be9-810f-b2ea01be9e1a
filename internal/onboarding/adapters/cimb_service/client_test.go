package cimb_service

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	httpclientmocks "gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocks/httpclient"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"
)

type CimbClientTestSuite struct {
	suite.Suite
	service        *service
	mockConnector  *connector.MockCIMBConnectorClient
	mockHttpClient *httpclientmocks.MockHttpClient
}

func TestCimbClientSuite(t *testing.T) {
	suite.Run(t, new(CimbClientTestSuite))
}

func (s *CimbClientTestSuite) SetupTest() {
	ubctrl := gomock.NewController(s.T())
	s.mockHttpClient = httpclientmocks.NewMockHttpClient(ubctrl)
	s.mockConnector = connector.NewMockCIMBConnectorClient(ubctrl)
	s.service = &service{
		env:             "dev",
		httpClient:      s.mockHttpClient,
		connectorClient: s.mockConnector,
		logger:          log.<PERSON><PERSON>elper(log.DefaultLogger),
	}
}

func (s *CimbClientTestSuite) TestGenerateRequestID() {
	// Test cases
	testCases := []struct {
		name         string
		env          string
		onboardingID int64
		expected     string
	}{
		{
			name:         "dev environment",
			env:          "dev",
			onboardingID: 123,
			expected:     "installment:dev-123",
		},
		{
			name:         "staging environment",
			env:          "stg",
			onboardingID: 456,
			expected:     "installment:stg-456",
		},
		{
			name:         "production environment",
			env:          "prod",
			onboardingID: 789,
			expected:     "installment-789",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			s.service.env = tc.env
			result, err := s.service.generateRequestID(tc.onboardingID)
			s.NoError(err)
			s.Equal(tc.expected, result)
		})
	}
}
