package cimb_service

import (
	"context"
	"encoding/base64"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

func (s *CimbClientTestSuite) TestSubmitCasaOnboarding_Success() {
	// Arrange
	ctx := context.Background()
	ctx = utils.AttachClientSessionToCtx(ctx, &model.ClientSession{
		Latitude:  10.123,
		Longitude: 106.456,
		RequestIP: "127.0.0.1",
		DeviceID:  "device123",
	})

	onboarding := &model.Onboarding{
		ID:        123,
		ZalopayID: 456,
		UserInfo: &cimb.UserOnboardingInfo{
			UserProfile: model.UserProfile{
				FullName:             "<PERSON>",
				PhoneNumber:          "0123456789",
				IdIssuePlace:         "HCM",
				IdExpireDate:         "2030-01-01",
				IdNumber:             "123456789",
				TempResidenceAddress: "123 Street",
				PermanentAddress:     "123 Street",
			},
			UserKycNfc: model.UserKycNfc{
				NfcDataDG13: model.KycNfcDataDG13{
					FullName:       "John Doe",
					EIDNumber:      "123456789",
					IssueDate:      "2020-01-01",
					Birthday:       "1990-01-01",
					Gender:         1,
					PlaceResidence: "123 Street",
					PlaceOrigin:    "HCM",
				},
				NfcSignature:  "signature",
				CollectSource: "jth", // Example source
			},
			UserOnboardingExtra: cimb.UserOnboardingExtra{
				Education:        "UNIVERSITY",
				LoanPurpose:      "PERSONAL",
				OverdraftLimit:   ********,
				EmploymentStatus: "FULL_TIME",
				MonthlyIncome:    "********",
				City:             "HCM",
				Occupation:       "ENGINEER",
				JobTitle:         "DEVELOPER",
				SourceOfFund:     "SALARY",
				FundPurpose:      "SAVING",
			},
			UserUnderwritingData: map[string]any{},
		},
		IcImages: model.ICImage{
			FrontIC: &model.Image{
				URL:         "front-url",
				Checksum:    "front-checksum",
				ContentType: "image/jpeg",
			},
			BackIC: &model.Image{
				URL:         "back-url",
				Checksum:    "back-checksum",
				ContentType: "image/jpeg",
			},
			SelfieIC: &model.Image{
				DataBuffer: []byte("selfie-data"),
			},
		},
	}

	customerTransaction, _ := structpb.NewStruct(map[string]any{})

	expectedReq := &connector.SubmitOnboardingV3Request{
		ZaloapyRequestId:            "installment:dev-123",
		ZalopayId:                   456,
		FullName:                    "John Doe",
		PhoneNumber:                 "0123456789",
		IdNumber:                    "123456789",
		IdNumberIssueDate:           "2020-01-01",
		IdNumberIssuePlace:          "HCM",
		IdNumberExpirationDate:      "2030-01-01",
		DateOfBirth:                 "1990-01-01",
		Gender:                      "MALE",
		CurrentAddress:              "123 Street",
		FullCertifyAddress:          "123 Street",
		SourceOfFund:                "SALARY",
		Occupation:                  "ENGINEER",
		JobTitle:                    "DEVELOPER",
		CurrentLivingCity:           "HCM",
		OpeningCasaPurpose:          "SAVING",
		NfcCheckingRequestSignature: "signature",
		SelfieImage:                 base64.StdEncoding.EncodeToString([]byte("selfie-data")),
		CustomerTransaction:         customerTransaction,
		Documents: []*connector.Documents{
			{
				Url:         "front-url",
				Type:        connector.DocumentType_CERT_FRONT_PIC,
				Checksum:    "front-checksum",
				ContentType: "image/jpeg",
			},
			{
				Url:         "back-url",
				Type:        connector.DocumentType_CERT_BACK_PIC,
				Checksum:    "back-checksum",
				ContentType: "image/jpeg",
			},
		},
		Overdraft: &connector.Overdraft{
			Type:             overdraftType,
			Education:        "UNIVERSITY",
			LoanPurpose:      "PERSONAL",
			RequestLimit:     ********,
			MonthlyIncome:    ********,
			EmploymentStatus: "FULL_TIME",
		},
		LocationData: &connector.LocationData{
			RequestLat: 10.123,
			RequestLng: 106.456,
			RequestIp:  "127.0.0.1",
		},
		DeviceInfo: &connector.DeviceInfo{
			DeviceId: "device123",
		},
		IsResubmitted:     false,
		OldIdNumber:       "",
		NfcCheckingSource: connector.NFCSource_NFC_ZALOPAY,
	}

	s.mockConnector.EXPECT().
		SubmitCASAOnboardingV3(ctx, expectedReq).
		Return(&connector.SubmitOnboardingV3Response{
			BankRequestId: "bank-req-123",
		}, nil)

	// Act
	result, err := s.service.SubmitCasaOnboarding(ctx, onboarding, false)

	// Assert
	s.NoError(err)
	s.Equal("bank-req-123", result)
}

func (s *CimbClientTestSuite) TestSubmitCasaOnboarding_NilOnboarding() {
	// Act
	result, err := s.service.SubmitCasaOnboarding(context.Background(), nil, false)

	// Assert
	s.Error(err)
	s.Empty(result)
	s.Contains(err.Error(), "onboarding is nil")
}

func (s *CimbClientTestSuite) TestSubmitCasaOnboarding_InvalidUserInfo() {
	// Arrange
	onboarding := &model.Onboarding{
		UserInfo: nil, // Not a cimb.UserOnboardingInfo
	}

	// Act
	result, err := s.service.SubmitCasaOnboarding(context.Background(), onboarding, false)

	// Assert
	s.Error(err)
	s.Empty(result)
	s.Contains(err.Error(), "convert to user onboarding info fail")
}

func (s *CimbClientTestSuite) TestSubmitCasaOnboarding_ConnectorError() {
	// Arrange
	ctx := context.Background()
	ctx = utils.AttachClientSessionToCtx(ctx, &model.ClientSession{})
	onboarding := &model.Onboarding{
		ID:        123,
		ZalopayID: 456,
		UserInfo: &cimb.UserOnboardingInfo{
			UserProfile: model.UserProfile{
				FullName: "John Doe",
			},
			UserOnboardingExtra: cimb.UserOnboardingExtra{},
			UserKycNfc: model.UserKycNfc{
				NfcDataRaw: model.KycNfcDataRaw{},
			},
		},
		IcImages: model.ICImage{
			FrontIC: &model.Image{
				URL:         "front-url",
				Checksum:    "front-checksum",
				ContentType: "image/jpeg",
			},
			BackIC: &model.Image{
				URL:         "back-url",
				Checksum:    "back-checksum",
				ContentType: "image/jpeg",
			},
			SelfieIC: &model.Image{
				DataBuffer: []byte("selfie-data"),
			},
		},
	}

	s.mockConnector.EXPECT().
		SubmitCASAOnboardingV3(ctx, gomock.Any()).
		Return(nil, errors.New("connector error"))

	// Act
	result, err := s.service.SubmitCasaOnboarding(ctx, onboarding, false)

	// Assert
	s.Error(err)
	s.Empty(result)
	s.Contains(err.Error(), "Submit casa onboarding to cimb fail")
}

func (s *CimbClientTestSuite) TestMapNfcCheckingSource() {
	// Test cases
	testCases := []struct {
		name     string
		source   string
		expected connector.NFCSource
	}{
		{
			name:     "jth source",
			source:   "jth",
			expected: connector.NFCSource_NFC_ZALOPAY,
		},
		{
			name:     "mb source",
			source:   "mb",
			expected: connector.NFCSource_NFC_ZALOPAY,
		},
		{
			name:     "vneid source",
			source:   "vneid",
			expected: connector.NFCSource_VNEID,
		},
		{
			name:     "unknown source",
			source:   "unknown",
			expected: connector.NFCSource_NFC_ZALOPAY, // Default case
		},
		{
			name:     "empty source",
			source:   "",
			expected: connector.NFCSource_NFC_ZALOPAY, // Default case
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Act
			result := mapNfcCheckingSource(tc.source)

			// Assert
			s.Equal(tc.expected, result)
		})
	}
}
