package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

func (s service) RequestOTP(ctx context.Context, req *dto.RequestOTPParams) (*dto.RequestOTPResult, error) {
	cliSession := utils.ClientSessionFromCtx(ctx)
	conOtpType := connector.OTPType(req.Type)
	zalopayID := req.ZalopayID
	requestID, _ := s.generateRequestID(req.OnboardingID)
	partnerRequestID := req.PartnerRequestID

	if partnerRequestID == "" {
		return nil, errors.Errorf("RequestOTP from cimb fail: partnerRequestID is empty, otpType: %v", req.Type)
	}

	resp, err := s.connectorClient.RequestOTP(ctx, &connector.ReqOTPRequest{
		Type:             conOtpType,
		ZalopayId:        zalopayID,
		BankRequestId:    partnerRequestID,
		ZalopayRequestId: requestID,
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
	})
	if err != nil {
		return nil, errors.Errorf("RequestContractOTP from cimb fail: %v", err)
	}

	return &dto.RequestOTPResult{
		WaitTime:     resp.GetWaitTime(),
		ResendTime:   resp.GetResendTime(),
		Status:       resp.GetStatus(),
		ErrorMessage: resp.GetErrorMessage(),
		PhoneNumber:  resp.GetPhoneNumber(),
		OtpRequestId: resp.GetOtpRequestId(),
		IsSendSms:    resp.GetIsSendSms(),
	}, nil
}

func (s service) VerifyOTP(ctx context.Context, req *dto.VerifyOTPParams) (bool, error) {
	otpType := connector.OTPType(req.Type)
	cliSession := utils.ClientSessionFromCtx(ctx)
	requestID, _ := s.generateRequestID(req.OnboardingID)

	resp, err := s.connectorClient.VerifyOTP(ctx, &connector.VerifyOTPRequest{
		Type:             otpType,
		ZalopayId:        req.ZalopayID,
		BankRequestId:    req.PartnerRequestID,
		BankOtpRequestId: req.PartnerOTPRequestId,
		BankOtpCode:      req.PartnerOTPCode,
		ZalopayRequestId: requestID,
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
	})
	if err != nil {
		return false, errors.Errorf("VerifyContractOTP from cimb fail: %v", err)
	}
	return resp.GetStatus(), nil
}
