package cimb_service

import (
	"context"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (s *CimbClientTestSuite) TestMarkContractComplete_Success() {
	// Arrange
	zalopayID := int64(123)
	faceReqID := "face_123"
	partnerReqID := "partner_123"
	partnerTransID := "trans_123"

	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	s.mockConnector.EXPECT().
		ConfirmContractCompletion(ctx, &connector.ConfirmContractCompletionRequest{
			ZalopayId:        zalopayID,
			BankRequestId:    partnerReqID,
			BankOtpRequestId: partnerTransID,
			AuthenticationInfo: &connector.AuthenticationInfo{
				Id:   faceReqID,
				Type: connector.AuthenticationInfo_PARTNER_PIN_PASS,
			},
			LocationData: &connector.LocationData{
				RequestLat: 10.123,
				RequestLng: 106.456,
				RequestIp:  "127.0.0.1",
			},
		}).
		Return(&connector.ConfirmContractCompletionResponse{
			IsSuccess: true,
		}, nil)

	// Act
	signed, err := s.service.MarkContractComplete(ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)

	// Assert
	s.NoError(err)
	s.True(signed)
}

func (s *CimbClientTestSuite) TestMarkContractComplete_AlreadySigned() {
	// Arrange
	zalopayID := int64(123)
	faceReqID := "face_123"
	partnerReqID := "partner_123"
	partnerTransID := "trans_123"

	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	errResp := status.New(codes.AlreadyExists, "contract has been signed")
	errResp, _ = errResp.WithDetails(&common.ErrorInfo{
		SubCode: common.SubCode_CONTRACT_HAS_BEEN_SIGNED,
	})
	s.mockConnector.EXPECT().
		ConfirmContractCompletion(ctx, gomock.Any()).
		Return(nil, errResp.Err())

	// Act
	signed, err := s.service.MarkContractComplete(ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)

	// Assert
	s.NoError(err)
	s.True(signed)
}

func (s *CimbClientTestSuite) TestMarkContractComplete_ConnectorError() {
	// Arrange
	zalopayID := int64(123)
	faceReqID := "face_123"
	partnerReqID := "partner_123"
	partnerTransID := "trans_123"

	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	errResp := status.New(codes.Internal, "internal error")
	errResp, _ = errResp.WithDetails(&common.ErrorInfo{
		SubCode: common.SubCode_INTERNAL,
	})
	s.mockConnector.EXPECT().
		ConfirmContractCompletion(ctx, gomock.Any()).
		Return(nil, errResp.Err())

	// Act
	signed, err := s.service.MarkContractComplete(ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)

	// Assert
	s.Error(err)
	s.False(signed)
}

func (s *CimbClientTestSuite) TestMarkContractComplete_NotSuccess() {
	// Arrange
	zalopayID := int64(123)
	faceReqID := "face_123"
	partnerReqID := "partner_123"
	partnerTransID := "trans_123"

	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	s.mockConnector.EXPECT().
		ConfirmContractCompletion(ctx, gomock.Any()).
		Return(&connector.ConfirmContractCompletionResponse{
			IsSuccess: false,
		}, nil)

	// Act
	signed, err := s.service.MarkContractComplete(ctx, zalopayID, faceReqID, partnerReqID, partnerTransID)

	// Assert
	s.Error(err)
	s.False(signed)
}
