package cimb_service

import (
	"context"
	"encoding/base64"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

func mapNfcCheckingSource(src string) connector.NFCSource {
	switch src {
	case "vneid":
		return connector.NFCSource_VNEID
	default:
		return connector.NFCSource_NFC_ZALOPAY
	}
}

func (s service) SubmitCasaOnboarding(ctx context.Context, onboarding *model.Onboarding, isResubmitted bool) (string, error) {
	logger := s.logger.WithContext(ctx)
	cliSession := utils.ClientSessionFromCtx(ctx)

	if onboarding == nil {
		err := errors.New("onboarding is nil")
		logger.Error(err)
		return "", err
	}

	userInfo, ok := cimb.AsUserOnboardingInfo(onboarding.UserInfo)
	if !ok {
		err := errors.New("convert to user onboarding info fail")
		logger.Error(err)
		return "", err
	}

	requestID, err := s.generateRequestID(onboarding.ID)
	if err != nil {
		logger.Errorf("make request id when submit casa fail, %v", err)
		return "", errors.Errorf("make request id when submit casa fail: %v", err)
	}

	customerTransaction, err := structpb.NewStruct(userInfo.UserUnderwritingData)
	if err != nil {
		logger.Errorf("convert customer transaction to structpb fail: %v", err)
		return "", errors.Errorf("convert customer transaction to structpb fail: %v", err)
	}

	nfcProfile := userInfo.NfcDataDG13
	userGender := zutils.GenderConvertToString(nfcProfile.Gender)
	idIssueDate := getFirstNonEmpty(nfcProfile.IssueDate, userInfo.IdIssueDate)
	submitDocuments := convertICImagesToDocuments(onboarding.IcImages)
	userSelfieImage := base64.StdEncoding.EncodeToString(onboarding.IcImages.SelfieIC.DataBuffer)
	currentAddress := getFirstNonEmpty(userInfo.TempResidenceAddress, nfcProfile.PlaceResidence, userInfo.PermanentAddress)
	certifyAddress := getFirstNonEmpty(nfcProfile.PlaceResidence, userInfo.PermanentAddress, userInfo.TempResidenceAddress)

	logger.Infof("start submit casa onboarding, onboarding: %v", onboarding)

	request := &connector.SubmitOnboardingV3Request{
		ZaloapyRequestId:       requestID,
		ZalopayId:              onboarding.ZalopayID,
		FullName:               nfcProfile.FullName,
		PhoneNumber:            userInfo.PhoneNumber,
		IdNumber:               nfcProfile.EIDNumber,
		OldIdNumber:            nfcProfile.OldIDNumber,
		IdNumberIssueDate:      idIssueDate,
		IdNumberIssuePlace:     userInfo.IdIssuePlace,
		IdNumberExpirationDate: userInfo.IdExpireDate,
		DateOfBirth:            nfcProfile.Birthday,
		Gender:                 userGender,
		CurrentAddress:         currentAddress,
		FullCertifyAddress:     certifyAddress,
		SourceOfFund:           userInfo.SourceOfFund,
		Occupation:             userInfo.Occupation,
		JobTitle:               userInfo.JobTitle,
		CurrentLivingCity:      userInfo.City,
		OpeningCasaPurpose:     userInfo.FundPurpose,
		SelfieImage:            userSelfieImage,
		Documents:              submitDocuments,
		CustomerTransaction:    customerTransaction,
		Overdraft: &connector.Overdraft{
			Type:             overdraftType,
			Education:        userInfo.Education,
			LoanPurpose:      userInfo.LoanPurpose,
			RequestLimit:     userInfo.OverdraftLimit,
			EmploymentStatus: userInfo.EmploymentStatus,
			MonthlyIncome:    cast.ToInt64(userInfo.MonthlyIncome),
		},
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
		DeviceInfo: &connector.DeviceInfo{
			DeviceId: cliSession.GetDeviceID(),
		},
		NfcId:                       userInfo.NfcId,
		IsResubmitted:               isResubmitted,
		NfcCheckingSource:           mapNfcCheckingSource(userInfo.CollectSource),
		NfcCheckingRequestSignature: getFirstNonEmpty(userInfo.VendorSignature, userInfo.NfcSignature),
	}

	resp, err := s.connectorClient.SubmitCASAOnboardingV3(ctx, request)
	connErr, parseErr := parseConnectorError(err)
	if err != nil && parseErr != nil {
		logger.Errorf("Get selfie upload url from cimb fail: %v", err)
		return "", errors.Errorf("Submit casa onboarding to cimb fail: %v", err)
	}
	if connErr != nil {
		logger.Errorf("Submit casa onboarding to cimb fail: %v", connErr)
		return "", model.NewPartnerError(subCodeToPartnerCode(connErr.SubCode), err)
	}
	if resp == nil || resp.GetBankRequestId() == "" {
		logger.Errorf("Submit casa onboarding to cimb fail: empty response, response: %v", resp)
		return "", errors.New("Submit casa onboarding to cimb fail: empty response or bank request id")
	}

	logger.Infow("msg", "submit casa onboarding success", "response", resp)
	return resp.GetBankRequestId(), nil
}

func convertICImagesToDocuments(images model.ICImage) []*connector.Documents {
	if images.IsEmpty() {
		return nil
	}

	submitDocs := make([]*connector.Documents, 0, 2)
	switch {
	case images.FrontIC != nil:
		submitDocs = append(submitDocs, &connector.Documents{
			Url:         images.FrontIC.GetURL(),
			Type:        connector.DocumentType_CERT_FRONT_PIC,
			Checksum:    images.FrontIC.GetChecksum(),
			ContentType: images.FrontIC.GetContentType(),
		})
		fallthrough
	case images.BackIC != nil:
		submitDocs = append(submitDocs, &connector.Documents{
			Url:         images.BackIC.GetURL(),
			Type:        connector.DocumentType_CERT_BACK_PIC,
			Checksum:    images.BackIC.GetChecksum(),
			ContentType: images.BackIC.GetContentType(),
		})
	}
	return submitDocs
}
