package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

func (s *CimbClientTestSuite) TestGetContractData_Success() {
	// Arrange
	onboardingID := int64(123)
	zalopayID := int64(456)
	partnerReqID := "partner_123"
	contractSigned := true

	expectedReqID := "installment:dev-123"
	expectedSignedURL := "signed-url"
	expectedUnsignedURL := "unsigned-url"

	s.mockConnector.EXPECT().
		GetContract(context.Background(), &connector.GetContractRequest{
			IsSigned:         contractSigned,
			ZalopayId:        zalopayID,
			BankRequestId:    partnerReq<PERSON>,
			ZalopayRequestId: expectedReqID,
		}).
		Return(&connector.GetContractResponse{
			SignedContractUrl:   expectedSignedURL,
			UnsignedContractUrl: expectedUnsignedURL,
		}, nil)

	// Act
	result, err := s.service.GetContractData(context.Background(), &dto.GetContractParams{
		OnboardingID:     onboardingID,
		ZalopayID:        zalopayID,
		PartnerRequestID: partnerReqID,
		ContractSigned:   contractSigned,
	})

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(expectedSignedURL, result.SignedContract)
	s.Equal(expectedUnsignedURL, result.UnsignedContract)
}

func (s *CimbClientTestSuite) TestGetContractData_Error() {
	// Arrange
	onboardingID := int64(123)
	zalopayID := int64(456)
	partnerReqID := "partner_123"
	contractSigned := true

	s.mockConnector.EXPECT().
		GetContract(context.Background(), gomock.Any()).
		Return(nil, errors.New("some error"))

	// Act
	result, err := s.service.GetContractData(context.Background(), &dto.GetContractParams{
		OnboardingID:     onboardingID,
		ZalopayID:        zalopayID,
		PartnerRequestID: partnerReqID,
		ContractSigned:   contractSigned,
	})

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "Get contract data from cimb fail")
}
