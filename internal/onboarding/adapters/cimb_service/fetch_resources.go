package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

func (s service) FetchResources(ctx context.Context, resourceTypes []string) ([]model.Resource, error) {
	var list []model.Resource
	var rTypes []connector.ResourceType

	for _, r := range resourceTypes {
		if val, ok := connector.ResourceType_value[r]; ok {
			rTypes = append(rTypes, connector.ResourceType(val))
		}
	}

	if len(rTypes) == 0 {
		return []model.Resource{}, nil
	}

	frResp, err := s.connectorClient.FetchResources(ctx, &connector.FetchResourcesRequest{
		ResourceTypes: rTypes,
	})
	if err != nil {
		return nil, errors.Errorf("FetchResources from cimb fail: %v", err)
	}

	for _, cimbResource := range frResp.GetResources() {
		var data []model.ResourceData
		for _, cimbResourceData := range cimbResource.GetData() {
			data = append(data, model.ResourceData{
				Code:       cimbResourceData.GetCode(),
				Vietnamese: cimbResourceData.GetVietnamese(),
				English:    cimbResourceData.GetEnglish(),
			})
		}

		list = append(list, model.Resource{
			Type: cimbResource.GetType(),
			Data: data,
		})
	}

	return list, nil
}
