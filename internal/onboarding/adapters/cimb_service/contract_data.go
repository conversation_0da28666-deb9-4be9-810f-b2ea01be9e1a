package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

func (s service) GetContractData(
	ctx context.Context, req *dto.GetContractParams) (*model.ContractData, error) {
	zlpReqID, _ := s.generateRequestID(req.OnboardingID)

	contract, err := s.connectorClient.GetContract(ctx, &connector.GetContractRequest{
		IsSigned:         req.ContractSigned,
		ZalopayId:        req.ZalopayID,
		BankRequestId:    req.PartnerRequestID,
		ZalopayRequestId: zlpReqID,
	})
	if err != nil {
		return nil, errors.Errorf("Get contract data from cimb fail: %v", err)
	}
	return &model.ContractData{
		SignedContract:   contract.GetSignedContractUrl(),
		UnsignedContract: contract.GetUnsignedContractUrl(),
	}, nil
}
