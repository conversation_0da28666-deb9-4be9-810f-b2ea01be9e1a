package cimb_service

import (
	"bytes"
	"context"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/common"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

func (s service) RequestSelfieUploadUrl(ctx context.Context,
	req *dto.SelfieUploadUrlParams) (url string, tnxExp bool, err error) {
	logger := s.logger.WithContext(ctx)
	cliSession := utils.ClientSessionFromCtx(ctx)

	// Step 1: Transform to CIMB request
	selfieResp, err := s.connectorClient.GetSelfieUploadURL(ctx, &connector.GetSelfieUploadURLRequest{
		ZalopayId:        req.ZalopayID,
		BankRequestId:    req.PartnerReqID,
		BankOtpRequestId: req.PartnerTransID,
		ContentType:      req.ContentType,
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
	})

	connErr, parseErr := parseConnectorError(err)

	if err != nil && parseErr != nil {
		logger.Errorf("Get selfie upload url from cimb fail: %v", err)
		return "", false, errors.Errorf("Get selfie upload url from cimb fail: %v", err)
	}
	if connErr != nil && connErr.SubCode == common.SubCode_CONTRACT_TRANSACTION_EXPIRED {
		logger.Warn("Get selfie upload url from cimb fail: contract transaction expired")
		return "", true, nil
	}
	if connErr != nil {
		logger.Errorf("Get selfie upload url from cimb fail: %v", connErr)
		return "", false, errors.Errorf("Get selfie upload url from cimb fail: %v", connErr)
	}
	if selfieResp.GetUploadUrl() == "" {
		logger.Errorf("Get selfie upload url from cimb fail: empty url")
		return "", false, errors.New("Get selfie upload url from cimb fail: empty url")
	}

	return selfieResp.GetUploadUrl(), false, nil
}

func (s service) UploadSelfieImage(ctx context.Context, req *dto.SelfieUploadParams) (txnExp bool, err error) {
	logger := s.logger.WithContext(ctx)
	cliSession := utils.ClientSessionFromCtx(ctx)
	requestID, _ := s.generateRequestID(req.OnboardingID)

	resp, err := s.connectorClient.UploadSelfie(ctx, &connector.UploadSelfieRequest{
		Url:              req.ImageUrl,
		ZalopayId:        req.ZalopayID,
		BankRequestId:    req.PartnerRequestID,
		ZalopayRequestId: requestID,
		BankOtpRequestId: req.PartnerOTPRequestId,
		LocationData: &connector.LocationData{
			RequestLat: cliSession.GetLat(),
			RequestLng: cliSession.GetLng(),
			RequestIp:  cliSession.GetIP(),
		},
	})

	connErr, parseErr := parseConnectorError(err)

	if err != nil && parseErr != nil {
		logger.Errorf("Get selfie upload url from cimb fail: %v", err)
		return false, errors.Errorf("Get selfie upload url from cimb fail: %+v", err)
	}
	if connErr != nil && connErr.SubCode == common.SubCode_CONTRACT_TRANSACTION_EXPIRED {
		logger.Warn("Get selfie upload url from cimb fail: contract transaction expired")
		return true, nil
	}
	if connErr != nil {
		logger.Errorf("Get selfie upload url from cimb fail: %+v", connErr)
		return false, errors.Errorf("Get selfie upload url from cimb fail: %v", connErr)
	}
	if !resp.GetIsSuccess() {
		logger.Warnf("upload selfie image fail")
		return false, errors.New("upload selfie image fail")
	}

	return false, nil
}

func (s service) UploadSelfieImageByURL(ctx context.Context, uploadUrl string, imageData *model.Image) error {
	buffer := bytes.NewBuffer(imageData.DataBuffer)
	header := map[string]string{"Content-Type": imageData.ContentType}
	options := httpclient.WithHeaders(header)

	statusCode, rawResp, err := s.httpClient.RawPut(ctx, uploadUrl, buffer, "UploadSelfieImageByURL", options)
	if err != nil {
		return err
	}

	if httpclient.IsError(statusCode) {
		return fmt.Errorf("upload selfie image fail, status code: %d", statusCode)
	}

	if rawResp == nil {
		return fmt.Errorf("upload selfie image fail, response is nil")
	}
	return nil
}
