package cimb_service

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model/cimb"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

func (s service) CheckOnboardingPermission(ctx context.Context,
	params *dto.OnboardingPermissionParams) (*model.OnboardingPermission, error) {
	permResp, err := s.connectorClient.CheckOnboardingPermission(ctx, &connector.CheckPermissionRequest{
		Timestamp:   time.Now().UnixMilli(),
		IdNumber:    params.IDNumber,
		ZalopayId:   params.ZalopayID,
		PhoneNumber: params.PhoneNumber,
		ProductType: connector.ProductType_PRODUCT_TYPE_CASA,
		ProductCode: connector.ProductCode_CASA_ZALOPAY_INSTALLMENT,
	})

	if err != nil {
		return nil, errors.Errorf("CheckOnboardingPermission from cimb fail: %v", err)
	}
	if permResp == nil {
		return nil, errors.Errorf("CheckOnboardingPermission has empty response")
	}
	return &model.OnboardingPermission{
		Allowed:    permResp.GetCanOnboard(),
		RejectCode: permResp.GetRejectCode(),
		RejectDesc: permResp.GetRejectReason(),
		IsWarning:  permResp.GetRejectCode() == cimb.RejectCodeCustomerExisted.String(),
	}, nil
}
