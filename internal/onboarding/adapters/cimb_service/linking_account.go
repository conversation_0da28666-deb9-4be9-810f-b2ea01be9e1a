package cimb_service

import (
	"context"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
)

func (s service) RequestLinkingAccount(ctx context.Context, onboarding *model.Onboarding) (string, error) {
	logger := s.logger.WithContext(ctx)
	requestID, err := s.generateRequestID(onboarding.ID)
	linkRequestID := uuid.NewString()

	if err != nil {
		logger.Errorf("failed to generate request ID: %v", err)
		return "", errors.Wrapf(err, "RequestLinkingAccount failed to generate request ID")
	}

	resp, err := s.connectorClient.LinkAccount(ctx, &connector.LinkAccountRequest{
		ZalopayRequestId:     requestID,
		ZalopayLinkRequestId: linkRequestID,
		ZalopayId:            onboarding.ZalopayID,
		PhoneNumber:          onboarding.GetUserInfo().GetPhoneNumber(),
		IdNumber:             onboarding.GetUserInfo().GetIdNumber(),
		TransactionRemark:    linkAccountRemark,
		ProductType:          connector.ProductType_PRODUCT_TYPE_INSTALLMENT,
	})
	if err != nil {
		logger.Errorf("failed to link account to cimb: %v", err)
		return "", errors.Wrapf(err, "RequestLinkingAccount failed to link account")
	}
	if resp.GetBankLinkingRequestId() == "" {
		logger.Errorf("emtpy bank linking request ID")
		return "", errors.New("RequestLinkingAccount empty bank linking request ID")
	}
	return resp.GetBankLinkingRequestId(), nil
}
