package cimb_service

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
)

func (s *CimbClientTestSuite) TestFetchResources_Success() {
	// Arrange
	ctx := context.Background()
	resourceTypes := []string{model.ResourceTypeCity}

	expectedResources := []*connector.CIMBResource{
		{
			Type: connector.ResourceType_CITY.String(),
			Data: []*connector.CIMBResource_ResourceData{
				{
					Code:       "code1",
					Vietnamese: "vn1",
					English:    "en1",
				},
			},
		},
	}

	s.mockConnector.EXPECT().
		FetchResources(ctx, &connector.FetchResourcesRequest{
			ResourceTypes: []connector.ResourceType{connector.ResourceType_CITY},
		}).
		Return(&connector.FetchResourcesResponse{
			Resources: expectedResources,
		}, nil)

	// Act
	result, err := s.service.FetchResources(ctx, resourceTypes)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Len(result, 1)
	s.Equal(connector.ResourceType_CITY.String(), result[0].Type)
	s.Len(result[0].Data, 1)
	s.Equal("code1", result[0].Data[0].Code)
	s.Equal("vn1", result[0].Data[0].Vietnamese)
	s.Equal("en1", result[0].Data[0].English)
}

func (s *CimbClientTestSuite) TestFetchResources_EmptyTypes() {
	// Arrange
	ctx := context.Background()
	resourceTypes := []string{}

	// Act
	result, err := s.service.FetchResources(ctx, resourceTypes)

	// Assert
	s.NoError(err)
	s.Empty(result)
}

func (s *CimbClientTestSuite) TestFetchResources_Error() {
	// Arrange
	ctx := context.Background()
	resourceTypes := []string{model.ResourceTypeCity}

	s.mockConnector.EXPECT().
		FetchResources(ctx, &connector.FetchResourcesRequest{
			ResourceTypes: []connector.ResourceType{connector.ResourceType_CITY},
		}).
		Return(nil, errors.New("some error"))

	// Act
	result, err := s.service.FetchResources(ctx, resourceTypes)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "FetchResources from cimb fail")
}
