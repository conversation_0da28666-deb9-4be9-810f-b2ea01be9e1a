package cimb_service

import (
	"context"

	"gitlab.zalopay.vn/fin/partner/cimb-connector/specs/generated/connector"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

func (s *CimbClientTestSuite) TestRequestOTP_Success() {
	// Arrange
	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	req := &dto.RequestOTPParams{
		Type:             model.OTPContract,
		ZalopayID:        123,
		OnboardingID:     456,
		PartnerRequestID: "pr123",
	}

	expectedReqID := "installment:dev-456"

	s.mockConnector.EXPECT().
		RequestOTP(ctx, &connector.ReqOTPRequest{
			Type:             connector.OTPType_CONTRACT_SIGNING,
			ZalopayId:        123,
			BankRequestId:    "pr123",
			ZalopayRequestId: expectedReqID,
			LocationData: &connector.LocationData{
				RequestLat: 10.123,
				RequestLng: 106.456,
				RequestIp:  "127.0.0.1",
			},
		}).
		Return(&connector.ReqOTPResponse{
			WaitTime:     int64(30),
			ResendTime:   int64(60),
			Status:       true,
			ErrorMessage: "",
			PhoneNumber:  "**********",
			OtpRequestId: "otp123",
			IsSendSms:    true,
		}, nil)

	// Act
	result, err := s.service.RequestOTP(ctx, req)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(int64(30), result.WaitTime)
	s.Equal(int64(60), result.ResendTime)
	s.True(result.Status)
	s.Equal("**********", result.PhoneNumber)
	s.Equal("otp123", result.OtpRequestId)
	s.True(result.IsSendSms)
}

func (s *CimbClientTestSuite) TestRequestOTP_EmptyPartnerRequestID() {
	// Arrange
	ctx := context.Background()
	req := &dto.RequestOTPParams{
		Type:             model.OTPContract,
		ZalopayID:        123,
		OnboardingID:     456,
		PartnerRequestID: "", // Empty partner request ID
	}

	// Act
	result, err := s.service.RequestOTP(ctx, req)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "partnerRequestID is empty")
}

func (s *CimbClientTestSuite) TestVerifyOTP_Success() {
	// Arrange
	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	req := &dto.VerifyOTPParams{
		Type:                model.OTPContract,
		ZalopayID:           123,
		OnboardingID:        456,
		PartnerRequestID:    "pr123",
		PartnerOTPRequestId: "otp123",
		PartnerOTPCode:      "123456",
	}

	expectedReqID := "installment:dev-456"

	s.mockConnector.EXPECT().
		VerifyOTP(ctx, &connector.VerifyOTPRequest{
			Type:             connector.OTPType_CONTRACT_SIGNING,
			ZalopayId:        123,
			BankRequestId:    "pr123",
			BankOtpRequestId: "otp123",
			ZalopayRequestId: expectedReqID,
			BankOtpCode:      "123456",
			LocationData: &connector.LocationData{
				RequestLat: 10.123,
				RequestLng: 106.456,
				RequestIp:  "127.0.0.1",
			},
		}).
		Return(&connector.VerifyOTPResponse{
			Status: true,
		}, nil)

	// Act
	result, err := s.service.VerifyOTP(ctx, req)

	// Assert
	s.NoError(err)
	s.True(result)
}

func (s *CimbClientTestSuite) TestVerifyOTP_InvalidOTP() {
	// Arrange
	ctx := utils.AttachClientSessionToCtx(context.Background(), &model.ClientSession{
		RequestIP: "127.0.0.1",
		Latitude:  10.123,
		Longitude: 106.456,
	})

	req := &dto.VerifyOTPParams{
		Type:                model.OTPContract,
		ZalopayID:           123,
		OnboardingID:        456,
		PartnerRequestID:    "pr123",
		PartnerOTPRequestId: "otp123",
		PartnerOTPCode:      "000000", // Invalid OTP
	}

	s.mockConnector.EXPECT().
		VerifyOTP(ctx, gomock.Any()).
		Return(&connector.VerifyOTPResponse{
			Status: false,
		}, nil)

	// Act
	result, err := s.service.VerifyOTP(ctx, req)

	// Assert
	s.NoError(err)
	s.False(result)
}
