package user_challenge

import (
	"context"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/adapters/user_challenge/mocks"
)

type UserChallengeTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	mockGrpcClient *mocks.MockUserFaceAuthenServiceClient
	service        *userChallenge
	ctx            context.Context
}

func TestUserChallengeSuite(t *testing.T) {
	suite.Run(t, new(UserChallengeTestSuite))
}

func (s *UserChallengeTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockGrpcClient = mocks.NewMockUserFaceAuthenServiceClient(s.ctrl)
	s.ctx = context.Background()

	conf := &config.Onboarding{
		UserChallenge: &config.Onboarding_UserChallenge{
			FaceSourceId: 123,
		},
	}

	s.service = NewClient(s.mockGrpcClient, conf, log.DefaultLogger).(*userChallenge)
}

func (s *UserChallengeTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *UserChallengeTestSuite) TestGetFaceChallengeStatus_Success() {
	// Arrange
	zalopayID := int64(12345)
	challengeID := "test-challenge-id"

	s.mockGrpcClient.EXPECT().
		GetStatus(s.ctx, &userfaceauthenv1.GetStatusRequest{
			ZalopayId: "12345",
			RequestId: challengeID,
			SourceId:  123,
		}).
		Return(&userfaceauthenv1.GetStatusResponse{
			Status: FaceChallengeStatusApproved,
		}, nil)

	// Act
	result, err := s.service.GetFaceChallengeStatus(s.ctx, zalopayID, challengeID)

	// Assert
	assert.NoError(s.T(), err)
	assert.True(s.T(), result.Approved)
	assert.Equal(s.T(), challengeID, result.ChallengeID)
}

func (s *UserChallengeTestSuite) TestGetFaceChallengeStatus_Rejected() {
	zalopayID := int64(12345)
	challengeID := "test-challenge-id"

	s.mockGrpcClient.EXPECT().
		GetStatus(s.ctx, gomock.Any()).
		Return(&userfaceauthenv1.GetStatusResponse{
			Status: FaceChallengeStatusRejected,
		}, nil)

	result, err := s.service.GetFaceChallengeStatus(s.ctx, zalopayID, challengeID)

	assert.NoError(s.T(), err)
	assert.False(s.T(), result.Approved)
	assert.Equal(s.T(), challengeID, result.ChallengeID)
}

func (s *UserChallengeTestSuite) TestGetFaceChallengeStatus_Error() {
	zalopayID := int64(12345)
	challengeID := "test-challenge-id"

	s.mockGrpcClient.EXPECT().
		GetStatus(s.ctx, gomock.Any()).
		Return(nil, assert.AnError)

	result, err := s.service.GetFaceChallengeStatus(s.ctx, zalopayID, challengeID)

	assert.Error(s.T(), err)
	assert.Nil(s.T(), result)
}

func (s *UserChallengeTestSuite) TestGetFaceChallengeData_Success() {
	// Arrange
	zalopayID := int64(12345)
	challengeID := "test-challenge-id"
	now := time.Now()

	s.mockGrpcClient.EXPECT().
		GetData(s.ctx, &userfaceauthenv1.GetDataRequest{
			ZalopayId: "12345",
			RequestId: challengeID,
		}).
		Return(&userfaceauthenv1.GetDataResponse{
			ImageUrl:      "http://example.com/image.jpg",
			Score:         0.95,
			ValidatedAtMs: now.UnixMilli(),
		}, nil)

	// Act
	result, err := s.service.GetFaceChallengeData(s.ctx, zalopayID, challengeID)

	// Assert
	assert.NoError(s.T(), err)
	assert.True(s.T(), result.Approved)
	assert.Equal(s.T(), "http://example.com/image.jpg", result.ImageUrl)
	assert.Equal(s.T(), 0.95, result.MatchScore)
	assert.Equal(s.T(), now.UnixMilli(), result.ValidateAt.UnixMilli())
	assert.Equal(s.T(), challengeID, result.ChallengeID)
}

func (s *UserChallengeTestSuite) TestGetFaceChallengeData_Error() {
	zalopayID := int64(12345)
	challengeID := "test-challenge-id"

	s.mockGrpcClient.EXPECT().
		GetData(s.ctx, gomock.Any()).
		Return(nil, assert.AnError)

	result, err := s.service.GetFaceChallengeData(s.ctx, zalopayID, challengeID)

	assert.Error(s.T(), err)
	assert.Nil(s.T(), result)
}
