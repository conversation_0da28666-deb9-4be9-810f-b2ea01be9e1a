package user_challenge

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type userChallenge struct {
	logger       *log.Helper
	faceSourceID int32
	grpcClient   userfaceauthenv1.UserFaceAuthenServiceClient
}

const (
	// FaceChallengeStatusApproved ...
	FaceChallengeStatusApproved = 1
	// FaceChallengeStatusRejected ...
	FaceChallengeStatusRejected = 2
)

func NewClient(grpcClient userfaceauthenv1.UserFaceAuthenServiceClient,
	conf *config.Onboarding, kLogger log.Logger) _interface.UserChallengeService {
	logger := log.NewHelper(log.With(kLogger, "adapters", "user-challenge"))
	return &userChallenge{
		logger:       logger,
		grpcClient:   grpcClient,
		faceSourceID: conf.GetUserChallenge().GetFaceSourceId(),
	}
}

func (u *userChallenge) GetFaceChallengeStatus(ctx context.Context,
	zalopayID int64, faceChallengeID string) (*model.UserFaceChallenge, error) {
	params := &userfaceauthenv1.GetStatusRequest{
		ZalopayId: cast.ToString(zalopayID),
		RequestId: faceChallengeID,
		SourceId:  u.faceSourceID,
	}

	resp, err := u.grpcClient.GetStatus(ctx, params)
	if err != nil {
		u.logger.WithContext(ctx).Errorf("get face challenge status failed, error=%v", err)
		return nil, errors.Errorf("get face challenge status failed, error=%v", err)
	}
	isApproved := resp.Status == FaceChallengeStatusApproved
	return &model.UserFaceChallenge{
		Approved:    isApproved,
		ChallengeID: faceChallengeID,
	}, nil
}

func (u *userChallenge) GetFaceChallengeData(ctx context.Context, zalopayID int64,
	faceChallengeID string) (*model.UserFaceChallenge, error) {
	params := &userfaceauthenv1.GetDataRequest{
		ZalopayId: cast.ToString(zalopayID),
		RequestId: faceChallengeID,
	}

	resp, err := u.grpcClient.GetData(ctx, params)
	if err != nil {
		u.logger.WithContext(ctx).Errorf("get face challenge data failed, error=%v", err)
		return nil, errors.Errorf("get face challenge data failed, error=%v", err)
	}
	return &model.UserFaceChallenge{
		Approved:    true,
		ImageUrl:    resp.ImageUrl,
		MatchScore:  resp.Score,
		ValidateAt:  time.UnixMilli(resp.ValidatedAtMs),
		ChallengeID: faceChallengeID,
	}, nil
}
