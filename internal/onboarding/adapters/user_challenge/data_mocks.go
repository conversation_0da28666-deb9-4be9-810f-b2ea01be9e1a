package user_challenge

import "gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1"

const mockReqID = "7sjFwiPnJrSFEqWiDaZNTmmbnX9PhD3KvK6Y6F4x3VJ7QWFm4PZcZyUn45bq2Lu9fXR41qSPF7UcrU66YSwf5rW5osbFCdUqKgSTRM4oTGZvSiNLdojFhTbdUHaLEXziXwvrhRBNF8uL8sFXVaUWzazrhh63UwXk16peyboG651QwFAp59Jv3Ga26rQ2ce86jY"
const mockUserID = "240614000000164"

var faceAuthMock = &userfaceauthenv1.GetDataRequest{
	ZalopayId: mockUserID,
	RequestId: mockReqID,
}

var faceChallengeStatusMock = &userfaceauthenv1.GetStatusRequest{
	ZalopayId: mockUserID,
	RequestId: mockReqID,
	SourceId:  4,
}
