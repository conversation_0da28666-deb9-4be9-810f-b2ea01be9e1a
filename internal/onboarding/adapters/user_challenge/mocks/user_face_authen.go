// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1 (interfaces: UserFaceAuthenServiceClient)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/user_face_authen.go -package=mocks gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1 UserFaceAuthenServiceClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	userfaceauthenv1 "gitlab.zalopay.vn/grpc-specifications/user-face-authen/pkg/user/userfaceauthenv1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUserFaceAuthenServiceClient is a mock of UserFaceAuthenServiceClient interface.
type MockUserFaceAuthenServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserFaceAuthenServiceClientMockRecorder
	isgomock struct{}
}

// MockUserFaceAuthenServiceClientMockRecorder is the mock recorder for MockUserFaceAuthenServiceClient.
type MockUserFaceAuthenServiceClientMockRecorder struct {
	mock *MockUserFaceAuthenServiceClient
}

// NewMockUserFaceAuthenServiceClient creates a new mock instance.
func NewMockUserFaceAuthenServiceClient(ctrl *gomock.Controller) *MockUserFaceAuthenServiceClient {
	mock := &MockUserFaceAuthenServiceClient{ctrl: ctrl}
	mock.recorder = &MockUserFaceAuthenServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserFaceAuthenServiceClient) EXPECT() *MockUserFaceAuthenServiceClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockUserFaceAuthenServiceClient) GetData(ctx context.Context, in *userfaceauthenv1.GetDataRequest, opts ...grpc.CallOption) (*userfaceauthenv1.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*userfaceauthenv1.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockUserFaceAuthenServiceClientMockRecorder) GetData(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockUserFaceAuthenServiceClient)(nil).GetData), varargs...)
}

// GetStatus mocks base method.
func (m *MockUserFaceAuthenServiceClient) GetStatus(ctx context.Context, in *userfaceauthenv1.GetStatusRequest, opts ...grpc.CallOption) (*userfaceauthenv1.GetStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStatus", varargs...)
	ret0, _ := ret[0].(*userfaceauthenv1.GetStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatus indicates an expected call of GetStatus.
func (mr *MockUserFaceAuthenServiceClientMockRecorder) GetStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatus", reflect.TypeOf((*MockUserFaceAuthenServiceClient)(nil).GetStatus), varargs...)
}
