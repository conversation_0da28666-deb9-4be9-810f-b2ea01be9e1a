package account_service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/account_service/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type client struct {
	logger     *log.Helper
	grpcClient v1.AccountClient
}

func NewClient(grpcClient v1.AccountClient, kLogger log.Logger) _interface.AccountService {
	logger := log.NewHelper(log.With(kLogger, "adapters", "account_service"))
	return &client{
		logger:     logger,
		grpcClient: grpcClient,
	}
}

func (c client) CreateAccount(ctx context.Context, params *dto.CreateAccountParams) (accountID int64, err error) {
	req := &v1.CreateAccountRequest{
		ZalopayId:    params.ZalopayID,
		OnboardingId: params.OnboardingID,
		PartnerCode:  params.PartnerCode.String(),
	}
	resp, err := c.grpcClient.CreateAccount(ctx, req)
	if err != nil {
		c.logger.Errorf("failed to create account: %v", err)
		return -1, err
	}
	return resp.AccountId, nil
}
