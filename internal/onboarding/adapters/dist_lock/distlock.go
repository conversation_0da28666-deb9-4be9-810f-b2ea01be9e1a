package dist_lock

import (
	"context"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/redis"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type distLock struct {
	cache  redis.CacheNoCaller
	logger *log.Helper
	holder map[string]redis.Lock
	mutex  sync.RWMutex
}

func NewDistLock(redisCache redis.CacheNoCaller, logger log.Logger) _interface.DistributedLock {
	holder := make(map[string]redis.Lock)
	logging := log.With(logger, "adapters", "dist-lock")
	return &distLock{
		cache:  redisCache,
		holder: holder,
		logger: log.NewHelper(logging),
		mutex:  sync.RWMutex{},
	}
}

func (d distLock) Acquire(ctx context.Context, resource string, ttl time.Duration) error {
	locker := d.cache.CreateRedisLock(resource, ttl)
	if err := locker.Lock(ctx); err != nil {
		d.logger.WithContext(ctx).Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
		return errors.Errorf("acquire lock failed, resource=%s, error=%v", resource, err)
	}

	d.mutex.Lock()
	d.holder[resource] = locker
	d.mutex.Unlock()

	return nil
}

func (d distLock) Release(ctx context.Context, resource string) error {
	d.mutex.RLock()
	locker := d.holder[resource]
	d.mutex.RUnlock()

	if locker == nil {
		return nil
	}
	newCtx := context.WithoutCancel(ctx)
	if err := locker.UnLock(newCtx); err != nil {
		d.logger.WithContext(newCtx).Errorf("release lock failed, resource=%s, error=%v", resource, err)
		return errors.Errorf("release lock failed, resource=%s, error=%v", resource, err)
	}

	// Release lock successfully
	d.mutex.Lock()
	delete(d.holder, resource)
	d.mutex.Unlock()

	return nil
}
