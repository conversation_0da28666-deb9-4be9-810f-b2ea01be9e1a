package minio_storage

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"
	"gitlab.zalopay.vn/fin/platform/common/mocks/s3mocks"
	"gitlab.zalopay.vn/fin/platform/common/s3storage"
	ubmock "go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	httpclientmocks "gitlab.zalopay.vn/fin/installment/installment-service/pkg/mocks/httpclient"
)

type MinioUploadTestSuite struct {
	suite.Suite
	mockS3Client   *s3mocks.MockClient
	mockHttpClient *httpclientmocks.MockHttpClient
	service        *MinioUpload
	ctx            context.Context
}

func TestMinioUploadSuite(t *testing.T) {
	suite.Run(t, new(MinioUploadTestSuite))
}

func (s *MinioUploadTestSuite) SetupTest() {
	s.ctx = context.Background()
	ubCtrl := ubmock.NewController(s.T())
	goCtrl := gomock.NewController(s.T())
	s.mockS3Client = s3mocks.NewMockClient(goCtrl)
	s.mockHttpClient = httpclientmocks.NewMockHttpClient(ubCtrl)
	s.service = NewMinioUpload(s.mockS3Client, base.KycImageHttpClient(s.mockHttpClient)).(*MinioUpload)
}

func (s *MinioUploadTestSuite) TestGetImageObj_Success() {
	// Arrange
	zalopayID := int64(12345)
	imagePath := "test/path"
	mockData := &s3storage.FileData{
		FullPath:    imagePath,
		Data:        []byte("test data"),
		ContentType: "image/jpeg",
	}

	s.mockS3Client.EXPECT().
		GetObject(s.ctx, "12345", imagePath).
		Return(mockData, nil)

	// Act
	result, err := s.service.GetImageObj(s.ctx, zalopayID, imagePath)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(mockData.FullPath, result.Path)
	s.Equal(mockData.ContentType, result.ContentType)
	s.Equal(mockData.Data, result.DataBuffer)
}

func (s *MinioUploadTestSuite) TestGetImageObj_Error() {
	// Arrange
	zalopayID := int64(12345)
	imagePath := "test/path"

	s.mockS3Client.EXPECT().
		GetObject(s.ctx, "12345", imagePath).
		Return(nil, errors.New("s3 error"))

	// Act
	result, err := s.service.GetImageObj(s.ctx, zalopayID, imagePath)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *MinioUploadTestSuite) TestGetImageUrl_Success() {
	// Arrange
	zalopayID := int64(12345)
	imagePath := "test/path"
	expectedURL := "https://test-url.com"

	s.mockS3Client.EXPECT().
		PresignedURL(s.ctx, "12345", imagePath).
		Return(expectedURL, nil)

	// Act
	result, err := s.service.GetImageUrl(s.ctx, zalopayID, imagePath)

	// Assert
	s.NoError(err)
	s.Equal(expectedURL, result)
}

func (s *MinioUploadTestSuite) TestGetImageUrl_Error() {
	// Arrange
	zalopayID := int64(12345)
	imagePath := "test/path"

	s.mockS3Client.EXPECT().
		PresignedURL(s.ctx, "12345", imagePath).
		Return("", errors.New("presign error"))

	// Act
	result, err := s.service.GetImageUrl(s.ctx, zalopayID, imagePath)

	// Assert
	s.Error(err)
	s.Empty(result)
}

func (s *MinioUploadTestSuite) TestGetFaceImageUrl_Success() {
	// Arrange
	zalopayID := int64(12345)
	imagePath := "test/path"
	expectedURL := "https://test-url.com"

	s.mockS3Client.EXPECT().
		PresignedURL(s.ctx, "12345", imagePath).
		Return(expectedURL, nil)

	// Act
	result, err := s.service.GetFaceImageUrl(s.ctx, zalopayID, imagePath)

	// Assert
	s.NoError(err)
	s.Equal(expectedURL, result)
}

func (s *MinioUploadTestSuite) TestUploadSignatureImage_Success() {
	// Arrange
	params := &dto.WetSignUploadParams{
		ZalopayID:   12345,
		ImagePath:   "test/path",
		ImageData:   []byte("test data"),
		ContentType: "image/jpeg",
	}

	s.mockS3Client.EXPECT().
		PutObject(s.ctx, "12345", gomock.Any()).
		Return(nil)

	// Act
	err := s.service.UploadSignatureImage(s.ctx, params)

	// Assert
	s.NoError(err)
}

func (s *MinioUploadTestSuite) TestUploadSignatureImage_Error() {
	// Arrange
	params := &dto.WetSignUploadParams{
		ZalopayID:   12345,
		ImagePath:   "test/path",
		ImageData:   []byte("test data"),
		ContentType: "image/jpeg",
	}

	s.mockS3Client.EXPECT().
		PutObject(s.ctx, "12345", gomock.Any()).
		Return(errors.New("upload error"))

	// Act
	err := s.service.UploadSignatureImage(s.ctx, params)

	// Assert
	s.Error(err)
}

func (s *MinioUploadTestSuite) TestFetchAndUploadFaceImage_Success() {
	// Arrange
	params := &dto.FaceImgStoreParams{
		ZalopayID: 12345,
		ImageUri:  "https://source-url.com",
		ImagePath: "test/path",
	}

	mockResponse := &httpclient.RawResponse{
		Body:        []byte("test data"),
		ContentType: "image/jpeg",
	}

	s.mockHttpClient.EXPECT().
		RawGet(s.ctx, params.ImageUri, "FetchAndUploadFaceImage").
		Return(200, mockResponse, nil)

	s.mockS3Client.EXPECT().
		PutObject(s.ctx, "12345", gomock.Any()).
		Return(nil)

	// Act
	result, err := s.service.FetchAndUploadFaceImage(s.ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(params.ImagePath, result.Path)
	s.Equal(mockResponse.ContentType, result.ContentType)
}

func (s *MinioUploadTestSuite) TestFetchAndUploadKYCImages_Success() {
	// Arrange
	params := &dto.UserKycImageStoreParams{
		ZalopayID:  12345,
		ImageUri:   "https://source-url.com",
		ImagePath:  "test/path",
		NeedBuffer: true,
	}

	mockResponse := &httpclient.RawResponse{
		Body:        []byte("test data"),
		ContentType: "image/jpeg",
	}

	fileData := &s3storage.FileData{
		FullPath:    params.ImagePath,
		Data:        mockResponse.Body,
		ContentType: mockResponse.ContentType,
	}

	expectedURL := "https://test-url.com"

	s.mockHttpClient.EXPECT().
		RawGet(s.ctx, params.ImageUri, "FetchAndUploadKYCImages").
		Return(200, mockResponse, nil)

	s.mockS3Client.EXPECT().
		PutObject(s.ctx, "12345", fileData).
		Return(nil)

	s.mockS3Client.EXPECT().
		PresignedURL(s.ctx, "12345", params.ImagePath).
		Return(expectedURL, nil)

	// Act
	result, err := s.service.FetchAndUploadKYCImages(s.ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(params.ImagePath, result.Path)
	s.Equal(mockResponse.ContentType, result.ContentType)
	s.Equal(expectedURL, result.URL)
	s.Equal(mockResponse.Body, result.DataBuffer)
}

func (s *MinioUploadTestSuite) TestFetchAndUploadKYCImages_HttpError() {
	// Arrange
	params := &dto.UserKycImageStoreParams{
		ZalopayID: 12345,
		ImageUri:  "https://source-url.com",
		ImagePath: "test/path",
	}

	s.mockHttpClient.EXPECT().
		RawGet(s.ctx, params.ImageUri, "FetchAndUploadKYCImages").
		Return(500, nil, errors.New("http error"))

	// Act
	result, err := s.service.FetchAndUploadKYCImages(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}
