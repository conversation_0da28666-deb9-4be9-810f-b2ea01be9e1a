package minio_storage

import (
	"context"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"
	"gitlab.zalopay.vn/fin/platform/common/s3storage"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/zutils"
)

type MinioUpload struct {
	s3Client   s3storage.Client
	httpClient base.KycImageHttpClient
}

type ImageData struct {
	Data        []byte
	ContentType string
}

func NewMinioUpload(s3Client s3storage.Client, httpClient base.KycImageHttpClient) _interface.MinioUploader {
	return &MinioUpload{
		s3Client:   s3Client,
		httpClient: httpClient,
	}
}

func (u *MinioUpload) GetImageObj(ctx context.Context, zalopayID int64, imagePath string) (*model.Image, error) {
	data, err := u.s3Client.GetObject(ctx, cast.ToString(zalopayID), imagePath)
	if err != nil {
		return nil, errors.Wrapf(err, "get face image fail, error=%v", err)
	}
	return &model.Image{
		Path:        data.FullPath,
		Checksum:    zutils.CalculateChecksum(data.Data),
		DataBuffer:  data.Data,
		ContentType: data.ContentType,
	}, nil

}

func (u *MinioUpload) GetImageUrl(ctx context.Context,
	zalopayID int64, imagePath string) (string, error) {
	url, err := u.s3Client.PresignedURL(ctx, cast.ToString(zalopayID), imagePath)

	if err != nil {
		return "", errors.Wrapf(err, "get presigned URL fail, error=%v", err)
	}
	return url, nil
}

func (u *MinioUpload) GetFaceImageUrl(ctx context.Context,
	zalopayID int64, imagePath string) (string, error) {
	url, err := u.GetImageUrl(ctx, zalopayID, imagePath)
	if err != nil {
		return "", errors.Wrapf(err, "get face image URL fail, error=%v", err)
	}
	return url, nil
}

func (u *MinioUpload) UploadSignatureImage(ctx context.Context, params *dto.WetSignUploadParams) error {
	zlpID := cast.ToString(params.ZalopayID)
	fileData := &s3storage.FileData{
		FullPath:    params.ImagePath,
		Data:        params.ImageData,
		ContentType: params.ContentType,
	}
	if err := u.s3Client.PutObject(ctx, zlpID, fileData); err != nil {
		return errors.Errorf("upload signature image fail, error=%v", err)
	}
	return nil
}

func (u *MinioUpload) FetchAndUploadFaceImage(ctx context.Context, params *dto.FaceImgStoreParams) (*model.Image, error) {
	statusCode, rawResp, err := u.httpClient.RawGet(ctx, params.ImageUri, "FetchAndUploadFaceImage")
	if err != nil {
		return nil, errors.Wrap(err, "get KYC image fail")
	}
	if httpclient.IsError(statusCode) {
		return nil, errors.Errorf("error when get KYC image, statusCode=%v", statusCode)
	}
	if rawResp == nil {
		return nil, errors.New("empty resp when fetch image from UM")
	}

	fileData := &s3storage.FileData{
		FullPath:    params.ImagePath,
		Data:        rawResp.Body,
		ContentType: rawResp.ContentType,
	}
	if err = u.s3Client.PutObject(ctx, cast.ToString(params.ZalopayID), fileData); err != nil {
		return nil, errors.Wrapf(err, "upload image to S3 fail, error=%v", err)
	}

	return &model.Image{
		Path:        fileData.FullPath,
		ContentType: fileData.ContentType,
	}, nil
}

func (u *MinioUpload) FetchAndUploadKYCImages(ctx context.Context, params *dto.UserKycImageStoreParams) (*model.Image, error) {
	statusCode, rawResp, err := u.httpClient.RawGet(ctx, params.ImageUri, "FetchAndUploadKYCImages")
	if err != nil {
		return nil, err
	}
	if httpclient.IsError(statusCode) {
		return nil, errors.Errorf("error when get KYC image, statusCode=%v", statusCode)
	}
	if rawResp == nil {
		return nil, errors.Errorf("empty resp when fetch image from UM")
	}

	fileData := &s3storage.FileData{
		FullPath:    params.ImagePath,
		Data:        rawResp.Body,
		ContentType: rawResp.ContentType,
	}
	if err = u.s3Client.PutObject(ctx, cast.ToString(params.ZalopayID), fileData); err != nil {
		return nil, errors.Wrapf(err, "upload image to S3 fail, error=%v", err)
	}

	fileUrl, err := u.s3Client.PresignedURL(ctx, cast.ToString(params.ZalopayID), fileData.FullPath)
	if err != nil {
		return nil, errors.Wrapf(err, "get presigned URL fail, error=%v", err)
	}

	result := &model.Image{
		URL:         fileUrl,
		Path:        fileData.FullPath,
		ContentType: fileData.ContentType,
		Checksum:    zutils.CalculateChecksum(rawResp.Body),
	}
	if params.NeedBuffer {
		result.DataBuffer = rawResp.Body
	}
	return result, nil
}
