package risk_system

import (
	"encoding/json"
)

//  API Financial Service Fraud Consulting
//  Spec: https://confluence.zalopay.vn/x/LKUXBg

const (
	MethodNameCommon       = "api"
	MethodNameUnderwriting = "fsCIMBUnderwriting"
	SuccessCode            = 1
)

const (
	fsCredSource        = "fs_backend"
	creditCredSource    = "credit_score_backend"
	fsOnboardingEvent   = "fs_installment_onboarding"
	fsUnderwritingEvent = "fsCIMBUnderwriting"
)

// Level is risk level
type Level int32

const (
	HighRiskLevel   Level = -1
	MediumRiskLevel Level = 0
	LowRiskLevel    Level = 1
)

const (
	DataCodeSuccess            = 1
	DataCodeIdentificationFail = 0
	DataCodeRejected           = -1
	DataCodeSuccess2           = 0

	ActionCodeFaceAuthentication = 10_000
	ActionCodeUpdateKyc          = 15_000
	ActionCodeContactAdmin       = 20
	ActionCodeContactCS          = 1
)

type CheckFraudRequest struct {
	RequestID   string `mapstructure:"requestId"`
	Source      string `mapstructure:"source"`
	Event       string `mapstructure:"event"`
	Data        string `mapstructure:"data"`
	RequestTime int64  `mapstructure:"requestTime"`
	Sign        string `mapstructure:"sign"`
}

type CheckFraudResponse struct {
	RequestID     string `json:"-"`
	ReturnCode    int    `json:"returncode"`
	ReturnMessage string `json:"returnmessage"`
	ReturnData    Data   `json:"data"`
}

type Data struct {
	Code       int32   `json:"code"`
	InfoCode   int32   `json:"infoCode"`
	ActionCode int32   `json:"actionCode"`
	Scores     []Score `json:"scores"`
}

type Score struct {
	PartnerCode      string `json:"partnercode"`
	ScoreBand        int32  `json:"scoreband"`
	EstimatedLimit   int64  `json:"estimatedlimit"`
	EstimatedProduct string `json:"estimatedproduct"`
}

func (r *CheckFraudResponse) HasFraud() bool {
	return r.ReturnCode == SuccessCode && Level(r.ReturnData.Code) == HighRiskLevel
}

func (r CheckFraudResponse) NeedFaceChallenge() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == MediumRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeFaceAuthentication &&
		r.ReturnData.InfoCode >= 630_000 && r.ReturnData.InfoCode <= 631_999
}

func (r CheckFraudResponse) NeedUpdateEKyc() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == MediumRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeUpdateKyc &&
		r.ReturnData.InfoCode >= 600_001 && r.ReturnData.InfoCode <= 600_100
}

func (r CheckFraudResponse) NeedContactCS() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == HighRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeContactCS &&
		r.ReturnData.InfoCode >= 600_101 && r.ReturnData.InfoCode <= 600_200
}

func (r CheckFraudResponse) IsRejected() bool {
	return r.ReturnCode == SuccessCode &&
		Level(r.ReturnData.Code) == HighRiskLevel &&
		r.ReturnData.ActionCode == ActionCodeContactAdmin &&
		r.ReturnData.InfoCode >= 600_201 && r.ReturnData.InfoCode <= 600_700
}

type FraudData struct {
	UserID   string `json:"userId"`
	DeviceID string `json:"deviceId"`
	UserIP   string `json:"userIp"`
}

func (d FraudData) Serialize() (string, error) {
	raw, err := json.Marshal(d)
	if err != nil {
		return "", err
	}
	return string(raw), nil
}

func (d FraudData) IsEmpty() bool {
	return d.UserID == "0" || d.UserID == ""
}

type UnderwritingPayload struct {
	UserID string `json:"userId"`
}

func (p UnderwritingPayload) Serialize() (string, error) {
	raw, err := json.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(raw), nil
}

type UnderwritingData map[string]any

type GetUnderwritingRequest struct {
	RequestID   string `mapstructure:"requestId"`
	Source      string `mapstructure:"source"`
	Event       string `mapstructure:"event"`
	Data        string `mapstructure:"data"`
	RequestTime int64  `mapstructure:"requestTime"`
	Sign        string `mapstructure:"sign"`
}

type GetUnderwritingResponse struct {
	RequestID     string           `json:"-"`
	ReturnCode    int              `json:"statusCode"`
	ReturnMessage string           `json:"statusMessage"`
	ReturnData    UnderwritingData `json:"data"`
}
