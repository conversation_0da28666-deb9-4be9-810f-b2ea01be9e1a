package risk_system

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	zpBase "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/zp_base"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
)

type RiskClientTestSuite struct {
	suite.Suite
	ctx                  context.Context
	ctrl                 *gomock.Controller
	service              *riskClient
	mockFraudGrpcClient  *zpBase.MockZPBaseClient
	mockCreditGrpcClient *zpBase.MockZPBaseClient
}

func TestRiskClientSuite(t *testing.T) {
	suite.Run(t, new(RiskClientTestSuite))
}

func (s *RiskClientTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockFraudGrpcClient = zpBase.NewMockZPBaseClient(s.ctrl)
	s.mockCreditGrpcClient = zpBase.NewMockZPBaseClient(s.ctrl)
	s.ctx = context.Background()

	conf := &config.Onboarding{
		FraudRisk: &config.Onboarding_FraudRisk{
			HashKey: "test-hash-key",
		},
	}

	s.service = NewClient(conf, s.mockFraudGrpcClient, s.mockCreditGrpcClient, log.DefaultLogger).(*riskClient)
}

func (s *RiskClientTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *RiskClientTestSuite) TestCheckFraud_Success() {
	// Arrange
	params := &dto.UserRiskEvaluationParams{
		ZalopayID:   12345,
		RequestIP:   "127.0.0.1",
		DeviceID:    "test-device-id",
		RequestTime: time.Now().UnixMilli(),
	}

	mockData := &CheckFraudResponse{
		ReturnCode: 1,
		ReturnData: Data{
			Code:       1,
			InfoCode:   1,
			ActionCode: 1,
		},
	}
	dataRaw, _ := json.Marshal(mockData)
	mockResp := &zpBase.GrpcBaseResponse{
		Response: string(dataRaw),
	}

	s.mockFraudGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.EvaluateRisk(s.ctx, params)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.False(result.HasFraud)
	s.Equal(int32(1), result.RiskCode)
	s.Equal(int32(1), result.InfoCode)
	s.Equal(int32(1), result.ActionCode)
}

func (s *RiskClientTestSuite) TestCheckFraud_EmptyData() {
	// Arrange
	params := &dto.UserRiskEvaluationParams{}

	// Act
	result, err := s.service.EvaluateRisk(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_GrpcError() {
	// Arrange
	params := &dto.UserRiskEvaluationParams{
		ZalopayID:   12345,
		RequestIP:   "127.0.0.1",
		DeviceID:    "test-device-id",
		RequestTime: time.Now().UnixMilli(),
	}

	s.mockFraudGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(nil, errors.New("grpc error"))

	// Act
	result, err := s.service.EvaluateRisk(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestCheckFraud_InvalidResponse() {
	// Arrange
	params := &dto.UserRiskEvaluationParams{
		ZalopayID:   12345,
		RequestIP:   "127.0.0.1",
		DeviceID:    "test-device-id",
		RequestTime: time.Now().UnixMilli(),
	}

	mockResp := &zpBase.GrpcBaseResponse{
		Response: `invalid json`,
	}

	s.mockFraudGrpcClient.EXPECT().
		SendRequest(s.ctx, gomock.Any()).
		Return(mockResp, nil)

	// Act
	result, err := s.service.EvaluateRisk(s.ctx, params)

	// Assert
	s.Error(err)
	s.Nil(result)
}

func (s *RiskClientTestSuite) TestBuildCheckFraudRequest_Success() {
	// Arrange
	data := FraudData{
		UserID:   "12345",
		UserIP:   "127.0.0.1",
		DeviceID: "test-device-id",
	}
	reqTime := time.Now().UnixMilli()
	payload := &dto.UserRiskEvaluationParams{
		ZalopayID:   cast.ToInt64(data.UserID),
		RequestIP:   data.UserIP,
		DeviceID:    data.DeviceID,
		RequestTime: reqTime,
	}

	// Act
	requestID, request, err := s.service.buildCheckRiskRequest(s.ctx, payload, "test-source", "test-event")

	// Assert
	s.NoError(err)
	s.NotEmpty(requestID)
	s.NotNil(request)
	s.Equal(MethodNameCommon, request.MethodName)

	// Verify request params
	params := request.Params
	s.NotEmpty(params["sign"])
	s.Equal("test-source", params["source"])
	s.Equal("test-event", params["event"])
	s.NotEmpty(params["data"])
}

func (s *RiskClientTestSuite) TestBuildCheckFraudRequest_EmptyData() {
	// Arrange
	reqTime := time.Now().UnixMilli()
	payload := &dto.UserRiskEvaluationParams{
		RequestTime: reqTime,
	}

	// Act
	requestID, request, err := s.service.buildCheckRiskRequest(s.ctx, payload, "test-source", "test-event")

	// Assert
	s.Error(err)
	s.Empty(requestID)
	s.Nil(request)
}
