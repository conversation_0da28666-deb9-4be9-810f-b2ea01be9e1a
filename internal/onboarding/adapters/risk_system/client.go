package risk_system

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"

	zpBase "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/zp_base"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type FraudSystem interface {
	zpBase.ZPBaseClient
}

type CreditSystem interface {
	zpBase.ZPBaseClient
}

type riskClient struct {
	logger           *log.Helper
	fraudHashKey     string
	creditHashKey    string
	fraudGrpcClient  FraudSystem
	creditGrpcClient CreditSystem
}

func NewClient(
	conf *config.Onboarding,
	fraudClient FraudSystem,
	creditClient CreditSystem,
	kLogger log.Logger) _interface.FraudService {
	fraudHashKey := conf.GetFraudRisk().GetHashKey()
	creditHashKey := conf.GetCreditScore().GetHashKey()
	logger := log.NewHelper(log.With(kLogger, "adapters", "risk"))
	return &riskClient{
		logger:           logger,
		fraudHashKey:     fraudHashKey,
		creditHashKey:    creditHashKey,
		fraudGrpcClient:  fraudClient,
		creditGrpcClient: creditClient,
	}
}

// EvaluateRisk will call API to RMS to check user has fraud
// Input:
// + userID: userID of user
// + deviceID and userIP: current info of device which user is using.
// + reqTime: current time call API (millisecond)
func (r *riskClient) EvaluateRisk(ctx context.Context, params *dto.UserRiskEvaluationParams) (*dto.UserRiskEvaluationResult, error) {
	logger := r.logger.WithContext(ctx)

	requestID, request, err := r.buildCheckRiskRequest(ctx, params, fsCredSource, fsOnboardingEvent)
	if err != nil {
		logger.Errorw("msg", "[RiskSystem] fail to build CheckFraud request", "error", err)
		return nil, err
	}

	// Step 2: Convert to BaseRequest of gRPC model & make request
	resp, err := r.fraudGrpcClient.SendRequest(ctx, request)

	// Step 3: Handle error (if exist) and marshal response to struct
	if err != nil {
		logger.Errorw("msg", "[RiskSystem] fail to call CheckFraud from Risk", "error", err)
		return nil, err
	}

	var response CheckFraudResponse
	if err = json.Unmarshal([]byte(resp.Response), &response); err != nil {
		logger.Errorw("msg", "[RiskSystem] fail to unmarshal response to struct", "error", err)
		return nil, err
	}
	response.RequestID = requestID

	return &dto.UserRiskEvaluationResult{
		HasFraud:   response.HasFraud(),
		RiskCode:   response.ReturnData.Code,
		InfoCode:   response.ReturnData.InfoCode,
		ActionCode: response.ReturnData.ActionCode,
	}, nil
}

func (r *riskClient) GetUnderwriting(ctx context.Context, params *dto.UserRiskUnderwritingParams) (map[string]any, error) {
	logger := r.logger.WithContext(ctx)

	request, err := r.buildGetUnderwritingRequest(ctx, params, creditCredSource, fsUnderwritingEvent)
	if err != nil {
		logger.Errorw("msg", "[CreditSystem] fail to build GetUnderwriting request", "error", err)
		return nil, err
	}

	logger.Infow("msg", "[CreditSystem] GetUnderwriting request", "request", request)

	// Step 2: Convert to BaseRequest of gRPC model & make request
	resp, err := r.creditGrpcClient.SendRequest(ctx, request)

	// Step 3: Handle error (if exist) and marshal response to struct
	if err != nil {
		logger.Errorw("msg", "[CreditSystem] fail to call GetUnderwriting from Risk", "error", err)
		return nil, err
	}

	var response GetUnderwritingResponse
	if err = json.Unmarshal([]byte(resp.Response), &response); err != nil {
		logger.Errorw("msg", "[CreditSystem] fail to unmarshal response to struct", "error", err)
		return nil, err
	}
	if response.ReturnCode != DataCodeSuccess2 {
		logger.Errorw("msg", "[CreditSystem] return code is not success", "response", response)
		return nil, fmt.Errorf("get underwriting failed, error=%v", response.ReturnMessage)
	}

	return response.ReturnData, nil
}

func (r *riskClient) buildCheckRiskRequest(
	ctx context.Context, params *dto.UserRiskEvaluationParams,
	source string, event string) (string, *zpBase.GrpcBaseRequest, error) {
	logger := r.logger.WithContext(ctx)
	reqTime := params.RequestTime

	data := FraudData{
		UserID:   cast.ToString(params.ZalopayID),
		UserIP:   params.RequestIP,
		DeviceID: params.DeviceID,
	}
	if data.IsEmpty() {
		logger.Error("Data check fraud is empty, should break flow")
		return "", nil, fmt.Errorf("could not process empty data, %v", data)
	}

	sData, err := data.Serialize()
	if err != nil {
		logger.Errorf("Failed to serialize request data, %v", err)
		return "", nil, err
	}

	// Compute signature = hash(data|curTime|hashKey)
	sign, err := computeSignature(ctx, sData, cast.ToString(reqTime), r.fraudHashKey)
	if err != nil {
		logger.Errorf("Failed to sign request, %v", err)
		return "", nil, err
	}

	// Compute requestID = "{user_id}-{device_id}-{request-time}"
	requestID := fmt.Sprintf("%v-%v", data.UserID, reqTime)
	request := CheckFraudRequest{
		Sign:        sign,
		Event:       event,
		Source:      source,
		Data:        sData,
		RequestID:   requestID,
		RequestTime: reqTime,
	}
	return requestID, &zpBase.GrpcBaseRequest{
		MethodName: MethodNameCommon,
		Params:     convert2MapString(request),
	}, nil
}

func (r *riskClient) buildGetUnderwritingRequest(
	ctx context.Context, params *dto.UserRiskUnderwritingParams,
	source string, event string) (*zpBase.GrpcBaseRequest, error) {
	logger := r.logger.WithContext(ctx)

	data := UnderwritingPayload{
		UserID: cast.ToString(params.ZalopayID),
	}
	sData, err := data.Serialize()
	if err != nil {
		logger.Errorf("Failed to serialize request data, %v", err)
		return nil, err
	}

	sign, err := computeSignature(ctx, sData, cast.ToString(params.RequestTime), r.creditHashKey)
	if err != nil {
		logger.Errorf("Failed to sign request, %v", err)
		return nil, err
	}

	requestID := fmt.Sprintf("%v-%v", data.UserID, params.RequestTime)
	request := CheckFraudRequest{
		Sign:        sign,
		Event:       event,
		Source:      source,
		Data:        sData,
		RequestID:   requestID,
		RequestTime: params.RequestTime,
	}
	return &zpBase.GrpcBaseRequest{
		MethodName: MethodNameUnderwriting,
		Params:     convert2MapString(request),
	}, nil
}
