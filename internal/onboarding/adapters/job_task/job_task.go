package job_task

import (
	"context"
	"fmt"
	"slices"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/sdk/client"
	"go.temporal.io/sdk/temporal"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/utils"
)

type jobTask struct {
	logger  *log.Helper
	tempCli client.Client
	config  *config.Onboarding_Schedulers
}

func NewJobTaskMgmt(conf *config.Onboarding, tempCli client.Client, kLogger log.Logger) _interface.JobTaskMgmt {
	logger := log.NewHelper(log.With(kLogger, "adapters", "job-task"))
	return &jobTask{
		logger:  logger,
		tempCli: tempCli,
		config:  conf.GetSchedulers(),
	}
}

func (s jobTask) RegisterQueryOnboardingStatusTask(
	ctx context.Context, params *model.OnboardingStatusTask) error {
	zalopayID := params.ZalopayID
	onboardID := params.OnboardingID
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetOnboardingStatus()
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE
	wfIdentifier := fmt.Sprintf(utils.WfIDOnboardingStatus.String(), zalopayID, onboardID)

	logger.Infow(
		"msg", "Start register polling onboarding status task",
		"zalopay_id", zalopayID, "onboarding_id", onboardID)

	wfOption := client.StartWorkflowOptions{
		ID:                       wfIdentifier,
		TaskQueue:                wfConfig.GetQueueName(),
		WorkflowIDReusePolicy:    wfReusePolicy,
		WorkflowExecutionTimeout: wfConfig.GetMaxTimeRetry().AsDuration(),
		RetryPolicy:              &temporal.RetryPolicy{MaximumAttempts: 1},
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorf("Fail to register polling onboarding status task, error=%v", err)
		return errors.Wrap(err, "register polling onboarding status task failed")
	}

	logger.Infof("Register polling onboarding status task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s jobTask) RegisterSubmitFaceImageTask(ctx context.Context, params *dto.FaceImgSubmitTask) error {
	zalopayID := params.ZalopayID
	onboardID := params.OnboardingID
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetSubmitFaceImage()
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE
	wfIdentifier := fmt.Sprintf(utils.WfIDFaceImageSubmit.String(), zalopayID, onboardID)

	logger.Infow(
		"msg", "Start register submit face image task",
		"zalopay_id", zalopayID, "onboarding_id", onboardID)

	wfOption := client.StartWorkflowOptions{
		ID:                       wfIdentifier,
		TaskQueue:                wfConfig.GetQueueName(),
		WorkflowIDReusePolicy:    wfReusePolicy,
		WorkflowExecutionTimeout: wfConfig.GetMaxTimeRetry().AsDuration(),
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts:    3,
			BackoffCoefficient: 1.5,
			InitialInterval:    wfConfig.GetTimeInterval().AsDuration(),
			MaximumInterval:    wfConfig.GetMaxTimeInterval().AsDuration(),
		},
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorf("Fail to register submit face image task, error=%v", err)
		return errors.Wrap(err, "register submit face image task failed")
	}

	logger.Infof("Register submit face image task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s jobTask) RegisterContractSigningTask(ctx context.Context, params *dto.ContractSingingTask) error {
	zalopayID := params.ZalopayID
	onboardID := params.OnboardingID
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetContractSigning()
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE
	wfIdentifier := fmt.Sprintf(utils.WfIDContractSigning.String(), zalopayID, onboardID)

	logger.Infow(
		"msg", "Start register contract signing task",
		"zalopay_id", zalopayID, "onboarding_id", onboardID)

	wfOption := client.StartWorkflowOptions{
		ID:                       wfIdentifier,
		TaskQueue:                wfConfig.GetQueueName(),
		WorkflowIDReusePolicy:    wfReusePolicy,
		WorkflowExecutionTimeout: wfConfig.GetMaxTimeRetry().AsDuration(),
		RetryPolicy: &temporal.RetryPolicy{
			MaximumAttempts:    3,
			BackoffCoefficient: 1.5,
			InitialInterval:    wfConfig.GetTimeInterval().AsDuration(),
			MaximumInterval:    wfConfig.GetMaxTimeInterval().AsDuration(),
		},
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), params)
	if err != nil {
		logger.Errorf("Fail to register contract signing task, error=%v", err)
		return errors.Wrap(err, "register contract signing task failed")
	}

	logger.Infof("Register contract signing task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s jobTask) CanTriggerQueryOnboardingStatusTask(ctx context.Context,
	params *model.OnboardingStatusTask) (bool, error) {
	wfIdentifier := fmt.Sprintf(utils.WfIDOnboardingStatus.String(), params.ZalopayID, params.OnboardingID)
	wfExecInfo, err := s.tempCli.DescribeWorkflowExecution(ctx, wfIdentifier, "")
	if err != nil {
		if notFoundErr := err.(*serviceerror.NotFound); notFoundErr != nil {
			return true, nil
		}
		return false, errors.Wrap(err, "cannot query information of onboarding status task")
	}

	wfStatus := wfExecInfo.GetWorkflowExecutionInfo().GetStatus()
	unAllowedStatuses := []enums.WorkflowExecutionStatus{
		enums.WORKFLOW_EXECUTION_STATUS_RUNNING,
		enums.WORKFLOW_EXECUTION_STATUS_COMPLETED,
	}

	return !slices.Contains(unAllowedStatuses, wfStatus), nil
}
