package image_fetcher

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/httpclient"

	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/cmd/base"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
)

type imgFetcher struct {
	kycImgClient base.KycImageHttpClient
}

func NewImageFetcher(kycImgClient base.KycImageHttpClient) _interface.ImageFetcher {
	return &imgFetcher{
		kycImgClient: kycImgClient,
	}
}

func (i imgFetcher) GetKycImageData(ctx context.Context, url string) (*model.Image, error) {
	statusCode, rawResp, err := i.kycImgClient.RawGet(ctx, url, "GetKycImageData")
	if err != nil {
		return nil, errors.Wrapf(err, "get image fail, url=%v", url)
	}
	if httpclient.IsError(statusCode) {
		return nil, errors.Errorf("error when get image, url=%v, statusCode=%v", url, statusCode)
	}
	if rawResp == nil {
		return nil, errors.Errorf("empty resp when fetch image from UM, url=%v", url)
	}

	imageData := &model.Image{
		DataBuffer:  rawResp.Body,
		ContentType: rawResp.ContentType,
	}
	return imageData, nil
}
