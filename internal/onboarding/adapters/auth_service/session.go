package auth_service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"

	"gitlab.zalopay.vn/grpc-specifications/session-service/pkg/api/sessionv1"
)

func NewClient(client sessionv1.SessionServiceClient, logger log.Logger) auth.Authenticator {
	return &authenticator{
		client: client,
		logger: log.NewHelper(log.With(logger, "adapters", "auth-service")),
	}
}

type authenticator struct {
	logger *log.Helper
	client sessionv1.SessionServiceClient
}

func (a *authenticator) Authenticate(ctx context.Context, sessionID string) (*sessionv1.Session, error) {
	if sessionID == "" {
		return nil, errors.Errorf("session id is empty")
	}

	session, err := a.client.GetSession(ctx, &sessionv1.GetSessionReq{
		SessionId: sessionID,
	})
	if err != nil {
		a.logger.Errorf("get session failed, sessionID=%v, error=%v", sessionID, err)
		return nil, errors.Wrap(err, "get session failed")
	}

	return session, nil
}
