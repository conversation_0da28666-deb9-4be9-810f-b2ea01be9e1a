package rate_limiter

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"gitlab.zalopay.vn/fin/platform/common/redis"

	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/ratelimit"
)

type rateLimiter struct {
	kycNfc ratelimit.Limiter
}

func NewClient(cache redis.CacheNoCaller, kLogger log.Logger) (_interface.RateLimiter, error) {
	logger := log.NewHelper(log.With(kLogger, "adapters", "rate-limiter"))
	kycNfcIns, err := ratelimit.NewFixedWindow(&ratelimit.FixedWindowConfig{
		RedisCli:    cache.GetRedisClient(),
		StoreType:   ratelimit.RedisStore,
		WindowSize:  24 * time.Hour,
		MaxRequests: 3,
	})
	if err != nil {
		logger.Errorf("fail to create rate limiter, err %v", err)
		return nil, err
	}
	return &rateLimiter{kycNfc: kycNfcIns}, nil
}

func (r rateLimiter) AllowResetKycNfc(ctx context.Context, key string) (bool, error) {
	allow, err := r.kycNfc.Allow(ctx, key)
	if err != nil {
		return false, errors.Wrapf(err, "fail to check rate limit for key %s", key)
	}
	return allow, nil
}
