package whitelist

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	abplatform "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ab_platform"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"go.uber.org/mock/gomock"
)

type WhitelistTestSuite struct {
	suite.Suite
	mockAB *abplatform.MockABPublicServiceClient
	client _interface.WhitelistSys
}

func TestWhitelistSuite(t *testing.T) {
	suite.Run(t, new(WhitelistTestSuite))
}

func (s *WhitelistTestSuite) SetupTest() {
	ctrl := gomock.NewController(s.T())
	s.mockAB = abplatform.NewMockABPublicServiceClient(ctrl)
}

func (s *WhitelistTestSuite) TestIsOnboardWhitelisted_UserInWhitelist() {
	// Setup
	conf := s.setupConfig(true, "test_key", "group_a")
	s.setupMockABResponse("test_key", "group_a", nil)
	s.client = NewClient(s.mockAB, conf, log.DefaultLogger)

	// Test
	result, err := s.client.IsOnboardWhitelisted(context.Background(), 123)

	// Assert
	s.NoError(err)
	s.True(result)

}

func (s *WhitelistTestSuite) TestIsOnboardWhitelisted_UserNotInWhitelist() {
	// Setup
	conf := s.setupConfig(true, "test_key", "group_a")
	s.setupMockABResponse("test_key", "group_b", nil)
	s.client = NewClient(s.mockAB, conf, log.DefaultLogger)

	// Test
	result, err := s.client.IsOnboardWhitelisted(context.Background(), 123)

	// Assert
	s.NoError(err)
	s.False(result)
}

func (s *WhitelistTestSuite) TestIsOnboardWhitelisted_WhitelistDisabled() {
	// Setup
	conf := s.setupConfig(false, "test_key", "group_a")
	s.client = NewClient(s.mockAB, conf, log.DefaultLogger)

	// Test
	result, err := s.client.IsOnboardWhitelisted(context.Background(), 123)

	// Assert
	s.NoError(err)
	s.True(result)
}

func (s *WhitelistTestSuite) setupConfig(enabled bool, experimentKey string, whitelistGroup string) *config.Onboarding {
	return &config.Onboarding{
		Whitelists: &config.Onboarding_Whitelists{
			Onboarding: &config.Whitelist{
				Enabled:        enabled,
				ExperimentKey:  experimentKey,
				WhitelistGroup: whitelistGroup,
			},
		},
	}
}

func (s *WhitelistTestSuite) setupMockABResponse(experimentKey string, whitelistGroup string, err error) {
	s.mockAB.EXPECT().GetExperimentByKey(gomock.Any(),
		&abplatform.GetExperimentByKeyRequest{ExperimentKey: experimentKey}).
		Return(&abplatform.GetExperimentResponse{
			Group: whitelistGroup,
		}, err)
}
