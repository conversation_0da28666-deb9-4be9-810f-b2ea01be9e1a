package whitelist

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	abplatform "gitlab.zalopay.vn/fin/installment/installment-service/api/external_services/ab_platform"
	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/onboarding/usecase/interface"
	"google.golang.org/grpc/metadata"
)

type client struct {
	logger   *log.Helper
	wlConfig map[WhitelistFeat]WhitelistItem
	abClient abplatform.ABPublicServiceClient
}

type WhitelistFeat string

const (
	Onboarding WhitelistFeat = "onboarding"
)

type WhitelistItem struct {
	Enabled        bool
	ExperimentKey  string
	WhitelistGroup string
}

func NewClient(
	abClient abplatform.ABPublicServiceClient,
	conf *config.Onboarding, kLogger log.Logger) _interface.WhitelistSys {
	logger := log.NewHelper(log.With(kLogger, "adapters", "whitelist"))
	whitelist := conf.GetWhitelists()
	configs := map[WhitelistFeat]WhitelistItem{
		Onboarding: {
			Enabled:        whitelist.GetOnboarding().GetEnabled(),
			ExperimentKey:  whitelist.GetOnboarding().GetExperimentKey(),
			WhitelistGroup: whitelist.GetOnboarding().GetWhitelistGroup(),
		},
	}
	return &client{
		logger:   logger,
		wlConfig: configs,
		abClient: abClient,
	}
}

func (c *client) IsOnboardWhitelisted(ctx context.Context, zaloPayID int64) (bool, error) {
	logger := c.logger.WithContext(ctx)

	item, ok := c.wlConfig[Onboarding]
	if !ok {
		logger.Error("Whitelist config not found")
		return false, errors.New("whitelist config not found")
	}

	if !item.Enabled {
		logger.Infof("Skip check whitelist onboarding because it not enabled by config, zalopayID=%v", zaloPayID)
		return true, nil
	}

	md := metadata.Pairs("x-zalopay-id", cast.ToString(zaloPayID))
	ctx = metadata.NewOutgoingContext(ctx, md)
	resp, err := c.abClient.GetExperimentByKey(ctx, &abplatform.GetExperimentByKeyRequest{
		ExperimentKey: item.ExperimentKey,
	})
	if err != nil {
		logger.Errorf("GetExperiment from ab-platform has error: %v", err)
		return false, errors.Wrap(err, "GetExperiment from ab-platform failed")
	}
	if resp.GetGroup() == item.WhitelistGroup {
		return true, nil
	}
	return false, nil
}
