syntax = "proto3";

package account_service.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "installment/api/account-service/v1;v1";

service Account {
  rpc CreateAccount(CreateAccountRequest) returns (CreateAccountResponse) {}
  rpc GetClientAccount(GetClientAccountRequest) returns (GetClientAccountResponse) {
    option (google.api.http) = {
      get: "/account/v1/account"
    };
  }
  rpc ListClientProposedService(ListClientProposedServiceRequest) returns (ListClientProposedServiceResponse) {
    option (google.api.http) = {
      get: "/account/v1/content/proposed-services"
    };
  }
  rpc GetAccount(GetAccountRequest) returns (GetAccountResponse) {}
  rpc GetAccountForPayment(GetAccountForPaymentRequest) returns (GetAccountForPaymentResponse) {}
  rpc ListAccountForStmtSync(ListAccountForStmtSyncRequest) returns (ListAccountForStmtSyncResponse) {}
}

message CreateAccountRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
  string partner_code = 3 [(validate.rules).string.min_len = 1];
}

message CreateAccountResponse {
  int64 account_id = 1;
}

message ListAccountRequest {
  repeated string partner_ids = 1;
}

message ListAccountResponse {
  repeated UserAccount accounts = 1;
}

message GetAccountRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  oneof query_by {
    int64 account_id = 2 [(validate.rules).int64.gt = 0];
    string partner_code = 3 [(validate.rules).string.min_len = 1];
  }
}

message GetAccountResponse {
  UserAccount account = 1;
}

message GetClientAccountRequest {
  oneof query_by {
    int64 account_id = 1 [json_name = 'account_id', (validate.rules).int64.gt = 0];
    string partner_code = 2 [json_name = 'partner_code', (validate.rules).string.min_len = 1];
  };
}

message GetClientAccountResponse {
  UserAccount account = 1;
}

message UserAccount {
  int64 account_id = 1 [json_name = 'account_id'];
  Status status = 2 [json_name = 'status'];
  string source = 3 [json_name = 'source'];
  string partner_code = 4 [json_name = 'partner_code'];
  string partner_account_name = 5 [json_name = 'partner_account_name'];
  string partner_account_number = 6 [json_name = 'partner_account_number'];
  int64 installment_limit = 7 [json_name = 'installment_limit'];
  int64 installment_balance = 8 [json_name = 'installment_balance'];
  InstallmentTerm installment_term = 9 [json_name = 'installment_term'];
  google.protobuf.Timestamp created_at = 10 [json_name = 'created_at'];
  optional int64 repayment_balance = 11 [json_name = 'repayment_balance'];
}

// StmtAccount represents a statement account and only use for statement service
message StmtAccount {
  int64 zalopay_id = 1;
  int64 account_id = 2;
  Status status = 3;
  string partner_code = 4;
  string partner_account_name = 5;
  string partner_account_number = 6;
}

// Account status
enum Status {
  UNSPECIFIED = 0;
  ACTIVE = 1;
  INACTIVE = 2;
  BLOCKED = 3;
  CLOSED = 4;
}

message ListClientProposedServiceRequest {
  string partner_code = 1;
}

message ListClientProposedServiceResponse {
  repeated ServiceProposed services = 1;
}

message ContentImage {
  string icon_url = 1 [json_name = 'icon_url'];
  string cover_url = 2 [json_name = 'cover_url'];
  string thumbnail_url = 3 [json_name = 'thumbnail_url'];
}

message ContentInteraction {
//  string type = 1;
  string zpa_url = 2 [json_name = 'zpa_url'];
  string zpi_url = 3 [json_name = 'zpi_url'];
}

message ServiceProposed {
  string code = 1;
  string name = 2;
  ContentImage content_image  = 3 [json_name = 'content_image'];
  ContentInteraction interaction = 4 [json_name = 'interaction'];
}

message GetAccountForPaymentRequest {
  int64 zalopay_id = 1 [json_name = 'zalopay_id', (validate.rules).int64.gt = 0];
  string partner_code = 2 [json_name = 'partner_code', (validate.rules).string.min_len = 1];
}

message GetAccountForPaymentResponse {
  UserAccount account = 1;
}

message ListAccountForStmtSyncRequest {
  int32 items_limit = 1 [(validate.rules).int32.gt = 0];
  int64 account_id_from = 2 [(validate.rules).int64.gte = 0];
  google.protobuf.Timestamp statement_date = 3 [(validate.rules).timestamp.required = true];
  repeated string partner_codes = 4;
}

message ListAccountForStmtSyncResponse {
  repeated StmtAccount accounts = 1;
}

message Pagination {
  int32 limit = 1;
  optional int32 offset = 2;
  optional string cursor = 3;
  optional int32 direction = 4;
}

message InstallmentTerm {
  // Installment fee explanation
  string fee_explanation = 1 [json_name = 'fee_explanation'];
  // Statement due date description
  string stmt_due_date_text = 2 [json_name = 'stmt_due_date_text'];
  // Statement incurred date description
  string stmt_incur_date_text = 3 [json_name = 'stmt_incur_date_text'];
}