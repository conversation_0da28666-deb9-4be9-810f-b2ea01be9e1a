// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: account_service/v1/account_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountRequestMultiError, or nil if none found.
func (m *CreateAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := CreateAccountRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := CreateAccountRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := CreateAccountRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateAccountRequestMultiError(errors)
	}

	return nil
}

// CreateAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountRequestMultiError) AllErrors() []error { return m }

// CreateAccountRequestValidationError is the validation error returned by
// CreateAccountRequest.Validate if the designated constraints aren't met.
type CreateAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountRequestValidationError) ErrorName() string {
	return "CreateAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountRequestValidationError{}

// Validate checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountResponseMultiError, or nil if none found.
func (m *CreateAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return CreateAccountResponseMultiError(errors)
	}

	return nil
}

// CreateAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountResponseMultiError) AllErrors() []error { return m }

// CreateAccountResponseValidationError is the validation error returned by
// CreateAccountResponse.Validate if the designated constraints aren't met.
type CreateAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountResponseValidationError) ErrorName() string {
	return "CreateAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountResponseValidationError{}

// Validate checks the field values on ListAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAccountRequestMultiError, or nil if none found.
func (m *ListAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListAccountRequestMultiError(errors)
	}

	return nil
}

// ListAccountRequestMultiError is an error wrapping multiple validation errors
// returned by ListAccountRequest.ValidateAll() if the designated constraints
// aren't met.
type ListAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAccountRequestMultiError) AllErrors() []error { return m }

// ListAccountRequestValidationError is the validation error returned by
// ListAccountRequest.Validate if the designated constraints aren't met.
type ListAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAccountRequestValidationError) ErrorName() string {
	return "ListAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAccountRequestValidationError{}

// Validate checks the field values on ListAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAccountResponseMultiError, or nil if none found.
func (m *ListAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAccountResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAccountResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAccountResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAccountResponseMultiError(errors)
	}

	return nil
}

// ListAccountResponseMultiError is an error wrapping multiple validation
// errors returned by ListAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAccountResponseMultiError) AllErrors() []error { return m }

// ListAccountResponseValidationError is the validation error returned by
// ListAccountResponse.Validate if the designated constraints aren't met.
type ListAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAccountResponseValidationError) ErrorName() string {
	return "ListAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAccountResponseValidationError{}

// Validate checks the field values on GetAccountRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountRequestMultiError, or nil if none found.
func (m *GetAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetAccountRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.QueryBy.(type) {
	case *GetAccountRequest_AccountId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetAccountId() <= 0 {
			err := GetAccountRequestValidationError{
				field:  "AccountId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetAccountRequest_PartnerCode:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
			err := GetAccountRequestValidationError{
				field:  "PartnerCode",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountRequestMultiError(errors)
	}

	return nil
}

// GetAccountRequestMultiError is an error wrapping multiple validation errors
// returned by GetAccountRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountRequestMultiError) AllErrors() []error { return m }

// GetAccountRequestValidationError is the validation error returned by
// GetAccountRequest.Validate if the designated constraints aren't met.
type GetAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountRequestValidationError) ErrorName() string {
	return "GetAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountRequestValidationError{}

// Validate checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountResponseMultiError, or nil if none found.
func (m *GetAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountResponseMultiError(errors)
	}

	return nil
}

// GetAccountResponseMultiError is an error wrapping multiple validation errors
// returned by GetAccountResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountResponseMultiError) AllErrors() []error { return m }

// GetAccountResponseValidationError is the validation error returned by
// GetAccountResponse.Validate if the designated constraints aren't met.
type GetAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountResponseValidationError) ErrorName() string {
	return "GetAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountResponseValidationError{}

// Validate checks the field values on GetClientAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientAccountRequestMultiError, or nil if none found.
func (m *GetClientAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.QueryBy.(type) {
	case *GetClientAccountRequest_AccountId:
		if v == nil {
			err := GetClientAccountRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetAccountId() <= 0 {
			err := GetClientAccountRequestValidationError{
				field:  "AccountId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetClientAccountRequest_PartnerCode:
		if v == nil {
			err := GetClientAccountRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
			err := GetClientAccountRequestValidationError{
				field:  "PartnerCode",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetClientAccountRequestMultiError(errors)
	}

	return nil
}

// GetClientAccountRequestMultiError is an error wrapping multiple validation
// errors returned by GetClientAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetClientAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientAccountRequestMultiError) AllErrors() []error { return m }

// GetClientAccountRequestValidationError is the validation error returned by
// GetClientAccountRequest.Validate if the designated constraints aren't met.
type GetClientAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientAccountRequestValidationError) ErrorName() string {
	return "GetClientAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientAccountRequestValidationError{}

// Validate checks the field values on GetClientAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientAccountResponseMultiError, or nil if none found.
func (m *GetClientAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClientAccountResponseMultiError(errors)
	}

	return nil
}

// GetClientAccountResponseMultiError is an error wrapping multiple validation
// errors returned by GetClientAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type GetClientAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientAccountResponseMultiError) AllErrors() []error { return m }

// GetClientAccountResponseValidationError is the validation error returned by
// GetClientAccountResponse.Validate if the designated constraints aren't met.
type GetClientAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientAccountResponseValidationError) ErrorName() string {
	return "GetClientAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientAccountResponseValidationError{}

// Validate checks the field values on UserAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserAccountMultiError, or
// nil if none found.
func (m *UserAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for Status

	// no validation rules for Source

	// no validation rules for PartnerCode

	// no validation rules for PartnerAccountName

	// no validation rules for PartnerAccountNumber

	// no validation rules for InstallmentLimit

	// no validation rules for InstallmentBalance

	if all {
		switch v := interface{}(m.GetInstallmentTerm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserAccountValidationError{
					field:  "InstallmentTerm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserAccountValidationError{
					field:  "InstallmentTerm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallmentTerm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserAccountValidationError{
				field:  "InstallmentTerm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserAccountValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserAccountValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserAccountValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.RepaymentBalance != nil {
		// no validation rules for RepaymentBalance
	}

	if len(errors) > 0 {
		return UserAccountMultiError(errors)
	}

	return nil
}

// UserAccountMultiError is an error wrapping multiple validation errors
// returned by UserAccount.ValidateAll() if the designated constraints aren't met.
type UserAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAccountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAccountMultiError) AllErrors() []error { return m }

// UserAccountValidationError is the validation error returned by
// UserAccount.Validate if the designated constraints aren't met.
type UserAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAccountValidationError) ErrorName() string { return "UserAccountValidationError" }

// Error satisfies the builtin error interface
func (e UserAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAccountValidationError{}

// Validate checks the field values on StmtAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StmtAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StmtAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StmtAccountMultiError, or
// nil if none found.
func (m *StmtAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *StmtAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZalopayId

	// no validation rules for AccountId

	// no validation rules for Status

	// no validation rules for PartnerCode

	// no validation rules for PartnerAccountName

	// no validation rules for PartnerAccountNumber

	if len(errors) > 0 {
		return StmtAccountMultiError(errors)
	}

	return nil
}

// StmtAccountMultiError is an error wrapping multiple validation errors
// returned by StmtAccount.ValidateAll() if the designated constraints aren't met.
type StmtAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StmtAccountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StmtAccountMultiError) AllErrors() []error { return m }

// StmtAccountValidationError is the validation error returned by
// StmtAccount.Validate if the designated constraints aren't met.
type StmtAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StmtAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StmtAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StmtAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StmtAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StmtAccountValidationError) ErrorName() string { return "StmtAccountValidationError" }

// Error satisfies the builtin error interface
func (e StmtAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStmtAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StmtAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StmtAccountValidationError{}

// Validate checks the field values on ListClientProposedServiceRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListClientProposedServiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientProposedServiceRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListClientProposedServiceRequestMultiError, or nil if none found.
func (m *ListClientProposedServiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientProposedServiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PartnerCode

	if len(errors) > 0 {
		return ListClientProposedServiceRequestMultiError(errors)
	}

	return nil
}

// ListClientProposedServiceRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListClientProposedServiceRequest.ValidateAll() if the designated
// constraints aren't met.
type ListClientProposedServiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientProposedServiceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientProposedServiceRequestMultiError) AllErrors() []error { return m }

// ListClientProposedServiceRequestValidationError is the validation error
// returned by ListClientProposedServiceRequest.Validate if the designated
// constraints aren't met.
type ListClientProposedServiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientProposedServiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientProposedServiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientProposedServiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientProposedServiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientProposedServiceRequestValidationError) ErrorName() string {
	return "ListClientProposedServiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientProposedServiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientProposedServiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientProposedServiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientProposedServiceRequestValidationError{}

// Validate checks the field values on ListClientProposedServiceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListClientProposedServiceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientProposedServiceResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListClientProposedServiceResponseMultiError, or nil if none found.
func (m *ListClientProposedServiceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientProposedServiceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientProposedServiceResponseValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientProposedServiceResponseValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientProposedServiceResponseValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClientProposedServiceResponseMultiError(errors)
	}

	return nil
}

// ListClientProposedServiceResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListClientProposedServiceResponse.ValidateAll() if the designated
// constraints aren't met.
type ListClientProposedServiceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientProposedServiceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientProposedServiceResponseMultiError) AllErrors() []error { return m }

// ListClientProposedServiceResponseValidationError is the validation error
// returned by ListClientProposedServiceResponse.Validate if the designated
// constraints aren't met.
type ListClientProposedServiceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientProposedServiceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientProposedServiceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientProposedServiceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientProposedServiceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientProposedServiceResponseValidationError) ErrorName() string {
	return "ListClientProposedServiceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientProposedServiceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientProposedServiceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientProposedServiceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientProposedServiceResponseValidationError{}

// Validate checks the field values on ContentImage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContentImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContentImage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContentImageMultiError, or
// nil if none found.
func (m *ContentImage) ValidateAll() error {
	return m.validate(true)
}

func (m *ContentImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IconUrl

	// no validation rules for CoverUrl

	// no validation rules for ThumbnailUrl

	if len(errors) > 0 {
		return ContentImageMultiError(errors)
	}

	return nil
}

// ContentImageMultiError is an error wrapping multiple validation errors
// returned by ContentImage.ValidateAll() if the designated constraints aren't met.
type ContentImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentImageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentImageMultiError) AllErrors() []error { return m }

// ContentImageValidationError is the validation error returned by
// ContentImage.Validate if the designated constraints aren't met.
type ContentImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentImageValidationError) ErrorName() string { return "ContentImageValidationError" }

// Error satisfies the builtin error interface
func (e ContentImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContentImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentImageValidationError{}

// Validate checks the field values on ContentInteraction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContentInteraction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContentInteraction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContentInteractionMultiError, or nil if none found.
func (m *ContentInteraction) ValidateAll() error {
	return m.validate(true)
}

func (m *ContentInteraction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZpaUrl

	// no validation rules for ZpiUrl

	if len(errors) > 0 {
		return ContentInteractionMultiError(errors)
	}

	return nil
}

// ContentInteractionMultiError is an error wrapping multiple validation errors
// returned by ContentInteraction.ValidateAll() if the designated constraints
// aren't met.
type ContentInteractionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentInteractionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentInteractionMultiError) AllErrors() []error { return m }

// ContentInteractionValidationError is the validation error returned by
// ContentInteraction.Validate if the designated constraints aren't met.
type ContentInteractionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentInteractionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentInteractionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentInteractionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentInteractionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentInteractionValidationError) ErrorName() string {
	return "ContentInteractionValidationError"
}

// Error satisfies the builtin error interface
func (e ContentInteractionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContentInteraction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentInteractionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentInteractionValidationError{}

// Validate checks the field values on ServiceProposed with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServiceProposed) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceProposed with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceProposedMultiError, or nil if none found.
func (m *ServiceProposed) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceProposed) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetContentImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceProposedValidationError{
					field:  "ContentImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceProposedValidationError{
					field:  "ContentImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContentImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceProposedValidationError{
				field:  "ContentImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInteraction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServiceProposedValidationError{
					field:  "Interaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServiceProposedValidationError{
					field:  "Interaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInteraction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServiceProposedValidationError{
				field:  "Interaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ServiceProposedMultiError(errors)
	}

	return nil
}

// ServiceProposedMultiError is an error wrapping multiple validation errors
// returned by ServiceProposed.ValidateAll() if the designated constraints
// aren't met.
type ServiceProposedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceProposedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceProposedMultiError) AllErrors() []error { return m }

// ServiceProposedValidationError is the validation error returned by
// ServiceProposed.Validate if the designated constraints aren't met.
type ServiceProposedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceProposedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceProposedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceProposedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceProposedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceProposedValidationError) ErrorName() string { return "ServiceProposedValidationError" }

// Error satisfies the builtin error interface
func (e ServiceProposedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceProposed.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceProposedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceProposedValidationError{}

// Validate checks the field values on GetAccountForPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountForPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountForPaymentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountForPaymentRequestMultiError, or nil if none found.
func (m *GetAccountForPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountForPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetAccountForPaymentRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := GetAccountForPaymentRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAccountForPaymentRequestMultiError(errors)
	}

	return nil
}

// GetAccountForPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by GetAccountForPaymentRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAccountForPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountForPaymentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountForPaymentRequestMultiError) AllErrors() []error { return m }

// GetAccountForPaymentRequestValidationError is the validation error returned
// by GetAccountForPaymentRequest.Validate if the designated constraints
// aren't met.
type GetAccountForPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountForPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountForPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountForPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountForPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountForPaymentRequestValidationError) ErrorName() string {
	return "GetAccountForPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountForPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountForPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountForPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountForPaymentRequestValidationError{}

// Validate checks the field values on GetAccountForPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountForPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountForPaymentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountForPaymentResponseMultiError, or nil if none found.
func (m *GetAccountForPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountForPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountForPaymentResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountForPaymentResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountForPaymentResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountForPaymentResponseMultiError(errors)
	}

	return nil
}

// GetAccountForPaymentResponseMultiError is an error wrapping multiple
// validation errors returned by GetAccountForPaymentResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAccountForPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountForPaymentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountForPaymentResponseMultiError) AllErrors() []error { return m }

// GetAccountForPaymentResponseValidationError is the validation error returned
// by GetAccountForPaymentResponse.Validate if the designated constraints
// aren't met.
type GetAccountForPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountForPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountForPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountForPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountForPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountForPaymentResponseValidationError) ErrorName() string {
	return "GetAccountForPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountForPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountForPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountForPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountForPaymentResponseValidationError{}

// Validate checks the field values on ListAccountForStmtSyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAccountForStmtSyncRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAccountForStmtSyncRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAccountForStmtSyncRequestMultiError, or nil if none found.
func (m *ListAccountForStmtSyncRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAccountForStmtSyncRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetItemsLimit() <= 0 {
		err := ListAccountForStmtSyncRequestValidationError{
			field:  "ItemsLimit",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAccountIdFrom() < 0 {
		err := ListAccountForStmtSyncRequestValidationError{
			field:  "AccountIdFrom",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStatementDate() == nil {
		err := ListAccountForStmtSyncRequestValidationError{
			field:  "StatementDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListAccountForStmtSyncRequestMultiError(errors)
	}

	return nil
}

// ListAccountForStmtSyncRequestMultiError is an error wrapping multiple
// validation errors returned by ListAccountForStmtSyncRequest.ValidateAll()
// if the designated constraints aren't met.
type ListAccountForStmtSyncRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAccountForStmtSyncRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAccountForStmtSyncRequestMultiError) AllErrors() []error { return m }

// ListAccountForStmtSyncRequestValidationError is the validation error
// returned by ListAccountForStmtSyncRequest.Validate if the designated
// constraints aren't met.
type ListAccountForStmtSyncRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAccountForStmtSyncRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAccountForStmtSyncRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAccountForStmtSyncRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAccountForStmtSyncRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAccountForStmtSyncRequestValidationError) ErrorName() string {
	return "ListAccountForStmtSyncRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAccountForStmtSyncRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAccountForStmtSyncRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAccountForStmtSyncRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAccountForStmtSyncRequestValidationError{}

// Validate checks the field values on ListAccountForStmtSyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAccountForStmtSyncResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAccountForStmtSyncResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAccountForStmtSyncResponseMultiError, or nil if none found.
func (m *ListAccountForStmtSyncResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAccountForStmtSyncResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAccountForStmtSyncResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAccountForStmtSyncResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAccountForStmtSyncResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAccountForStmtSyncResponseMultiError(errors)
	}

	return nil
}

// ListAccountForStmtSyncResponseMultiError is an error wrapping multiple
// validation errors returned by ListAccountForStmtSyncResponse.ValidateAll()
// if the designated constraints aren't met.
type ListAccountForStmtSyncResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAccountForStmtSyncResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAccountForStmtSyncResponseMultiError) AllErrors() []error { return m }

// ListAccountForStmtSyncResponseValidationError is the validation error
// returned by ListAccountForStmtSyncResponse.Validate if the designated
// constraints aren't met.
type ListAccountForStmtSyncResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAccountForStmtSyncResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAccountForStmtSyncResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAccountForStmtSyncResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAccountForStmtSyncResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAccountForStmtSyncResponseValidationError) ErrorName() string {
	return "ListAccountForStmtSyncResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAccountForStmtSyncResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAccountForStmtSyncResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAccountForStmtSyncResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAccountForStmtSyncResponseValidationError{}

// Validate checks the field values on Pagination with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Pagination) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Pagination with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaginationMultiError, or
// nil if none found.
func (m *Pagination) ValidateAll() error {
	return m.validate(true)
}

func (m *Pagination) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Limit

	if m.Offset != nil {
		// no validation rules for Offset
	}

	if m.Cursor != nil {
		// no validation rules for Cursor
	}

	if m.Direction != nil {
		// no validation rules for Direction
	}

	if len(errors) > 0 {
		return PaginationMultiError(errors)
	}

	return nil
}

// PaginationMultiError is an error wrapping multiple validation errors
// returned by Pagination.ValidateAll() if the designated constraints aren't met.
type PaginationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaginationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaginationMultiError) AllErrors() []error { return m }

// PaginationValidationError is the validation error returned by
// Pagination.Validate if the designated constraints aren't met.
type PaginationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaginationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaginationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaginationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaginationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaginationValidationError) ErrorName() string { return "PaginationValidationError" }

// Error satisfies the builtin error interface
func (e PaginationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPagination.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaginationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaginationValidationError{}

// Validate checks the field values on InstallmentTerm with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InstallmentTerm) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallmentTerm with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstallmentTermMultiError, or nil if none found.
func (m *InstallmentTerm) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallmentTerm) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeeExplanation

	// no validation rules for StmtDueDateText

	// no validation rules for StmtIncurDateText

	if len(errors) > 0 {
		return InstallmentTermMultiError(errors)
	}

	return nil
}

// InstallmentTermMultiError is an error wrapping multiple validation errors
// returned by InstallmentTerm.ValidateAll() if the designated constraints
// aren't met.
type InstallmentTermMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallmentTermMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallmentTermMultiError) AllErrors() []error { return m }

// InstallmentTermValidationError is the validation error returned by
// InstallmentTerm.Validate if the designated constraints aren't met.
type InstallmentTermValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallmentTermValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallmentTermValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallmentTermValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallmentTermValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallmentTermValidationError) ErrorName() string { return "InstallmentTermValidationError" }

// Error satisfies the builtin error interface
func (e InstallmentTermValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallmentTerm.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallmentTermValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallmentTermValidationError{}
