// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: account_service/v1/account_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAccountGetClientAccount = "/account_service.v1.Account/GetClientAccount"
const OperationAccountListClientProposedService = "/account_service.v1.Account/ListClientProposedService"

type AccountHTTPServer interface {
	GetClientAccount(context.Context, *GetClientAccountRequest) (*GetClientAccountResponse, error)
	ListClientProposedService(context.Context, *ListClientProposedServiceRequest) (*ListClientProposedServiceResponse, error)
}

func RegisterAccountHTTPServer(s *http.Server, srv AccountHTTPServer) {
	r := s.Route("/")
	r.GET("/account/v1/account", _Account_GetClientAccount0_HTTP_Handler(srv))
	r.GET("/account/v1/content/proposed-services", _Account_ListClientProposedService0_HTTP_Handler(srv))
}

func _Account_GetClientAccount0_HTTP_Handler(srv AccountHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientAccountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAccountGetClientAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientAccount(ctx, req.(*GetClientAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientAccountResponse)
		return ctx.Result(200, reply)
	}
}

func _Account_ListClientProposedService0_HTTP_Handler(srv AccountHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClientProposedServiceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAccountListClientProposedService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClientProposedService(ctx, req.(*ListClientProposedServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClientProposedServiceResponse)
		return ctx.Result(200, reply)
	}
}

type AccountHTTPClient interface {
	GetClientAccount(ctx context.Context, req *GetClientAccountRequest, opts ...http.CallOption) (rsp *GetClientAccountResponse, err error)
	ListClientProposedService(ctx context.Context, req *ListClientProposedServiceRequest, opts ...http.CallOption) (rsp *ListClientProposedServiceResponse, err error)
}

type AccountHTTPClientImpl struct {
	cc *http.Client
}

func NewAccountHTTPClient(client *http.Client) AccountHTTPClient {
	return &AccountHTTPClientImpl{client}
}

func (c *AccountHTTPClientImpl) GetClientAccount(ctx context.Context, in *GetClientAccountRequest, opts ...http.CallOption) (*GetClientAccountResponse, error) {
	var out GetClientAccountResponse
	pattern := "/account/v1/account"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAccountGetClientAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AccountHTTPClientImpl) ListClientProposedService(ctx context.Context, in *ListClientProposedServiceRequest, opts ...http.CallOption) (*ListClientProposedServiceResponse, error) {
	var out ListClientProposedServiceResponse
	pattern := "/account/v1/content/proposed-services"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAccountListClientProposedService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
