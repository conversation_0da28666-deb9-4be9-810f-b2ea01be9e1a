// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: account_service/v1/account_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Account_CreateAccount_FullMethodName             = "/account_service.v1.Account/CreateAccount"
	Account_GetClientAccount_FullMethodName          = "/account_service.v1.Account/GetClientAccount"
	Account_ListClientProposedService_FullMethodName = "/account_service.v1.Account/ListClientProposedService"
	Account_GetAccount_FullMethodName                = "/account_service.v1.Account/GetAccount"
	Account_GetAccountForPayment_FullMethodName      = "/account_service.v1.Account/GetAccountForPayment"
	Account_ListAccountForStmtSync_FullMethodName    = "/account_service.v1.Account/ListAccountForStmtSync"
)

// AccountClient is the client API for Account service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountClient interface {
	CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error)
	GetClientAccount(ctx context.Context, in *GetClientAccountRequest, opts ...grpc.CallOption) (*GetClientAccountResponse, error)
	ListClientProposedService(ctx context.Context, in *ListClientProposedServiceRequest, opts ...grpc.CallOption) (*ListClientProposedServiceResponse, error)
	GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error)
	GetAccountForPayment(ctx context.Context, in *GetAccountForPaymentRequest, opts ...grpc.CallOption) (*GetAccountForPaymentResponse, error)
	ListAccountForStmtSync(ctx context.Context, in *ListAccountForStmtSyncRequest, opts ...grpc.CallOption) (*ListAccountForStmtSyncResponse, error)
}

type accountClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountClient(cc grpc.ClientConnInterface) AccountClient {
	return &accountClient{cc}
}

func (c *accountClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountResponse)
	err := c.cc.Invoke(ctx, Account_CreateAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) GetClientAccount(ctx context.Context, in *GetClientAccountRequest, opts ...grpc.CallOption) (*GetClientAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientAccountResponse)
	err := c.cc.Invoke(ctx, Account_GetClientAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) ListClientProposedService(ctx context.Context, in *ListClientProposedServiceRequest, opts ...grpc.CallOption) (*ListClientProposedServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClientProposedServiceResponse)
	err := c.cc.Invoke(ctx, Account_ListClientProposedService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountResponse)
	err := c.cc.Invoke(ctx, Account_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) GetAccountForPayment(ctx context.Context, in *GetAccountForPaymentRequest, opts ...grpc.CallOption) (*GetAccountForPaymentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountForPaymentResponse)
	err := c.cc.Invoke(ctx, Account_GetAccountForPayment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) ListAccountForStmtSync(ctx context.Context, in *ListAccountForStmtSyncRequest, opts ...grpc.CallOption) (*ListAccountForStmtSyncResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAccountForStmtSyncResponse)
	err := c.cc.Invoke(ctx, Account_ListAccountForStmtSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServer is the server API for Account service.
// All implementations must embed UnimplementedAccountServer
// for forward compatibility.
type AccountServer interface {
	CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error)
	GetClientAccount(context.Context, *GetClientAccountRequest) (*GetClientAccountResponse, error)
	ListClientProposedService(context.Context, *ListClientProposedServiceRequest) (*ListClientProposedServiceResponse, error)
	GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error)
	GetAccountForPayment(context.Context, *GetAccountForPaymentRequest) (*GetAccountForPaymentResponse, error)
	ListAccountForStmtSync(context.Context, *ListAccountForStmtSyncRequest) (*ListAccountForStmtSyncResponse, error)
	mustEmbedUnimplementedAccountServer()
}

// UnimplementedAccountServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccountServer struct{}

func (UnimplementedAccountServer) CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedAccountServer) GetClientAccount(context.Context, *GetClientAccountRequest) (*GetClientAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientAccount not implemented")
}
func (UnimplementedAccountServer) ListClientProposedService(context.Context, *ListClientProposedServiceRequest) (*ListClientProposedServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClientProposedService not implemented")
}
func (UnimplementedAccountServer) GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedAccountServer) GetAccountForPayment(context.Context, *GetAccountForPaymentRequest) (*GetAccountForPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountForPayment not implemented")
}
func (UnimplementedAccountServer) ListAccountForStmtSync(context.Context, *ListAccountForStmtSyncRequest) (*ListAccountForStmtSyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccountForStmtSync not implemented")
}
func (UnimplementedAccountServer) mustEmbedUnimplementedAccountServer() {}
func (UnimplementedAccountServer) testEmbeddedByValue()                 {}

// UnsafeAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServer will
// result in compilation errors.
type UnsafeAccountServer interface {
	mustEmbedUnimplementedAccountServer()
}

func RegisterAccountServer(s grpc.ServiceRegistrar, srv AccountServer) {
	// If the following call pancis, it indicates UnimplementedAccountServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Account_ServiceDesc, srv)
}

func _Account_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_CreateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).CreateAccount(ctx, req.(*CreateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_GetClientAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).GetClientAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_GetClientAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).GetClientAccount(ctx, req.(*GetClientAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ListClientProposedService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClientProposedServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).ListClientProposedService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_ListClientProposedService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).ListClientProposedService(ctx, req.(*ListClientProposedServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).GetAccount(ctx, req.(*GetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_GetAccountForPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountForPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).GetAccountForPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_GetAccountForPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).GetAccountForPayment(ctx, req.(*GetAccountForPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ListAccountForStmtSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountForStmtSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).ListAccountForStmtSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_ListAccountForStmtSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).ListAccountForStmtSync(ctx, req.(*ListAccountForStmtSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Account_ServiceDesc is the grpc.ServiceDesc for Account service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Account_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "account_service.v1.Account",
	HandlerType: (*AccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAccount",
			Handler:    _Account_CreateAccount_Handler,
		},
		{
			MethodName: "GetClientAccount",
			Handler:    _Account_GetClientAccount_Handler,
		},
		{
			MethodName: "ListClientProposedService",
			Handler:    _Account_ListClientProposedService_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _Account_GetAccount_Handler,
		},
		{
			MethodName: "GetAccountForPayment",
			Handler:    _Account_GetAccountForPayment_Handler,
		},
		{
			MethodName: "ListAccountForStmtSync",
			Handler:    _Account_ListAccountForStmtSync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "account_service/v1/account_service.proto",
}
