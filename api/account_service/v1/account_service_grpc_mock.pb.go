// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: account_service/v1/account_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAccountClient is a mock of AccountClient interface.
type MockAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockAccountClientMockRecorder
}

// MockAccountClientMockRecorder is the mock recorder for MockAccountClient.
type MockAccountClientMockRecorder struct {
	mock *MockAccountClient
}

// NewMockAccountClient creates a new mock instance.
func NewMockAccountClient(ctrl *gomock.Controller) *MockAccountClient {
	mock := &MockAccountClient{ctrl: ctrl}
	mock.recorder = &MockAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountClient) EXPECT() *MockAccountClientMockRecorder {
	return m.recorder
}

// CreateAccount mocks base method.
func (m *MockAccountClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAccount", varargs...)
	ret0, _ := ret[0].(*CreateAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAccount indicates an expected call of CreateAccount.
func (mr *MockAccountClientMockRecorder) CreateAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccount", reflect.TypeOf((*MockAccountClient)(nil).CreateAccount), varargs...)
}

// GetAccount mocks base method.
func (m *MockAccountClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccount", varargs...)
	ret0, _ := ret[0].(*GetAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockAccountClientMockRecorder) GetAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockAccountClient)(nil).GetAccount), varargs...)
}

// GetAccountForPayment mocks base method.
func (m *MockAccountClient) GetAccountForPayment(ctx context.Context, in *GetAccountForPaymentRequest, opts ...grpc.CallOption) (*GetAccountForPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountForPayment", varargs...)
	ret0, _ := ret[0].(*GetAccountForPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountForPayment indicates an expected call of GetAccountForPayment.
func (mr *MockAccountClientMockRecorder) GetAccountForPayment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountForPayment", reflect.TypeOf((*MockAccountClient)(nil).GetAccountForPayment), varargs...)
}

// GetClientAccount mocks base method.
func (m *MockAccountClient) GetClientAccount(ctx context.Context, in *GetClientAccountRequest, opts ...grpc.CallOption) (*GetClientAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientAccount", varargs...)
	ret0, _ := ret[0].(*GetClientAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientAccount indicates an expected call of GetClientAccount.
func (mr *MockAccountClientMockRecorder) GetClientAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientAccount", reflect.TypeOf((*MockAccountClient)(nil).GetClientAccount), varargs...)
}

// ListAccountForStmtSync mocks base method.
func (m *MockAccountClient) ListAccountForStmtSync(ctx context.Context, in *ListAccountForStmtSyncRequest, opts ...grpc.CallOption) (*ListAccountForStmtSyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAccountForStmtSync", varargs...)
	ret0, _ := ret[0].(*ListAccountForStmtSyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountForStmtSync indicates an expected call of ListAccountForStmtSync.
func (mr *MockAccountClientMockRecorder) ListAccountForStmtSync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountForStmtSync", reflect.TypeOf((*MockAccountClient)(nil).ListAccountForStmtSync), varargs...)
}

// ListClientProposedService mocks base method.
func (m *MockAccountClient) ListClientProposedService(ctx context.Context, in *ListClientProposedServiceRequest, opts ...grpc.CallOption) (*ListClientProposedServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListClientProposedService", varargs...)
	ret0, _ := ret[0].(*ListClientProposedServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientProposedService indicates an expected call of ListClientProposedService.
func (mr *MockAccountClientMockRecorder) ListClientProposedService(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientProposedService", reflect.TypeOf((*MockAccountClient)(nil).ListClientProposedService), varargs...)
}

// MockAccountServer is a mock of AccountServer interface.
type MockAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountServerMockRecorder
}

// MockAccountServerMockRecorder is the mock recorder for MockAccountServer.
type MockAccountServerMockRecorder struct {
	mock *MockAccountServer
}

// NewMockAccountServer creates a new mock instance.
func NewMockAccountServer(ctrl *gomock.Controller) *MockAccountServer {
	mock := &MockAccountServer{ctrl: ctrl}
	mock.recorder = &MockAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountServer) EXPECT() *MockAccountServerMockRecorder {
	return m.recorder
}

// CreateAccount mocks base method.
func (m *MockAccountServer) CreateAccount(ctx context.Context, in *CreateAccountRequest) (*CreateAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAccount", ctx, in)
	ret0, _ := ret[0].(*CreateAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAccount indicates an expected call of CreateAccount.
func (mr *MockAccountServerMockRecorder) CreateAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccount", reflect.TypeOf((*MockAccountServer)(nil).CreateAccount), ctx, in)
}

// GetAccount mocks base method.
func (m *MockAccountServer) GetAccount(ctx context.Context, in *GetAccountRequest) (*GetAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccount", ctx, in)
	ret0, _ := ret[0].(*GetAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockAccountServerMockRecorder) GetAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockAccountServer)(nil).GetAccount), ctx, in)
}

// GetAccountForPayment mocks base method.
func (m *MockAccountServer) GetAccountForPayment(ctx context.Context, in *GetAccountForPaymentRequest) (*GetAccountForPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountForPayment", ctx, in)
	ret0, _ := ret[0].(*GetAccountForPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountForPayment indicates an expected call of GetAccountForPayment.
func (mr *MockAccountServerMockRecorder) GetAccountForPayment(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountForPayment", reflect.TypeOf((*MockAccountServer)(nil).GetAccountForPayment), ctx, in)
}

// GetClientAccount mocks base method.
func (m *MockAccountServer) GetClientAccount(ctx context.Context, in *GetClientAccountRequest) (*GetClientAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientAccount", ctx, in)
	ret0, _ := ret[0].(*GetClientAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientAccount indicates an expected call of GetClientAccount.
func (mr *MockAccountServerMockRecorder) GetClientAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientAccount", reflect.TypeOf((*MockAccountServer)(nil).GetClientAccount), ctx, in)
}

// ListAccountForStmtSync mocks base method.
func (m *MockAccountServer) ListAccountForStmtSync(ctx context.Context, in *ListAccountForStmtSyncRequest) (*ListAccountForStmtSyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountForStmtSync", ctx, in)
	ret0, _ := ret[0].(*ListAccountForStmtSyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountForStmtSync indicates an expected call of ListAccountForStmtSync.
func (mr *MockAccountServerMockRecorder) ListAccountForStmtSync(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountForStmtSync", reflect.TypeOf((*MockAccountServer)(nil).ListAccountForStmtSync), ctx, in)
}

// ListClientProposedService mocks base method.
func (m *MockAccountServer) ListClientProposedService(ctx context.Context, in *ListClientProposedServiceRequest) (*ListClientProposedServiceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClientProposedService", ctx, in)
	ret0, _ := ret[0].(*ListClientProposedServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientProposedService indicates an expected call of ListClientProposedService.
func (mr *MockAccountServerMockRecorder) ListClientProposedService(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientProposedService", reflect.TypeOf((*MockAccountServer)(nil).ListClientProposedService), ctx, in)
}
