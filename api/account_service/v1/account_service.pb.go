// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: account_service/v1/account_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Account status
type Status int32

const (
	Status_UNSPECIFIED Status = 0
	Status_ACTIVE      Status = 1
	Status_INACTIVE    Status = 2
	Status_BLOCKED     Status = 3
	Status_CLOSED      Status = 4
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "BLOCKED",
		4: "CLOSED",
	}
	Status_value = map[string]int32{
		"UNSPECIFIED": 0,
		"ACTIVE":      1,
		"INACTIVE":    2,
		"BLOCKED":     3,
		"CLOSED":      4,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_account_service_v1_account_service_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_account_service_v1_account_service_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{0}
}

type CreateAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,3,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAccountRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *CreateAccountRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *CreateAccountRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type CreateAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     int64                  `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountResponse) Reset() {
	*x = CreateAccountResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResponse) ProtoMessage() {}

func (x *CreateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAccountResponse) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

type ListAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerIds    []string               `protobuf:"bytes,1,rep,name=partner_ids,json=partnerIds,proto3" json:"partner_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountRequest) Reset() {
	*x = ListAccountRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountRequest) ProtoMessage() {}

func (x *ListAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountRequest.ProtoReflect.Descriptor instead.
func (*ListAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAccountRequest) GetPartnerIds() []string {
	if x != nil {
		return x.PartnerIds
	}
	return nil
}

type ListAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accounts      []*UserAccount         `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountResponse) Reset() {
	*x = ListAccountResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountResponse) ProtoMessage() {}

func (x *ListAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountResponse.ProtoReflect.Descriptor instead.
func (*ListAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAccountResponse) GetAccounts() []*UserAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type GetAccountRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// Types that are valid to be assigned to QueryBy:
	//
	//	*GetAccountRequest_AccountId
	//	*GetAccountRequest_PartnerCode
	QueryBy       isGetAccountRequest_QueryBy `protobuf_oneof:"query_by"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountRequest) Reset() {
	*x = GetAccountRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRequest) ProtoMessage() {}

func (x *GetAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRequest.ProtoReflect.Descriptor instead.
func (*GetAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAccountRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetAccountRequest) GetQueryBy() isGetAccountRequest_QueryBy {
	if x != nil {
		return x.QueryBy
	}
	return nil
}

func (x *GetAccountRequest) GetAccountId() int64 {
	if x != nil {
		if x, ok := x.QueryBy.(*GetAccountRequest_AccountId); ok {
			return x.AccountId
		}
	}
	return 0
}

func (x *GetAccountRequest) GetPartnerCode() string {
	if x != nil {
		if x, ok := x.QueryBy.(*GetAccountRequest_PartnerCode); ok {
			return x.PartnerCode
		}
	}
	return ""
}

type isGetAccountRequest_QueryBy interface {
	isGetAccountRequest_QueryBy()
}

type GetAccountRequest_AccountId struct {
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetAccountRequest_PartnerCode struct {
	PartnerCode string `protobuf:"bytes,3,opt,name=partner_code,json=partnerCode,proto3,oneof"`
}

func (*GetAccountRequest_AccountId) isGetAccountRequest_QueryBy() {}

func (*GetAccountRequest_PartnerCode) isGetAccountRequest_QueryBy() {}

type GetAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *UserAccount           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountResponse) Reset() {
	*x = GetAccountResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResponse) ProtoMessage() {}

func (x *GetAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAccountResponse) GetAccount() *UserAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetClientAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to QueryBy:
	//
	//	*GetClientAccountRequest_AccountId
	//	*GetClientAccountRequest_PartnerCode
	QueryBy       isGetClientAccountRequest_QueryBy `protobuf_oneof:"query_by"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientAccountRequest) Reset() {
	*x = GetClientAccountRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientAccountRequest) ProtoMessage() {}

func (x *GetClientAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientAccountRequest.ProtoReflect.Descriptor instead.
func (*GetClientAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetClientAccountRequest) GetQueryBy() isGetClientAccountRequest_QueryBy {
	if x != nil {
		return x.QueryBy
	}
	return nil
}

func (x *GetClientAccountRequest) GetAccountId() int64 {
	if x != nil {
		if x, ok := x.QueryBy.(*GetClientAccountRequest_AccountId); ok {
			return x.AccountId
		}
	}
	return 0
}

func (x *GetClientAccountRequest) GetPartnerCode() string {
	if x != nil {
		if x, ok := x.QueryBy.(*GetClientAccountRequest_PartnerCode); ok {
			return x.PartnerCode
		}
	}
	return ""
}

type isGetClientAccountRequest_QueryBy interface {
	isGetClientAccountRequest_QueryBy()
}

type GetClientAccountRequest_AccountId struct {
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,proto3,oneof"`
}

type GetClientAccountRequest_PartnerCode struct {
	PartnerCode string `protobuf:"bytes,2,opt,name=partner_code,proto3,oneof"`
}

func (*GetClientAccountRequest_AccountId) isGetClientAccountRequest_QueryBy() {}

func (*GetClientAccountRequest_PartnerCode) isGetClientAccountRequest_QueryBy() {}

type GetClientAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *UserAccount           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientAccountResponse) Reset() {
	*x = GetClientAccountResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientAccountResponse) ProtoMessage() {}

func (x *GetClientAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientAccountResponse.ProtoReflect.Descriptor instead.
func (*GetClientAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetClientAccountResponse) GetAccount() *UserAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

type UserAccount struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	AccountId            int64                  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	Status               Status                 `protobuf:"varint,2,opt,name=status,proto3,enum=account_service.v1.Status" json:"status,omitempty"`
	Source               string                 `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	PartnerCode          string                 `protobuf:"bytes,4,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	PartnerAccountName   string                 `protobuf:"bytes,5,opt,name=partner_account_name,proto3" json:"partner_account_name,omitempty"`
	PartnerAccountNumber string                 `protobuf:"bytes,6,opt,name=partner_account_number,proto3" json:"partner_account_number,omitempty"`
	InstallmentLimit     int64                  `protobuf:"varint,7,opt,name=installment_limit,proto3" json:"installment_limit,omitempty"`
	InstallmentBalance   int64                  `protobuf:"varint,8,opt,name=installment_balance,proto3" json:"installment_balance,omitempty"`
	InstallmentTerm      *InstallmentTerm       `protobuf:"bytes,9,opt,name=installment_term,proto3" json:"installment_term,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,proto3" json:"created_at,omitempty"`
	RepaymentBalance     *int64                 `protobuf:"varint,11,opt,name=repayment_balance,proto3,oneof" json:"repayment_balance,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UserAccount) Reset() {
	*x = UserAccount{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAccount) ProtoMessage() {}

func (x *UserAccount) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAccount.ProtoReflect.Descriptor instead.
func (*UserAccount) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{8}
}

func (x *UserAccount) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *UserAccount) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_UNSPECIFIED
}

func (x *UserAccount) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *UserAccount) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *UserAccount) GetPartnerAccountName() string {
	if x != nil {
		return x.PartnerAccountName
	}
	return ""
}

func (x *UserAccount) GetPartnerAccountNumber() string {
	if x != nil {
		return x.PartnerAccountNumber
	}
	return ""
}

func (x *UserAccount) GetInstallmentLimit() int64 {
	if x != nil {
		return x.InstallmentLimit
	}
	return 0
}

func (x *UserAccount) GetInstallmentBalance() int64 {
	if x != nil {
		return x.InstallmentBalance
	}
	return 0
}

func (x *UserAccount) GetInstallmentTerm() *InstallmentTerm {
	if x != nil {
		return x.InstallmentTerm
	}
	return nil
}

func (x *UserAccount) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UserAccount) GetRepaymentBalance() int64 {
	if x != nil && x.RepaymentBalance != nil {
		return *x.RepaymentBalance
	}
	return 0
}

// StmtAccount represents a statement account and only use for statement service
type StmtAccount struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId            int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	AccountId            int64                  `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	Status               Status                 `protobuf:"varint,3,opt,name=status,proto3,enum=account_service.v1.Status" json:"status,omitempty"`
	PartnerCode          string                 `protobuf:"bytes,4,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	PartnerAccountName   string                 `protobuf:"bytes,5,opt,name=partner_account_name,json=partnerAccountName,proto3" json:"partner_account_name,omitempty"`
	PartnerAccountNumber string                 `protobuf:"bytes,6,opt,name=partner_account_number,json=partnerAccountNumber,proto3" json:"partner_account_number,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *StmtAccount) Reset() {
	*x = StmtAccount{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StmtAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StmtAccount) ProtoMessage() {}

func (x *StmtAccount) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StmtAccount.ProtoReflect.Descriptor instead.
func (*StmtAccount) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{9}
}

func (x *StmtAccount) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *StmtAccount) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StmtAccount) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_UNSPECIFIED
}

func (x *StmtAccount) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *StmtAccount) GetPartnerAccountName() string {
	if x != nil {
		return x.PartnerAccountName
	}
	return ""
}

func (x *StmtAccount) GetPartnerAccountNumber() string {
	if x != nil {
		return x.PartnerAccountNumber
	}
	return ""
}

type ListClientProposedServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientProposedServiceRequest) Reset() {
	*x = ListClientProposedServiceRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientProposedServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientProposedServiceRequest) ProtoMessage() {}

func (x *ListClientProposedServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientProposedServiceRequest.ProtoReflect.Descriptor instead.
func (*ListClientProposedServiceRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListClientProposedServiceRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type ListClientProposedServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*ServiceProposed     `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientProposedServiceResponse) Reset() {
	*x = ListClientProposedServiceResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientProposedServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientProposedServiceResponse) ProtoMessage() {}

func (x *ListClientProposedServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientProposedServiceResponse.ProtoReflect.Descriptor instead.
func (*ListClientProposedServiceResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListClientProposedServiceResponse) GetServices() []*ServiceProposed {
	if x != nil {
		return x.Services
	}
	return nil
}

type ContentImage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IconUrl       string                 `protobuf:"bytes,1,opt,name=icon_url,proto3" json:"icon_url,omitempty"`
	CoverUrl      string                 `protobuf:"bytes,2,opt,name=cover_url,proto3" json:"cover_url,omitempty"`
	ThumbnailUrl  string                 `protobuf:"bytes,3,opt,name=thumbnail_url,proto3" json:"thumbnail_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContentImage) Reset() {
	*x = ContentImage{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContentImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentImage) ProtoMessage() {}

func (x *ContentImage) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentImage.ProtoReflect.Descriptor instead.
func (*ContentImage) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{12}
}

func (x *ContentImage) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ContentImage) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *ContentImage) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

type ContentInteraction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// string type = 1;
	ZpaUrl        string `protobuf:"bytes,2,opt,name=zpa_url,proto3" json:"zpa_url,omitempty"`
	ZpiUrl        string `protobuf:"bytes,3,opt,name=zpi_url,proto3" json:"zpi_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContentInteraction) Reset() {
	*x = ContentInteraction{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContentInteraction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentInteraction) ProtoMessage() {}

func (x *ContentInteraction) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentInteraction.ProtoReflect.Descriptor instead.
func (*ContentInteraction) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{13}
}

func (x *ContentInteraction) GetZpaUrl() string {
	if x != nil {
		return x.ZpaUrl
	}
	return ""
}

func (x *ContentInteraction) GetZpiUrl() string {
	if x != nil {
		return x.ZpiUrl
	}
	return ""
}

type ServiceProposed struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ContentImage  *ContentImage          `protobuf:"bytes,3,opt,name=content_image,proto3" json:"content_image,omitempty"`
	Interaction   *ContentInteraction    `protobuf:"bytes,4,opt,name=interaction,proto3" json:"interaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceProposed) Reset() {
	*x = ServiceProposed{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceProposed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceProposed) ProtoMessage() {}

func (x *ServiceProposed) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceProposed.ProtoReflect.Descriptor instead.
func (*ServiceProposed) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{14}
}

func (x *ServiceProposed) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ServiceProposed) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceProposed) GetContentImage() *ContentImage {
	if x != nil {
		return x.ContentImage
	}
	return nil
}

func (x *ServiceProposed) GetInteraction() *ContentInteraction {
	if x != nil {
		return x.Interaction
	}
	return nil
}

type GetAccountForPaymentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,proto3" json:"zalopay_id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,2,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountForPaymentRequest) Reset() {
	*x = GetAccountForPaymentRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountForPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountForPaymentRequest) ProtoMessage() {}

func (x *GetAccountForPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountForPaymentRequest.ProtoReflect.Descriptor instead.
func (*GetAccountForPaymentRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetAccountForPaymentRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetAccountForPaymentRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type GetAccountForPaymentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *UserAccount           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountForPaymentResponse) Reset() {
	*x = GetAccountForPaymentResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountForPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountForPaymentResponse) ProtoMessage() {}

func (x *GetAccountForPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountForPaymentResponse.ProtoReflect.Descriptor instead.
func (*GetAccountForPaymentResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetAccountForPaymentResponse) GetAccount() *UserAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

type ListAccountForStmtSyncRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemsLimit    int32                  `protobuf:"varint,1,opt,name=items_limit,json=itemsLimit,proto3" json:"items_limit,omitempty"`
	AccountIdFrom int64                  `protobuf:"varint,2,opt,name=account_id_from,json=accountIdFrom,proto3" json:"account_id_from,omitempty"`
	StatementDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=statement_date,json=statementDate,proto3" json:"statement_date,omitempty"`
	PartnerCodes  []string               `protobuf:"bytes,4,rep,name=partner_codes,json=partnerCodes,proto3" json:"partner_codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountForStmtSyncRequest) Reset() {
	*x = ListAccountForStmtSyncRequest{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountForStmtSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountForStmtSyncRequest) ProtoMessage() {}

func (x *ListAccountForStmtSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountForStmtSyncRequest.ProtoReflect.Descriptor instead.
func (*ListAccountForStmtSyncRequest) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListAccountForStmtSyncRequest) GetItemsLimit() int32 {
	if x != nil {
		return x.ItemsLimit
	}
	return 0
}

func (x *ListAccountForStmtSyncRequest) GetAccountIdFrom() int64 {
	if x != nil {
		return x.AccountIdFrom
	}
	return 0
}

func (x *ListAccountForStmtSyncRequest) GetStatementDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StatementDate
	}
	return nil
}

func (x *ListAccountForStmtSyncRequest) GetPartnerCodes() []string {
	if x != nil {
		return x.PartnerCodes
	}
	return nil
}

type ListAccountForStmtSyncResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accounts      []*StmtAccount         `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAccountForStmtSyncResponse) Reset() {
	*x = ListAccountForStmtSyncResponse{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAccountForStmtSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountForStmtSyncResponse) ProtoMessage() {}

func (x *ListAccountForStmtSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountForStmtSyncResponse.ProtoReflect.Descriptor instead.
func (*ListAccountForStmtSyncResponse) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{18}
}

func (x *ListAccountForStmtSyncResponse) GetAccounts() []*StmtAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset        *int32                 `protobuf:"varint,2,opt,name=offset,proto3,oneof" json:"offset,omitempty"`
	Cursor        *string                `protobuf:"bytes,3,opt,name=cursor,proto3,oneof" json:"cursor,omitempty"`
	Direction     *int32                 `protobuf:"varint,4,opt,name=direction,proto3,oneof" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{19}
}

func (x *Pagination) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *Pagination) GetOffset() int32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *Pagination) GetCursor() string {
	if x != nil && x.Cursor != nil {
		return *x.Cursor
	}
	return ""
}

func (x *Pagination) GetDirection() int32 {
	if x != nil && x.Direction != nil {
		return *x.Direction
	}
	return 0
}

type InstallmentTerm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Installment fee explanation
	FeeExplanation string `protobuf:"bytes,1,opt,name=fee_explanation,proto3" json:"fee_explanation,omitempty"`
	// Statement due date description
	StmtDueDateText string `protobuf:"bytes,2,opt,name=stmt_due_date_text,proto3" json:"stmt_due_date_text,omitempty"`
	// Statement incurred date description
	StmtIncurDateText string `protobuf:"bytes,3,opt,name=stmt_incur_date_text,proto3" json:"stmt_incur_date_text,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InstallmentTerm) Reset() {
	*x = InstallmentTerm{}
	mi := &file_account_service_v1_account_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallmentTerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentTerm) ProtoMessage() {}

func (x *InstallmentTerm) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_v1_account_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentTerm.ProtoReflect.Descriptor instead.
func (*InstallmentTerm) Descriptor() ([]byte, []int) {
	return file_account_service_v1_account_service_proto_rawDescGZIP(), []int{20}
}

func (x *InstallmentTerm) GetFeeExplanation() string {
	if x != nil {
		return x.FeeExplanation
	}
	return ""
}

func (x *InstallmentTerm) GetStmtDueDateText() string {
	if x != nil {
		return x.StmtDueDateText
	}
	return ""
}

func (x *InstallmentTerm) GetStmtIncurDateText() string {
	if x != nil {
		return x.StmtIncurDateText
	}
	return ""
}

var File_account_service_v1_account_service_proto protoreflect.FileDescriptor

const file_account_service_v1_account_service_proto_rawDesc = "" +
	"\n" +
	"(account_service/v1/account_service.proto\x12\x12account_service.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17validate/validate.proto\"\x98\x01\n" +
	"\x14CreateAccountRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId\x12*\n" +
	"\fpartner_code\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\"6\n" +
	"\x15CreateAccountResponse\x12\x1d\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03R\taccountId\"5\n" +
	"\x12ListAccountRequest\x12\x1f\n" +
	"\vpartner_ids\x18\x01 \x03(\tR\n" +
	"partnerIds\"R\n" +
	"\x13ListAccountResponse\x12;\n" +
	"\baccounts\x18\x01 \x03(\v2\x1f.account_service.v1.UserAccountR\baccounts\"\x9f\x01\n" +
	"\x11GetAccountRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12(\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\taccountId\x12,\n" +
	"\fpartner_code\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01H\x00R\vpartnerCodeB\n" +
	"\n" +
	"\bquery_by\"O\n" +
	"\x12GetAccountResponse\x129\n" +
	"\aaccount\x18\x01 \x01(\v2\x1f.account_service.v1.UserAccountR\aaccount\"\x7f\n" +
	"\x17GetClientAccountRequest\x12)\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\n" +
	"account_id\x12-\n" +
	"\fpartner_code\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01H\x00R\fpartner_codeB\n" +
	"\n" +
	"\bquery_by\"U\n" +
	"\x18GetClientAccountResponse\x129\n" +
	"\aaccount\x18\x01 \x01(\v2\x1f.account_service.v1.UserAccountR\aaccount\"\xbf\x04\n" +
	"\vUserAccount\x12\x1e\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03R\n" +
	"account_id\x122\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1a.account_service.v1.StatusR\x06status\x12\x16\n" +
	"\x06source\x18\x03 \x01(\tR\x06source\x12\"\n" +
	"\fpartner_code\x18\x04 \x01(\tR\fpartner_code\x122\n" +
	"\x14partner_account_name\x18\x05 \x01(\tR\x14partner_account_name\x126\n" +
	"\x16partner_account_number\x18\x06 \x01(\tR\x16partner_account_number\x12,\n" +
	"\x11installment_limit\x18\a \x01(\x03R\x11installment_limit\x120\n" +
	"\x13installment_balance\x18\b \x01(\x03R\x13installment_balance\x12O\n" +
	"\x10installment_term\x18\t \x01(\v2#.account_service.v1.InstallmentTermR\x10installment_term\x12:\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"created_at\x121\n" +
	"\x11repayment_balance\x18\v \x01(\x03H\x00R\x11repayment_balance\x88\x01\x01B\x14\n" +
	"\x12_repayment_balance\"\x8a\x02\n" +
	"\vStmtAccount\x12\x1d\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03R\tzalopayId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\taccountId\x122\n" +
	"\x06status\x18\x03 \x01(\x0e2\x1a.account_service.v1.StatusR\x06status\x12!\n" +
	"\fpartner_code\x18\x04 \x01(\tR\vpartnerCode\x120\n" +
	"\x14partner_account_name\x18\x05 \x01(\tR\x12partnerAccountName\x124\n" +
	"\x16partner_account_number\x18\x06 \x01(\tR\x14partnerAccountNumber\"E\n" +
	" ListClientProposedServiceRequest\x12!\n" +
	"\fpartner_code\x18\x01 \x01(\tR\vpartnerCode\"d\n" +
	"!ListClientProposedServiceResponse\x12?\n" +
	"\bservices\x18\x01 \x03(\v2#.account_service.v1.ServiceProposedR\bservices\"n\n" +
	"\fContentImage\x12\x1a\n" +
	"\bicon_url\x18\x01 \x01(\tR\bicon_url\x12\x1c\n" +
	"\tcover_url\x18\x02 \x01(\tR\tcover_url\x12$\n" +
	"\rthumbnail_url\x18\x03 \x01(\tR\rthumbnail_url\"H\n" +
	"\x12ContentInteraction\x12\x18\n" +
	"\azpa_url\x18\x02 \x01(\tR\azpa_url\x12\x18\n" +
	"\azpi_url\x18\x03 \x01(\tR\azpi_url\"\xcb\x01\n" +
	"\x0fServiceProposed\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12F\n" +
	"\rcontent_image\x18\x03 \x01(\v2 .account_service.v1.ContentImageR\rcontent_image\x12H\n" +
	"\vinteraction\x18\x04 \x01(\v2&.account_service.v1.ContentInteractionR\vinteraction\"s\n" +
	"\x1bGetAccountForPaymentRequest\x12'\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"zalopay_id\x12+\n" +
	"\fpartner_code\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fpartner_code\"Y\n" +
	"\x1cGetAccountForPaymentResponse\x129\n" +
	"\aaccount\x18\x01 \x01(\v2\x1f.account_service.v1.UserAccountR\aaccount\"\xec\x01\n" +
	"\x1dListAccountForStmtSyncRequest\x12(\n" +
	"\vitems_limit\x18\x01 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\n" +
	"itemsLimit\x12/\n" +
	"\x0faccount_id_from\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02(\x00R\raccountIdFrom\x12K\n" +
	"\x0estatement_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\b\xfaB\x05\xb2\x01\x02\b\x01R\rstatementDate\x12#\n" +
	"\rpartner_codes\x18\x04 \x03(\tR\fpartnerCodes\"]\n" +
	"\x1eListAccountForStmtSyncResponse\x12;\n" +
	"\baccounts\x18\x01 \x03(\v2\x1f.account_service.v1.StmtAccountR\baccounts\"\xa3\x01\n" +
	"\n" +
	"Pagination\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\x12\x1b\n" +
	"\x06offset\x18\x02 \x01(\x05H\x00R\x06offset\x88\x01\x01\x12\x1b\n" +
	"\x06cursor\x18\x03 \x01(\tH\x01R\x06cursor\x88\x01\x01\x12!\n" +
	"\tdirection\x18\x04 \x01(\x05H\x02R\tdirection\x88\x01\x01B\t\n" +
	"\a_offsetB\t\n" +
	"\a_cursorB\f\n" +
	"\n" +
	"_direction\"\x9f\x01\n" +
	"\x0fInstallmentTerm\x12(\n" +
	"\x0ffee_explanation\x18\x01 \x01(\tR\x0ffee_explanation\x12.\n" +
	"\x12stmt_due_date_text\x18\x02 \x01(\tR\x12stmt_due_date_text\x122\n" +
	"\x14stmt_incur_date_text\x18\x03 \x01(\tR\x14stmt_incur_date_text*L\n" +
	"\x06Status\x12\x0f\n" +
	"\vUNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aBLOCKED\x10\x03\x12\n" +
	"\n" +
	"\x06CLOSED\x10\x042\x98\x06\n" +
	"\aAccount\x12f\n" +
	"\rCreateAccount\x12(.account_service.v1.CreateAccountRequest\x1a).account_service.v1.CreateAccountResponse\"\x00\x12\x8a\x01\n" +
	"\x10GetClientAccount\x12+.account_service.v1.GetClientAccountRequest\x1a,.account_service.v1.GetClientAccountResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/account/v1/account\x12\xb7\x01\n" +
	"\x19ListClientProposedService\x124.account_service.v1.ListClientProposedServiceRequest\x1a5.account_service.v1.ListClientProposedServiceResponse\"-\x82\xd3\xe4\x93\x02'\x12%/account/v1/content/proposed-services\x12]\n" +
	"\n" +
	"GetAccount\x12%.account_service.v1.GetAccountRequest\x1a&.account_service.v1.GetAccountResponse\"\x00\x12{\n" +
	"\x14GetAccountForPayment\x12/.account_service.v1.GetAccountForPaymentRequest\x1a0.account_service.v1.GetAccountForPaymentResponse\"\x00\x12\x81\x01\n" +
	"\x16ListAccountForStmtSync\x121.account_service.v1.ListAccountForStmtSyncRequest\x1a2.account_service.v1.ListAccountForStmtSyncResponse\"\x00B'Z%installment/api/account-service/v1;v1b\x06proto3"

var (
	file_account_service_v1_account_service_proto_rawDescOnce sync.Once
	file_account_service_v1_account_service_proto_rawDescData []byte
)

func file_account_service_v1_account_service_proto_rawDescGZIP() []byte {
	file_account_service_v1_account_service_proto_rawDescOnce.Do(func() {
		file_account_service_v1_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_account_service_v1_account_service_proto_rawDesc), len(file_account_service_v1_account_service_proto_rawDesc)))
	})
	return file_account_service_v1_account_service_proto_rawDescData
}

var file_account_service_v1_account_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_account_service_v1_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_account_service_v1_account_service_proto_goTypes = []any{
	(Status)(0),                               // 0: account_service.v1.Status
	(*CreateAccountRequest)(nil),              // 1: account_service.v1.CreateAccountRequest
	(*CreateAccountResponse)(nil),             // 2: account_service.v1.CreateAccountResponse
	(*ListAccountRequest)(nil),                // 3: account_service.v1.ListAccountRequest
	(*ListAccountResponse)(nil),               // 4: account_service.v1.ListAccountResponse
	(*GetAccountRequest)(nil),                 // 5: account_service.v1.GetAccountRequest
	(*GetAccountResponse)(nil),                // 6: account_service.v1.GetAccountResponse
	(*GetClientAccountRequest)(nil),           // 7: account_service.v1.GetClientAccountRequest
	(*GetClientAccountResponse)(nil),          // 8: account_service.v1.GetClientAccountResponse
	(*UserAccount)(nil),                       // 9: account_service.v1.UserAccount
	(*StmtAccount)(nil),                       // 10: account_service.v1.StmtAccount
	(*ListClientProposedServiceRequest)(nil),  // 11: account_service.v1.ListClientProposedServiceRequest
	(*ListClientProposedServiceResponse)(nil), // 12: account_service.v1.ListClientProposedServiceResponse
	(*ContentImage)(nil),                      // 13: account_service.v1.ContentImage
	(*ContentInteraction)(nil),                // 14: account_service.v1.ContentInteraction
	(*ServiceProposed)(nil),                   // 15: account_service.v1.ServiceProposed
	(*GetAccountForPaymentRequest)(nil),       // 16: account_service.v1.GetAccountForPaymentRequest
	(*GetAccountForPaymentResponse)(nil),      // 17: account_service.v1.GetAccountForPaymentResponse
	(*ListAccountForStmtSyncRequest)(nil),     // 18: account_service.v1.ListAccountForStmtSyncRequest
	(*ListAccountForStmtSyncResponse)(nil),    // 19: account_service.v1.ListAccountForStmtSyncResponse
	(*Pagination)(nil),                        // 20: account_service.v1.Pagination
	(*InstallmentTerm)(nil),                   // 21: account_service.v1.InstallmentTerm
	(*timestamppb.Timestamp)(nil),             // 22: google.protobuf.Timestamp
}
var file_account_service_v1_account_service_proto_depIdxs = []int32{
	9,  // 0: account_service.v1.ListAccountResponse.accounts:type_name -> account_service.v1.UserAccount
	9,  // 1: account_service.v1.GetAccountResponse.account:type_name -> account_service.v1.UserAccount
	9,  // 2: account_service.v1.GetClientAccountResponse.account:type_name -> account_service.v1.UserAccount
	0,  // 3: account_service.v1.UserAccount.status:type_name -> account_service.v1.Status
	21, // 4: account_service.v1.UserAccount.installment_term:type_name -> account_service.v1.InstallmentTerm
	22, // 5: account_service.v1.UserAccount.created_at:type_name -> google.protobuf.Timestamp
	0,  // 6: account_service.v1.StmtAccount.status:type_name -> account_service.v1.Status
	15, // 7: account_service.v1.ListClientProposedServiceResponse.services:type_name -> account_service.v1.ServiceProposed
	13, // 8: account_service.v1.ServiceProposed.content_image:type_name -> account_service.v1.ContentImage
	14, // 9: account_service.v1.ServiceProposed.interaction:type_name -> account_service.v1.ContentInteraction
	9,  // 10: account_service.v1.GetAccountForPaymentResponse.account:type_name -> account_service.v1.UserAccount
	22, // 11: account_service.v1.ListAccountForStmtSyncRequest.statement_date:type_name -> google.protobuf.Timestamp
	10, // 12: account_service.v1.ListAccountForStmtSyncResponse.accounts:type_name -> account_service.v1.StmtAccount
	1,  // 13: account_service.v1.Account.CreateAccount:input_type -> account_service.v1.CreateAccountRequest
	7,  // 14: account_service.v1.Account.GetClientAccount:input_type -> account_service.v1.GetClientAccountRequest
	11, // 15: account_service.v1.Account.ListClientProposedService:input_type -> account_service.v1.ListClientProposedServiceRequest
	5,  // 16: account_service.v1.Account.GetAccount:input_type -> account_service.v1.GetAccountRequest
	16, // 17: account_service.v1.Account.GetAccountForPayment:input_type -> account_service.v1.GetAccountForPaymentRequest
	18, // 18: account_service.v1.Account.ListAccountForStmtSync:input_type -> account_service.v1.ListAccountForStmtSyncRequest
	2,  // 19: account_service.v1.Account.CreateAccount:output_type -> account_service.v1.CreateAccountResponse
	8,  // 20: account_service.v1.Account.GetClientAccount:output_type -> account_service.v1.GetClientAccountResponse
	12, // 21: account_service.v1.Account.ListClientProposedService:output_type -> account_service.v1.ListClientProposedServiceResponse
	6,  // 22: account_service.v1.Account.GetAccount:output_type -> account_service.v1.GetAccountResponse
	17, // 23: account_service.v1.Account.GetAccountForPayment:output_type -> account_service.v1.GetAccountForPaymentResponse
	19, // 24: account_service.v1.Account.ListAccountForStmtSync:output_type -> account_service.v1.ListAccountForStmtSyncResponse
	19, // [19:25] is the sub-list for method output_type
	13, // [13:19] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_account_service_v1_account_service_proto_init() }
func file_account_service_v1_account_service_proto_init() {
	if File_account_service_v1_account_service_proto != nil {
		return
	}
	file_account_service_v1_account_service_proto_msgTypes[4].OneofWrappers = []any{
		(*GetAccountRequest_AccountId)(nil),
		(*GetAccountRequest_PartnerCode)(nil),
	}
	file_account_service_v1_account_service_proto_msgTypes[6].OneofWrappers = []any{
		(*GetClientAccountRequest_AccountId)(nil),
		(*GetClientAccountRequest_PartnerCode)(nil),
	}
	file_account_service_v1_account_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_account_service_v1_account_service_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_account_service_v1_account_service_proto_rawDesc), len(file_account_service_v1_account_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_account_service_v1_account_service_proto_goTypes,
		DependencyIndexes: file_account_service_v1_account_service_proto_depIdxs,
		EnumInfos:         file_account_service_v1_account_service_proto_enumTypes,
		MessageInfos:      file_account_service_v1_account_service_proto_msgTypes,
	}.Build()
	File_account_service_v1_account_service_proto = out.File
	file_account_service_v1_account_service_proto_goTypes = nil
	file_account_service_v1_account_service_proto_depIdxs = nil
}
