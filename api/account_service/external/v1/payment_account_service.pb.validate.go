// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: account_service/external/v1/payment_account_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetBasicAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBasicAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBasicAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBasicAccountRequestMultiError, or nil if none found.
func (m *GetBasicAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBasicAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetBasicAccountRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := GetBasicAccountRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBasicAccountRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBasicAccountRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBasicAccountRequestValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBasicAccountRequestMultiError(errors)
	}

	return nil
}

// GetBasicAccountRequestMultiError is an error wrapping multiple validation
// errors returned by GetBasicAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBasicAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBasicAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBasicAccountRequestMultiError) AllErrors() []error { return m }

// GetBasicAccountRequestValidationError is the validation error returned by
// GetBasicAccountRequest.Validate if the designated constraints aren't met.
type GetBasicAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBasicAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBasicAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBasicAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBasicAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBasicAccountRequestValidationError) ErrorName() string {
	return "GetBasicAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBasicAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBasicAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBasicAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBasicAccountRequestValidationError{}

// Validate checks the field values on GetBasicAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBasicAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBasicAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBasicAccountResponseMultiError, or nil if none found.
func (m *GetBasicAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBasicAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBasicAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBasicAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBasicAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBasicAccountResponseMultiError(errors)
	}

	return nil
}

// GetBasicAccountResponseMultiError is an error wrapping multiple validation
// errors returned by GetBasicAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBasicAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBasicAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBasicAccountResponseMultiError) AllErrors() []error { return m }

// GetBasicAccountResponseValidationError is the validation error returned by
// GetBasicAccountResponse.Validate if the designated constraints aren't met.
type GetBasicAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBasicAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBasicAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBasicAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBasicAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBasicAccountResponseValidationError) ErrorName() string {
	return "GetBasicAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBasicAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBasicAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBasicAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBasicAccountResponseValidationError{}

// Validate checks the field values on GetPaymentAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentAccountRequestMultiError, or nil if none found.
func (m *GetPaymentAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetPaymentAccountRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := GetPaymentAccountRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOrderInfo() == nil {
		err := GetPaymentAccountRequestValidationError{
			field:  "OrderInfo",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentAccountRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentAccountRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentAccountRequestValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentAccountRequestValidationError{
					field:  "ClientInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentAccountRequestValidationError{
					field:  "ClientInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentAccountRequestValidationError{
				field:  "ClientInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentAccountRequestMultiError(errors)
	}

	return nil
}

// GetPaymentAccountRequestMultiError is an error wrapping multiple validation
// errors returned by GetPaymentAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentAccountRequestMultiError) AllErrors() []error { return m }

// GetPaymentAccountRequestValidationError is the validation error returned by
// GetPaymentAccountRequest.Validate if the designated constraints aren't met.
type GetPaymentAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentAccountRequestValidationError) ErrorName() string {
	return "GetPaymentAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentAccountRequestValidationError{}

// Validate checks the field values on GetPaymentAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentAccountResponseMultiError, or nil if none found.
func (m *GetPaymentAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentAccountResponseValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentAccountResponseValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentAccountResponseValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentAccountResponseMultiError(errors)
	}

	return nil
}

// GetPaymentAccountResponseMultiError is an error wrapping multiple validation
// errors returned by GetPaymentAccountResponse.ValidateAll() if the
// designated constraints aren't met.
type GetPaymentAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentAccountResponseMultiError) AllErrors() []error { return m }

// GetPaymentAccountResponseValidationError is the validation error returned by
// GetPaymentAccountResponse.Validate if the designated constraints aren't met.
type GetPaymentAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentAccountResponseValidationError) ErrorName() string {
	return "GetPaymentAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentAccountResponseValidationError{}

// Validate checks the field values on CheckFullAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFullAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFullAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFullAccountRequestMultiError, or nil if none found.
func (m *CheckFullAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFullAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := CheckFullAccountRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := CheckFullAccountRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckFullAccountRequestMultiError(errors)
	}

	return nil
}

// CheckFullAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CheckFullAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckFullAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFullAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFullAccountRequestMultiError) AllErrors() []error { return m }

// CheckFullAccountRequestValidationError is the validation error returned by
// CheckFullAccountRequest.Validate if the designated constraints aren't met.
type CheckFullAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFullAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFullAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFullAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFullAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFullAccountRequestValidationError) ErrorName() string {
	return "CheckFullAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFullAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFullAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFullAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFullAccountRequestValidationError{}

// Validate checks the field values on CheckFullAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFullAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFullAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFullAccountResponseMultiError, or nil if none found.
func (m *CheckFullAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFullAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckFullAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckFullAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckFullAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckFullAccountResponseValidationError{
					field:  "Outstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckFullAccountResponseValidationError{
					field:  "Outstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckFullAccountResponseValidationError{
				field:  "Outstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckFullAccountResponseMultiError(errors)
	}

	return nil
}

// CheckFullAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CheckFullAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckFullAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFullAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFullAccountResponseMultiError) AllErrors() []error { return m }

// CheckFullAccountResponseValidationError is the validation error returned by
// CheckFullAccountResponse.Validate if the designated constraints aren't met.
type CheckFullAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFullAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFullAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFullAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFullAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFullAccountResponseValidationError) ErrorName() string {
	return "CheckFullAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFullAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFullAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFullAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFullAccountResponseValidationError{}

// Validate checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountInfoMultiError, or
// nil if none found.
func (m *AccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZalopayId

	// no validation rules for AccountId

	// no validation rules for PartnerCode

	// no validation rules for Status

	// no validation rules for Balance

	// no validation rules for TotalLimit

	if m.PartnerAccountName != nil {
		// no validation rules for PartnerAccountName
	}

	if m.PartnerAccountNumber != nil {
		// no validation rules for PartnerAccountNumber
	}

	if m.CreatedAt != nil {

		if all {
			switch v := interface{}(m.GetCreatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountInfoValidationError{
						field:  "CreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountInfoValidationError{
						field:  "CreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.UpdatedAt != nil {

		if all {
			switch v := interface{}(m.GetUpdatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountInfoValidationError{
						field:  "UpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountInfoValidationError{
						field:  "UpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountInfoMultiError(errors)
	}

	return nil
}

// AccountInfoMultiError is an error wrapping multiple validation errors
// returned by AccountInfo.ValidateAll() if the designated constraints aren't met.
type AccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoMultiError) AllErrors() []error { return m }

// AccountInfoValidationError is the validation error returned by
// AccountInfo.Validate if the designated constraints aren't met.
type AccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoValidationError) ErrorName() string { return "AccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoValidationError{}

// Validate checks the field values on OutstandingInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutstandingInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutstandingInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutstandingInfoMultiError, or nil if none found.
func (m *OutstandingInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OutstandingInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalOutstanding

	// no validation rules for TotalDueAmount

	// no validation rules for TotalDueRepaid

	// no validation rules for TotalDuePenalty

	if m.DueCreatedAt != nil {

		if all {
			switch v := interface{}(m.GetDueCreatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutstandingInfoValidationError{
						field:  "DueCreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutstandingInfoValidationError{
						field:  "DueCreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDueCreatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutstandingInfoValidationError{
					field:  "DueCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DueUpdatedAt != nil {

		if all {
			switch v := interface{}(m.GetDueUpdatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutstandingInfoValidationError{
						field:  "DueUpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutstandingInfoValidationError{
						field:  "DueUpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDueUpdatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutstandingInfoValidationError{
					field:  "DueUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutstandingInfoMultiError(errors)
	}

	return nil
}

// OutstandingInfoMultiError is an error wrapping multiple validation errors
// returned by OutstandingInfo.ValidateAll() if the designated constraints
// aren't met.
type OutstandingInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutstandingInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutstandingInfoMultiError) AllErrors() []error { return m }

// OutstandingInfoValidationError is the validation error returned by
// OutstandingInfo.Validate if the designated constraints aren't met.
type OutstandingInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutstandingInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutstandingInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutstandingInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutstandingInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutstandingInfoValidationError) ErrorName() string { return "OutstandingInfoValidationError" }

// Error satisfies the builtin error interface
func (e OutstandingInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutstandingInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutstandingInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutstandingInfoValidationError{}

// Validate checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ActionMultiError, or nil if none found.
func (m *Action) ValidateAll() error {
	return m.validate(true)
}

func (m *Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Text

	// no validation rules for ZpiUrl

	// no validation rules for ZpaUrl

	if len(errors) > 0 {
		return ActionMultiError(errors)
	}

	return nil
}

// ActionMultiError is an error wrapping multiple validation errors returned by
// Action.ValidateAll() if the designated constraints aren't met.
type ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionMultiError) AllErrors() []error { return m }

// ActionValidationError is the validation error returned by Action.Validate if
// the designated constraints aren't met.
type ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionValidationError) ErrorName() string { return "ActionValidationError" }

// Error satisfies the builtin error interface
func (e ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionValidationError{}

// Validate checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Message) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MessageMultiError, or nil if none found.
func (m *Message) ValidateAll() error {
	return m.validate(true)
}

func (m *Message) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for Color

	if len(errors) > 0 {
		return MessageMultiError(errors)
	}

	return nil
}

// MessageMultiError is an error wrapping multiple validation errors returned
// by Message.ValidateAll() if the designated constraints aren't met.
type MessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessageMultiError) AllErrors() []error { return m }

// MessageValidationError is the validation error returned by Message.Validate
// if the designated constraints aren't met.
type MessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessageValidationError) ErrorName() string { return "MessageValidationError" }

// Error satisfies the builtin error interface
func (e MessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessageValidationError{}

// Validate checks the field values on InteractionGuide with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InteractionGuide) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractionGuide with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InteractionGuideMultiError, or nil if none found.
func (m *InteractionGuide) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractionGuide) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InteractionGuideMultiError(errors)
	}

	return nil
}

// InteractionGuideMultiError is an error wrapping multiple validation errors
// returned by InteractionGuide.ValidateAll() if the designated constraints
// aren't met.
type InteractionGuideMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractionGuideMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractionGuideMultiError) AllErrors() []error { return m }

// InteractionGuideValidationError is the validation error returned by
// InteractionGuide.Validate if the designated constraints aren't met.
type InteractionGuideValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractionGuideValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractionGuideValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractionGuideValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractionGuideValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractionGuideValidationError) ErrorName() string { return "InteractionGuideValidationError" }

// Error satisfies the builtin error interface
func (e InteractionGuideValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractionGuide.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractionGuideValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractionGuideValidationError{}

// Validate checks the field values on GetBasicAccountRequest_OrderInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBasicAccountRequest_OrderInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBasicAccountRequest_OrderInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBasicAccountRequest_OrderInfoMultiError, or nil if none found.
func (m *GetBasicAccountRequest_OrderInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBasicAccountRequest_OrderInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	// no validation rules for ChargeAmount

	// no validation rules for EmbedData

	if len(errors) > 0 {
		return GetBasicAccountRequest_OrderInfoMultiError(errors)
	}

	return nil
}

// GetBasicAccountRequest_OrderInfoMultiError is an error wrapping multiple
// validation errors returned by
// GetBasicAccountRequest_OrderInfo.ValidateAll() if the designated
// constraints aren't met.
type GetBasicAccountRequest_OrderInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBasicAccountRequest_OrderInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBasicAccountRequest_OrderInfoMultiError) AllErrors() []error { return m }

// GetBasicAccountRequest_OrderInfoValidationError is the validation error
// returned by GetBasicAccountRequest_OrderInfo.Validate if the designated
// constraints aren't met.
type GetBasicAccountRequest_OrderInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBasicAccountRequest_OrderInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBasicAccountRequest_OrderInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBasicAccountRequest_OrderInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBasicAccountRequest_OrderInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBasicAccountRequest_OrderInfoValidationError) ErrorName() string {
	return "GetBasicAccountRequest_OrderInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetBasicAccountRequest_OrderInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBasicAccountRequest_OrderInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBasicAccountRequest_OrderInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBasicAccountRequest_OrderInfoValidationError{}

// Validate checks the field values on GetPaymentAccountRequest_OrderInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaymentAccountRequest_OrderInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentAccountRequest_OrderInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaymentAccountRequest_OrderInfoMultiError, or nil if none found.
func (m *GetPaymentAccountRequest_OrderInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentAccountRequest_OrderInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAppId() <= 0 {
		err := GetPaymentAccountRequest_OrderInfoValidationError{
			field:  "AppId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := GetPaymentAccountRequest_OrderInfoValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetChargeAmount() <= 0 {
		err := GetPaymentAccountRequest_OrderInfoValidationError{
			field:  "ChargeAmount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MerchantName

	// no validation rules for ServiceType

	// no validation rules for Description

	// no validation rules for OrderSource

	// no validation rules for EmbedData

	// no validation rules for Mno

	if len(errors) > 0 {
		return GetPaymentAccountRequest_OrderInfoMultiError(errors)
	}

	return nil
}

// GetPaymentAccountRequest_OrderInfoMultiError is an error wrapping multiple
// validation errors returned by
// GetPaymentAccountRequest_OrderInfo.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentAccountRequest_OrderInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentAccountRequest_OrderInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentAccountRequest_OrderInfoMultiError) AllErrors() []error { return m }

// GetPaymentAccountRequest_OrderInfoValidationError is the validation error
// returned by GetPaymentAccountRequest_OrderInfo.Validate if the designated
// constraints aren't met.
type GetPaymentAccountRequest_OrderInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentAccountRequest_OrderInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentAccountRequest_OrderInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentAccountRequest_OrderInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentAccountRequest_OrderInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentAccountRequest_OrderInfoValidationError) ErrorName() string {
	return "GetPaymentAccountRequest_OrderInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentAccountRequest_OrderInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentAccountRequest_OrderInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentAccountRequest_OrderInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentAccountRequest_OrderInfoValidationError{}

// Validate checks the field values on GetPaymentAccountRequest_ClientInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPaymentAccountRequest_ClientInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentAccountRequest_ClientInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaymentAccountRequest_ClientInfoMultiError, or nil if none found.
func (m *GetPaymentAccountRequest_ClientInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentAccountRequest_ClientInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for UserIp

	// no validation rules for UserLevel

	// no validation rules for OsPlatform

	// no validation rules for OsVersion

	// no validation rules for AppVersion

	// no validation rules for DeviceModel

	// no validation rules for ExUserInfo

	if len(errors) > 0 {
		return GetPaymentAccountRequest_ClientInfoMultiError(errors)
	}

	return nil
}

// GetPaymentAccountRequest_ClientInfoMultiError is an error wrapping multiple
// validation errors returned by
// GetPaymentAccountRequest_ClientInfo.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentAccountRequest_ClientInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentAccountRequest_ClientInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentAccountRequest_ClientInfoMultiError) AllErrors() []error { return m }

// GetPaymentAccountRequest_ClientInfoValidationError is the validation error
// returned by GetPaymentAccountRequest_ClientInfo.Validate if the designated
// constraints aren't met.
type GetPaymentAccountRequest_ClientInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentAccountRequest_ClientInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentAccountRequest_ClientInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentAccountRequest_ClientInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentAccountRequest_ClientInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentAccountRequest_ClientInfoValidationError) ErrorName() string {
	return "GetPaymentAccountRequest_ClientInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentAccountRequest_ClientInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentAccountRequest_ClientInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentAccountRequest_ClientInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentAccountRequest_ClientInfoValidationError{}
