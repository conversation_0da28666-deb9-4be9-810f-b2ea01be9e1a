syntax = "proto3";

package account_service.external.v1;

option go_package = "installment-service/api/account-service/v1;v1";

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

service PaymentAccount {
  rpc CheckFullAccount(CheckFullAccountRequest) returns (CheckFullAccountResponse) {}
  rpc GetBasicAccount(GetBasicAccountRequest) returns (GetBasicAccountResponse) {}
  rpc GetPaymentAccount(GetPaymentAccountRequest) returns (GetPaymentAccountResponse) {}
}

message GetBasicAccountRequest {
  message OrderInfo {
    // Order app id
    int32 app_id = 1;
    // User charge amount
    int64 charge_amount = 3;
    // Embed data
    string embed_data = 4;
  }

  // ZaloPay user id
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  // partner code of installment user account
  string partner_code = 2 [(validate.rules).string.min_len = 1];
  // order information
  OrderInfo order_info = 3 [deprecated = true];
}

message GetBasicAccountResponse {
  AccountInfo account = 1;
}

message GetPaymentAccountRequest {
  message OrderInfo {
    // Order app id
    int32 app_id = 1 [(validate.rules).int32.gt = 0];
    // Order app transaction id
    string app_trans_id = 2 [(validate.rules).string.min_len = 1]; // #Agreement_Payment
    // User charge amount
    int64 charge_amount = 3 [(validate.rules).int64.gt = 0];
    // Merchant name
    string merchant_name = 4; // #Agreement_Payment
    // Grab/Baemin has multiple service/merchant type
    string service_type = 5; // #Agreement_Payment
    // Order description
    string description = 6;
    // Order source
    int32 order_source = 7;
    // Embed data
    string embed_data = 8; // #Agreement_Payment
    // MNO
    string mno = 9;
  }
  message ClientInfo {
    // Client device id
    string device_id = 1;
    // Client user ip
    string user_ip = 2;
    // Client user level
    string user_level = 3;
    // Client os platform
    string os_platform = 4;
    // Client os version
    string os_version = 5;
    // Client app version
    string app_version = 6;
    // Client device model
    string device_model = 7;
    // User extra info
    string ex_user_info = 9; // #Agreement_Payment
  }

  // zalopay user id
  int64 zalopay_id = 2 [(validate.rules).int64.gt = 0];
  // partner code of installment user account
  string partner_code = 3 [(validate.rules).string.min_len = 1];
  // order information
  OrderInfo order_info = 4 [(validate.rules).message.required = true];
  // client information
  ClientInfo client_info = 5;
}

message GetPaymentAccountResponse {
  AccountInfo account = 1;
  Action action = 2;
  Message message = 3;
}

message CheckFullAccountRequest {
  // ZaloPay user id
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  // partner code of installment user account
  string partner_code = 2 [(validate.rules).string.min_len = 1];
}

message CheckFullAccountResponse {
  AccountInfo account = 1;
  OutstandingInfo outstanding = 2;
}

message AccountInfo {
  // Identity fields (1-3)
  // ZaloPay user id
  int64 zalopay_id = 1;
  // Installment account id
  int64 account_id = 2;
  // Installment partner code of this account
  string partner_code = 3;

  // Finance fields (4-15)
  // Account status
  PaymentAccountStatus status = 5;
  // Installment available balance of the account, can be used for payment
  int64 balance = 4;
  // Installment limit of the account
  int64 total_limit = 6;

  // Details fields (16-29)
  // Installment account name
  optional string partner_account_name = 16;
  // Installment account number
  optional string partner_account_number = 17;

  // Metadata fields (30+)
  // Created at
  optional google.protobuf.Timestamp created_at = 30;
  // Updated at
  optional google.protobuf.Timestamp updated_at = 31;
}

message OutstandingInfo {
  // Total outstanding amount = due + non due
  int64 total_outstanding = 1;
  // Outstanding due amount
  int64 total_due_amount = 2;
  // Outstanding due repaid amount
  int64 total_due_repaid = 3;
  // Outstanding due penalty amount
  int64 total_due_penalty = 4;
  // Due created at
  optional google.protobuf.Timestamp due_created_at = 30;
  // Due updated at
  optional google.protobuf.Timestamp due_updated_at = 31;
}

enum PaymentAccountStatus {
  // Unspecified status
  ACCOUNT_UNSPECIFIED = 0;
  // Account active
  ACCOUNT_ACTIVE = 1;
  // Account inactive
  ACCOUNT_INACTIVE = 2;
  // Account not registered
  ACCOUNT_NOT_REGISTER = 3;
  // Account in onboarding
  ACCOUNT_IN_ONBOARDING = 4;
  // Installment is under maintenance
  ACCOUNT_MAINTENANCE= 5;
  // Account hit risk rule
  ACCOUNT_RISK_RULE = 6;
  // Account temporarily locked
  ACCOUNT_TEMPORARY_LOCKED = 7;
  // Installment payment not applicable
  ACCOUNT_NOT_APPLICABLE = 8;
}

message Action {
  ActionType type = 1;
  string text = 2;
  string zpi_url = 3;
  string zpa_url = 4;
}

enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  ACTION_TYPE_PLAN_SELECTION = 1;
}

message Message {
  string text = 1;
  string color = 2;
}

message InteractionGuide {
  Action action = 1;
  Message message = 2;
  Message notice = 3;
}
