// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: account_service/external/v1/payment_account_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaymentAccountStatus int32

const (
	// Unspecified status
	PaymentAccountStatus_ACCOUNT_UNSPECIFIED PaymentAccountStatus = 0
	// Account active
	PaymentAccountStatus_ACCOUNT_ACTIVE PaymentAccountStatus = 1
	// Account inactive
	PaymentAccountStatus_ACCOUNT_INACTIVE PaymentAccountStatus = 2
	// Account not registered
	PaymentAccountStatus_ACCOUNT_NOT_REGISTER PaymentAccountStatus = 3
	// Account in onboarding
	PaymentAccountStatus_ACCOUNT_IN_ONBOARDING PaymentAccountStatus = 4
	// Installment is under maintenance
	PaymentAccountStatus_ACCOUNT_MAINTENANCE PaymentAccountStatus = 5
	// Account hit risk rule
	PaymentAccountStatus_ACCOUNT_RISK_RULE PaymentAccountStatus = 6
	// Account temporarily locked
	PaymentAccountStatus_ACCOUNT_TEMPORARY_LOCKED PaymentAccountStatus = 7
	// Installment payment not applicable
	PaymentAccountStatus_ACCOUNT_NOT_APPLICABLE PaymentAccountStatus = 8
)

// Enum value maps for PaymentAccountStatus.
var (
	PaymentAccountStatus_name = map[int32]string{
		0: "ACCOUNT_UNSPECIFIED",
		1: "ACCOUNT_ACTIVE",
		2: "ACCOUNT_INACTIVE",
		3: "ACCOUNT_NOT_REGISTER",
		4: "ACCOUNT_IN_ONBOARDING",
		5: "ACCOUNT_MAINTENANCE",
		6: "ACCOUNT_RISK_RULE",
		7: "ACCOUNT_TEMPORARY_LOCKED",
		8: "ACCOUNT_NOT_APPLICABLE",
	}
	PaymentAccountStatus_value = map[string]int32{
		"ACCOUNT_UNSPECIFIED":      0,
		"ACCOUNT_ACTIVE":           1,
		"ACCOUNT_INACTIVE":         2,
		"ACCOUNT_NOT_REGISTER":     3,
		"ACCOUNT_IN_ONBOARDING":    4,
		"ACCOUNT_MAINTENANCE":      5,
		"ACCOUNT_RISK_RULE":        6,
		"ACCOUNT_TEMPORARY_LOCKED": 7,
		"ACCOUNT_NOT_APPLICABLE":   8,
	}
)

func (x PaymentAccountStatus) Enum() *PaymentAccountStatus {
	p := new(PaymentAccountStatus)
	*p = x
	return p
}

func (x PaymentAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_account_service_external_v1_payment_account_service_proto_enumTypes[0].Descriptor()
}

func (PaymentAccountStatus) Type() protoreflect.EnumType {
	return &file_account_service_external_v1_payment_account_service_proto_enumTypes[0]
}

func (x PaymentAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentAccountStatus.Descriptor instead.
func (PaymentAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{0}
}

type ActionType int32

const (
	ActionType_ACTION_TYPE_UNSPECIFIED    ActionType = 0
	ActionType_ACTION_TYPE_PLAN_SELECTION ActionType = 1
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "ACTION_TYPE_PLAN_SELECTION",
	}
	ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED":    0,
		"ACTION_TYPE_PLAN_SELECTION": 1,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_service_external_v1_payment_account_service_proto_enumTypes[1].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_account_service_external_v1_payment_account_service_proto_enumTypes[1]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{1}
}

type GetBasicAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ZaloPay user id
	ZalopayId int64 `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// partner code of installment user account
	PartnerCode string `protobuf:"bytes,2,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	// order information
	//
	// Deprecated: Marked as deprecated in account_service/external/v1/payment_account_service.proto.
	OrderInfo     *GetBasicAccountRequest_OrderInfo `protobuf:"bytes,3,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBasicAccountRequest) Reset() {
	*x = GetBasicAccountRequest{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBasicAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBasicAccountRequest) ProtoMessage() {}

func (x *GetBasicAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBasicAccountRequest.ProtoReflect.Descriptor instead.
func (*GetBasicAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetBasicAccountRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetBasicAccountRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

// Deprecated: Marked as deprecated in account_service/external/v1/payment_account_service.proto.
func (x *GetBasicAccountRequest) GetOrderInfo() *GetBasicAccountRequest_OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

type GetBasicAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountInfo           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBasicAccountResponse) Reset() {
	*x = GetBasicAccountResponse{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBasicAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBasicAccountResponse) ProtoMessage() {}

func (x *GetBasicAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBasicAccountResponse.ProtoReflect.Descriptor instead.
func (*GetBasicAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetBasicAccountResponse) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetPaymentAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// zalopay user id
	ZalopayId int64 `protobuf:"varint,2,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// partner code of installment user account
	PartnerCode string `protobuf:"bytes,3,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	// order information
	OrderInfo *GetPaymentAccountRequest_OrderInfo `protobuf:"bytes,4,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// client information
	ClientInfo    *GetPaymentAccountRequest_ClientInfo `protobuf:"bytes,5,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentAccountRequest) Reset() {
	*x = GetPaymentAccountRequest{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentAccountRequest) ProtoMessage() {}

func (x *GetPaymentAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentAccountRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPaymentAccountRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetPaymentAccountRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *GetPaymentAccountRequest) GetOrderInfo() *GetPaymentAccountRequest_OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *GetPaymentAccountRequest) GetClientInfo() *GetPaymentAccountRequest_ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

type GetPaymentAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountInfo           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Action        *Action                `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	Message       *Message               `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentAccountResponse) Reset() {
	*x = GetPaymentAccountResponse{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentAccountResponse) ProtoMessage() {}

func (x *GetPaymentAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentAccountResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPaymentAccountResponse) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetPaymentAccountResponse) GetAction() *Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *GetPaymentAccountResponse) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

type CheckFullAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ZaloPay user id
	ZalopayId int64 `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// partner code of installment user account
	PartnerCode   string `protobuf:"bytes,2,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFullAccountRequest) Reset() {
	*x = CheckFullAccountRequest{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFullAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFullAccountRequest) ProtoMessage() {}

func (x *CheckFullAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFullAccountRequest.ProtoReflect.Descriptor instead.
func (*CheckFullAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckFullAccountRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *CheckFullAccountRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type CheckFullAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountInfo           `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Outstanding   *OutstandingInfo       `protobuf:"bytes,2,opt,name=outstanding,proto3" json:"outstanding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFullAccountResponse) Reset() {
	*x = CheckFullAccountResponse{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFullAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFullAccountResponse) ProtoMessage() {}

func (x *CheckFullAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFullAccountResponse.ProtoReflect.Descriptor instead.
func (*CheckFullAccountResponse) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{5}
}

func (x *CheckFullAccountResponse) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *CheckFullAccountResponse) GetOutstanding() *OutstandingInfo {
	if x != nil {
		return x.Outstanding
	}
	return nil
}

type AccountInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Identity fields (1-3)
	// ZaloPay user id
	ZalopayId int64 `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// Installment account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Installment partner code of this account
	PartnerCode string `protobuf:"bytes,3,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	// Finance fields (4-15)
	// Account status
	Status PaymentAccountStatus `protobuf:"varint,5,opt,name=status,proto3,enum=account_service.external.v1.PaymentAccountStatus" json:"status,omitempty"`
	// Installment available balance of the account, can be used for payment
	Balance int64 `protobuf:"varint,4,opt,name=balance,proto3" json:"balance,omitempty"`
	// Installment limit of the account
	TotalLimit int64 `protobuf:"varint,6,opt,name=total_limit,json=totalLimit,proto3" json:"total_limit,omitempty"`
	// Details fields (16-29)
	// Installment account name
	PartnerAccountName *string `protobuf:"bytes,16,opt,name=partner_account_name,json=partnerAccountName,proto3,oneof" json:"partner_account_name,omitempty"`
	// Installment account number
	PartnerAccountNumber *string `protobuf:"bytes,17,opt,name=partner_account_number,json=partnerAccountNumber,proto3,oneof" json:"partner_account_number,omitempty"`
	// Metadata fields (30+)
	// Created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// Updated at
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{6}
}

func (x *AccountInfo) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *AccountInfo) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AccountInfo) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *AccountInfo) GetStatus() PaymentAccountStatus {
	if x != nil {
		return x.Status
	}
	return PaymentAccountStatus_ACCOUNT_UNSPECIFIED
}

func (x *AccountInfo) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *AccountInfo) GetTotalLimit() int64 {
	if x != nil {
		return x.TotalLimit
	}
	return 0
}

func (x *AccountInfo) GetPartnerAccountName() string {
	if x != nil && x.PartnerAccountName != nil {
		return *x.PartnerAccountName
	}
	return ""
}

func (x *AccountInfo) GetPartnerAccountNumber() string {
	if x != nil && x.PartnerAccountNumber != nil {
		return *x.PartnerAccountNumber
	}
	return ""
}

func (x *AccountInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AccountInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type OutstandingInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Total outstanding amount = due + non due
	TotalOutstanding int64 `protobuf:"varint,1,opt,name=total_outstanding,json=totalOutstanding,proto3" json:"total_outstanding,omitempty"`
	// Outstanding due amount
	TotalDueAmount int64 `protobuf:"varint,2,opt,name=total_due_amount,json=totalDueAmount,proto3" json:"total_due_amount,omitempty"`
	// Outstanding due repaid amount
	TotalDueRepaid int64 `protobuf:"varint,3,opt,name=total_due_repaid,json=totalDueRepaid,proto3" json:"total_due_repaid,omitempty"`
	// Outstanding due penalty amount
	TotalDuePenalty int64 `protobuf:"varint,4,opt,name=total_due_penalty,json=totalDuePenalty,proto3" json:"total_due_penalty,omitempty"`
	// Due created at
	DueCreatedAt *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=due_created_at,json=dueCreatedAt,proto3,oneof" json:"due_created_at,omitempty"`
	// Due updated at
	DueUpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=due_updated_at,json=dueUpdatedAt,proto3,oneof" json:"due_updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OutstandingInfo) Reset() {
	*x = OutstandingInfo{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutstandingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutstandingInfo) ProtoMessage() {}

func (x *OutstandingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutstandingInfo.ProtoReflect.Descriptor instead.
func (*OutstandingInfo) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{7}
}

func (x *OutstandingInfo) GetTotalOutstanding() int64 {
	if x != nil {
		return x.TotalOutstanding
	}
	return 0
}

func (x *OutstandingInfo) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *OutstandingInfo) GetTotalDueRepaid() int64 {
	if x != nil {
		return x.TotalDueRepaid
	}
	return 0
}

func (x *OutstandingInfo) GetTotalDuePenalty() int64 {
	if x != nil {
		return x.TotalDuePenalty
	}
	return 0
}

func (x *OutstandingInfo) GetDueCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DueCreatedAt
	}
	return nil
}

func (x *OutstandingInfo) GetDueUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DueUpdatedAt
	}
	return nil
}

type Action struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ActionType             `protobuf:"varint,1,opt,name=type,proto3,enum=account_service.external.v1.ActionType" json:"type,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	ZpiUrl        string                 `protobuf:"bytes,3,opt,name=zpi_url,json=zpiUrl,proto3" json:"zpi_url,omitempty"`
	ZpaUrl        string                 `protobuf:"bytes,4,opt,name=zpa_url,json=zpaUrl,proto3" json:"zpa_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Action) Reset() {
	*x = Action{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Action) ProtoMessage() {}

func (x *Action) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Action.ProtoReflect.Descriptor instead.
func (*Action) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{8}
}

func (x *Action) GetType() ActionType {
	if x != nil {
		return x.Type
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *Action) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Action) GetZpiUrl() string {
	if x != nil {
		return x.ZpiUrl
	}
	return ""
}

func (x *Action) GetZpaUrl() string {
	if x != nil {
		return x.ZpaUrl
	}
	return ""
}

type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Color         string                 `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{9}
}

func (x *Message) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Message) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type InteractionGuide struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        *Action                `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Message       *Message               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Notice        *Message               `protobuf:"bytes,3,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InteractionGuide) Reset() {
	*x = InteractionGuide{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InteractionGuide) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InteractionGuide) ProtoMessage() {}

func (x *InteractionGuide) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InteractionGuide.ProtoReflect.Descriptor instead.
func (*InteractionGuide) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{10}
}

func (x *InteractionGuide) GetAction() *Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *InteractionGuide) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *InteractionGuide) GetNotice() *Message {
	if x != nil {
		return x.Notice
	}
	return nil
}

type GetBasicAccountRequest_OrderInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Order app id
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// User charge amount
	ChargeAmount int64 `protobuf:"varint,3,opt,name=charge_amount,json=chargeAmount,proto3" json:"charge_amount,omitempty"`
	// Embed data
	EmbedData     string `protobuf:"bytes,4,opt,name=embed_data,json=embedData,proto3" json:"embed_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBasicAccountRequest_OrderInfo) Reset() {
	*x = GetBasicAccountRequest_OrderInfo{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBasicAccountRequest_OrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBasicAccountRequest_OrderInfo) ProtoMessage() {}

func (x *GetBasicAccountRequest_OrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBasicAccountRequest_OrderInfo.ProtoReflect.Descriptor instead.
func (*GetBasicAccountRequest_OrderInfo) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetBasicAccountRequest_OrderInfo) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetBasicAccountRequest_OrderInfo) GetChargeAmount() int64 {
	if x != nil {
		return x.ChargeAmount
	}
	return 0
}

func (x *GetBasicAccountRequest_OrderInfo) GetEmbedData() string {
	if x != nil {
		return x.EmbedData
	}
	return ""
}

type GetPaymentAccountRequest_OrderInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Order app id
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// Order app transaction id
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"` // #Agreement_Payment
	// User charge amount
	ChargeAmount int64 `protobuf:"varint,3,opt,name=charge_amount,json=chargeAmount,proto3" json:"charge_amount,omitempty"`
	// Merchant name
	MerchantName string `protobuf:"bytes,4,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"` // #Agreement_Payment
	// Grab/Baemin has multiple service/merchant type
	ServiceType string `protobuf:"bytes,5,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"` // #Agreement_Payment
	// Order description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// Order source
	OrderSource int32 `protobuf:"varint,7,opt,name=order_source,json=orderSource,proto3" json:"order_source,omitempty"`
	// Embed data
	EmbedData string `protobuf:"bytes,8,opt,name=embed_data,json=embedData,proto3" json:"embed_data,omitempty"` // #Agreement_Payment
	// MNO
	Mno           string `protobuf:"bytes,9,opt,name=mno,proto3" json:"mno,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentAccountRequest_OrderInfo) Reset() {
	*x = GetPaymentAccountRequest_OrderInfo{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentAccountRequest_OrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentAccountRequest_OrderInfo) ProtoMessage() {}

func (x *GetPaymentAccountRequest_OrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentAccountRequest_OrderInfo.ProtoReflect.Descriptor instead.
func (*GetPaymentAccountRequest_OrderInfo) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetPaymentAccountRequest_OrderInfo) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetPaymentAccountRequest_OrderInfo) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *GetPaymentAccountRequest_OrderInfo) GetChargeAmount() int64 {
	if x != nil {
		return x.ChargeAmount
	}
	return 0
}

func (x *GetPaymentAccountRequest_OrderInfo) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *GetPaymentAccountRequest_OrderInfo) GetServiceType() string {
	if x != nil {
		return x.ServiceType
	}
	return ""
}

func (x *GetPaymentAccountRequest_OrderInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetPaymentAccountRequest_OrderInfo) GetOrderSource() int32 {
	if x != nil {
		return x.OrderSource
	}
	return 0
}

func (x *GetPaymentAccountRequest_OrderInfo) GetEmbedData() string {
	if x != nil {
		return x.EmbedData
	}
	return ""
}

func (x *GetPaymentAccountRequest_OrderInfo) GetMno() string {
	if x != nil {
		return x.Mno
	}
	return ""
}

type GetPaymentAccountRequest_ClientInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Client device id
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Client user ip
	UserIp string `protobuf:"bytes,2,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// Client user level
	UserLevel string `protobuf:"bytes,3,opt,name=user_level,json=userLevel,proto3" json:"user_level,omitempty"`
	// Client os platform
	OsPlatform string `protobuf:"bytes,4,opt,name=os_platform,json=osPlatform,proto3" json:"os_platform,omitempty"`
	// Client os version
	OsVersion string `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	// Client app version
	AppVersion string `protobuf:"bytes,6,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// Client device model
	DeviceModel string `protobuf:"bytes,7,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	// User extra info
	ExUserInfo    string `protobuf:"bytes,9,opt,name=ex_user_info,json=exUserInfo,proto3" json:"ex_user_info,omitempty"` // #Agreement_Payment
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentAccountRequest_ClientInfo) Reset() {
	*x = GetPaymentAccountRequest_ClientInfo{}
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentAccountRequest_ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentAccountRequest_ClientInfo) ProtoMessage() {}

func (x *GetPaymentAccountRequest_ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_service_external_v1_payment_account_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentAccountRequest_ClientInfo.ProtoReflect.Descriptor instead.
func (*GetPaymentAccountRequest_ClientInfo) Descriptor() ([]byte, []int) {
	return file_account_service_external_v1_payment_account_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *GetPaymentAccountRequest_ClientInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetUserLevel() string {
	if x != nil {
		return x.UserLevel
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetOsPlatform() string {
	if x != nil {
		return x.OsPlatform
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *GetPaymentAccountRequest_ClientInfo) GetExUserInfo() string {
	if x != nil {
		return x.ExUserInfo
	}
	return ""
}

var File_account_service_external_v1_payment_account_service_proto protoreflect.FileDescriptor

const file_account_service_external_v1_payment_account_service_proto_rawDesc = "" +
	"\n" +
	"9account_service/external/v1/payment_account_service.proto\x12\x1baccount_service.external.v1\x1a\x17validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb6\x02\n" +
	"\x16GetBasicAccountRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12*\n" +
	"\fpartner_code\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\x12`\n" +
	"\n" +
	"order_info\x18\x03 \x01(\v2=.account_service.external.v1.GetBasicAccountRequest.OrderInfoB\x02\x18\x01R\torderInfo\x1af\n" +
	"\tOrderInfo\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\x05R\x05appId\x12#\n" +
	"\rcharge_amount\x18\x03 \x01(\x03R\fchargeAmount\x12\x1d\n" +
	"\n" +
	"embed_data\x18\x04 \x01(\tR\tembedData\"]\n" +
	"\x17GetBasicAccountResponse\x12B\n" +
	"\aaccount\x18\x01 \x01(\v2(.account_service.external.v1.AccountInfoR\aaccount\"\x8a\a\n" +
	"\x18GetPaymentAccountRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12*\n" +
	"\fpartner_code\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\x12h\n" +
	"\n" +
	"order_info\x18\x04 \x01(\v2?.account_service.external.v1.GetPaymentAccountRequest.OrderInfoB\b\xfaB\x05\x8a\x01\x02\x10\x01R\torderInfo\x12a\n" +
	"\vclient_info\x18\x05 \x01(\v2@.account_service.external.v1.GetPaymentAccountRequest.ClientInfoR\n" +
	"clientInfo\x1a\xc2\x02\n" +
	"\tOrderInfo\x12\x1e\n" +
	"\x06app_id\x18\x01 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\x05appId\x12)\n" +
	"\fapp_trans_id\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"appTransId\x12,\n" +
	"\rcharge_amount\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fchargeAmount\x12#\n" +
	"\rmerchant_name\x18\x04 \x01(\tR\fmerchantName\x12!\n" +
	"\fservice_type\x18\x05 \x01(\tR\vserviceType\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12!\n" +
	"\forder_source\x18\a \x01(\x05R\vorderSource\x12\x1d\n" +
	"\n" +
	"embed_data\x18\b \x01(\tR\tembedData\x12\x10\n" +
	"\x03mno\x18\t \x01(\tR\x03mno\x1a\x87\x02\n" +
	"\n" +
	"ClientInfo\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x17\n" +
	"\auser_ip\x18\x02 \x01(\tR\x06userIp\x12\x1d\n" +
	"\n" +
	"user_level\x18\x03 \x01(\tR\tuserLevel\x12\x1f\n" +
	"\vos_platform\x18\x04 \x01(\tR\n" +
	"osPlatform\x12\x1d\n" +
	"\n" +
	"os_version\x18\x05 \x01(\tR\tosVersion\x12\x1f\n" +
	"\vapp_version\x18\x06 \x01(\tR\n" +
	"appVersion\x12!\n" +
	"\fdevice_model\x18\a \x01(\tR\vdeviceModel\x12 \n" +
	"\fex_user_info\x18\t \x01(\tR\n" +
	"exUserInfo\"\xdc\x01\n" +
	"\x19GetPaymentAccountResponse\x12B\n" +
	"\aaccount\x18\x01 \x01(\v2(.account_service.external.v1.AccountInfoR\aaccount\x12;\n" +
	"\x06action\x18\x02 \x01(\v2#.account_service.external.v1.ActionR\x06action\x12>\n" +
	"\amessage\x18\x03 \x01(\v2$.account_service.external.v1.MessageR\amessage\"m\n" +
	"\x17CheckFullAccountRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12*\n" +
	"\fpartner_code\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\"\xae\x01\n" +
	"\x18CheckFullAccountResponse\x12B\n" +
	"\aaccount\x18\x01 \x01(\v2(.account_service.external.v1.AccountInfoR\aaccount\x12N\n" +
	"\voutstanding\x18\x02 \x01(\v2,.account_service.external.v1.OutstandingInfoR\voutstanding\"\xb8\x04\n" +
	"\vAccountInfo\x12\x1d\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03R\tzalopayId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\taccountId\x12!\n" +
	"\fpartner_code\x18\x03 \x01(\tR\vpartnerCode\x12I\n" +
	"\x06status\x18\x05 \x01(\x0e21.account_service.external.v1.PaymentAccountStatusR\x06status\x12\x18\n" +
	"\abalance\x18\x04 \x01(\x03R\abalance\x12\x1f\n" +
	"\vtotal_limit\x18\x06 \x01(\x03R\n" +
	"totalLimit\x125\n" +
	"\x14partner_account_name\x18\x10 \x01(\tH\x00R\x12partnerAccountName\x88\x01\x01\x129\n" +
	"\x16partner_account_number\x18\x11 \x01(\tH\x01R\x14partnerAccountNumber\x88\x01\x01\x12>\n" +
	"\n" +
	"created_at\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampH\x02R\tcreatedAt\x88\x01\x01\x12>\n" +
	"\n" +
	"updated_at\x18\x1f \x01(\v2\x1a.google.protobuf.TimestampH\x03R\tupdatedAt\x88\x01\x01B\x17\n" +
	"\x15_partner_account_nameB\x19\n" +
	"\x17_partner_account_numberB\r\n" +
	"\v_created_atB\r\n" +
	"\v_updated_at\"\xf2\x02\n" +
	"\x0fOutstandingInfo\x12+\n" +
	"\x11total_outstanding\x18\x01 \x01(\x03R\x10totalOutstanding\x12(\n" +
	"\x10total_due_amount\x18\x02 \x01(\x03R\x0etotalDueAmount\x12(\n" +
	"\x10total_due_repaid\x18\x03 \x01(\x03R\x0etotalDueRepaid\x12*\n" +
	"\x11total_due_penalty\x18\x04 \x01(\x03R\x0ftotalDuePenalty\x12E\n" +
	"\x0edue_created_at\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampH\x00R\fdueCreatedAt\x88\x01\x01\x12E\n" +
	"\x0edue_updated_at\x18\x1f \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fdueUpdatedAt\x88\x01\x01B\x11\n" +
	"\x0f_due_created_atB\x11\n" +
	"\x0f_due_updated_at\"\x8b\x01\n" +
	"\x06Action\x12;\n" +
	"\x04type\x18\x01 \x01(\x0e2'.account_service.external.v1.ActionTypeR\x04type\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x17\n" +
	"\azpi_url\x18\x03 \x01(\tR\x06zpiUrl\x12\x17\n" +
	"\azpa_url\x18\x04 \x01(\tR\x06zpaUrl\"3\n" +
	"\aMessage\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x14\n" +
	"\x05color\x18\x02 \x01(\tR\x05color\"\xcd\x01\n" +
	"\x10InteractionGuide\x12;\n" +
	"\x06action\x18\x01 \x01(\v2#.account_service.external.v1.ActionR\x06action\x12>\n" +
	"\amessage\x18\x02 \x01(\v2$.account_service.external.v1.MessageR\amessage\x12<\n" +
	"\x06notice\x18\x03 \x01(\v2$.account_service.external.v1.MessageR\x06notice*\xf8\x01\n" +
	"\x14PaymentAccountStatus\x12\x17\n" +
	"\x13ACCOUNT_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eACCOUNT_ACTIVE\x10\x01\x12\x14\n" +
	"\x10ACCOUNT_INACTIVE\x10\x02\x12\x18\n" +
	"\x14ACCOUNT_NOT_REGISTER\x10\x03\x12\x19\n" +
	"\x15ACCOUNT_IN_ONBOARDING\x10\x04\x12\x17\n" +
	"\x13ACCOUNT_MAINTENANCE\x10\x05\x12\x15\n" +
	"\x11ACCOUNT_RISK_RULE\x10\x06\x12\x1c\n" +
	"\x18ACCOUNT_TEMPORARY_LOCKED\x10\a\x12\x1a\n" +
	"\x16ACCOUNT_NOT_APPLICABLE\x10\b*I\n" +
	"\n" +
	"ActionType\x12\x1b\n" +
	"\x17ACTION_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aACTION_TYPE_PLAN_SELECTION\x10\x012\x9b\x03\n" +
	"\x0ePaymentAccount\x12\x81\x01\n" +
	"\x10CheckFullAccount\x124.account_service.external.v1.CheckFullAccountRequest\x1a5.account_service.external.v1.CheckFullAccountResponse\"\x00\x12~\n" +
	"\x0fGetBasicAccount\x123.account_service.external.v1.GetBasicAccountRequest\x1a4.account_service.external.v1.GetBasicAccountResponse\"\x00\x12\x84\x01\n" +
	"\x11GetPaymentAccount\x125.account_service.external.v1.GetPaymentAccountRequest\x1a6.account_service.external.v1.GetPaymentAccountResponse\"\x00B/Z-installment-service/api/account-service/v1;v1b\x06proto3"

var (
	file_account_service_external_v1_payment_account_service_proto_rawDescOnce sync.Once
	file_account_service_external_v1_payment_account_service_proto_rawDescData []byte
)

func file_account_service_external_v1_payment_account_service_proto_rawDescGZIP() []byte {
	file_account_service_external_v1_payment_account_service_proto_rawDescOnce.Do(func() {
		file_account_service_external_v1_payment_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_account_service_external_v1_payment_account_service_proto_rawDesc), len(file_account_service_external_v1_payment_account_service_proto_rawDesc)))
	})
	return file_account_service_external_v1_payment_account_service_proto_rawDescData
}

var file_account_service_external_v1_payment_account_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_account_service_external_v1_payment_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_account_service_external_v1_payment_account_service_proto_goTypes = []any{
	(PaymentAccountStatus)(0),                   // 0: account_service.external.v1.PaymentAccountStatus
	(ActionType)(0),                             // 1: account_service.external.v1.ActionType
	(*GetBasicAccountRequest)(nil),              // 2: account_service.external.v1.GetBasicAccountRequest
	(*GetBasicAccountResponse)(nil),             // 3: account_service.external.v1.GetBasicAccountResponse
	(*GetPaymentAccountRequest)(nil),            // 4: account_service.external.v1.GetPaymentAccountRequest
	(*GetPaymentAccountResponse)(nil),           // 5: account_service.external.v1.GetPaymentAccountResponse
	(*CheckFullAccountRequest)(nil),             // 6: account_service.external.v1.CheckFullAccountRequest
	(*CheckFullAccountResponse)(nil),            // 7: account_service.external.v1.CheckFullAccountResponse
	(*AccountInfo)(nil),                         // 8: account_service.external.v1.AccountInfo
	(*OutstandingInfo)(nil),                     // 9: account_service.external.v1.OutstandingInfo
	(*Action)(nil),                              // 10: account_service.external.v1.Action
	(*Message)(nil),                             // 11: account_service.external.v1.Message
	(*InteractionGuide)(nil),                    // 12: account_service.external.v1.InteractionGuide
	(*GetBasicAccountRequest_OrderInfo)(nil),    // 13: account_service.external.v1.GetBasicAccountRequest.OrderInfo
	(*GetPaymentAccountRequest_OrderInfo)(nil),  // 14: account_service.external.v1.GetPaymentAccountRequest.OrderInfo
	(*GetPaymentAccountRequest_ClientInfo)(nil), // 15: account_service.external.v1.GetPaymentAccountRequest.ClientInfo
	(*timestamppb.Timestamp)(nil),               // 16: google.protobuf.Timestamp
}
var file_account_service_external_v1_payment_account_service_proto_depIdxs = []int32{
	13, // 0: account_service.external.v1.GetBasicAccountRequest.order_info:type_name -> account_service.external.v1.GetBasicAccountRequest.OrderInfo
	8,  // 1: account_service.external.v1.GetBasicAccountResponse.account:type_name -> account_service.external.v1.AccountInfo
	14, // 2: account_service.external.v1.GetPaymentAccountRequest.order_info:type_name -> account_service.external.v1.GetPaymentAccountRequest.OrderInfo
	15, // 3: account_service.external.v1.GetPaymentAccountRequest.client_info:type_name -> account_service.external.v1.GetPaymentAccountRequest.ClientInfo
	8,  // 4: account_service.external.v1.GetPaymentAccountResponse.account:type_name -> account_service.external.v1.AccountInfo
	10, // 5: account_service.external.v1.GetPaymentAccountResponse.action:type_name -> account_service.external.v1.Action
	11, // 6: account_service.external.v1.GetPaymentAccountResponse.message:type_name -> account_service.external.v1.Message
	8,  // 7: account_service.external.v1.CheckFullAccountResponse.account:type_name -> account_service.external.v1.AccountInfo
	9,  // 8: account_service.external.v1.CheckFullAccountResponse.outstanding:type_name -> account_service.external.v1.OutstandingInfo
	0,  // 9: account_service.external.v1.AccountInfo.status:type_name -> account_service.external.v1.PaymentAccountStatus
	16, // 10: account_service.external.v1.AccountInfo.created_at:type_name -> google.protobuf.Timestamp
	16, // 11: account_service.external.v1.AccountInfo.updated_at:type_name -> google.protobuf.Timestamp
	16, // 12: account_service.external.v1.OutstandingInfo.due_created_at:type_name -> google.protobuf.Timestamp
	16, // 13: account_service.external.v1.OutstandingInfo.due_updated_at:type_name -> google.protobuf.Timestamp
	1,  // 14: account_service.external.v1.Action.type:type_name -> account_service.external.v1.ActionType
	10, // 15: account_service.external.v1.InteractionGuide.action:type_name -> account_service.external.v1.Action
	11, // 16: account_service.external.v1.InteractionGuide.message:type_name -> account_service.external.v1.Message
	11, // 17: account_service.external.v1.InteractionGuide.notice:type_name -> account_service.external.v1.Message
	6,  // 18: account_service.external.v1.PaymentAccount.CheckFullAccount:input_type -> account_service.external.v1.CheckFullAccountRequest
	2,  // 19: account_service.external.v1.PaymentAccount.GetBasicAccount:input_type -> account_service.external.v1.GetBasicAccountRequest
	4,  // 20: account_service.external.v1.PaymentAccount.GetPaymentAccount:input_type -> account_service.external.v1.GetPaymentAccountRequest
	7,  // 21: account_service.external.v1.PaymentAccount.CheckFullAccount:output_type -> account_service.external.v1.CheckFullAccountResponse
	3,  // 22: account_service.external.v1.PaymentAccount.GetBasicAccount:output_type -> account_service.external.v1.GetBasicAccountResponse
	5,  // 23: account_service.external.v1.PaymentAccount.GetPaymentAccount:output_type -> account_service.external.v1.GetPaymentAccountResponse
	21, // [21:24] is the sub-list for method output_type
	18, // [18:21] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_account_service_external_v1_payment_account_service_proto_init() }
func file_account_service_external_v1_payment_account_service_proto_init() {
	if File_account_service_external_v1_payment_account_service_proto != nil {
		return
	}
	file_account_service_external_v1_payment_account_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_account_service_external_v1_payment_account_service_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_account_service_external_v1_payment_account_service_proto_rawDesc), len(file_account_service_external_v1_payment_account_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_account_service_external_v1_payment_account_service_proto_goTypes,
		DependencyIndexes: file_account_service_external_v1_payment_account_service_proto_depIdxs,
		EnumInfos:         file_account_service_external_v1_payment_account_service_proto_enumTypes,
		MessageInfos:      file_account_service_external_v1_payment_account_service_proto_msgTypes,
	}.Build()
	File_account_service_external_v1_payment_account_service_proto = out.File
	file_account_service_external_v1_payment_account_service_proto_goTypes = nil
	file_account_service_external_v1_payment_account_service_proto_depIdxs = nil
}
