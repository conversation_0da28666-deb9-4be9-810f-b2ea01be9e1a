// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: account_service/external/v1/payment_account_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPaymentAccountClient is a mock of PaymentAccountClient interface.
type MockPaymentAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentAccountClientMockRecorder
}

// MockPaymentAccountClientMockRecorder is the mock recorder for MockPaymentAccountClient.
type MockPaymentAccountClientMockRecorder struct {
	mock *MockPaymentAccountClient
}

// NewMockPaymentAccountClient creates a new mock instance.
func NewMockPaymentAccountClient(ctrl *gomock.Controller) *MockPaymentAccountClient {
	mock := &MockPaymentAccountClient{ctrl: ctrl}
	mock.recorder = &MockPaymentAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentAccountClient) EXPECT() *MockPaymentAccountClientMockRecorder {
	return m.recorder
}

// CheckFullAccount mocks base method.
func (m *MockPaymentAccountClient) CheckFullAccount(ctx context.Context, in *CheckFullAccountRequest, opts ...grpc.CallOption) (*CheckFullAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckFullAccount", varargs...)
	ret0, _ := ret[0].(*CheckFullAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFullAccount indicates an expected call of CheckFullAccount.
func (mr *MockPaymentAccountClientMockRecorder) CheckFullAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFullAccount", reflect.TypeOf((*MockPaymentAccountClient)(nil).CheckFullAccount), varargs...)
}

// GetBasicAccount mocks base method.
func (m *MockPaymentAccountClient) GetBasicAccount(ctx context.Context, in *GetBasicAccountRequest, opts ...grpc.CallOption) (*GetBasicAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBasicAccount", varargs...)
	ret0, _ := ret[0].(*GetBasicAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasicAccount indicates an expected call of GetBasicAccount.
func (mr *MockPaymentAccountClientMockRecorder) GetBasicAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasicAccount", reflect.TypeOf((*MockPaymentAccountClient)(nil).GetBasicAccount), varargs...)
}

// GetPaymentAccount mocks base method.
func (m *MockPaymentAccountClient) GetPaymentAccount(ctx context.Context, in *GetPaymentAccountRequest, opts ...grpc.CallOption) (*GetPaymentAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentAccount", varargs...)
	ret0, _ := ret[0].(*GetPaymentAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentAccount indicates an expected call of GetPaymentAccount.
func (mr *MockPaymentAccountClientMockRecorder) GetPaymentAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentAccount", reflect.TypeOf((*MockPaymentAccountClient)(nil).GetPaymentAccount), varargs...)
}

// MockPaymentAccountServer is a mock of PaymentAccountServer interface.
type MockPaymentAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentAccountServerMockRecorder
}

// MockPaymentAccountServerMockRecorder is the mock recorder for MockPaymentAccountServer.
type MockPaymentAccountServerMockRecorder struct {
	mock *MockPaymentAccountServer
}

// NewMockPaymentAccountServer creates a new mock instance.
func NewMockPaymentAccountServer(ctrl *gomock.Controller) *MockPaymentAccountServer {
	mock := &MockPaymentAccountServer{ctrl: ctrl}
	mock.recorder = &MockPaymentAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentAccountServer) EXPECT() *MockPaymentAccountServerMockRecorder {
	return m.recorder
}

// CheckFullAccount mocks base method.
func (m *MockPaymentAccountServer) CheckFullAccount(ctx context.Context, in *CheckFullAccountRequest) (*CheckFullAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFullAccount", ctx, in)
	ret0, _ := ret[0].(*CheckFullAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFullAccount indicates an expected call of CheckFullAccount.
func (mr *MockPaymentAccountServerMockRecorder) CheckFullAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFullAccount", reflect.TypeOf((*MockPaymentAccountServer)(nil).CheckFullAccount), ctx, in)
}

// GetBasicAccount mocks base method.
func (m *MockPaymentAccountServer) GetBasicAccount(ctx context.Context, in *GetBasicAccountRequest) (*GetBasicAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBasicAccount", ctx, in)
	ret0, _ := ret[0].(*GetBasicAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasicAccount indicates an expected call of GetBasicAccount.
func (mr *MockPaymentAccountServerMockRecorder) GetBasicAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasicAccount", reflect.TypeOf((*MockPaymentAccountServer)(nil).GetBasicAccount), ctx, in)
}

// GetPaymentAccount mocks base method.
func (m *MockPaymentAccountServer) GetPaymentAccount(ctx context.Context, in *GetPaymentAccountRequest) (*GetPaymentAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentAccount", ctx, in)
	ret0, _ := ret[0].(*GetPaymentAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentAccount indicates an expected call of GetPaymentAccount.
func (mr *MockPaymentAccountServerMockRecorder) GetPaymentAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentAccount", reflect.TypeOf((*MockPaymentAccountServer)(nil).GetPaymentAccount), ctx, in)
}
