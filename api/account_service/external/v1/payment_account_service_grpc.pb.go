// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: account_service/external/v1/payment_account_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PaymentAccount_CheckFullAccount_FullMethodName  = "/account_service.external.v1.PaymentAccount/CheckFullAccount"
	PaymentAccount_GetBasicAccount_FullMethodName   = "/account_service.external.v1.PaymentAccount/GetBasicAccount"
	PaymentAccount_GetPaymentAccount_FullMethodName = "/account_service.external.v1.PaymentAccount/GetPaymentAccount"
)

// PaymentAccountClient is the client API for PaymentAccount service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentAccountClient interface {
	CheckFullAccount(ctx context.Context, in *CheckFullAccountRequest, opts ...grpc.CallOption) (*CheckFullAccountResponse, error)
	GetBasicAccount(ctx context.Context, in *GetBasicAccountRequest, opts ...grpc.CallOption) (*GetBasicAccountResponse, error)
	GetPaymentAccount(ctx context.Context, in *GetPaymentAccountRequest, opts ...grpc.CallOption) (*GetPaymentAccountResponse, error)
}

type paymentAccountClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentAccountClient(cc grpc.ClientConnInterface) PaymentAccountClient {
	return &paymentAccountClient{cc}
}

func (c *paymentAccountClient) CheckFullAccount(ctx context.Context, in *CheckFullAccountRequest, opts ...grpc.CallOption) (*CheckFullAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckFullAccountResponse)
	err := c.cc.Invoke(ctx, PaymentAccount_CheckFullAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAccountClient) GetBasicAccount(ctx context.Context, in *GetBasicAccountRequest, opts ...grpc.CallOption) (*GetBasicAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBasicAccountResponse)
	err := c.cc.Invoke(ctx, PaymentAccount_GetBasicAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentAccountClient) GetPaymentAccount(ctx context.Context, in *GetPaymentAccountRequest, opts ...grpc.CallOption) (*GetPaymentAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentAccountResponse)
	err := c.cc.Invoke(ctx, PaymentAccount_GetPaymentAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentAccountServer is the server API for PaymentAccount service.
// All implementations must embed UnimplementedPaymentAccountServer
// for forward compatibility.
type PaymentAccountServer interface {
	CheckFullAccount(context.Context, *CheckFullAccountRequest) (*CheckFullAccountResponse, error)
	GetBasicAccount(context.Context, *GetBasicAccountRequest) (*GetBasicAccountResponse, error)
	GetPaymentAccount(context.Context, *GetPaymentAccountRequest) (*GetPaymentAccountResponse, error)
	mustEmbedUnimplementedPaymentAccountServer()
}

// UnimplementedPaymentAccountServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPaymentAccountServer struct{}

func (UnimplementedPaymentAccountServer) CheckFullAccount(context.Context, *CheckFullAccountRequest) (*CheckFullAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFullAccount not implemented")
}
func (UnimplementedPaymentAccountServer) GetBasicAccount(context.Context, *GetBasicAccountRequest) (*GetBasicAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBasicAccount not implemented")
}
func (UnimplementedPaymentAccountServer) GetPaymentAccount(context.Context, *GetPaymentAccountRequest) (*GetPaymentAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentAccount not implemented")
}
func (UnimplementedPaymentAccountServer) mustEmbedUnimplementedPaymentAccountServer() {}
func (UnimplementedPaymentAccountServer) testEmbeddedByValue()                        {}

// UnsafePaymentAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentAccountServer will
// result in compilation errors.
type UnsafePaymentAccountServer interface {
	mustEmbedUnimplementedPaymentAccountServer()
}

func RegisterPaymentAccountServer(s grpc.ServiceRegistrar, srv PaymentAccountServer) {
	// If the following call pancis, it indicates UnimplementedPaymentAccountServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PaymentAccount_ServiceDesc, srv)
}

func _PaymentAccount_CheckFullAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFullAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAccountServer).CheckFullAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAccount_CheckFullAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAccountServer).CheckFullAccount(ctx, req.(*CheckFullAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAccount_GetBasicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBasicAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAccountServer).GetBasicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAccount_GetBasicAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAccountServer).GetBasicAccount(ctx, req.(*GetBasicAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentAccount_GetPaymentAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentAccountServer).GetPaymentAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PaymentAccount_GetPaymentAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentAccountServer).GetPaymentAccount(ctx, req.(*GetPaymentAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentAccount_ServiceDesc is the grpc.ServiceDesc for PaymentAccount service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentAccount_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "account_service.external.v1.PaymentAccount",
	HandlerType: (*PaymentAccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckFullAccount",
			Handler:    _PaymentAccount_CheckFullAccount_Handler,
		},
		{
			MethodName: "GetBasicAccount",
			Handler:    _PaymentAccount_GetBasicAccount_Handler,
		},
		{
			MethodName: "GetPaymentAccount",
			Handler:    _PaymentAccount_GetPaymentAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "account_service/external/v1/payment_account_service.proto",
}
