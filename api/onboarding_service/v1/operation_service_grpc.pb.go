// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: onboarding_service/v1/operation_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Operation_TriggerSyncOnboarding_FullMethodName     = "/onboarding_service.v1.Operation/TriggerSyncOnboarding"
	Operation_TriggerSubmitSelfieJob_FullMethodName    = "/onboarding_service.v1.Operation/TriggerSubmitSelfieJob"
	Operation_TriggerContractSigningJob_FullMethodName = "/onboarding_service.v1.Operation/TriggerContractSigningJob"
	Operation_TriggerSyncOnboardingJob_FullMethodName  = "/onboarding_service.v1.Operation/TriggerSyncOnboardingJob"
	Operation_TriggerSyncOnboardingsJob_FullMethodName = "/onboarding_service.v1.Operation/TriggerSyncOnboardingsJob"
)

// OperationClient is the client API for Operation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OperationClient interface {
	TriggerSyncOnboarding(ctx context.Context, in *TriggerSyncOnboardingRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingResponse, error)
	// Deprecated: Do not use.
	TriggerSubmitSelfieJob(ctx context.Context, in *TriggerSubmitSelfieJobRequest, opts ...grpc.CallOption) (*TriggerSubmitSelfieJobResponse, error)
	TriggerContractSigningJob(ctx context.Context, in *TriggerContractSigningJobRequest, opts ...grpc.CallOption) (*TriggerContractSigningJobResponse, error)
	TriggerSyncOnboardingJob(ctx context.Context, in *TriggerSyncOnboardingJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingJobResponse, error)
	TriggerSyncOnboardingsJob(ctx context.Context, in *TriggerSyncOnboardingsJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingsJobResponse, error)
}

type operationClient struct {
	cc grpc.ClientConnInterface
}

func NewOperationClient(cc grpc.ClientConnInterface) OperationClient {
	return &operationClient{cc}
}

func (c *operationClient) TriggerSyncOnboarding(ctx context.Context, in *TriggerSyncOnboardingRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSyncOnboardingResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSyncOnboarding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *operationClient) TriggerSubmitSelfieJob(ctx context.Context, in *TriggerSubmitSelfieJobRequest, opts ...grpc.CallOption) (*TriggerSubmitSelfieJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSubmitSelfieJobResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSubmitSelfieJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) TriggerContractSigningJob(ctx context.Context, in *TriggerContractSigningJobRequest, opts ...grpc.CallOption) (*TriggerContractSigningJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerContractSigningJobResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerContractSigningJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) TriggerSyncOnboardingJob(ctx context.Context, in *TriggerSyncOnboardingJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSyncOnboardingJobResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSyncOnboardingJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) TriggerSyncOnboardingsJob(ctx context.Context, in *TriggerSyncOnboardingsJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingsJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSyncOnboardingsJobResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSyncOnboardingsJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OperationServer is the server API for Operation service.
// All implementations must embed UnimplementedOperationServer
// for forward compatibility.
type OperationServer interface {
	TriggerSyncOnboarding(context.Context, *TriggerSyncOnboardingRequest) (*TriggerSyncOnboardingResponse, error)
	// Deprecated: Do not use.
	TriggerSubmitSelfieJob(context.Context, *TriggerSubmitSelfieJobRequest) (*TriggerSubmitSelfieJobResponse, error)
	TriggerContractSigningJob(context.Context, *TriggerContractSigningJobRequest) (*TriggerContractSigningJobResponse, error)
	TriggerSyncOnboardingJob(context.Context, *TriggerSyncOnboardingJobRequest) (*TriggerSyncOnboardingJobResponse, error)
	TriggerSyncOnboardingsJob(context.Context, *TriggerSyncOnboardingsJobRequest) (*TriggerSyncOnboardingsJobResponse, error)
	mustEmbedUnimplementedOperationServer()
}

// UnimplementedOperationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOperationServer struct{}

func (UnimplementedOperationServer) TriggerSyncOnboarding(context.Context, *TriggerSyncOnboardingRequest) (*TriggerSyncOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSyncOnboarding not implemented")
}
func (UnimplementedOperationServer) TriggerSubmitSelfieJob(context.Context, *TriggerSubmitSelfieJobRequest) (*TriggerSubmitSelfieJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSubmitSelfieJob not implemented")
}
func (UnimplementedOperationServer) TriggerContractSigningJob(context.Context, *TriggerContractSigningJobRequest) (*TriggerContractSigningJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerContractSigningJob not implemented")
}
func (UnimplementedOperationServer) TriggerSyncOnboardingJob(context.Context, *TriggerSyncOnboardingJobRequest) (*TriggerSyncOnboardingJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSyncOnboardingJob not implemented")
}
func (UnimplementedOperationServer) TriggerSyncOnboardingsJob(context.Context, *TriggerSyncOnboardingsJobRequest) (*TriggerSyncOnboardingsJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSyncOnboardingsJob not implemented")
}
func (UnimplementedOperationServer) mustEmbedUnimplementedOperationServer() {}
func (UnimplementedOperationServer) testEmbeddedByValue()                   {}

// UnsafeOperationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OperationServer will
// result in compilation errors.
type UnsafeOperationServer interface {
	mustEmbedUnimplementedOperationServer()
}

func RegisterOperationServer(s grpc.ServiceRegistrar, srv OperationServer) {
	// If the following call pancis, it indicates UnimplementedOperationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Operation_ServiceDesc, srv)
}

func _Operation_TriggerSyncOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSyncOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSyncOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSyncOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSyncOnboarding(ctx, req.(*TriggerSyncOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerSubmitSelfieJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSubmitSelfieJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSubmitSelfieJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSubmitSelfieJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSubmitSelfieJob(ctx, req.(*TriggerSubmitSelfieJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerContractSigningJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerContractSigningJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerContractSigningJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerContractSigningJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerContractSigningJob(ctx, req.(*TriggerContractSigningJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerSyncOnboardingJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSyncOnboardingJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSyncOnboardingJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSyncOnboardingJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSyncOnboardingJob(ctx, req.(*TriggerSyncOnboardingJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerSyncOnboardingsJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSyncOnboardingsJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSyncOnboardingsJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSyncOnboardingsJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSyncOnboardingsJob(ctx, req.(*TriggerSyncOnboardingsJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Operation_ServiceDesc is the grpc.ServiceDesc for Operation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Operation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "onboarding_service.v1.Operation",
	HandlerType: (*OperationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TriggerSyncOnboarding",
			Handler:    _Operation_TriggerSyncOnboarding_Handler,
		},
		{
			MethodName: "TriggerSubmitSelfieJob",
			Handler:    _Operation_TriggerSubmitSelfieJob_Handler,
		},
		{
			MethodName: "TriggerContractSigningJob",
			Handler:    _Operation_TriggerContractSigningJob_Handler,
		},
		{
			MethodName: "TriggerSyncOnboardingJob",
			Handler:    _Operation_TriggerSyncOnboardingJob_Handler,
		},
		{
			MethodName: "TriggerSyncOnboardingsJob",
			Handler:    _Operation_TriggerSyncOnboardingsJob_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "onboarding_service/v1/operation_service.proto",
}
