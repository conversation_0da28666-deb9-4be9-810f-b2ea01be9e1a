// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: onboarding_service/v1/onboarding_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationOnboardingGetClientContract = "/onboarding_service.v1.Onboarding/GetClientContract"
const OperationOnboardingGetClientEkycProfile = "/onboarding_service.v1.Onboarding/GetClientEkycProfile"
const OperationOnboardingGetClientOnboarding = "/onboarding_service.v1.Onboarding/GetClientOnboarding"
const OperationOnboardingGetClientPermissions = "/onboarding_service.v1.Onboarding/GetClientPermissions"
const OperationOnboardingGetClientRejection = "/onboarding_service.v1.Onboarding/GetClientRejection"
const OperationOnboardingGetClientResources = "/onboarding_service.v1.Onboarding/GetClientResources"
const OperationOnboardingLinkingClientAccount = "/onboarding_service.v1.Onboarding/LinkingClientAccount"
const OperationOnboardingListClientOnboarding = "/onboarding_service.v1.Onboarding/ListClientOnboarding"
const OperationOnboardingRegisterClientOnboarding = "/onboarding_service.v1.Onboarding/RegisterClientOnboarding"
const OperationOnboardingReinitClientOnboarding = "/onboarding_service.v1.Onboarding/ReinitClientOnboarding"
const OperationOnboardingRequestClientOTP = "/onboarding_service.v1.Onboarding/RequestClientOTP"
const OperationOnboardingResetClientEkycNfc = "/onboarding_service.v1.Onboarding/ResetClientEkycNfc"
const OperationOnboardingSubmitClientApplication = "/onboarding_service.v1.Onboarding/SubmitClientApplication"
const OperationOnboardingSubmitClientFaceChallenge = "/onboarding_service.v1.Onboarding/SubmitClientFaceChallenge"
const OperationOnboardingSubmitClientWetSign = "/onboarding_service.v1.Onboarding/SubmitClientWetSign"
const OperationOnboardingVerifyClientOTP = "/onboarding_service.v1.Onboarding/VerifyClientOTP"

type OnboardingHTTPServer interface {
	GetClientContract(context.Context, *GetClientContractRequest) (*GetClientContractResponse, error)
	GetClientEkycProfile(context.Context, *GetClientEkycProfileRequest) (*GetClientEkycProfileResponse, error)
	GetClientOnboarding(context.Context, *GetClientOnboardingRequest) (*GetClientOnboardingResponse, error)
	GetClientPermissions(context.Context, *GetClientPermissionsRequest) (*GetClientPermissionsResponse, error)
	GetClientRejection(context.Context, *GetClientRejectionRequest) (*GetClientRejectionResponse, error)
	GetClientResources(context.Context, *GetClientResourcesRequest) (*GetClientResourcesResponse, error)
	LinkingClientAccount(context.Context, *LinkingClientAccountRequest) (*LinkingClientAccountResponse, error)
	ListClientOnboarding(context.Context, *ListClientOnboardingRequest) (*ListClientOnboardingResponse, error)
	RegisterClientOnboarding(context.Context, *RegisterClientOnboardingRequest) (*RegisterClientOnboardingResponse, error)
	ReinitClientOnboarding(context.Context, *ReinitClientOnboardingRequest) (*ReinitClientOnboardingResponse, error)
	RequestClientOTP(context.Context, *RequestClientOTPRequest) (*RequestClientOTPResponse, error)
	ResetClientEkycNfc(context.Context, *ResetClientEkycNfcRequest) (*ResetClientEkycNfcResponse, error)
	SubmitClientApplication(context.Context, *SubmitClientApplicationRequest) (*SubmitClientApplicationResponse, error)
	SubmitClientFaceChallenge(context.Context, *SubmitClientFaceChallengeRequest) (*SubmitClientFaceChallengeResponse, error)
	SubmitClientWetSign(context.Context, *SubmitClientWetSignRequest) (*SubmitClientWetSignResponse, error)
	VerifyClientOTP(context.Context, *VerifyClientOTPRequest) (*VerifyClientOTPResponse, error)
}

func RegisterOnboardingHTTPServer(s *http.Server, srv OnboardingHTTPServer) {
	r := s.Route("/")
	r.GET("/onboarding/v1/users/permissions", _Onboarding_GetClientPermissions0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/users/ekyc-profile", _Onboarding_GetClientEkycProfile0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/users/ekyc-nfc/reset", _Onboarding_ResetClientEkycNfc0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/register", _Onboarding_RegisterClientOnboarding0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/onboardings/{onboarding_id}", _Onboarding_GetClientOnboarding0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/onboardings", _Onboarding_ListClientOnboarding0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/application", _Onboarding_SubmitClientApplication0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/wet-sign", _Onboarding_SubmitClientWetSign0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/face-challenge", _Onboarding_SubmitClientFaceChallenge0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/request-otp", _Onboarding_RequestClientOTP0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/verify-otp", _Onboarding_VerifyClientOTP0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/resources", _Onboarding_GetClientResources0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/contract", _Onboarding_GetClientContract0_HTTP_Handler(srv))
	r.GET("/onboarding/v1/rejection", _Onboarding_GetClientRejection0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/reinitiate", _Onboarding_ReinitClientOnboarding0_HTTP_Handler(srv))
	r.POST("/onboarding/v1/link-account", _Onboarding_LinkingClientAccount0_HTTP_Handler(srv))
}

func _Onboarding_GetClientPermissions0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientPermissionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientPermissions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientPermissions(ctx, req.(*GetClientPermissionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientPermissionsResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_GetClientEkycProfile0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientEkycProfileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientEkycProfile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientEkycProfile(ctx, req.(*GetClientEkycProfileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientEkycProfileResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_ResetClientEkycNfc0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResetClientEkycNfcRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingResetClientEkycNfc)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResetClientEkycNfc(ctx, req.(*ResetClientEkycNfcRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResetClientEkycNfcResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_RegisterClientOnboarding0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegisterClientOnboardingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingRegisterClientOnboarding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegisterClientOnboarding(ctx, req.(*RegisterClientOnboardingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegisterClientOnboardingResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_GetClientOnboarding0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientOnboardingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientOnboarding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientOnboarding(ctx, req.(*GetClientOnboardingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientOnboardingResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_ListClientOnboarding0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClientOnboardingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingListClientOnboarding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClientOnboarding(ctx, req.(*ListClientOnboardingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClientOnboardingResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_SubmitClientApplication0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubmitClientApplicationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingSubmitClientApplication)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitClientApplication(ctx, req.(*SubmitClientApplicationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubmitClientApplicationResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_SubmitClientWetSign0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubmitClientWetSignRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingSubmitClientWetSign)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitClientWetSign(ctx, req.(*SubmitClientWetSignRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubmitClientWetSignResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_SubmitClientFaceChallenge0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubmitClientFaceChallengeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingSubmitClientFaceChallenge)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitClientFaceChallenge(ctx, req.(*SubmitClientFaceChallengeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubmitClientFaceChallengeResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_RequestClientOTP0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RequestClientOTPRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingRequestClientOTP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RequestClientOTP(ctx, req.(*RequestClientOTPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RequestClientOTPResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_VerifyClientOTP0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VerifyClientOTPRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingVerifyClientOTP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VerifyClientOTP(ctx, req.(*VerifyClientOTPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VerifyClientOTPResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_GetClientResources0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientResourcesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientResources)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientResources(ctx, req.(*GetClientResourcesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientResourcesResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_GetClientContract0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientContractRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientContract)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientContract(ctx, req.(*GetClientContractRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientContractResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_GetClientRejection0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientRejectionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingGetClientRejection)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientRejection(ctx, req.(*GetClientRejectionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientRejectionResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_ReinitClientOnboarding0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReinitClientOnboardingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingReinitClientOnboarding)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReinitClientOnboarding(ctx, req.(*ReinitClientOnboardingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReinitClientOnboardingResponse)
		return ctx.Result(200, reply)
	}
}

func _Onboarding_LinkingClientAccount0_HTTP_Handler(srv OnboardingHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LinkingClientAccountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOnboardingLinkingClientAccount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LinkingClientAccount(ctx, req.(*LinkingClientAccountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LinkingClientAccountResponse)
		return ctx.Result(200, reply)
	}
}

type OnboardingHTTPClient interface {
	GetClientContract(ctx context.Context, req *GetClientContractRequest, opts ...http.CallOption) (rsp *GetClientContractResponse, err error)
	GetClientEkycProfile(ctx context.Context, req *GetClientEkycProfileRequest, opts ...http.CallOption) (rsp *GetClientEkycProfileResponse, err error)
	GetClientOnboarding(ctx context.Context, req *GetClientOnboardingRequest, opts ...http.CallOption) (rsp *GetClientOnboardingResponse, err error)
	GetClientPermissions(ctx context.Context, req *GetClientPermissionsRequest, opts ...http.CallOption) (rsp *GetClientPermissionsResponse, err error)
	GetClientRejection(ctx context.Context, req *GetClientRejectionRequest, opts ...http.CallOption) (rsp *GetClientRejectionResponse, err error)
	GetClientResources(ctx context.Context, req *GetClientResourcesRequest, opts ...http.CallOption) (rsp *GetClientResourcesResponse, err error)
	LinkingClientAccount(ctx context.Context, req *LinkingClientAccountRequest, opts ...http.CallOption) (rsp *LinkingClientAccountResponse, err error)
	ListClientOnboarding(ctx context.Context, req *ListClientOnboardingRequest, opts ...http.CallOption) (rsp *ListClientOnboardingResponse, err error)
	RegisterClientOnboarding(ctx context.Context, req *RegisterClientOnboardingRequest, opts ...http.CallOption) (rsp *RegisterClientOnboardingResponse, err error)
	ReinitClientOnboarding(ctx context.Context, req *ReinitClientOnboardingRequest, opts ...http.CallOption) (rsp *ReinitClientOnboardingResponse, err error)
	RequestClientOTP(ctx context.Context, req *RequestClientOTPRequest, opts ...http.CallOption) (rsp *RequestClientOTPResponse, err error)
	ResetClientEkycNfc(ctx context.Context, req *ResetClientEkycNfcRequest, opts ...http.CallOption) (rsp *ResetClientEkycNfcResponse, err error)
	SubmitClientApplication(ctx context.Context, req *SubmitClientApplicationRequest, opts ...http.CallOption) (rsp *SubmitClientApplicationResponse, err error)
	SubmitClientFaceChallenge(ctx context.Context, req *SubmitClientFaceChallengeRequest, opts ...http.CallOption) (rsp *SubmitClientFaceChallengeResponse, err error)
	SubmitClientWetSign(ctx context.Context, req *SubmitClientWetSignRequest, opts ...http.CallOption) (rsp *SubmitClientWetSignResponse, err error)
	VerifyClientOTP(ctx context.Context, req *VerifyClientOTPRequest, opts ...http.CallOption) (rsp *VerifyClientOTPResponse, err error)
}

type OnboardingHTTPClientImpl struct {
	cc *http.Client
}

func NewOnboardingHTTPClient(client *http.Client) OnboardingHTTPClient {
	return &OnboardingHTTPClientImpl{client}
}

func (c *OnboardingHTTPClientImpl) GetClientContract(ctx context.Context, in *GetClientContractRequest, opts ...http.CallOption) (*GetClientContractResponse, error) {
	var out GetClientContractResponse
	pattern := "/onboarding/v1/contract"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientContract))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) GetClientEkycProfile(ctx context.Context, in *GetClientEkycProfileRequest, opts ...http.CallOption) (*GetClientEkycProfileResponse, error) {
	var out GetClientEkycProfileResponse
	pattern := "/onboarding/v1/users/ekyc-profile"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientEkycProfile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) GetClientOnboarding(ctx context.Context, in *GetClientOnboardingRequest, opts ...http.CallOption) (*GetClientOnboardingResponse, error) {
	var out GetClientOnboardingResponse
	pattern := "/onboarding/v1/onboardings/{onboarding_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientOnboarding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) GetClientPermissions(ctx context.Context, in *GetClientPermissionsRequest, opts ...http.CallOption) (*GetClientPermissionsResponse, error) {
	var out GetClientPermissionsResponse
	pattern := "/onboarding/v1/users/permissions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientPermissions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) GetClientRejection(ctx context.Context, in *GetClientRejectionRequest, opts ...http.CallOption) (*GetClientRejectionResponse, error) {
	var out GetClientRejectionResponse
	pattern := "/onboarding/v1/rejection"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientRejection))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) GetClientResources(ctx context.Context, in *GetClientResourcesRequest, opts ...http.CallOption) (*GetClientResourcesResponse, error) {
	var out GetClientResourcesResponse
	pattern := "/onboarding/v1/resources"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingGetClientResources))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) LinkingClientAccount(ctx context.Context, in *LinkingClientAccountRequest, opts ...http.CallOption) (*LinkingClientAccountResponse, error) {
	var out LinkingClientAccountResponse
	pattern := "/onboarding/v1/link-account"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingLinkingClientAccount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) ListClientOnboarding(ctx context.Context, in *ListClientOnboardingRequest, opts ...http.CallOption) (*ListClientOnboardingResponse, error) {
	var out ListClientOnboardingResponse
	pattern := "/onboarding/v1/onboardings"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOnboardingListClientOnboarding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) RegisterClientOnboarding(ctx context.Context, in *RegisterClientOnboardingRequest, opts ...http.CallOption) (*RegisterClientOnboardingResponse, error) {
	var out RegisterClientOnboardingResponse
	pattern := "/onboarding/v1/register"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingRegisterClientOnboarding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) ReinitClientOnboarding(ctx context.Context, in *ReinitClientOnboardingRequest, opts ...http.CallOption) (*ReinitClientOnboardingResponse, error) {
	var out ReinitClientOnboardingResponse
	pattern := "/onboarding/v1/reinitiate"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingReinitClientOnboarding))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) RequestClientOTP(ctx context.Context, in *RequestClientOTPRequest, opts ...http.CallOption) (*RequestClientOTPResponse, error) {
	var out RequestClientOTPResponse
	pattern := "/onboarding/v1/request-otp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingRequestClientOTP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) ResetClientEkycNfc(ctx context.Context, in *ResetClientEkycNfcRequest, opts ...http.CallOption) (*ResetClientEkycNfcResponse, error) {
	var out ResetClientEkycNfcResponse
	pattern := "/onboarding/v1/users/ekyc-nfc/reset"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingResetClientEkycNfc))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) SubmitClientApplication(ctx context.Context, in *SubmitClientApplicationRequest, opts ...http.CallOption) (*SubmitClientApplicationResponse, error) {
	var out SubmitClientApplicationResponse
	pattern := "/onboarding/v1/application"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingSubmitClientApplication))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) SubmitClientFaceChallenge(ctx context.Context, in *SubmitClientFaceChallengeRequest, opts ...http.CallOption) (*SubmitClientFaceChallengeResponse, error) {
	var out SubmitClientFaceChallengeResponse
	pattern := "/onboarding/v1/face-challenge"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingSubmitClientFaceChallenge))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) SubmitClientWetSign(ctx context.Context, in *SubmitClientWetSignRequest, opts ...http.CallOption) (*SubmitClientWetSignResponse, error) {
	var out SubmitClientWetSignResponse
	pattern := "/onboarding/v1/wet-sign"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingSubmitClientWetSign))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OnboardingHTTPClientImpl) VerifyClientOTP(ctx context.Context, in *VerifyClientOTPRequest, opts ...http.CallOption) (*VerifyClientOTPResponse, error) {
	var out VerifyClientOTPResponse
	pattern := "/onboarding/v1/verify-otp"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOnboardingVerifyClientOTP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
