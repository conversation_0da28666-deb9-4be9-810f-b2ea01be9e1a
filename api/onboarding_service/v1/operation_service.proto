syntax = "proto3";

package onboarding_service.v1;

option go_package = "installment/api/onboarding-service/v1;v1";

import "validate/validate.proto";

service Operation {
  rpc TriggerSyncOnboarding(TriggerSyncOnboardingRequest) returns (TriggerSyncOnboardingResponse) {}
  rpc TriggerSubmitSelfieJob(TriggerSubmitSelfieJobRequest) returns (TriggerSubmitSelfieJobResponse){
    option deprecated = true;
  }
  rpc TriggerContractSigningJob(TriggerContractSigningJobRequest) returns (TriggerContractSigningJobResponse) {}
  rpc TriggerSyncOnboardingJob(TriggerSyncOnboardingJobRequest) returns (TriggerSyncOnboardingJobResponse) {}
  rpc TriggerSyncOnboardingsJob(TriggerSyncOnboardingsJobRequest) returns (TriggerSyncOnboardingsJobResponse) {}
}

message TriggerSyncOnboardingRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
}

message TriggerSyncOnboardingResponse {
}

message TriggerSyncOnboardingJobRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
}

message TriggerSyncOnboardingJobResponse {
}

message TriggerSubmitSelfieJobRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
}

message TriggerSubmitSelfieJobResponse {
}

message TriggerSyncOnboardingsJobRequest {
  repeated OnboardingOp data = 1 [(validate.rules).repeated.min_items = 1];
}

message TriggerSyncOnboardingsJobResponse {
}

message TriggerContractSigningJobRequest {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
}

message TriggerContractSigningJobResponse {
}

message OnboardingOp {
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  int64 onboarding_id = 2 [(validate.rules).int64.gt = 0];
}
