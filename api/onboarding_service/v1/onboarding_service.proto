syntax = "proto3";

package onboarding_service.v1;

option go_package = "installment/api/onboarding-service/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

service Onboarding {
	rpc GetClientPermissions(GetClientPermissionsRequest) returns (GetClientPermissionsResponse) {
		option (google.api.http) = {
			get: "/onboarding/v1/users/permissions",
		};
	}
	rpc GetClientEkycProfile(GetClientEkycProfileRequest) returns (GetClientEkycProfileResponse) {
		option (google.api.http) = {
			get: "/onboarding/v1/users/ekyc-profile",
		};
	}
	rpc ResetClientEkycNfc(ResetClientEkycNfcRequest) returns (ResetClientEkycNfcResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/users/ekyc-nfc/reset",
			body: "*",
		};
	}
	rpc RegisterClientOnboarding(RegisterClientOnboardingRequest) returns (RegisterClientOnboardingResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/register",
			body: "*",
		};
	}
	rpc GetClientOnboarding(GetClientOnboardingRequest) returns (GetClientOnboardingResponse) {
		option (google.api.http) = {
			get: "/onboarding/v1/onboardings/{onboarding_id}",
		};
	}
	rpc ListClientOnboarding(ListClientOnboardingRequest) returns (ListClientOnboardingResponse) {
		option (google.api.http) = {
			get: "/onboarding/v1/onboardings",
		};
	}
	rpc SubmitClientApplication(SubmitClientApplicationRequest) returns (SubmitClientApplicationResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/application",
			body: "*",
		};
	}
	rpc SubmitClientWetSign(SubmitClientWetSignRequest) returns (SubmitClientWetSignResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/wet-sign",
			body: "*",
		};
	}
	rpc SubmitClientFaceChallenge(SubmitClientFaceChallengeRequest) returns (SubmitClientFaceChallengeResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/face-challenge",
			body: "*",
		};
	}
	rpc RequestClientOTP(RequestClientOTPRequest) returns (RequestClientOTPResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/request-otp",
			body: "*",
		};
	}
	rpc VerifyClientOTP(VerifyClientOTPRequest) returns (VerifyClientOTPResponse) {
		option (google.api.http) = {
			post: "/onboarding/v1/verify-otp",
			body: "*",
		};
	}
	rpc GetClientResources(GetClientResourcesRequest) returns (GetClientResourcesResponse){
		option (google.api.http) = {
			get: "/onboarding/v1/resources",
		};
	}
	rpc GetClientContract(GetClientContractRequest) returns (GetClientContractResponse){
		option (google.api.http) = {
			get: "/onboarding/v1/contract",
		};
	}
	rpc GetClientRejection(GetClientRejectionRequest) returns (GetClientRejectionResponse){
		option (google.api.http) = {
			get: "/onboarding/v1/rejection",
		};
	}
	rpc ReinitClientOnboarding(ReinitClientOnboardingRequest) returns (ReinitClientOnboardingResponse){
		option (google.api.http) = {
			post: "/onboarding/v1/reinitiate",
			body: "*",
		};
	}
	rpc LinkingClientAccount(LinkingClientAccountRequest) returns (LinkingClientAccountResponse){
		option (google.api.http) = {
			post: "/onboarding/v1/link-account",
			body: "*",
		};
	}
	rpc QueryOnboardingStatus(QueryOnboardingStatusRequest) returns (QueryOnboardingStatusResponse){}
	rpc ListOnboardingByUserIDs(ListOnboardingByUserIDsRequest) returns (ListOnboardingByUserIDsResponse){}
}

message RegisterClientOnboardingRequest {
	optional string partner_code = 1 [json_name = "partner_code"];
}

message RegisterClientOnboardingResponse {
	int64 onboarding_id = 1 [json_name = "onboarding_id"];
}

message ReinitClientOnboardingRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
}

message ReinitClientOnboardingResponse {
}

message GetClientPermissionsRequest {
}

message GetClientPermissionsResponse {
	bool is_whitelisted = 1 [json_name = "is_whitelisted"];
	BindStatus bind_status =  2 [json_name = "bind_status"];
	KycStatusData kyc_status = 3 [json_name = "kyc_status"];
	RiskInfo risk_info = 4 [json_name = "risk_info"];

	/**
		* Binding info include some general info about user account and onboarding
		* If user not onboarded or not bind account, this field will be empty
	 */
	BindInfo bind_info = 5 [json_name = "bind_info"];
}

message GetClientEkycProfileRequest {
	bool need_verify_profile = 1 [json_name = "need_verify_profile"];
}

message GetClientEkycProfileResponse {
	UserProfile profile_info = 1 [json_name = "profile_info"];
	repeated ProfileIssue profile_issues = 2 [json_name = "profile_issues"];
}

message ResetClientEkycNfcRequest {
}

message ResetClientEkycNfcResponse {
}

message LinkingClientAccountRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
}

message LinkingClientAccountResponse {
}

message GetClientOnboardingRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
}

message GetClientOnboardingResponse {
	string status = 1 [json_name = "status"];
	string partner_code = 2 [json_name = "partner_code"];
	string current_step = 3 [json_name = "current_step"];
	string full_name = 4 [json_name = "full_name"];
	string gender = 5 [json_name = "gender"];
	string phone_number = 6 [json_name = "phone_number"];
	string id_number = 7 [json_name = "id_number"];
	string id_issue_date = 8 [json_name = "id_issue_date"];
	string id_issue_place = 9 [json_name = "id_issue_place"];
	string date_of_birth = 10 [json_name = "date_of_birth"];
	string permanent_address = 11 [json_name = "permanent_address"];
	string temp_residence_address = 12 [json_name = "temp_residence_address"];
}

message ListClientOnboardingRequest {}

message ListClientOnboardingResponse {
	message Onboarding {
		int64 id = 1 [json_name = "id"];
		string partner_code = 2 [json_name = "partner_code"];
		string status = 3 [json_name = "status"];
		string current_step = 4 [json_name = "current_step"];
		google.protobuf.Timestamp created_at = 5 [json_name = "created_at"];
		google.protobuf.Timestamp updated_at = 6 [json_name = "updated_at"];
		repeated string all_step = 7 [json_name = "all_step"];
		string next_step = 8 [json_name = "next_step"];
	}
	repeated Onboarding onboardings = 1;
}

message SubmitClientApplicationRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
	string job_title = 2 [json_name = "job_title"];
	string occupation = 3 [json_name = "occupation"];
	string living_city = 4 [json_name = "living_city"];
	string monthly_income = 5 [json_name = "monthly_income", (validate.rules).string.min_len = 1];
	string temp_residence_address = 6 [json_name = "temp_residence_address", (validate.rules).string.min_len = 1];
	string education = 7 [json_name = "education"];
	string source_of_fund = 8 [json_name = "source_of_fund"];
	string employment_status = 9 [json_name = "employment_status"];
}

message SubmitClientApplicationResponse {
	bool application_rejected = 1 [json_name = "application_rejected"];
	bool require_link_account = 2 [json_name = "require_link_account"];
}

message SubmitClientWetSignRequest {
	message SignImageData {
		string image_data = 1 [json_name = "image_data", (validate.rules).string.min_len = 1];
		string image_type = 2 [json_name = "image_type", (validate.rules).string.min_len = 1];
	}
	message SignImageUri {
		string image_url = 1 [json_name = "image_url", (validate.rules).string.min_len = 1];
		string image_type = 2 [json_name = "image_type", (validate.rules).string.min_len = 1];
	}

	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
	oneof sign_image_res {
		option (validate.required) = true;
		SignImageUri image_uri = 2 [json_name = "sign_image_uri"];
		SignImageData image_data = 3 [json_name = "sign_image_data"];
	}
}

message SubmitClientWetSignResponse {
	int64 onboarding_id = 1 [json_name = "onboarding_id"];
}

message SubmitClientFaceChallengeRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
	string face_request_id = 2 [json_name = "face_request_id", (validate.rules).string.min_len = 1];
}

message SubmitClientFaceChallengeResponse {
	int64 onboarding_id = 1 [json_name = "onboarding_id"];
}

enum OTPType {
	CONTRACT_SIGNING = 0;
	LINK_TYPE_3 = 1;
}

message RequestClientOTPRequest {
	OTPType otp_type = 1 [json_name = "otp_type"];
	int64 onboarding_id = 2 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
	string partner_code = 3 [json_name = "partner_code", (validate.rules).string.min_len = 1];
}

message RequestClientOTPResponse {
	int64 wait_time = 1 [json_name = "wait_time"];
	int64 resend_time = 2 [json_name = "resend_time"];
	bool status = 3 [json_name = "status"];
	string error_message = 4 [json_name = "error_message"];
	string phone_number = 5 [json_name = "phone_number"];
	string otp_request_id = 6 [json_name = "otp_request_id"];
	bool is_send_sms = 7 [json_name = "is_send_sms"];
}

message VerifyClientOTPRequest {
	string otp_code = 1 [json_name = "otp_code", (validate.rules).string.min_len = 1];
	OTPType otp_type = 2 [json_name = "otp_type"];
	int64 onboarding_id = 3 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
	string partner_code = 4 [json_name = "partner_code", (validate.rules).string.min_len = 1];
}

message VerifyClientOTPResponse {
	bool status = 1 [json_name = "status"];
}

message GetClientContractRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
}

message GetClientContractResponse {
	string unsigned_contract_url = 1 [json_name = "unsigned_contract_url"];
	string signed_contract_url = 2 [json_name = "signed_contract_url"];
}

message GetClientRejectionRequest {
	int64 onboarding_id = 1 [json_name = "onboarding_id", (validate.rules).int64.gt = 0];
}

message GetClientRejectionResponse {
	string code = 1;
	Content content = 2;
	repeated Action actions = 3;
}

message QueryOnboardingStatusRequest {
	int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
	string partner_code = 2 [(validate.rules).string.min_len = 1];
}

message QueryOnboardingStatusResponse {
	message Flags {
		bool is_unregister = 1;
		bool is_onboarding = 2;
		bool is_rejected = 3;
	}
	Flags flags = 1;
	int64 onboarding_id = 2;
	string current_step = 3;
	string partner_code = 4;
}

message ListOnboardingByUserIDsRequest {
	repeated int64 zalopay_ids = 1 [(validate.rules).repeated = {min_items: 1, max_items: 50}];
}

message ListOnboardingByUserIDsResponse {
	repeated OnboardingData onboardings = 1;
}

message GetClientResourcesRequest {
	repeated ResourceType resource_types = 1 [json_name = "resource_types", (validate.rules).repeated.min_items = 1];
}

message GetClientResourcesResponse {
	repeated CIMBResource resources = 1;
}

// OnboardingData is a base domain model to include all user onboarding information
message OnboardingData {
	int64 id = 1;
	int64 zalopay_id = 2;
	string status = 3;
	string partner_code = 4;
	string current_step = 5;
	string reject_code = 6;
	OnboardingProfile profile_info = 7;
	PartnerOnboarding partner_data = 8;
	optional OnboardingFlags status_flags = 9;
}

// OnboardingProfile is a profile that user use to submit onboarding request
message OnboardingProfile {
	string full_name = 1;
	string gender = 2;
	string phone_number = 3;
	string id_number = 4;
	string id_issue_date = 5;
	string id_issue_place = 6;
	string date_of_birth = 7;
	string permanent_address = 8;
	string temp_residence_address = 9;
}

message OnboardingFlags {
	bool is_unregister = 1;
	bool is_onboarding = 2;
	bool is_rejected = 3;
}

message PartnerOnboarding {
	optional CIMBOnboarding cimb_data = 1;
}

message CIMBOnboarding  {
  	string od_status = 1;
	string casa_status = 2;
	string sign_status = 3;
	string error_detail = 4;
	string manual_approval_reason	= 5;
}

message CIMBResource {
	string type = 1;
	repeated CIMBResourceData data = 2;
}

message CIMBResourceData {
	string code = 1;
	// Data description in vietnamese
	string vietnamese = 2;
	// Data description in english
	string english = 3;
}

// UserProfile is present zalopay user kyc profile information, not onboarding profile
message UserProfile {
	// User KYC level in ZaloPay system
	int32 kyc_level = 1 [json_name = "kyc_level"];
	// User profile level in ZaloPay system
	int32 profile_level = 2 [json_name = "profile_level"];
	// User registed fullname
	string full_name = 3 [json_name = "full_name"];
	// User registed gender
	Gender gender = 4 [json_name = "gender"];
	// User registed phone
	string phone_number = 5 [json_name = "phone_number"];
	// User registed address
	string permanent_address = 6 [json_name = "permanent_address"];
	// User registed id type | 1: CMND, 2: passport, 3: CCCD, 4: CMSQ, 5: CCCD gắn chip
	int32 id_type = 7 [json_name = "id_type"];
	// User registed id number
	string id_number = 8 [json_name = "id_number"];
	// User registed email
	string email = 9 [json_name = "email"];
	// User registed date of birth
	string date_of_birth = 10 [json_name = "date_of_birth"];
	// Id issued location
	string id_issue_place = 11 [json_name = "id_issue_place"];
	// Id issued date
	string id_issue_date = 12 [json_name = "id_issue_date"];
}

message Content {
	string title = 1 [json_name = "title"];
	string message = 2 [json_name = "message"];
}

message Action {
	string code = 1 [json_name = "code"];
	string title = 2 [json_name = "title"];
	string variant = 3 [json_name = "variant"];
	string zpa_action_url = 4 [json_name = "zpa_action_url"];
	string zpi_action_url = 5 [json_name = "zpi_action_url"];
	map<string, google.protobuf.Any> metadata = 6 [json_name = "metadata"];
}

message RejectInfo {
	string code = 1 [json_name = "code"];
	Notice notice = 2 [json_name = "notice"];
}

message RiskInfo {
	string code = 1 [json_name = "code"];
	string level = 2 [json_name = "level"];
	Notice notice = 3 [json_name = "notice"];
}

message ProfileIssue {
	string code = 1 [json_name = "code"];
	Notice notice = 2 [json_name = "notice"];
}

message KycStatusData{
	KycStatus status = 1 [json_name = "status"];
	Notice notice = 2 [json_name = "notice"];
}

message Notice {
	Content content = 1 [json_name = "content"];
	repeated Action actions = 2 [json_name = "actions"];
}

message BindInfo {
	// Binding onboarding id
	int64 onboarding_id = 1 [json_name = "onboarding_id"];
	// Binding account id
	int64 account_id = 2 [json_name = "account_id"];
	// Binding partner code
	string partner_code = 3 [json_name = "partner_code"];
}

enum Gender {
	UNKNOWN = 0;
	MALE = 1;
	FEMALE = 2;
}

enum BindStatus {
	BIND_STATUS_UNKNOWN = 0;
	BIND_STATUS_UNBOUND = 1;
	BIND_STATUS_BOUND = 2;
	BIND_STATUS_ONBOARDING = 3;
}

enum KycStatus {
	KYC_STATUS_INVALID = 0;
	KYC_STATUS_PROCESSING = 1;
	KYC_STATUS_APPROVE = 2;
	KYC_STATUS_REJECT = 3;
	KYC_STATUS_BYPASS = 4;
	KYC_STATUS_UNKNOWN = 5;
}

enum ResourceType {
	NATURE_OF_BUSINESS = 0;
	OCCUPATION = 1;
	JOB_TITLE = 2;
	CITY = 3;
	DISTRICT = 4;
	WARD = 5;
	GENDER = 6;
	SOURCE_OF_FUND = 7;
	MARITAL_STATUS = 8;
	EMPLOYMENT_STATUS = 9;
	GENERAL_TERM_AND_CONDITION = 10;
	COMPANY_TYPE = 11;
	LOAN_TERM_AND_CONDITION = 12;
	OPENING_CASA_PURPOSE = 13;
	NAPAS_BANK = 14;
	REFERENCE_CONTACT_TYPE = 15;
	CUSTOMER_TYPE = 16;
	EDUCATION = 17;
	LOAN_CANCEL_REASON = 18;
	INCOME = 19;
	FUND_PURPOSE = 20;
}
