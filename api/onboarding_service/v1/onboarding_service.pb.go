// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: onboarding_service/v1/onboarding_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OTPType int32

const (
	OTPType_CONTRACT_SIGNING OTPType = 0
	OTPType_LINK_TYPE_3      OTPType = 1
)

// Enum value maps for OTPType.
var (
	OTPType_name = map[int32]string{
		0: "CONTRACT_SIGNING",
		1: "LINK_TYPE_3",
	}
	OTPType_value = map[string]int32{
		"CONTRACT_SIGNING": 0,
		"LINK_TYPE_3":      1,
	}
)

func (x OTPType) Enum() *OTPType {
	p := new(OTPType)
	*p = x
	return p
}

func (x OTPType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OTPType) Descriptor() protoreflect.EnumDescriptor {
	return file_onboarding_service_v1_onboarding_service_proto_enumTypes[0].Descriptor()
}

func (OTPType) Type() protoreflect.EnumType {
	return &file_onboarding_service_v1_onboarding_service_proto_enumTypes[0]
}

func (x OTPType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OTPType.Descriptor instead.
func (OTPType) EnumDescriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{0}
}

type Gender int32

const (
	Gender_UNKNOWN Gender = 0
	Gender_MALE    Gender = 1
	Gender_FEMALE  Gender = 2
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_onboarding_service_v1_onboarding_service_proto_enumTypes[1].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_onboarding_service_v1_onboarding_service_proto_enumTypes[1]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{1}
}

type BindStatus int32

const (
	BindStatus_BIND_STATUS_UNKNOWN    BindStatus = 0
	BindStatus_BIND_STATUS_UNBOUND    BindStatus = 1
	BindStatus_BIND_STATUS_BOUND      BindStatus = 2
	BindStatus_BIND_STATUS_ONBOARDING BindStatus = 3
)

// Enum value maps for BindStatus.
var (
	BindStatus_name = map[int32]string{
		0: "BIND_STATUS_UNKNOWN",
		1: "BIND_STATUS_UNBOUND",
		2: "BIND_STATUS_BOUND",
		3: "BIND_STATUS_ONBOARDING",
	}
	BindStatus_value = map[string]int32{
		"BIND_STATUS_UNKNOWN":    0,
		"BIND_STATUS_UNBOUND":    1,
		"BIND_STATUS_BOUND":      2,
		"BIND_STATUS_ONBOARDING": 3,
	}
)

func (x BindStatus) Enum() *BindStatus {
	p := new(BindStatus)
	*p = x
	return p
}

func (x BindStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BindStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_onboarding_service_v1_onboarding_service_proto_enumTypes[2].Descriptor()
}

func (BindStatus) Type() protoreflect.EnumType {
	return &file_onboarding_service_v1_onboarding_service_proto_enumTypes[2]
}

func (x BindStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BindStatus.Descriptor instead.
func (BindStatus) EnumDescriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{2}
}

type KycStatus int32

const (
	KycStatus_KYC_STATUS_INVALID    KycStatus = 0
	KycStatus_KYC_STATUS_PROCESSING KycStatus = 1
	KycStatus_KYC_STATUS_APPROVE    KycStatus = 2
	KycStatus_KYC_STATUS_REJECT     KycStatus = 3
	KycStatus_KYC_STATUS_BYPASS     KycStatus = 4
	KycStatus_KYC_STATUS_UNKNOWN    KycStatus = 5
)

// Enum value maps for KycStatus.
var (
	KycStatus_name = map[int32]string{
		0: "KYC_STATUS_INVALID",
		1: "KYC_STATUS_PROCESSING",
		2: "KYC_STATUS_APPROVE",
		3: "KYC_STATUS_REJECT",
		4: "KYC_STATUS_BYPASS",
		5: "KYC_STATUS_UNKNOWN",
	}
	KycStatus_value = map[string]int32{
		"KYC_STATUS_INVALID":    0,
		"KYC_STATUS_PROCESSING": 1,
		"KYC_STATUS_APPROVE":    2,
		"KYC_STATUS_REJECT":     3,
		"KYC_STATUS_BYPASS":     4,
		"KYC_STATUS_UNKNOWN":    5,
	}
)

func (x KycStatus) Enum() *KycStatus {
	p := new(KycStatus)
	*p = x
	return p
}

func (x KycStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KycStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_onboarding_service_v1_onboarding_service_proto_enumTypes[3].Descriptor()
}

func (KycStatus) Type() protoreflect.EnumType {
	return &file_onboarding_service_v1_onboarding_service_proto_enumTypes[3]
}

func (x KycStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KycStatus.Descriptor instead.
func (KycStatus) EnumDescriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{3}
}

type ResourceType int32

const (
	ResourceType_NATURE_OF_BUSINESS         ResourceType = 0
	ResourceType_OCCUPATION                 ResourceType = 1
	ResourceType_JOB_TITLE                  ResourceType = 2
	ResourceType_CITY                       ResourceType = 3
	ResourceType_DISTRICT                   ResourceType = 4
	ResourceType_WARD                       ResourceType = 5
	ResourceType_GENDER                     ResourceType = 6
	ResourceType_SOURCE_OF_FUND             ResourceType = 7
	ResourceType_MARITAL_STATUS             ResourceType = 8
	ResourceType_EMPLOYMENT_STATUS          ResourceType = 9
	ResourceType_GENERAL_TERM_AND_CONDITION ResourceType = 10
	ResourceType_COMPANY_TYPE               ResourceType = 11
	ResourceType_LOAN_TERM_AND_CONDITION    ResourceType = 12
	ResourceType_OPENING_CASA_PURPOSE       ResourceType = 13
	ResourceType_NAPAS_BANK                 ResourceType = 14
	ResourceType_REFERENCE_CONTACT_TYPE     ResourceType = 15
	ResourceType_CUSTOMER_TYPE              ResourceType = 16
	ResourceType_EDUCATION                  ResourceType = 17
	ResourceType_LOAN_CANCEL_REASON         ResourceType = 18
	ResourceType_INCOME                     ResourceType = 19
	ResourceType_FUND_PURPOSE               ResourceType = 20
)

// Enum value maps for ResourceType.
var (
	ResourceType_name = map[int32]string{
		0:  "NATURE_OF_BUSINESS",
		1:  "OCCUPATION",
		2:  "JOB_TITLE",
		3:  "CITY",
		4:  "DISTRICT",
		5:  "WARD",
		6:  "GENDER",
		7:  "SOURCE_OF_FUND",
		8:  "MARITAL_STATUS",
		9:  "EMPLOYMENT_STATUS",
		10: "GENERAL_TERM_AND_CONDITION",
		11: "COMPANY_TYPE",
		12: "LOAN_TERM_AND_CONDITION",
		13: "OPENING_CASA_PURPOSE",
		14: "NAPAS_BANK",
		15: "REFERENCE_CONTACT_TYPE",
		16: "CUSTOMER_TYPE",
		17: "EDUCATION",
		18: "LOAN_CANCEL_REASON",
		19: "INCOME",
		20: "FUND_PURPOSE",
	}
	ResourceType_value = map[string]int32{
		"NATURE_OF_BUSINESS":         0,
		"OCCUPATION":                 1,
		"JOB_TITLE":                  2,
		"CITY":                       3,
		"DISTRICT":                   4,
		"WARD":                       5,
		"GENDER":                     6,
		"SOURCE_OF_FUND":             7,
		"MARITAL_STATUS":             8,
		"EMPLOYMENT_STATUS":          9,
		"GENERAL_TERM_AND_CONDITION": 10,
		"COMPANY_TYPE":               11,
		"LOAN_TERM_AND_CONDITION":    12,
		"OPENING_CASA_PURPOSE":       13,
		"NAPAS_BANK":                 14,
		"REFERENCE_CONTACT_TYPE":     15,
		"CUSTOMER_TYPE":              16,
		"EDUCATION":                  17,
		"LOAN_CANCEL_REASON":         18,
		"INCOME":                     19,
		"FUND_PURPOSE":               20,
	}
)

func (x ResourceType) Enum() *ResourceType {
	p := new(ResourceType)
	*p = x
	return p
}

func (x ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_onboarding_service_v1_onboarding_service_proto_enumTypes[4].Descriptor()
}

func (ResourceType) Type() protoreflect.EnumType {
	return &file_onboarding_service_v1_onboarding_service_proto_enumTypes[4]
}

func (x ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceType.Descriptor instead.
func (ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{4}
}

type RegisterClientOnboardingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   *string                `protobuf:"bytes,1,opt,name=partner_code,proto3,oneof" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterClientOnboardingRequest) Reset() {
	*x = RegisterClientOnboardingRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterClientOnboardingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterClientOnboardingRequest) ProtoMessage() {}

func (x *RegisterClientOnboardingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterClientOnboardingRequest.ProtoReflect.Descriptor instead.
func (*RegisterClientOnboardingRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterClientOnboardingRequest) GetPartnerCode() string {
	if x != nil && x.PartnerCode != nil {
		return *x.PartnerCode
	}
	return ""
}

type RegisterClientOnboardingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterClientOnboardingResponse) Reset() {
	*x = RegisterClientOnboardingResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterClientOnboardingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterClientOnboardingResponse) ProtoMessage() {}

func (x *RegisterClientOnboardingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterClientOnboardingResponse.ProtoReflect.Descriptor instead.
func (*RegisterClientOnboardingResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterClientOnboardingResponse) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type ReinitClientOnboardingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReinitClientOnboardingRequest) Reset() {
	*x = ReinitClientOnboardingRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReinitClientOnboardingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReinitClientOnboardingRequest) ProtoMessage() {}

func (x *ReinitClientOnboardingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReinitClientOnboardingRequest.ProtoReflect.Descriptor instead.
func (*ReinitClientOnboardingRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReinitClientOnboardingRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type ReinitClientOnboardingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReinitClientOnboardingResponse) Reset() {
	*x = ReinitClientOnboardingResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReinitClientOnboardingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReinitClientOnboardingResponse) ProtoMessage() {}

func (x *ReinitClientOnboardingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReinitClientOnboardingResponse.ProtoReflect.Descriptor instead.
func (*ReinitClientOnboardingResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{3}
}

type GetClientPermissionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientPermissionsRequest) Reset() {
	*x = GetClientPermissionsRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientPermissionsRequest) ProtoMessage() {}

func (x *GetClientPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientPermissionsRequest.ProtoReflect.Descriptor instead.
func (*GetClientPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{4}
}

type GetClientPermissionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsWhitelisted bool                   `protobuf:"varint,1,opt,name=is_whitelisted,proto3" json:"is_whitelisted,omitempty"`
	BindStatus    BindStatus             `protobuf:"varint,2,opt,name=bind_status,proto3,enum=onboarding_service.v1.BindStatus" json:"bind_status,omitempty"`
	KycStatus     *KycStatusData         `protobuf:"bytes,3,opt,name=kyc_status,proto3" json:"kyc_status,omitempty"`
	RiskInfo      *RiskInfo              `protobuf:"bytes,4,opt,name=risk_info,proto3" json:"risk_info,omitempty"`
	// *
	// Binding info include some general info about user account and onboarding
	// If user not onboarded or not bind account, this field will be empty
	BindInfo      *BindInfo `protobuf:"bytes,5,opt,name=bind_info,proto3" json:"bind_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientPermissionsResponse) Reset() {
	*x = GetClientPermissionsResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientPermissionsResponse) ProtoMessage() {}

func (x *GetClientPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientPermissionsResponse.ProtoReflect.Descriptor instead.
func (*GetClientPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetClientPermissionsResponse) GetIsWhitelisted() bool {
	if x != nil {
		return x.IsWhitelisted
	}
	return false
}

func (x *GetClientPermissionsResponse) GetBindStatus() BindStatus {
	if x != nil {
		return x.BindStatus
	}
	return BindStatus_BIND_STATUS_UNKNOWN
}

func (x *GetClientPermissionsResponse) GetKycStatus() *KycStatusData {
	if x != nil {
		return x.KycStatus
	}
	return nil
}

func (x *GetClientPermissionsResponse) GetRiskInfo() *RiskInfo {
	if x != nil {
		return x.RiskInfo
	}
	return nil
}

func (x *GetClientPermissionsResponse) GetBindInfo() *BindInfo {
	if x != nil {
		return x.BindInfo
	}
	return nil
}

type GetClientEkycProfileRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	NeedVerifyProfile bool                   `protobuf:"varint,1,opt,name=need_verify_profile,proto3" json:"need_verify_profile,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetClientEkycProfileRequest) Reset() {
	*x = GetClientEkycProfileRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientEkycProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientEkycProfileRequest) ProtoMessage() {}

func (x *GetClientEkycProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientEkycProfileRequest.ProtoReflect.Descriptor instead.
func (*GetClientEkycProfileRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetClientEkycProfileRequest) GetNeedVerifyProfile() bool {
	if x != nil {
		return x.NeedVerifyProfile
	}
	return false
}

type GetClientEkycProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProfileInfo   *UserProfile           `protobuf:"bytes,1,opt,name=profile_info,proto3" json:"profile_info,omitempty"`
	ProfileIssues []*ProfileIssue        `protobuf:"bytes,2,rep,name=profile_issues,proto3" json:"profile_issues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientEkycProfileResponse) Reset() {
	*x = GetClientEkycProfileResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientEkycProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientEkycProfileResponse) ProtoMessage() {}

func (x *GetClientEkycProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientEkycProfileResponse.ProtoReflect.Descriptor instead.
func (*GetClientEkycProfileResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetClientEkycProfileResponse) GetProfileInfo() *UserProfile {
	if x != nil {
		return x.ProfileInfo
	}
	return nil
}

func (x *GetClientEkycProfileResponse) GetProfileIssues() []*ProfileIssue {
	if x != nil {
		return x.ProfileIssues
	}
	return nil
}

type ResetClientEkycNfcRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetClientEkycNfcRequest) Reset() {
	*x = ResetClientEkycNfcRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetClientEkycNfcRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetClientEkycNfcRequest) ProtoMessage() {}

func (x *ResetClientEkycNfcRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetClientEkycNfcRequest.ProtoReflect.Descriptor instead.
func (*ResetClientEkycNfcRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{8}
}

type ResetClientEkycNfcResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetClientEkycNfcResponse) Reset() {
	*x = ResetClientEkycNfcResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetClientEkycNfcResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetClientEkycNfcResponse) ProtoMessage() {}

func (x *ResetClientEkycNfcResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetClientEkycNfcResponse.ProtoReflect.Descriptor instead.
func (*ResetClientEkycNfcResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{9}
}

type LinkingClientAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkingClientAccountRequest) Reset() {
	*x = LinkingClientAccountRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkingClientAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkingClientAccountRequest) ProtoMessage() {}

func (x *LinkingClientAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkingClientAccountRequest.ProtoReflect.Descriptor instead.
func (*LinkingClientAccountRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{10}
}

func (x *LinkingClientAccountRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type LinkingClientAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkingClientAccountResponse) Reset() {
	*x = LinkingClientAccountResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkingClientAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkingClientAccountResponse) ProtoMessage() {}

func (x *LinkingClientAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkingClientAccountResponse.ProtoReflect.Descriptor instead.
func (*LinkingClientAccountResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{11}
}

type GetClientOnboardingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientOnboardingRequest) Reset() {
	*x = GetClientOnboardingRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientOnboardingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientOnboardingRequest) ProtoMessage() {}

func (x *GetClientOnboardingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientOnboardingRequest.ProtoReflect.Descriptor instead.
func (*GetClientOnboardingRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetClientOnboardingRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type GetClientOnboardingResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Status               string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PartnerCode          string                 `protobuf:"bytes,2,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	CurrentStep          string                 `protobuf:"bytes,3,opt,name=current_step,proto3" json:"current_step,omitempty"`
	FullName             string                 `protobuf:"bytes,4,opt,name=full_name,proto3" json:"full_name,omitempty"`
	Gender               string                 `protobuf:"bytes,5,opt,name=gender,proto3" json:"gender,omitempty"`
	PhoneNumber          string                 `protobuf:"bytes,6,opt,name=phone_number,proto3" json:"phone_number,omitempty"`
	IdNumber             string                 `protobuf:"bytes,7,opt,name=id_number,proto3" json:"id_number,omitempty"`
	IdIssueDate          string                 `protobuf:"bytes,8,opt,name=id_issue_date,proto3" json:"id_issue_date,omitempty"`
	IdIssuePlace         string                 `protobuf:"bytes,9,opt,name=id_issue_place,proto3" json:"id_issue_place,omitempty"`
	DateOfBirth          string                 `protobuf:"bytes,10,opt,name=date_of_birth,proto3" json:"date_of_birth,omitempty"`
	PermanentAddress     string                 `protobuf:"bytes,11,opt,name=permanent_address,proto3" json:"permanent_address,omitempty"`
	TempResidenceAddress string                 `protobuf:"bytes,12,opt,name=temp_residence_address,proto3" json:"temp_residence_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetClientOnboardingResponse) Reset() {
	*x = GetClientOnboardingResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientOnboardingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientOnboardingResponse) ProtoMessage() {}

func (x *GetClientOnboardingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientOnboardingResponse.ProtoReflect.Descriptor instead.
func (*GetClientOnboardingResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetClientOnboardingResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetCurrentStep() string {
	if x != nil {
		return x.CurrentStep
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetIdNumber() string {
	if x != nil {
		return x.IdNumber
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetIdIssueDate() string {
	if x != nil {
		return x.IdIssueDate
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetIdIssuePlace() string {
	if x != nil {
		return x.IdIssuePlace
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

func (x *GetClientOnboardingResponse) GetTempResidenceAddress() string {
	if x != nil {
		return x.TempResidenceAddress
	}
	return ""
}

type ListClientOnboardingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientOnboardingRequest) Reset() {
	*x = ListClientOnboardingRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientOnboardingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientOnboardingRequest) ProtoMessage() {}

func (x *ListClientOnboardingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientOnboardingRequest.ProtoReflect.Descriptor instead.
func (*ListClientOnboardingRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{14}
}

type ListClientOnboardingResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Onboardings   []*ListClientOnboardingResponse_Onboarding `protobuf:"bytes,1,rep,name=onboardings,proto3" json:"onboardings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientOnboardingResponse) Reset() {
	*x = ListClientOnboardingResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientOnboardingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientOnboardingResponse) ProtoMessage() {}

func (x *ListClientOnboardingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientOnboardingResponse.ProtoReflect.Descriptor instead.
func (*ListClientOnboardingResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListClientOnboardingResponse) GetOnboardings() []*ListClientOnboardingResponse_Onboarding {
	if x != nil {
		return x.Onboardings
	}
	return nil
}

type SubmitClientApplicationRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId         int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	JobTitle             string                 `protobuf:"bytes,2,opt,name=job_title,proto3" json:"job_title,omitempty"`
	Occupation           string                 `protobuf:"bytes,3,opt,name=occupation,proto3" json:"occupation,omitempty"`
	LivingCity           string                 `protobuf:"bytes,4,opt,name=living_city,proto3" json:"living_city,omitempty"`
	MonthlyIncome        string                 `protobuf:"bytes,5,opt,name=monthly_income,proto3" json:"monthly_income,omitempty"`
	TempResidenceAddress string                 `protobuf:"bytes,6,opt,name=temp_residence_address,proto3" json:"temp_residence_address,omitempty"`
	Education            string                 `protobuf:"bytes,7,opt,name=education,proto3" json:"education,omitempty"`
	SourceOfFund         string                 `protobuf:"bytes,8,opt,name=source_of_fund,proto3" json:"source_of_fund,omitempty"`
	EmploymentStatus     string                 `protobuf:"bytes,9,opt,name=employment_status,proto3" json:"employment_status,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *SubmitClientApplicationRequest) Reset() {
	*x = SubmitClientApplicationRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientApplicationRequest) ProtoMessage() {}

func (x *SubmitClientApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientApplicationRequest.ProtoReflect.Descriptor instead.
func (*SubmitClientApplicationRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{16}
}

func (x *SubmitClientApplicationRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *SubmitClientApplicationRequest) GetJobTitle() string {
	if x != nil {
		return x.JobTitle
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetLivingCity() string {
	if x != nil {
		return x.LivingCity
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetMonthlyIncome() string {
	if x != nil {
		return x.MonthlyIncome
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetTempResidenceAddress() string {
	if x != nil {
		return x.TempResidenceAddress
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetEducation() string {
	if x != nil {
		return x.Education
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetSourceOfFund() string {
	if x != nil {
		return x.SourceOfFund
	}
	return ""
}

func (x *SubmitClientApplicationRequest) GetEmploymentStatus() string {
	if x != nil {
		return x.EmploymentStatus
	}
	return ""
}

type SubmitClientApplicationResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ApplicationRejected bool                   `protobuf:"varint,1,opt,name=application_rejected,proto3" json:"application_rejected,omitempty"`
	RequireLinkAccount  bool                   `protobuf:"varint,2,opt,name=require_link_account,proto3" json:"require_link_account,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SubmitClientApplicationResponse) Reset() {
	*x = SubmitClientApplicationResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientApplicationResponse) ProtoMessage() {}

func (x *SubmitClientApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientApplicationResponse.ProtoReflect.Descriptor instead.
func (*SubmitClientApplicationResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{17}
}

func (x *SubmitClientApplicationResponse) GetApplicationRejected() bool {
	if x != nil {
		return x.ApplicationRejected
	}
	return false
}

func (x *SubmitClientApplicationResponse) GetRequireLinkAccount() bool {
	if x != nil {
		return x.RequireLinkAccount
	}
	return false
}

type SubmitClientWetSignRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	// Types that are valid to be assigned to SignImageRes:
	//
	//	*SubmitClientWetSignRequest_ImageUri
	//	*SubmitClientWetSignRequest_ImageData
	SignImageRes  isSubmitClientWetSignRequest_SignImageRes `protobuf_oneof:"sign_image_res"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientWetSignRequest) Reset() {
	*x = SubmitClientWetSignRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientWetSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientWetSignRequest) ProtoMessage() {}

func (x *SubmitClientWetSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientWetSignRequest.ProtoReflect.Descriptor instead.
func (*SubmitClientWetSignRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{18}
}

func (x *SubmitClientWetSignRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *SubmitClientWetSignRequest) GetSignImageRes() isSubmitClientWetSignRequest_SignImageRes {
	if x != nil {
		return x.SignImageRes
	}
	return nil
}

func (x *SubmitClientWetSignRequest) GetImageUri() *SubmitClientWetSignRequest_SignImageUri {
	if x != nil {
		if x, ok := x.SignImageRes.(*SubmitClientWetSignRequest_ImageUri); ok {
			return x.ImageUri
		}
	}
	return nil
}

func (x *SubmitClientWetSignRequest) GetImageData() *SubmitClientWetSignRequest_SignImageData {
	if x != nil {
		if x, ok := x.SignImageRes.(*SubmitClientWetSignRequest_ImageData); ok {
			return x.ImageData
		}
	}
	return nil
}

type isSubmitClientWetSignRequest_SignImageRes interface {
	isSubmitClientWetSignRequest_SignImageRes()
}

type SubmitClientWetSignRequest_ImageUri struct {
	ImageUri *SubmitClientWetSignRequest_SignImageUri `protobuf:"bytes,2,opt,name=image_uri,json=sign_image_uri,proto3,oneof"`
}

type SubmitClientWetSignRequest_ImageData struct {
	ImageData *SubmitClientWetSignRequest_SignImageData `protobuf:"bytes,3,opt,name=image_data,json=sign_image_data,proto3,oneof"`
}

func (*SubmitClientWetSignRequest_ImageUri) isSubmitClientWetSignRequest_SignImageRes() {}

func (*SubmitClientWetSignRequest_ImageData) isSubmitClientWetSignRequest_SignImageRes() {}

type SubmitClientWetSignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientWetSignResponse) Reset() {
	*x = SubmitClientWetSignResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientWetSignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientWetSignResponse) ProtoMessage() {}

func (x *SubmitClientWetSignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientWetSignResponse.ProtoReflect.Descriptor instead.
func (*SubmitClientWetSignResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{19}
}

func (x *SubmitClientWetSignResponse) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type SubmitClientFaceChallengeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	FaceRequestId string                 `protobuf:"bytes,2,opt,name=face_request_id,proto3" json:"face_request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientFaceChallengeRequest) Reset() {
	*x = SubmitClientFaceChallengeRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientFaceChallengeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientFaceChallengeRequest) ProtoMessage() {}

func (x *SubmitClientFaceChallengeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientFaceChallengeRequest.ProtoReflect.Descriptor instead.
func (*SubmitClientFaceChallengeRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{20}
}

func (x *SubmitClientFaceChallengeRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *SubmitClientFaceChallengeRequest) GetFaceRequestId() string {
	if x != nil {
		return x.FaceRequestId
	}
	return ""
}

type SubmitClientFaceChallengeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientFaceChallengeResponse) Reset() {
	*x = SubmitClientFaceChallengeResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientFaceChallengeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientFaceChallengeResponse) ProtoMessage() {}

func (x *SubmitClientFaceChallengeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientFaceChallengeResponse.ProtoReflect.Descriptor instead.
func (*SubmitClientFaceChallengeResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{21}
}

func (x *SubmitClientFaceChallengeResponse) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type RequestClientOTPRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OtpType       OTPType                `protobuf:"varint,1,opt,name=otp_type,proto3,enum=onboarding_service.v1.OTPType" json:"otp_type,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,3,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestClientOTPRequest) Reset() {
	*x = RequestClientOTPRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestClientOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestClientOTPRequest) ProtoMessage() {}

func (x *RequestClientOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestClientOTPRequest.ProtoReflect.Descriptor instead.
func (*RequestClientOTPRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{22}
}

func (x *RequestClientOTPRequest) GetOtpType() OTPType {
	if x != nil {
		return x.OtpType
	}
	return OTPType_CONTRACT_SIGNING
}

func (x *RequestClientOTPRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *RequestClientOTPRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type RequestClientOTPResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WaitTime      int64                  `protobuf:"varint,1,opt,name=wait_time,proto3" json:"wait_time,omitempty"`
	ResendTime    int64                  `protobuf:"varint,2,opt,name=resend_time,proto3" json:"resend_time,omitempty"`
	Status        bool                   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,4,opt,name=error_message,proto3" json:"error_message,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,5,opt,name=phone_number,proto3" json:"phone_number,omitempty"`
	OtpRequestId  string                 `protobuf:"bytes,6,opt,name=otp_request_id,proto3" json:"otp_request_id,omitempty"`
	IsSendSms     bool                   `protobuf:"varint,7,opt,name=is_send_sms,proto3" json:"is_send_sms,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestClientOTPResponse) Reset() {
	*x = RequestClientOTPResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestClientOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestClientOTPResponse) ProtoMessage() {}

func (x *RequestClientOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestClientOTPResponse.ProtoReflect.Descriptor instead.
func (*RequestClientOTPResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{23}
}

func (x *RequestClientOTPResponse) GetWaitTime() int64 {
	if x != nil {
		return x.WaitTime
	}
	return 0
}

func (x *RequestClientOTPResponse) GetResendTime() int64 {
	if x != nil {
		return x.ResendTime
	}
	return 0
}

func (x *RequestClientOTPResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *RequestClientOTPResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *RequestClientOTPResponse) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *RequestClientOTPResponse) GetOtpRequestId() string {
	if x != nil {
		return x.OtpRequestId
	}
	return ""
}

func (x *RequestClientOTPResponse) GetIsSendSms() bool {
	if x != nil {
		return x.IsSendSms
	}
	return false
}

type VerifyClientOTPRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OtpCode       string                 `protobuf:"bytes,1,opt,name=otp_code,proto3" json:"otp_code,omitempty"`
	OtpType       OTPType                `protobuf:"varint,2,opt,name=otp_type,proto3,enum=onboarding_service.v1.OTPType" json:"otp_type,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,3,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,4,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyClientOTPRequest) Reset() {
	*x = VerifyClientOTPRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyClientOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClientOTPRequest) ProtoMessage() {}

func (x *VerifyClientOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClientOTPRequest.ProtoReflect.Descriptor instead.
func (*VerifyClientOTPRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{24}
}

func (x *VerifyClientOTPRequest) GetOtpCode() string {
	if x != nil {
		return x.OtpCode
	}
	return ""
}

func (x *VerifyClientOTPRequest) GetOtpType() OTPType {
	if x != nil {
		return x.OtpType
	}
	return OTPType_CONTRACT_SIGNING
}

func (x *VerifyClientOTPRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *VerifyClientOTPRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type VerifyClientOTPResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyClientOTPResponse) Reset() {
	*x = VerifyClientOTPResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyClientOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClientOTPResponse) ProtoMessage() {}

func (x *VerifyClientOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClientOTPResponse.ProtoReflect.Descriptor instead.
func (*VerifyClientOTPResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{25}
}

func (x *VerifyClientOTPResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type GetClientContractRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientContractRequest) Reset() {
	*x = GetClientContractRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientContractRequest) ProtoMessage() {}

func (x *GetClientContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientContractRequest.ProtoReflect.Descriptor instead.
func (*GetClientContractRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetClientContractRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type GetClientContractResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	UnsignedContractUrl string                 `protobuf:"bytes,1,opt,name=unsigned_contract_url,proto3" json:"unsigned_contract_url,omitempty"`
	SignedContractUrl   string                 `protobuf:"bytes,2,opt,name=signed_contract_url,proto3" json:"signed_contract_url,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetClientContractResponse) Reset() {
	*x = GetClientContractResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientContractResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientContractResponse) ProtoMessage() {}

func (x *GetClientContractResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientContractResponse.ProtoReflect.Descriptor instead.
func (*GetClientContractResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetClientContractResponse) GetUnsignedContractUrl() string {
	if x != nil {
		return x.UnsignedContractUrl
	}
	return ""
}

func (x *GetClientContractResponse) GetSignedContractUrl() string {
	if x != nil {
		return x.SignedContractUrl
	}
	return ""
}

type GetClientRejectionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OnboardingId  int64                  `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientRejectionRequest) Reset() {
	*x = GetClientRejectionRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientRejectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientRejectionRequest) ProtoMessage() {}

func (x *GetClientRejectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientRejectionRequest.ProtoReflect.Descriptor instead.
func (*GetClientRejectionRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetClientRejectionRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type GetClientRejectionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Content       *Content               `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Actions       []*Action              `protobuf:"bytes,3,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientRejectionResponse) Reset() {
	*x = GetClientRejectionResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientRejectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientRejectionResponse) ProtoMessage() {}

func (x *GetClientRejectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientRejectionResponse.ProtoReflect.Descriptor instead.
func (*GetClientRejectionResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetClientRejectionResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetClientRejectionResponse) GetContent() *Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *GetClientRejectionResponse) GetActions() []*Action {
	if x != nil {
		return x.Actions
	}
	return nil
}

type QueryOnboardingStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,2,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryOnboardingStatusRequest) Reset() {
	*x = QueryOnboardingStatusRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryOnboardingStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOnboardingStatusRequest) ProtoMessage() {}

func (x *QueryOnboardingStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOnboardingStatusRequest.ProtoReflect.Descriptor instead.
func (*QueryOnboardingStatusRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{30}
}

func (x *QueryOnboardingStatusRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *QueryOnboardingStatusRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type QueryOnboardingStatusResponse struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Flags         *QueryOnboardingStatusResponse_Flags `protobuf:"bytes,1,opt,name=flags,proto3" json:"flags,omitempty"`
	OnboardingId  int64                                `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	CurrentStep   string                               `protobuf:"bytes,3,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
	PartnerCode   string                               `protobuf:"bytes,4,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryOnboardingStatusResponse) Reset() {
	*x = QueryOnboardingStatusResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryOnboardingStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOnboardingStatusResponse) ProtoMessage() {}

func (x *QueryOnboardingStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOnboardingStatusResponse.ProtoReflect.Descriptor instead.
func (*QueryOnboardingStatusResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{31}
}

func (x *QueryOnboardingStatusResponse) GetFlags() *QueryOnboardingStatusResponse_Flags {
	if x != nil {
		return x.Flags
	}
	return nil
}

func (x *QueryOnboardingStatusResponse) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *QueryOnboardingStatusResponse) GetCurrentStep() string {
	if x != nil {
		return x.CurrentStep
	}
	return ""
}

func (x *QueryOnboardingStatusResponse) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type ListOnboardingByUserIDsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayIds    []int64                `protobuf:"varint,1,rep,packed,name=zalopay_ids,json=zalopayIds,proto3" json:"zalopay_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOnboardingByUserIDsRequest) Reset() {
	*x = ListOnboardingByUserIDsRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOnboardingByUserIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOnboardingByUserIDsRequest) ProtoMessage() {}

func (x *ListOnboardingByUserIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOnboardingByUserIDsRequest.ProtoReflect.Descriptor instead.
func (*ListOnboardingByUserIDsRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{32}
}

func (x *ListOnboardingByUserIDsRequest) GetZalopayIds() []int64 {
	if x != nil {
		return x.ZalopayIds
	}
	return nil
}

type ListOnboardingByUserIDsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Onboardings   []*OnboardingData      `protobuf:"bytes,1,rep,name=onboardings,proto3" json:"onboardings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOnboardingByUserIDsResponse) Reset() {
	*x = ListOnboardingByUserIDsResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOnboardingByUserIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOnboardingByUserIDsResponse) ProtoMessage() {}

func (x *ListOnboardingByUserIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOnboardingByUserIDsResponse.ProtoReflect.Descriptor instead.
func (*ListOnboardingByUserIDsResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListOnboardingByUserIDsResponse) GetOnboardings() []*OnboardingData {
	if x != nil {
		return x.Onboardings
	}
	return nil
}

type GetClientResourcesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceTypes []ResourceType         `protobuf:"varint,1,rep,packed,name=resource_types,proto3,enum=onboarding_service.v1.ResourceType" json:"resource_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientResourcesRequest) Reset() {
	*x = GetClientResourcesRequest{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientResourcesRequest) ProtoMessage() {}

func (x *GetClientResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientResourcesRequest.ProtoReflect.Descriptor instead.
func (*GetClientResourcesRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetClientResourcesRequest) GetResourceTypes() []ResourceType {
	if x != nil {
		return x.ResourceTypes
	}
	return nil
}

type GetClientResourcesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resources     []*CIMBResource        `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientResourcesResponse) Reset() {
	*x = GetClientResourcesResponse{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientResourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientResourcesResponse) ProtoMessage() {}

func (x *GetClientResourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientResourcesResponse.ProtoReflect.Descriptor instead.
func (*GetClientResourcesResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetClientResourcesResponse) GetResources() []*CIMBResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

// OnboardingData is a base domain model to include all user onboarding information
type OnboardingData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ZalopayId     int64                  `protobuf:"varint,2,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,4,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	CurrentStep   string                 `protobuf:"bytes,5,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
	RejectCode    string                 `protobuf:"bytes,6,opt,name=reject_code,json=rejectCode,proto3" json:"reject_code,omitempty"`
	ProfileInfo   *OnboardingProfile     `protobuf:"bytes,7,opt,name=profile_info,json=profileInfo,proto3" json:"profile_info,omitempty"`
	PartnerData   *PartnerOnboarding     `protobuf:"bytes,8,opt,name=partner_data,json=partnerData,proto3" json:"partner_data,omitempty"`
	StatusFlags   *OnboardingFlags       `protobuf:"bytes,9,opt,name=status_flags,json=statusFlags,proto3,oneof" json:"status_flags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnboardingData) Reset() {
	*x = OnboardingData{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnboardingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingData) ProtoMessage() {}

func (x *OnboardingData) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingData.ProtoReflect.Descriptor instead.
func (*OnboardingData) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{36}
}

func (x *OnboardingData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OnboardingData) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *OnboardingData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *OnboardingData) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *OnboardingData) GetCurrentStep() string {
	if x != nil {
		return x.CurrentStep
	}
	return ""
}

func (x *OnboardingData) GetRejectCode() string {
	if x != nil {
		return x.RejectCode
	}
	return ""
}

func (x *OnboardingData) GetProfileInfo() *OnboardingProfile {
	if x != nil {
		return x.ProfileInfo
	}
	return nil
}

func (x *OnboardingData) GetPartnerData() *PartnerOnboarding {
	if x != nil {
		return x.PartnerData
	}
	return nil
}

func (x *OnboardingData) GetStatusFlags() *OnboardingFlags {
	if x != nil {
		return x.StatusFlags
	}
	return nil
}

// OnboardingProfile is a profile that user use to submit onboarding request
type OnboardingProfile struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	FullName             string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Gender               string                 `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	PhoneNumber          string                 `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	IdNumber             string                 `protobuf:"bytes,4,opt,name=id_number,json=idNumber,proto3" json:"id_number,omitempty"`
	IdIssueDate          string                 `protobuf:"bytes,5,opt,name=id_issue_date,json=idIssueDate,proto3" json:"id_issue_date,omitempty"`
	IdIssuePlace         string                 `protobuf:"bytes,6,opt,name=id_issue_place,json=idIssuePlace,proto3" json:"id_issue_place,omitempty"`
	DateOfBirth          string                 `protobuf:"bytes,7,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	PermanentAddress     string                 `protobuf:"bytes,8,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	TempResidenceAddress string                 `protobuf:"bytes,9,opt,name=temp_residence_address,json=tempResidenceAddress,proto3" json:"temp_residence_address,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *OnboardingProfile) Reset() {
	*x = OnboardingProfile{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnboardingProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingProfile) ProtoMessage() {}

func (x *OnboardingProfile) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingProfile.ProtoReflect.Descriptor instead.
func (*OnboardingProfile) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{37}
}

func (x *OnboardingProfile) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *OnboardingProfile) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *OnboardingProfile) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *OnboardingProfile) GetIdNumber() string {
	if x != nil {
		return x.IdNumber
	}
	return ""
}

func (x *OnboardingProfile) GetIdIssueDate() string {
	if x != nil {
		return x.IdIssueDate
	}
	return ""
}

func (x *OnboardingProfile) GetIdIssuePlace() string {
	if x != nil {
		return x.IdIssuePlace
	}
	return ""
}

func (x *OnboardingProfile) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *OnboardingProfile) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

func (x *OnboardingProfile) GetTempResidenceAddress() string {
	if x != nil {
		return x.TempResidenceAddress
	}
	return ""
}

type OnboardingFlags struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsUnregister  bool                   `protobuf:"varint,1,opt,name=is_unregister,json=isUnregister,proto3" json:"is_unregister,omitempty"`
	IsOnboarding  bool                   `protobuf:"varint,2,opt,name=is_onboarding,json=isOnboarding,proto3" json:"is_onboarding,omitempty"`
	IsRejected    bool                   `protobuf:"varint,3,opt,name=is_rejected,json=isRejected,proto3" json:"is_rejected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnboardingFlags) Reset() {
	*x = OnboardingFlags{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnboardingFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingFlags) ProtoMessage() {}

func (x *OnboardingFlags) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingFlags.ProtoReflect.Descriptor instead.
func (*OnboardingFlags) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{38}
}

func (x *OnboardingFlags) GetIsUnregister() bool {
	if x != nil {
		return x.IsUnregister
	}
	return false
}

func (x *OnboardingFlags) GetIsOnboarding() bool {
	if x != nil {
		return x.IsOnboarding
	}
	return false
}

func (x *OnboardingFlags) GetIsRejected() bool {
	if x != nil {
		return x.IsRejected
	}
	return false
}

type PartnerOnboarding struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CimbData      *CIMBOnboarding        `protobuf:"bytes,1,opt,name=cimb_data,json=cimbData,proto3,oneof" json:"cimb_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PartnerOnboarding) Reset() {
	*x = PartnerOnboarding{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PartnerOnboarding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartnerOnboarding) ProtoMessage() {}

func (x *PartnerOnboarding) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartnerOnboarding.ProtoReflect.Descriptor instead.
func (*PartnerOnboarding) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{39}
}

func (x *PartnerOnboarding) GetCimbData() *CIMBOnboarding {
	if x != nil {
		return x.CimbData
	}
	return nil
}

type CIMBOnboarding struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	OdStatus             string                 `protobuf:"bytes,1,opt,name=od_status,json=odStatus,proto3" json:"od_status,omitempty"`
	CasaStatus           string                 `protobuf:"bytes,2,opt,name=casa_status,json=casaStatus,proto3" json:"casa_status,omitempty"`
	SignStatus           string                 `protobuf:"bytes,3,opt,name=sign_status,json=signStatus,proto3" json:"sign_status,omitempty"`
	ErrorDetail          string                 `protobuf:"bytes,4,opt,name=error_detail,json=errorDetail,proto3" json:"error_detail,omitempty"`
	ManualApprovalReason string                 `protobuf:"bytes,5,opt,name=manual_approval_reason,json=manualApprovalReason,proto3" json:"manual_approval_reason,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CIMBOnboarding) Reset() {
	*x = CIMBOnboarding{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CIMBOnboarding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CIMBOnboarding) ProtoMessage() {}

func (x *CIMBOnboarding) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CIMBOnboarding.ProtoReflect.Descriptor instead.
func (*CIMBOnboarding) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{40}
}

func (x *CIMBOnboarding) GetOdStatus() string {
	if x != nil {
		return x.OdStatus
	}
	return ""
}

func (x *CIMBOnboarding) GetCasaStatus() string {
	if x != nil {
		return x.CasaStatus
	}
	return ""
}

func (x *CIMBOnboarding) GetSignStatus() string {
	if x != nil {
		return x.SignStatus
	}
	return ""
}

func (x *CIMBOnboarding) GetErrorDetail() string {
	if x != nil {
		return x.ErrorDetail
	}
	return ""
}

func (x *CIMBOnboarding) GetManualApprovalReason() string {
	if x != nil {
		return x.ManualApprovalReason
	}
	return ""
}

type CIMBResource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Data          []*CIMBResourceData    `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CIMBResource) Reset() {
	*x = CIMBResource{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CIMBResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CIMBResource) ProtoMessage() {}

func (x *CIMBResource) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CIMBResource.ProtoReflect.Descriptor instead.
func (*CIMBResource) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{41}
}

func (x *CIMBResource) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CIMBResource) GetData() []*CIMBResourceData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CIMBResourceData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Code  string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// Data description in vietnamese
	Vietnamese string `protobuf:"bytes,2,opt,name=vietnamese,proto3" json:"vietnamese,omitempty"`
	// Data description in english
	English       string `protobuf:"bytes,3,opt,name=english,proto3" json:"english,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CIMBResourceData) Reset() {
	*x = CIMBResourceData{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CIMBResourceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CIMBResourceData) ProtoMessage() {}

func (x *CIMBResourceData) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CIMBResourceData.ProtoReflect.Descriptor instead.
func (*CIMBResourceData) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{42}
}

func (x *CIMBResourceData) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CIMBResourceData) GetVietnamese() string {
	if x != nil {
		return x.Vietnamese
	}
	return ""
}

func (x *CIMBResourceData) GetEnglish() string {
	if x != nil {
		return x.English
	}
	return ""
}

// UserProfile is present zalopay user kyc profile information, not onboarding profile
type UserProfile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// User KYC level in ZaloPay system
	KycLevel int32 `protobuf:"varint,1,opt,name=kyc_level,proto3" json:"kyc_level,omitempty"`
	// User profile level in ZaloPay system
	ProfileLevel int32 `protobuf:"varint,2,opt,name=profile_level,proto3" json:"profile_level,omitempty"`
	// User registed fullname
	FullName string `protobuf:"bytes,3,opt,name=full_name,proto3" json:"full_name,omitempty"`
	// User registed gender
	Gender Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=onboarding_service.v1.Gender" json:"gender,omitempty"`
	// User registed phone
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,proto3" json:"phone_number,omitempty"`
	// User registed address
	PermanentAddress string `protobuf:"bytes,6,opt,name=permanent_address,proto3" json:"permanent_address,omitempty"`
	// User registed id type | 1: CMND, 2: passport, 3: CCCD, 4: CMSQ, 5: CCCD gắn chip
	IdType int32 `protobuf:"varint,7,opt,name=id_type,proto3" json:"id_type,omitempty"`
	// User registed id number
	IdNumber string `protobuf:"bytes,8,opt,name=id_number,proto3" json:"id_number,omitempty"`
	// User registed email
	Email string `protobuf:"bytes,9,opt,name=email,proto3" json:"email,omitempty"`
	// User registed date of birth
	DateOfBirth string `protobuf:"bytes,10,opt,name=date_of_birth,proto3" json:"date_of_birth,omitempty"`
	// Id issued location
	IdIssuePlace string `protobuf:"bytes,11,opt,name=id_issue_place,proto3" json:"id_issue_place,omitempty"`
	// Id issued date
	IdIssueDate   string `protobuf:"bytes,12,opt,name=id_issue_date,proto3" json:"id_issue_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserProfile) Reset() {
	*x = UserProfile{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProfile) ProtoMessage() {}

func (x *UserProfile) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProfile.ProtoReflect.Descriptor instead.
func (*UserProfile) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{43}
}

func (x *UserProfile) GetKycLevel() int32 {
	if x != nil {
		return x.KycLevel
	}
	return 0
}

func (x *UserProfile) GetProfileLevel() int32 {
	if x != nil {
		return x.ProfileLevel
	}
	return 0
}

func (x *UserProfile) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UserProfile) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *UserProfile) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UserProfile) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

func (x *UserProfile) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

func (x *UserProfile) GetIdNumber() string {
	if x != nil {
		return x.IdNumber
	}
	return ""
}

func (x *UserProfile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserProfile) GetDateOfBirth() string {
	if x != nil {
		return x.DateOfBirth
	}
	return ""
}

func (x *UserProfile) GetIdIssuePlace() string {
	if x != nil {
		return x.IdIssuePlace
	}
	return ""
}

func (x *UserProfile) GetIdIssueDate() string {
	if x != nil {
		return x.IdIssueDate
	}
	return ""
}

type Content struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Content) Reset() {
	*x = Content{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{44}
}

func (x *Content) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Content) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Action struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Variant       string                 `protobuf:"bytes,3,opt,name=variant,proto3" json:"variant,omitempty"`
	ZpaActionUrl  string                 `protobuf:"bytes,4,opt,name=zpa_action_url,proto3" json:"zpa_action_url,omitempty"`
	ZpiActionUrl  string                 `protobuf:"bytes,5,opt,name=zpi_action_url,proto3" json:"zpi_action_url,omitempty"`
	Metadata      map[string]*anypb.Any  `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Action) Reset() {
	*x = Action{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Action) ProtoMessage() {}

func (x *Action) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Action.ProtoReflect.Descriptor instead.
func (*Action) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{45}
}

func (x *Action) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Action) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Action) GetVariant() string {
	if x != nil {
		return x.Variant
	}
	return ""
}

func (x *Action) GetZpaActionUrl() string {
	if x != nil {
		return x.ZpaActionUrl
	}
	return ""
}

func (x *Action) GetZpiActionUrl() string {
	if x != nil {
		return x.ZpiActionUrl
	}
	return ""
}

func (x *Action) GetMetadata() map[string]*anypb.Any {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type RejectInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Notice        *Notice                `protobuf:"bytes,2,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectInfo) Reset() {
	*x = RejectInfo{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectInfo) ProtoMessage() {}

func (x *RejectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectInfo.ProtoReflect.Descriptor instead.
func (*RejectInfo) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{46}
}

func (x *RejectInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RejectInfo) GetNotice() *Notice {
	if x != nil {
		return x.Notice
	}
	return nil
}

type RiskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Level         string                 `protobuf:"bytes,2,opt,name=level,proto3" json:"level,omitempty"`
	Notice        *Notice                `protobuf:"bytes,3,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RiskInfo) Reset() {
	*x = RiskInfo{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfo) ProtoMessage() {}

func (x *RiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfo.ProtoReflect.Descriptor instead.
func (*RiskInfo) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{47}
}

func (x *RiskInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RiskInfo) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *RiskInfo) GetNotice() *Notice {
	if x != nil {
		return x.Notice
	}
	return nil
}

type ProfileIssue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Notice        *Notice                `protobuf:"bytes,2,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileIssue) Reset() {
	*x = ProfileIssue{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileIssue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileIssue) ProtoMessage() {}

func (x *ProfileIssue) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileIssue.ProtoReflect.Descriptor instead.
func (*ProfileIssue) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{48}
}

func (x *ProfileIssue) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ProfileIssue) GetNotice() *Notice {
	if x != nil {
		return x.Notice
	}
	return nil
}

type KycStatusData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        KycStatus              `protobuf:"varint,1,opt,name=status,proto3,enum=onboarding_service.v1.KycStatus" json:"status,omitempty"`
	Notice        *Notice                `protobuf:"bytes,2,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KycStatusData) Reset() {
	*x = KycStatusData{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KycStatusData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycStatusData) ProtoMessage() {}

func (x *KycStatusData) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycStatusData.ProtoReflect.Descriptor instead.
func (*KycStatusData) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{49}
}

func (x *KycStatusData) GetStatus() KycStatus {
	if x != nil {
		return x.Status
	}
	return KycStatus_KYC_STATUS_INVALID
}

func (x *KycStatusData) GetNotice() *Notice {
	if x != nil {
		return x.Notice
	}
	return nil
}

type Notice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *Content               `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Actions       []*Action              `protobuf:"bytes,2,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notice) Reset() {
	*x = Notice{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notice) ProtoMessage() {}

func (x *Notice) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notice.ProtoReflect.Descriptor instead.
func (*Notice) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{50}
}

func (x *Notice) GetContent() *Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Notice) GetActions() []*Action {
	if x != nil {
		return x.Actions
	}
	return nil
}

type BindInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Binding onboarding id
	OnboardingId int64 `protobuf:"varint,1,opt,name=onboarding_id,proto3" json:"onboarding_id,omitempty"`
	// Binding account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,proto3" json:"account_id,omitempty"`
	// Binding partner code
	PartnerCode   string `protobuf:"bytes,3,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindInfo) Reset() {
	*x = BindInfo{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindInfo) ProtoMessage() {}

func (x *BindInfo) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindInfo.ProtoReflect.Descriptor instead.
func (*BindInfo) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{51}
}

func (x *BindInfo) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

func (x *BindInfo) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *BindInfo) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type ListClientOnboardingResponse_Onboarding struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PartnerCode   string                 `protobuf:"bytes,2,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	CurrentStep   string                 `protobuf:"bytes,4,opt,name=current_step,proto3" json:"current_step,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,proto3" json:"updated_at,omitempty"`
	AllStep       []string               `protobuf:"bytes,7,rep,name=all_step,proto3" json:"all_step,omitempty"`
	NextStep      string                 `protobuf:"bytes,8,opt,name=next_step,proto3" json:"next_step,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientOnboardingResponse_Onboarding) Reset() {
	*x = ListClientOnboardingResponse_Onboarding{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientOnboardingResponse_Onboarding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientOnboardingResponse_Onboarding) ProtoMessage() {}

func (x *ListClientOnboardingResponse_Onboarding) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientOnboardingResponse_Onboarding.ProtoReflect.Descriptor instead.
func (*ListClientOnboardingResponse_Onboarding) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ListClientOnboardingResponse_Onboarding) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListClientOnboardingResponse_Onboarding) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *ListClientOnboardingResponse_Onboarding) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListClientOnboardingResponse_Onboarding) GetCurrentStep() string {
	if x != nil {
		return x.CurrentStep
	}
	return ""
}

func (x *ListClientOnboardingResponse_Onboarding) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ListClientOnboardingResponse_Onboarding) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ListClientOnboardingResponse_Onboarding) GetAllStep() []string {
	if x != nil {
		return x.AllStep
	}
	return nil
}

func (x *ListClientOnboardingResponse_Onboarding) GetNextStep() string {
	if x != nil {
		return x.NextStep
	}
	return ""
}

type SubmitClientWetSignRequest_SignImageData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImageData     string                 `protobuf:"bytes,1,opt,name=image_data,proto3" json:"image_data,omitempty"`
	ImageType     string                 `protobuf:"bytes,2,opt,name=image_type,proto3" json:"image_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientWetSignRequest_SignImageData) Reset() {
	*x = SubmitClientWetSignRequest_SignImageData{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientWetSignRequest_SignImageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientWetSignRequest_SignImageData) ProtoMessage() {}

func (x *SubmitClientWetSignRequest_SignImageData) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientWetSignRequest_SignImageData.ProtoReflect.Descriptor instead.
func (*SubmitClientWetSignRequest_SignImageData) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *SubmitClientWetSignRequest_SignImageData) GetImageData() string {
	if x != nil {
		return x.ImageData
	}
	return ""
}

func (x *SubmitClientWetSignRequest_SignImageData) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

type SubmitClientWetSignRequest_SignImageUri struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImageUrl      string                 `protobuf:"bytes,1,opt,name=image_url,proto3" json:"image_url,omitempty"`
	ImageType     string                 `protobuf:"bytes,2,opt,name=image_type,proto3" json:"image_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitClientWetSignRequest_SignImageUri) Reset() {
	*x = SubmitClientWetSignRequest_SignImageUri{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitClientWetSignRequest_SignImageUri) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClientWetSignRequest_SignImageUri) ProtoMessage() {}

func (x *SubmitClientWetSignRequest_SignImageUri) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClientWetSignRequest_SignImageUri.ProtoReflect.Descriptor instead.
func (*SubmitClientWetSignRequest_SignImageUri) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{18, 1}
}

func (x *SubmitClientWetSignRequest_SignImageUri) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *SubmitClientWetSignRequest_SignImageUri) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

type QueryOnboardingStatusResponse_Flags struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsUnregister  bool                   `protobuf:"varint,1,opt,name=is_unregister,json=isUnregister,proto3" json:"is_unregister,omitempty"`
	IsOnboarding  bool                   `protobuf:"varint,2,opt,name=is_onboarding,json=isOnboarding,proto3" json:"is_onboarding,omitempty"`
	IsRejected    bool                   `protobuf:"varint,3,opt,name=is_rejected,json=isRejected,proto3" json:"is_rejected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryOnboardingStatusResponse_Flags) Reset() {
	*x = QueryOnboardingStatusResponse_Flags{}
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryOnboardingStatusResponse_Flags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOnboardingStatusResponse_Flags) ProtoMessage() {}

func (x *QueryOnboardingStatusResponse_Flags) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_onboarding_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOnboardingStatusResponse_Flags.ProtoReflect.Descriptor instead.
func (*QueryOnboardingStatusResponse_Flags) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP(), []int{31, 0}
}

func (x *QueryOnboardingStatusResponse_Flags) GetIsUnregister() bool {
	if x != nil {
		return x.IsUnregister
	}
	return false
}

func (x *QueryOnboardingStatusResponse_Flags) GetIsOnboarding() bool {
	if x != nil {
		return x.IsOnboarding
	}
	return false
}

func (x *QueryOnboardingStatusResponse_Flags) GetIsRejected() bool {
	if x != nil {
		return x.IsRejected
	}
	return false
}

var File_onboarding_service_v1_onboarding_service_proto protoreflect.FileDescriptor

const file_onboarding_service_v1_onboarding_service_proto_rawDesc = "" +
	"\n" +
	".onboarding_service/v1/onboarding_service.proto\x12\x15onboarding_service.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x19google/protobuf/any.proto\x1a\x17validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"[\n" +
	"\x1fRegisterClientOnboardingRequest\x12'\n" +
	"\fpartner_code\x18\x01 \x01(\tH\x00R\fpartner_code\x88\x01\x01B\x0f\n" +
	"\r_partner_code\"H\n" +
	" RegisterClientOnboardingResponse\x12$\n" +
	"\ronboarding_id\x18\x01 \x01(\x03R\ronboarding_id\"N\n" +
	"\x1dReinitClientOnboardingRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\" \n" +
	"\x1eReinitClientOnboardingResponse\"\x1d\n" +
	"\x1bGetClientPermissionsRequest\"\xcf\x02\n" +
	"\x1cGetClientPermissionsResponse\x12&\n" +
	"\x0eis_whitelisted\x18\x01 \x01(\bR\x0eis_whitelisted\x12C\n" +
	"\vbind_status\x18\x02 \x01(\x0e2!.onboarding_service.v1.BindStatusR\vbind_status\x12D\n" +
	"\n" +
	"kyc_status\x18\x03 \x01(\v2$.onboarding_service.v1.KycStatusDataR\n" +
	"kyc_status\x12=\n" +
	"\trisk_info\x18\x04 \x01(\v2\x1f.onboarding_service.v1.RiskInfoR\trisk_info\x12=\n" +
	"\tbind_info\x18\x05 \x01(\v2\x1f.onboarding_service.v1.BindInfoR\tbind_info\"O\n" +
	"\x1bGetClientEkycProfileRequest\x120\n" +
	"\x13need_verify_profile\x18\x01 \x01(\bR\x13need_verify_profile\"\xb3\x01\n" +
	"\x1cGetClientEkycProfileResponse\x12F\n" +
	"\fprofile_info\x18\x01 \x01(\v2\".onboarding_service.v1.UserProfileR\fprofile_info\x12K\n" +
	"\x0eprofile_issues\x18\x02 \x03(\v2#.onboarding_service.v1.ProfileIssueR\x0eprofile_issues\"\x1b\n" +
	"\x19ResetClientEkycNfcRequest\"\x1c\n" +
	"\x1aResetClientEkycNfcResponse\"L\n" +
	"\x1bLinkingClientAccountRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\"\x1e\n" +
	"\x1cLinkingClientAccountResponse\"K\n" +
	"\x1aGetClientOnboardingRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\"\xcf\x03\n" +
	"\x1bGetClientOnboardingResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\"\n" +
	"\fpartner_code\x18\x02 \x01(\tR\fpartner_code\x12\"\n" +
	"\fcurrent_step\x18\x03 \x01(\tR\fcurrent_step\x12\x1c\n" +
	"\tfull_name\x18\x04 \x01(\tR\tfull_name\x12\x16\n" +
	"\x06gender\x18\x05 \x01(\tR\x06gender\x12\"\n" +
	"\fphone_number\x18\x06 \x01(\tR\fphone_number\x12\x1c\n" +
	"\tid_number\x18\a \x01(\tR\tid_number\x12$\n" +
	"\rid_issue_date\x18\b \x01(\tR\rid_issue_date\x12&\n" +
	"\x0eid_issue_place\x18\t \x01(\tR\x0eid_issue_place\x12$\n" +
	"\rdate_of_birth\x18\n" +
	" \x01(\tR\rdate_of_birth\x12,\n" +
	"\x11permanent_address\x18\v \x01(\tR\x11permanent_address\x126\n" +
	"\x16temp_residence_address\x18\f \x01(\tR\x16temp_residence_address\"\x1d\n" +
	"\x1bListClientOnboardingRequest\"\xb1\x03\n" +
	"\x1cListClientOnboardingResponse\x12`\n" +
	"\vonboardings\x18\x01 \x03(\v2>.onboarding_service.v1.ListClientOnboardingResponse.OnboardingR\vonboardings\x1a\xae\x02\n" +
	"\n" +
	"Onboarding\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n" +
	"\fpartner_code\x18\x02 \x01(\tR\fpartner_code\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\"\n" +
	"\fcurrent_step\x18\x04 \x01(\tR\fcurrent_step\x12:\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"created_at\x12:\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updated_at\x12\x1a\n" +
	"\ball_step\x18\a \x03(\tR\ball_step\x12\x1c\n" +
	"\tnext_step\x18\b \x01(\tR\tnext_step\"\x95\x03\n" +
	"\x1eSubmitClientApplicationRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\x12\x1c\n" +
	"\tjob_title\x18\x02 \x01(\tR\tjob_title\x12\x1e\n" +
	"\n" +
	"occupation\x18\x03 \x01(\tR\n" +
	"occupation\x12 \n" +
	"\vliving_city\x18\x04 \x01(\tR\vliving_city\x12/\n" +
	"\x0emonthly_income\x18\x05 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x0emonthly_income\x12?\n" +
	"\x16temp_residence_address\x18\x06 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x16temp_residence_address\x12\x1c\n" +
	"\teducation\x18\a \x01(\tR\teducation\x12&\n" +
	"\x0esource_of_fund\x18\b \x01(\tR\x0esource_of_fund\x12,\n" +
	"\x11employment_status\x18\t \x01(\tR\x11employment_status\"\x89\x01\n" +
	"\x1fSubmitClientApplicationResponse\x122\n" +
	"\x14application_rejected\x18\x01 \x01(\bR\x14application_rejected\x122\n" +
	"\x14require_link_account\x18\x02 \x01(\bR\x14require_link_account\"\xf2\x03\n" +
	"\x1aSubmitClientWetSignRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\x12c\n" +
	"\timage_uri\x18\x02 \x01(\v2>.onboarding_service.v1.SubmitClientWetSignRequest.SignImageUriH\x00R\x0esign_image_uri\x12f\n" +
	"\n" +
	"image_data\x18\x03 \x01(\v2?.onboarding_service.v1.SubmitClientWetSignRequest.SignImageDataH\x00R\x0fsign_image_data\x1aa\n" +
	"\rSignImageData\x12'\n" +
	"\n" +
	"image_data\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"image_data\x12'\n" +
	"\n" +
	"image_type\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"image_type\x1a^\n" +
	"\fSignImageUri\x12%\n" +
	"\timage_url\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\timage_url\x12'\n" +
	"\n" +
	"image_type\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"image_typeB\x15\n" +
	"\x0esign_image_res\x12\x03\xf8B\x01\"C\n" +
	"\x1bSubmitClientWetSignResponse\x12$\n" +
	"\ronboarding_id\x18\x01 \x01(\x03R\ronboarding_id\"\x84\x01\n" +
	" SubmitClientFaceChallengeRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\x121\n" +
	"\x0fface_request_id\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x0fface_request_id\"I\n" +
	"!SubmitClientFaceChallengeResponse\x12$\n" +
	"\ronboarding_id\x18\x01 \x01(\x03R\ronboarding_id\"\xb1\x01\n" +
	"\x17RequestClientOTPRequest\x12:\n" +
	"\botp_type\x18\x01 \x01(\x0e2\x1e.onboarding_service.v1.OTPTypeR\botp_type\x12-\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\x12+\n" +
	"\fpartner_code\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fpartner_code\"\x86\x02\n" +
	"\x18RequestClientOTPResponse\x12\x1c\n" +
	"\twait_time\x18\x01 \x01(\x03R\twait_time\x12 \n" +
	"\vresend_time\x18\x02 \x01(\x03R\vresend_time\x12\x16\n" +
	"\x06status\x18\x03 \x01(\bR\x06status\x12$\n" +
	"\rerror_message\x18\x04 \x01(\tR\rerror_message\x12\"\n" +
	"\fphone_number\x18\x05 \x01(\tR\fphone_number\x12&\n" +
	"\x0eotp_request_id\x18\x06 \x01(\tR\x0eotp_request_id\x12 \n" +
	"\vis_send_sms\x18\a \x01(\bR\vis_send_sms\"\xd5\x01\n" +
	"\x16VerifyClientOTPRequest\x12#\n" +
	"\botp_code\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\botp_code\x12:\n" +
	"\botp_type\x18\x02 \x01(\x0e2\x1e.onboarding_service.v1.OTPTypeR\botp_type\x12-\n" +
	"\ronboarding_id\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\x12+\n" +
	"\fpartner_code\x18\x04 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fpartner_code\"1\n" +
	"\x17VerifyClientOTPResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"I\n" +
	"\x18GetClientContractRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\"\x83\x01\n" +
	"\x19GetClientContractResponse\x124\n" +
	"\x15unsigned_contract_url\x18\x01 \x01(\tR\x15unsigned_contract_url\x120\n" +
	"\x13signed_contract_url\x18\x02 \x01(\tR\x13signed_contract_url\"J\n" +
	"\x19GetClientRejectionRequest\x12-\n" +
	"\ronboarding_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\ronboarding_id\"\xa3\x01\n" +
	"\x1aGetClientRejectionResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x128\n" +
	"\acontent\x18\x02 \x01(\v2\x1e.onboarding_service.v1.ContentR\acontent\x127\n" +
	"\aactions\x18\x03 \x03(\v2\x1d.onboarding_service.v1.ActionR\aactions\"r\n" +
	"\x1cQueryOnboardingStatusRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12*\n" +
	"\fpartner_code\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\"\xd0\x02\n" +
	"\x1dQueryOnboardingStatusResponse\x12P\n" +
	"\x05flags\x18\x01 \x01(\v2:.onboarding_service.v1.QueryOnboardingStatusResponse.FlagsR\x05flags\x12#\n" +
	"\ronboarding_id\x18\x02 \x01(\x03R\fonboardingId\x12!\n" +
	"\fcurrent_step\x18\x03 \x01(\tR\vcurrentStep\x12!\n" +
	"\fpartner_code\x18\x04 \x01(\tR\vpartnerCode\x1ar\n" +
	"\x05Flags\x12#\n" +
	"\ris_unregister\x18\x01 \x01(\bR\fisUnregister\x12#\n" +
	"\ris_onboarding\x18\x02 \x01(\bR\fisOnboarding\x12\x1f\n" +
	"\vis_rejected\x18\x03 \x01(\bR\n" +
	"isRejected\"M\n" +
	"\x1eListOnboardingByUserIDsRequest\x12+\n" +
	"\vzalopay_ids\x18\x01 \x03(\x03B\n" +
	"\xfaB\a\x92\x01\x04\b\x01\x102R\n" +
	"zalopayIds\"j\n" +
	"\x1fListOnboardingByUserIDsResponse\x12G\n" +
	"\vonboardings\x18\x01 \x03(\v2%.onboarding_service.v1.OnboardingDataR\vonboardings\"r\n" +
	"\x19GetClientResourcesRequest\x12U\n" +
	"\x0eresource_types\x18\x01 \x03(\x0e2#.onboarding_service.v1.ResourceTypeB\b\xfaB\x05\x92\x01\x02\b\x01R\x0eresource_types\"_\n" +
	"\x1aGetClientResourcesResponse\x12A\n" +
	"\tresources\x18\x01 \x03(\v2#.onboarding_service.v1.CIMBResourceR\tresources\"\xb9\x03\n" +
	"\x0eOnboardingData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"zalopay_id\x18\x02 \x01(\x03R\tzalopayId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12!\n" +
	"\fpartner_code\x18\x04 \x01(\tR\vpartnerCode\x12!\n" +
	"\fcurrent_step\x18\x05 \x01(\tR\vcurrentStep\x12\x1f\n" +
	"\vreject_code\x18\x06 \x01(\tR\n" +
	"rejectCode\x12K\n" +
	"\fprofile_info\x18\a \x01(\v2(.onboarding_service.v1.OnboardingProfileR\vprofileInfo\x12K\n" +
	"\fpartner_data\x18\b \x01(\v2(.onboarding_service.v1.PartnerOnboardingR\vpartnerData\x12N\n" +
	"\fstatus_flags\x18\t \x01(\v2&.onboarding_service.v1.OnboardingFlagsH\x00R\vstatusFlags\x88\x01\x01B\x0f\n" +
	"\r_status_flags\"\xd9\x02\n" +
	"\x11OnboardingProfile\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x16\n" +
	"\x06gender\x18\x02 \x01(\tR\x06gender\x12!\n" +
	"\fphone_number\x18\x03 \x01(\tR\vphoneNumber\x12\x1b\n" +
	"\tid_number\x18\x04 \x01(\tR\bidNumber\x12\"\n" +
	"\rid_issue_date\x18\x05 \x01(\tR\vidIssueDate\x12$\n" +
	"\x0eid_issue_place\x18\x06 \x01(\tR\fidIssuePlace\x12\"\n" +
	"\rdate_of_birth\x18\a \x01(\tR\vdateOfBirth\x12+\n" +
	"\x11permanent_address\x18\b \x01(\tR\x10permanentAddress\x124\n" +
	"\x16temp_residence_address\x18\t \x01(\tR\x14tempResidenceAddress\"|\n" +
	"\x0fOnboardingFlags\x12#\n" +
	"\ris_unregister\x18\x01 \x01(\bR\fisUnregister\x12#\n" +
	"\ris_onboarding\x18\x02 \x01(\bR\fisOnboarding\x12\x1f\n" +
	"\vis_rejected\x18\x03 \x01(\bR\n" +
	"isRejected\"j\n" +
	"\x11PartnerOnboarding\x12G\n" +
	"\tcimb_data\x18\x01 \x01(\v2%.onboarding_service.v1.CIMBOnboardingH\x00R\bcimbData\x88\x01\x01B\f\n" +
	"\n" +
	"_cimb_data\"\xc8\x01\n" +
	"\x0eCIMBOnboarding\x12\x1b\n" +
	"\tod_status\x18\x01 \x01(\tR\bodStatus\x12\x1f\n" +
	"\vcasa_status\x18\x02 \x01(\tR\n" +
	"casaStatus\x12\x1f\n" +
	"\vsign_status\x18\x03 \x01(\tR\n" +
	"signStatus\x12!\n" +
	"\ferror_detail\x18\x04 \x01(\tR\verrorDetail\x124\n" +
	"\x16manual_approval_reason\x18\x05 \x01(\tR\x14manualApprovalReason\"_\n" +
	"\fCIMBResource\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12;\n" +
	"\x04data\x18\x02 \x03(\v2'.onboarding_service.v1.CIMBResourceDataR\x04data\"`\n" +
	"\x10CIMBResourceData\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x1e\n" +
	"\n" +
	"vietnamese\x18\x02 \x01(\tR\n" +
	"vietnamese\x12\x18\n" +
	"\aenglish\x18\x03 \x01(\tR\aenglish\"\xba\x03\n" +
	"\vUserProfile\x12\x1c\n" +
	"\tkyc_level\x18\x01 \x01(\x05R\tkyc_level\x12$\n" +
	"\rprofile_level\x18\x02 \x01(\x05R\rprofile_level\x12\x1c\n" +
	"\tfull_name\x18\x03 \x01(\tR\tfull_name\x125\n" +
	"\x06gender\x18\x04 \x01(\x0e2\x1d.onboarding_service.v1.GenderR\x06gender\x12\"\n" +
	"\fphone_number\x18\x05 \x01(\tR\fphone_number\x12,\n" +
	"\x11permanent_address\x18\x06 \x01(\tR\x11permanent_address\x12\x18\n" +
	"\aid_type\x18\a \x01(\x05R\aid_type\x12\x1c\n" +
	"\tid_number\x18\b \x01(\tR\tid_number\x12\x14\n" +
	"\x05email\x18\t \x01(\tR\x05email\x12$\n" +
	"\rdate_of_birth\x18\n" +
	" \x01(\tR\rdate_of_birth\x12&\n" +
	"\x0eid_issue_place\x18\v \x01(\tR\x0eid_issue_place\x12$\n" +
	"\rid_issue_date\x18\f \x01(\tR\rid_issue_date\"9\n" +
	"\aContent\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xb8\x02\n" +
	"\x06Action\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x18\n" +
	"\avariant\x18\x03 \x01(\tR\avariant\x12&\n" +
	"\x0ezpa_action_url\x18\x04 \x01(\tR\x0ezpa_action_url\x12&\n" +
	"\x0ezpi_action_url\x18\x05 \x01(\tR\x0ezpi_action_url\x12G\n" +
	"\bmetadata\x18\x06 \x03(\v2+.onboarding_service.v1.Action.MetadataEntryR\bmetadata\x1aQ\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.google.protobuf.AnyR\x05value:\x028\x01\"W\n" +
	"\n" +
	"RejectInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x125\n" +
	"\x06notice\x18\x02 \x01(\v2\x1d.onboarding_service.v1.NoticeR\x06notice\"k\n" +
	"\bRiskInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x14\n" +
	"\x05level\x18\x02 \x01(\tR\x05level\x125\n" +
	"\x06notice\x18\x03 \x01(\v2\x1d.onboarding_service.v1.NoticeR\x06notice\"Y\n" +
	"\fProfileIssue\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x125\n" +
	"\x06notice\x18\x02 \x01(\v2\x1d.onboarding_service.v1.NoticeR\x06notice\"\x80\x01\n" +
	"\rKycStatusData\x128\n" +
	"\x06status\x18\x01 \x01(\x0e2 .onboarding_service.v1.KycStatusR\x06status\x125\n" +
	"\x06notice\x18\x02 \x01(\v2\x1d.onboarding_service.v1.NoticeR\x06notice\"{\n" +
	"\x06Notice\x128\n" +
	"\acontent\x18\x01 \x01(\v2\x1e.onboarding_service.v1.ContentR\acontent\x127\n" +
	"\aactions\x18\x02 \x03(\v2\x1d.onboarding_service.v1.ActionR\aactions\"t\n" +
	"\bBindInfo\x12$\n" +
	"\ronboarding_id\x18\x01 \x01(\x03R\ronboarding_id\x12\x1e\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\n" +
	"account_id\x12\"\n" +
	"\fpartner_code\x18\x03 \x01(\tR\fpartner_code*0\n" +
	"\aOTPType\x12\x14\n" +
	"\x10CONTRACT_SIGNING\x10\x00\x12\x0f\n" +
	"\vLINK_TYPE_3\x10\x01*+\n" +
	"\x06Gender\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\b\n" +
	"\x04MALE\x10\x01\x12\n" +
	"\n" +
	"\x06FEMALE\x10\x02*q\n" +
	"\n" +
	"BindStatus\x12\x17\n" +
	"\x13BIND_STATUS_UNKNOWN\x10\x00\x12\x17\n" +
	"\x13BIND_STATUS_UNBOUND\x10\x01\x12\x15\n" +
	"\x11BIND_STATUS_BOUND\x10\x02\x12\x1a\n" +
	"\x16BIND_STATUS_ONBOARDING\x10\x03*\x9c\x01\n" +
	"\tKycStatus\x12\x16\n" +
	"\x12KYC_STATUS_INVALID\x10\x00\x12\x19\n" +
	"\x15KYC_STATUS_PROCESSING\x10\x01\x12\x16\n" +
	"\x12KYC_STATUS_APPROVE\x10\x02\x12\x15\n" +
	"\x11KYC_STATUS_REJECT\x10\x03\x12\x15\n" +
	"\x11KYC_STATUS_BYPASS\x10\x04\x12\x16\n" +
	"\x12KYC_STATUS_UNKNOWN\x10\x05*\x9f\x03\n" +
	"\fResourceType\x12\x16\n" +
	"\x12NATURE_OF_BUSINESS\x10\x00\x12\x0e\n" +
	"\n" +
	"OCCUPATION\x10\x01\x12\r\n" +
	"\tJOB_TITLE\x10\x02\x12\b\n" +
	"\x04CITY\x10\x03\x12\f\n" +
	"\bDISTRICT\x10\x04\x12\b\n" +
	"\x04WARD\x10\x05\x12\n" +
	"\n" +
	"\x06GENDER\x10\x06\x12\x12\n" +
	"\x0eSOURCE_OF_FUND\x10\a\x12\x12\n" +
	"\x0eMARITAL_STATUS\x10\b\x12\x15\n" +
	"\x11EMPLOYMENT_STATUS\x10\t\x12\x1e\n" +
	"\x1aGENERAL_TERM_AND_CONDITION\x10\n" +
	"\x12\x10\n" +
	"\fCOMPANY_TYPE\x10\v\x12\x1b\n" +
	"\x17LOAN_TERM_AND_CONDITION\x10\f\x12\x18\n" +
	"\x14OPENING_CASA_PURPOSE\x10\r\x12\x0e\n" +
	"\n" +
	"NAPAS_BANK\x10\x0e\x12\x1a\n" +
	"\x16REFERENCE_CONTACT_TYPE\x10\x0f\x12\x11\n" +
	"\rCUSTOMER_TYPE\x10\x10\x12\r\n" +
	"\tEDUCATION\x10\x11\x12\x16\n" +
	"\x12LOAN_CANCEL_REASON\x10\x12\x12\n" +
	"\n" +
	"\x06INCOME\x10\x13\x12\x10\n" +
	"\fFUND_PURPOSE\x10\x142\xa4\x17\n" +
	"\n" +
	"Onboarding\x12\xa9\x01\n" +
	"\x14GetClientPermissions\x122.onboarding_service.v1.GetClientPermissionsRequest\x1a3.onboarding_service.v1.GetClientPermissionsResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /onboarding/v1/users/permissions\x12\xaa\x01\n" +
	"\x14GetClientEkycProfile\x122.onboarding_service.v1.GetClientEkycProfileRequest\x1a3.onboarding_service.v1.GetClientEkycProfileResponse\")\x82\xd3\xe4\x93\x02#\x12!/onboarding/v1/users/ekyc-profile\x12\xa9\x01\n" +
	"\x12ResetClientEkycNfc\x120.onboarding_service.v1.ResetClientEkycNfcRequest\x1a1.onboarding_service.v1.ResetClientEkycNfcResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/onboarding/v1/users/ekyc-nfc/reset\x12\xaf\x01\n" +
	"\x18RegisterClientOnboarding\x126.onboarding_service.v1.RegisterClientOnboardingRequest\x1a7.onboarding_service.v1.RegisterClientOnboardingResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/onboarding/v1/register\x12\xb0\x01\n" +
	"\x13GetClientOnboarding\x121.onboarding_service.v1.GetClientOnboardingRequest\x1a2.onboarding_service.v1.GetClientOnboardingResponse\"2\x82\xd3\xe4\x93\x02,\x12*/onboarding/v1/onboardings/{onboarding_id}\x12\xa3\x01\n" +
	"\x14ListClientOnboarding\x122.onboarding_service.v1.ListClientOnboardingRequest\x1a3.onboarding_service.v1.ListClientOnboardingResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/onboarding/v1/onboardings\x12\xaf\x01\n" +
	"\x17SubmitClientApplication\x125.onboarding_service.v1.SubmitClientApplicationRequest\x1a6.onboarding_service.v1.SubmitClientApplicationResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/onboarding/v1/application\x12\xa0\x01\n" +
	"\x13SubmitClientWetSign\x121.onboarding_service.v1.SubmitClientWetSignRequest\x1a2.onboarding_service.v1.SubmitClientWetSignResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/onboarding/v1/wet-sign\x12\xb8\x01\n" +
	"\x19SubmitClientFaceChallenge\x127.onboarding_service.v1.SubmitClientFaceChallengeRequest\x1a8.onboarding_service.v1.SubmitClientFaceChallengeResponse\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/onboarding/v1/face-challenge\x12\x9a\x01\n" +
	"\x10RequestClientOTP\x12..onboarding_service.v1.RequestClientOTPRequest\x1a/.onboarding_service.v1.RequestClientOTPResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/onboarding/v1/request-otp\x12\x96\x01\n" +
	"\x0fVerifyClientOTP\x12-.onboarding_service.v1.VerifyClientOTPRequest\x1a..onboarding_service.v1.VerifyClientOTPResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/onboarding/v1/verify-otp\x12\x9b\x01\n" +
	"\x12GetClientResources\x120.onboarding_service.v1.GetClientResourcesRequest\x1a1.onboarding_service.v1.GetClientResourcesResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/onboarding/v1/resources\x12\x97\x01\n" +
	"\x11GetClientContract\x12/.onboarding_service.v1.GetClientContractRequest\x1a0.onboarding_service.v1.GetClientContractResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/onboarding/v1/contract\x12\x9b\x01\n" +
	"\x12GetClientRejection\x120.onboarding_service.v1.GetClientRejectionRequest\x1a1.onboarding_service.v1.GetClientRejectionResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/onboarding/v1/rejection\x12\xab\x01\n" +
	"\x16ReinitClientOnboarding\x124.onboarding_service.v1.ReinitClientOnboardingRequest\x1a5.onboarding_service.v1.ReinitClientOnboardingResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/onboarding/v1/reinitiate\x12\xa7\x01\n" +
	"\x14LinkingClientAccount\x122.onboarding_service.v1.LinkingClientAccountRequest\x1a3.onboarding_service.v1.LinkingClientAccountResponse\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/onboarding/v1/link-account\x12\x84\x01\n" +
	"\x15QueryOnboardingStatus\x123.onboarding_service.v1.QueryOnboardingStatusRequest\x1a4.onboarding_service.v1.QueryOnboardingStatusResponse\"\x00\x12\x8a\x01\n" +
	"\x17ListOnboardingByUserIDs\x125.onboarding_service.v1.ListOnboardingByUserIDsRequest\x1a6.onboarding_service.v1.ListOnboardingByUserIDsResponse\"\x00B*Z(installment/api/onboarding-service/v1;v1b\x06proto3"

var (
	file_onboarding_service_v1_onboarding_service_proto_rawDescOnce sync.Once
	file_onboarding_service_v1_onboarding_service_proto_rawDescData []byte
)

func file_onboarding_service_v1_onboarding_service_proto_rawDescGZIP() []byte {
	file_onboarding_service_v1_onboarding_service_proto_rawDescOnce.Do(func() {
		file_onboarding_service_v1_onboarding_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_onboarding_service_v1_onboarding_service_proto_rawDesc), len(file_onboarding_service_v1_onboarding_service_proto_rawDesc)))
	})
	return file_onboarding_service_v1_onboarding_service_proto_rawDescData
}

var file_onboarding_service_v1_onboarding_service_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_onboarding_service_v1_onboarding_service_proto_msgTypes = make([]protoimpl.MessageInfo, 57)
var file_onboarding_service_v1_onboarding_service_proto_goTypes = []any{
	(OTPType)(0),                                     // 0: onboarding_service.v1.OTPType
	(Gender)(0),                                      // 1: onboarding_service.v1.Gender
	(BindStatus)(0),                                  // 2: onboarding_service.v1.BindStatus
	(KycStatus)(0),                                   // 3: onboarding_service.v1.KycStatus
	(ResourceType)(0),                                // 4: onboarding_service.v1.ResourceType
	(*RegisterClientOnboardingRequest)(nil),          // 5: onboarding_service.v1.RegisterClientOnboardingRequest
	(*RegisterClientOnboardingResponse)(nil),         // 6: onboarding_service.v1.RegisterClientOnboardingResponse
	(*ReinitClientOnboardingRequest)(nil),            // 7: onboarding_service.v1.ReinitClientOnboardingRequest
	(*ReinitClientOnboardingResponse)(nil),           // 8: onboarding_service.v1.ReinitClientOnboardingResponse
	(*GetClientPermissionsRequest)(nil),              // 9: onboarding_service.v1.GetClientPermissionsRequest
	(*GetClientPermissionsResponse)(nil),             // 10: onboarding_service.v1.GetClientPermissionsResponse
	(*GetClientEkycProfileRequest)(nil),              // 11: onboarding_service.v1.GetClientEkycProfileRequest
	(*GetClientEkycProfileResponse)(nil),             // 12: onboarding_service.v1.GetClientEkycProfileResponse
	(*ResetClientEkycNfcRequest)(nil),                // 13: onboarding_service.v1.ResetClientEkycNfcRequest
	(*ResetClientEkycNfcResponse)(nil),               // 14: onboarding_service.v1.ResetClientEkycNfcResponse
	(*LinkingClientAccountRequest)(nil),              // 15: onboarding_service.v1.LinkingClientAccountRequest
	(*LinkingClientAccountResponse)(nil),             // 16: onboarding_service.v1.LinkingClientAccountResponse
	(*GetClientOnboardingRequest)(nil),               // 17: onboarding_service.v1.GetClientOnboardingRequest
	(*GetClientOnboardingResponse)(nil),              // 18: onboarding_service.v1.GetClientOnboardingResponse
	(*ListClientOnboardingRequest)(nil),              // 19: onboarding_service.v1.ListClientOnboardingRequest
	(*ListClientOnboardingResponse)(nil),             // 20: onboarding_service.v1.ListClientOnboardingResponse
	(*SubmitClientApplicationRequest)(nil),           // 21: onboarding_service.v1.SubmitClientApplicationRequest
	(*SubmitClientApplicationResponse)(nil),          // 22: onboarding_service.v1.SubmitClientApplicationResponse
	(*SubmitClientWetSignRequest)(nil),               // 23: onboarding_service.v1.SubmitClientWetSignRequest
	(*SubmitClientWetSignResponse)(nil),              // 24: onboarding_service.v1.SubmitClientWetSignResponse
	(*SubmitClientFaceChallengeRequest)(nil),         // 25: onboarding_service.v1.SubmitClientFaceChallengeRequest
	(*SubmitClientFaceChallengeResponse)(nil),        // 26: onboarding_service.v1.SubmitClientFaceChallengeResponse
	(*RequestClientOTPRequest)(nil),                  // 27: onboarding_service.v1.RequestClientOTPRequest
	(*RequestClientOTPResponse)(nil),                 // 28: onboarding_service.v1.RequestClientOTPResponse
	(*VerifyClientOTPRequest)(nil),                   // 29: onboarding_service.v1.VerifyClientOTPRequest
	(*VerifyClientOTPResponse)(nil),                  // 30: onboarding_service.v1.VerifyClientOTPResponse
	(*GetClientContractRequest)(nil),                 // 31: onboarding_service.v1.GetClientContractRequest
	(*GetClientContractResponse)(nil),                // 32: onboarding_service.v1.GetClientContractResponse
	(*GetClientRejectionRequest)(nil),                // 33: onboarding_service.v1.GetClientRejectionRequest
	(*GetClientRejectionResponse)(nil),               // 34: onboarding_service.v1.GetClientRejectionResponse
	(*QueryOnboardingStatusRequest)(nil),             // 35: onboarding_service.v1.QueryOnboardingStatusRequest
	(*QueryOnboardingStatusResponse)(nil),            // 36: onboarding_service.v1.QueryOnboardingStatusResponse
	(*ListOnboardingByUserIDsRequest)(nil),           // 37: onboarding_service.v1.ListOnboardingByUserIDsRequest
	(*ListOnboardingByUserIDsResponse)(nil),          // 38: onboarding_service.v1.ListOnboardingByUserIDsResponse
	(*GetClientResourcesRequest)(nil),                // 39: onboarding_service.v1.GetClientResourcesRequest
	(*GetClientResourcesResponse)(nil),               // 40: onboarding_service.v1.GetClientResourcesResponse
	(*OnboardingData)(nil),                           // 41: onboarding_service.v1.OnboardingData
	(*OnboardingProfile)(nil),                        // 42: onboarding_service.v1.OnboardingProfile
	(*OnboardingFlags)(nil),                          // 43: onboarding_service.v1.OnboardingFlags
	(*PartnerOnboarding)(nil),                        // 44: onboarding_service.v1.PartnerOnboarding
	(*CIMBOnboarding)(nil),                           // 45: onboarding_service.v1.CIMBOnboarding
	(*CIMBResource)(nil),                             // 46: onboarding_service.v1.CIMBResource
	(*CIMBResourceData)(nil),                         // 47: onboarding_service.v1.CIMBResourceData
	(*UserProfile)(nil),                              // 48: onboarding_service.v1.UserProfile
	(*Content)(nil),                                  // 49: onboarding_service.v1.Content
	(*Action)(nil),                                   // 50: onboarding_service.v1.Action
	(*RejectInfo)(nil),                               // 51: onboarding_service.v1.RejectInfo
	(*RiskInfo)(nil),                                 // 52: onboarding_service.v1.RiskInfo
	(*ProfileIssue)(nil),                             // 53: onboarding_service.v1.ProfileIssue
	(*KycStatusData)(nil),                            // 54: onboarding_service.v1.KycStatusData
	(*Notice)(nil),                                   // 55: onboarding_service.v1.Notice
	(*BindInfo)(nil),                                 // 56: onboarding_service.v1.BindInfo
	(*ListClientOnboardingResponse_Onboarding)(nil),  // 57: onboarding_service.v1.ListClientOnboardingResponse.Onboarding
	(*SubmitClientWetSignRequest_SignImageData)(nil), // 58: onboarding_service.v1.SubmitClientWetSignRequest.SignImageData
	(*SubmitClientWetSignRequest_SignImageUri)(nil),  // 59: onboarding_service.v1.SubmitClientWetSignRequest.SignImageUri
	(*QueryOnboardingStatusResponse_Flags)(nil),      // 60: onboarding_service.v1.QueryOnboardingStatusResponse.Flags
	nil,                           // 61: onboarding_service.v1.Action.MetadataEntry
	(*timestamppb.Timestamp)(nil), // 62: google.protobuf.Timestamp
	(*anypb.Any)(nil),             // 63: google.protobuf.Any
}
var file_onboarding_service_v1_onboarding_service_proto_depIdxs = []int32{
	2,  // 0: onboarding_service.v1.GetClientPermissionsResponse.bind_status:type_name -> onboarding_service.v1.BindStatus
	54, // 1: onboarding_service.v1.GetClientPermissionsResponse.kyc_status:type_name -> onboarding_service.v1.KycStatusData
	52, // 2: onboarding_service.v1.GetClientPermissionsResponse.risk_info:type_name -> onboarding_service.v1.RiskInfo
	56, // 3: onboarding_service.v1.GetClientPermissionsResponse.bind_info:type_name -> onboarding_service.v1.BindInfo
	48, // 4: onboarding_service.v1.GetClientEkycProfileResponse.profile_info:type_name -> onboarding_service.v1.UserProfile
	53, // 5: onboarding_service.v1.GetClientEkycProfileResponse.profile_issues:type_name -> onboarding_service.v1.ProfileIssue
	57, // 6: onboarding_service.v1.ListClientOnboardingResponse.onboardings:type_name -> onboarding_service.v1.ListClientOnboardingResponse.Onboarding
	59, // 7: onboarding_service.v1.SubmitClientWetSignRequest.image_uri:type_name -> onboarding_service.v1.SubmitClientWetSignRequest.SignImageUri
	58, // 8: onboarding_service.v1.SubmitClientWetSignRequest.image_data:type_name -> onboarding_service.v1.SubmitClientWetSignRequest.SignImageData
	0,  // 9: onboarding_service.v1.RequestClientOTPRequest.otp_type:type_name -> onboarding_service.v1.OTPType
	0,  // 10: onboarding_service.v1.VerifyClientOTPRequest.otp_type:type_name -> onboarding_service.v1.OTPType
	49, // 11: onboarding_service.v1.GetClientRejectionResponse.content:type_name -> onboarding_service.v1.Content
	50, // 12: onboarding_service.v1.GetClientRejectionResponse.actions:type_name -> onboarding_service.v1.Action
	60, // 13: onboarding_service.v1.QueryOnboardingStatusResponse.flags:type_name -> onboarding_service.v1.QueryOnboardingStatusResponse.Flags
	41, // 14: onboarding_service.v1.ListOnboardingByUserIDsResponse.onboardings:type_name -> onboarding_service.v1.OnboardingData
	4,  // 15: onboarding_service.v1.GetClientResourcesRequest.resource_types:type_name -> onboarding_service.v1.ResourceType
	46, // 16: onboarding_service.v1.GetClientResourcesResponse.resources:type_name -> onboarding_service.v1.CIMBResource
	42, // 17: onboarding_service.v1.OnboardingData.profile_info:type_name -> onboarding_service.v1.OnboardingProfile
	44, // 18: onboarding_service.v1.OnboardingData.partner_data:type_name -> onboarding_service.v1.PartnerOnboarding
	43, // 19: onboarding_service.v1.OnboardingData.status_flags:type_name -> onboarding_service.v1.OnboardingFlags
	45, // 20: onboarding_service.v1.PartnerOnboarding.cimb_data:type_name -> onboarding_service.v1.CIMBOnboarding
	47, // 21: onboarding_service.v1.CIMBResource.data:type_name -> onboarding_service.v1.CIMBResourceData
	1,  // 22: onboarding_service.v1.UserProfile.gender:type_name -> onboarding_service.v1.Gender
	61, // 23: onboarding_service.v1.Action.metadata:type_name -> onboarding_service.v1.Action.MetadataEntry
	55, // 24: onboarding_service.v1.RejectInfo.notice:type_name -> onboarding_service.v1.Notice
	55, // 25: onboarding_service.v1.RiskInfo.notice:type_name -> onboarding_service.v1.Notice
	55, // 26: onboarding_service.v1.ProfileIssue.notice:type_name -> onboarding_service.v1.Notice
	3,  // 27: onboarding_service.v1.KycStatusData.status:type_name -> onboarding_service.v1.KycStatus
	55, // 28: onboarding_service.v1.KycStatusData.notice:type_name -> onboarding_service.v1.Notice
	49, // 29: onboarding_service.v1.Notice.content:type_name -> onboarding_service.v1.Content
	50, // 30: onboarding_service.v1.Notice.actions:type_name -> onboarding_service.v1.Action
	62, // 31: onboarding_service.v1.ListClientOnboardingResponse.Onboarding.created_at:type_name -> google.protobuf.Timestamp
	62, // 32: onboarding_service.v1.ListClientOnboardingResponse.Onboarding.updated_at:type_name -> google.protobuf.Timestamp
	63, // 33: onboarding_service.v1.Action.MetadataEntry.value:type_name -> google.protobuf.Any
	9,  // 34: onboarding_service.v1.Onboarding.GetClientPermissions:input_type -> onboarding_service.v1.GetClientPermissionsRequest
	11, // 35: onboarding_service.v1.Onboarding.GetClientEkycProfile:input_type -> onboarding_service.v1.GetClientEkycProfileRequest
	13, // 36: onboarding_service.v1.Onboarding.ResetClientEkycNfc:input_type -> onboarding_service.v1.ResetClientEkycNfcRequest
	5,  // 37: onboarding_service.v1.Onboarding.RegisterClientOnboarding:input_type -> onboarding_service.v1.RegisterClientOnboardingRequest
	17, // 38: onboarding_service.v1.Onboarding.GetClientOnboarding:input_type -> onboarding_service.v1.GetClientOnboardingRequest
	19, // 39: onboarding_service.v1.Onboarding.ListClientOnboarding:input_type -> onboarding_service.v1.ListClientOnboardingRequest
	21, // 40: onboarding_service.v1.Onboarding.SubmitClientApplication:input_type -> onboarding_service.v1.SubmitClientApplicationRequest
	23, // 41: onboarding_service.v1.Onboarding.SubmitClientWetSign:input_type -> onboarding_service.v1.SubmitClientWetSignRequest
	25, // 42: onboarding_service.v1.Onboarding.SubmitClientFaceChallenge:input_type -> onboarding_service.v1.SubmitClientFaceChallengeRequest
	27, // 43: onboarding_service.v1.Onboarding.RequestClientOTP:input_type -> onboarding_service.v1.RequestClientOTPRequest
	29, // 44: onboarding_service.v1.Onboarding.VerifyClientOTP:input_type -> onboarding_service.v1.VerifyClientOTPRequest
	39, // 45: onboarding_service.v1.Onboarding.GetClientResources:input_type -> onboarding_service.v1.GetClientResourcesRequest
	31, // 46: onboarding_service.v1.Onboarding.GetClientContract:input_type -> onboarding_service.v1.GetClientContractRequest
	33, // 47: onboarding_service.v1.Onboarding.GetClientRejection:input_type -> onboarding_service.v1.GetClientRejectionRequest
	7,  // 48: onboarding_service.v1.Onboarding.ReinitClientOnboarding:input_type -> onboarding_service.v1.ReinitClientOnboardingRequest
	15, // 49: onboarding_service.v1.Onboarding.LinkingClientAccount:input_type -> onboarding_service.v1.LinkingClientAccountRequest
	35, // 50: onboarding_service.v1.Onboarding.QueryOnboardingStatus:input_type -> onboarding_service.v1.QueryOnboardingStatusRequest
	37, // 51: onboarding_service.v1.Onboarding.ListOnboardingByUserIDs:input_type -> onboarding_service.v1.ListOnboardingByUserIDsRequest
	10, // 52: onboarding_service.v1.Onboarding.GetClientPermissions:output_type -> onboarding_service.v1.GetClientPermissionsResponse
	12, // 53: onboarding_service.v1.Onboarding.GetClientEkycProfile:output_type -> onboarding_service.v1.GetClientEkycProfileResponse
	14, // 54: onboarding_service.v1.Onboarding.ResetClientEkycNfc:output_type -> onboarding_service.v1.ResetClientEkycNfcResponse
	6,  // 55: onboarding_service.v1.Onboarding.RegisterClientOnboarding:output_type -> onboarding_service.v1.RegisterClientOnboardingResponse
	18, // 56: onboarding_service.v1.Onboarding.GetClientOnboarding:output_type -> onboarding_service.v1.GetClientOnboardingResponse
	20, // 57: onboarding_service.v1.Onboarding.ListClientOnboarding:output_type -> onboarding_service.v1.ListClientOnboardingResponse
	22, // 58: onboarding_service.v1.Onboarding.SubmitClientApplication:output_type -> onboarding_service.v1.SubmitClientApplicationResponse
	24, // 59: onboarding_service.v1.Onboarding.SubmitClientWetSign:output_type -> onboarding_service.v1.SubmitClientWetSignResponse
	26, // 60: onboarding_service.v1.Onboarding.SubmitClientFaceChallenge:output_type -> onboarding_service.v1.SubmitClientFaceChallengeResponse
	28, // 61: onboarding_service.v1.Onboarding.RequestClientOTP:output_type -> onboarding_service.v1.RequestClientOTPResponse
	30, // 62: onboarding_service.v1.Onboarding.VerifyClientOTP:output_type -> onboarding_service.v1.VerifyClientOTPResponse
	40, // 63: onboarding_service.v1.Onboarding.GetClientResources:output_type -> onboarding_service.v1.GetClientResourcesResponse
	32, // 64: onboarding_service.v1.Onboarding.GetClientContract:output_type -> onboarding_service.v1.GetClientContractResponse
	34, // 65: onboarding_service.v1.Onboarding.GetClientRejection:output_type -> onboarding_service.v1.GetClientRejectionResponse
	8,  // 66: onboarding_service.v1.Onboarding.ReinitClientOnboarding:output_type -> onboarding_service.v1.ReinitClientOnboardingResponse
	16, // 67: onboarding_service.v1.Onboarding.LinkingClientAccount:output_type -> onboarding_service.v1.LinkingClientAccountResponse
	36, // 68: onboarding_service.v1.Onboarding.QueryOnboardingStatus:output_type -> onboarding_service.v1.QueryOnboardingStatusResponse
	38, // 69: onboarding_service.v1.Onboarding.ListOnboardingByUserIDs:output_type -> onboarding_service.v1.ListOnboardingByUserIDsResponse
	52, // [52:70] is the sub-list for method output_type
	34, // [34:52] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_onboarding_service_v1_onboarding_service_proto_init() }
func file_onboarding_service_v1_onboarding_service_proto_init() {
	if File_onboarding_service_v1_onboarding_service_proto != nil {
		return
	}
	file_onboarding_service_v1_onboarding_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_onboarding_service_v1_onboarding_service_proto_msgTypes[18].OneofWrappers = []any{
		(*SubmitClientWetSignRequest_ImageUri)(nil),
		(*SubmitClientWetSignRequest_ImageData)(nil),
	}
	file_onboarding_service_v1_onboarding_service_proto_msgTypes[36].OneofWrappers = []any{}
	file_onboarding_service_v1_onboarding_service_proto_msgTypes[39].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_onboarding_service_v1_onboarding_service_proto_rawDesc), len(file_onboarding_service_v1_onboarding_service_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   57,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_onboarding_service_v1_onboarding_service_proto_goTypes,
		DependencyIndexes: file_onboarding_service_v1_onboarding_service_proto_depIdxs,
		EnumInfos:         file_onboarding_service_v1_onboarding_service_proto_enumTypes,
		MessageInfos:      file_onboarding_service_v1_onboarding_service_proto_msgTypes,
	}.Build()
	File_onboarding_service_v1_onboarding_service_proto = out.File
	file_onboarding_service_v1_onboarding_service_proto_goTypes = nil
	file_onboarding_service_v1_onboarding_service_proto_depIdxs = nil
}
