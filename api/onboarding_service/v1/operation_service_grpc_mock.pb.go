// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: onboarding_service/v1/operation_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockOperationClient is a mock of OperationClient interface.
type MockOperationClient struct {
	ctrl     *gomock.Controller
	recorder *MockOperationClientMockRecorder
}

// MockOperationClientMockRecorder is the mock recorder for MockOperationClient.
type MockOperationClientMockRecorder struct {
	mock *MockOperationClient
}

// NewMockOperationClient creates a new mock instance.
func NewMockOperationClient(ctrl *gomock.Controller) *MockOperationClient {
	mock := &MockOperationClient{ctrl: ctrl}
	mock.recorder = &MockOperationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOperationClient) EXPECT() *MockOperationClientMockRecorder {
	return m.recorder
}

// TriggerContractSigningJob mocks base method.
func (m *MockOperationClient) TriggerContractSigningJob(ctx context.Context, in *TriggerContractSigningJobRequest, opts ...grpc.CallOption) (*TriggerContractSigningJobResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerContractSigningJob", varargs...)
	ret0, _ := ret[0].(*TriggerContractSigningJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerContractSigningJob indicates an expected call of TriggerContractSigningJob.
func (mr *MockOperationClientMockRecorder) TriggerContractSigningJob(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerContractSigningJob", reflect.TypeOf((*MockOperationClient)(nil).TriggerContractSigningJob), varargs...)
}

// TriggerSubmitSelfieJob mocks base method.
func (m *MockOperationClient) TriggerSubmitSelfieJob(ctx context.Context, in *TriggerSubmitSelfieJobRequest, opts ...grpc.CallOption) (*TriggerSubmitSelfieJobResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSubmitSelfieJob", varargs...)
	ret0, _ := ret[0].(*TriggerSubmitSelfieJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSubmitSelfieJob indicates an expected call of TriggerSubmitSelfieJob.
func (mr *MockOperationClientMockRecorder) TriggerSubmitSelfieJob(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSubmitSelfieJob", reflect.TypeOf((*MockOperationClient)(nil).TriggerSubmitSelfieJob), varargs...)
}

// TriggerSyncOnboarding mocks base method.
func (m *MockOperationClient) TriggerSyncOnboarding(ctx context.Context, in *TriggerSyncOnboardingRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSyncOnboarding", varargs...)
	ret0, _ := ret[0].(*TriggerSyncOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboarding indicates an expected call of TriggerSyncOnboarding.
func (mr *MockOperationClientMockRecorder) TriggerSyncOnboarding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboarding", reflect.TypeOf((*MockOperationClient)(nil).TriggerSyncOnboarding), varargs...)
}

// TriggerSyncOnboardingJob mocks base method.
func (m *MockOperationClient) TriggerSyncOnboardingJob(ctx context.Context, in *TriggerSyncOnboardingJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingJobResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSyncOnboardingJob", varargs...)
	ret0, _ := ret[0].(*TriggerSyncOnboardingJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboardingJob indicates an expected call of TriggerSyncOnboardingJob.
func (mr *MockOperationClientMockRecorder) TriggerSyncOnboardingJob(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboardingJob", reflect.TypeOf((*MockOperationClient)(nil).TriggerSyncOnboardingJob), varargs...)
}

// TriggerSyncOnboardingsJob mocks base method.
func (m *MockOperationClient) TriggerSyncOnboardingsJob(ctx context.Context, in *TriggerSyncOnboardingsJobRequest, opts ...grpc.CallOption) (*TriggerSyncOnboardingsJobResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSyncOnboardingsJob", varargs...)
	ret0, _ := ret[0].(*TriggerSyncOnboardingsJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboardingsJob indicates an expected call of TriggerSyncOnboardingsJob.
func (mr *MockOperationClientMockRecorder) TriggerSyncOnboardingsJob(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboardingsJob", reflect.TypeOf((*MockOperationClient)(nil).TriggerSyncOnboardingsJob), varargs...)
}

// MockOperationServer is a mock of OperationServer interface.
type MockOperationServer struct {
	ctrl     *gomock.Controller
	recorder *MockOperationServerMockRecorder
}

// MockOperationServerMockRecorder is the mock recorder for MockOperationServer.
type MockOperationServerMockRecorder struct {
	mock *MockOperationServer
}

// NewMockOperationServer creates a new mock instance.
func NewMockOperationServer(ctrl *gomock.Controller) *MockOperationServer {
	mock := &MockOperationServer{ctrl: ctrl}
	mock.recorder = &MockOperationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOperationServer) EXPECT() *MockOperationServerMockRecorder {
	return m.recorder
}

// TriggerContractSigningJob mocks base method.
func (m *MockOperationServer) TriggerContractSigningJob(ctx context.Context, in *TriggerContractSigningJobRequest) (*TriggerContractSigningJobResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerContractSigningJob", ctx, in)
	ret0, _ := ret[0].(*TriggerContractSigningJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerContractSigningJob indicates an expected call of TriggerContractSigningJob.
func (mr *MockOperationServerMockRecorder) TriggerContractSigningJob(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerContractSigningJob", reflect.TypeOf((*MockOperationServer)(nil).TriggerContractSigningJob), ctx, in)
}

// TriggerSubmitSelfieJob mocks base method.
func (m *MockOperationServer) TriggerSubmitSelfieJob(ctx context.Context, in *TriggerSubmitSelfieJobRequest) (*TriggerSubmitSelfieJobResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSubmitSelfieJob", ctx, in)
	ret0, _ := ret[0].(*TriggerSubmitSelfieJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSubmitSelfieJob indicates an expected call of TriggerSubmitSelfieJob.
func (mr *MockOperationServerMockRecorder) TriggerSubmitSelfieJob(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSubmitSelfieJob", reflect.TypeOf((*MockOperationServer)(nil).TriggerSubmitSelfieJob), ctx, in)
}

// TriggerSyncOnboarding mocks base method.
func (m *MockOperationServer) TriggerSyncOnboarding(ctx context.Context, in *TriggerSyncOnboardingRequest) (*TriggerSyncOnboardingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSyncOnboarding", ctx, in)
	ret0, _ := ret[0].(*TriggerSyncOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboarding indicates an expected call of TriggerSyncOnboarding.
func (mr *MockOperationServerMockRecorder) TriggerSyncOnboarding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboarding", reflect.TypeOf((*MockOperationServer)(nil).TriggerSyncOnboarding), ctx, in)
}

// TriggerSyncOnboardingJob mocks base method.
func (m *MockOperationServer) TriggerSyncOnboardingJob(ctx context.Context, in *TriggerSyncOnboardingJobRequest) (*TriggerSyncOnboardingJobResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSyncOnboardingJob", ctx, in)
	ret0, _ := ret[0].(*TriggerSyncOnboardingJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboardingJob indicates an expected call of TriggerSyncOnboardingJob.
func (mr *MockOperationServerMockRecorder) TriggerSyncOnboardingJob(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboardingJob", reflect.TypeOf((*MockOperationServer)(nil).TriggerSyncOnboardingJob), ctx, in)
}

// TriggerSyncOnboardingsJob mocks base method.
func (m *MockOperationServer) TriggerSyncOnboardingsJob(ctx context.Context, in *TriggerSyncOnboardingsJobRequest) (*TriggerSyncOnboardingsJobResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSyncOnboardingsJob", ctx, in)
	ret0, _ := ret[0].(*TriggerSyncOnboardingsJobResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncOnboardingsJob indicates an expected call of TriggerSyncOnboardingsJob.
func (mr *MockOperationServerMockRecorder) TriggerSyncOnboardingsJob(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncOnboardingsJob", reflect.TypeOf((*MockOperationServer)(nil).TriggerSyncOnboardingsJob), ctx, in)
}
