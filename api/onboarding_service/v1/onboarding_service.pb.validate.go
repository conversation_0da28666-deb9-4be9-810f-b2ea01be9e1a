// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: onboarding_service/v1/onboarding_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterClientOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterClientOnboardingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RegisterClientOnboardingRequestMultiError, or nil if none found.
func (m *RegisterClientOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterClientOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.PartnerCode != nil {
		// no validation rules for PartnerCode
	}

	if len(errors) > 0 {
		return RegisterClientOnboardingRequestMultiError(errors)
	}

	return nil
}

// RegisterClientOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by RegisterClientOnboardingRequest.ValidateAll()
// if the designated constraints aren't met.
type RegisterClientOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterClientOnboardingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterClientOnboardingRequestMultiError) AllErrors() []error { return m }

// RegisterClientOnboardingRequestValidationError is the validation error
// returned by RegisterClientOnboardingRequest.Validate if the designated
// constraints aren't met.
type RegisterClientOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterClientOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterClientOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterClientOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterClientOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterClientOnboardingRequestValidationError) ErrorName() string {
	return "RegisterClientOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterClientOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterClientOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterClientOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterClientOnboardingRequestValidationError{}

// Validate checks the field values on RegisterClientOnboardingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RegisterClientOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterClientOnboardingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RegisterClientOnboardingResponseMultiError, or nil if none found.
func (m *RegisterClientOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterClientOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingId

	if len(errors) > 0 {
		return RegisterClientOnboardingResponseMultiError(errors)
	}

	return nil
}

// RegisterClientOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by
// RegisterClientOnboardingResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterClientOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterClientOnboardingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterClientOnboardingResponseMultiError) AllErrors() []error { return m }

// RegisterClientOnboardingResponseValidationError is the validation error
// returned by RegisterClientOnboardingResponse.Validate if the designated
// constraints aren't met.
type RegisterClientOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterClientOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterClientOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterClientOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterClientOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterClientOnboardingResponseValidationError) ErrorName() string {
	return "RegisterClientOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterClientOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterClientOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterClientOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterClientOnboardingResponseValidationError{}

// Validate checks the field values on ReinitClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReinitClientOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReinitClientOnboardingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReinitClientOnboardingRequestMultiError, or nil if none found.
func (m *ReinitClientOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReinitClientOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := ReinitClientOnboardingRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReinitClientOnboardingRequestMultiError(errors)
	}

	return nil
}

// ReinitClientOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by ReinitClientOnboardingRequest.ValidateAll()
// if the designated constraints aren't met.
type ReinitClientOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReinitClientOnboardingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReinitClientOnboardingRequestMultiError) AllErrors() []error { return m }

// ReinitClientOnboardingRequestValidationError is the validation error
// returned by ReinitClientOnboardingRequest.Validate if the designated
// constraints aren't met.
type ReinitClientOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReinitClientOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReinitClientOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReinitClientOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReinitClientOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReinitClientOnboardingRequestValidationError) ErrorName() string {
	return "ReinitClientOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReinitClientOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReinitClientOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReinitClientOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReinitClientOnboardingRequestValidationError{}

// Validate checks the field values on ReinitClientOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReinitClientOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReinitClientOnboardingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReinitClientOnboardingResponseMultiError, or nil if none found.
func (m *ReinitClientOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReinitClientOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReinitClientOnboardingResponseMultiError(errors)
	}

	return nil
}

// ReinitClientOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by ReinitClientOnboardingResponse.ValidateAll()
// if the designated constraints aren't met.
type ReinitClientOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReinitClientOnboardingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReinitClientOnboardingResponseMultiError) AllErrors() []error { return m }

// ReinitClientOnboardingResponseValidationError is the validation error
// returned by ReinitClientOnboardingResponse.Validate if the designated
// constraints aren't met.
type ReinitClientOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReinitClientOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReinitClientOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReinitClientOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReinitClientOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReinitClientOnboardingResponseValidationError) ErrorName() string {
	return "ReinitClientOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReinitClientOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReinitClientOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReinitClientOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReinitClientOnboardingResponseValidationError{}

// Validate checks the field values on GetClientPermissionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientPermissionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientPermissionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientPermissionsRequestMultiError, or nil if none found.
func (m *GetClientPermissionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientPermissionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetClientPermissionsRequestMultiError(errors)
	}

	return nil
}

// GetClientPermissionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientPermissionsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetClientPermissionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientPermissionsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientPermissionsRequestMultiError) AllErrors() []error { return m }

// GetClientPermissionsRequestValidationError is the validation error returned
// by GetClientPermissionsRequest.Validate if the designated constraints
// aren't met.
type GetClientPermissionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientPermissionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientPermissionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientPermissionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientPermissionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientPermissionsRequestValidationError) ErrorName() string {
	return "GetClientPermissionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientPermissionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientPermissionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientPermissionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientPermissionsRequestValidationError{}

// Validate checks the field values on GetClientPermissionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientPermissionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientPermissionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientPermissionsResponseMultiError, or nil if none found.
func (m *GetClientPermissionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientPermissionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsWhitelisted

	// no validation rules for BindStatus

	if all {
		switch v := interface{}(m.GetKycStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "KycStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "KycStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientPermissionsResponseValidationError{
				field:  "KycStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "RiskInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "RiskInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientPermissionsResponseValidationError{
				field:  "RiskInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBindInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "BindInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientPermissionsResponseValidationError{
					field:  "BindInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBindInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientPermissionsResponseValidationError{
				field:  "BindInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClientPermissionsResponseMultiError(errors)
	}

	return nil
}

// GetClientPermissionsResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientPermissionsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientPermissionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientPermissionsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientPermissionsResponseMultiError) AllErrors() []error { return m }

// GetClientPermissionsResponseValidationError is the validation error returned
// by GetClientPermissionsResponse.Validate if the designated constraints
// aren't met.
type GetClientPermissionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientPermissionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientPermissionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientPermissionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientPermissionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientPermissionsResponseValidationError) ErrorName() string {
	return "GetClientPermissionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientPermissionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientPermissionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientPermissionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientPermissionsResponseValidationError{}

// Validate checks the field values on GetClientEkycProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientEkycProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientEkycProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientEkycProfileRequestMultiError, or nil if none found.
func (m *GetClientEkycProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientEkycProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NeedVerifyProfile

	if len(errors) > 0 {
		return GetClientEkycProfileRequestMultiError(errors)
	}

	return nil
}

// GetClientEkycProfileRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientEkycProfileRequest.ValidateAll() if
// the designated constraints aren't met.
type GetClientEkycProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientEkycProfileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientEkycProfileRequestMultiError) AllErrors() []error { return m }

// GetClientEkycProfileRequestValidationError is the validation error returned
// by GetClientEkycProfileRequest.Validate if the designated constraints
// aren't met.
type GetClientEkycProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientEkycProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientEkycProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientEkycProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientEkycProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientEkycProfileRequestValidationError) ErrorName() string {
	return "GetClientEkycProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientEkycProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientEkycProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientEkycProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientEkycProfileRequestValidationError{}

// Validate checks the field values on GetClientEkycProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientEkycProfileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientEkycProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientEkycProfileResponseMultiError, or nil if none found.
func (m *GetClientEkycProfileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientEkycProfileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProfileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientEkycProfileResponseValidationError{
					field:  "ProfileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientEkycProfileResponseValidationError{
					field:  "ProfileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientEkycProfileResponseValidationError{
				field:  "ProfileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProfileIssues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClientEkycProfileResponseValidationError{
						field:  fmt.Sprintf("ProfileIssues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClientEkycProfileResponseValidationError{
						field:  fmt.Sprintf("ProfileIssues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClientEkycProfileResponseValidationError{
					field:  fmt.Sprintf("ProfileIssues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetClientEkycProfileResponseMultiError(errors)
	}

	return nil
}

// GetClientEkycProfileResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientEkycProfileResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientEkycProfileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientEkycProfileResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientEkycProfileResponseMultiError) AllErrors() []error { return m }

// GetClientEkycProfileResponseValidationError is the validation error returned
// by GetClientEkycProfileResponse.Validate if the designated constraints
// aren't met.
type GetClientEkycProfileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientEkycProfileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientEkycProfileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientEkycProfileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientEkycProfileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientEkycProfileResponseValidationError) ErrorName() string {
	return "GetClientEkycProfileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientEkycProfileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientEkycProfileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientEkycProfileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientEkycProfileResponseValidationError{}

// Validate checks the field values on ResetClientEkycNfcRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetClientEkycNfcRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetClientEkycNfcRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetClientEkycNfcRequestMultiError, or nil if none found.
func (m *ResetClientEkycNfcRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetClientEkycNfcRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ResetClientEkycNfcRequestMultiError(errors)
	}

	return nil
}

// ResetClientEkycNfcRequestMultiError is an error wrapping multiple validation
// errors returned by ResetClientEkycNfcRequest.ValidateAll() if the
// designated constraints aren't met.
type ResetClientEkycNfcRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetClientEkycNfcRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetClientEkycNfcRequestMultiError) AllErrors() []error { return m }

// ResetClientEkycNfcRequestValidationError is the validation error returned by
// ResetClientEkycNfcRequest.Validate if the designated constraints aren't met.
type ResetClientEkycNfcRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetClientEkycNfcRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetClientEkycNfcRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetClientEkycNfcRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetClientEkycNfcRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetClientEkycNfcRequestValidationError) ErrorName() string {
	return "ResetClientEkycNfcRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetClientEkycNfcRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetClientEkycNfcRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetClientEkycNfcRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetClientEkycNfcRequestValidationError{}

// Validate checks the field values on ResetClientEkycNfcResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetClientEkycNfcResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetClientEkycNfcResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetClientEkycNfcResponseMultiError, or nil if none found.
func (m *ResetClientEkycNfcResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetClientEkycNfcResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ResetClientEkycNfcResponseMultiError(errors)
	}

	return nil
}

// ResetClientEkycNfcResponseMultiError is an error wrapping multiple
// validation errors returned by ResetClientEkycNfcResponse.ValidateAll() if
// the designated constraints aren't met.
type ResetClientEkycNfcResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetClientEkycNfcResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetClientEkycNfcResponseMultiError) AllErrors() []error { return m }

// ResetClientEkycNfcResponseValidationError is the validation error returned
// by ResetClientEkycNfcResponse.Validate if the designated constraints aren't met.
type ResetClientEkycNfcResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetClientEkycNfcResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetClientEkycNfcResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetClientEkycNfcResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetClientEkycNfcResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetClientEkycNfcResponseValidationError) ErrorName() string {
	return "ResetClientEkycNfcResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetClientEkycNfcResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetClientEkycNfcResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetClientEkycNfcResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetClientEkycNfcResponseValidationError{}

// Validate checks the field values on LinkingClientAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkingClientAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkingClientAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkingClientAccountRequestMultiError, or nil if none found.
func (m *LinkingClientAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkingClientAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := LinkingClientAccountRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LinkingClientAccountRequestMultiError(errors)
	}

	return nil
}

// LinkingClientAccountRequestMultiError is an error wrapping multiple
// validation errors returned by LinkingClientAccountRequest.ValidateAll() if
// the designated constraints aren't met.
type LinkingClientAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkingClientAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkingClientAccountRequestMultiError) AllErrors() []error { return m }

// LinkingClientAccountRequestValidationError is the validation error returned
// by LinkingClientAccountRequest.Validate if the designated constraints
// aren't met.
type LinkingClientAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkingClientAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkingClientAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkingClientAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkingClientAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkingClientAccountRequestValidationError) ErrorName() string {
	return "LinkingClientAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkingClientAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkingClientAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkingClientAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkingClientAccountRequestValidationError{}

// Validate checks the field values on LinkingClientAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkingClientAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkingClientAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkingClientAccountResponseMultiError, or nil if none found.
func (m *LinkingClientAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkingClientAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LinkingClientAccountResponseMultiError(errors)
	}

	return nil
}

// LinkingClientAccountResponseMultiError is an error wrapping multiple
// validation errors returned by LinkingClientAccountResponse.ValidateAll() if
// the designated constraints aren't met.
type LinkingClientAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkingClientAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkingClientAccountResponseMultiError) AllErrors() []error { return m }

// LinkingClientAccountResponseValidationError is the validation error returned
// by LinkingClientAccountResponse.Validate if the designated constraints
// aren't met.
type LinkingClientAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkingClientAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkingClientAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkingClientAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkingClientAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkingClientAccountResponseValidationError) ErrorName() string {
	return "LinkingClientAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LinkingClientAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkingClientAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkingClientAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkingClientAccountResponseValidationError{}

// Validate checks the field values on GetClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientOnboardingRequestMultiError, or nil if none found.
func (m *GetClientOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := GetClientOnboardingRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientOnboardingRequestMultiError(errors)
	}

	return nil
}

// GetClientOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientOnboardingRequest.ValidateAll() if
// the designated constraints aren't met.
type GetClientOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientOnboardingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientOnboardingRequestMultiError) AllErrors() []error { return m }

// GetClientOnboardingRequestValidationError is the validation error returned
// by GetClientOnboardingRequest.Validate if the designated constraints aren't met.
type GetClientOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientOnboardingRequestValidationError) ErrorName() string {
	return "GetClientOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientOnboardingRequestValidationError{}

// Validate checks the field values on GetClientOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientOnboardingResponseMultiError, or nil if none found.
func (m *GetClientOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for PartnerCode

	// no validation rules for CurrentStep

	// no validation rules for FullName

	// no validation rules for Gender

	// no validation rules for PhoneNumber

	// no validation rules for IdNumber

	// no validation rules for IdIssueDate

	// no validation rules for IdIssuePlace

	// no validation rules for DateOfBirth

	// no validation rules for PermanentAddress

	// no validation rules for TempResidenceAddress

	if len(errors) > 0 {
		return GetClientOnboardingResponseMultiError(errors)
	}

	return nil
}

// GetClientOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientOnboardingResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientOnboardingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientOnboardingResponseMultiError) AllErrors() []error { return m }

// GetClientOnboardingResponseValidationError is the validation error returned
// by GetClientOnboardingResponse.Validate if the designated constraints
// aren't met.
type GetClientOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientOnboardingResponseValidationError) ErrorName() string {
	return "GetClientOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientOnboardingResponseValidationError{}

// Validate checks the field values on ListClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClientOnboardingRequestMultiError, or nil if none found.
func (m *ListClientOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListClientOnboardingRequestMultiError(errors)
	}

	return nil
}

// ListClientOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by ListClientOnboardingRequest.ValidateAll() if
// the designated constraints aren't met.
type ListClientOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientOnboardingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientOnboardingRequestMultiError) AllErrors() []error { return m }

// ListClientOnboardingRequestValidationError is the validation error returned
// by ListClientOnboardingRequest.Validate if the designated constraints
// aren't met.
type ListClientOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientOnboardingRequestValidationError) ErrorName() string {
	return "ListClientOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientOnboardingRequestValidationError{}

// Validate checks the field values on ListClientOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClientOnboardingResponseMultiError, or nil if none found.
func (m *ListClientOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOnboardings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientOnboardingResponseValidationError{
						field:  fmt.Sprintf("Onboardings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientOnboardingResponseValidationError{
						field:  fmt.Sprintf("Onboardings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientOnboardingResponseValidationError{
					field:  fmt.Sprintf("Onboardings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClientOnboardingResponseMultiError(errors)
	}

	return nil
}

// ListClientOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by ListClientOnboardingResponse.ValidateAll() if
// the designated constraints aren't met.
type ListClientOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientOnboardingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientOnboardingResponseMultiError) AllErrors() []error { return m }

// ListClientOnboardingResponseValidationError is the validation error returned
// by ListClientOnboardingResponse.Validate if the designated constraints
// aren't met.
type ListClientOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientOnboardingResponseValidationError) ErrorName() string {
	return "ListClientOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientOnboardingResponseValidationError{}

// Validate checks the field values on SubmitClientApplicationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClientApplicationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientApplicationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitClientApplicationRequestMultiError, or nil if none found.
func (m *SubmitClientApplicationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientApplicationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := SubmitClientApplicationRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for JobTitle

	// no validation rules for Occupation

	// no validation rules for LivingCity

	if utf8.RuneCountInString(m.GetMonthlyIncome()) < 1 {
		err := SubmitClientApplicationRequestValidationError{
			field:  "MonthlyIncome",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTempResidenceAddress()) < 1 {
		err := SubmitClientApplicationRequestValidationError{
			field:  "TempResidenceAddress",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Education

	// no validation rules for SourceOfFund

	// no validation rules for EmploymentStatus

	if len(errors) > 0 {
		return SubmitClientApplicationRequestMultiError(errors)
	}

	return nil
}

// SubmitClientApplicationRequestMultiError is an error wrapping multiple
// validation errors returned by SubmitClientApplicationRequest.ValidateAll()
// if the designated constraints aren't met.
type SubmitClientApplicationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientApplicationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientApplicationRequestMultiError) AllErrors() []error { return m }

// SubmitClientApplicationRequestValidationError is the validation error
// returned by SubmitClientApplicationRequest.Validate if the designated
// constraints aren't met.
type SubmitClientApplicationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientApplicationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientApplicationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientApplicationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientApplicationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientApplicationRequestValidationError) ErrorName() string {
	return "SubmitClientApplicationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientApplicationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientApplicationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientApplicationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientApplicationRequestValidationError{}

// Validate checks the field values on SubmitClientApplicationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClientApplicationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientApplicationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitClientApplicationResponseMultiError, or nil if none found.
func (m *SubmitClientApplicationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientApplicationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationRejected

	// no validation rules for RequireLinkAccount

	if len(errors) > 0 {
		return SubmitClientApplicationResponseMultiError(errors)
	}

	return nil
}

// SubmitClientApplicationResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitClientApplicationResponse.ValidateAll()
// if the designated constraints aren't met.
type SubmitClientApplicationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientApplicationResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientApplicationResponseMultiError) AllErrors() []error { return m }

// SubmitClientApplicationResponseValidationError is the validation error
// returned by SubmitClientApplicationResponse.Validate if the designated
// constraints aren't met.
type SubmitClientApplicationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientApplicationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientApplicationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientApplicationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientApplicationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientApplicationResponseValidationError) ErrorName() string {
	return "SubmitClientApplicationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientApplicationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientApplicationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientApplicationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientApplicationResponseValidationError{}

// Validate checks the field values on SubmitClientWetSignRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClientWetSignRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientWetSignRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitClientWetSignRequestMultiError, or nil if none found.
func (m *SubmitClientWetSignRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientWetSignRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := SubmitClientWetSignRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	oneofSignImageResPresent := false
	switch v := m.SignImageRes.(type) {
	case *SubmitClientWetSignRequest_ImageUri:
		if v == nil {
			err := SubmitClientWetSignRequestValidationError{
				field:  "SignImageRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofSignImageResPresent = true

		if all {
			switch v := interface{}(m.GetImageUri()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitClientWetSignRequestValidationError{
						field:  "ImageUri",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitClientWetSignRequestValidationError{
						field:  "ImageUri",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImageUri()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitClientWetSignRequestValidationError{
					field:  "ImageUri",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SubmitClientWetSignRequest_ImageData:
		if v == nil {
			err := SubmitClientWetSignRequestValidationError{
				field:  "SignImageRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofSignImageResPresent = true

		if all {
			switch v := interface{}(m.GetImageData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitClientWetSignRequestValidationError{
						field:  "ImageData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitClientWetSignRequestValidationError{
						field:  "ImageData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImageData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitClientWetSignRequestValidationError{
					field:  "ImageData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	if !oneofSignImageResPresent {
		err := SubmitClientWetSignRequestValidationError{
			field:  "SignImageRes",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SubmitClientWetSignRequestMultiError(errors)
	}

	return nil
}

// SubmitClientWetSignRequestMultiError is an error wrapping multiple
// validation errors returned by SubmitClientWetSignRequest.ValidateAll() if
// the designated constraints aren't met.
type SubmitClientWetSignRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientWetSignRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientWetSignRequestMultiError) AllErrors() []error { return m }

// SubmitClientWetSignRequestValidationError is the validation error returned
// by SubmitClientWetSignRequest.Validate if the designated constraints aren't met.
type SubmitClientWetSignRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientWetSignRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientWetSignRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientWetSignRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientWetSignRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientWetSignRequestValidationError) ErrorName() string {
	return "SubmitClientWetSignRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientWetSignRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientWetSignRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientWetSignRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientWetSignRequestValidationError{}

// Validate checks the field values on SubmitClientWetSignResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClientWetSignResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientWetSignResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitClientWetSignResponseMultiError, or nil if none found.
func (m *SubmitClientWetSignResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientWetSignResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingId

	if len(errors) > 0 {
		return SubmitClientWetSignResponseMultiError(errors)
	}

	return nil
}

// SubmitClientWetSignResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitClientWetSignResponse.ValidateAll() if
// the designated constraints aren't met.
type SubmitClientWetSignResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientWetSignResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientWetSignResponseMultiError) AllErrors() []error { return m }

// SubmitClientWetSignResponseValidationError is the validation error returned
// by SubmitClientWetSignResponse.Validate if the designated constraints
// aren't met.
type SubmitClientWetSignResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientWetSignResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientWetSignResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientWetSignResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientWetSignResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientWetSignResponseValidationError) ErrorName() string {
	return "SubmitClientWetSignResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientWetSignResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientWetSignResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientWetSignResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientWetSignResponseValidationError{}

// Validate checks the field values on SubmitClientFaceChallengeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SubmitClientFaceChallengeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientFaceChallengeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SubmitClientFaceChallengeRequestMultiError, or nil if none found.
func (m *SubmitClientFaceChallengeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientFaceChallengeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := SubmitClientFaceChallengeRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetFaceRequestId()) < 1 {
		err := SubmitClientFaceChallengeRequestValidationError{
			field:  "FaceRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SubmitClientFaceChallengeRequestMultiError(errors)
	}

	return nil
}

// SubmitClientFaceChallengeRequestMultiError is an error wrapping multiple
// validation errors returned by
// SubmitClientFaceChallengeRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitClientFaceChallengeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientFaceChallengeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientFaceChallengeRequestMultiError) AllErrors() []error { return m }

// SubmitClientFaceChallengeRequestValidationError is the validation error
// returned by SubmitClientFaceChallengeRequest.Validate if the designated
// constraints aren't met.
type SubmitClientFaceChallengeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientFaceChallengeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientFaceChallengeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientFaceChallengeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientFaceChallengeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientFaceChallengeRequestValidationError) ErrorName() string {
	return "SubmitClientFaceChallengeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientFaceChallengeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientFaceChallengeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientFaceChallengeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientFaceChallengeRequestValidationError{}

// Validate checks the field values on SubmitClientFaceChallengeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SubmitClientFaceChallengeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClientFaceChallengeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SubmitClientFaceChallengeResponseMultiError, or nil if none found.
func (m *SubmitClientFaceChallengeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientFaceChallengeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingId

	if len(errors) > 0 {
		return SubmitClientFaceChallengeResponseMultiError(errors)
	}

	return nil
}

// SubmitClientFaceChallengeResponseMultiError is an error wrapping multiple
// validation errors returned by
// SubmitClientFaceChallengeResponse.ValidateAll() if the designated
// constraints aren't met.
type SubmitClientFaceChallengeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientFaceChallengeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientFaceChallengeResponseMultiError) AllErrors() []error { return m }

// SubmitClientFaceChallengeResponseValidationError is the validation error
// returned by SubmitClientFaceChallengeResponse.Validate if the designated
// constraints aren't met.
type SubmitClientFaceChallengeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientFaceChallengeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientFaceChallengeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientFaceChallengeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientFaceChallengeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientFaceChallengeResponseValidationError) ErrorName() string {
	return "SubmitClientFaceChallengeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientFaceChallengeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientFaceChallengeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientFaceChallengeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientFaceChallengeResponseValidationError{}

// Validate checks the field values on RequestClientOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestClientOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestClientOTPRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestClientOTPRequestMultiError, or nil if none found.
func (m *RequestClientOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestClientOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpType

	if m.GetOnboardingId() <= 0 {
		err := RequestClientOTPRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := RequestClientOTPRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RequestClientOTPRequestMultiError(errors)
	}

	return nil
}

// RequestClientOTPRequestMultiError is an error wrapping multiple validation
// errors returned by RequestClientOTPRequest.ValidateAll() if the designated
// constraints aren't met.
type RequestClientOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestClientOTPRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestClientOTPRequestMultiError) AllErrors() []error { return m }

// RequestClientOTPRequestValidationError is the validation error returned by
// RequestClientOTPRequest.Validate if the designated constraints aren't met.
type RequestClientOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestClientOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestClientOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestClientOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestClientOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestClientOTPRequestValidationError) ErrorName() string {
	return "RequestClientOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RequestClientOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestClientOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestClientOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestClientOTPRequestValidationError{}

// Validate checks the field values on RequestClientOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestClientOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestClientOTPResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestClientOTPResponseMultiError, or nil if none found.
func (m *RequestClientOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestClientOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WaitTime

	// no validation rules for ResendTime

	// no validation rules for Status

	// no validation rules for ErrorMessage

	// no validation rules for PhoneNumber

	// no validation rules for OtpRequestId

	// no validation rules for IsSendSms

	if len(errors) > 0 {
		return RequestClientOTPResponseMultiError(errors)
	}

	return nil
}

// RequestClientOTPResponseMultiError is an error wrapping multiple validation
// errors returned by RequestClientOTPResponse.ValidateAll() if the designated
// constraints aren't met.
type RequestClientOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestClientOTPResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestClientOTPResponseMultiError) AllErrors() []error { return m }

// RequestClientOTPResponseValidationError is the validation error returned by
// RequestClientOTPResponse.Validate if the designated constraints aren't met.
type RequestClientOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestClientOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestClientOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestClientOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestClientOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestClientOTPResponseValidationError) ErrorName() string {
	return "RequestClientOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RequestClientOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestClientOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestClientOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestClientOTPResponseValidationError{}

// Validate checks the field values on VerifyClientOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyClientOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyClientOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyClientOTPRequestMultiError, or nil if none found.
func (m *VerifyClientOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyClientOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOtpCode()) < 1 {
		err := VerifyClientOTPRequestValidationError{
			field:  "OtpCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OtpType

	if m.GetOnboardingId() <= 0 {
		err := VerifyClientOTPRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := VerifyClientOTPRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return VerifyClientOTPRequestMultiError(errors)
	}

	return nil
}

// VerifyClientOTPRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyClientOTPRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyClientOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyClientOTPRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyClientOTPRequestMultiError) AllErrors() []error { return m }

// VerifyClientOTPRequestValidationError is the validation error returned by
// VerifyClientOTPRequest.Validate if the designated constraints aren't met.
type VerifyClientOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyClientOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyClientOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyClientOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyClientOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyClientOTPRequestValidationError) ErrorName() string {
	return "VerifyClientOTPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyClientOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyClientOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyClientOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyClientOTPRequestValidationError{}

// Validate checks the field values on VerifyClientOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyClientOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyClientOTPResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyClientOTPResponseMultiError, or nil if none found.
func (m *VerifyClientOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyClientOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return VerifyClientOTPResponseMultiError(errors)
	}

	return nil
}

// VerifyClientOTPResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyClientOTPResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyClientOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyClientOTPResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyClientOTPResponseMultiError) AllErrors() []error { return m }

// VerifyClientOTPResponseValidationError is the validation error returned by
// VerifyClientOTPResponse.Validate if the designated constraints aren't met.
type VerifyClientOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyClientOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyClientOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyClientOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyClientOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyClientOTPResponseValidationError) ErrorName() string {
	return "VerifyClientOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyClientOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyClientOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyClientOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyClientOTPResponseValidationError{}

// Validate checks the field values on GetClientContractRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientContractRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientContractRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientContractRequestMultiError, or nil if none found.
func (m *GetClientContractRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientContractRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := GetClientContractRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientContractRequestMultiError(errors)
	}

	return nil
}

// GetClientContractRequestMultiError is an error wrapping multiple validation
// errors returned by GetClientContractRequest.ValidateAll() if the designated
// constraints aren't met.
type GetClientContractRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientContractRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientContractRequestMultiError) AllErrors() []error { return m }

// GetClientContractRequestValidationError is the validation error returned by
// GetClientContractRequest.Validate if the designated constraints aren't met.
type GetClientContractRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientContractRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientContractRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientContractRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientContractRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientContractRequestValidationError) ErrorName() string {
	return "GetClientContractRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientContractRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientContractRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientContractRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientContractRequestValidationError{}

// Validate checks the field values on GetClientContractResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientContractResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientContractResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientContractResponseMultiError, or nil if none found.
func (m *GetClientContractResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientContractResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UnsignedContractUrl

	// no validation rules for SignedContractUrl

	if len(errors) > 0 {
		return GetClientContractResponseMultiError(errors)
	}

	return nil
}

// GetClientContractResponseMultiError is an error wrapping multiple validation
// errors returned by GetClientContractResponse.ValidateAll() if the
// designated constraints aren't met.
type GetClientContractResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientContractResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientContractResponseMultiError) AllErrors() []error { return m }

// GetClientContractResponseValidationError is the validation error returned by
// GetClientContractResponse.Validate if the designated constraints aren't met.
type GetClientContractResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientContractResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientContractResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientContractResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientContractResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientContractResponseValidationError) ErrorName() string {
	return "GetClientContractResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientContractResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientContractResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientContractResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientContractResponseValidationError{}

// Validate checks the field values on GetClientRejectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientRejectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientRejectionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientRejectionRequestMultiError, or nil if none found.
func (m *GetClientRejectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientRejectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetOnboardingId() <= 0 {
		err := GetClientRejectionRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientRejectionRequestMultiError(errors)
	}

	return nil
}

// GetClientRejectionRequestMultiError is an error wrapping multiple validation
// errors returned by GetClientRejectionRequest.ValidateAll() if the
// designated constraints aren't met.
type GetClientRejectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientRejectionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientRejectionRequestMultiError) AllErrors() []error { return m }

// GetClientRejectionRequestValidationError is the validation error returned by
// GetClientRejectionRequest.Validate if the designated constraints aren't met.
type GetClientRejectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientRejectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientRejectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientRejectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientRejectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientRejectionRequestValidationError) ErrorName() string {
	return "GetClientRejectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientRejectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientRejectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientRejectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientRejectionRequestValidationError{}

// Validate checks the field values on GetClientRejectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientRejectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientRejectionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientRejectionResponseMultiError, or nil if none found.
func (m *GetClientRejectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientRejectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientRejectionResponseValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientRejectionResponseValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientRejectionResponseValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClientRejectionResponseValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClientRejectionResponseValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClientRejectionResponseValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetClientRejectionResponseMultiError(errors)
	}

	return nil
}

// GetClientRejectionResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientRejectionResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientRejectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientRejectionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientRejectionResponseMultiError) AllErrors() []error { return m }

// GetClientRejectionResponseValidationError is the validation error returned
// by GetClientRejectionResponse.Validate if the designated constraints aren't met.
type GetClientRejectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientRejectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientRejectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientRejectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientRejectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientRejectionResponseValidationError) ErrorName() string {
	return "GetClientRejectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientRejectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientRejectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientRejectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientRejectionResponseValidationError{}

// Validate checks the field values on QueryOnboardingStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryOnboardingStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryOnboardingStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryOnboardingStatusRequestMultiError, or nil if none found.
func (m *QueryOnboardingStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryOnboardingStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := QueryOnboardingStatusRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := QueryOnboardingStatusRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return QueryOnboardingStatusRequestMultiError(errors)
	}

	return nil
}

// QueryOnboardingStatusRequestMultiError is an error wrapping multiple
// validation errors returned by QueryOnboardingStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type QueryOnboardingStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryOnboardingStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryOnboardingStatusRequestMultiError) AllErrors() []error { return m }

// QueryOnboardingStatusRequestValidationError is the validation error returned
// by QueryOnboardingStatusRequest.Validate if the designated constraints
// aren't met.
type QueryOnboardingStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryOnboardingStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryOnboardingStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryOnboardingStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryOnboardingStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryOnboardingStatusRequestValidationError) ErrorName() string {
	return "QueryOnboardingStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryOnboardingStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryOnboardingStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryOnboardingStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryOnboardingStatusRequestValidationError{}

// Validate checks the field values on QueryOnboardingStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryOnboardingStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryOnboardingStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryOnboardingStatusResponseMultiError, or nil if none found.
func (m *QueryOnboardingStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryOnboardingStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFlags()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryOnboardingStatusResponseValidationError{
					field:  "Flags",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryOnboardingStatusResponseValidationError{
					field:  "Flags",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFlags()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryOnboardingStatusResponseValidationError{
				field:  "Flags",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingId

	// no validation rules for CurrentStep

	// no validation rules for PartnerCode

	if len(errors) > 0 {
		return QueryOnboardingStatusResponseMultiError(errors)
	}

	return nil
}

// QueryOnboardingStatusResponseMultiError is an error wrapping multiple
// validation errors returned by QueryOnboardingStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type QueryOnboardingStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryOnboardingStatusResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryOnboardingStatusResponseMultiError) AllErrors() []error { return m }

// QueryOnboardingStatusResponseValidationError is the validation error
// returned by QueryOnboardingStatusResponse.Validate if the designated
// constraints aren't met.
type QueryOnboardingStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryOnboardingStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryOnboardingStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryOnboardingStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryOnboardingStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryOnboardingStatusResponseValidationError) ErrorName() string {
	return "QueryOnboardingStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryOnboardingStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryOnboardingStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryOnboardingStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryOnboardingStatusResponseValidationError{}

// Validate checks the field values on ListOnboardingByUserIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOnboardingByUserIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOnboardingByUserIDsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListOnboardingByUserIDsRequestMultiError, or nil if none found.
func (m *ListOnboardingByUserIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOnboardingByUserIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetZalopayIds()); l < 1 || l > 50 {
		err := ListOnboardingByUserIDsRequestValidationError{
			field:  "ZalopayIds",
			reason: "value must contain between 1 and 50 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListOnboardingByUserIDsRequestMultiError(errors)
	}

	return nil
}

// ListOnboardingByUserIDsRequestMultiError is an error wrapping multiple
// validation errors returned by ListOnboardingByUserIDsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListOnboardingByUserIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOnboardingByUserIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOnboardingByUserIDsRequestMultiError) AllErrors() []error { return m }

// ListOnboardingByUserIDsRequestValidationError is the validation error
// returned by ListOnboardingByUserIDsRequest.Validate if the designated
// constraints aren't met.
type ListOnboardingByUserIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOnboardingByUserIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOnboardingByUserIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOnboardingByUserIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOnboardingByUserIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOnboardingByUserIDsRequestValidationError) ErrorName() string {
	return "ListOnboardingByUserIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListOnboardingByUserIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOnboardingByUserIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOnboardingByUserIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOnboardingByUserIDsRequestValidationError{}

// Validate checks the field values on ListOnboardingByUserIDsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOnboardingByUserIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOnboardingByUserIDsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListOnboardingByUserIDsResponseMultiError, or nil if none found.
func (m *ListOnboardingByUserIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOnboardingByUserIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOnboardings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListOnboardingByUserIDsResponseValidationError{
						field:  fmt.Sprintf("Onboardings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListOnboardingByUserIDsResponseValidationError{
						field:  fmt.Sprintf("Onboardings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListOnboardingByUserIDsResponseValidationError{
					field:  fmt.Sprintf("Onboardings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListOnboardingByUserIDsResponseMultiError(errors)
	}

	return nil
}

// ListOnboardingByUserIDsResponseMultiError is an error wrapping multiple
// validation errors returned by ListOnboardingByUserIDsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListOnboardingByUserIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOnboardingByUserIDsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOnboardingByUserIDsResponseMultiError) AllErrors() []error { return m }

// ListOnboardingByUserIDsResponseValidationError is the validation error
// returned by ListOnboardingByUserIDsResponse.Validate if the designated
// constraints aren't met.
type ListOnboardingByUserIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOnboardingByUserIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOnboardingByUserIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOnboardingByUserIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOnboardingByUserIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOnboardingByUserIDsResponseValidationError) ErrorName() string {
	return "ListOnboardingByUserIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListOnboardingByUserIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOnboardingByUserIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOnboardingByUserIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOnboardingByUserIDsResponseValidationError{}

// Validate checks the field values on GetClientResourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientResourcesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientResourcesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientResourcesRequestMultiError, or nil if none found.
func (m *GetClientResourcesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientResourcesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetResourceTypes()) < 1 {
		err := GetClientResourcesRequestValidationError{
			field:  "ResourceTypes",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientResourcesRequestMultiError(errors)
	}

	return nil
}

// GetClientResourcesRequestMultiError is an error wrapping multiple validation
// errors returned by GetClientResourcesRequest.ValidateAll() if the
// designated constraints aren't met.
type GetClientResourcesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientResourcesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientResourcesRequestMultiError) AllErrors() []error { return m }

// GetClientResourcesRequestValidationError is the validation error returned by
// GetClientResourcesRequest.Validate if the designated constraints aren't met.
type GetClientResourcesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientResourcesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientResourcesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientResourcesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientResourcesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientResourcesRequestValidationError) ErrorName() string {
	return "GetClientResourcesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientResourcesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientResourcesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientResourcesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientResourcesRequestValidationError{}

// Validate checks the field values on GetClientResourcesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientResourcesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientResourcesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientResourcesResponseMultiError, or nil if none found.
func (m *GetClientResourcesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientResourcesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClientResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClientResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClientResourcesResponseValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetClientResourcesResponseMultiError(errors)
	}

	return nil
}

// GetClientResourcesResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientResourcesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientResourcesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientResourcesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientResourcesResponseMultiError) AllErrors() []error { return m }

// GetClientResourcesResponseValidationError is the validation error returned
// by GetClientResourcesResponse.Validate if the designated constraints aren't met.
type GetClientResourcesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientResourcesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientResourcesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientResourcesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientResourcesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientResourcesResponseValidationError) ErrorName() string {
	return "GetClientResourcesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientResourcesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientResourcesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientResourcesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientResourcesResponseValidationError{}

// Validate checks the field values on OnboardingData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OnboardingData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OnboardingDataMultiError,
// or nil if none found.
func (m *OnboardingData) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ZalopayId

	// no validation rules for Status

	// no validation rules for PartnerCode

	// no validation rules for CurrentStep

	// no validation rules for RejectCode

	if all {
		switch v := interface{}(m.GetProfileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "ProfileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "ProfileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDataValidationError{
				field:  "ProfileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPartnerData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "PartnerData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDataValidationError{
					field:  "PartnerData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDataValidationError{
				field:  "PartnerData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.StatusFlags != nil {

		if all {
			switch v := interface{}(m.GetStatusFlags()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OnboardingDataValidationError{
						field:  "StatusFlags",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OnboardingDataValidationError{
						field:  "StatusFlags",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStatusFlags()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OnboardingDataValidationError{
					field:  "StatusFlags",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OnboardingDataMultiError(errors)
	}

	return nil
}

// OnboardingDataMultiError is an error wrapping multiple validation errors
// returned by OnboardingData.ValidateAll() if the designated constraints
// aren't met.
type OnboardingDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingDataMultiError) AllErrors() []error { return m }

// OnboardingDataValidationError is the validation error returned by
// OnboardingData.Validate if the designated constraints aren't met.
type OnboardingDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingDataValidationError) ErrorName() string { return "OnboardingDataValidationError" }

// Error satisfies the builtin error interface
func (e OnboardingDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingDataValidationError{}

// Validate checks the field values on OnboardingProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingProfile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingProfileMultiError, or nil if none found.
func (m *OnboardingProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FullName

	// no validation rules for Gender

	// no validation rules for PhoneNumber

	// no validation rules for IdNumber

	// no validation rules for IdIssueDate

	// no validation rules for IdIssuePlace

	// no validation rules for DateOfBirth

	// no validation rules for PermanentAddress

	// no validation rules for TempResidenceAddress

	if len(errors) > 0 {
		return OnboardingProfileMultiError(errors)
	}

	return nil
}

// OnboardingProfileMultiError is an error wrapping multiple validation errors
// returned by OnboardingProfile.ValidateAll() if the designated constraints
// aren't met.
type OnboardingProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingProfileMultiError) AllErrors() []error { return m }

// OnboardingProfileValidationError is the validation error returned by
// OnboardingProfile.Validate if the designated constraints aren't met.
type OnboardingProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingProfileValidationError) ErrorName() string {
	return "OnboardingProfileValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingProfileValidationError{}

// Validate checks the field values on OnboardingFlags with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingFlags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingFlags with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingFlagsMultiError, or nil if none found.
func (m *OnboardingFlags) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingFlags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUnregister

	// no validation rules for IsOnboarding

	// no validation rules for IsRejected

	if len(errors) > 0 {
		return OnboardingFlagsMultiError(errors)
	}

	return nil
}

// OnboardingFlagsMultiError is an error wrapping multiple validation errors
// returned by OnboardingFlags.ValidateAll() if the designated constraints
// aren't met.
type OnboardingFlagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingFlagsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingFlagsMultiError) AllErrors() []error { return m }

// OnboardingFlagsValidationError is the validation error returned by
// OnboardingFlags.Validate if the designated constraints aren't met.
type OnboardingFlagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingFlagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingFlagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingFlagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingFlagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingFlagsValidationError) ErrorName() string { return "OnboardingFlagsValidationError" }

// Error satisfies the builtin error interface
func (e OnboardingFlagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingFlags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingFlagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingFlagsValidationError{}

// Validate checks the field values on PartnerOnboarding with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PartnerOnboarding) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PartnerOnboarding with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PartnerOnboardingMultiError, or nil if none found.
func (m *PartnerOnboarding) ValidateAll() error {
	return m.validate(true)
}

func (m *PartnerOnboarding) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CimbData != nil {

		if all {
			switch v := interface{}(m.GetCimbData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PartnerOnboardingValidationError{
						field:  "CimbData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PartnerOnboardingValidationError{
						field:  "CimbData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCimbData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PartnerOnboardingValidationError{
					field:  "CimbData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PartnerOnboardingMultiError(errors)
	}

	return nil
}

// PartnerOnboardingMultiError is an error wrapping multiple validation errors
// returned by PartnerOnboarding.ValidateAll() if the designated constraints
// aren't met.
type PartnerOnboardingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PartnerOnboardingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PartnerOnboardingMultiError) AllErrors() []error { return m }

// PartnerOnboardingValidationError is the validation error returned by
// PartnerOnboarding.Validate if the designated constraints aren't met.
type PartnerOnboardingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PartnerOnboardingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PartnerOnboardingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PartnerOnboardingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PartnerOnboardingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PartnerOnboardingValidationError) ErrorName() string {
	return "PartnerOnboardingValidationError"
}

// Error satisfies the builtin error interface
func (e PartnerOnboardingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPartnerOnboarding.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PartnerOnboardingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PartnerOnboardingValidationError{}

// Validate checks the field values on CIMBOnboarding with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CIMBOnboarding) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CIMBOnboarding with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CIMBOnboardingMultiError,
// or nil if none found.
func (m *CIMBOnboarding) ValidateAll() error {
	return m.validate(true)
}

func (m *CIMBOnboarding) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OdStatus

	// no validation rules for CasaStatus

	// no validation rules for SignStatus

	// no validation rules for ErrorDetail

	// no validation rules for ManualApprovalReason

	if len(errors) > 0 {
		return CIMBOnboardingMultiError(errors)
	}

	return nil
}

// CIMBOnboardingMultiError is an error wrapping multiple validation errors
// returned by CIMBOnboarding.ValidateAll() if the designated constraints
// aren't met.
type CIMBOnboardingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CIMBOnboardingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CIMBOnboardingMultiError) AllErrors() []error { return m }

// CIMBOnboardingValidationError is the validation error returned by
// CIMBOnboarding.Validate if the designated constraints aren't met.
type CIMBOnboardingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CIMBOnboardingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CIMBOnboardingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CIMBOnboardingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CIMBOnboardingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CIMBOnboardingValidationError) ErrorName() string { return "CIMBOnboardingValidationError" }

// Error satisfies the builtin error interface
func (e CIMBOnboardingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCIMBOnboarding.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CIMBOnboardingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CIMBOnboardingValidationError{}

// Validate checks the field values on CIMBResource with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CIMBResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CIMBResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CIMBResourceMultiError, or
// nil if none found.
func (m *CIMBResource) ValidateAll() error {
	return m.validate(true)
}

func (m *CIMBResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CIMBResourceValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CIMBResourceValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CIMBResourceValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CIMBResourceMultiError(errors)
	}

	return nil
}

// CIMBResourceMultiError is an error wrapping multiple validation errors
// returned by CIMBResource.ValidateAll() if the designated constraints aren't met.
type CIMBResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CIMBResourceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CIMBResourceMultiError) AllErrors() []error { return m }

// CIMBResourceValidationError is the validation error returned by
// CIMBResource.Validate if the designated constraints aren't met.
type CIMBResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CIMBResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CIMBResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CIMBResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CIMBResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CIMBResourceValidationError) ErrorName() string { return "CIMBResourceValidationError" }

// Error satisfies the builtin error interface
func (e CIMBResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCIMBResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CIMBResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CIMBResourceValidationError{}

// Validate checks the field values on CIMBResourceData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CIMBResourceData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CIMBResourceData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CIMBResourceDataMultiError, or nil if none found.
func (m *CIMBResourceData) ValidateAll() error {
	return m.validate(true)
}

func (m *CIMBResourceData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Vietnamese

	// no validation rules for English

	if len(errors) > 0 {
		return CIMBResourceDataMultiError(errors)
	}

	return nil
}

// CIMBResourceDataMultiError is an error wrapping multiple validation errors
// returned by CIMBResourceData.ValidateAll() if the designated constraints
// aren't met.
type CIMBResourceDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CIMBResourceDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CIMBResourceDataMultiError) AllErrors() []error { return m }

// CIMBResourceDataValidationError is the validation error returned by
// CIMBResourceData.Validate if the designated constraints aren't met.
type CIMBResourceDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CIMBResourceDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CIMBResourceDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CIMBResourceDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CIMBResourceDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CIMBResourceDataValidationError) ErrorName() string { return "CIMBResourceDataValidationError" }

// Error satisfies the builtin error interface
func (e CIMBResourceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCIMBResourceData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CIMBResourceDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CIMBResourceDataValidationError{}

// Validate checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserProfileMultiError, or
// nil if none found.
func (m *UserProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *UserProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KycLevel

	// no validation rules for ProfileLevel

	// no validation rules for FullName

	// no validation rules for Gender

	// no validation rules for PhoneNumber

	// no validation rules for PermanentAddress

	// no validation rules for IdType

	// no validation rules for IdNumber

	// no validation rules for Email

	// no validation rules for DateOfBirth

	// no validation rules for IdIssuePlace

	// no validation rules for IdIssueDate

	if len(errors) > 0 {
		return UserProfileMultiError(errors)
	}

	return nil
}

// UserProfileMultiError is an error wrapping multiple validation errors
// returned by UserProfile.ValidateAll() if the designated constraints aren't met.
type UserProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserProfileMultiError) AllErrors() []error { return m }

// UserProfileValidationError is the validation error returned by
// UserProfile.Validate if the designated constraints aren't met.
type UserProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserProfileValidationError) ErrorName() string { return "UserProfileValidationError" }

// Error satisfies the builtin error interface
func (e UserProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserProfileValidationError{}

// Validate checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContentMultiError, or nil if none found.
func (m *Content) ValidateAll() error {
	return m.validate(true)
}

func (m *Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Message

	if len(errors) > 0 {
		return ContentMultiError(errors)
	}

	return nil
}

// ContentMultiError is an error wrapping multiple validation errors returned
// by Content.ValidateAll() if the designated constraints aren't met.
type ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentMultiError) AllErrors() []error { return m }

// ContentValidationError is the validation error returned by Content.Validate
// if the designated constraints aren't met.
type ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentValidationError) ErrorName() string { return "ContentValidationError" }

// Error satisfies the builtin error interface
func (e ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentValidationError{}

// Validate checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ActionMultiError, or nil if none found.
func (m *Action) ValidateAll() error {
	return m.validate(true)
}

func (m *Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Title

	// no validation rules for Variant

	// no validation rules for ZpaActionUrl

	// no validation rules for ZpiActionUrl

	{
		sorted_keys := make([]string, len(m.GetMetadata()))
		i := 0
		for key := range m.GetMetadata() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMetadata()[key]
			_ = val

			// no validation rules for Metadata[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ActionValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ActionValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ActionValidationError{
						field:  fmt.Sprintf("Metadata[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ActionMultiError(errors)
	}

	return nil
}

// ActionMultiError is an error wrapping multiple validation errors returned by
// Action.ValidateAll() if the designated constraints aren't met.
type ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionMultiError) AllErrors() []error { return m }

// ActionValidationError is the validation error returned by Action.Validate if
// the designated constraints aren't met.
type ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionValidationError) ErrorName() string { return "ActionValidationError" }

// Error satisfies the builtin error interface
func (e ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionValidationError{}

// Validate checks the field values on RejectInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RejectInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RejectInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RejectInfoMultiError, or
// nil if none found.
func (m *RejectInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RejectInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RejectInfoValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RejectInfoValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RejectInfoValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RejectInfoMultiError(errors)
	}

	return nil
}

// RejectInfoMultiError is an error wrapping multiple validation errors
// returned by RejectInfo.ValidateAll() if the designated constraints aren't met.
type RejectInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RejectInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RejectInfoMultiError) AllErrors() []error { return m }

// RejectInfoValidationError is the validation error returned by
// RejectInfo.Validate if the designated constraints aren't met.
type RejectInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RejectInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RejectInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RejectInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RejectInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RejectInfoValidationError) ErrorName() string { return "RejectInfoValidationError" }

// Error satisfies the builtin error interface
func (e RejectInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRejectInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RejectInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RejectInfoValidationError{}

// Validate checks the field values on RiskInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskInfoMultiError, or nil
// if none found.
func (m *RiskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Level

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskInfoValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskInfoValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskInfoValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskInfoMultiError(errors)
	}

	return nil
}

// RiskInfoMultiError is an error wrapping multiple validation errors returned
// by RiskInfo.ValidateAll() if the designated constraints aren't met.
type RiskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoMultiError) AllErrors() []error { return m }

// RiskInfoValidationError is the validation error returned by
// RiskInfo.Validate if the designated constraints aren't met.
type RiskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoValidationError) ErrorName() string { return "RiskInfoValidationError" }

// Error satisfies the builtin error interface
func (e RiskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoValidationError{}

// Validate checks the field values on ProfileIssue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProfileIssue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfileIssue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProfileIssueMultiError, or
// nil if none found.
func (m *ProfileIssue) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfileIssue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileIssueValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileIssueValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileIssueValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProfileIssueMultiError(errors)
	}

	return nil
}

// ProfileIssueMultiError is an error wrapping multiple validation errors
// returned by ProfileIssue.ValidateAll() if the designated constraints aren't met.
type ProfileIssueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfileIssueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfileIssueMultiError) AllErrors() []error { return m }

// ProfileIssueValidationError is the validation error returned by
// ProfileIssue.Validate if the designated constraints aren't met.
type ProfileIssueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfileIssueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfileIssueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfileIssueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfileIssueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfileIssueValidationError) ErrorName() string { return "ProfileIssueValidationError" }

// Error satisfies the builtin error interface
func (e ProfileIssueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfileIssue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfileIssueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfileIssueValidationError{}

// Validate checks the field values on KycStatusData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycStatusData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycStatusData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KycStatusDataMultiError, or
// nil if none found.
func (m *KycStatusData) ValidateAll() error {
	return m.validate(true)
}

func (m *KycStatusData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycStatusDataValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycStatusDataValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycStatusDataValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KycStatusDataMultiError(errors)
	}

	return nil
}

// KycStatusDataMultiError is an error wrapping multiple validation errors
// returned by KycStatusData.ValidateAll() if the designated constraints
// aren't met.
type KycStatusDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycStatusDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycStatusDataMultiError) AllErrors() []error { return m }

// KycStatusDataValidationError is the validation error returned by
// KycStatusData.Validate if the designated constraints aren't met.
type KycStatusDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycStatusDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycStatusDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycStatusDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycStatusDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycStatusDataValidationError) ErrorName() string { return "KycStatusDataValidationError" }

// Error satisfies the builtin error interface
func (e KycStatusDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycStatusData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycStatusDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycStatusDataValidationError{}

// Validate checks the field values on Notice with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Notice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Notice with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NoticeMultiError, or nil if none found.
func (m *Notice) ValidateAll() error {
	return m.validate(true)
}

func (m *Notice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NoticeValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NoticeValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NoticeValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NoticeValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NoticeValidationError{
						field:  fmt.Sprintf("Actions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NoticeValidationError{
					field:  fmt.Sprintf("Actions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NoticeMultiError(errors)
	}

	return nil
}

// NoticeMultiError is an error wrapping multiple validation errors returned by
// Notice.ValidateAll() if the designated constraints aren't met.
type NoticeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoticeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoticeMultiError) AllErrors() []error { return m }

// NoticeValidationError is the validation error returned by Notice.Validate if
// the designated constraints aren't met.
type NoticeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoticeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoticeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoticeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoticeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoticeValidationError) ErrorName() string { return "NoticeValidationError" }

// Error satisfies the builtin error interface
func (e NoticeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoticeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoticeValidationError{}

// Validate checks the field values on BindInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BindInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BindInfoMultiError, or nil
// if none found.
func (m *BindInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BindInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingId

	// no validation rules for AccountId

	// no validation rules for PartnerCode

	if len(errors) > 0 {
		return BindInfoMultiError(errors)
	}

	return nil
}

// BindInfoMultiError is an error wrapping multiple validation errors returned
// by BindInfo.ValidateAll() if the designated constraints aren't met.
type BindInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindInfoMultiError) AllErrors() []error { return m }

// BindInfoValidationError is the validation error returned by
// BindInfo.Validate if the designated constraints aren't met.
type BindInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindInfoValidationError) ErrorName() string { return "BindInfoValidationError" }

// Error satisfies the builtin error interface
func (e BindInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindInfoValidationError{}

// Validate checks the field values on ListClientOnboardingResponse_Onboarding
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListClientOnboardingResponse_Onboarding) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListClientOnboardingResponse_Onboarding with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListClientOnboardingResponse_OnboardingMultiError, or nil if none found.
func (m *ListClientOnboardingResponse_Onboarding) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientOnboardingResponse_Onboarding) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PartnerCode

	// no validation rules for Status

	// no validation rules for CurrentStep

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClientOnboardingResponse_OnboardingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClientOnboardingResponse_OnboardingValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClientOnboardingResponse_OnboardingValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClientOnboardingResponse_OnboardingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClientOnboardingResponse_OnboardingValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClientOnboardingResponse_OnboardingValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NextStep

	if len(errors) > 0 {
		return ListClientOnboardingResponse_OnboardingMultiError(errors)
	}

	return nil
}

// ListClientOnboardingResponse_OnboardingMultiError is an error wrapping
// multiple validation errors returned by
// ListClientOnboardingResponse_Onboarding.ValidateAll() if the designated
// constraints aren't met.
type ListClientOnboardingResponse_OnboardingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientOnboardingResponse_OnboardingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientOnboardingResponse_OnboardingMultiError) AllErrors() []error { return m }

// ListClientOnboardingResponse_OnboardingValidationError is the validation
// error returned by ListClientOnboardingResponse_Onboarding.Validate if the
// designated constraints aren't met.
type ListClientOnboardingResponse_OnboardingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientOnboardingResponse_OnboardingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientOnboardingResponse_OnboardingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientOnboardingResponse_OnboardingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientOnboardingResponse_OnboardingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientOnboardingResponse_OnboardingValidationError) ErrorName() string {
	return "ListClientOnboardingResponse_OnboardingValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientOnboardingResponse_OnboardingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientOnboardingResponse_Onboarding.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientOnboardingResponse_OnboardingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientOnboardingResponse_OnboardingValidationError{}

// Validate checks the field values on SubmitClientWetSignRequest_SignImageData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SubmitClientWetSignRequest_SignImageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SubmitClientWetSignRequest_SignImageData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SubmitClientWetSignRequest_SignImageDataMultiError, or nil if none found.
func (m *SubmitClientWetSignRequest_SignImageData) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientWetSignRequest_SignImageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetImageData()) < 1 {
		err := SubmitClientWetSignRequest_SignImageDataValidationError{
			field:  "ImageData",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetImageType()) < 1 {
		err := SubmitClientWetSignRequest_SignImageDataValidationError{
			field:  "ImageType",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SubmitClientWetSignRequest_SignImageDataMultiError(errors)
	}

	return nil
}

// SubmitClientWetSignRequest_SignImageDataMultiError is an error wrapping
// multiple validation errors returned by
// SubmitClientWetSignRequest_SignImageData.ValidateAll() if the designated
// constraints aren't met.
type SubmitClientWetSignRequest_SignImageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientWetSignRequest_SignImageDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientWetSignRequest_SignImageDataMultiError) AllErrors() []error { return m }

// SubmitClientWetSignRequest_SignImageDataValidationError is the validation
// error returned by SubmitClientWetSignRequest_SignImageData.Validate if the
// designated constraints aren't met.
type SubmitClientWetSignRequest_SignImageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientWetSignRequest_SignImageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientWetSignRequest_SignImageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientWetSignRequest_SignImageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientWetSignRequest_SignImageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientWetSignRequest_SignImageDataValidationError) ErrorName() string {
	return "SubmitClientWetSignRequest_SignImageDataValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientWetSignRequest_SignImageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientWetSignRequest_SignImageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientWetSignRequest_SignImageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientWetSignRequest_SignImageDataValidationError{}

// Validate checks the field values on SubmitClientWetSignRequest_SignImageUri
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SubmitClientWetSignRequest_SignImageUri) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SubmitClientWetSignRequest_SignImageUri with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SubmitClientWetSignRequest_SignImageUriMultiError, or nil if none found.
func (m *SubmitClientWetSignRequest_SignImageUri) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClientWetSignRequest_SignImageUri) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetImageUrl()) < 1 {
		err := SubmitClientWetSignRequest_SignImageUriValidationError{
			field:  "ImageUrl",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetImageType()) < 1 {
		err := SubmitClientWetSignRequest_SignImageUriValidationError{
			field:  "ImageType",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SubmitClientWetSignRequest_SignImageUriMultiError(errors)
	}

	return nil
}

// SubmitClientWetSignRequest_SignImageUriMultiError is an error wrapping
// multiple validation errors returned by
// SubmitClientWetSignRequest_SignImageUri.ValidateAll() if the designated
// constraints aren't met.
type SubmitClientWetSignRequest_SignImageUriMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClientWetSignRequest_SignImageUriMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClientWetSignRequest_SignImageUriMultiError) AllErrors() []error { return m }

// SubmitClientWetSignRequest_SignImageUriValidationError is the validation
// error returned by SubmitClientWetSignRequest_SignImageUri.Validate if the
// designated constraints aren't met.
type SubmitClientWetSignRequest_SignImageUriValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClientWetSignRequest_SignImageUriValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClientWetSignRequest_SignImageUriValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClientWetSignRequest_SignImageUriValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClientWetSignRequest_SignImageUriValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClientWetSignRequest_SignImageUriValidationError) ErrorName() string {
	return "SubmitClientWetSignRequest_SignImageUriValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClientWetSignRequest_SignImageUriValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClientWetSignRequest_SignImageUri.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClientWetSignRequest_SignImageUriValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClientWetSignRequest_SignImageUriValidationError{}

// Validate checks the field values on QueryOnboardingStatusResponse_Flags with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *QueryOnboardingStatusResponse_Flags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryOnboardingStatusResponse_Flags
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// QueryOnboardingStatusResponse_FlagsMultiError, or nil if none found.
func (m *QueryOnboardingStatusResponse_Flags) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryOnboardingStatusResponse_Flags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUnregister

	// no validation rules for IsOnboarding

	// no validation rules for IsRejected

	if len(errors) > 0 {
		return QueryOnboardingStatusResponse_FlagsMultiError(errors)
	}

	return nil
}

// QueryOnboardingStatusResponse_FlagsMultiError is an error wrapping multiple
// validation errors returned by
// QueryOnboardingStatusResponse_Flags.ValidateAll() if the designated
// constraints aren't met.
type QueryOnboardingStatusResponse_FlagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryOnboardingStatusResponse_FlagsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryOnboardingStatusResponse_FlagsMultiError) AllErrors() []error { return m }

// QueryOnboardingStatusResponse_FlagsValidationError is the validation error
// returned by QueryOnboardingStatusResponse_Flags.Validate if the designated
// constraints aren't met.
type QueryOnboardingStatusResponse_FlagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryOnboardingStatusResponse_FlagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryOnboardingStatusResponse_FlagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryOnboardingStatusResponse_FlagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryOnboardingStatusResponse_FlagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryOnboardingStatusResponse_FlagsValidationError) ErrorName() string {
	return "QueryOnboardingStatusResponse_FlagsValidationError"
}

// Error satisfies the builtin error interface
func (e QueryOnboardingStatusResponse_FlagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryOnboardingStatusResponse_Flags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryOnboardingStatusResponse_FlagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryOnboardingStatusResponse_FlagsValidationError{}
