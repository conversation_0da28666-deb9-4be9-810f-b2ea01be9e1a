// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: onboarding_service/v1/onboarding_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockOnboardingClient is a mock of OnboardingClient interface.
type MockOnboardingClient struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingClientMockRecorder
}

// MockOnboardingClientMockRecorder is the mock recorder for MockOnboardingClient.
type MockOnboardingClientMockRecorder struct {
	mock *MockOnboardingClient
}

// NewMockOnboardingClient creates a new mock instance.
func NewMockOnboardingClient(ctrl *gomock.Controller) *MockOnboardingClient {
	mock := &MockOnboardingClient{ctrl: ctrl}
	mock.recorder = &MockOnboardingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingClient) EXPECT() *MockOnboardingClientMockRecorder {
	return m.recorder
}

// GetClientContract mocks base method.
func (m *MockOnboardingClient) GetClientContract(ctx context.Context, in *GetClientContractRequest, opts ...grpc.CallOption) (*GetClientContractResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientContract", varargs...)
	ret0, _ := ret[0].(*GetClientContractResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientContract indicates an expected call of GetClientContract.
func (mr *MockOnboardingClientMockRecorder) GetClientContract(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientContract", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientContract), varargs...)
}

// GetClientEkycProfile mocks base method.
func (m *MockOnboardingClient) GetClientEkycProfile(ctx context.Context, in *GetClientEkycProfileRequest, opts ...grpc.CallOption) (*GetClientEkycProfileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientEkycProfile", varargs...)
	ret0, _ := ret[0].(*GetClientEkycProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientEkycProfile indicates an expected call of GetClientEkycProfile.
func (mr *MockOnboardingClientMockRecorder) GetClientEkycProfile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientEkycProfile", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientEkycProfile), varargs...)
}

// GetClientOnboarding mocks base method.
func (m *MockOnboardingClient) GetClientOnboarding(ctx context.Context, in *GetClientOnboardingRequest, opts ...grpc.CallOption) (*GetClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientOnboarding", varargs...)
	ret0, _ := ret[0].(*GetClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientOnboarding indicates an expected call of GetClientOnboarding.
func (mr *MockOnboardingClientMockRecorder) GetClientOnboarding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientOnboarding", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientOnboarding), varargs...)
}

// GetClientPermissions mocks base method.
func (m *MockOnboardingClient) GetClientPermissions(ctx context.Context, in *GetClientPermissionsRequest, opts ...grpc.CallOption) (*GetClientPermissionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientPermissions", varargs...)
	ret0, _ := ret[0].(*GetClientPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientPermissions indicates an expected call of GetClientPermissions.
func (mr *MockOnboardingClientMockRecorder) GetClientPermissions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientPermissions", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientPermissions), varargs...)
}

// GetClientRejection mocks base method.
func (m *MockOnboardingClient) GetClientRejection(ctx context.Context, in *GetClientRejectionRequest, opts ...grpc.CallOption) (*GetClientRejectionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientRejection", varargs...)
	ret0, _ := ret[0].(*GetClientRejectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientRejection indicates an expected call of GetClientRejection.
func (mr *MockOnboardingClientMockRecorder) GetClientRejection(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientRejection", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientRejection), varargs...)
}

// GetClientResources mocks base method.
func (m *MockOnboardingClient) GetClientResources(ctx context.Context, in *GetClientResourcesRequest, opts ...grpc.CallOption) (*GetClientResourcesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientResources", varargs...)
	ret0, _ := ret[0].(*GetClientResourcesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientResources indicates an expected call of GetClientResources.
func (mr *MockOnboardingClientMockRecorder) GetClientResources(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientResources", reflect.TypeOf((*MockOnboardingClient)(nil).GetClientResources), varargs...)
}

// LinkingClientAccount mocks base method.
func (m *MockOnboardingClient) LinkingClientAccount(ctx context.Context, in *LinkingClientAccountRequest, opts ...grpc.CallOption) (*LinkingClientAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LinkingClientAccount", varargs...)
	ret0, _ := ret[0].(*LinkingClientAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkingClientAccount indicates an expected call of LinkingClientAccount.
func (mr *MockOnboardingClientMockRecorder) LinkingClientAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkingClientAccount", reflect.TypeOf((*MockOnboardingClient)(nil).LinkingClientAccount), varargs...)
}

// ListClientOnboarding mocks base method.
func (m *MockOnboardingClient) ListClientOnboarding(ctx context.Context, in *ListClientOnboardingRequest, opts ...grpc.CallOption) (*ListClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListClientOnboarding", varargs...)
	ret0, _ := ret[0].(*ListClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientOnboarding indicates an expected call of ListClientOnboarding.
func (mr *MockOnboardingClientMockRecorder) ListClientOnboarding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientOnboarding", reflect.TypeOf((*MockOnboardingClient)(nil).ListClientOnboarding), varargs...)
}

// ListOnboardingByUserIDs mocks base method.
func (m *MockOnboardingClient) ListOnboardingByUserIDs(ctx context.Context, in *ListOnboardingByUserIDsRequest, opts ...grpc.CallOption) (*ListOnboardingByUserIDsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListOnboardingByUserIDs", varargs...)
	ret0, _ := ret[0].(*ListOnboardingByUserIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnboardingByUserIDs indicates an expected call of ListOnboardingByUserIDs.
func (mr *MockOnboardingClientMockRecorder) ListOnboardingByUserIDs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnboardingByUserIDs", reflect.TypeOf((*MockOnboardingClient)(nil).ListOnboardingByUserIDs), varargs...)
}

// QueryOnboardingStatus mocks base method.
func (m *MockOnboardingClient) QueryOnboardingStatus(ctx context.Context, in *QueryOnboardingStatusRequest, opts ...grpc.CallOption) (*QueryOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryOnboardingStatus", varargs...)
	ret0, _ := ret[0].(*QueryOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOnboardingStatus indicates an expected call of QueryOnboardingStatus.
func (mr *MockOnboardingClientMockRecorder) QueryOnboardingStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOnboardingStatus", reflect.TypeOf((*MockOnboardingClient)(nil).QueryOnboardingStatus), varargs...)
}

// RegisterClientOnboarding mocks base method.
func (m *MockOnboardingClient) RegisterClientOnboarding(ctx context.Context, in *RegisterClientOnboardingRequest, opts ...grpc.CallOption) (*RegisterClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RegisterClientOnboarding", varargs...)
	ret0, _ := ret[0].(*RegisterClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterClientOnboarding indicates an expected call of RegisterClientOnboarding.
func (mr *MockOnboardingClientMockRecorder) RegisterClientOnboarding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterClientOnboarding", reflect.TypeOf((*MockOnboardingClient)(nil).RegisterClientOnboarding), varargs...)
}

// ReinitClientOnboarding mocks base method.
func (m *MockOnboardingClient) ReinitClientOnboarding(ctx context.Context, in *ReinitClientOnboardingRequest, opts ...grpc.CallOption) (*ReinitClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReinitClientOnboarding", varargs...)
	ret0, _ := ret[0].(*ReinitClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReinitClientOnboarding indicates an expected call of ReinitClientOnboarding.
func (mr *MockOnboardingClientMockRecorder) ReinitClientOnboarding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinitClientOnboarding", reflect.TypeOf((*MockOnboardingClient)(nil).ReinitClientOnboarding), varargs...)
}

// RequestClientOTP mocks base method.
func (m *MockOnboardingClient) RequestClientOTP(ctx context.Context, in *RequestClientOTPRequest, opts ...grpc.CallOption) (*RequestClientOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RequestClientOTP", varargs...)
	ret0, _ := ret[0].(*RequestClientOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestClientOTP indicates an expected call of RequestClientOTP.
func (mr *MockOnboardingClientMockRecorder) RequestClientOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestClientOTP", reflect.TypeOf((*MockOnboardingClient)(nil).RequestClientOTP), varargs...)
}

// ResetClientEkycNfc mocks base method.
func (m *MockOnboardingClient) ResetClientEkycNfc(ctx context.Context, in *ResetClientEkycNfcRequest, opts ...grpc.CallOption) (*ResetClientEkycNfcResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResetClientEkycNfc", varargs...)
	ret0, _ := ret[0].(*ResetClientEkycNfcResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetClientEkycNfc indicates an expected call of ResetClientEkycNfc.
func (mr *MockOnboardingClientMockRecorder) ResetClientEkycNfc(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClientEkycNfc", reflect.TypeOf((*MockOnboardingClient)(nil).ResetClientEkycNfc), varargs...)
}

// SubmitClientApplication mocks base method.
func (m *MockOnboardingClient) SubmitClientApplication(ctx context.Context, in *SubmitClientApplicationRequest, opts ...grpc.CallOption) (*SubmitClientApplicationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitClientApplication", varargs...)
	ret0, _ := ret[0].(*SubmitClientApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientApplication indicates an expected call of SubmitClientApplication.
func (mr *MockOnboardingClientMockRecorder) SubmitClientApplication(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientApplication", reflect.TypeOf((*MockOnboardingClient)(nil).SubmitClientApplication), varargs...)
}

// SubmitClientFaceChallenge mocks base method.
func (m *MockOnboardingClient) SubmitClientFaceChallenge(ctx context.Context, in *SubmitClientFaceChallengeRequest, opts ...grpc.CallOption) (*SubmitClientFaceChallengeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitClientFaceChallenge", varargs...)
	ret0, _ := ret[0].(*SubmitClientFaceChallengeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientFaceChallenge indicates an expected call of SubmitClientFaceChallenge.
func (mr *MockOnboardingClientMockRecorder) SubmitClientFaceChallenge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientFaceChallenge", reflect.TypeOf((*MockOnboardingClient)(nil).SubmitClientFaceChallenge), varargs...)
}

// SubmitClientWetSign mocks base method.
func (m *MockOnboardingClient) SubmitClientWetSign(ctx context.Context, in *SubmitClientWetSignRequest, opts ...grpc.CallOption) (*SubmitClientWetSignResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitClientWetSign", varargs...)
	ret0, _ := ret[0].(*SubmitClientWetSignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientWetSign indicates an expected call of SubmitClientWetSign.
func (mr *MockOnboardingClientMockRecorder) SubmitClientWetSign(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientWetSign", reflect.TypeOf((*MockOnboardingClient)(nil).SubmitClientWetSign), varargs...)
}

// VerifyClientOTP mocks base method.
func (m *MockOnboardingClient) VerifyClientOTP(ctx context.Context, in *VerifyClientOTPRequest, opts ...grpc.CallOption) (*VerifyClientOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyClientOTP", varargs...)
	ret0, _ := ret[0].(*VerifyClientOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyClientOTP indicates an expected call of VerifyClientOTP.
func (mr *MockOnboardingClientMockRecorder) VerifyClientOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyClientOTP", reflect.TypeOf((*MockOnboardingClient)(nil).VerifyClientOTP), varargs...)
}

// MockOnboardingServer is a mock of OnboardingServer interface.
type MockOnboardingServer struct {
	ctrl     *gomock.Controller
	recorder *MockOnboardingServerMockRecorder
}

// MockOnboardingServerMockRecorder is the mock recorder for MockOnboardingServer.
type MockOnboardingServerMockRecorder struct {
	mock *MockOnboardingServer
}

// NewMockOnboardingServer creates a new mock instance.
func NewMockOnboardingServer(ctrl *gomock.Controller) *MockOnboardingServer {
	mock := &MockOnboardingServer{ctrl: ctrl}
	mock.recorder = &MockOnboardingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOnboardingServer) EXPECT() *MockOnboardingServerMockRecorder {
	return m.recorder
}

// GetClientContract mocks base method.
func (m *MockOnboardingServer) GetClientContract(ctx context.Context, in *GetClientContractRequest) (*GetClientContractResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientContract", ctx, in)
	ret0, _ := ret[0].(*GetClientContractResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientContract indicates an expected call of GetClientContract.
func (mr *MockOnboardingServerMockRecorder) GetClientContract(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientContract", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientContract), ctx, in)
}

// GetClientEkycProfile mocks base method.
func (m *MockOnboardingServer) GetClientEkycProfile(ctx context.Context, in *GetClientEkycProfileRequest) (*GetClientEkycProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientEkycProfile", ctx, in)
	ret0, _ := ret[0].(*GetClientEkycProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientEkycProfile indicates an expected call of GetClientEkycProfile.
func (mr *MockOnboardingServerMockRecorder) GetClientEkycProfile(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientEkycProfile", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientEkycProfile), ctx, in)
}

// GetClientOnboarding mocks base method.
func (m *MockOnboardingServer) GetClientOnboarding(ctx context.Context, in *GetClientOnboardingRequest) (*GetClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientOnboarding", ctx, in)
	ret0, _ := ret[0].(*GetClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientOnboarding indicates an expected call of GetClientOnboarding.
func (mr *MockOnboardingServerMockRecorder) GetClientOnboarding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientOnboarding", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientOnboarding), ctx, in)
}

// GetClientPermissions mocks base method.
func (m *MockOnboardingServer) GetClientPermissions(ctx context.Context, in *GetClientPermissionsRequest) (*GetClientPermissionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientPermissions", ctx, in)
	ret0, _ := ret[0].(*GetClientPermissionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientPermissions indicates an expected call of GetClientPermissions.
func (mr *MockOnboardingServerMockRecorder) GetClientPermissions(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientPermissions", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientPermissions), ctx, in)
}

// GetClientRejection mocks base method.
func (m *MockOnboardingServer) GetClientRejection(ctx context.Context, in *GetClientRejectionRequest) (*GetClientRejectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientRejection", ctx, in)
	ret0, _ := ret[0].(*GetClientRejectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientRejection indicates an expected call of GetClientRejection.
func (mr *MockOnboardingServerMockRecorder) GetClientRejection(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientRejection", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientRejection), ctx, in)
}

// GetClientResources mocks base method.
func (m *MockOnboardingServer) GetClientResources(ctx context.Context, in *GetClientResourcesRequest) (*GetClientResourcesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientResources", ctx, in)
	ret0, _ := ret[0].(*GetClientResourcesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientResources indicates an expected call of GetClientResources.
func (mr *MockOnboardingServerMockRecorder) GetClientResources(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientResources", reflect.TypeOf((*MockOnboardingServer)(nil).GetClientResources), ctx, in)
}

// LinkingClientAccount mocks base method.
func (m *MockOnboardingServer) LinkingClientAccount(ctx context.Context, in *LinkingClientAccountRequest) (*LinkingClientAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkingClientAccount", ctx, in)
	ret0, _ := ret[0].(*LinkingClientAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkingClientAccount indicates an expected call of LinkingClientAccount.
func (mr *MockOnboardingServerMockRecorder) LinkingClientAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkingClientAccount", reflect.TypeOf((*MockOnboardingServer)(nil).LinkingClientAccount), ctx, in)
}

// ListClientOnboarding mocks base method.
func (m *MockOnboardingServer) ListClientOnboarding(ctx context.Context, in *ListClientOnboardingRequest) (*ListClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClientOnboarding", ctx, in)
	ret0, _ := ret[0].(*ListClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientOnboarding indicates an expected call of ListClientOnboarding.
func (mr *MockOnboardingServerMockRecorder) ListClientOnboarding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientOnboarding", reflect.TypeOf((*MockOnboardingServer)(nil).ListClientOnboarding), ctx, in)
}

// ListOnboardingByUserIDs mocks base method.
func (m *MockOnboardingServer) ListOnboardingByUserIDs(ctx context.Context, in *ListOnboardingByUserIDsRequest) (*ListOnboardingByUserIDsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnboardingByUserIDs", ctx, in)
	ret0, _ := ret[0].(*ListOnboardingByUserIDsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnboardingByUserIDs indicates an expected call of ListOnboardingByUserIDs.
func (mr *MockOnboardingServerMockRecorder) ListOnboardingByUserIDs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnboardingByUserIDs", reflect.TypeOf((*MockOnboardingServer)(nil).ListOnboardingByUserIDs), ctx, in)
}

// QueryOnboardingStatus mocks base method.
func (m *MockOnboardingServer) QueryOnboardingStatus(ctx context.Context, in *QueryOnboardingStatusRequest) (*QueryOnboardingStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryOnboardingStatus", ctx, in)
	ret0, _ := ret[0].(*QueryOnboardingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOnboardingStatus indicates an expected call of QueryOnboardingStatus.
func (mr *MockOnboardingServerMockRecorder) QueryOnboardingStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOnboardingStatus", reflect.TypeOf((*MockOnboardingServer)(nil).QueryOnboardingStatus), ctx, in)
}

// RegisterClientOnboarding mocks base method.
func (m *MockOnboardingServer) RegisterClientOnboarding(ctx context.Context, in *RegisterClientOnboardingRequest) (*RegisterClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterClientOnboarding", ctx, in)
	ret0, _ := ret[0].(*RegisterClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterClientOnboarding indicates an expected call of RegisterClientOnboarding.
func (mr *MockOnboardingServerMockRecorder) RegisterClientOnboarding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterClientOnboarding", reflect.TypeOf((*MockOnboardingServer)(nil).RegisterClientOnboarding), ctx, in)
}

// ReinitClientOnboarding mocks base method.
func (m *MockOnboardingServer) ReinitClientOnboarding(ctx context.Context, in *ReinitClientOnboardingRequest) (*ReinitClientOnboardingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinitClientOnboarding", ctx, in)
	ret0, _ := ret[0].(*ReinitClientOnboardingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReinitClientOnboarding indicates an expected call of ReinitClientOnboarding.
func (mr *MockOnboardingServerMockRecorder) ReinitClientOnboarding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinitClientOnboarding", reflect.TypeOf((*MockOnboardingServer)(nil).ReinitClientOnboarding), ctx, in)
}

// RequestClientOTP mocks base method.
func (m *MockOnboardingServer) RequestClientOTP(ctx context.Context, in *RequestClientOTPRequest) (*RequestClientOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestClientOTP", ctx, in)
	ret0, _ := ret[0].(*RequestClientOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestClientOTP indicates an expected call of RequestClientOTP.
func (mr *MockOnboardingServerMockRecorder) RequestClientOTP(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestClientOTP", reflect.TypeOf((*MockOnboardingServer)(nil).RequestClientOTP), ctx, in)
}

// ResetClientEkycNfc mocks base method.
func (m *MockOnboardingServer) ResetClientEkycNfc(ctx context.Context, in *ResetClientEkycNfcRequest) (*ResetClientEkycNfcResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetClientEkycNfc", ctx, in)
	ret0, _ := ret[0].(*ResetClientEkycNfcResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResetClientEkycNfc indicates an expected call of ResetClientEkycNfc.
func (mr *MockOnboardingServerMockRecorder) ResetClientEkycNfc(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetClientEkycNfc", reflect.TypeOf((*MockOnboardingServer)(nil).ResetClientEkycNfc), ctx, in)
}

// SubmitClientApplication mocks base method.
func (m *MockOnboardingServer) SubmitClientApplication(ctx context.Context, in *SubmitClientApplicationRequest) (*SubmitClientApplicationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitClientApplication", ctx, in)
	ret0, _ := ret[0].(*SubmitClientApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientApplication indicates an expected call of SubmitClientApplication.
func (mr *MockOnboardingServerMockRecorder) SubmitClientApplication(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientApplication", reflect.TypeOf((*MockOnboardingServer)(nil).SubmitClientApplication), ctx, in)
}

// SubmitClientFaceChallenge mocks base method.
func (m *MockOnboardingServer) SubmitClientFaceChallenge(ctx context.Context, in *SubmitClientFaceChallengeRequest) (*SubmitClientFaceChallengeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitClientFaceChallenge", ctx, in)
	ret0, _ := ret[0].(*SubmitClientFaceChallengeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientFaceChallenge indicates an expected call of SubmitClientFaceChallenge.
func (mr *MockOnboardingServerMockRecorder) SubmitClientFaceChallenge(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientFaceChallenge", reflect.TypeOf((*MockOnboardingServer)(nil).SubmitClientFaceChallenge), ctx, in)
}

// SubmitClientWetSign mocks base method.
func (m *MockOnboardingServer) SubmitClientWetSign(ctx context.Context, in *SubmitClientWetSignRequest) (*SubmitClientWetSignResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitClientWetSign", ctx, in)
	ret0, _ := ret[0].(*SubmitClientWetSignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitClientWetSign indicates an expected call of SubmitClientWetSign.
func (mr *MockOnboardingServerMockRecorder) SubmitClientWetSign(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitClientWetSign", reflect.TypeOf((*MockOnboardingServer)(nil).SubmitClientWetSign), ctx, in)
}

// VerifyClientOTP mocks base method.
func (m *MockOnboardingServer) VerifyClientOTP(ctx context.Context, in *VerifyClientOTPRequest) (*VerifyClientOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyClientOTP", ctx, in)
	ret0, _ := ret[0].(*VerifyClientOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyClientOTP indicates an expected call of VerifyClientOTP.
func (mr *MockOnboardingServerMockRecorder) VerifyClientOTP(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyClientOTP", reflect.TypeOf((*MockOnboardingServer)(nil).VerifyClientOTP), ctx, in)
}
