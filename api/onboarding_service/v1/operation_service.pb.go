// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: onboarding_service/v1/operation_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TriggerSyncOnboardingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingRequest) Reset() {
	*x = TriggerSyncOnboardingRequest{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingRequest) ProtoMessage() {}

func (x *TriggerSyncOnboardingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingRequest.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{0}
}

func (x *TriggerSyncOnboardingRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *TriggerSyncOnboardingRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type TriggerSyncOnboardingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingResponse) Reset() {
	*x = TriggerSyncOnboardingResponse{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingResponse) ProtoMessage() {}

func (x *TriggerSyncOnboardingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingResponse.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{1}
}

type TriggerSyncOnboardingJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingJobRequest) Reset() {
	*x = TriggerSyncOnboardingJobRequest{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingJobRequest) ProtoMessage() {}

func (x *TriggerSyncOnboardingJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingJobRequest.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingJobRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{2}
}

func (x *TriggerSyncOnboardingJobRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *TriggerSyncOnboardingJobRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type TriggerSyncOnboardingJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingJobResponse) Reset() {
	*x = TriggerSyncOnboardingJobResponse{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingJobResponse) ProtoMessage() {}

func (x *TriggerSyncOnboardingJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingJobResponse.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingJobResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{3}
}

type TriggerSubmitSelfieJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSubmitSelfieJobRequest) Reset() {
	*x = TriggerSubmitSelfieJobRequest{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSubmitSelfieJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSubmitSelfieJobRequest) ProtoMessage() {}

func (x *TriggerSubmitSelfieJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSubmitSelfieJobRequest.ProtoReflect.Descriptor instead.
func (*TriggerSubmitSelfieJobRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{4}
}

func (x *TriggerSubmitSelfieJobRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *TriggerSubmitSelfieJobRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type TriggerSubmitSelfieJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSubmitSelfieJobResponse) Reset() {
	*x = TriggerSubmitSelfieJobResponse{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSubmitSelfieJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSubmitSelfieJobResponse) ProtoMessage() {}

func (x *TriggerSubmitSelfieJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSubmitSelfieJobResponse.ProtoReflect.Descriptor instead.
func (*TriggerSubmitSelfieJobResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{5}
}

type TriggerSyncOnboardingsJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*OnboardingOp        `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingsJobRequest) Reset() {
	*x = TriggerSyncOnboardingsJobRequest{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingsJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingsJobRequest) ProtoMessage() {}

func (x *TriggerSyncOnboardingsJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingsJobRequest.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingsJobRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{6}
}

func (x *TriggerSyncOnboardingsJobRequest) GetData() []*OnboardingOp {
	if x != nil {
		return x.Data
	}
	return nil
}

type TriggerSyncOnboardingsJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncOnboardingsJobResponse) Reset() {
	*x = TriggerSyncOnboardingsJobResponse{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncOnboardingsJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncOnboardingsJobResponse) ProtoMessage() {}

func (x *TriggerSyncOnboardingsJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncOnboardingsJobResponse.ProtoReflect.Descriptor instead.
func (*TriggerSyncOnboardingsJobResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{7}
}

type TriggerContractSigningJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerContractSigningJobRequest) Reset() {
	*x = TriggerContractSigningJobRequest{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerContractSigningJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerContractSigningJobRequest) ProtoMessage() {}

func (x *TriggerContractSigningJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerContractSigningJobRequest.ProtoReflect.Descriptor instead.
func (*TriggerContractSigningJobRequest) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{8}
}

func (x *TriggerContractSigningJobRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *TriggerContractSigningJobRequest) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

type TriggerContractSigningJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerContractSigningJobResponse) Reset() {
	*x = TriggerContractSigningJobResponse{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerContractSigningJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerContractSigningJobResponse) ProtoMessage() {}

func (x *TriggerContractSigningJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerContractSigningJobResponse.ProtoReflect.Descriptor instead.
func (*TriggerContractSigningJobResponse) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{9}
}

type OnboardingOp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	OnboardingId  int64                  `protobuf:"varint,2,opt,name=onboarding_id,json=onboardingId,proto3" json:"onboarding_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnboardingOp) Reset() {
	*x = OnboardingOp{}
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnboardingOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingOp) ProtoMessage() {}

func (x *OnboardingOp) ProtoReflect() protoreflect.Message {
	mi := &file_onboarding_service_v1_operation_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingOp.ProtoReflect.Descriptor instead.
func (*OnboardingOp) Descriptor() ([]byte, []int) {
	return file_onboarding_service_v1_operation_service_proto_rawDescGZIP(), []int{10}
}

func (x *OnboardingOp) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *OnboardingOp) GetOnboardingId() int64 {
	if x != nil {
		return x.OnboardingId
	}
	return 0
}

var File_onboarding_service_v1_operation_service_proto protoreflect.FileDescriptor

const file_onboarding_service_v1_operation_service_proto_rawDesc = "" +
	"\n" +
	"-onboarding_service/v1/operation_service.proto\x12\x15onboarding_service.v1\x1a\x17validate/validate.proto\"t\n" +
	"\x1cTriggerSyncOnboardingRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId\"\x1f\n" +
	"\x1dTriggerSyncOnboardingResponse\"w\n" +
	"\x1fTriggerSyncOnboardingJobRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId\"\"\n" +
	" TriggerSyncOnboardingJobResponse\"u\n" +
	"\x1dTriggerSubmitSelfieJobRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId\" \n" +
	"\x1eTriggerSubmitSelfieJobResponse\"e\n" +
	" TriggerSyncOnboardingsJobRequest\x12A\n" +
	"\x04data\x18\x01 \x03(\v2#.onboarding_service.v1.OnboardingOpB\b\xfaB\x05\x92\x01\x02\b\x01R\x04data\"#\n" +
	"!TriggerSyncOnboardingsJobResponse\"x\n" +
	" TriggerContractSigningJobRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId\"#\n" +
	"!TriggerContractSigningJobResponse\"d\n" +
	"\fOnboardingOp\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12,\n" +
	"\ronboarding_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fonboardingId2\xd5\x05\n" +
	"\tOperation\x12\x84\x01\n" +
	"\x15TriggerSyncOnboarding\x123.onboarding_service.v1.TriggerSyncOnboardingRequest\x1a4.onboarding_service.v1.TriggerSyncOnboardingResponse\"\x00\x12\x8a\x01\n" +
	"\x16TriggerSubmitSelfieJob\x124.onboarding_service.v1.TriggerSubmitSelfieJobRequest\x1a5.onboarding_service.v1.TriggerSubmitSelfieJobResponse\"\x03\x88\x02\x01\x12\x90\x01\n" +
	"\x19TriggerContractSigningJob\x127.onboarding_service.v1.TriggerContractSigningJobRequest\x1a8.onboarding_service.v1.TriggerContractSigningJobResponse\"\x00\x12\x8d\x01\n" +
	"\x18TriggerSyncOnboardingJob\x126.onboarding_service.v1.TriggerSyncOnboardingJobRequest\x1a7.onboarding_service.v1.TriggerSyncOnboardingJobResponse\"\x00\x12\x90\x01\n" +
	"\x19TriggerSyncOnboardingsJob\x127.onboarding_service.v1.TriggerSyncOnboardingsJobRequest\x1a8.onboarding_service.v1.TriggerSyncOnboardingsJobResponse\"\x00B*Z(installment/api/onboarding-service/v1;v1b\x06proto3"

var (
	file_onboarding_service_v1_operation_service_proto_rawDescOnce sync.Once
	file_onboarding_service_v1_operation_service_proto_rawDescData []byte
)

func file_onboarding_service_v1_operation_service_proto_rawDescGZIP() []byte {
	file_onboarding_service_v1_operation_service_proto_rawDescOnce.Do(func() {
		file_onboarding_service_v1_operation_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_onboarding_service_v1_operation_service_proto_rawDesc), len(file_onboarding_service_v1_operation_service_proto_rawDesc)))
	})
	return file_onboarding_service_v1_operation_service_proto_rawDescData
}

var file_onboarding_service_v1_operation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_onboarding_service_v1_operation_service_proto_goTypes = []any{
	(*TriggerSyncOnboardingRequest)(nil),      // 0: onboarding_service.v1.TriggerSyncOnboardingRequest
	(*TriggerSyncOnboardingResponse)(nil),     // 1: onboarding_service.v1.TriggerSyncOnboardingResponse
	(*TriggerSyncOnboardingJobRequest)(nil),   // 2: onboarding_service.v1.TriggerSyncOnboardingJobRequest
	(*TriggerSyncOnboardingJobResponse)(nil),  // 3: onboarding_service.v1.TriggerSyncOnboardingJobResponse
	(*TriggerSubmitSelfieJobRequest)(nil),     // 4: onboarding_service.v1.TriggerSubmitSelfieJobRequest
	(*TriggerSubmitSelfieJobResponse)(nil),    // 5: onboarding_service.v1.TriggerSubmitSelfieJobResponse
	(*TriggerSyncOnboardingsJobRequest)(nil),  // 6: onboarding_service.v1.TriggerSyncOnboardingsJobRequest
	(*TriggerSyncOnboardingsJobResponse)(nil), // 7: onboarding_service.v1.TriggerSyncOnboardingsJobResponse
	(*TriggerContractSigningJobRequest)(nil),  // 8: onboarding_service.v1.TriggerContractSigningJobRequest
	(*TriggerContractSigningJobResponse)(nil), // 9: onboarding_service.v1.TriggerContractSigningJobResponse
	(*OnboardingOp)(nil),                      // 10: onboarding_service.v1.OnboardingOp
}
var file_onboarding_service_v1_operation_service_proto_depIdxs = []int32{
	10, // 0: onboarding_service.v1.TriggerSyncOnboardingsJobRequest.data:type_name -> onboarding_service.v1.OnboardingOp
	0,  // 1: onboarding_service.v1.Operation.TriggerSyncOnboarding:input_type -> onboarding_service.v1.TriggerSyncOnboardingRequest
	4,  // 2: onboarding_service.v1.Operation.TriggerSubmitSelfieJob:input_type -> onboarding_service.v1.TriggerSubmitSelfieJobRequest
	8,  // 3: onboarding_service.v1.Operation.TriggerContractSigningJob:input_type -> onboarding_service.v1.TriggerContractSigningJobRequest
	2,  // 4: onboarding_service.v1.Operation.TriggerSyncOnboardingJob:input_type -> onboarding_service.v1.TriggerSyncOnboardingJobRequest
	6,  // 5: onboarding_service.v1.Operation.TriggerSyncOnboardingsJob:input_type -> onboarding_service.v1.TriggerSyncOnboardingsJobRequest
	1,  // 6: onboarding_service.v1.Operation.TriggerSyncOnboarding:output_type -> onboarding_service.v1.TriggerSyncOnboardingResponse
	5,  // 7: onboarding_service.v1.Operation.TriggerSubmitSelfieJob:output_type -> onboarding_service.v1.TriggerSubmitSelfieJobResponse
	9,  // 8: onboarding_service.v1.Operation.TriggerContractSigningJob:output_type -> onboarding_service.v1.TriggerContractSigningJobResponse
	3,  // 9: onboarding_service.v1.Operation.TriggerSyncOnboardingJob:output_type -> onboarding_service.v1.TriggerSyncOnboardingJobResponse
	7,  // 10: onboarding_service.v1.Operation.TriggerSyncOnboardingsJob:output_type -> onboarding_service.v1.TriggerSyncOnboardingsJobResponse
	6,  // [6:11] is the sub-list for method output_type
	1,  // [1:6] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_onboarding_service_v1_operation_service_proto_init() }
func file_onboarding_service_v1_operation_service_proto_init() {
	if File_onboarding_service_v1_operation_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_onboarding_service_v1_operation_service_proto_rawDesc), len(file_onboarding_service_v1_operation_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_onboarding_service_v1_operation_service_proto_goTypes,
		DependencyIndexes: file_onboarding_service_v1_operation_service_proto_depIdxs,
		MessageInfos:      file_onboarding_service_v1_operation_service_proto_msgTypes,
	}.Build()
	File_onboarding_service_v1_operation_service_proto = out.File
	file_onboarding_service_v1_operation_service_proto_goTypes = nil
	file_onboarding_service_v1_operation_service_proto_depIdxs = nil
}
