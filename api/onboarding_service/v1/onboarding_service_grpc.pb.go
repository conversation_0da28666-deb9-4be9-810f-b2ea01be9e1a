// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: onboarding_service/v1/onboarding_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Onboarding_GetClientPermissions_FullMethodName      = "/onboarding_service.v1.Onboarding/GetClientPermissions"
	Onboarding_GetClientEkycProfile_FullMethodName      = "/onboarding_service.v1.Onboarding/GetClientEkycProfile"
	Onboarding_ResetClientEkycNfc_FullMethodName        = "/onboarding_service.v1.Onboarding/ResetClientEkycNfc"
	Onboarding_RegisterClientOnboarding_FullMethodName  = "/onboarding_service.v1.Onboarding/RegisterClientOnboarding"
	Onboarding_GetClientOnboarding_FullMethodName       = "/onboarding_service.v1.Onboarding/GetClientOnboarding"
	Onboarding_ListClientOnboarding_FullMethodName      = "/onboarding_service.v1.Onboarding/ListClientOnboarding"
	Onboarding_SubmitClientApplication_FullMethodName   = "/onboarding_service.v1.Onboarding/SubmitClientApplication"
	Onboarding_SubmitClientWetSign_FullMethodName       = "/onboarding_service.v1.Onboarding/SubmitClientWetSign"
	Onboarding_SubmitClientFaceChallenge_FullMethodName = "/onboarding_service.v1.Onboarding/SubmitClientFaceChallenge"
	Onboarding_RequestClientOTP_FullMethodName          = "/onboarding_service.v1.Onboarding/RequestClientOTP"
	Onboarding_VerifyClientOTP_FullMethodName           = "/onboarding_service.v1.Onboarding/VerifyClientOTP"
	Onboarding_GetClientResources_FullMethodName        = "/onboarding_service.v1.Onboarding/GetClientResources"
	Onboarding_GetClientContract_FullMethodName         = "/onboarding_service.v1.Onboarding/GetClientContract"
	Onboarding_GetClientRejection_FullMethodName        = "/onboarding_service.v1.Onboarding/GetClientRejection"
	Onboarding_ReinitClientOnboarding_FullMethodName    = "/onboarding_service.v1.Onboarding/ReinitClientOnboarding"
	Onboarding_LinkingClientAccount_FullMethodName      = "/onboarding_service.v1.Onboarding/LinkingClientAccount"
	Onboarding_QueryOnboardingStatus_FullMethodName     = "/onboarding_service.v1.Onboarding/QueryOnboardingStatus"
	Onboarding_ListOnboardingByUserIDs_FullMethodName   = "/onboarding_service.v1.Onboarding/ListOnboardingByUserIDs"
)

// OnboardingClient is the client API for Onboarding service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnboardingClient interface {
	GetClientPermissions(ctx context.Context, in *GetClientPermissionsRequest, opts ...grpc.CallOption) (*GetClientPermissionsResponse, error)
	GetClientEkycProfile(ctx context.Context, in *GetClientEkycProfileRequest, opts ...grpc.CallOption) (*GetClientEkycProfileResponse, error)
	ResetClientEkycNfc(ctx context.Context, in *ResetClientEkycNfcRequest, opts ...grpc.CallOption) (*ResetClientEkycNfcResponse, error)
	RegisterClientOnboarding(ctx context.Context, in *RegisterClientOnboardingRequest, opts ...grpc.CallOption) (*RegisterClientOnboardingResponse, error)
	GetClientOnboarding(ctx context.Context, in *GetClientOnboardingRequest, opts ...grpc.CallOption) (*GetClientOnboardingResponse, error)
	ListClientOnboarding(ctx context.Context, in *ListClientOnboardingRequest, opts ...grpc.CallOption) (*ListClientOnboardingResponse, error)
	SubmitClientApplication(ctx context.Context, in *SubmitClientApplicationRequest, opts ...grpc.CallOption) (*SubmitClientApplicationResponse, error)
	SubmitClientWetSign(ctx context.Context, in *SubmitClientWetSignRequest, opts ...grpc.CallOption) (*SubmitClientWetSignResponse, error)
	SubmitClientFaceChallenge(ctx context.Context, in *SubmitClientFaceChallengeRequest, opts ...grpc.CallOption) (*SubmitClientFaceChallengeResponse, error)
	RequestClientOTP(ctx context.Context, in *RequestClientOTPRequest, opts ...grpc.CallOption) (*RequestClientOTPResponse, error)
	VerifyClientOTP(ctx context.Context, in *VerifyClientOTPRequest, opts ...grpc.CallOption) (*VerifyClientOTPResponse, error)
	GetClientResources(ctx context.Context, in *GetClientResourcesRequest, opts ...grpc.CallOption) (*GetClientResourcesResponse, error)
	GetClientContract(ctx context.Context, in *GetClientContractRequest, opts ...grpc.CallOption) (*GetClientContractResponse, error)
	GetClientRejection(ctx context.Context, in *GetClientRejectionRequest, opts ...grpc.CallOption) (*GetClientRejectionResponse, error)
	ReinitClientOnboarding(ctx context.Context, in *ReinitClientOnboardingRequest, opts ...grpc.CallOption) (*ReinitClientOnboardingResponse, error)
	LinkingClientAccount(ctx context.Context, in *LinkingClientAccountRequest, opts ...grpc.CallOption) (*LinkingClientAccountResponse, error)
	QueryOnboardingStatus(ctx context.Context, in *QueryOnboardingStatusRequest, opts ...grpc.CallOption) (*QueryOnboardingStatusResponse, error)
	ListOnboardingByUserIDs(ctx context.Context, in *ListOnboardingByUserIDsRequest, opts ...grpc.CallOption) (*ListOnboardingByUserIDsResponse, error)
}

type onboardingClient struct {
	cc grpc.ClientConnInterface
}

func NewOnboardingClient(cc grpc.ClientConnInterface) OnboardingClient {
	return &onboardingClient{cc}
}

func (c *onboardingClient) GetClientPermissions(ctx context.Context, in *GetClientPermissionsRequest, opts ...grpc.CallOption) (*GetClientPermissionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientPermissionsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientPermissions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetClientEkycProfile(ctx context.Context, in *GetClientEkycProfileRequest, opts ...grpc.CallOption) (*GetClientEkycProfileResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientEkycProfileResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientEkycProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ResetClientEkycNfc(ctx context.Context, in *ResetClientEkycNfcRequest, opts ...grpc.CallOption) (*ResetClientEkycNfcResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetClientEkycNfcResponse)
	err := c.cc.Invoke(ctx, Onboarding_ResetClientEkycNfc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) RegisterClientOnboarding(ctx context.Context, in *RegisterClientOnboardingRequest, opts ...grpc.CallOption) (*RegisterClientOnboardingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterClientOnboardingResponse)
	err := c.cc.Invoke(ctx, Onboarding_RegisterClientOnboarding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetClientOnboarding(ctx context.Context, in *GetClientOnboardingRequest, opts ...grpc.CallOption) (*GetClientOnboardingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientOnboardingResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientOnboarding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ListClientOnboarding(ctx context.Context, in *ListClientOnboardingRequest, opts ...grpc.CallOption) (*ListClientOnboardingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClientOnboardingResponse)
	err := c.cc.Invoke(ctx, Onboarding_ListClientOnboarding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) SubmitClientApplication(ctx context.Context, in *SubmitClientApplicationRequest, opts ...grpc.CallOption) (*SubmitClientApplicationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitClientApplicationResponse)
	err := c.cc.Invoke(ctx, Onboarding_SubmitClientApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) SubmitClientWetSign(ctx context.Context, in *SubmitClientWetSignRequest, opts ...grpc.CallOption) (*SubmitClientWetSignResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitClientWetSignResponse)
	err := c.cc.Invoke(ctx, Onboarding_SubmitClientWetSign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) SubmitClientFaceChallenge(ctx context.Context, in *SubmitClientFaceChallengeRequest, opts ...grpc.CallOption) (*SubmitClientFaceChallengeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubmitClientFaceChallengeResponse)
	err := c.cc.Invoke(ctx, Onboarding_SubmitClientFaceChallenge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) RequestClientOTP(ctx context.Context, in *RequestClientOTPRequest, opts ...grpc.CallOption) (*RequestClientOTPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RequestClientOTPResponse)
	err := c.cc.Invoke(ctx, Onboarding_RequestClientOTP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) VerifyClientOTP(ctx context.Context, in *VerifyClientOTPRequest, opts ...grpc.CallOption) (*VerifyClientOTPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyClientOTPResponse)
	err := c.cc.Invoke(ctx, Onboarding_VerifyClientOTP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetClientResources(ctx context.Context, in *GetClientResourcesRequest, opts ...grpc.CallOption) (*GetClientResourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientResourcesResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientResources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetClientContract(ctx context.Context, in *GetClientContractRequest, opts ...grpc.CallOption) (*GetClientContractResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientContractResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetClientRejection(ctx context.Context, in *GetClientRejectionRequest, opts ...grpc.CallOption) (*GetClientRejectionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientRejectionResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetClientRejection_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ReinitClientOnboarding(ctx context.Context, in *ReinitClientOnboardingRequest, opts ...grpc.CallOption) (*ReinitClientOnboardingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReinitClientOnboardingResponse)
	err := c.cc.Invoke(ctx, Onboarding_ReinitClientOnboarding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) LinkingClientAccount(ctx context.Context, in *LinkingClientAccountRequest, opts ...grpc.CallOption) (*LinkingClientAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LinkingClientAccountResponse)
	err := c.cc.Invoke(ctx, Onboarding_LinkingClientAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) QueryOnboardingStatus(ctx context.Context, in *QueryOnboardingStatusRequest, opts ...grpc.CallOption) (*QueryOnboardingStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryOnboardingStatusResponse)
	err := c.cc.Invoke(ctx, Onboarding_QueryOnboardingStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ListOnboardingByUserIDs(ctx context.Context, in *ListOnboardingByUserIDsRequest, opts ...grpc.CallOption) (*ListOnboardingByUserIDsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListOnboardingByUserIDsResponse)
	err := c.cc.Invoke(ctx, Onboarding_ListOnboardingByUserIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnboardingServer is the server API for Onboarding service.
// All implementations must embed UnimplementedOnboardingServer
// for forward compatibility.
type OnboardingServer interface {
	GetClientPermissions(context.Context, *GetClientPermissionsRequest) (*GetClientPermissionsResponse, error)
	GetClientEkycProfile(context.Context, *GetClientEkycProfileRequest) (*GetClientEkycProfileResponse, error)
	ResetClientEkycNfc(context.Context, *ResetClientEkycNfcRequest) (*ResetClientEkycNfcResponse, error)
	RegisterClientOnboarding(context.Context, *RegisterClientOnboardingRequest) (*RegisterClientOnboardingResponse, error)
	GetClientOnboarding(context.Context, *GetClientOnboardingRequest) (*GetClientOnboardingResponse, error)
	ListClientOnboarding(context.Context, *ListClientOnboardingRequest) (*ListClientOnboardingResponse, error)
	SubmitClientApplication(context.Context, *SubmitClientApplicationRequest) (*SubmitClientApplicationResponse, error)
	SubmitClientWetSign(context.Context, *SubmitClientWetSignRequest) (*SubmitClientWetSignResponse, error)
	SubmitClientFaceChallenge(context.Context, *SubmitClientFaceChallengeRequest) (*SubmitClientFaceChallengeResponse, error)
	RequestClientOTP(context.Context, *RequestClientOTPRequest) (*RequestClientOTPResponse, error)
	VerifyClientOTP(context.Context, *VerifyClientOTPRequest) (*VerifyClientOTPResponse, error)
	GetClientResources(context.Context, *GetClientResourcesRequest) (*GetClientResourcesResponse, error)
	GetClientContract(context.Context, *GetClientContractRequest) (*GetClientContractResponse, error)
	GetClientRejection(context.Context, *GetClientRejectionRequest) (*GetClientRejectionResponse, error)
	ReinitClientOnboarding(context.Context, *ReinitClientOnboardingRequest) (*ReinitClientOnboardingResponse, error)
	LinkingClientAccount(context.Context, *LinkingClientAccountRequest) (*LinkingClientAccountResponse, error)
	QueryOnboardingStatus(context.Context, *QueryOnboardingStatusRequest) (*QueryOnboardingStatusResponse, error)
	ListOnboardingByUserIDs(context.Context, *ListOnboardingByUserIDsRequest) (*ListOnboardingByUserIDsResponse, error)
	mustEmbedUnimplementedOnboardingServer()
}

// UnimplementedOnboardingServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOnboardingServer struct{}

func (UnimplementedOnboardingServer) GetClientPermissions(context.Context, *GetClientPermissionsRequest) (*GetClientPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientPermissions not implemented")
}
func (UnimplementedOnboardingServer) GetClientEkycProfile(context.Context, *GetClientEkycProfileRequest) (*GetClientEkycProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientEkycProfile not implemented")
}
func (UnimplementedOnboardingServer) ResetClientEkycNfc(context.Context, *ResetClientEkycNfcRequest) (*ResetClientEkycNfcResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetClientEkycNfc not implemented")
}
func (UnimplementedOnboardingServer) RegisterClientOnboarding(context.Context, *RegisterClientOnboardingRequest) (*RegisterClientOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterClientOnboarding not implemented")
}
func (UnimplementedOnboardingServer) GetClientOnboarding(context.Context, *GetClientOnboardingRequest) (*GetClientOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientOnboarding not implemented")
}
func (UnimplementedOnboardingServer) ListClientOnboarding(context.Context, *ListClientOnboardingRequest) (*ListClientOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClientOnboarding not implemented")
}
func (UnimplementedOnboardingServer) SubmitClientApplication(context.Context, *SubmitClientApplicationRequest) (*SubmitClientApplicationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitClientApplication not implemented")
}
func (UnimplementedOnboardingServer) SubmitClientWetSign(context.Context, *SubmitClientWetSignRequest) (*SubmitClientWetSignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitClientWetSign not implemented")
}
func (UnimplementedOnboardingServer) SubmitClientFaceChallenge(context.Context, *SubmitClientFaceChallengeRequest) (*SubmitClientFaceChallengeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitClientFaceChallenge not implemented")
}
func (UnimplementedOnboardingServer) RequestClientOTP(context.Context, *RequestClientOTPRequest) (*RequestClientOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestClientOTP not implemented")
}
func (UnimplementedOnboardingServer) VerifyClientOTP(context.Context, *VerifyClientOTPRequest) (*VerifyClientOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyClientOTP not implemented")
}
func (UnimplementedOnboardingServer) GetClientResources(context.Context, *GetClientResourcesRequest) (*GetClientResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientResources not implemented")
}
func (UnimplementedOnboardingServer) GetClientContract(context.Context, *GetClientContractRequest) (*GetClientContractResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientContract not implemented")
}
func (UnimplementedOnboardingServer) GetClientRejection(context.Context, *GetClientRejectionRequest) (*GetClientRejectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientRejection not implemented")
}
func (UnimplementedOnboardingServer) ReinitClientOnboarding(context.Context, *ReinitClientOnboardingRequest) (*ReinitClientOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReinitClientOnboarding not implemented")
}
func (UnimplementedOnboardingServer) LinkingClientAccount(context.Context, *LinkingClientAccountRequest) (*LinkingClientAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkingClientAccount not implemented")
}
func (UnimplementedOnboardingServer) QueryOnboardingStatus(context.Context, *QueryOnboardingStatusRequest) (*QueryOnboardingStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryOnboardingStatus not implemented")
}
func (UnimplementedOnboardingServer) ListOnboardingByUserIDs(context.Context, *ListOnboardingByUserIDsRequest) (*ListOnboardingByUserIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOnboardingByUserIDs not implemented")
}
func (UnimplementedOnboardingServer) mustEmbedUnimplementedOnboardingServer() {}
func (UnimplementedOnboardingServer) testEmbeddedByValue()                    {}

// UnsafeOnboardingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnboardingServer will
// result in compilation errors.
type UnsafeOnboardingServer interface {
	mustEmbedUnimplementedOnboardingServer()
}

func RegisterOnboardingServer(s grpc.ServiceRegistrar, srv OnboardingServer) {
	// If the following call pancis, it indicates UnimplementedOnboardingServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Onboarding_ServiceDesc, srv)
}

func _Onboarding_GetClientPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientPermissions(ctx, req.(*GetClientPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetClientEkycProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientEkycProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientEkycProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientEkycProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientEkycProfile(ctx, req.(*GetClientEkycProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ResetClientEkycNfc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetClientEkycNfcRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ResetClientEkycNfc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ResetClientEkycNfc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ResetClientEkycNfc(ctx, req.(*ResetClientEkycNfcRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_RegisterClientOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterClientOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).RegisterClientOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_RegisterClientOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).RegisterClientOnboarding(ctx, req.(*RegisterClientOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetClientOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientOnboarding(ctx, req.(*GetClientOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ListClientOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClientOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ListClientOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ListClientOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ListClientOnboarding(ctx, req.(*ListClientOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_SubmitClientApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitClientApplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).SubmitClientApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_SubmitClientApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).SubmitClientApplication(ctx, req.(*SubmitClientApplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_SubmitClientWetSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitClientWetSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).SubmitClientWetSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_SubmitClientWetSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).SubmitClientWetSign(ctx, req.(*SubmitClientWetSignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_SubmitClientFaceChallenge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitClientFaceChallengeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).SubmitClientFaceChallenge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_SubmitClientFaceChallenge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).SubmitClientFaceChallenge(ctx, req.(*SubmitClientFaceChallengeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_RequestClientOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestClientOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).RequestClientOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_RequestClientOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).RequestClientOTP(ctx, req.(*RequestClientOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_VerifyClientOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyClientOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).VerifyClientOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_VerifyClientOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).VerifyClientOTP(ctx, req.(*VerifyClientOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetClientResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientResources(ctx, req.(*GetClientResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetClientContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientContract(ctx, req.(*GetClientContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetClientRejection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientRejectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetClientRejection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetClientRejection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetClientRejection(ctx, req.(*GetClientRejectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ReinitClientOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReinitClientOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ReinitClientOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ReinitClientOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ReinitClientOnboarding(ctx, req.(*ReinitClientOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_LinkingClientAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkingClientAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).LinkingClientAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_LinkingClientAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).LinkingClientAccount(ctx, req.(*LinkingClientAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_QueryOnboardingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryOnboardingStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).QueryOnboardingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_QueryOnboardingStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).QueryOnboardingStatus(ctx, req.(*QueryOnboardingStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ListOnboardingByUserIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOnboardingByUserIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ListOnboardingByUserIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ListOnboardingByUserIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ListOnboardingByUserIDs(ctx, req.(*ListOnboardingByUserIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Onboarding_ServiceDesc is the grpc.ServiceDesc for Onboarding service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Onboarding_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "onboarding_service.v1.Onboarding",
	HandlerType: (*OnboardingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetClientPermissions",
			Handler:    _Onboarding_GetClientPermissions_Handler,
		},
		{
			MethodName: "GetClientEkycProfile",
			Handler:    _Onboarding_GetClientEkycProfile_Handler,
		},
		{
			MethodName: "ResetClientEkycNfc",
			Handler:    _Onboarding_ResetClientEkycNfc_Handler,
		},
		{
			MethodName: "RegisterClientOnboarding",
			Handler:    _Onboarding_RegisterClientOnboarding_Handler,
		},
		{
			MethodName: "GetClientOnboarding",
			Handler:    _Onboarding_GetClientOnboarding_Handler,
		},
		{
			MethodName: "ListClientOnboarding",
			Handler:    _Onboarding_ListClientOnboarding_Handler,
		},
		{
			MethodName: "SubmitClientApplication",
			Handler:    _Onboarding_SubmitClientApplication_Handler,
		},
		{
			MethodName: "SubmitClientWetSign",
			Handler:    _Onboarding_SubmitClientWetSign_Handler,
		},
		{
			MethodName: "SubmitClientFaceChallenge",
			Handler:    _Onboarding_SubmitClientFaceChallenge_Handler,
		},
		{
			MethodName: "RequestClientOTP",
			Handler:    _Onboarding_RequestClientOTP_Handler,
		},
		{
			MethodName: "VerifyClientOTP",
			Handler:    _Onboarding_VerifyClientOTP_Handler,
		},
		{
			MethodName: "GetClientResources",
			Handler:    _Onboarding_GetClientResources_Handler,
		},
		{
			MethodName: "GetClientContract",
			Handler:    _Onboarding_GetClientContract_Handler,
		},
		{
			MethodName: "GetClientRejection",
			Handler:    _Onboarding_GetClientRejection_Handler,
		},
		{
			MethodName: "ReinitClientOnboarding",
			Handler:    _Onboarding_ReinitClientOnboarding_Handler,
		},
		{
			MethodName: "LinkingClientAccount",
			Handler:    _Onboarding_LinkingClientAccount_Handler,
		},
		{
			MethodName: "QueryOnboardingStatus",
			Handler:    _Onboarding_QueryOnboardingStatus_Handler,
		},
		{
			MethodName: "ListOnboardingByUserIDs",
			Handler:    _Onboarding_ListOnboardingByUserIDs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "onboarding_service/v1/onboarding_service.proto",
}
