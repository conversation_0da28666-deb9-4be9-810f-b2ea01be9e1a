// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: onboarding_service/v1/operation_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TriggerSyncOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncOnboardingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingRequestMultiError, or nil if none found.
func (m *TriggerSyncOnboardingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := TriggerSyncOnboardingRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := TriggerSyncOnboardingRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TriggerSyncOnboardingRequestMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncOnboardingRequest.ValidateAll() if
// the designated constraints aren't met.
type TriggerSyncOnboardingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingRequestMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingRequestValidationError is the validation error returned
// by TriggerSyncOnboardingRequest.Validate if the designated constraints
// aren't met.
type TriggerSyncOnboardingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingRequestValidationError) ErrorName() string {
	return "TriggerSyncOnboardingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingRequestValidationError{}

// Validate checks the field values on TriggerSyncOnboardingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncOnboardingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingResponseMultiError, or nil if none found.
func (m *TriggerSyncOnboardingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSyncOnboardingResponseMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncOnboardingResponse.ValidateAll()
// if the designated constraints aren't met.
type TriggerSyncOnboardingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingResponseMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingResponseValidationError is the validation error
// returned by TriggerSyncOnboardingResponse.Validate if the designated
// constraints aren't met.
type TriggerSyncOnboardingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingResponseValidationError) ErrorName() string {
	return "TriggerSyncOnboardingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingResponseValidationError{}

// Validate checks the field values on TriggerSyncOnboardingJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncOnboardingJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingJobRequestMultiError, or nil if none found.
func (m *TriggerSyncOnboardingJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := TriggerSyncOnboardingJobRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := TriggerSyncOnboardingJobRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TriggerSyncOnboardingJobRequestMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingJobRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncOnboardingJobRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerSyncOnboardingJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingJobRequestMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingJobRequestValidationError is the validation error
// returned by TriggerSyncOnboardingJobRequest.Validate if the designated
// constraints aren't met.
type TriggerSyncOnboardingJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingJobRequestValidationError) ErrorName() string {
	return "TriggerSyncOnboardingJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingJobRequestValidationError{}

// Validate checks the field values on TriggerSyncOnboardingJobResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerSyncOnboardingJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingJobResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingJobResponseMultiError, or nil if none found.
func (m *TriggerSyncOnboardingJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSyncOnboardingJobResponseMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingJobResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerSyncOnboardingJobResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerSyncOnboardingJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingJobResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingJobResponseMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingJobResponseValidationError is the validation error
// returned by TriggerSyncOnboardingJobResponse.Validate if the designated
// constraints aren't met.
type TriggerSyncOnboardingJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingJobResponseValidationError) ErrorName() string {
	return "TriggerSyncOnboardingJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingJobResponseValidationError{}

// Validate checks the field values on TriggerSubmitSelfieJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSubmitSelfieJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSubmitSelfieJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSubmitSelfieJobRequestMultiError, or nil if none found.
func (m *TriggerSubmitSelfieJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSubmitSelfieJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := TriggerSubmitSelfieJobRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := TriggerSubmitSelfieJobRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TriggerSubmitSelfieJobRequestMultiError(errors)
	}

	return nil
}

// TriggerSubmitSelfieJobRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerSubmitSelfieJobRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerSubmitSelfieJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSubmitSelfieJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSubmitSelfieJobRequestMultiError) AllErrors() []error { return m }

// TriggerSubmitSelfieJobRequestValidationError is the validation error
// returned by TriggerSubmitSelfieJobRequest.Validate if the designated
// constraints aren't met.
type TriggerSubmitSelfieJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSubmitSelfieJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSubmitSelfieJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSubmitSelfieJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSubmitSelfieJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSubmitSelfieJobRequestValidationError) ErrorName() string {
	return "TriggerSubmitSelfieJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSubmitSelfieJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSubmitSelfieJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSubmitSelfieJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSubmitSelfieJobRequestValidationError{}

// Validate checks the field values on TriggerSubmitSelfieJobResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSubmitSelfieJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSubmitSelfieJobResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSubmitSelfieJobResponseMultiError, or nil if none found.
func (m *TriggerSubmitSelfieJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSubmitSelfieJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSubmitSelfieJobResponseMultiError(errors)
	}

	return nil
}

// TriggerSubmitSelfieJobResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerSubmitSelfieJobResponse.ValidateAll()
// if the designated constraints aren't met.
type TriggerSubmitSelfieJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSubmitSelfieJobResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSubmitSelfieJobResponseMultiError) AllErrors() []error { return m }

// TriggerSubmitSelfieJobResponseValidationError is the validation error
// returned by TriggerSubmitSelfieJobResponse.Validate if the designated
// constraints aren't met.
type TriggerSubmitSelfieJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSubmitSelfieJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSubmitSelfieJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSubmitSelfieJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSubmitSelfieJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSubmitSelfieJobResponseValidationError) ErrorName() string {
	return "TriggerSubmitSelfieJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSubmitSelfieJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSubmitSelfieJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSubmitSelfieJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSubmitSelfieJobResponseValidationError{}

// Validate checks the field values on TriggerSyncOnboardingsJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerSyncOnboardingsJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingsJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingsJobRequestMultiError, or nil if none found.
func (m *TriggerSyncOnboardingsJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingsJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetData()) < 1 {
		err := TriggerSyncOnboardingsJobRequestValidationError{
			field:  "Data",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TriggerSyncOnboardingsJobRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TriggerSyncOnboardingsJobRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TriggerSyncOnboardingsJobRequestValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TriggerSyncOnboardingsJobRequestMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingsJobRequestMultiError is an error wrapping multiple
// validation errors returned by
// TriggerSyncOnboardingsJobRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerSyncOnboardingsJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingsJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingsJobRequestMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingsJobRequestValidationError is the validation error
// returned by TriggerSyncOnboardingsJobRequest.Validate if the designated
// constraints aren't met.
type TriggerSyncOnboardingsJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingsJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingsJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingsJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingsJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingsJobRequestValidationError) ErrorName() string {
	return "TriggerSyncOnboardingsJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingsJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingsJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingsJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingsJobRequestValidationError{}

// Validate checks the field values on TriggerSyncOnboardingsJobResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerSyncOnboardingsJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncOnboardingsJobResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TriggerSyncOnboardingsJobResponseMultiError, or nil if none found.
func (m *TriggerSyncOnboardingsJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncOnboardingsJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSyncOnboardingsJobResponseMultiError(errors)
	}

	return nil
}

// TriggerSyncOnboardingsJobResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerSyncOnboardingsJobResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerSyncOnboardingsJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncOnboardingsJobResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncOnboardingsJobResponseMultiError) AllErrors() []error { return m }

// TriggerSyncOnboardingsJobResponseValidationError is the validation error
// returned by TriggerSyncOnboardingsJobResponse.Validate if the designated
// constraints aren't met.
type TriggerSyncOnboardingsJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncOnboardingsJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncOnboardingsJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncOnboardingsJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncOnboardingsJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncOnboardingsJobResponseValidationError) ErrorName() string {
	return "TriggerSyncOnboardingsJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncOnboardingsJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncOnboardingsJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncOnboardingsJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncOnboardingsJobResponseValidationError{}

// Validate checks the field values on TriggerContractSigningJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerContractSigningJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerContractSigningJobRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerContractSigningJobRequestMultiError, or nil if none found.
func (m *TriggerContractSigningJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerContractSigningJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := TriggerContractSigningJobRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := TriggerContractSigningJobRequestValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TriggerContractSigningJobRequestMultiError(errors)
	}

	return nil
}

// TriggerContractSigningJobRequestMultiError is an error wrapping multiple
// validation errors returned by
// TriggerContractSigningJobRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerContractSigningJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerContractSigningJobRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerContractSigningJobRequestMultiError) AllErrors() []error { return m }

// TriggerContractSigningJobRequestValidationError is the validation error
// returned by TriggerContractSigningJobRequest.Validate if the designated
// constraints aren't met.
type TriggerContractSigningJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerContractSigningJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerContractSigningJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerContractSigningJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerContractSigningJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerContractSigningJobRequestValidationError) ErrorName() string {
	return "TriggerContractSigningJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerContractSigningJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerContractSigningJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerContractSigningJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerContractSigningJobRequestValidationError{}

// Validate checks the field values on TriggerContractSigningJobResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TriggerContractSigningJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerContractSigningJobResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// TriggerContractSigningJobResponseMultiError, or nil if none found.
func (m *TriggerContractSigningJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerContractSigningJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerContractSigningJobResponseMultiError(errors)
	}

	return nil
}

// TriggerContractSigningJobResponseMultiError is an error wrapping multiple
// validation errors returned by
// TriggerContractSigningJobResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerContractSigningJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerContractSigningJobResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerContractSigningJobResponseMultiError) AllErrors() []error { return m }

// TriggerContractSigningJobResponseValidationError is the validation error
// returned by TriggerContractSigningJobResponse.Validate if the designated
// constraints aren't met.
type TriggerContractSigningJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerContractSigningJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerContractSigningJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerContractSigningJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerContractSigningJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerContractSigningJobResponseValidationError) ErrorName() string {
	return "TriggerContractSigningJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerContractSigningJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerContractSigningJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerContractSigningJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerContractSigningJobResponseValidationError{}

// Validate checks the field values on OnboardingOp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OnboardingOp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingOp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OnboardingOpMultiError, or
// nil if none found.
func (m *OnboardingOp) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingOp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := OnboardingOpValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOnboardingId() <= 0 {
		err := OnboardingOpValidationError{
			field:  "OnboardingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return OnboardingOpMultiError(errors)
	}

	return nil
}

// OnboardingOpMultiError is an error wrapping multiple validation errors
// returned by OnboardingOp.ValidateAll() if the designated constraints aren't met.
type OnboardingOpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingOpMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingOpMultiError) AllErrors() []error { return m }

// OnboardingOpValidationError is the validation error returned by
// OnboardingOp.Validate if the designated constraints aren't met.
type OnboardingOpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingOpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingOpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingOpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingOpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingOpValidationError) ErrorName() string { return "OnboardingOpValidationError" }

// Error satisfies the builtin error interface
func (e OnboardingOpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingOp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingOpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingOpValidationError{}
