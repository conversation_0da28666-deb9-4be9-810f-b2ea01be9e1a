syntax = "proto3";

package management_service.v1;

option go_package = "installment/api/management-service/v1;v1";

import "validate/validate.proto";

service Operation {
  rpc SetMaintenanceState(SetMaintenanceStateRequest) returns (SetMaintenanceStateResponse);
  rpc GetMaintenanceState(GetMaintenanceStateRequest) returns (GetMaintenanceStateResponse);
  rpc MockStatementPeriod(MockStatementPeriodRequest) returns (MockStatementPeriodResponse);
  rpc TriggerSyncStatements(TriggerSyncStatementsRequest) returns (TriggerSyncStatementsResponse);
  rpc TriggerSyncInstallments(TriggerSyncInstallmentsRequest) returns (TriggerSyncInstallmentsResponse) {}
}

message MockStatementPeriodRequest {
  string statement_date = 1;
  string statement_date_overdue = 2;
}

message MockStatementPeriodResponse {
}

message TriggerSyncInstallmentsRequest {
}

message TriggerSyncInstallmentsResponse {
}


message TriggerSyncStatementsRequest{
  oneof sync_type {
    ManualSync manual_sync = 1;
    ScheduledSync scheduled_sync = 2;
  }
}

message TriggerSyncStatementsResponse{
  string status = 1;
  string message = 2;
  string sync_id = 3;
}


message ManualSync {
  // Currently, we only support sync all
}

message ScheduledSync {
  enum SyncOption {
    SYNC_ALL = 0;
    SYNC_FAILED = 1;
  }
  SyncOption sync_option = 1;
  optional string statement_date = 2;
}

message SetMaintenanceStateRequest {
  string partner_code = 1 [(validate.rules).string.min_len = 1];
  MaintenanceState state = 2 [(validate.rules).message.required = true];
}

message SetMaintenanceStateResponse {
}

message GetMaintenanceStateRequest {
  string partner_code = 1 [(validate.rules).string.min_len = 1];
}

message GetMaintenanceStateResponse {
  string partner_code = 1;
  MaintenanceState state = 2;
}

message MaintenanceState {
  bool all = 1;
  bool onboarding = 2;
  bool purchase = 3;
  bool repayment = 4;
}