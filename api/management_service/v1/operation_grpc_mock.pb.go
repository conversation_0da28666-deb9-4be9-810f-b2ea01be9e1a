// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: management_service/v1/operation.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockOperationClient is a mock of OperationClient interface.
type MockOperationClient struct {
	ctrl     *gomock.Controller
	recorder *MockOperationClientMockRecorder
}

// MockOperationClientMockRecorder is the mock recorder for MockOperationClient.
type MockOperationClientMockRecorder struct {
	mock *MockOperationClient
}

// NewMockOperationClient creates a new mock instance.
func NewMockOperationClient(ctrl *gomock.Controller) *MockOperationClient {
	mock := &MockOperationClient{ctrl: ctrl}
	mock.recorder = &MockOperationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOperationClient) EXPECT() *MockOperationClientMockRecorder {
	return m.recorder
}

// GetMaintenanceState mocks base method.
func (m *MockOperationClient) GetMaintenanceState(ctx context.Context, in *GetMaintenanceStateRequest, opts ...grpc.CallOption) (*GetMaintenanceStateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMaintenanceState", varargs...)
	ret0, _ := ret[0].(*GetMaintenanceStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaintenanceState indicates an expected call of GetMaintenanceState.
func (mr *MockOperationClientMockRecorder) GetMaintenanceState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaintenanceState", reflect.TypeOf((*MockOperationClient)(nil).GetMaintenanceState), varargs...)
}

// MockStatementPeriod mocks base method.
func (m *MockOperationClient) MockStatementPeriod(ctx context.Context, in *MockStatementPeriodRequest, opts ...grpc.CallOption) (*MockStatementPeriodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MockStatementPeriod", varargs...)
	ret0, _ := ret[0].(*MockStatementPeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MockStatementPeriod indicates an expected call of MockStatementPeriod.
func (mr *MockOperationClientMockRecorder) MockStatementPeriod(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MockStatementPeriod", reflect.TypeOf((*MockOperationClient)(nil).MockStatementPeriod), varargs...)
}

// SetMaintenanceState mocks base method.
func (m *MockOperationClient) SetMaintenanceState(ctx context.Context, in *SetMaintenanceStateRequest, opts ...grpc.CallOption) (*SetMaintenanceStateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMaintenanceState", varargs...)
	ret0, _ := ret[0].(*SetMaintenanceStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMaintenanceState indicates an expected call of SetMaintenanceState.
func (mr *MockOperationClientMockRecorder) SetMaintenanceState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMaintenanceState", reflect.TypeOf((*MockOperationClient)(nil).SetMaintenanceState), varargs...)
}

// TriggerSyncInstallments mocks base method.
func (m *MockOperationClient) TriggerSyncInstallments(ctx context.Context, in *TriggerSyncInstallmentsRequest, opts ...grpc.CallOption) (*TriggerSyncInstallmentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSyncInstallments", varargs...)
	ret0, _ := ret[0].(*TriggerSyncInstallmentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncInstallments indicates an expected call of TriggerSyncInstallments.
func (mr *MockOperationClientMockRecorder) TriggerSyncInstallments(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncInstallments", reflect.TypeOf((*MockOperationClient)(nil).TriggerSyncInstallments), varargs...)
}

// TriggerSyncStatements mocks base method.
func (m *MockOperationClient) TriggerSyncStatements(ctx context.Context, in *TriggerSyncStatementsRequest, opts ...grpc.CallOption) (*TriggerSyncStatementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerSyncStatements", varargs...)
	ret0, _ := ret[0].(*TriggerSyncStatementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncStatements indicates an expected call of TriggerSyncStatements.
func (mr *MockOperationClientMockRecorder) TriggerSyncStatements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncStatements", reflect.TypeOf((*MockOperationClient)(nil).TriggerSyncStatements), varargs...)
}

// MockOperationServer is a mock of OperationServer interface.
type MockOperationServer struct {
	ctrl     *gomock.Controller
	recorder *MockOperationServerMockRecorder
}

// MockOperationServerMockRecorder is the mock recorder for MockOperationServer.
type MockOperationServerMockRecorder struct {
	mock *MockOperationServer
}

// NewMockOperationServer creates a new mock instance.
func NewMockOperationServer(ctrl *gomock.Controller) *MockOperationServer {
	mock := &MockOperationServer{ctrl: ctrl}
	mock.recorder = &MockOperationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOperationServer) EXPECT() *MockOperationServerMockRecorder {
	return m.recorder
}

// GetMaintenanceState mocks base method.
func (m *MockOperationServer) GetMaintenanceState(ctx context.Context, in *GetMaintenanceStateRequest) (*GetMaintenanceStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaintenanceState", ctx, in)
	ret0, _ := ret[0].(*GetMaintenanceStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaintenanceState indicates an expected call of GetMaintenanceState.
func (mr *MockOperationServerMockRecorder) GetMaintenanceState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaintenanceState", reflect.TypeOf((*MockOperationServer)(nil).GetMaintenanceState), ctx, in)
}

// MockStatementPeriod mocks base method.
func (m *MockOperationServer) MockStatementPeriod(ctx context.Context, in *MockStatementPeriodRequest) (*MockStatementPeriodResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MockStatementPeriod", ctx, in)
	ret0, _ := ret[0].(*MockStatementPeriodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MockStatementPeriod indicates an expected call of MockStatementPeriod.
func (mr *MockOperationServerMockRecorder) MockStatementPeriod(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MockStatementPeriod", reflect.TypeOf((*MockOperationServer)(nil).MockStatementPeriod), ctx, in)
}

// SetMaintenanceState mocks base method.
func (m *MockOperationServer) SetMaintenanceState(ctx context.Context, in *SetMaintenanceStateRequest) (*SetMaintenanceStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMaintenanceState", ctx, in)
	ret0, _ := ret[0].(*SetMaintenanceStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMaintenanceState indicates an expected call of SetMaintenanceState.
func (mr *MockOperationServerMockRecorder) SetMaintenanceState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMaintenanceState", reflect.TypeOf((*MockOperationServer)(nil).SetMaintenanceState), ctx, in)
}

// TriggerSyncInstallments mocks base method.
func (m *MockOperationServer) TriggerSyncInstallments(ctx context.Context, in *TriggerSyncInstallmentsRequest) (*TriggerSyncInstallmentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSyncInstallments", ctx, in)
	ret0, _ := ret[0].(*TriggerSyncInstallmentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncInstallments indicates an expected call of TriggerSyncInstallments.
func (mr *MockOperationServerMockRecorder) TriggerSyncInstallments(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncInstallments", reflect.TypeOf((*MockOperationServer)(nil).TriggerSyncInstallments), ctx, in)
}

// TriggerSyncStatements mocks base method.
func (m *MockOperationServer) TriggerSyncStatements(ctx context.Context, in *TriggerSyncStatementsRequest) (*TriggerSyncStatementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerSyncStatements", ctx, in)
	ret0, _ := ret[0].(*TriggerSyncStatementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerSyncStatements indicates an expected call of TriggerSyncStatements.
func (mr *MockOperationServerMockRecorder) TriggerSyncStatements(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerSyncStatements", reflect.TypeOf((*MockOperationServer)(nil).TriggerSyncStatements), ctx, in)
}
