// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: management_service/v1/operation.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MockStatementPeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MockStatementPeriodRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MockStatementPeriodRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MockStatementPeriodRequestMultiError, or nil if none found.
func (m *MockStatementPeriodRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MockStatementPeriodRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StatementDate

	// no validation rules for StatementDateOverdue

	if len(errors) > 0 {
		return MockStatementPeriodRequestMultiError(errors)
	}

	return nil
}

// MockStatementPeriodRequestMultiError is an error wrapping multiple
// validation errors returned by MockStatementPeriodRequest.ValidateAll() if
// the designated constraints aren't met.
type MockStatementPeriodRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MockStatementPeriodRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MockStatementPeriodRequestMultiError) AllErrors() []error { return m }

// MockStatementPeriodRequestValidationError is the validation error returned
// by MockStatementPeriodRequest.Validate if the designated constraints aren't met.
type MockStatementPeriodRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MockStatementPeriodRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MockStatementPeriodRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MockStatementPeriodRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MockStatementPeriodRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MockStatementPeriodRequestValidationError) ErrorName() string {
	return "MockStatementPeriodRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MockStatementPeriodRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMockStatementPeriodRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MockStatementPeriodRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MockStatementPeriodRequestValidationError{}

// Validate checks the field values on MockStatementPeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MockStatementPeriodResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MockStatementPeriodResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MockStatementPeriodResponseMultiError, or nil if none found.
func (m *MockStatementPeriodResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MockStatementPeriodResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MockStatementPeriodResponseMultiError(errors)
	}

	return nil
}

// MockStatementPeriodResponseMultiError is an error wrapping multiple
// validation errors returned by MockStatementPeriodResponse.ValidateAll() if
// the designated constraints aren't met.
type MockStatementPeriodResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MockStatementPeriodResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MockStatementPeriodResponseMultiError) AllErrors() []error { return m }

// MockStatementPeriodResponseValidationError is the validation error returned
// by MockStatementPeriodResponse.Validate if the designated constraints
// aren't met.
type MockStatementPeriodResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MockStatementPeriodResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MockStatementPeriodResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MockStatementPeriodResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MockStatementPeriodResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MockStatementPeriodResponseValidationError) ErrorName() string {
	return "MockStatementPeriodResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MockStatementPeriodResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMockStatementPeriodResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MockStatementPeriodResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MockStatementPeriodResponseValidationError{}

// Validate checks the field values on TriggerSyncInstallmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncInstallmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncInstallmentsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncInstallmentsRequestMultiError, or nil if none found.
func (m *TriggerSyncInstallmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncInstallmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSyncInstallmentsRequestMultiError(errors)
	}

	return nil
}

// TriggerSyncInstallmentsRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncInstallmentsRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerSyncInstallmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncInstallmentsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncInstallmentsRequestMultiError) AllErrors() []error { return m }

// TriggerSyncInstallmentsRequestValidationError is the validation error
// returned by TriggerSyncInstallmentsRequest.Validate if the designated
// constraints aren't met.
type TriggerSyncInstallmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncInstallmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncInstallmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncInstallmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncInstallmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncInstallmentsRequestValidationError) ErrorName() string {
	return "TriggerSyncInstallmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncInstallmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncInstallmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncInstallmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncInstallmentsRequestValidationError{}

// Validate checks the field values on TriggerSyncInstallmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncInstallmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncInstallmentsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncInstallmentsResponseMultiError, or nil if none found.
func (m *TriggerSyncInstallmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncInstallmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerSyncInstallmentsResponseMultiError(errors)
	}

	return nil
}

// TriggerSyncInstallmentsResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncInstallmentsResponse.ValidateAll()
// if the designated constraints aren't met.
type TriggerSyncInstallmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncInstallmentsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncInstallmentsResponseMultiError) AllErrors() []error { return m }

// TriggerSyncInstallmentsResponseValidationError is the validation error
// returned by TriggerSyncInstallmentsResponse.Validate if the designated
// constraints aren't met.
type TriggerSyncInstallmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncInstallmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncInstallmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncInstallmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncInstallmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncInstallmentsResponseValidationError) ErrorName() string {
	return "TriggerSyncInstallmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncInstallmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncInstallmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncInstallmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncInstallmentsResponseValidationError{}

// Validate checks the field values on TriggerSyncStatementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncStatementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncStatementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TriggerSyncStatementsRequestMultiError, or nil if none found.
func (m *TriggerSyncStatementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncStatementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.SyncType.(type) {
	case *TriggerSyncStatementsRequest_ManualSync:
		if v == nil {
			err := TriggerSyncStatementsRequestValidationError{
				field:  "SyncType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetManualSync()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TriggerSyncStatementsRequestValidationError{
						field:  "ManualSync",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TriggerSyncStatementsRequestValidationError{
						field:  "ManualSync",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetManualSync()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TriggerSyncStatementsRequestValidationError{
					field:  "ManualSync",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TriggerSyncStatementsRequest_ScheduledSync:
		if v == nil {
			err := TriggerSyncStatementsRequestValidationError{
				field:  "SyncType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScheduledSync()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TriggerSyncStatementsRequestValidationError{
						field:  "ScheduledSync",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TriggerSyncStatementsRequestValidationError{
						field:  "ScheduledSync",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScheduledSync()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TriggerSyncStatementsRequestValidationError{
					field:  "ScheduledSync",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TriggerSyncStatementsRequestMultiError(errors)
	}

	return nil
}

// TriggerSyncStatementsRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncStatementsRequest.ValidateAll() if
// the designated constraints aren't met.
type TriggerSyncStatementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncStatementsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncStatementsRequestMultiError) AllErrors() []error { return m }

// TriggerSyncStatementsRequestValidationError is the validation error returned
// by TriggerSyncStatementsRequest.Validate if the designated constraints
// aren't met.
type TriggerSyncStatementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncStatementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncStatementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncStatementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncStatementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncStatementsRequestValidationError) ErrorName() string {
	return "TriggerSyncStatementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncStatementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncStatementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncStatementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncStatementsRequestValidationError{}

// Validate checks the field values on TriggerSyncStatementsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerSyncStatementsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerSyncStatementsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerSyncStatementsResponseMultiError, or nil if none found.
func (m *TriggerSyncStatementsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerSyncStatementsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for SyncId

	if len(errors) > 0 {
		return TriggerSyncStatementsResponseMultiError(errors)
	}

	return nil
}

// TriggerSyncStatementsResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerSyncStatementsResponse.ValidateAll()
// if the designated constraints aren't met.
type TriggerSyncStatementsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerSyncStatementsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerSyncStatementsResponseMultiError) AllErrors() []error { return m }

// TriggerSyncStatementsResponseValidationError is the validation error
// returned by TriggerSyncStatementsResponse.Validate if the designated
// constraints aren't met.
type TriggerSyncStatementsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerSyncStatementsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerSyncStatementsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerSyncStatementsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerSyncStatementsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerSyncStatementsResponseValidationError) ErrorName() string {
	return "TriggerSyncStatementsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerSyncStatementsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerSyncStatementsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerSyncStatementsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerSyncStatementsResponseValidationError{}

// Validate checks the field values on ManualSync with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ManualSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualSync with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ManualSyncMultiError, or
// nil if none found.
func (m *ManualSync) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ManualSyncMultiError(errors)
	}

	return nil
}

// ManualSyncMultiError is an error wrapping multiple validation errors
// returned by ManualSync.ValidateAll() if the designated constraints aren't met.
type ManualSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualSyncMultiError) AllErrors() []error { return m }

// ManualSyncValidationError is the validation error returned by
// ManualSync.Validate if the designated constraints aren't met.
type ManualSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualSyncValidationError) ErrorName() string { return "ManualSyncValidationError" }

// Error satisfies the builtin error interface
func (e ManualSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualSyncValidationError{}

// Validate checks the field values on ScheduledSync with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScheduledSync) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScheduledSync with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScheduledSyncMultiError, or
// nil if none found.
func (m *ScheduledSync) ValidateAll() error {
	return m.validate(true)
}

func (m *ScheduledSync) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SyncOption

	if m.StatementDate != nil {
		// no validation rules for StatementDate
	}

	if len(errors) > 0 {
		return ScheduledSyncMultiError(errors)
	}

	return nil
}

// ScheduledSyncMultiError is an error wrapping multiple validation errors
// returned by ScheduledSync.ValidateAll() if the designated constraints
// aren't met.
type ScheduledSyncMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScheduledSyncMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScheduledSyncMultiError) AllErrors() []error { return m }

// ScheduledSyncValidationError is the validation error returned by
// ScheduledSync.Validate if the designated constraints aren't met.
type ScheduledSyncValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScheduledSyncValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScheduledSyncValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScheduledSyncValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScheduledSyncValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScheduledSyncValidationError) ErrorName() string { return "ScheduledSyncValidationError" }

// Error satisfies the builtin error interface
func (e ScheduledSyncValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScheduledSync.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScheduledSyncValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScheduledSyncValidationError{}

// Validate checks the field values on SetMaintenanceStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetMaintenanceStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetMaintenanceStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetMaintenanceStateRequestMultiError, or nil if none found.
func (m *SetMaintenanceStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetMaintenanceStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := SetMaintenanceStateRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetState() == nil {
		err := SetMaintenanceStateRequestValidationError{
			field:  "State",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetMaintenanceStateRequestValidationError{
					field:  "State",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetMaintenanceStateRequestValidationError{
					field:  "State",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetMaintenanceStateRequestValidationError{
				field:  "State",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetMaintenanceStateRequestMultiError(errors)
	}

	return nil
}

// SetMaintenanceStateRequestMultiError is an error wrapping multiple
// validation errors returned by SetMaintenanceStateRequest.ValidateAll() if
// the designated constraints aren't met.
type SetMaintenanceStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetMaintenanceStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetMaintenanceStateRequestMultiError) AllErrors() []error { return m }

// SetMaintenanceStateRequestValidationError is the validation error returned
// by SetMaintenanceStateRequest.Validate if the designated constraints aren't met.
type SetMaintenanceStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetMaintenanceStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetMaintenanceStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetMaintenanceStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetMaintenanceStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetMaintenanceStateRequestValidationError) ErrorName() string {
	return "SetMaintenanceStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetMaintenanceStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetMaintenanceStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetMaintenanceStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetMaintenanceStateRequestValidationError{}

// Validate checks the field values on SetMaintenanceStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetMaintenanceStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetMaintenanceStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetMaintenanceStateResponseMultiError, or nil if none found.
func (m *SetMaintenanceStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetMaintenanceStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SetMaintenanceStateResponseMultiError(errors)
	}

	return nil
}

// SetMaintenanceStateResponseMultiError is an error wrapping multiple
// validation errors returned by SetMaintenanceStateResponse.ValidateAll() if
// the designated constraints aren't met.
type SetMaintenanceStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetMaintenanceStateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetMaintenanceStateResponseMultiError) AllErrors() []error { return m }

// SetMaintenanceStateResponseValidationError is the validation error returned
// by SetMaintenanceStateResponse.Validate if the designated constraints
// aren't met.
type SetMaintenanceStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetMaintenanceStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetMaintenanceStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetMaintenanceStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetMaintenanceStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetMaintenanceStateResponseValidationError) ErrorName() string {
	return "SetMaintenanceStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetMaintenanceStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetMaintenanceStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetMaintenanceStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetMaintenanceStateResponseValidationError{}

// Validate checks the field values on GetMaintenanceStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaintenanceStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaintenanceStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaintenanceStateRequestMultiError, or nil if none found.
func (m *GetMaintenanceStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaintenanceStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := GetMaintenanceStateRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMaintenanceStateRequestMultiError(errors)
	}

	return nil
}

// GetMaintenanceStateRequestMultiError is an error wrapping multiple
// validation errors returned by GetMaintenanceStateRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMaintenanceStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaintenanceStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaintenanceStateRequestMultiError) AllErrors() []error { return m }

// GetMaintenanceStateRequestValidationError is the validation error returned
// by GetMaintenanceStateRequest.Validate if the designated constraints aren't met.
type GetMaintenanceStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaintenanceStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaintenanceStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaintenanceStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaintenanceStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaintenanceStateRequestValidationError) ErrorName() string {
	return "GetMaintenanceStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaintenanceStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaintenanceStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaintenanceStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaintenanceStateRequestValidationError{}

// Validate checks the field values on GetMaintenanceStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMaintenanceStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMaintenanceStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMaintenanceStateResponseMultiError, or nil if none found.
func (m *GetMaintenanceStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMaintenanceStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PartnerCode

	if all {
		switch v := interface{}(m.GetState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMaintenanceStateResponseValidationError{
					field:  "State",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMaintenanceStateResponseValidationError{
					field:  "State",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMaintenanceStateResponseValidationError{
				field:  "State",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMaintenanceStateResponseMultiError(errors)
	}

	return nil
}

// GetMaintenanceStateResponseMultiError is an error wrapping multiple
// validation errors returned by GetMaintenanceStateResponse.ValidateAll() if
// the designated constraints aren't met.
type GetMaintenanceStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMaintenanceStateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMaintenanceStateResponseMultiError) AllErrors() []error { return m }

// GetMaintenanceStateResponseValidationError is the validation error returned
// by GetMaintenanceStateResponse.Validate if the designated constraints
// aren't met.
type GetMaintenanceStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMaintenanceStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMaintenanceStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMaintenanceStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMaintenanceStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMaintenanceStateResponseValidationError) ErrorName() string {
	return "GetMaintenanceStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMaintenanceStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMaintenanceStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMaintenanceStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMaintenanceStateResponseValidationError{}

// Validate checks the field values on MaintenanceState with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MaintenanceState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MaintenanceState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MaintenanceStateMultiError, or nil if none found.
func (m *MaintenanceState) ValidateAll() error {
	return m.validate(true)
}

func (m *MaintenanceState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for All

	// no validation rules for Onboarding

	// no validation rules for Purchase

	// no validation rules for Repayment

	if len(errors) > 0 {
		return MaintenanceStateMultiError(errors)
	}

	return nil
}

// MaintenanceStateMultiError is an error wrapping multiple validation errors
// returned by MaintenanceState.ValidateAll() if the designated constraints
// aren't met.
type MaintenanceStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MaintenanceStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MaintenanceStateMultiError) AllErrors() []error { return m }

// MaintenanceStateValidationError is the validation error returned by
// MaintenanceState.Validate if the designated constraints aren't met.
type MaintenanceStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MaintenanceStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MaintenanceStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MaintenanceStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MaintenanceStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MaintenanceStateValidationError) ErrorName() string { return "MaintenanceStateValidationError" }

// Error satisfies the builtin error interface
func (e MaintenanceStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMaintenanceState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MaintenanceStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MaintenanceStateValidationError{}
