// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: management_service/v1/management.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetOutstandingInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutstandingInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutstandingInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutstandingInfoRequestMultiError, or nil if none found.
func (m *GetOutstandingInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutstandingInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetOutstandingInfoRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAccountId() <= 0 {
		err := GetOutstandingInfoRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetOutstandingInfoRequestMultiError(errors)
	}

	return nil
}

// GetOutstandingInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetOutstandingInfoRequest.ValidateAll() if the
// designated constraints aren't met.
type GetOutstandingInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutstandingInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutstandingInfoRequestMultiError) AllErrors() []error { return m }

// GetOutstandingInfoRequestValidationError is the validation error returned by
// GetOutstandingInfoRequest.Validate if the designated constraints aren't met.
type GetOutstandingInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutstandingInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutstandingInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutstandingInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutstandingInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutstandingInfoRequestValidationError) ErrorName() string {
	return "GetOutstandingInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutstandingInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutstandingInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutstandingInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutstandingInfoRequestValidationError{}

// Validate checks the field values on GetOutstandingInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutstandingInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutstandingInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutstandingInfoResponseMultiError, or nil if none found.
func (m *GetOutstandingInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutstandingInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalOutstanding

	// no validation rules for TotalDueAmount

	// no validation rules for TotalDueRepaid

	// no validation rules for TotalDuePenalty

	if m.DueCreatedAt != nil {

		if all {
			switch v := interface{}(m.GetDueCreatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOutstandingInfoResponseValidationError{
						field:  "DueCreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOutstandingInfoResponseValidationError{
						field:  "DueCreatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDueCreatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOutstandingInfoResponseValidationError{
					field:  "DueCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.DueUpdatedAt != nil {

		if all {
			switch v := interface{}(m.GetDueUpdatedAt()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOutstandingInfoResponseValidationError{
						field:  "DueUpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOutstandingInfoResponseValidationError{
						field:  "DueUpdatedAt",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDueUpdatedAt()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOutstandingInfoResponseValidationError{
					field:  "DueUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetOutstandingInfoResponseMultiError(errors)
	}

	return nil
}

// GetOutstandingInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetOutstandingInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetOutstandingInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutstandingInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutstandingInfoResponseMultiError) AllErrors() []error { return m }

// GetOutstandingInfoResponseValidationError is the validation error returned
// by GetOutstandingInfoResponse.Validate if the designated constraints aren't met.
type GetOutstandingInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutstandingInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutstandingInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutstandingInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutstandingInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutstandingInfoResponseValidationError) ErrorName() string {
	return "GetOutstandingInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutstandingInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutstandingInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutstandingInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutstandingInfoResponseValidationError{}
