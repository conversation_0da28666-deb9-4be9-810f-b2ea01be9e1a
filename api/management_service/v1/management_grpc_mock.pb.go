// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: management_service/v1/management.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockManagementClient is a mock of ManagementClient interface.
type MockManagementClient struct {
	ctrl     *gomock.Controller
	recorder *MockManagementClientMockRecorder
}

// MockManagementClientMockRecorder is the mock recorder for MockManagementClient.
type MockManagementClientMockRecorder struct {
	mock *MockManagementClient
}

// NewMockManagementClient creates a new mock instance.
func NewMockManagementClient(ctrl *gomock.Controller) *MockManagementClient {
	mock := &MockManagementClient{ctrl: ctrl}
	mock.recorder = &MockManagementClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManagementClient) EXPECT() *MockManagementClientMockRecorder {
	return m.recorder
}

// GetOutstandingInfo mocks base method.
func (m *MockManagementClient) GetOutstandingInfo(ctx context.Context, in *GetOutstandingInfoRequest, opts ...grpc.CallOption) (*GetOutstandingInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOutstandingInfo", varargs...)
	ret0, _ := ret[0].(*GetOutstandingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOutstandingInfo indicates an expected call of GetOutstandingInfo.
func (mr *MockManagementClientMockRecorder) GetOutstandingInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOutstandingInfo", reflect.TypeOf((*MockManagementClient)(nil).GetOutstandingInfo), varargs...)
}

// MockManagementServer is a mock of ManagementServer interface.
type MockManagementServer struct {
	ctrl     *gomock.Controller
	recorder *MockManagementServerMockRecorder
}

// MockManagementServerMockRecorder is the mock recorder for MockManagementServer.
type MockManagementServerMockRecorder struct {
	mock *MockManagementServer
}

// NewMockManagementServer creates a new mock instance.
func NewMockManagementServer(ctrl *gomock.Controller) *MockManagementServer {
	mock := &MockManagementServer{ctrl: ctrl}
	mock.recorder = &MockManagementServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManagementServer) EXPECT() *MockManagementServerMockRecorder {
	return m.recorder
}

// GetOutstandingInfo mocks base method.
func (m *MockManagementServer) GetOutstandingInfo(ctx context.Context, in *GetOutstandingInfoRequest) (*GetOutstandingInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOutstandingInfo", ctx, in)
	ret0, _ := ret[0].(*GetOutstandingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOutstandingInfo indicates an expected call of GetOutstandingInfo.
func (mr *MockManagementServerMockRecorder) GetOutstandingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOutstandingInfo", reflect.TypeOf((*MockManagementServer)(nil).GetOutstandingInfo), ctx, in)
}
