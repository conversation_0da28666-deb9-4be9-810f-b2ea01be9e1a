// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: management_service/v1/operation.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScheduledSync_SyncOption int32

const (
	ScheduledSync_SYNC_ALL    ScheduledSync_SyncOption = 0
	ScheduledSync_SYNC_FAILED ScheduledSync_SyncOption = 1
)

// Enum value maps for ScheduledSync_SyncOption.
var (
	ScheduledSync_SyncOption_name = map[int32]string{
		0: "SYNC_ALL",
		1: "SYNC_FAILED",
	}
	ScheduledSync_SyncOption_value = map[string]int32{
		"SYNC_ALL":    0,
		"SYNC_FAILED": 1,
	}
)

func (x ScheduledSync_SyncOption) Enum() *ScheduledSync_SyncOption {
	p := new(ScheduledSync_SyncOption)
	*p = x
	return p
}

func (x ScheduledSync_SyncOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScheduledSync_SyncOption) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_v1_operation_proto_enumTypes[0].Descriptor()
}

func (ScheduledSync_SyncOption) Type() protoreflect.EnumType {
	return &file_management_service_v1_operation_proto_enumTypes[0]
}

func (x ScheduledSync_SyncOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScheduledSync_SyncOption.Descriptor instead.
func (ScheduledSync_SyncOption) EnumDescriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{7, 0}
}

type MockStatementPeriodRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	StatementDate        string                 `protobuf:"bytes,1,opt,name=statement_date,json=statementDate,proto3" json:"statement_date,omitempty"`
	StatementDateOverdue string                 `protobuf:"bytes,2,opt,name=statement_date_overdue,json=statementDateOverdue,proto3" json:"statement_date_overdue,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *MockStatementPeriodRequest) Reset() {
	*x = MockStatementPeriodRequest{}
	mi := &file_management_service_v1_operation_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MockStatementPeriodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockStatementPeriodRequest) ProtoMessage() {}

func (x *MockStatementPeriodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockStatementPeriodRequest.ProtoReflect.Descriptor instead.
func (*MockStatementPeriodRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{0}
}

func (x *MockStatementPeriodRequest) GetStatementDate() string {
	if x != nil {
		return x.StatementDate
	}
	return ""
}

func (x *MockStatementPeriodRequest) GetStatementDateOverdue() string {
	if x != nil {
		return x.StatementDateOverdue
	}
	return ""
}

type MockStatementPeriodResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MockStatementPeriodResponse) Reset() {
	*x = MockStatementPeriodResponse{}
	mi := &file_management_service_v1_operation_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MockStatementPeriodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockStatementPeriodResponse) ProtoMessage() {}

func (x *MockStatementPeriodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockStatementPeriodResponse.ProtoReflect.Descriptor instead.
func (*MockStatementPeriodResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{1}
}

type TriggerSyncInstallmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncInstallmentsRequest) Reset() {
	*x = TriggerSyncInstallmentsRequest{}
	mi := &file_management_service_v1_operation_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncInstallmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncInstallmentsRequest) ProtoMessage() {}

func (x *TriggerSyncInstallmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncInstallmentsRequest.ProtoReflect.Descriptor instead.
func (*TriggerSyncInstallmentsRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{2}
}

type TriggerSyncInstallmentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncInstallmentsResponse) Reset() {
	*x = TriggerSyncInstallmentsResponse{}
	mi := &file_management_service_v1_operation_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncInstallmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncInstallmentsResponse) ProtoMessage() {}

func (x *TriggerSyncInstallmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncInstallmentsResponse.ProtoReflect.Descriptor instead.
func (*TriggerSyncInstallmentsResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{3}
}

type TriggerSyncStatementsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to SyncType:
	//
	//	*TriggerSyncStatementsRequest_ManualSync
	//	*TriggerSyncStatementsRequest_ScheduledSync
	SyncType      isTriggerSyncStatementsRequest_SyncType `protobuf_oneof:"sync_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncStatementsRequest) Reset() {
	*x = TriggerSyncStatementsRequest{}
	mi := &file_management_service_v1_operation_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncStatementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncStatementsRequest) ProtoMessage() {}

func (x *TriggerSyncStatementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncStatementsRequest.ProtoReflect.Descriptor instead.
func (*TriggerSyncStatementsRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{4}
}

func (x *TriggerSyncStatementsRequest) GetSyncType() isTriggerSyncStatementsRequest_SyncType {
	if x != nil {
		return x.SyncType
	}
	return nil
}

func (x *TriggerSyncStatementsRequest) GetManualSync() *ManualSync {
	if x != nil {
		if x, ok := x.SyncType.(*TriggerSyncStatementsRequest_ManualSync); ok {
			return x.ManualSync
		}
	}
	return nil
}

func (x *TriggerSyncStatementsRequest) GetScheduledSync() *ScheduledSync {
	if x != nil {
		if x, ok := x.SyncType.(*TriggerSyncStatementsRequest_ScheduledSync); ok {
			return x.ScheduledSync
		}
	}
	return nil
}

type isTriggerSyncStatementsRequest_SyncType interface {
	isTriggerSyncStatementsRequest_SyncType()
}

type TriggerSyncStatementsRequest_ManualSync struct {
	ManualSync *ManualSync `protobuf:"bytes,1,opt,name=manual_sync,json=manualSync,proto3,oneof"`
}

type TriggerSyncStatementsRequest_ScheduledSync struct {
	ScheduledSync *ScheduledSync `protobuf:"bytes,2,opt,name=scheduled_sync,json=scheduledSync,proto3,oneof"`
}

func (*TriggerSyncStatementsRequest_ManualSync) isTriggerSyncStatementsRequest_SyncType() {}

func (*TriggerSyncStatementsRequest_ScheduledSync) isTriggerSyncStatementsRequest_SyncType() {}

type TriggerSyncStatementsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	SyncId        string                 `protobuf:"bytes,3,opt,name=sync_id,json=syncId,proto3" json:"sync_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TriggerSyncStatementsResponse) Reset() {
	*x = TriggerSyncStatementsResponse{}
	mi := &file_management_service_v1_operation_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TriggerSyncStatementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerSyncStatementsResponse) ProtoMessage() {}

func (x *TriggerSyncStatementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerSyncStatementsResponse.ProtoReflect.Descriptor instead.
func (*TriggerSyncStatementsResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{5}
}

func (x *TriggerSyncStatementsResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TriggerSyncStatementsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TriggerSyncStatementsResponse) GetSyncId() string {
	if x != nil {
		return x.SyncId
	}
	return ""
}

type ManualSync struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManualSync) Reset() {
	*x = ManualSync{}
	mi := &file_management_service_v1_operation_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManualSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualSync) ProtoMessage() {}

func (x *ManualSync) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualSync.ProtoReflect.Descriptor instead.
func (*ManualSync) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{6}
}

type ScheduledSync struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	SyncOption    ScheduledSync_SyncOption `protobuf:"varint,1,opt,name=sync_option,json=syncOption,proto3,enum=management_service.v1.ScheduledSync_SyncOption" json:"sync_option,omitempty"`
	StatementDate *string                  `protobuf:"bytes,2,opt,name=statement_date,json=statementDate,proto3,oneof" json:"statement_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScheduledSync) Reset() {
	*x = ScheduledSync{}
	mi := &file_management_service_v1_operation_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduledSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduledSync) ProtoMessage() {}

func (x *ScheduledSync) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduledSync.ProtoReflect.Descriptor instead.
func (*ScheduledSync) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{7}
}

func (x *ScheduledSync) GetSyncOption() ScheduledSync_SyncOption {
	if x != nil {
		return x.SyncOption
	}
	return ScheduledSync_SYNC_ALL
}

func (x *ScheduledSync) GetStatementDate() string {
	if x != nil && x.StatementDate != nil {
		return *x.StatementDate
	}
	return ""
}

type SetMaintenanceStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	State         *MaintenanceState      `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMaintenanceStateRequest) Reset() {
	*x = SetMaintenanceStateRequest{}
	mi := &file_management_service_v1_operation_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMaintenanceStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaintenanceStateRequest) ProtoMessage() {}

func (x *SetMaintenanceStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaintenanceStateRequest.ProtoReflect.Descriptor instead.
func (*SetMaintenanceStateRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{8}
}

func (x *SetMaintenanceStateRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *SetMaintenanceStateRequest) GetState() *MaintenanceState {
	if x != nil {
		return x.State
	}
	return nil
}

type SetMaintenanceStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMaintenanceStateResponse) Reset() {
	*x = SetMaintenanceStateResponse{}
	mi := &file_management_service_v1_operation_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMaintenanceStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaintenanceStateResponse) ProtoMessage() {}

func (x *SetMaintenanceStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaintenanceStateResponse.ProtoReflect.Descriptor instead.
func (*SetMaintenanceStateResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{9}
}

type GetMaintenanceStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMaintenanceStateRequest) Reset() {
	*x = GetMaintenanceStateRequest{}
	mi := &file_management_service_v1_operation_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaintenanceStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintenanceStateRequest) ProtoMessage() {}

func (x *GetMaintenanceStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintenanceStateRequest.ProtoReflect.Descriptor instead.
func (*GetMaintenanceStateRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{10}
}

func (x *GetMaintenanceStateRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

type GetMaintenanceStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	State         *MaintenanceState      `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMaintenanceStateResponse) Reset() {
	*x = GetMaintenanceStateResponse{}
	mi := &file_management_service_v1_operation_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMaintenanceStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaintenanceStateResponse) ProtoMessage() {}

func (x *GetMaintenanceStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaintenanceStateResponse.ProtoReflect.Descriptor instead.
func (*GetMaintenanceStateResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{11}
}

func (x *GetMaintenanceStateResponse) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *GetMaintenanceStateResponse) GetState() *MaintenanceState {
	if x != nil {
		return x.State
	}
	return nil
}

type MaintenanceState struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	All           bool                   `protobuf:"varint,1,opt,name=all,proto3" json:"all,omitempty"`
	Onboarding    bool                   `protobuf:"varint,2,opt,name=onboarding,proto3" json:"onboarding,omitempty"`
	Purchase      bool                   `protobuf:"varint,3,opt,name=purchase,proto3" json:"purchase,omitempty"`
	Repayment     bool                   `protobuf:"varint,4,opt,name=repayment,proto3" json:"repayment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MaintenanceState) Reset() {
	*x = MaintenanceState{}
	mi := &file_management_service_v1_operation_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaintenanceState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintenanceState) ProtoMessage() {}

func (x *MaintenanceState) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_operation_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintenanceState.ProtoReflect.Descriptor instead.
func (*MaintenanceState) Descriptor() ([]byte, []int) {
	return file_management_service_v1_operation_proto_rawDescGZIP(), []int{12}
}

func (x *MaintenanceState) GetAll() bool {
	if x != nil {
		return x.All
	}
	return false
}

func (x *MaintenanceState) GetOnboarding() bool {
	if x != nil {
		return x.Onboarding
	}
	return false
}

func (x *MaintenanceState) GetPurchase() bool {
	if x != nil {
		return x.Purchase
	}
	return false
}

func (x *MaintenanceState) GetRepayment() bool {
	if x != nil {
		return x.Repayment
	}
	return false
}

var File_management_service_v1_operation_proto protoreflect.FileDescriptor

const file_management_service_v1_operation_proto_rawDesc = "" +
	"\n" +
	"%management_service/v1/operation.proto\x12\x15management_service.v1\x1a\x17validate/validate.proto\"y\n" +
	"\x1aMockStatementPeriodRequest\x12%\n" +
	"\x0estatement_date\x18\x01 \x01(\tR\rstatementDate\x124\n" +
	"\x16statement_date_overdue\x18\x02 \x01(\tR\x14statementDateOverdue\"\x1d\n" +
	"\x1bMockStatementPeriodResponse\" \n" +
	"\x1eTriggerSyncInstallmentsRequest\"!\n" +
	"\x1fTriggerSyncInstallmentsResponse\"\xc0\x01\n" +
	"\x1cTriggerSyncStatementsRequest\x12D\n" +
	"\vmanual_sync\x18\x01 \x01(\v2!.management_service.v1.ManualSyncH\x00R\n" +
	"manualSync\x12M\n" +
	"\x0escheduled_sync\x18\x02 \x01(\v2$.management_service.v1.ScheduledSyncH\x00R\rscheduledSyncB\v\n" +
	"\tsync_type\"j\n" +
	"\x1dTriggerSyncStatementsResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x17\n" +
	"\async_id\x18\x03 \x01(\tR\x06syncId\"\f\n" +
	"\n" +
	"ManualSync\"\xcd\x01\n" +
	"\rScheduledSync\x12P\n" +
	"\vsync_option\x18\x01 \x01(\x0e2/.management_service.v1.ScheduledSync.SyncOptionR\n" +
	"syncOption\x12*\n" +
	"\x0estatement_date\x18\x02 \x01(\tH\x00R\rstatementDate\x88\x01\x01\"+\n" +
	"\n" +
	"SyncOption\x12\f\n" +
	"\bSYNC_ALL\x10\x00\x12\x0f\n" +
	"\vSYNC_FAILED\x10\x01B\x11\n" +
	"\x0f_statement_date\"\x91\x01\n" +
	"\x1aSetMaintenanceStateRequest\x12*\n" +
	"\fpartner_code\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\x12G\n" +
	"\x05state\x18\x02 \x01(\v2'.management_service.v1.MaintenanceStateB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x05state\"\x1d\n" +
	"\x1bSetMaintenanceStateResponse\"H\n" +
	"\x1aGetMaintenanceStateRequest\x12*\n" +
	"\fpartner_code\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\vpartnerCode\"\x7f\n" +
	"\x1bGetMaintenanceStateResponse\x12!\n" +
	"\fpartner_code\x18\x01 \x01(\tR\vpartnerCode\x12=\n" +
	"\x05state\x18\x02 \x01(\v2'.management_service.v1.MaintenanceStateR\x05state\"~\n" +
	"\x10MaintenanceState\x12\x10\n" +
	"\x03all\x18\x01 \x01(\bR\x03all\x12\x1e\n" +
	"\n" +
	"onboarding\x18\x02 \x01(\bR\n" +
	"onboarding\x12\x1a\n" +
	"\bpurchase\x18\x03 \x01(\bR\bpurchase\x12\x1c\n" +
	"\trepayment\x18\x04 \x01(\bR\trepayment2\x97\x05\n" +
	"\tOperation\x12|\n" +
	"\x13SetMaintenanceState\x121.management_service.v1.SetMaintenanceStateRequest\x1a2.management_service.v1.SetMaintenanceStateResponse\x12|\n" +
	"\x13GetMaintenanceState\x121.management_service.v1.GetMaintenanceStateRequest\x1a2.management_service.v1.GetMaintenanceStateResponse\x12|\n" +
	"\x13MockStatementPeriod\x121.management_service.v1.MockStatementPeriodRequest\x1a2.management_service.v1.MockStatementPeriodResponse\x12\x82\x01\n" +
	"\x15TriggerSyncStatements\x123.management_service.v1.TriggerSyncStatementsRequest\x1a4.management_service.v1.TriggerSyncStatementsResponse\x12\x8a\x01\n" +
	"\x17TriggerSyncInstallments\x125.management_service.v1.TriggerSyncInstallmentsRequest\x1a6.management_service.v1.TriggerSyncInstallmentsResponse\"\x00B*Z(installment/api/management-service/v1;v1b\x06proto3"

var (
	file_management_service_v1_operation_proto_rawDescOnce sync.Once
	file_management_service_v1_operation_proto_rawDescData []byte
)

func file_management_service_v1_operation_proto_rawDescGZIP() []byte {
	file_management_service_v1_operation_proto_rawDescOnce.Do(func() {
		file_management_service_v1_operation_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_management_service_v1_operation_proto_rawDesc), len(file_management_service_v1_operation_proto_rawDesc)))
	})
	return file_management_service_v1_operation_proto_rawDescData
}

var file_management_service_v1_operation_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_management_service_v1_operation_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_management_service_v1_operation_proto_goTypes = []any{
	(ScheduledSync_SyncOption)(0),           // 0: management_service.v1.ScheduledSync.SyncOption
	(*MockStatementPeriodRequest)(nil),      // 1: management_service.v1.MockStatementPeriodRequest
	(*MockStatementPeriodResponse)(nil),     // 2: management_service.v1.MockStatementPeriodResponse
	(*TriggerSyncInstallmentsRequest)(nil),  // 3: management_service.v1.TriggerSyncInstallmentsRequest
	(*TriggerSyncInstallmentsResponse)(nil), // 4: management_service.v1.TriggerSyncInstallmentsResponse
	(*TriggerSyncStatementsRequest)(nil),    // 5: management_service.v1.TriggerSyncStatementsRequest
	(*TriggerSyncStatementsResponse)(nil),   // 6: management_service.v1.TriggerSyncStatementsResponse
	(*ManualSync)(nil),                      // 7: management_service.v1.ManualSync
	(*ScheduledSync)(nil),                   // 8: management_service.v1.ScheduledSync
	(*SetMaintenanceStateRequest)(nil),      // 9: management_service.v1.SetMaintenanceStateRequest
	(*SetMaintenanceStateResponse)(nil),     // 10: management_service.v1.SetMaintenanceStateResponse
	(*GetMaintenanceStateRequest)(nil),      // 11: management_service.v1.GetMaintenanceStateRequest
	(*GetMaintenanceStateResponse)(nil),     // 12: management_service.v1.GetMaintenanceStateResponse
	(*MaintenanceState)(nil),                // 13: management_service.v1.MaintenanceState
}
var file_management_service_v1_operation_proto_depIdxs = []int32{
	7,  // 0: management_service.v1.TriggerSyncStatementsRequest.manual_sync:type_name -> management_service.v1.ManualSync
	8,  // 1: management_service.v1.TriggerSyncStatementsRequest.scheduled_sync:type_name -> management_service.v1.ScheduledSync
	0,  // 2: management_service.v1.ScheduledSync.sync_option:type_name -> management_service.v1.ScheduledSync.SyncOption
	13, // 3: management_service.v1.SetMaintenanceStateRequest.state:type_name -> management_service.v1.MaintenanceState
	13, // 4: management_service.v1.GetMaintenanceStateResponse.state:type_name -> management_service.v1.MaintenanceState
	9,  // 5: management_service.v1.Operation.SetMaintenanceState:input_type -> management_service.v1.SetMaintenanceStateRequest
	11, // 6: management_service.v1.Operation.GetMaintenanceState:input_type -> management_service.v1.GetMaintenanceStateRequest
	1,  // 7: management_service.v1.Operation.MockStatementPeriod:input_type -> management_service.v1.MockStatementPeriodRequest
	5,  // 8: management_service.v1.Operation.TriggerSyncStatements:input_type -> management_service.v1.TriggerSyncStatementsRequest
	3,  // 9: management_service.v1.Operation.TriggerSyncInstallments:input_type -> management_service.v1.TriggerSyncInstallmentsRequest
	10, // 10: management_service.v1.Operation.SetMaintenanceState:output_type -> management_service.v1.SetMaintenanceStateResponse
	12, // 11: management_service.v1.Operation.GetMaintenanceState:output_type -> management_service.v1.GetMaintenanceStateResponse
	2,  // 12: management_service.v1.Operation.MockStatementPeriod:output_type -> management_service.v1.MockStatementPeriodResponse
	6,  // 13: management_service.v1.Operation.TriggerSyncStatements:output_type -> management_service.v1.TriggerSyncStatementsResponse
	4,  // 14: management_service.v1.Operation.TriggerSyncInstallments:output_type -> management_service.v1.TriggerSyncInstallmentsResponse
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_management_service_v1_operation_proto_init() }
func file_management_service_v1_operation_proto_init() {
	if File_management_service_v1_operation_proto != nil {
		return
	}
	file_management_service_v1_operation_proto_msgTypes[4].OneofWrappers = []any{
		(*TriggerSyncStatementsRequest_ManualSync)(nil),
		(*TriggerSyncStatementsRequest_ScheduledSync)(nil),
	}
	file_management_service_v1_operation_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_management_service_v1_operation_proto_rawDesc), len(file_management_service_v1_operation_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_management_service_v1_operation_proto_goTypes,
		DependencyIndexes: file_management_service_v1_operation_proto_depIdxs,
		EnumInfos:         file_management_service_v1_operation_proto_enumTypes,
		MessageInfos:      file_management_service_v1_operation_proto_msgTypes,
	}.Build()
	File_management_service_v1_operation_proto = out.File
	file_management_service_v1_operation_proto_goTypes = nil
	file_management_service_v1_operation_proto_depIdxs = nil
}
