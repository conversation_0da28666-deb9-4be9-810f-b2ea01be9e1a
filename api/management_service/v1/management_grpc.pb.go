// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: management_service/v1/management.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Management_GetOutstandingInfo_FullMethodName = "/management_service.v1.Management/GetOutstandingInfo"
)

// ManagementClient is the client API for Management service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ManagementClient interface {
	GetOutstandingInfo(ctx context.Context, in *GetOutstandingInfoRequest, opts ...grpc.CallOption) (*GetOutstandingInfoResponse, error)
}

type managementClient struct {
	cc grpc.ClientConnInterface
}

func NewManagementClient(cc grpc.ClientConnInterface) ManagementClient {
	return &managementClient{cc}
}

func (c *managementClient) GetOutstandingInfo(ctx context.Context, in *GetOutstandingInfoRequest, opts ...grpc.CallOption) (*GetOutstandingInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOutstandingInfoResponse)
	err := c.cc.Invoke(ctx, Management_GetOutstandingInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ManagementServer is the server API for Management service.
// All implementations must embed UnimplementedManagementServer
// for forward compatibility.
type ManagementServer interface {
	GetOutstandingInfo(context.Context, *GetOutstandingInfoRequest) (*GetOutstandingInfoResponse, error)
	mustEmbedUnimplementedManagementServer()
}

// UnimplementedManagementServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedManagementServer struct{}

func (UnimplementedManagementServer) GetOutstandingInfo(context.Context, *GetOutstandingInfoRequest) (*GetOutstandingInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOutstandingInfo not implemented")
}
func (UnimplementedManagementServer) mustEmbedUnimplementedManagementServer() {}
func (UnimplementedManagementServer) testEmbeddedByValue()                    {}

// UnsafeManagementServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ManagementServer will
// result in compilation errors.
type UnsafeManagementServer interface {
	mustEmbedUnimplementedManagementServer()
}

func RegisterManagementServer(s grpc.ServiceRegistrar, srv ManagementServer) {
	// If the following call pancis, it indicates UnimplementedManagementServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Management_ServiceDesc, srv)
}

func _Management_GetOutstandingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOutstandingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagementServer).GetOutstandingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Management_GetOutstandingInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagementServer).GetOutstandingInfo(ctx, req.(*GetOutstandingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Management_ServiceDesc is the grpc.ServiceDesc for Management service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Management_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "management_service.v1.Management",
	HandlerType: (*ManagementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOutstandingInfo",
			Handler:    _Management_GetOutstandingInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "management_service/v1/management.proto",
}
