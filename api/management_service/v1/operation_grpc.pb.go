// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: management_service/v1/operation.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Operation_SetMaintenanceState_FullMethodName     = "/management_service.v1.Operation/SetMaintenanceState"
	Operation_GetMaintenanceState_FullMethodName     = "/management_service.v1.Operation/GetMaintenanceState"
	Operation_MockStatementPeriod_FullMethodName     = "/management_service.v1.Operation/MockStatementPeriod"
	Operation_TriggerSyncStatements_FullMethodName   = "/management_service.v1.Operation/TriggerSyncStatements"
	Operation_TriggerSyncInstallments_FullMethodName = "/management_service.v1.Operation/TriggerSyncInstallments"
)

// OperationClient is the client API for Operation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OperationClient interface {
	SetMaintenanceState(ctx context.Context, in *SetMaintenanceStateRequest, opts ...grpc.CallOption) (*SetMaintenanceStateResponse, error)
	GetMaintenanceState(ctx context.Context, in *GetMaintenanceStateRequest, opts ...grpc.CallOption) (*GetMaintenanceStateResponse, error)
	MockStatementPeriod(ctx context.Context, in *MockStatementPeriodRequest, opts ...grpc.CallOption) (*MockStatementPeriodResponse, error)
	TriggerSyncStatements(ctx context.Context, in *TriggerSyncStatementsRequest, opts ...grpc.CallOption) (*TriggerSyncStatementsResponse, error)
	TriggerSyncInstallments(ctx context.Context, in *TriggerSyncInstallmentsRequest, opts ...grpc.CallOption) (*TriggerSyncInstallmentsResponse, error)
}

type operationClient struct {
	cc grpc.ClientConnInterface
}

func NewOperationClient(cc grpc.ClientConnInterface) OperationClient {
	return &operationClient{cc}
}

func (c *operationClient) SetMaintenanceState(ctx context.Context, in *SetMaintenanceStateRequest, opts ...grpc.CallOption) (*SetMaintenanceStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetMaintenanceStateResponse)
	err := c.cc.Invoke(ctx, Operation_SetMaintenanceState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) GetMaintenanceState(ctx context.Context, in *GetMaintenanceStateRequest, opts ...grpc.CallOption) (*GetMaintenanceStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMaintenanceStateResponse)
	err := c.cc.Invoke(ctx, Operation_GetMaintenanceState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) MockStatementPeriod(ctx context.Context, in *MockStatementPeriodRequest, opts ...grpc.CallOption) (*MockStatementPeriodResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MockStatementPeriodResponse)
	err := c.cc.Invoke(ctx, Operation_MockStatementPeriod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) TriggerSyncStatements(ctx context.Context, in *TriggerSyncStatementsRequest, opts ...grpc.CallOption) (*TriggerSyncStatementsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSyncStatementsResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSyncStatements_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationClient) TriggerSyncInstallments(ctx context.Context, in *TriggerSyncInstallmentsRequest, opts ...grpc.CallOption) (*TriggerSyncInstallmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerSyncInstallmentsResponse)
	err := c.cc.Invoke(ctx, Operation_TriggerSyncInstallments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OperationServer is the server API for Operation service.
// All implementations must embed UnimplementedOperationServer
// for forward compatibility.
type OperationServer interface {
	SetMaintenanceState(context.Context, *SetMaintenanceStateRequest) (*SetMaintenanceStateResponse, error)
	GetMaintenanceState(context.Context, *GetMaintenanceStateRequest) (*GetMaintenanceStateResponse, error)
	MockStatementPeriod(context.Context, *MockStatementPeriodRequest) (*MockStatementPeriodResponse, error)
	TriggerSyncStatements(context.Context, *TriggerSyncStatementsRequest) (*TriggerSyncStatementsResponse, error)
	TriggerSyncInstallments(context.Context, *TriggerSyncInstallmentsRequest) (*TriggerSyncInstallmentsResponse, error)
	mustEmbedUnimplementedOperationServer()
}

// UnimplementedOperationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOperationServer struct{}

func (UnimplementedOperationServer) SetMaintenanceState(context.Context, *SetMaintenanceStateRequest) (*SetMaintenanceStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMaintenanceState not implemented")
}
func (UnimplementedOperationServer) GetMaintenanceState(context.Context, *GetMaintenanceStateRequest) (*GetMaintenanceStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaintenanceState not implemented")
}
func (UnimplementedOperationServer) MockStatementPeriod(context.Context, *MockStatementPeriodRequest) (*MockStatementPeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MockStatementPeriod not implemented")
}
func (UnimplementedOperationServer) TriggerSyncStatements(context.Context, *TriggerSyncStatementsRequest) (*TriggerSyncStatementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSyncStatements not implemented")
}
func (UnimplementedOperationServer) TriggerSyncInstallments(context.Context, *TriggerSyncInstallmentsRequest) (*TriggerSyncInstallmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerSyncInstallments not implemented")
}
func (UnimplementedOperationServer) mustEmbedUnimplementedOperationServer() {}
func (UnimplementedOperationServer) testEmbeddedByValue()                   {}

// UnsafeOperationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OperationServer will
// result in compilation errors.
type UnsafeOperationServer interface {
	mustEmbedUnimplementedOperationServer()
}

func RegisterOperationServer(s grpc.ServiceRegistrar, srv OperationServer) {
	// If the following call pancis, it indicates UnimplementedOperationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Operation_ServiceDesc, srv)
}

func _Operation_SetMaintenanceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMaintenanceStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).SetMaintenanceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_SetMaintenanceState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).SetMaintenanceState(ctx, req.(*SetMaintenanceStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_GetMaintenanceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaintenanceStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).GetMaintenanceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_GetMaintenanceState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).GetMaintenanceState(ctx, req.(*GetMaintenanceStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_MockStatementPeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MockStatementPeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).MockStatementPeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_MockStatementPeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).MockStatementPeriod(ctx, req.(*MockStatementPeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerSyncStatements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSyncStatementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSyncStatements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSyncStatements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSyncStatements(ctx, req.(*TriggerSyncStatementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Operation_TriggerSyncInstallments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerSyncInstallmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationServer).TriggerSyncInstallments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Operation_TriggerSyncInstallments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationServer).TriggerSyncInstallments(ctx, req.(*TriggerSyncInstallmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Operation_ServiceDesc is the grpc.ServiceDesc for Operation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Operation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "management_service.v1.Operation",
	HandlerType: (*OperationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetMaintenanceState",
			Handler:    _Operation_SetMaintenanceState_Handler,
		},
		{
			MethodName: "GetMaintenanceState",
			Handler:    _Operation_GetMaintenanceState_Handler,
		},
		{
			MethodName: "MockStatementPeriod",
			Handler:    _Operation_MockStatementPeriod_Handler,
		},
		{
			MethodName: "TriggerSyncStatements",
			Handler:    _Operation_TriggerSyncStatements_Handler,
		},
		{
			MethodName: "TriggerSyncInstallments",
			Handler:    _Operation_TriggerSyncInstallments_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "management_service/v1/operation.proto",
}
