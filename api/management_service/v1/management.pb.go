// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: management_service/v1/management.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetOutstandingInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZalopayId     int64                  `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	AccountId     int64                  `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOutstandingInfoRequest) Reset() {
	*x = GetOutstandingInfoRequest{}
	mi := &file_management_service_v1_management_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOutstandingInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOutstandingInfoRequest) ProtoMessage() {}

func (x *GetOutstandingInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_management_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOutstandingInfoRequest.ProtoReflect.Descriptor instead.
func (*GetOutstandingInfoRequest) Descriptor() ([]byte, []int) {
	return file_management_service_v1_management_proto_rawDescGZIP(), []int{0}
}

func (x *GetOutstandingInfoRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetOutstandingInfoRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

type GetOutstandingInfoResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TotalOutstanding int64                  `protobuf:"varint,1,opt,name=total_outstanding,json=totalOutstanding,proto3" json:"total_outstanding,omitempty"`
	TotalDueAmount   int64                  `protobuf:"varint,2,opt,name=total_due_amount,json=totalDueAmount,proto3" json:"total_due_amount,omitempty"`
	TotalDueRepaid   int64                  `protobuf:"varint,3,opt,name=total_due_repaid,json=totalDueRepaid,proto3" json:"total_due_repaid,omitempty"`
	TotalDuePenalty  int64                  `protobuf:"varint,4,opt,name=total_due_penalty,json=totalDuePenalty,proto3" json:"total_due_penalty,omitempty"`
	// Created at is the time when the outstanding due info is created
	DueCreatedAt *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=due_created_at,json=dueCreatedAt,proto3,oneof" json:"due_created_at,omitempty"`
	// Updated at is the time when the outstanding due info is updated
	DueUpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=due_updated_at,json=dueUpdatedAt,proto3,oneof" json:"due_updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOutstandingInfoResponse) Reset() {
	*x = GetOutstandingInfoResponse{}
	mi := &file_management_service_v1_management_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOutstandingInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOutstandingInfoResponse) ProtoMessage() {}

func (x *GetOutstandingInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_v1_management_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOutstandingInfoResponse.ProtoReflect.Descriptor instead.
func (*GetOutstandingInfoResponse) Descriptor() ([]byte, []int) {
	return file_management_service_v1_management_proto_rawDescGZIP(), []int{1}
}

func (x *GetOutstandingInfoResponse) GetTotalOutstanding() int64 {
	if x != nil {
		return x.TotalOutstanding
	}
	return 0
}

func (x *GetOutstandingInfoResponse) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *GetOutstandingInfoResponse) GetTotalDueRepaid() int64 {
	if x != nil {
		return x.TotalDueRepaid
	}
	return 0
}

func (x *GetOutstandingInfoResponse) GetTotalDuePenalty() int64 {
	if x != nil {
		return x.TotalDuePenalty
	}
	return 0
}

func (x *GetOutstandingInfoResponse) GetDueCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DueCreatedAt
	}
	return nil
}

func (x *GetOutstandingInfoResponse) GetDueUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DueUpdatedAt
	}
	return nil
}

var File_management_service_v1_management_proto protoreflect.FileDescriptor

const file_management_service_v1_management_proto_rawDesc = "" +
	"\n" +
	"&management_service/v1/management.proto\x12\x15management_service.v1\x1a\x17validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"k\n" +
	"\x19GetOutstandingInfoRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12&\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\taccountId\"\xfd\x02\n" +
	"\x1aGetOutstandingInfoResponse\x12+\n" +
	"\x11total_outstanding\x18\x01 \x01(\x03R\x10totalOutstanding\x12(\n" +
	"\x10total_due_amount\x18\x02 \x01(\x03R\x0etotalDueAmount\x12(\n" +
	"\x10total_due_repaid\x18\x03 \x01(\x03R\x0etotalDueRepaid\x12*\n" +
	"\x11total_due_penalty\x18\x04 \x01(\x03R\x0ftotalDuePenalty\x12E\n" +
	"\x0edue_created_at\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampH\x00R\fdueCreatedAt\x88\x01\x01\x12E\n" +
	"\x0edue_updated_at\x18\x1f \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fdueUpdatedAt\x88\x01\x01B\x11\n" +
	"\x0f_due_created_atB\x11\n" +
	"\x0f_due_updated_at2\x89\x01\n" +
	"\n" +
	"Management\x12{\n" +
	"\x12GetOutstandingInfo\x120.management_service.v1.GetOutstandingInfoRequest\x1a1.management_service.v1.GetOutstandingInfoResponse\"\x00B*Z(installment/api/management-service/v1;v1b\x06proto3"

var (
	file_management_service_v1_management_proto_rawDescOnce sync.Once
	file_management_service_v1_management_proto_rawDescData []byte
)

func file_management_service_v1_management_proto_rawDescGZIP() []byte {
	file_management_service_v1_management_proto_rawDescOnce.Do(func() {
		file_management_service_v1_management_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_management_service_v1_management_proto_rawDesc), len(file_management_service_v1_management_proto_rawDesc)))
	})
	return file_management_service_v1_management_proto_rawDescData
}

var file_management_service_v1_management_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_management_service_v1_management_proto_goTypes = []any{
	(*GetOutstandingInfoRequest)(nil),  // 0: management_service.v1.GetOutstandingInfoRequest
	(*GetOutstandingInfoResponse)(nil), // 1: management_service.v1.GetOutstandingInfoResponse
	(*timestamppb.Timestamp)(nil),      // 2: google.protobuf.Timestamp
}
var file_management_service_v1_management_proto_depIdxs = []int32{
	2, // 0: management_service.v1.GetOutstandingInfoResponse.due_created_at:type_name -> google.protobuf.Timestamp
	2, // 1: management_service.v1.GetOutstandingInfoResponse.due_updated_at:type_name -> google.protobuf.Timestamp
	0, // 2: management_service.v1.Management.GetOutstandingInfo:input_type -> management_service.v1.GetOutstandingInfoRequest
	1, // 3: management_service.v1.Management.GetOutstandingInfo:output_type -> management_service.v1.GetOutstandingInfoResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_management_service_v1_management_proto_init() }
func file_management_service_v1_management_proto_init() {
	if File_management_service_v1_management_proto != nil {
		return
	}
	file_management_service_v1_management_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_management_service_v1_management_proto_rawDesc), len(file_management_service_v1_management_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_management_service_v1_management_proto_goTypes,
		DependencyIndexes: file_management_service_v1_management_proto_depIdxs,
		MessageInfos:      file_management_service_v1_management_proto_msgTypes,
	}.Build()
	File_management_service_v1_management_proto = out.File
	file_management_service_v1_management_proto_goTypes = nil
	file_management_service_v1_management_proto_depIdxs = nil
}
