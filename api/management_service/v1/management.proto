syntax = "proto3";

package management_service.v1;

option go_package = "installment/api/management-service/v1;v1";

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

service Management {
    rpc GetOutstandingInfo(GetOutstandingInfoRequest) returns (GetOutstandingInfoResponse) {}
}

message GetOutstandingInfoRequest {
    int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
    int64 account_id = 2 [(validate.rules).int64.gt = 0];
}

message GetOutstandingInfoResponse {
    int64 total_outstanding = 1;
    int64 total_due_amount = 2;
    int64 total_due_repaid = 3;
    int64 total_due_penalty = 4;
    // Created at is the time when the outstanding due info is created
    optional google.protobuf.Timestamp due_created_at = 30;
    // Updated at is the time when the outstanding due info is updated
    optional google.protobuf.Timestamp due_updated_at = 31;
}