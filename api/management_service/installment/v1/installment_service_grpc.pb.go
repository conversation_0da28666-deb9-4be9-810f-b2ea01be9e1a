// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: management_service/installment/v1/installment_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Installment_ListClientEligiblePlans_FullMethodName = "/management_service.installment.v1.Installment/ListClientEligiblePlans"
	Installment_GetClientPlanDetail_FullMethodName     = "/management_service.installment.v1.Installment/GetClientPlanDetail"
	Installment_AcceptClientPlan_FullMethodName        = "/management_service.installment.v1.Installment/AcceptClientPlan"
	Installment_GetPaymentPlanDetail_FullMethodName    = "/management_service.installment.v1.Installment/GetPaymentPlanDetail"
	Installment_GetClientInstallment_FullMethodName    = "/management_service.installment.v1.Installment/GetClientInstallment"
	Installment_GetClientEarlyDischarge_FullMethodName = "/management_service.installment.v1.Installment/GetClientEarlyDischarge"
	Installment_GetInstallmentStatus_FullMethodName    = "/management_service.installment.v1.Installment/GetInstallmentStatus"
	Installment_GetEarlyDischargeRefund_FullMethodName = "/management_service.installment.v1.Installment/GetEarlyDischargeRefund"
	Installment_NotifyInstallmentRefund_FullMethodName = "/management_service.installment.v1.Installment/NotifyInstallmentRefund"
)

// InstallmentClient is the client API for Installment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type InstallmentClient interface {
	// PLan
	ListClientEligiblePlans(ctx context.Context, in *ListClientEligiblePlansRequest, opts ...grpc.CallOption) (*ListClientEligiblePlansResponse, error)
	GetClientPlanDetail(ctx context.Context, in *GetClientPlanDetailRequest, opts ...grpc.CallOption) (*GetClientPlanDetailResponse, error)
	AcceptClientPlan(ctx context.Context, in *AcceptClientPlanRequest, opts ...grpc.CallOption) (*AcceptClientPlanResponse, error)
	GetPaymentPlanDetail(ctx context.Context, in *GetPaymentPlanDetailRequest, opts ...grpc.CallOption) (*GetPaymentPlanDetailResponse, error)
	// Installment
	GetClientInstallment(ctx context.Context, in *GetClientInstallmentRequest, opts ...grpc.CallOption) (*GetClientInstallmentResponse, error)
	GetClientEarlyDischarge(ctx context.Context, in *GetClientEarlyDischargeRequest, opts ...grpc.CallOption) (*GetClientEarlyDischargeResponse, error)
	GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest, opts ...grpc.CallOption) (*GetInstallmentStatusResponse, error)
	GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest, opts ...grpc.CallOption) (*GetEarlyDischargeRefundResponse, error)
	NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund, opts ...grpc.CallOption) (*NotifyInstallmentRefundResponse, error)
}

type installmentClient struct {
	cc grpc.ClientConnInterface
}

func NewInstallmentClient(cc grpc.ClientConnInterface) InstallmentClient {
	return &installmentClient{cc}
}

func (c *installmentClient) ListClientEligiblePlans(ctx context.Context, in *ListClientEligiblePlansRequest, opts ...grpc.CallOption) (*ListClientEligiblePlansResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClientEligiblePlansResponse)
	err := c.cc.Invoke(ctx, Installment_ListClientEligiblePlans_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetClientPlanDetail(ctx context.Context, in *GetClientPlanDetailRequest, opts ...grpc.CallOption) (*GetClientPlanDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientPlanDetailResponse)
	err := c.cc.Invoke(ctx, Installment_GetClientPlanDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) AcceptClientPlan(ctx context.Context, in *AcceptClientPlanRequest, opts ...grpc.CallOption) (*AcceptClientPlanResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AcceptClientPlanResponse)
	err := c.cc.Invoke(ctx, Installment_AcceptClientPlan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetPaymentPlanDetail(ctx context.Context, in *GetPaymentPlanDetailRequest, opts ...grpc.CallOption) (*GetPaymentPlanDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentPlanDetailResponse)
	err := c.cc.Invoke(ctx, Installment_GetPaymentPlanDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetClientInstallment(ctx context.Context, in *GetClientInstallmentRequest, opts ...grpc.CallOption) (*GetClientInstallmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientInstallmentResponse)
	err := c.cc.Invoke(ctx, Installment_GetClientInstallment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetClientEarlyDischarge(ctx context.Context, in *GetClientEarlyDischargeRequest, opts ...grpc.CallOption) (*GetClientEarlyDischargeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientEarlyDischargeResponse)
	err := c.cc.Invoke(ctx, Installment_GetClientEarlyDischarge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest, opts ...grpc.CallOption) (*GetInstallmentStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInstallmentStatusResponse)
	err := c.cc.Invoke(ctx, Installment_GetInstallmentStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest, opts ...grpc.CallOption) (*GetEarlyDischargeRefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEarlyDischargeRefundResponse)
	err := c.cc.Invoke(ctx, Installment_GetEarlyDischargeRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *installmentClient) NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund, opts ...grpc.CallOption) (*NotifyInstallmentRefundResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NotifyInstallmentRefundResponse)
	err := c.cc.Invoke(ctx, Installment_NotifyInstallmentRefund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InstallmentServer is the server API for Installment service.
// All implementations must embed UnimplementedInstallmentServer
// for forward compatibility.
type InstallmentServer interface {
	// PLan
	ListClientEligiblePlans(context.Context, *ListClientEligiblePlansRequest) (*ListClientEligiblePlansResponse, error)
	GetClientPlanDetail(context.Context, *GetClientPlanDetailRequest) (*GetClientPlanDetailResponse, error)
	AcceptClientPlan(context.Context, *AcceptClientPlanRequest) (*AcceptClientPlanResponse, error)
	GetPaymentPlanDetail(context.Context, *GetPaymentPlanDetailRequest) (*GetPaymentPlanDetailResponse, error)
	// Installment
	GetClientInstallment(context.Context, *GetClientInstallmentRequest) (*GetClientInstallmentResponse, error)
	GetClientEarlyDischarge(context.Context, *GetClientEarlyDischargeRequest) (*GetClientEarlyDischargeResponse, error)
	GetInstallmentStatus(context.Context, *GetInstallmentStatusRequest) (*GetInstallmentStatusResponse, error)
	GetEarlyDischargeRefund(context.Context, *GetEarlyDischargeRefundRequest) (*GetEarlyDischargeRefundResponse, error)
	NotifyInstallmentRefund(context.Context, *InstallmentRefund) (*NotifyInstallmentRefundResponse, error)
	mustEmbedUnimplementedInstallmentServer()
}

// UnimplementedInstallmentServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInstallmentServer struct{}

func (UnimplementedInstallmentServer) ListClientEligiblePlans(context.Context, *ListClientEligiblePlansRequest) (*ListClientEligiblePlansResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClientEligiblePlans not implemented")
}
func (UnimplementedInstallmentServer) GetClientPlanDetail(context.Context, *GetClientPlanDetailRequest) (*GetClientPlanDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientPlanDetail not implemented")
}
func (UnimplementedInstallmentServer) AcceptClientPlan(context.Context, *AcceptClientPlanRequest) (*AcceptClientPlanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptClientPlan not implemented")
}
func (UnimplementedInstallmentServer) GetPaymentPlanDetail(context.Context, *GetPaymentPlanDetailRequest) (*GetPaymentPlanDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentPlanDetail not implemented")
}
func (UnimplementedInstallmentServer) GetClientInstallment(context.Context, *GetClientInstallmentRequest) (*GetClientInstallmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientInstallment not implemented")
}
func (UnimplementedInstallmentServer) GetClientEarlyDischarge(context.Context, *GetClientEarlyDischargeRequest) (*GetClientEarlyDischargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientEarlyDischarge not implemented")
}
func (UnimplementedInstallmentServer) GetInstallmentStatus(context.Context, *GetInstallmentStatusRequest) (*GetInstallmentStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstallmentStatus not implemented")
}
func (UnimplementedInstallmentServer) GetEarlyDischargeRefund(context.Context, *GetEarlyDischargeRefundRequest) (*GetEarlyDischargeRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEarlyDischargeRefund not implemented")
}
func (UnimplementedInstallmentServer) NotifyInstallmentRefund(context.Context, *InstallmentRefund) (*NotifyInstallmentRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyInstallmentRefund not implemented")
}
func (UnimplementedInstallmentServer) mustEmbedUnimplementedInstallmentServer() {}
func (UnimplementedInstallmentServer) testEmbeddedByValue()                     {}

// UnsafeInstallmentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InstallmentServer will
// result in compilation errors.
type UnsafeInstallmentServer interface {
	mustEmbedUnimplementedInstallmentServer()
}

func RegisterInstallmentServer(s grpc.ServiceRegistrar, srv InstallmentServer) {
	// If the following call pancis, it indicates UnimplementedInstallmentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Installment_ServiceDesc, srv)
}

func _Installment_ListClientEligiblePlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClientEligiblePlansRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).ListClientEligiblePlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_ListClientEligiblePlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).ListClientEligiblePlans(ctx, req.(*ListClientEligiblePlansRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetClientPlanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientPlanDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetClientPlanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetClientPlanDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetClientPlanDetail(ctx, req.(*GetClientPlanDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_AcceptClientPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptClientPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).AcceptClientPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_AcceptClientPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).AcceptClientPlan(ctx, req.(*AcceptClientPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetPaymentPlanDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentPlanDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetPaymentPlanDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetPaymentPlanDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetPaymentPlanDetail(ctx, req.(*GetPaymentPlanDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetClientInstallment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientInstallmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetClientInstallment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetClientInstallment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetClientInstallment(ctx, req.(*GetClientInstallmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetClientEarlyDischarge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientEarlyDischargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetClientEarlyDischarge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetClientEarlyDischarge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetClientEarlyDischarge(ctx, req.(*GetClientEarlyDischargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetInstallmentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstallmentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetInstallmentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetInstallmentStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetInstallmentStatus(ctx, req.(*GetInstallmentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_GetEarlyDischargeRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEarlyDischargeRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).GetEarlyDischargeRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_GetEarlyDischargeRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).GetEarlyDischargeRefund(ctx, req.(*GetEarlyDischargeRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Installment_NotifyInstallmentRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallmentRefund)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InstallmentServer).NotifyInstallmentRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Installment_NotifyInstallmentRefund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InstallmentServer).NotifyInstallmentRefund(ctx, req.(*InstallmentRefund))
	}
	return interceptor(ctx, in, info, handler)
}

// Installment_ServiceDesc is the grpc.ServiceDesc for Installment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Installment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "management_service.installment.v1.Installment",
	HandlerType: (*InstallmentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListClientEligiblePlans",
			Handler:    _Installment_ListClientEligiblePlans_Handler,
		},
		{
			MethodName: "GetClientPlanDetail",
			Handler:    _Installment_GetClientPlanDetail_Handler,
		},
		{
			MethodName: "AcceptClientPlan",
			Handler:    _Installment_AcceptClientPlan_Handler,
		},
		{
			MethodName: "GetPaymentPlanDetail",
			Handler:    _Installment_GetPaymentPlanDetail_Handler,
		},
		{
			MethodName: "GetClientInstallment",
			Handler:    _Installment_GetClientInstallment_Handler,
		},
		{
			MethodName: "GetClientEarlyDischarge",
			Handler:    _Installment_GetClientEarlyDischarge_Handler,
		},
		{
			MethodName: "GetInstallmentStatus",
			Handler:    _Installment_GetInstallmentStatus_Handler,
		},
		{
			MethodName: "GetEarlyDischargeRefund",
			Handler:    _Installment_GetEarlyDischargeRefund_Handler,
		},
		{
			MethodName: "NotifyInstallmentRefund",
			Handler:    _Installment_NotifyInstallmentRefund_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "management_service/installment/v1/installment_service.proto",
}
