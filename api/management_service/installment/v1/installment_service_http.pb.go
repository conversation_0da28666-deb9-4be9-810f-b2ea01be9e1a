// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: management_service/installment/v1/installment_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationInstallmentAcceptClientPlan = "/management_service.installment.v1.Installment/AcceptClientPlan"
const OperationInstallmentGetClientEarlyDischarge = "/management_service.installment.v1.Installment/GetClientEarlyDischarge"
const OperationInstallmentGetClientInstallment = "/management_service.installment.v1.Installment/GetClientInstallment"
const OperationInstallmentGetClientPlanDetail = "/management_service.installment.v1.Installment/GetClientPlanDetail"
const OperationInstallmentListClientEligiblePlans = "/management_service.installment.v1.Installment/ListClientEligiblePlans"

type InstallmentHTTPServer interface {
	AcceptClientPlan(context.Context, *AcceptClientPlanRequest) (*AcceptClientPlanResponse, error)
	GetClientEarlyDischarge(context.Context, *GetClientEarlyDischargeRequest) (*GetClientEarlyDischargeResponse, error)
	// GetClientInstallment Installment
	GetClientInstallment(context.Context, *GetClientInstallmentRequest) (*GetClientInstallmentResponse, error)
	GetClientPlanDetail(context.Context, *GetClientPlanDetailRequest) (*GetClientPlanDetailResponse, error)
	// ListClientEligiblePlans PLan
	ListClientEligiblePlans(context.Context, *ListClientEligiblePlansRequest) (*ListClientEligiblePlansResponse, error)
}

func RegisterInstallmentHTTPServer(s *http.Server, srv InstallmentHTTPServer) {
	r := s.Route("/")
	r.GET("/installment/v1/plans/eligible", _Installment_ListClientEligiblePlans0_HTTP_Handler(srv))
	r.GET("/installment/v1/plans/detail", _Installment_GetClientPlanDetail0_HTTP_Handler(srv))
	r.POST("/installment/v1/plans/acceptance", _Installment_AcceptClientPlan0_HTTP_Handler(srv))
	r.GET("/installment/v1/info", _Installment_GetClientInstallment0_HTTP_Handler(srv))
	r.GET("/installment/v1/early-discharge", _Installment_GetClientEarlyDischarge0_HTTP_Handler(srv))
}

func _Installment_ListClientEligiblePlans0_HTTP_Handler(srv InstallmentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClientEligiblePlansRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationInstallmentListClientEligiblePlans)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClientEligiblePlans(ctx, req.(*ListClientEligiblePlansRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClientEligiblePlansResponse)
		return ctx.Result(200, reply)
	}
}

func _Installment_GetClientPlanDetail0_HTTP_Handler(srv InstallmentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientPlanDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationInstallmentGetClientPlanDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientPlanDetail(ctx, req.(*GetClientPlanDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientPlanDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _Installment_AcceptClientPlan0_HTTP_Handler(srv InstallmentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AcceptClientPlanRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationInstallmentAcceptClientPlan)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AcceptClientPlan(ctx, req.(*AcceptClientPlanRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AcceptClientPlanResponse)
		return ctx.Result(200, reply)
	}
}

func _Installment_GetClientInstallment0_HTTP_Handler(srv InstallmentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientInstallmentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationInstallmentGetClientInstallment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientInstallment(ctx, req.(*GetClientInstallmentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientInstallmentResponse)
		return ctx.Result(200, reply)
	}
}

func _Installment_GetClientEarlyDischarge0_HTTP_Handler(srv InstallmentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientEarlyDischargeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationInstallmentGetClientEarlyDischarge)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientEarlyDischarge(ctx, req.(*GetClientEarlyDischargeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientEarlyDischargeResponse)
		return ctx.Result(200, reply)
	}
}

type InstallmentHTTPClient interface {
	AcceptClientPlan(ctx context.Context, req *AcceptClientPlanRequest, opts ...http.CallOption) (rsp *AcceptClientPlanResponse, err error)
	GetClientEarlyDischarge(ctx context.Context, req *GetClientEarlyDischargeRequest, opts ...http.CallOption) (rsp *GetClientEarlyDischargeResponse, err error)
	GetClientInstallment(ctx context.Context, req *GetClientInstallmentRequest, opts ...http.CallOption) (rsp *GetClientInstallmentResponse, err error)
	GetClientPlanDetail(ctx context.Context, req *GetClientPlanDetailRequest, opts ...http.CallOption) (rsp *GetClientPlanDetailResponse, err error)
	ListClientEligiblePlans(ctx context.Context, req *ListClientEligiblePlansRequest, opts ...http.CallOption) (rsp *ListClientEligiblePlansResponse, err error)
}

type InstallmentHTTPClientImpl struct {
	cc *http.Client
}

func NewInstallmentHTTPClient(client *http.Client) InstallmentHTTPClient {
	return &InstallmentHTTPClientImpl{client}
}

func (c *InstallmentHTTPClientImpl) AcceptClientPlan(ctx context.Context, in *AcceptClientPlanRequest, opts ...http.CallOption) (*AcceptClientPlanResponse, error) {
	var out AcceptClientPlanResponse
	pattern := "/installment/v1/plans/acceptance"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationInstallmentAcceptClientPlan))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *InstallmentHTTPClientImpl) GetClientEarlyDischarge(ctx context.Context, in *GetClientEarlyDischargeRequest, opts ...http.CallOption) (*GetClientEarlyDischargeResponse, error) {
	var out GetClientEarlyDischargeResponse
	pattern := "/installment/v1/early-discharge"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationInstallmentGetClientEarlyDischarge))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *InstallmentHTTPClientImpl) GetClientInstallment(ctx context.Context, in *GetClientInstallmentRequest, opts ...http.CallOption) (*GetClientInstallmentResponse, error) {
	var out GetClientInstallmentResponse
	pattern := "/installment/v1/info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationInstallmentGetClientInstallment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *InstallmentHTTPClientImpl) GetClientPlanDetail(ctx context.Context, in *GetClientPlanDetailRequest, opts ...http.CallOption) (*GetClientPlanDetailResponse, error) {
	var out GetClientPlanDetailResponse
	pattern := "/installment/v1/plans/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationInstallmentGetClientPlanDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *InstallmentHTTPClientImpl) ListClientEligiblePlans(ctx context.Context, in *ListClientEligiblePlansRequest, opts ...http.CallOption) (*ListClientEligiblePlansResponse, error) {
	var out ListClientEligiblePlansResponse
	pattern := "/installment/v1/plans/eligible"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationInstallmentListClientEligiblePlans))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
