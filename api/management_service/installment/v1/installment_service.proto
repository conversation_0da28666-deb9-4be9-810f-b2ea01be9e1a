syntax = "proto3";

package management_service.installment.v1;

option go_package = "installment/api/installment-service/v1;v1";

import "validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

service Installment {
  // PLan
  rpc ListClientEligiblePlans(ListClientEligiblePlansRequest) returns (ListClientEligiblePlansResponse) {
    option (google.api.http) = {
      get: "/installment/v1/plans/eligible"
    };
  }
  rpc GetClientPlanDetail(GetClientPlanDetailRequest) returns (GetClientPlanDetailResponse) {
    option (google.api.http) = {
      get: "/installment/v1/plans/detail"
    };
  }
  rpc AcceptClientPlan(AcceptClientPlanRequest) returns (AcceptClientPlanResponse) {
    option (google.api.http) = {
      post: "/installment/v1/plans/acceptance",
      body: "*"
    };
  }
  rpc GetPaymentPlanDetail(GetPaymentPlanDetailRequest) returns (GetPaymentPlanDetailResponse) {}

  // Installment
  rpc GetClientInstallment(GetClientInstallmentRequest) returns (GetClientInstallmentResponse) {
    option (google.api.http) = {
      get: "/installment/v1/info"
    };
  }
  rpc GetClientEarlyDischarge(GetClientEarlyDischargeRequest) returns (GetClientEarlyDischargeResponse) {
    option (google.api.http) = {
      get: "/installment/v1/early-discharge"
    };
  }
  rpc GetInstallmentStatus(GetInstallmentStatusRequest) returns (GetInstallmentStatusResponse) {}
  rpc GetEarlyDischargeRefund(GetEarlyDischargeRefundRequest) returns (GetEarlyDischargeRefundResponse) {}
  rpc NotifyInstallmentRefund(InstallmentRefund) returns (NotifyInstallmentRefundResponse) {}
}

message ListClientEligiblePlansRequest {
  string partner_code = 1 [json_name = 'partner_code', (validate.rules).string.min_len = 1];
  int32 app_id = 2 [json_name = 'app_id', (validate.rules).int32.gt = 0];
  string app_trans_id = 3 [json_name = 'app_trans_id', (validate.rules).string.min_len = 1];
  int64 charge_amount = 4 [json_name = 'charge_amount', (validate.rules).int64.gt = 0];
}

message ListClientEligiblePlansResponse {
  message Plan {
    // Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
    string plan_key = 1 [json_name = 'plan_key'];
    // Plan info includes general information of the plan
    BasePlan plan_info = 2 [json_name = 'plan_info'];
    // Cost info of the plan includes total of each fee amount and fee info url
    CostInfo cost_info = 3 [json_name = 'cost_info'];
    // Is highlight plan
    bool is_popular = 4 [json_name = 'is_popular'];
    // Plan detail url
    string plan_detail_url = 5 [json_name = 'plan_detail_url'];
    // List of scheduled repayment
    repeated PlanRepaymentSchedule scheduled_repayments = 6 [json_name = 'scheduled_repayments'];
  }
  // List of plans
  repeated Plan plan_options = 1 [json_name = 'plan_options'];
  // Plan key selected
  string plan_selected = 2 [json_name = 'plan_selected'];
}

message GetClientPlanDetailRequest {
  string plan_key = 1 [json_name = 'plan_key', (validate.rules).string.min_len = 1];
}

message GetClientPlanDetailResponse {
  // Plan info includes general information of the plan
  BasePlan plan_info = 1 [json_name = 'plan_info'];
  // Cost info of the plan includes total of each fee amount and fee info url
  CostInfo cost_info = 2 [json_name = 'cost_info'];
  // Plan detail url
  string plan_detail_url = 3 [json_name = 'plan_detail_url'];
}

message AcceptClientPlanRequest {
  string plan_key = 1 [json_name = 'plan_key', (validate.rules).string.min_len = 1];
  int32 app_id = 2 [json_name = 'app_id', (validate.rules).int32.gt = 0];
  string app_trans_id = 3 [json_name = 'app_trans_id', (validate.rules).string.min_len = 1];
}

message AcceptClientPlanResponse {
  string plan_key = 1 [json_name = 'plan_key'];
}

message GetPaymentPlanDetailRequest {
  message OrderInfo {
    // Order app id
    int32 app_id = 1 [(validate.rules).int32.gt = 0];
    // Order app transaction id
    string app_trans_id = 2 [(validate.rules).string.min_len = 1]; // #Agreement_Payment
    // User charge amount
    int64 charge_amount = 3 [(validate.rules).int64.gt = 0];
  }
  // ZaloPay user id
  int64 zalopay_id = 1 [(validate.rules).int64.gt = 0];
  // Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
  string plan_key = 2 [(validate.rules).string.min_len = 0];
  // Order information
  OrderInfo order_info = 3 [(validate.rules).message.required = true];
}

message GetPaymentPlanDetailResponse {
  // Plan info includes general information of the plan
  BasePlan plan_info = 1;
  // Plan detail links
  string plan_detail_url = 2;
  // List of scheduled repayment
  string fs_charge_info = 3;
  // List of scheduled repayment
  InteractionGuide interaction_guide = 4;
  // Metadata
  google.protobuf.Struct metadata = 5;
}

message GetClientInstallmentRequest {
  int64 zp_trans_id = 1 [json_name = 'zp_trans_id', (validate.rules).int64.gt = 0];
}

message GetClientInstallmentResponse {
  InstallmentData installment = 1 [json_name = 'installment'];
  optional EarlyDischarge early_discharge = 2 [json_name = 'early_discharge'];
  repeated RepaymentSchedule repayment_schedules = 9 [json_name = 'repayment_schedules'];
}

message GetInstallmentStatusRequest {
  int64 zp_trans_id = 1 [(validate.rules).int64.gt = 0];
  bool force_latest = 2;
}

message GetInstallmentStatusResponse {
  InstallmentStatus status = 1;
  optional InstallmentBase info = 2;
}

message GetClientEarlyDischargeRequest {
  int64 zp_trans_id = 1 [(validate.rules).int64.gt = 0];
}

message GetClientEarlyDischargeResponse {
  EarlyDischarge early_discharge = 1 [json_name = 'early_discharge'];
}

message GetEarlyDischargeRefundRequest {
  int64 zp_trans_id = 1 [json_name = 'zp_trans_id', (validate.rules).int64.gt = 0];
  optional bool force_latest = 2 [json_name = 'force_latest'];
}

message GetEarlyDischargeRefundResponse {
  EarlyDischargeForRefund discharge_info = 1 [json_name = 'discharge_info'];
}

message NotifyInstallmentRefundResponse {}

message InstallmentBase {
  int64 id = 1;
  int32 tenor = 2;
  int64 zp_trans_id = 3;
  string partner_code = 4;
  string partner_inst_id = 5;
  double interest_rate = 6;
  int64 principal_amount = 7;
}

message InstallmentData {
  int64 id = 1 [json_name = 'id'];
  int32 tenure = 2 [json_name = 'tenure'];
  InstallmentStatus status = 3 [json_name = 'status'];
  int64 principal_amount = 4 [json_name = 'principal_amount'];
  int64 interest_amount = 5 [json_name = 'interest_amount'];
  int64 penalty_amount = 6 [json_name = 'penalty_amount'];
  int64 total_amount_due = 7 [json_name = 'total_amount_due'];
  int64 total_paid_amount = 8 [json_name = 'total_paid_amount'];
  int64 total_remaining_amount = 9 [json_name = 'total_remaining_amount'];
}

message InstallmentRefund {
  int64 zp_trans_id = 1 [json_name = 'zp_trans_id'];
  int32 version = 2 [json_name = 'version'];
  int64 net_refund_amount = 3 [json_name = 'net_refund_amount'];
  int64 total_refund_amount = 4 [json_name = 'total_refund_amount'];
  int64 user_topup_amount = 5 [json_name = 'user_topup_amount'];
  bool user_topup_required = 6 [json_name = 'user_topup_required'];
}

message BasePlan {
  // Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
  optional string plan_key = 1 [json_name = 'plan_key'];
  // Tenor unit is the unit of the tenor, it can be day, month, year
  int64 tenor_number = 2 [json_name = 'tenor_number'];
  // Emi amount is the amount of each installment
  int64 emi_amount = 3 [json_name = 'emi_amount'];
  // Total amount is the total amount of the plan
  int64 total_amount = 4 [json_name = 'total_amount'];
  // Principal amount is the principal amount of the plan
  int64 principal_amount = 5 [json_name = 'principal_amount'];
  // Plan status
  PlanStatus status = 6 [json_name = 'status'];
}

enum PlanStatus {
  // Plan status is unspecified
  PLAN_STATUS_UNSPECIFIED = 0;
  // Plan status is active
  PLAN_STATUS_ACTIVE = 1;
  // Plan status is inactive
  PLAN_STATUS_INACTIVE = 2;
  // Plan status is invalid
  PLAN_STATUS_INVALID = 3;
}

enum RepayStatus {
  // Repay status is unspecified
  REPAY_STATUS_UNSPECIFIED = 0;
  // Repay status is pending
  REPAY_STATUS_PENDING = 1;
  // Repay status is success
  REPAY_STATUS_DUE = 2;
  // Repay status is failed
  REPAY_STATUS_PAID = 3;
  // Repay status is overdue
  REPAY_STATUS_OVERDUE = 4;
}

enum InstallmentStatus {
  // Installment status is unspecified
  INSTALLMENT_STATUS_UNSPECIFIED = 0;
  // Installment status is init
  INSTALLMENT_STATUS_INIT = 1;
  // Installment status is open
  INSTALLMENT_STATUS_OPEN = 2;
  // Installment status is closed
  INSTALLMENT_STATUS_CLOSED = 3;
}

message FullPlan {
  // Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
  string plan_key = 1 [json_name = 'plan_key'];
  // Plan info includes general information of the plan
  BasePlan plan_info = 2 [json_name = 'plan_info'];
  // Cost info of the plan includes total of each fee amount and fee info url
  CostInfo cost_info = 6 [json_name = 'cost_info'];
  // Plan detail url
  string plan_detail_url = 7 [json_name = 'plan_detail_url'];
}

message CostInfo {
  // Total cost amount is the total amount of the plan
  int64 total_cost_amount = 1 [json_name = 'total_cost_amount'];
  // Total fee amount is the total amount of all fees
  int64 total_fee_amount = 2 [json_name = 'total_fee_amount'];
  // Platform fee amount is the fee amount of the platform
  int64 platform_fee_amount = 3 [json_name = 'platform_fee_amount'];
  // Conversion fee amount is the fee amount of the conversion
  int64 conversion_fee_amount = 4 [json_name = 'conversion_fee_amount'];
  // Interest amount is the interest amount of the plan
  int64 interest_amount = 5 [json_name = 'interest_amount'];
  // List of fee info
  repeated Fee list_fee_info = 6 [json_name = 'list_fee_info'];
  // Fee explanation
  string fee_explanation = 7 [json_name = 'fee_explanation'];
}

message PlanRepaymentSchedule {
  int64 amount = 1 [json_name = 'amount'];
  string due_date = 2 [json_name = 'due_date'];
  int32 installment_number = 3 [json_name = 'installment_number'];
}

message RepaymentSchedule {
  int32 seq_no = 1 [json_name = 'seq_no'];
  RepayStatus status = 2 [json_name = 'status'];
  string due_date = 3 [json_name = 'due_date'];
  int64 due_amount = 4 [json_name = 'due_amount'];
  int64 penalty_amount = 5 [json_name = 'penalty_amount'];
  int64 total_due_amount = 6 [json_name = 'total_due_amount'];
  int64 total_paid_amount = 7 [json_name = 'total_paid_amount'];
  int64 total_remaining_amount = 8 [json_name = 'total_remaining_amount'];
}

enum EarlyDischargeKind {
  // Early discharge type is unspecified
  EARLY_DISCHARGE_KIND_UNSPECIFIED = 0;
  // Early discharge type is normal
  EARLY_DISCHARGE_KIND_NORMAL = 1;
  // Early discharge type is refund
  EARLY_DISCHARGE_KIND_REFUND = 2;
}

enum EarlyDischargeStatus {
  // Early discharge status is unspecified
  EARLY_DISCHARGE_STATUS_UNSPECIFIED = 0;
  // Early discharge status is open
  EARLY_DISCHARGE_STATUS_ELIGIBLE = 1;
  // Early discharge status is init
  EARLY_DISCHARGE_STATUS_INELIGIBLE = 2;
  // Early discharge status is processing
  EARLY_DISCHARGE_STATUS_PROCESSING = 3;
  // Early discharge status is closed
  EARLY_DISCHARGE_STATUS_CLOSED = 4;
}

message EarlyDischargeBase {
  EarlyDischargeKind kind = 1 [json_name = 'kind'];
  EarlyDischargeStatus status = 2 [json_name = 'status'];
  optional bool allowed = 3 [json_name = 'allowed'];
  optional bool in_session = 5 [json_name = 'in_session'];
  optional Session session_info = 4 [json_name = 'session_info'];
}

message EarlyDischargeDetail {
  int64 total_discharge_amount = 1 [json_name = 'total_discharge_amount'];
  int64 early_discharge_amount = 2 [json_name = 'early_discharge_amount'];
  int64 early_discharge_fee = 3 [json_name = 'early_discharge_fee'];
  int64 total_outstanding_amount = 4 [json_name = 'total_outstanding_amount'];
  optional int64 outstanding_principal_amount = 5 [json_name = 'outstanding_principal_amount'];
  optional int64 outstanding_interest_amount = 6 [json_name = 'outstanding_interest_amount'];
  optional int64 outstanding_penalty_amount = 7 [json_name = 'outstanding_penalty_amount'];
}

message EarlyDischarge {
  EarlyDischargeBase info = 1 [json_name = 'info'];
  optional EarlyDischargeDetail detail = 2 [json_name = 'detail'];
}

// EarlyDischargeForRefund need to be use the `composition` approach when fields grow in the future, may be > 5 fields
message EarlyDischargeForRefund {
  EarlyDischargeStatus status = 1 [json_name = 'status'];
  // discharge_amount is the raw total amount of the early discharge
  int64 discharge_amount = 2 [json_name = 'discharge_amount'];
  // session_available ref to in_session in EarlyDischargeBase
  bool session_available  = 3 [json_name = 'session_available'];
}

message InteractionGuide {
  Action action = 1;
  Message message = 2;
  Message notice = 3;
}

message Action {
  ActionType type = 1;
  string text = 2;
  string zpi_url = 3;
  string zpa_url = 4;
}

enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  ACTION_TYPE_PLAN_SELECTION = 1;
}

message Message {
  string text = 1;
  string color = 2;
}

enum FeeType {
  FEE_TYPE_INVALID = 0;
  FEE_TYPE_CONVERSION = 1;
  FEE_TYPE_PLATFORM = 2;
}

message Fee {
  FeeType fee_type = 1;
  int64 fee_amount = 2;
  string message = 3;
  string message_en = 4;
}

// Session defines the operational time window when early discharge is available
message Session {
  // Start time in 24-hour format "HH:MM"
  string start_time = 1 [json_name = 'start_time'];
  // End time in 24-hour format "HH:MM"
  string end_time = 2 [json_name = 'end_time'];
}