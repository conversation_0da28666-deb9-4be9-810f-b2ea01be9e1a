// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: management_service/installment/v1/installment_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListClientEligiblePlansRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientEligiblePlansRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientEligiblePlansRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListClientEligiblePlansRequestMultiError, or nil if none found.
func (m *ListClientEligiblePlansRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientEligiblePlansRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPartnerCode()) < 1 {
		err := ListClientEligiblePlansRequestValidationError{
			field:  "PartnerCode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppId() <= 0 {
		err := ListClientEligiblePlansRequestValidationError{
			field:  "AppId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := ListClientEligiblePlansRequestValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetChargeAmount() <= 0 {
		err := ListClientEligiblePlansRequestValidationError{
			field:  "ChargeAmount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListClientEligiblePlansRequestMultiError(errors)
	}

	return nil
}

// ListClientEligiblePlansRequestMultiError is an error wrapping multiple
// validation errors returned by ListClientEligiblePlansRequest.ValidateAll()
// if the designated constraints aren't met.
type ListClientEligiblePlansRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientEligiblePlansRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientEligiblePlansRequestMultiError) AllErrors() []error { return m }

// ListClientEligiblePlansRequestValidationError is the validation error
// returned by ListClientEligiblePlansRequest.Validate if the designated
// constraints aren't met.
type ListClientEligiblePlansRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientEligiblePlansRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientEligiblePlansRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientEligiblePlansRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientEligiblePlansRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientEligiblePlansRequestValidationError) ErrorName() string {
	return "ListClientEligiblePlansRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientEligiblePlansRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientEligiblePlansRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientEligiblePlansRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientEligiblePlansRequestValidationError{}

// Validate checks the field values on ListClientEligiblePlansResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientEligiblePlansResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientEligiblePlansResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListClientEligiblePlansResponseMultiError, or nil if none found.
func (m *ListClientEligiblePlansResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientEligiblePlansResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPlanOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientEligiblePlansResponseValidationError{
						field:  fmt.Sprintf("PlanOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientEligiblePlansResponseValidationError{
						field:  fmt.Sprintf("PlanOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientEligiblePlansResponseValidationError{
					field:  fmt.Sprintf("PlanOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PlanSelected

	if len(errors) > 0 {
		return ListClientEligiblePlansResponseMultiError(errors)
	}

	return nil
}

// ListClientEligiblePlansResponseMultiError is an error wrapping multiple
// validation errors returned by ListClientEligiblePlansResponse.ValidateAll()
// if the designated constraints aren't met.
type ListClientEligiblePlansResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientEligiblePlansResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientEligiblePlansResponseMultiError) AllErrors() []error { return m }

// ListClientEligiblePlansResponseValidationError is the validation error
// returned by ListClientEligiblePlansResponse.Validate if the designated
// constraints aren't met.
type ListClientEligiblePlansResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientEligiblePlansResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientEligiblePlansResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientEligiblePlansResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientEligiblePlansResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientEligiblePlansResponseValidationError) ErrorName() string {
	return "ListClientEligiblePlansResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientEligiblePlansResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientEligiblePlansResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientEligiblePlansResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientEligiblePlansResponseValidationError{}

// Validate checks the field values on GetClientPlanDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientPlanDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientPlanDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientPlanDetailRequestMultiError, or nil if none found.
func (m *GetClientPlanDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientPlanDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlanKey()) < 1 {
		err := GetClientPlanDetailRequestValidationError{
			field:  "PlanKey",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientPlanDetailRequestMultiError(errors)
	}

	return nil
}

// GetClientPlanDetailRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientPlanDetailRequest.ValidateAll() if
// the designated constraints aren't met.
type GetClientPlanDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientPlanDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientPlanDetailRequestMultiError) AllErrors() []error { return m }

// GetClientPlanDetailRequestValidationError is the validation error returned
// by GetClientPlanDetailRequest.Validate if the designated constraints aren't met.
type GetClientPlanDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientPlanDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientPlanDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientPlanDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientPlanDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientPlanDetailRequestValidationError) ErrorName() string {
	return "GetClientPlanDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientPlanDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientPlanDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientPlanDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientPlanDetailRequestValidationError{}

// Validate checks the field values on GetClientPlanDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientPlanDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientPlanDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientPlanDetailResponseMultiError, or nil if none found.
func (m *GetClientPlanDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientPlanDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientPlanDetailResponseValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientPlanDetailResponseValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientPlanDetailResponseValidationError{
				field:  "PlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCostInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientPlanDetailResponseValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientPlanDetailResponseValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCostInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientPlanDetailResponseValidationError{
				field:  "CostInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanDetailUrl

	if len(errors) > 0 {
		return GetClientPlanDetailResponseMultiError(errors)
	}

	return nil
}

// GetClientPlanDetailResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientPlanDetailResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientPlanDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientPlanDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientPlanDetailResponseMultiError) AllErrors() []error { return m }

// GetClientPlanDetailResponseValidationError is the validation error returned
// by GetClientPlanDetailResponse.Validate if the designated constraints
// aren't met.
type GetClientPlanDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientPlanDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientPlanDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientPlanDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientPlanDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientPlanDetailResponseValidationError) ErrorName() string {
	return "GetClientPlanDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientPlanDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientPlanDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientPlanDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientPlanDetailResponseValidationError{}

// Validate checks the field values on AcceptClientPlanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AcceptClientPlanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcceptClientPlanRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcceptClientPlanRequestMultiError, or nil if none found.
func (m *AcceptClientPlanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AcceptClientPlanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlanKey()) < 1 {
		err := AcceptClientPlanRequestValidationError{
			field:  "PlanKey",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppId() <= 0 {
		err := AcceptClientPlanRequestValidationError{
			field:  "AppId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := AcceptClientPlanRequestValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AcceptClientPlanRequestMultiError(errors)
	}

	return nil
}

// AcceptClientPlanRequestMultiError is an error wrapping multiple validation
// errors returned by AcceptClientPlanRequest.ValidateAll() if the designated
// constraints aren't met.
type AcceptClientPlanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcceptClientPlanRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcceptClientPlanRequestMultiError) AllErrors() []error { return m }

// AcceptClientPlanRequestValidationError is the validation error returned by
// AcceptClientPlanRequest.Validate if the designated constraints aren't met.
type AcceptClientPlanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcceptClientPlanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcceptClientPlanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcceptClientPlanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcceptClientPlanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcceptClientPlanRequestValidationError) ErrorName() string {
	return "AcceptClientPlanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AcceptClientPlanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcceptClientPlanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcceptClientPlanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcceptClientPlanRequestValidationError{}

// Validate checks the field values on AcceptClientPlanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AcceptClientPlanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcceptClientPlanResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcceptClientPlanResponseMultiError, or nil if none found.
func (m *AcceptClientPlanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AcceptClientPlanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanKey

	if len(errors) > 0 {
		return AcceptClientPlanResponseMultiError(errors)
	}

	return nil
}

// AcceptClientPlanResponseMultiError is an error wrapping multiple validation
// errors returned by AcceptClientPlanResponse.ValidateAll() if the designated
// constraints aren't met.
type AcceptClientPlanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcceptClientPlanResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcceptClientPlanResponseMultiError) AllErrors() []error { return m }

// AcceptClientPlanResponseValidationError is the validation error returned by
// AcceptClientPlanResponse.Validate if the designated constraints aren't met.
type AcceptClientPlanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcceptClientPlanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcceptClientPlanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcceptClientPlanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcceptClientPlanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcceptClientPlanResponseValidationError) ErrorName() string {
	return "AcceptClientPlanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AcceptClientPlanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcceptClientPlanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcceptClientPlanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcceptClientPlanResponseValidationError{}

// Validate checks the field values on GetPaymentPlanDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentPlanDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentPlanDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentPlanDetailRequestMultiError, or nil if none found.
func (m *GetPaymentPlanDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentPlanDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZalopayId() <= 0 {
		err := GetPaymentPlanDetailRequestValidationError{
			field:  "ZalopayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPlanKey()) < 0 {
		err := GetPaymentPlanDetailRequestValidationError{
			field:  "PlanKey",
			reason: "value length must be at least 0 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOrderInfo() == nil {
		err := GetPaymentPlanDetailRequestValidationError{
			field:  "OrderInfo",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentPlanDetailRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentPlanDetailRequestValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentPlanDetailRequestValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentPlanDetailRequestMultiError(errors)
	}

	return nil
}

// GetPaymentPlanDetailRequestMultiError is an error wrapping multiple
// validation errors returned by GetPaymentPlanDetailRequest.ValidateAll() if
// the designated constraints aren't met.
type GetPaymentPlanDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentPlanDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentPlanDetailRequestMultiError) AllErrors() []error { return m }

// GetPaymentPlanDetailRequestValidationError is the validation error returned
// by GetPaymentPlanDetailRequest.Validate if the designated constraints
// aren't met.
type GetPaymentPlanDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentPlanDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentPlanDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentPlanDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentPlanDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentPlanDetailRequestValidationError) ErrorName() string {
	return "GetPaymentPlanDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentPlanDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentPlanDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentPlanDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentPlanDetailRequestValidationError{}

// Validate checks the field values on GetPaymentPlanDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentPlanDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentPlanDetailResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentPlanDetailResponseMultiError, or nil if none found.
func (m *GetPaymentPlanDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentPlanDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentPlanDetailResponseValidationError{
				field:  "PlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanDetailUrl

	// no validation rules for FsChargeInfo

	if all {
		switch v := interface{}(m.GetInteractionGuide()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "InteractionGuide",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "InteractionGuide",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInteractionGuide()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentPlanDetailResponseValidationError{
				field:  "InteractionGuide",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentPlanDetailResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentPlanDetailResponseValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaymentPlanDetailResponseMultiError(errors)
	}

	return nil
}

// GetPaymentPlanDetailResponseMultiError is an error wrapping multiple
// validation errors returned by GetPaymentPlanDetailResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPaymentPlanDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentPlanDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentPlanDetailResponseMultiError) AllErrors() []error { return m }

// GetPaymentPlanDetailResponseValidationError is the validation error returned
// by GetPaymentPlanDetailResponse.Validate if the designated constraints
// aren't met.
type GetPaymentPlanDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentPlanDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentPlanDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentPlanDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentPlanDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentPlanDetailResponseValidationError) ErrorName() string {
	return "GetPaymentPlanDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentPlanDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentPlanDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentPlanDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentPlanDetailResponseValidationError{}

// Validate checks the field values on GetClientInstallmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientInstallmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientInstallmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientInstallmentRequestMultiError, or nil if none found.
func (m *GetClientInstallmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientInstallmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZpTransId() <= 0 {
		err := GetClientInstallmentRequestValidationError{
			field:  "ZpTransId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientInstallmentRequestMultiError(errors)
	}

	return nil
}

// GetClientInstallmentRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientInstallmentRequest.ValidateAll() if
// the designated constraints aren't met.
type GetClientInstallmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientInstallmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientInstallmentRequestMultiError) AllErrors() []error { return m }

// GetClientInstallmentRequestValidationError is the validation error returned
// by GetClientInstallmentRequest.Validate if the designated constraints
// aren't met.
type GetClientInstallmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientInstallmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientInstallmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientInstallmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientInstallmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientInstallmentRequestValidationError) ErrorName() string {
	return "GetClientInstallmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientInstallmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientInstallmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientInstallmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientInstallmentRequestValidationError{}

// Validate checks the field values on GetClientInstallmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientInstallmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientInstallmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientInstallmentResponseMultiError, or nil if none found.
func (m *GetClientInstallmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientInstallmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInstallment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientInstallmentResponseValidationError{
					field:  "Installment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientInstallmentResponseValidationError{
					field:  "Installment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientInstallmentResponseValidationError{
				field:  "Installment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRepaymentSchedules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClientInstallmentResponseValidationError{
						field:  fmt.Sprintf("RepaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClientInstallmentResponseValidationError{
						field:  fmt.Sprintf("RepaymentSchedules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClientInstallmentResponseValidationError{
					field:  fmt.Sprintf("RepaymentSchedules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.EarlyDischarge != nil {

		if all {
			switch v := interface{}(m.GetEarlyDischarge()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClientInstallmentResponseValidationError{
						field:  "EarlyDischarge",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClientInstallmentResponseValidationError{
						field:  "EarlyDischarge",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEarlyDischarge()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClientInstallmentResponseValidationError{
					field:  "EarlyDischarge",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetClientInstallmentResponseMultiError(errors)
	}

	return nil
}

// GetClientInstallmentResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientInstallmentResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientInstallmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientInstallmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientInstallmentResponseMultiError) AllErrors() []error { return m }

// GetClientInstallmentResponseValidationError is the validation error returned
// by GetClientInstallmentResponse.Validate if the designated constraints
// aren't met.
type GetClientInstallmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientInstallmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientInstallmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientInstallmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientInstallmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientInstallmentResponseValidationError) ErrorName() string {
	return "GetClientInstallmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientInstallmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientInstallmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientInstallmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientInstallmentResponseValidationError{}

// Validate checks the field values on GetInstallmentStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInstallmentStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInstallmentStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInstallmentStatusRequestMultiError, or nil if none found.
func (m *GetInstallmentStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInstallmentStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZpTransId() <= 0 {
		err := GetInstallmentStatusRequestValidationError{
			field:  "ZpTransId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ForceLatest

	if len(errors) > 0 {
		return GetInstallmentStatusRequestMultiError(errors)
	}

	return nil
}

// GetInstallmentStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetInstallmentStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetInstallmentStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInstallmentStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInstallmentStatusRequestMultiError) AllErrors() []error { return m }

// GetInstallmentStatusRequestValidationError is the validation error returned
// by GetInstallmentStatusRequest.Validate if the designated constraints
// aren't met.
type GetInstallmentStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInstallmentStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInstallmentStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInstallmentStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInstallmentStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInstallmentStatusRequestValidationError) ErrorName() string {
	return "GetInstallmentStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetInstallmentStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInstallmentStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInstallmentStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInstallmentStatusRequestValidationError{}

// Validate checks the field values on GetInstallmentStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetInstallmentStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetInstallmentStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetInstallmentStatusResponseMultiError, or nil if none found.
func (m *GetInstallmentStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetInstallmentStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if m.Info != nil {

		if all {
			switch v := interface{}(m.GetInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetInstallmentStatusResponseValidationError{
						field:  "Info",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetInstallmentStatusResponseValidationError{
						field:  "Info",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetInstallmentStatusResponseValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetInstallmentStatusResponseMultiError(errors)
	}

	return nil
}

// GetInstallmentStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetInstallmentStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type GetInstallmentStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetInstallmentStatusResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetInstallmentStatusResponseMultiError) AllErrors() []error { return m }

// GetInstallmentStatusResponseValidationError is the validation error returned
// by GetInstallmentStatusResponse.Validate if the designated constraints
// aren't met.
type GetInstallmentStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetInstallmentStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetInstallmentStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetInstallmentStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetInstallmentStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetInstallmentStatusResponseValidationError) ErrorName() string {
	return "GetInstallmentStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetInstallmentStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetInstallmentStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetInstallmentStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetInstallmentStatusResponseValidationError{}

// Validate checks the field values on GetClientEarlyDischargeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientEarlyDischargeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientEarlyDischargeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetClientEarlyDischargeRequestMultiError, or nil if none found.
func (m *GetClientEarlyDischargeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientEarlyDischargeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZpTransId() <= 0 {
		err := GetClientEarlyDischargeRequestValidationError{
			field:  "ZpTransId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientEarlyDischargeRequestMultiError(errors)
	}

	return nil
}

// GetClientEarlyDischargeRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientEarlyDischargeRequest.ValidateAll()
// if the designated constraints aren't met.
type GetClientEarlyDischargeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientEarlyDischargeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientEarlyDischargeRequestMultiError) AllErrors() []error { return m }

// GetClientEarlyDischargeRequestValidationError is the validation error
// returned by GetClientEarlyDischargeRequest.Validate if the designated
// constraints aren't met.
type GetClientEarlyDischargeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientEarlyDischargeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientEarlyDischargeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientEarlyDischargeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientEarlyDischargeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientEarlyDischargeRequestValidationError) ErrorName() string {
	return "GetClientEarlyDischargeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientEarlyDischargeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientEarlyDischargeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientEarlyDischargeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientEarlyDischargeRequestValidationError{}

// Validate checks the field values on GetClientEarlyDischargeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientEarlyDischargeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientEarlyDischargeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetClientEarlyDischargeResponseMultiError, or nil if none found.
func (m *GetClientEarlyDischargeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientEarlyDischargeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEarlyDischarge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientEarlyDischargeResponseValidationError{
					field:  "EarlyDischarge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientEarlyDischargeResponseValidationError{
					field:  "EarlyDischarge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEarlyDischarge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientEarlyDischargeResponseValidationError{
				field:  "EarlyDischarge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClientEarlyDischargeResponseMultiError(errors)
	}

	return nil
}

// GetClientEarlyDischargeResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientEarlyDischargeResponse.ValidateAll()
// if the designated constraints aren't met.
type GetClientEarlyDischargeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientEarlyDischargeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientEarlyDischargeResponseMultiError) AllErrors() []error { return m }

// GetClientEarlyDischargeResponseValidationError is the validation error
// returned by GetClientEarlyDischargeResponse.Validate if the designated
// constraints aren't met.
type GetClientEarlyDischargeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientEarlyDischargeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientEarlyDischargeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientEarlyDischargeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientEarlyDischargeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientEarlyDischargeResponseValidationError) ErrorName() string {
	return "GetClientEarlyDischargeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientEarlyDischargeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientEarlyDischargeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientEarlyDischargeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientEarlyDischargeResponseValidationError{}

// Validate checks the field values on GetEarlyDischargeRefundRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEarlyDischargeRefundRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarlyDischargeRefundRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEarlyDischargeRefundRequestMultiError, or nil if none found.
func (m *GetEarlyDischargeRefundRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarlyDischargeRefundRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetZpTransId() <= 0 {
		err := GetEarlyDischargeRefundRequestValidationError{
			field:  "ZpTransId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.ForceLatest != nil {
		// no validation rules for ForceLatest
	}

	if len(errors) > 0 {
		return GetEarlyDischargeRefundRequestMultiError(errors)
	}

	return nil
}

// GetEarlyDischargeRefundRequestMultiError is an error wrapping multiple
// validation errors returned by GetEarlyDischargeRefundRequest.ValidateAll()
// if the designated constraints aren't met.
type GetEarlyDischargeRefundRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarlyDischargeRefundRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarlyDischargeRefundRequestMultiError) AllErrors() []error { return m }

// GetEarlyDischargeRefundRequestValidationError is the validation error
// returned by GetEarlyDischargeRefundRequest.Validate if the designated
// constraints aren't met.
type GetEarlyDischargeRefundRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarlyDischargeRefundRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarlyDischargeRefundRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarlyDischargeRefundRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarlyDischargeRefundRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarlyDischargeRefundRequestValidationError) ErrorName() string {
	return "GetEarlyDischargeRefundRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarlyDischargeRefundRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarlyDischargeRefundRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarlyDischargeRefundRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarlyDischargeRefundRequestValidationError{}

// Validate checks the field values on GetEarlyDischargeRefundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEarlyDischargeRefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarlyDischargeRefundResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEarlyDischargeRefundResponseMultiError, or nil if none found.
func (m *GetEarlyDischargeRefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarlyDischargeRefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDischargeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarlyDischargeRefundResponseValidationError{
					field:  "DischargeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarlyDischargeRefundResponseValidationError{
					field:  "DischargeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDischargeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarlyDischargeRefundResponseValidationError{
				field:  "DischargeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEarlyDischargeRefundResponseMultiError(errors)
	}

	return nil
}

// GetEarlyDischargeRefundResponseMultiError is an error wrapping multiple
// validation errors returned by GetEarlyDischargeRefundResponse.ValidateAll()
// if the designated constraints aren't met.
type GetEarlyDischargeRefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarlyDischargeRefundResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarlyDischargeRefundResponseMultiError) AllErrors() []error { return m }

// GetEarlyDischargeRefundResponseValidationError is the validation error
// returned by GetEarlyDischargeRefundResponse.Validate if the designated
// constraints aren't met.
type GetEarlyDischargeRefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarlyDischargeRefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarlyDischargeRefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarlyDischargeRefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarlyDischargeRefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarlyDischargeRefundResponseValidationError) ErrorName() string {
	return "GetEarlyDischargeRefundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarlyDischargeRefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarlyDischargeRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarlyDischargeRefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarlyDischargeRefundResponseValidationError{}

// Validate checks the field values on NotifyInstallmentRefundResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NotifyInstallmentRefundResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotifyInstallmentRefundResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NotifyInstallmentRefundResponseMultiError, or nil if none found.
func (m *NotifyInstallmentRefundResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *NotifyInstallmentRefundResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return NotifyInstallmentRefundResponseMultiError(errors)
	}

	return nil
}

// NotifyInstallmentRefundResponseMultiError is an error wrapping multiple
// validation errors returned by NotifyInstallmentRefundResponse.ValidateAll()
// if the designated constraints aren't met.
type NotifyInstallmentRefundResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotifyInstallmentRefundResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotifyInstallmentRefundResponseMultiError) AllErrors() []error { return m }

// NotifyInstallmentRefundResponseValidationError is the validation error
// returned by NotifyInstallmentRefundResponse.Validate if the designated
// constraints aren't met.
type NotifyInstallmentRefundResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotifyInstallmentRefundResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotifyInstallmentRefundResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotifyInstallmentRefundResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotifyInstallmentRefundResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotifyInstallmentRefundResponseValidationError) ErrorName() string {
	return "NotifyInstallmentRefundResponseValidationError"
}

// Error satisfies the builtin error interface
func (e NotifyInstallmentRefundResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotifyInstallmentRefundResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotifyInstallmentRefundResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotifyInstallmentRefundResponseValidationError{}

// Validate checks the field values on InstallmentBase with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InstallmentBase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallmentBase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstallmentBaseMultiError, or nil if none found.
func (m *InstallmentBase) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallmentBase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Tenor

	// no validation rules for ZpTransId

	// no validation rules for PartnerCode

	// no validation rules for PartnerInstId

	// no validation rules for InterestRate

	// no validation rules for PrincipalAmount

	if len(errors) > 0 {
		return InstallmentBaseMultiError(errors)
	}

	return nil
}

// InstallmentBaseMultiError is an error wrapping multiple validation errors
// returned by InstallmentBase.ValidateAll() if the designated constraints
// aren't met.
type InstallmentBaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallmentBaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallmentBaseMultiError) AllErrors() []error { return m }

// InstallmentBaseValidationError is the validation error returned by
// InstallmentBase.Validate if the designated constraints aren't met.
type InstallmentBaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallmentBaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallmentBaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallmentBaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallmentBaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallmentBaseValidationError) ErrorName() string { return "InstallmentBaseValidationError" }

// Error satisfies the builtin error interface
func (e InstallmentBaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallmentBase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallmentBaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallmentBaseValidationError{}

// Validate checks the field values on InstallmentData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InstallmentData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallmentData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstallmentDataMultiError, or nil if none found.
func (m *InstallmentData) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallmentData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Tenure

	// no validation rules for Status

	// no validation rules for PrincipalAmount

	// no validation rules for InterestAmount

	// no validation rules for PenaltyAmount

	// no validation rules for TotalAmountDue

	// no validation rules for TotalPaidAmount

	// no validation rules for TotalRemainingAmount

	if len(errors) > 0 {
		return InstallmentDataMultiError(errors)
	}

	return nil
}

// InstallmentDataMultiError is an error wrapping multiple validation errors
// returned by InstallmentData.ValidateAll() if the designated constraints
// aren't met.
type InstallmentDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallmentDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallmentDataMultiError) AllErrors() []error { return m }

// InstallmentDataValidationError is the validation error returned by
// InstallmentData.Validate if the designated constraints aren't met.
type InstallmentDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallmentDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallmentDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallmentDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallmentDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallmentDataValidationError) ErrorName() string { return "InstallmentDataValidationError" }

// Error satisfies the builtin error interface
func (e InstallmentDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallmentData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallmentDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallmentDataValidationError{}

// Validate checks the field values on InstallmentRefund with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InstallmentRefund) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InstallmentRefund with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InstallmentRefundMultiError, or nil if none found.
func (m *InstallmentRefund) ValidateAll() error {
	return m.validate(true)
}

func (m *InstallmentRefund) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ZpTransId

	// no validation rules for Version

	// no validation rules for NetRefundAmount

	// no validation rules for TotalRefundAmount

	// no validation rules for UserTopupAmount

	// no validation rules for UserTopupRequired

	if len(errors) > 0 {
		return InstallmentRefundMultiError(errors)
	}

	return nil
}

// InstallmentRefundMultiError is an error wrapping multiple validation errors
// returned by InstallmentRefund.ValidateAll() if the designated constraints
// aren't met.
type InstallmentRefundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstallmentRefundMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstallmentRefundMultiError) AllErrors() []error { return m }

// InstallmentRefundValidationError is the validation error returned by
// InstallmentRefund.Validate if the designated constraints aren't met.
type InstallmentRefundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstallmentRefundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstallmentRefundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstallmentRefundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstallmentRefundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstallmentRefundValidationError) ErrorName() string {
	return "InstallmentRefundValidationError"
}

// Error satisfies the builtin error interface
func (e InstallmentRefundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstallmentRefund.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstallmentRefundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstallmentRefundValidationError{}

// Validate checks the field values on BasePlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BasePlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BasePlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BasePlanMultiError, or nil
// if none found.
func (m *BasePlan) ValidateAll() error {
	return m.validate(true)
}

func (m *BasePlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenorNumber

	// no validation rules for EmiAmount

	// no validation rules for TotalAmount

	// no validation rules for PrincipalAmount

	// no validation rules for Status

	if m.PlanKey != nil {
		// no validation rules for PlanKey
	}

	if len(errors) > 0 {
		return BasePlanMultiError(errors)
	}

	return nil
}

// BasePlanMultiError is an error wrapping multiple validation errors returned
// by BasePlan.ValidateAll() if the designated constraints aren't met.
type BasePlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BasePlanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BasePlanMultiError) AllErrors() []error { return m }

// BasePlanValidationError is the validation error returned by
// BasePlan.Validate if the designated constraints aren't met.
type BasePlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BasePlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BasePlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BasePlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BasePlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BasePlanValidationError) ErrorName() string { return "BasePlanValidationError" }

// Error satisfies the builtin error interface
func (e BasePlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBasePlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BasePlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BasePlanValidationError{}

// Validate checks the field values on FullPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FullPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FullPlanMultiError, or nil
// if none found.
func (m *FullPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *FullPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanKey

	if all {
		switch v := interface{}(m.GetPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullPlanValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullPlanValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullPlanValidationError{
				field:  "PlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCostInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullPlanValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullPlanValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCostInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullPlanValidationError{
				field:  "CostInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanDetailUrl

	if len(errors) > 0 {
		return FullPlanMultiError(errors)
	}

	return nil
}

// FullPlanMultiError is an error wrapping multiple validation errors returned
// by FullPlan.ValidateAll() if the designated constraints aren't met.
type FullPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullPlanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullPlanMultiError) AllErrors() []error { return m }

// FullPlanValidationError is the validation error returned by
// FullPlan.Validate if the designated constraints aren't met.
type FullPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullPlanValidationError) ErrorName() string { return "FullPlanValidationError" }

// Error satisfies the builtin error interface
func (e FullPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullPlanValidationError{}

// Validate checks the field values on CostInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CostInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CostInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CostInfoMultiError, or nil
// if none found.
func (m *CostInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CostInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalCostAmount

	// no validation rules for TotalFeeAmount

	// no validation rules for PlatformFeeAmount

	// no validation rules for ConversionFeeAmount

	// no validation rules for InterestAmount

	for idx, item := range m.GetListFeeInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CostInfoValidationError{
						field:  fmt.Sprintf("ListFeeInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CostInfoValidationError{
						field:  fmt.Sprintf("ListFeeInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CostInfoValidationError{
					field:  fmt.Sprintf("ListFeeInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FeeExplanation

	if len(errors) > 0 {
		return CostInfoMultiError(errors)
	}

	return nil
}

// CostInfoMultiError is an error wrapping multiple validation errors returned
// by CostInfo.ValidateAll() if the designated constraints aren't met.
type CostInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CostInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CostInfoMultiError) AllErrors() []error { return m }

// CostInfoValidationError is the validation error returned by
// CostInfo.Validate if the designated constraints aren't met.
type CostInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CostInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CostInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CostInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CostInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CostInfoValidationError) ErrorName() string { return "CostInfoValidationError" }

// Error satisfies the builtin error interface
func (e CostInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCostInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CostInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CostInfoValidationError{}

// Validate checks the field values on PlanRepaymentSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlanRepaymentSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanRepaymentSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlanRepaymentScheduleMultiError, or nil if none found.
func (m *PlanRepaymentSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanRepaymentSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amount

	// no validation rules for DueDate

	// no validation rules for InstallmentNumber

	if len(errors) > 0 {
		return PlanRepaymentScheduleMultiError(errors)
	}

	return nil
}

// PlanRepaymentScheduleMultiError is an error wrapping multiple validation
// errors returned by PlanRepaymentSchedule.ValidateAll() if the designated
// constraints aren't met.
type PlanRepaymentScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanRepaymentScheduleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanRepaymentScheduleMultiError) AllErrors() []error { return m }

// PlanRepaymentScheduleValidationError is the validation error returned by
// PlanRepaymentSchedule.Validate if the designated constraints aren't met.
type PlanRepaymentScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanRepaymentScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanRepaymentScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanRepaymentScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanRepaymentScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanRepaymentScheduleValidationError) ErrorName() string {
	return "PlanRepaymentScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e PlanRepaymentScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanRepaymentSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanRepaymentScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanRepaymentScheduleValidationError{}

// Validate checks the field values on RepaymentSchedule with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RepaymentSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RepaymentSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RepaymentScheduleMultiError, or nil if none found.
func (m *RepaymentSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *RepaymentSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SeqNo

	// no validation rules for Status

	// no validation rules for DueDate

	// no validation rules for DueAmount

	// no validation rules for PenaltyAmount

	// no validation rules for TotalDueAmount

	// no validation rules for TotalPaidAmount

	// no validation rules for TotalRemainingAmount

	if len(errors) > 0 {
		return RepaymentScheduleMultiError(errors)
	}

	return nil
}

// RepaymentScheduleMultiError is an error wrapping multiple validation errors
// returned by RepaymentSchedule.ValidateAll() if the designated constraints
// aren't met.
type RepaymentScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RepaymentScheduleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RepaymentScheduleMultiError) AllErrors() []error { return m }

// RepaymentScheduleValidationError is the validation error returned by
// RepaymentSchedule.Validate if the designated constraints aren't met.
type RepaymentScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RepaymentScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RepaymentScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RepaymentScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RepaymentScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RepaymentScheduleValidationError) ErrorName() string {
	return "RepaymentScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e RepaymentScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRepaymentSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RepaymentScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RepaymentScheduleValidationError{}

// Validate checks the field values on EarlyDischargeBase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EarlyDischargeBase) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarlyDischargeBase with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EarlyDischargeBaseMultiError, or nil if none found.
func (m *EarlyDischargeBase) ValidateAll() error {
	return m.validate(true)
}

func (m *EarlyDischargeBase) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Status

	if m.Allowed != nil {
		// no validation rules for Allowed
	}

	if m.InSession != nil {
		// no validation rules for InSession
	}

	if m.SessionInfo != nil {

		if all {
			switch v := interface{}(m.GetSessionInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EarlyDischargeBaseValidationError{
						field:  "SessionInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EarlyDischargeBaseValidationError{
						field:  "SessionInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSessionInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EarlyDischargeBaseValidationError{
					field:  "SessionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EarlyDischargeBaseMultiError(errors)
	}

	return nil
}

// EarlyDischargeBaseMultiError is an error wrapping multiple validation errors
// returned by EarlyDischargeBase.ValidateAll() if the designated constraints
// aren't met.
type EarlyDischargeBaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarlyDischargeBaseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarlyDischargeBaseMultiError) AllErrors() []error { return m }

// EarlyDischargeBaseValidationError is the validation error returned by
// EarlyDischargeBase.Validate if the designated constraints aren't met.
type EarlyDischargeBaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarlyDischargeBaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarlyDischargeBaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarlyDischargeBaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarlyDischargeBaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarlyDischargeBaseValidationError) ErrorName() string {
	return "EarlyDischargeBaseValidationError"
}

// Error satisfies the builtin error interface
func (e EarlyDischargeBaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarlyDischargeBase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarlyDischargeBaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarlyDischargeBaseValidationError{}

// Validate checks the field values on EarlyDischargeDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EarlyDischargeDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarlyDischargeDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EarlyDischargeDetailMultiError, or nil if none found.
func (m *EarlyDischargeDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *EarlyDischargeDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalDischargeAmount

	// no validation rules for EarlyDischargeAmount

	// no validation rules for EarlyDischargeFee

	// no validation rules for TotalOutstandingAmount

	if m.OutstandingPrincipalAmount != nil {
		// no validation rules for OutstandingPrincipalAmount
	}

	if m.OutstandingInterestAmount != nil {
		// no validation rules for OutstandingInterestAmount
	}

	if m.OutstandingPenaltyAmount != nil {
		// no validation rules for OutstandingPenaltyAmount
	}

	if len(errors) > 0 {
		return EarlyDischargeDetailMultiError(errors)
	}

	return nil
}

// EarlyDischargeDetailMultiError is an error wrapping multiple validation
// errors returned by EarlyDischargeDetail.ValidateAll() if the designated
// constraints aren't met.
type EarlyDischargeDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarlyDischargeDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarlyDischargeDetailMultiError) AllErrors() []error { return m }

// EarlyDischargeDetailValidationError is the validation error returned by
// EarlyDischargeDetail.Validate if the designated constraints aren't met.
type EarlyDischargeDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarlyDischargeDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarlyDischargeDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarlyDischargeDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarlyDischargeDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarlyDischargeDetailValidationError) ErrorName() string {
	return "EarlyDischargeDetailValidationError"
}

// Error satisfies the builtin error interface
func (e EarlyDischargeDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarlyDischargeDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarlyDischargeDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarlyDischargeDetailValidationError{}

// Validate checks the field values on EarlyDischarge with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EarlyDischarge) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarlyDischarge with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EarlyDischargeMultiError,
// or nil if none found.
func (m *EarlyDischarge) ValidateAll() error {
	return m.validate(true)
}

func (m *EarlyDischarge) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EarlyDischargeValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EarlyDischargeValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EarlyDischargeValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Detail != nil {

		if all {
			switch v := interface{}(m.GetDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EarlyDischargeValidationError{
						field:  "Detail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EarlyDischargeValidationError{
						field:  "Detail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EarlyDischargeValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EarlyDischargeMultiError(errors)
	}

	return nil
}

// EarlyDischargeMultiError is an error wrapping multiple validation errors
// returned by EarlyDischarge.ValidateAll() if the designated constraints
// aren't met.
type EarlyDischargeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarlyDischargeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarlyDischargeMultiError) AllErrors() []error { return m }

// EarlyDischargeValidationError is the validation error returned by
// EarlyDischarge.Validate if the designated constraints aren't met.
type EarlyDischargeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarlyDischargeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarlyDischargeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarlyDischargeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarlyDischargeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarlyDischargeValidationError) ErrorName() string { return "EarlyDischargeValidationError" }

// Error satisfies the builtin error interface
func (e EarlyDischargeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarlyDischarge.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarlyDischargeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarlyDischargeValidationError{}

// Validate checks the field values on EarlyDischargeForRefund with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EarlyDischargeForRefund) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EarlyDischargeForRefund with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EarlyDischargeForRefundMultiError, or nil if none found.
func (m *EarlyDischargeForRefund) ValidateAll() error {
	return m.validate(true)
}

func (m *EarlyDischargeForRefund) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for DischargeAmount

	// no validation rules for SessionAvailable

	if len(errors) > 0 {
		return EarlyDischargeForRefundMultiError(errors)
	}

	return nil
}

// EarlyDischargeForRefundMultiError is an error wrapping multiple validation
// errors returned by EarlyDischargeForRefund.ValidateAll() if the designated
// constraints aren't met.
type EarlyDischargeForRefundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EarlyDischargeForRefundMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EarlyDischargeForRefundMultiError) AllErrors() []error { return m }

// EarlyDischargeForRefundValidationError is the validation error returned by
// EarlyDischargeForRefund.Validate if the designated constraints aren't met.
type EarlyDischargeForRefundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EarlyDischargeForRefundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EarlyDischargeForRefundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EarlyDischargeForRefundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EarlyDischargeForRefundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EarlyDischargeForRefundValidationError) ErrorName() string {
	return "EarlyDischargeForRefundValidationError"
}

// Error satisfies the builtin error interface
func (e EarlyDischargeForRefundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEarlyDischargeForRefund.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EarlyDischargeForRefundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EarlyDischargeForRefundValidationError{}

// Validate checks the field values on InteractionGuide with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InteractionGuide) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InteractionGuide with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InteractionGuideMultiError, or nil if none found.
func (m *InteractionGuide) ValidateAll() error {
	return m.validate(true)
}

func (m *InteractionGuide) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InteractionGuideValidationError{
					field:  "Notice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InteractionGuideValidationError{
				field:  "Notice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InteractionGuideMultiError(errors)
	}

	return nil
}

// InteractionGuideMultiError is an error wrapping multiple validation errors
// returned by InteractionGuide.ValidateAll() if the designated constraints
// aren't met.
type InteractionGuideMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InteractionGuideMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InteractionGuideMultiError) AllErrors() []error { return m }

// InteractionGuideValidationError is the validation error returned by
// InteractionGuide.Validate if the designated constraints aren't met.
type InteractionGuideValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InteractionGuideValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InteractionGuideValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InteractionGuideValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InteractionGuideValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InteractionGuideValidationError) ErrorName() string { return "InteractionGuideValidationError" }

// Error satisfies the builtin error interface
func (e InteractionGuideValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInteractionGuide.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InteractionGuideValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InteractionGuideValidationError{}

// Validate checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Action) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Action with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ActionMultiError, or nil if none found.
func (m *Action) ValidateAll() error {
	return m.validate(true)
}

func (m *Action) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Text

	// no validation rules for ZpiUrl

	// no validation rules for ZpaUrl

	if len(errors) > 0 {
		return ActionMultiError(errors)
	}

	return nil
}

// ActionMultiError is an error wrapping multiple validation errors returned by
// Action.ValidateAll() if the designated constraints aren't met.
type ActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionMultiError) AllErrors() []error { return m }

// ActionValidationError is the validation error returned by Action.Validate if
// the designated constraints aren't met.
type ActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionValidationError) ErrorName() string { return "ActionValidationError" }

// Error satisfies the builtin error interface
func (e ActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionValidationError{}

// Validate checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Message) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MessageMultiError, or nil if none found.
func (m *Message) ValidateAll() error {
	return m.validate(true)
}

func (m *Message) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for Color

	if len(errors) > 0 {
		return MessageMultiError(errors)
	}

	return nil
}

// MessageMultiError is an error wrapping multiple validation errors returned
// by Message.ValidateAll() if the designated constraints aren't met.
type MessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessageMultiError) AllErrors() []error { return m }

// MessageValidationError is the validation error returned by Message.Validate
// if the designated constraints aren't met.
type MessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessageValidationError) ErrorName() string { return "MessageValidationError" }

// Error satisfies the builtin error interface
func (e MessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessageValidationError{}

// Validate checks the field values on Fee with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Fee) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Fee with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FeeMultiError, or nil if none found.
func (m *Fee) ValidateAll() error {
	return m.validate(true)
}

func (m *Fee) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeeType

	// no validation rules for FeeAmount

	// no validation rules for Message

	// no validation rules for MessageEn

	if len(errors) > 0 {
		return FeeMultiError(errors)
	}

	return nil
}

// FeeMultiError is an error wrapping multiple validation errors returned by
// Fee.ValidateAll() if the designated constraints aren't met.
type FeeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeeMultiError) AllErrors() []error { return m }

// FeeValidationError is the validation error returned by Fee.Validate if the
// designated constraints aren't met.
type FeeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeeValidationError) ErrorName() string { return "FeeValidationError" }

// Error satisfies the builtin error interface
func (e FeeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFee.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeeValidationError{}

// Validate checks the field values on Session with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Session) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Session with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SessionMultiError, or nil if none found.
func (m *Session) ValidateAll() error {
	return m.validate(true)
}

func (m *Session) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StartTime

	// no validation rules for EndTime

	if len(errors) > 0 {
		return SessionMultiError(errors)
	}

	return nil
}

// SessionMultiError is an error wrapping multiple validation errors returned
// by Session.ValidateAll() if the designated constraints aren't met.
type SessionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SessionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SessionMultiError) AllErrors() []error { return m }

// SessionValidationError is the validation error returned by Session.Validate
// if the designated constraints aren't met.
type SessionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SessionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SessionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SessionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SessionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SessionValidationError) ErrorName() string { return "SessionValidationError" }

// Error satisfies the builtin error interface
func (e SessionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSession.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SessionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SessionValidationError{}

// Validate checks the field values on ListClientEligiblePlansResponse_Plan
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListClientEligiblePlansResponse_Plan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientEligiblePlansResponse_Plan
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListClientEligiblePlansResponse_PlanMultiError, or nil if none found.
func (m *ListClientEligiblePlansResponse_Plan) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientEligiblePlansResponse_Plan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanKey

	if all {
		switch v := interface{}(m.GetPlanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
					field:  "PlanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClientEligiblePlansResponse_PlanValidationError{
				field:  "PlanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCostInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
					field:  "CostInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCostInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClientEligiblePlansResponse_PlanValidationError{
				field:  "CostInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsPopular

	// no validation rules for PlanDetailUrl

	for idx, item := range m.GetScheduledRepayments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
						field:  fmt.Sprintf("ScheduledRepayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientEligiblePlansResponse_PlanValidationError{
						field:  fmt.Sprintf("ScheduledRepayments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientEligiblePlansResponse_PlanValidationError{
					field:  fmt.Sprintf("ScheduledRepayments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClientEligiblePlansResponse_PlanMultiError(errors)
	}

	return nil
}

// ListClientEligiblePlansResponse_PlanMultiError is an error wrapping multiple
// validation errors returned by
// ListClientEligiblePlansResponse_Plan.ValidateAll() if the designated
// constraints aren't met.
type ListClientEligiblePlansResponse_PlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientEligiblePlansResponse_PlanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientEligiblePlansResponse_PlanMultiError) AllErrors() []error { return m }

// ListClientEligiblePlansResponse_PlanValidationError is the validation error
// returned by ListClientEligiblePlansResponse_Plan.Validate if the designated
// constraints aren't met.
type ListClientEligiblePlansResponse_PlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientEligiblePlansResponse_PlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientEligiblePlansResponse_PlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientEligiblePlansResponse_PlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientEligiblePlansResponse_PlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientEligiblePlansResponse_PlanValidationError) ErrorName() string {
	return "ListClientEligiblePlansResponse_PlanValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientEligiblePlansResponse_PlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientEligiblePlansResponse_Plan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientEligiblePlansResponse_PlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientEligiblePlansResponse_PlanValidationError{}

// Validate checks the field values on GetPaymentPlanDetailRequest_OrderInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaymentPlanDetailRequest_OrderInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentPlanDetailRequest_OrderInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaymentPlanDetailRequest_OrderInfoMultiError, or nil if none found.
func (m *GetPaymentPlanDetailRequest_OrderInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentPlanDetailRequest_OrderInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAppId() <= 0 {
		err := GetPaymentPlanDetailRequest_OrderInfoValidationError{
			field:  "AppId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppTransId()) < 1 {
		err := GetPaymentPlanDetailRequest_OrderInfoValidationError{
			field:  "AppTransId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetChargeAmount() <= 0 {
		err := GetPaymentPlanDetailRequest_OrderInfoValidationError{
			field:  "ChargeAmount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPaymentPlanDetailRequest_OrderInfoMultiError(errors)
	}

	return nil
}

// GetPaymentPlanDetailRequest_OrderInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetPaymentPlanDetailRequest_OrderInfo.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentPlanDetailRequest_OrderInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentPlanDetailRequest_OrderInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentPlanDetailRequest_OrderInfoMultiError) AllErrors() []error { return m }

// GetPaymentPlanDetailRequest_OrderInfoValidationError is the validation error
// returned by GetPaymentPlanDetailRequest_OrderInfo.Validate if the
// designated constraints aren't met.
type GetPaymentPlanDetailRequest_OrderInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) ErrorName() string {
	return "GetPaymentPlanDetailRequest_OrderInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentPlanDetailRequest_OrderInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentPlanDetailRequest_OrderInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentPlanDetailRequest_OrderInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentPlanDetailRequest_OrderInfoValidationError{}
