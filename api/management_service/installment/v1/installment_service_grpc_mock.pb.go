// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: management_service/installment/v1/installment_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockInstallmentClient is a mock of InstallmentClient interface.
type MockInstallmentClient struct {
	ctrl     *gomock.Controller
	recorder *MockInstallmentClientMockRecorder
}

// MockInstallmentClientMockRecorder is the mock recorder for MockInstallmentClient.
type MockInstallmentClientMockRecorder struct {
	mock *MockInstallmentClient
}

// NewMockInstallmentClient creates a new mock instance.
func NewMockInstallmentClient(ctrl *gomock.Controller) *MockInstallmentClient {
	mock := &MockInstallmentClient{ctrl: ctrl}
	mock.recorder = &MockInstallmentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstallmentClient) EXPECT() *MockInstallmentClientMockRecorder {
	return m.recorder
}

// AcceptClientPlan mocks base method.
func (m *MockInstallmentClient) AcceptClientPlan(ctx context.Context, in *AcceptClientPlanRequest, opts ...grpc.CallOption) (*AcceptClientPlanResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptClientPlan", varargs...)
	ret0, _ := ret[0].(*AcceptClientPlanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptClientPlan indicates an expected call of AcceptClientPlan.
func (mr *MockInstallmentClientMockRecorder) AcceptClientPlan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptClientPlan", reflect.TypeOf((*MockInstallmentClient)(nil).AcceptClientPlan), varargs...)
}

// GetClientEarlyDischarge mocks base method.
func (m *MockInstallmentClient) GetClientEarlyDischarge(ctx context.Context, in *GetClientEarlyDischargeRequest, opts ...grpc.CallOption) (*GetClientEarlyDischargeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientEarlyDischarge", varargs...)
	ret0, _ := ret[0].(*GetClientEarlyDischargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientEarlyDischarge indicates an expected call of GetClientEarlyDischarge.
func (mr *MockInstallmentClientMockRecorder) GetClientEarlyDischarge(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientEarlyDischarge", reflect.TypeOf((*MockInstallmentClient)(nil).GetClientEarlyDischarge), varargs...)
}

// GetClientInstallment mocks base method.
func (m *MockInstallmentClient) GetClientInstallment(ctx context.Context, in *GetClientInstallmentRequest, opts ...grpc.CallOption) (*GetClientInstallmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientInstallment", varargs...)
	ret0, _ := ret[0].(*GetClientInstallmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientInstallment indicates an expected call of GetClientInstallment.
func (mr *MockInstallmentClientMockRecorder) GetClientInstallment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientInstallment", reflect.TypeOf((*MockInstallmentClient)(nil).GetClientInstallment), varargs...)
}

// GetClientPlanDetail mocks base method.
func (m *MockInstallmentClient) GetClientPlanDetail(ctx context.Context, in *GetClientPlanDetailRequest, opts ...grpc.CallOption) (*GetClientPlanDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientPlanDetail", varargs...)
	ret0, _ := ret[0].(*GetClientPlanDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientPlanDetail indicates an expected call of GetClientPlanDetail.
func (mr *MockInstallmentClientMockRecorder) GetClientPlanDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientPlanDetail", reflect.TypeOf((*MockInstallmentClient)(nil).GetClientPlanDetail), varargs...)
}

// GetEarlyDischargeRefund mocks base method.
func (m *MockInstallmentClient) GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest, opts ...grpc.CallOption) (*GetEarlyDischargeRefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEarlyDischargeRefund", varargs...)
	ret0, _ := ret[0].(*GetEarlyDischargeRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeRefund indicates an expected call of GetEarlyDischargeRefund.
func (mr *MockInstallmentClientMockRecorder) GetEarlyDischargeRefund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeRefund", reflect.TypeOf((*MockInstallmentClient)(nil).GetEarlyDischargeRefund), varargs...)
}

// GetInstallmentStatus mocks base method.
func (m *MockInstallmentClient) GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest, opts ...grpc.CallOption) (*GetInstallmentStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInstallmentStatus", varargs...)
	ret0, _ := ret[0].(*GetInstallmentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentStatus indicates an expected call of GetInstallmentStatus.
func (mr *MockInstallmentClientMockRecorder) GetInstallmentStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentStatus", reflect.TypeOf((*MockInstallmentClient)(nil).GetInstallmentStatus), varargs...)
}

// GetPaymentPlanDetail mocks base method.
func (m *MockInstallmentClient) GetPaymentPlanDetail(ctx context.Context, in *GetPaymentPlanDetailRequest, opts ...grpc.CallOption) (*GetPaymentPlanDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentPlanDetail", varargs...)
	ret0, _ := ret[0].(*GetPaymentPlanDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentPlanDetail indicates an expected call of GetPaymentPlanDetail.
func (mr *MockInstallmentClientMockRecorder) GetPaymentPlanDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentPlanDetail", reflect.TypeOf((*MockInstallmentClient)(nil).GetPaymentPlanDetail), varargs...)
}

// ListClientEligiblePlans mocks base method.
func (m *MockInstallmentClient) ListClientEligiblePlans(ctx context.Context, in *ListClientEligiblePlansRequest, opts ...grpc.CallOption) (*ListClientEligiblePlansResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListClientEligiblePlans", varargs...)
	ret0, _ := ret[0].(*ListClientEligiblePlansResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientEligiblePlans indicates an expected call of ListClientEligiblePlans.
func (mr *MockInstallmentClientMockRecorder) ListClientEligiblePlans(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientEligiblePlans", reflect.TypeOf((*MockInstallmentClient)(nil).ListClientEligiblePlans), varargs...)
}

// NotifyInstallmentRefund mocks base method.
func (m *MockInstallmentClient) NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund, opts ...grpc.CallOption) (*NotifyInstallmentRefundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NotifyInstallmentRefund", varargs...)
	ret0, _ := ret[0].(*NotifyInstallmentRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyInstallmentRefund indicates an expected call of NotifyInstallmentRefund.
func (mr *MockInstallmentClientMockRecorder) NotifyInstallmentRefund(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyInstallmentRefund", reflect.TypeOf((*MockInstallmentClient)(nil).NotifyInstallmentRefund), varargs...)
}

// MockInstallmentServer is a mock of InstallmentServer interface.
type MockInstallmentServer struct {
	ctrl     *gomock.Controller
	recorder *MockInstallmentServerMockRecorder
}

// MockInstallmentServerMockRecorder is the mock recorder for MockInstallmentServer.
type MockInstallmentServerMockRecorder struct {
	mock *MockInstallmentServer
}

// NewMockInstallmentServer creates a new mock instance.
func NewMockInstallmentServer(ctrl *gomock.Controller) *MockInstallmentServer {
	mock := &MockInstallmentServer{ctrl: ctrl}
	mock.recorder = &MockInstallmentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInstallmentServer) EXPECT() *MockInstallmentServerMockRecorder {
	return m.recorder
}

// AcceptClientPlan mocks base method.
func (m *MockInstallmentServer) AcceptClientPlan(ctx context.Context, in *AcceptClientPlanRequest) (*AcceptClientPlanResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptClientPlan", ctx, in)
	ret0, _ := ret[0].(*AcceptClientPlanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptClientPlan indicates an expected call of AcceptClientPlan.
func (mr *MockInstallmentServerMockRecorder) AcceptClientPlan(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptClientPlan", reflect.TypeOf((*MockInstallmentServer)(nil).AcceptClientPlan), ctx, in)
}

// GetClientEarlyDischarge mocks base method.
func (m *MockInstallmentServer) GetClientEarlyDischarge(ctx context.Context, in *GetClientEarlyDischargeRequest) (*GetClientEarlyDischargeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientEarlyDischarge", ctx, in)
	ret0, _ := ret[0].(*GetClientEarlyDischargeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientEarlyDischarge indicates an expected call of GetClientEarlyDischarge.
func (mr *MockInstallmentServerMockRecorder) GetClientEarlyDischarge(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientEarlyDischarge", reflect.TypeOf((*MockInstallmentServer)(nil).GetClientEarlyDischarge), ctx, in)
}

// GetClientInstallment mocks base method.
func (m *MockInstallmentServer) GetClientInstallment(ctx context.Context, in *GetClientInstallmentRequest) (*GetClientInstallmentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientInstallment", ctx, in)
	ret0, _ := ret[0].(*GetClientInstallmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientInstallment indicates an expected call of GetClientInstallment.
func (mr *MockInstallmentServerMockRecorder) GetClientInstallment(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientInstallment", reflect.TypeOf((*MockInstallmentServer)(nil).GetClientInstallment), ctx, in)
}

// GetClientPlanDetail mocks base method.
func (m *MockInstallmentServer) GetClientPlanDetail(ctx context.Context, in *GetClientPlanDetailRequest) (*GetClientPlanDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientPlanDetail", ctx, in)
	ret0, _ := ret[0].(*GetClientPlanDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientPlanDetail indicates an expected call of GetClientPlanDetail.
func (mr *MockInstallmentServerMockRecorder) GetClientPlanDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientPlanDetail", reflect.TypeOf((*MockInstallmentServer)(nil).GetClientPlanDetail), ctx, in)
}

// GetEarlyDischargeRefund mocks base method.
func (m *MockInstallmentServer) GetEarlyDischargeRefund(ctx context.Context, in *GetEarlyDischargeRefundRequest) (*GetEarlyDischargeRefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarlyDischargeRefund", ctx, in)
	ret0, _ := ret[0].(*GetEarlyDischargeRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarlyDischargeRefund indicates an expected call of GetEarlyDischargeRefund.
func (mr *MockInstallmentServerMockRecorder) GetEarlyDischargeRefund(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarlyDischargeRefund", reflect.TypeOf((*MockInstallmentServer)(nil).GetEarlyDischargeRefund), ctx, in)
}

// GetInstallmentStatus mocks base method.
func (m *MockInstallmentServer) GetInstallmentStatus(ctx context.Context, in *GetInstallmentStatusRequest) (*GetInstallmentStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstallmentStatus", ctx, in)
	ret0, _ := ret[0].(*GetInstallmentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstallmentStatus indicates an expected call of GetInstallmentStatus.
func (mr *MockInstallmentServerMockRecorder) GetInstallmentStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstallmentStatus", reflect.TypeOf((*MockInstallmentServer)(nil).GetInstallmentStatus), ctx, in)
}

// GetPaymentPlanDetail mocks base method.
func (m *MockInstallmentServer) GetPaymentPlanDetail(ctx context.Context, in *GetPaymentPlanDetailRequest) (*GetPaymentPlanDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentPlanDetail", ctx, in)
	ret0, _ := ret[0].(*GetPaymentPlanDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentPlanDetail indicates an expected call of GetPaymentPlanDetail.
func (mr *MockInstallmentServerMockRecorder) GetPaymentPlanDetail(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentPlanDetail", reflect.TypeOf((*MockInstallmentServer)(nil).GetPaymentPlanDetail), ctx, in)
}

// ListClientEligiblePlans mocks base method.
func (m *MockInstallmentServer) ListClientEligiblePlans(ctx context.Context, in *ListClientEligiblePlansRequest) (*ListClientEligiblePlansResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClientEligiblePlans", ctx, in)
	ret0, _ := ret[0].(*ListClientEligiblePlansResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientEligiblePlans indicates an expected call of ListClientEligiblePlans.
func (mr *MockInstallmentServerMockRecorder) ListClientEligiblePlans(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientEligiblePlans", reflect.TypeOf((*MockInstallmentServer)(nil).ListClientEligiblePlans), ctx, in)
}

// NotifyInstallmentRefund mocks base method.
func (m *MockInstallmentServer) NotifyInstallmentRefund(ctx context.Context, in *InstallmentRefund) (*NotifyInstallmentRefundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyInstallmentRefund", ctx, in)
	ret0, _ := ret[0].(*NotifyInstallmentRefundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyInstallmentRefund indicates an expected call of NotifyInstallmentRefund.
func (mr *MockInstallmentServerMockRecorder) NotifyInstallmentRefund(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyInstallmentRefund", reflect.TypeOf((*MockInstallmentServer)(nil).NotifyInstallmentRefund), ctx, in)
}
