// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: management_service/installment/v1/installment_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlanStatus int32

const (
	// Plan status is unspecified
	PlanStatus_PLAN_STATUS_UNSPECIFIED PlanStatus = 0
	// Plan status is active
	PlanStatus_PLAN_STATUS_ACTIVE PlanStatus = 1
	// Plan status is inactive
	PlanStatus_PLAN_STATUS_INACTIVE PlanStatus = 2
	// Plan status is invalid
	PlanStatus_PLAN_STATUS_INVALID PlanStatus = 3
)

// Enum value maps for PlanStatus.
var (
	PlanStatus_name = map[int32]string{
		0: "PLAN_STATUS_UNSPECIFIED",
		1: "PLAN_STATUS_ACTIVE",
		2: "PLAN_STATUS_INACTIVE",
		3: "PLAN_STATUS_INVALID",
	}
	PlanStatus_value = map[string]int32{
		"PLAN_STATUS_UNSPECIFIED": 0,
		"PLAN_STATUS_ACTIVE":      1,
		"PLAN_STATUS_INACTIVE":    2,
		"PLAN_STATUS_INVALID":     3,
	}
)

func (x PlanStatus) Enum() *PlanStatus {
	p := new(PlanStatus)
	*p = x
	return p
}

func (x PlanStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlanStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[0].Descriptor()
}

func (PlanStatus) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[0]
}

func (x PlanStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlanStatus.Descriptor instead.
func (PlanStatus) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{0}
}

type RepayStatus int32

const (
	// Repay status is unspecified
	RepayStatus_REPAY_STATUS_UNSPECIFIED RepayStatus = 0
	// Repay status is pending
	RepayStatus_REPAY_STATUS_PENDING RepayStatus = 1
	// Repay status is success
	RepayStatus_REPAY_STATUS_DUE RepayStatus = 2
	// Repay status is failed
	RepayStatus_REPAY_STATUS_PAID RepayStatus = 3
	// Repay status is overdue
	RepayStatus_REPAY_STATUS_OVERDUE RepayStatus = 4
)

// Enum value maps for RepayStatus.
var (
	RepayStatus_name = map[int32]string{
		0: "REPAY_STATUS_UNSPECIFIED",
		1: "REPAY_STATUS_PENDING",
		2: "REPAY_STATUS_DUE",
		3: "REPAY_STATUS_PAID",
		4: "REPAY_STATUS_OVERDUE",
	}
	RepayStatus_value = map[string]int32{
		"REPAY_STATUS_UNSPECIFIED": 0,
		"REPAY_STATUS_PENDING":     1,
		"REPAY_STATUS_DUE":         2,
		"REPAY_STATUS_PAID":        3,
		"REPAY_STATUS_OVERDUE":     4,
	}
)

func (x RepayStatus) Enum() *RepayStatus {
	p := new(RepayStatus)
	*p = x
	return p
}

func (x RepayStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepayStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[1].Descriptor()
}

func (RepayStatus) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[1]
}

func (x RepayStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepayStatus.Descriptor instead.
func (RepayStatus) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{1}
}

type InstallmentStatus int32

const (
	// Installment status is unspecified
	InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED InstallmentStatus = 0
	// Installment status is init
	InstallmentStatus_INSTALLMENT_STATUS_INIT InstallmentStatus = 1
	// Installment status is open
	InstallmentStatus_INSTALLMENT_STATUS_OPEN InstallmentStatus = 2
	// Installment status is closed
	InstallmentStatus_INSTALLMENT_STATUS_CLOSED InstallmentStatus = 3
)

// Enum value maps for InstallmentStatus.
var (
	InstallmentStatus_name = map[int32]string{
		0: "INSTALLMENT_STATUS_UNSPECIFIED",
		1: "INSTALLMENT_STATUS_INIT",
		2: "INSTALLMENT_STATUS_OPEN",
		3: "INSTALLMENT_STATUS_CLOSED",
	}
	InstallmentStatus_value = map[string]int32{
		"INSTALLMENT_STATUS_UNSPECIFIED": 0,
		"INSTALLMENT_STATUS_INIT":        1,
		"INSTALLMENT_STATUS_OPEN":        2,
		"INSTALLMENT_STATUS_CLOSED":      3,
	}
)

func (x InstallmentStatus) Enum() *InstallmentStatus {
	p := new(InstallmentStatus)
	*p = x
	return p
}

func (x InstallmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstallmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[2].Descriptor()
}

func (InstallmentStatus) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[2]
}

func (x InstallmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstallmentStatus.Descriptor instead.
func (InstallmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{2}
}

type EarlyDischargeKind int32

const (
	// Early discharge type is unspecified
	EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED EarlyDischargeKind = 0
	// Early discharge type is normal
	EarlyDischargeKind_EARLY_DISCHARGE_KIND_NORMAL EarlyDischargeKind = 1
	// Early discharge type is refund
	EarlyDischargeKind_EARLY_DISCHARGE_KIND_REFUND EarlyDischargeKind = 2
)

// Enum value maps for EarlyDischargeKind.
var (
	EarlyDischargeKind_name = map[int32]string{
		0: "EARLY_DISCHARGE_KIND_UNSPECIFIED",
		1: "EARLY_DISCHARGE_KIND_NORMAL",
		2: "EARLY_DISCHARGE_KIND_REFUND",
	}
	EarlyDischargeKind_value = map[string]int32{
		"EARLY_DISCHARGE_KIND_UNSPECIFIED": 0,
		"EARLY_DISCHARGE_KIND_NORMAL":      1,
		"EARLY_DISCHARGE_KIND_REFUND":      2,
	}
)

func (x EarlyDischargeKind) Enum() *EarlyDischargeKind {
	p := new(EarlyDischargeKind)
	*p = x
	return p
}

func (x EarlyDischargeKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EarlyDischargeKind) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[3].Descriptor()
}

func (EarlyDischargeKind) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[3]
}

func (x EarlyDischargeKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EarlyDischargeKind.Descriptor instead.
func (EarlyDischargeKind) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{3}
}

type EarlyDischargeStatus int32

const (
	// Early discharge status is unspecified
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED EarlyDischargeStatus = 0
	// Early discharge status is open
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_ELIGIBLE EarlyDischargeStatus = 1
	// Early discharge status is init
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_INELIGIBLE EarlyDischargeStatus = 2
	// Early discharge status is processing
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_PROCESSING EarlyDischargeStatus = 3
	// Early discharge status is closed
	EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_CLOSED EarlyDischargeStatus = 4
)

// Enum value maps for EarlyDischargeStatus.
var (
	EarlyDischargeStatus_name = map[int32]string{
		0: "EARLY_DISCHARGE_STATUS_UNSPECIFIED",
		1: "EARLY_DISCHARGE_STATUS_ELIGIBLE",
		2: "EARLY_DISCHARGE_STATUS_INELIGIBLE",
		3: "EARLY_DISCHARGE_STATUS_PROCESSING",
		4: "EARLY_DISCHARGE_STATUS_CLOSED",
	}
	EarlyDischargeStatus_value = map[string]int32{
		"EARLY_DISCHARGE_STATUS_UNSPECIFIED": 0,
		"EARLY_DISCHARGE_STATUS_ELIGIBLE":    1,
		"EARLY_DISCHARGE_STATUS_INELIGIBLE":  2,
		"EARLY_DISCHARGE_STATUS_PROCESSING":  3,
		"EARLY_DISCHARGE_STATUS_CLOSED":      4,
	}
)

func (x EarlyDischargeStatus) Enum() *EarlyDischargeStatus {
	p := new(EarlyDischargeStatus)
	*p = x
	return p
}

func (x EarlyDischargeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EarlyDischargeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[4].Descriptor()
}

func (EarlyDischargeStatus) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[4]
}

func (x EarlyDischargeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EarlyDischargeStatus.Descriptor instead.
func (EarlyDischargeStatus) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{4}
}

type ActionType int32

const (
	ActionType_ACTION_TYPE_UNSPECIFIED    ActionType = 0
	ActionType_ACTION_TYPE_PLAN_SELECTION ActionType = 1
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "ACTION_TYPE_PLAN_SELECTION",
	}
	ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED":    0,
		"ACTION_TYPE_PLAN_SELECTION": 1,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[5].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[5]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{5}
}

type FeeType int32

const (
	FeeType_FEE_TYPE_INVALID    FeeType = 0
	FeeType_FEE_TYPE_CONVERSION FeeType = 1
	FeeType_FEE_TYPE_PLATFORM   FeeType = 2
)

// Enum value maps for FeeType.
var (
	FeeType_name = map[int32]string{
		0: "FEE_TYPE_INVALID",
		1: "FEE_TYPE_CONVERSION",
		2: "FEE_TYPE_PLATFORM",
	}
	FeeType_value = map[string]int32{
		"FEE_TYPE_INVALID":    0,
		"FEE_TYPE_CONVERSION": 1,
		"FEE_TYPE_PLATFORM":   2,
	}
)

func (x FeeType) Enum() *FeeType {
	p := new(FeeType)
	*p = x
	return p
}

func (x FeeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeeType) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_installment_v1_installment_service_proto_enumTypes[6].Descriptor()
}

func (FeeType) Type() protoreflect.EnumType {
	return &file_management_service_installment_v1_installment_service_proto_enumTypes[6]
}

func (x FeeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeeType.Descriptor instead.
func (FeeType) EnumDescriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{6}
}

type ListClientEligiblePlansRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,proto3" json:"partner_code,omitempty"`
	AppId         int32                  `protobuf:"varint,2,opt,name=app_id,proto3" json:"app_id,omitempty"`
	AppTransId    string                 `protobuf:"bytes,3,opt,name=app_trans_id,proto3" json:"app_trans_id,omitempty"`
	ChargeAmount  int64                  `protobuf:"varint,4,opt,name=charge_amount,proto3" json:"charge_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientEligiblePlansRequest) Reset() {
	*x = ListClientEligiblePlansRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientEligiblePlansRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientEligiblePlansRequest) ProtoMessage() {}

func (x *ListClientEligiblePlansRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientEligiblePlansRequest.ProtoReflect.Descriptor instead.
func (*ListClientEligiblePlansRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListClientEligiblePlansRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *ListClientEligiblePlansRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *ListClientEligiblePlansRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *ListClientEligiblePlansRequest) GetChargeAmount() int64 {
	if x != nil {
		return x.ChargeAmount
	}
	return 0
}

type ListClientEligiblePlansResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of plans
	PlanOptions []*ListClientEligiblePlansResponse_Plan `protobuf:"bytes,1,rep,name=plan_options,proto3" json:"plan_options,omitempty"`
	// Plan key selected
	PlanSelected  string `protobuf:"bytes,2,opt,name=plan_selected,proto3" json:"plan_selected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientEligiblePlansResponse) Reset() {
	*x = ListClientEligiblePlansResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientEligiblePlansResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientEligiblePlansResponse) ProtoMessage() {}

func (x *ListClientEligiblePlansResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientEligiblePlansResponse.ProtoReflect.Descriptor instead.
func (*ListClientEligiblePlansResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListClientEligiblePlansResponse) GetPlanOptions() []*ListClientEligiblePlansResponse_Plan {
	if x != nil {
		return x.PlanOptions
	}
	return nil
}

func (x *ListClientEligiblePlansResponse) GetPlanSelected() string {
	if x != nil {
		return x.PlanSelected
	}
	return ""
}

type GetClientPlanDetailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanKey       string                 `protobuf:"bytes,1,opt,name=plan_key,proto3" json:"plan_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientPlanDetailRequest) Reset() {
	*x = GetClientPlanDetailRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientPlanDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientPlanDetailRequest) ProtoMessage() {}

func (x *GetClientPlanDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientPlanDetailRequest.ProtoReflect.Descriptor instead.
func (*GetClientPlanDetailRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetClientPlanDetailRequest) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

type GetClientPlanDetailResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Plan info includes general information of the plan
	PlanInfo *BasePlan `protobuf:"bytes,1,opt,name=plan_info,proto3" json:"plan_info,omitempty"`
	// Cost info of the plan includes total of each fee amount and fee info url
	CostInfo *CostInfo `protobuf:"bytes,2,opt,name=cost_info,proto3" json:"cost_info,omitempty"`
	// Plan detail url
	PlanDetailUrl string `protobuf:"bytes,3,opt,name=plan_detail_url,proto3" json:"plan_detail_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientPlanDetailResponse) Reset() {
	*x = GetClientPlanDetailResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientPlanDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientPlanDetailResponse) ProtoMessage() {}

func (x *GetClientPlanDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientPlanDetailResponse.ProtoReflect.Descriptor instead.
func (*GetClientPlanDetailResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetClientPlanDetailResponse) GetPlanInfo() *BasePlan {
	if x != nil {
		return x.PlanInfo
	}
	return nil
}

func (x *GetClientPlanDetailResponse) GetCostInfo() *CostInfo {
	if x != nil {
		return x.CostInfo
	}
	return nil
}

func (x *GetClientPlanDetailResponse) GetPlanDetailUrl() string {
	if x != nil {
		return x.PlanDetailUrl
	}
	return ""
}

type AcceptClientPlanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanKey       string                 `protobuf:"bytes,1,opt,name=plan_key,proto3" json:"plan_key,omitempty"`
	AppId         int32                  `protobuf:"varint,2,opt,name=app_id,proto3" json:"app_id,omitempty"`
	AppTransId    string                 `protobuf:"bytes,3,opt,name=app_trans_id,proto3" json:"app_trans_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptClientPlanRequest) Reset() {
	*x = AcceptClientPlanRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptClientPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptClientPlanRequest) ProtoMessage() {}

func (x *AcceptClientPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptClientPlanRequest.ProtoReflect.Descriptor instead.
func (*AcceptClientPlanRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{4}
}

func (x *AcceptClientPlanRequest) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

func (x *AcceptClientPlanRequest) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *AcceptClientPlanRequest) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

type AcceptClientPlanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanKey       string                 `protobuf:"bytes,1,opt,name=plan_key,proto3" json:"plan_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcceptClientPlanResponse) Reset() {
	*x = AcceptClientPlanResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcceptClientPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptClientPlanResponse) ProtoMessage() {}

func (x *AcceptClientPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptClientPlanResponse.ProtoReflect.Descriptor instead.
func (*AcceptClientPlanResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{5}
}

func (x *AcceptClientPlanResponse) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

type GetPaymentPlanDetailRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ZaloPay user id
	ZalopayId int64 `protobuf:"varint,1,opt,name=zalopay_id,json=zalopayId,proto3" json:"zalopay_id,omitempty"`
	// Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
	PlanKey string `protobuf:"bytes,2,opt,name=plan_key,json=planKey,proto3" json:"plan_key,omitempty"`
	// Order information
	OrderInfo     *GetPaymentPlanDetailRequest_OrderInfo `protobuf:"bytes,3,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentPlanDetailRequest) Reset() {
	*x = GetPaymentPlanDetailRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentPlanDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentPlanDetailRequest) ProtoMessage() {}

func (x *GetPaymentPlanDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentPlanDetailRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentPlanDetailRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetPaymentPlanDetailRequest) GetZalopayId() int64 {
	if x != nil {
		return x.ZalopayId
	}
	return 0
}

func (x *GetPaymentPlanDetailRequest) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

func (x *GetPaymentPlanDetailRequest) GetOrderInfo() *GetPaymentPlanDetailRequest_OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

type GetPaymentPlanDetailResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Plan info includes general information of the plan
	PlanInfo *BasePlan `protobuf:"bytes,1,opt,name=plan_info,json=planInfo,proto3" json:"plan_info,omitempty"`
	// Plan detail links
	PlanDetailUrl string `protobuf:"bytes,2,opt,name=plan_detail_url,json=planDetailUrl,proto3" json:"plan_detail_url,omitempty"`
	// List of scheduled repayment
	FsChargeInfo string `protobuf:"bytes,3,opt,name=fs_charge_info,json=fsChargeInfo,proto3" json:"fs_charge_info,omitempty"`
	// List of scheduled repayment
	InteractionGuide *InteractionGuide `protobuf:"bytes,4,opt,name=interaction_guide,json=interactionGuide,proto3" json:"interaction_guide,omitempty"`
	// Metadata
	Metadata      *structpb.Struct `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentPlanDetailResponse) Reset() {
	*x = GetPaymentPlanDetailResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentPlanDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentPlanDetailResponse) ProtoMessage() {}

func (x *GetPaymentPlanDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentPlanDetailResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentPlanDetailResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetPaymentPlanDetailResponse) GetPlanInfo() *BasePlan {
	if x != nil {
		return x.PlanInfo
	}
	return nil
}

func (x *GetPaymentPlanDetailResponse) GetPlanDetailUrl() string {
	if x != nil {
		return x.PlanDetailUrl
	}
	return ""
}

func (x *GetPaymentPlanDetailResponse) GetFsChargeInfo() string {
	if x != nil {
		return x.FsChargeInfo
	}
	return ""
}

func (x *GetPaymentPlanDetailResponse) GetInteractionGuide() *InteractionGuide {
	if x != nil {
		return x.InteractionGuide
	}
	return nil
}

func (x *GetPaymentPlanDetailResponse) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type GetClientInstallmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZpTransId     int64                  `protobuf:"varint,1,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientInstallmentRequest) Reset() {
	*x = GetClientInstallmentRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientInstallmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientInstallmentRequest) ProtoMessage() {}

func (x *GetClientInstallmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientInstallmentRequest.ProtoReflect.Descriptor instead.
func (*GetClientInstallmentRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetClientInstallmentRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

type GetClientInstallmentResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Installment        *InstallmentData       `protobuf:"bytes,1,opt,name=installment,proto3" json:"installment,omitempty"`
	EarlyDischarge     *EarlyDischarge        `protobuf:"bytes,2,opt,name=early_discharge,proto3,oneof" json:"early_discharge,omitempty"`
	RepaymentSchedules []*RepaymentSchedule   `protobuf:"bytes,9,rep,name=repayment_schedules,proto3" json:"repayment_schedules,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetClientInstallmentResponse) Reset() {
	*x = GetClientInstallmentResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientInstallmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientInstallmentResponse) ProtoMessage() {}

func (x *GetClientInstallmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientInstallmentResponse.ProtoReflect.Descriptor instead.
func (*GetClientInstallmentResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetClientInstallmentResponse) GetInstallment() *InstallmentData {
	if x != nil {
		return x.Installment
	}
	return nil
}

func (x *GetClientInstallmentResponse) GetEarlyDischarge() *EarlyDischarge {
	if x != nil {
		return x.EarlyDischarge
	}
	return nil
}

func (x *GetClientInstallmentResponse) GetRepaymentSchedules() []*RepaymentSchedule {
	if x != nil {
		return x.RepaymentSchedules
	}
	return nil
}

type GetInstallmentStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZpTransId     int64                  `protobuf:"varint,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	ForceLatest   bool                   `protobuf:"varint,2,opt,name=force_latest,json=forceLatest,proto3" json:"force_latest,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInstallmentStatusRequest) Reset() {
	*x = GetInstallmentStatusRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInstallmentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstallmentStatusRequest) ProtoMessage() {}

func (x *GetInstallmentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstallmentStatusRequest.ProtoReflect.Descriptor instead.
func (*GetInstallmentStatusRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetInstallmentStatusRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *GetInstallmentStatusRequest) GetForceLatest() bool {
	if x != nil {
		return x.ForceLatest
	}
	return false
}

type GetInstallmentStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        InstallmentStatus      `protobuf:"varint,1,opt,name=status,proto3,enum=management_service.installment.v1.InstallmentStatus" json:"status,omitempty"`
	Info          *InstallmentBase       `protobuf:"bytes,2,opt,name=info,proto3,oneof" json:"info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInstallmentStatusResponse) Reset() {
	*x = GetInstallmentStatusResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInstallmentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstallmentStatusResponse) ProtoMessage() {}

func (x *GetInstallmentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstallmentStatusResponse.ProtoReflect.Descriptor instead.
func (*GetInstallmentStatusResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetInstallmentStatusResponse) GetStatus() InstallmentStatus {
	if x != nil {
		return x.Status
	}
	return InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED
}

func (x *GetInstallmentStatusResponse) GetInfo() *InstallmentBase {
	if x != nil {
		return x.Info
	}
	return nil
}

type GetClientEarlyDischargeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZpTransId     int64                  `protobuf:"varint,1,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientEarlyDischargeRequest) Reset() {
	*x = GetClientEarlyDischargeRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientEarlyDischargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientEarlyDischargeRequest) ProtoMessage() {}

func (x *GetClientEarlyDischargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientEarlyDischargeRequest.ProtoReflect.Descriptor instead.
func (*GetClientEarlyDischargeRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetClientEarlyDischargeRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

type GetClientEarlyDischargeResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	EarlyDischarge *EarlyDischarge        `protobuf:"bytes,1,opt,name=early_discharge,proto3" json:"early_discharge,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetClientEarlyDischargeResponse) Reset() {
	*x = GetClientEarlyDischargeResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientEarlyDischargeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientEarlyDischargeResponse) ProtoMessage() {}

func (x *GetClientEarlyDischargeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientEarlyDischargeResponse.ProtoReflect.Descriptor instead.
func (*GetClientEarlyDischargeResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetClientEarlyDischargeResponse) GetEarlyDischarge() *EarlyDischarge {
	if x != nil {
		return x.EarlyDischarge
	}
	return nil
}

type GetEarlyDischargeRefundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZpTransId     int64                  `protobuf:"varint,1,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
	ForceLatest   *bool                  `protobuf:"varint,2,opt,name=force_latest,proto3,oneof" json:"force_latest,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEarlyDischargeRefundRequest) Reset() {
	*x = GetEarlyDischargeRefundRequest{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEarlyDischargeRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarlyDischargeRefundRequest) ProtoMessage() {}

func (x *GetEarlyDischargeRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarlyDischargeRefundRequest.ProtoReflect.Descriptor instead.
func (*GetEarlyDischargeRefundRequest) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetEarlyDischargeRefundRequest) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *GetEarlyDischargeRefundRequest) GetForceLatest() bool {
	if x != nil && x.ForceLatest != nil {
		return *x.ForceLatest
	}
	return false
}

type GetEarlyDischargeRefundResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	DischargeInfo *EarlyDischargeForRefund `protobuf:"bytes,1,opt,name=discharge_info,proto3" json:"discharge_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEarlyDischargeRefundResponse) Reset() {
	*x = GetEarlyDischargeRefundResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEarlyDischargeRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEarlyDischargeRefundResponse) ProtoMessage() {}

func (x *GetEarlyDischargeRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEarlyDischargeRefundResponse.ProtoReflect.Descriptor instead.
func (*GetEarlyDischargeRefundResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetEarlyDischargeRefundResponse) GetDischargeInfo() *EarlyDischargeForRefund {
	if x != nil {
		return x.DischargeInfo
	}
	return nil
}

type NotifyInstallmentRefundResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotifyInstallmentRefundResponse) Reset() {
	*x = NotifyInstallmentRefundResponse{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotifyInstallmentRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyInstallmentRefundResponse) ProtoMessage() {}

func (x *NotifyInstallmentRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyInstallmentRefundResponse.ProtoReflect.Descriptor instead.
func (*NotifyInstallmentRefundResponse) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{16}
}

type InstallmentBase struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tenor           int32                  `protobuf:"varint,2,opt,name=tenor,proto3" json:"tenor,omitempty"`
	ZpTransId       int64                  `protobuf:"varint,3,opt,name=zp_trans_id,json=zpTransId,proto3" json:"zp_trans_id,omitempty"`
	PartnerCode     string                 `protobuf:"bytes,4,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	PartnerInstId   string                 `protobuf:"bytes,5,opt,name=partner_inst_id,json=partnerInstId,proto3" json:"partner_inst_id,omitempty"`
	InterestRate    float64                `protobuf:"fixed64,6,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	PrincipalAmount int64                  `protobuf:"varint,7,opt,name=principal_amount,json=principalAmount,proto3" json:"principal_amount,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *InstallmentBase) Reset() {
	*x = InstallmentBase{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallmentBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentBase) ProtoMessage() {}

func (x *InstallmentBase) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentBase.ProtoReflect.Descriptor instead.
func (*InstallmentBase) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{17}
}

func (x *InstallmentBase) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InstallmentBase) GetTenor() int32 {
	if x != nil {
		return x.Tenor
	}
	return 0
}

func (x *InstallmentBase) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *InstallmentBase) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *InstallmentBase) GetPartnerInstId() string {
	if x != nil {
		return x.PartnerInstId
	}
	return ""
}

func (x *InstallmentBase) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *InstallmentBase) GetPrincipalAmount() int64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

type InstallmentData struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tenure               int32                  `protobuf:"varint,2,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Status               InstallmentStatus      `protobuf:"varint,3,opt,name=status,proto3,enum=management_service.installment.v1.InstallmentStatus" json:"status,omitempty"`
	PrincipalAmount      int64                  `protobuf:"varint,4,opt,name=principal_amount,proto3" json:"principal_amount,omitempty"`
	InterestAmount       int64                  `protobuf:"varint,5,opt,name=interest_amount,proto3" json:"interest_amount,omitempty"`
	PenaltyAmount        int64                  `protobuf:"varint,6,opt,name=penalty_amount,proto3" json:"penalty_amount,omitempty"`
	TotalAmountDue       int64                  `protobuf:"varint,7,opt,name=total_amount_due,proto3" json:"total_amount_due,omitempty"`
	TotalPaidAmount      int64                  `protobuf:"varint,8,opt,name=total_paid_amount,proto3" json:"total_paid_amount,omitempty"`
	TotalRemainingAmount int64                  `protobuf:"varint,9,opt,name=total_remaining_amount,proto3" json:"total_remaining_amount,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *InstallmentData) Reset() {
	*x = InstallmentData{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallmentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentData) ProtoMessage() {}

func (x *InstallmentData) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentData.ProtoReflect.Descriptor instead.
func (*InstallmentData) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{18}
}

func (x *InstallmentData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InstallmentData) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *InstallmentData) GetStatus() InstallmentStatus {
	if x != nil {
		return x.Status
	}
	return InstallmentStatus_INSTALLMENT_STATUS_UNSPECIFIED
}

func (x *InstallmentData) GetPrincipalAmount() int64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

func (x *InstallmentData) GetInterestAmount() int64 {
	if x != nil {
		return x.InterestAmount
	}
	return 0
}

func (x *InstallmentData) GetPenaltyAmount() int64 {
	if x != nil {
		return x.PenaltyAmount
	}
	return 0
}

func (x *InstallmentData) GetTotalAmountDue() int64 {
	if x != nil {
		return x.TotalAmountDue
	}
	return 0
}

func (x *InstallmentData) GetTotalPaidAmount() int64 {
	if x != nil {
		return x.TotalPaidAmount
	}
	return 0
}

func (x *InstallmentData) GetTotalRemainingAmount() int64 {
	if x != nil {
		return x.TotalRemainingAmount
	}
	return 0
}

type InstallmentRefund struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ZpTransId         int64                  `protobuf:"varint,1,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
	Version           int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	NetRefundAmount   int64                  `protobuf:"varint,3,opt,name=net_refund_amount,proto3" json:"net_refund_amount,omitempty"`
	TotalRefundAmount int64                  `protobuf:"varint,4,opt,name=total_refund_amount,proto3" json:"total_refund_amount,omitempty"`
	UserTopupAmount   int64                  `protobuf:"varint,5,opt,name=user_topup_amount,proto3" json:"user_topup_amount,omitempty"`
	UserTopupRequired bool                   `protobuf:"varint,6,opt,name=user_topup_required,proto3" json:"user_topup_required,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InstallmentRefund) Reset() {
	*x = InstallmentRefund{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallmentRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallmentRefund) ProtoMessage() {}

func (x *InstallmentRefund) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallmentRefund.ProtoReflect.Descriptor instead.
func (*InstallmentRefund) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{19}
}

func (x *InstallmentRefund) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *InstallmentRefund) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *InstallmentRefund) GetNetRefundAmount() int64 {
	if x != nil {
		return x.NetRefundAmount
	}
	return 0
}

func (x *InstallmentRefund) GetTotalRefundAmount() int64 {
	if x != nil {
		return x.TotalRefundAmount
	}
	return 0
}

func (x *InstallmentRefund) GetUserTopupAmount() int64 {
	if x != nil {
		return x.UserTopupAmount
	}
	return 0
}

func (x *InstallmentRefund) GetUserTopupRequired() bool {
	if x != nil {
		return x.UserTopupRequired
	}
	return false
}

type BasePlan struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
	PlanKey *string `protobuf:"bytes,1,opt,name=plan_key,proto3,oneof" json:"plan_key,omitempty"`
	// Tenor unit is the unit of the tenor, it can be day, month, year
	TenorNumber int64 `protobuf:"varint,2,opt,name=tenor_number,proto3" json:"tenor_number,omitempty"`
	// Emi amount is the amount of each installment
	EmiAmount int64 `protobuf:"varint,3,opt,name=emi_amount,proto3" json:"emi_amount,omitempty"`
	// Total amount is the total amount of the plan
	TotalAmount int64 `protobuf:"varint,4,opt,name=total_amount,proto3" json:"total_amount,omitempty"`
	// Principal amount is the principal amount of the plan
	PrincipalAmount int64 `protobuf:"varint,5,opt,name=principal_amount,proto3" json:"principal_amount,omitempty"`
	// Plan status
	Status        PlanStatus `protobuf:"varint,6,opt,name=status,proto3,enum=management_service.installment.v1.PlanStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BasePlan) Reset() {
	*x = BasePlan{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BasePlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasePlan) ProtoMessage() {}

func (x *BasePlan) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasePlan.ProtoReflect.Descriptor instead.
func (*BasePlan) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{20}
}

func (x *BasePlan) GetPlanKey() string {
	if x != nil && x.PlanKey != nil {
		return *x.PlanKey
	}
	return ""
}

func (x *BasePlan) GetTenorNumber() int64 {
	if x != nil {
		return x.TenorNumber
	}
	return 0
}

func (x *BasePlan) GetEmiAmount() int64 {
	if x != nil {
		return x.EmiAmount
	}
	return 0
}

func (x *BasePlan) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *BasePlan) GetPrincipalAmount() int64 {
	if x != nil {
		return x.PrincipalAmount
	}
	return 0
}

func (x *BasePlan) GetStatus() PlanStatus {
	if x != nil {
		return x.Status
	}
	return PlanStatus_PLAN_STATUS_UNSPECIFIED
}

type FullPlan struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
	PlanKey string `protobuf:"bytes,1,opt,name=plan_key,proto3" json:"plan_key,omitempty"`
	// Plan info includes general information of the plan
	PlanInfo *BasePlan `protobuf:"bytes,2,opt,name=plan_info,proto3" json:"plan_info,omitempty"`
	// Cost info of the plan includes total of each fee amount and fee info url
	CostInfo *CostInfo `protobuf:"bytes,6,opt,name=cost_info,proto3" json:"cost_info,omitempty"`
	// Plan detail url
	PlanDetailUrl string `protobuf:"bytes,7,opt,name=plan_detail_url,proto3" json:"plan_detail_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FullPlan) Reset() {
	*x = FullPlan{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullPlan) ProtoMessage() {}

func (x *FullPlan) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullPlan.ProtoReflect.Descriptor instead.
func (*FullPlan) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{21}
}

func (x *FullPlan) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

func (x *FullPlan) GetPlanInfo() *BasePlan {
	if x != nil {
		return x.PlanInfo
	}
	return nil
}

func (x *FullPlan) GetCostInfo() *CostInfo {
	if x != nil {
		return x.CostInfo
	}
	return nil
}

func (x *FullPlan) GetPlanDetailUrl() string {
	if x != nil {
		return x.PlanDetailUrl
	}
	return ""
}

type CostInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Total cost amount is the total amount of the plan
	TotalCostAmount int64 `protobuf:"varint,1,opt,name=total_cost_amount,proto3" json:"total_cost_amount,omitempty"`
	// Total fee amount is the total amount of all fees
	TotalFeeAmount int64 `protobuf:"varint,2,opt,name=total_fee_amount,proto3" json:"total_fee_amount,omitempty"`
	// Platform fee amount is the fee amount of the platform
	PlatformFeeAmount int64 `protobuf:"varint,3,opt,name=platform_fee_amount,proto3" json:"platform_fee_amount,omitempty"`
	// Conversion fee amount is the fee amount of the conversion
	ConversionFeeAmount int64 `protobuf:"varint,4,opt,name=conversion_fee_amount,proto3" json:"conversion_fee_amount,omitempty"`
	// Interest amount is the interest amount of the plan
	InterestAmount int64 `protobuf:"varint,5,opt,name=interest_amount,proto3" json:"interest_amount,omitempty"`
	// List of fee info
	ListFeeInfo []*Fee `protobuf:"bytes,6,rep,name=list_fee_info,proto3" json:"list_fee_info,omitempty"`
	// Fee explanation
	FeeExplanation string `protobuf:"bytes,7,opt,name=fee_explanation,proto3" json:"fee_explanation,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CostInfo) Reset() {
	*x = CostInfo{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CostInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CostInfo) ProtoMessage() {}

func (x *CostInfo) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CostInfo.ProtoReflect.Descriptor instead.
func (*CostInfo) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{22}
}

func (x *CostInfo) GetTotalCostAmount() int64 {
	if x != nil {
		return x.TotalCostAmount
	}
	return 0
}

func (x *CostInfo) GetTotalFeeAmount() int64 {
	if x != nil {
		return x.TotalFeeAmount
	}
	return 0
}

func (x *CostInfo) GetPlatformFeeAmount() int64 {
	if x != nil {
		return x.PlatformFeeAmount
	}
	return 0
}

func (x *CostInfo) GetConversionFeeAmount() int64 {
	if x != nil {
		return x.ConversionFeeAmount
	}
	return 0
}

func (x *CostInfo) GetInterestAmount() int64 {
	if x != nil {
		return x.InterestAmount
	}
	return 0
}

func (x *CostInfo) GetListFeeInfo() []*Fee {
	if x != nil {
		return x.ListFeeInfo
	}
	return nil
}

func (x *CostInfo) GetFeeExplanation() string {
	if x != nil {
		return x.FeeExplanation
	}
	return ""
}

type PlanRepaymentSchedule struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Amount            int64                  `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	DueDate           string                 `protobuf:"bytes,2,opt,name=due_date,proto3" json:"due_date,omitempty"`
	InstallmentNumber int32                  `protobuf:"varint,3,opt,name=installment_number,proto3" json:"installment_number,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PlanRepaymentSchedule) Reset() {
	*x = PlanRepaymentSchedule{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanRepaymentSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanRepaymentSchedule) ProtoMessage() {}

func (x *PlanRepaymentSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanRepaymentSchedule.ProtoReflect.Descriptor instead.
func (*PlanRepaymentSchedule) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{23}
}

func (x *PlanRepaymentSchedule) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PlanRepaymentSchedule) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *PlanRepaymentSchedule) GetInstallmentNumber() int32 {
	if x != nil {
		return x.InstallmentNumber
	}
	return 0
}

type RepaymentSchedule struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	SeqNo                int32                  `protobuf:"varint,1,opt,name=seq_no,proto3" json:"seq_no,omitempty"`
	Status               RepayStatus            `protobuf:"varint,2,opt,name=status,proto3,enum=management_service.installment.v1.RepayStatus" json:"status,omitempty"`
	DueDate              string                 `protobuf:"bytes,3,opt,name=due_date,proto3" json:"due_date,omitempty"`
	DueAmount            int64                  `protobuf:"varint,4,opt,name=due_amount,proto3" json:"due_amount,omitempty"`
	PenaltyAmount        int64                  `protobuf:"varint,5,opt,name=penalty_amount,proto3" json:"penalty_amount,omitempty"`
	TotalDueAmount       int64                  `protobuf:"varint,6,opt,name=total_due_amount,proto3" json:"total_due_amount,omitempty"`
	TotalPaidAmount      int64                  `protobuf:"varint,7,opt,name=total_paid_amount,proto3" json:"total_paid_amount,omitempty"`
	TotalRemainingAmount int64                  `protobuf:"varint,8,opt,name=total_remaining_amount,proto3" json:"total_remaining_amount,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RepaymentSchedule) Reset() {
	*x = RepaymentSchedule{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RepaymentSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepaymentSchedule) ProtoMessage() {}

func (x *RepaymentSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepaymentSchedule.ProtoReflect.Descriptor instead.
func (*RepaymentSchedule) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{24}
}

func (x *RepaymentSchedule) GetSeqNo() int32 {
	if x != nil {
		return x.SeqNo
	}
	return 0
}

func (x *RepaymentSchedule) GetStatus() RepayStatus {
	if x != nil {
		return x.Status
	}
	return RepayStatus_REPAY_STATUS_UNSPECIFIED
}

func (x *RepaymentSchedule) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *RepaymentSchedule) GetDueAmount() int64 {
	if x != nil {
		return x.DueAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetPenaltyAmount() int64 {
	if x != nil {
		return x.PenaltyAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalPaidAmount() int64 {
	if x != nil {
		return x.TotalPaidAmount
	}
	return 0
}

func (x *RepaymentSchedule) GetTotalRemainingAmount() int64 {
	if x != nil {
		return x.TotalRemainingAmount
	}
	return 0
}

type EarlyDischargeBase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kind          EarlyDischargeKind     `protobuf:"varint,1,opt,name=kind,proto3,enum=management_service.installment.v1.EarlyDischargeKind" json:"kind,omitempty"`
	Status        EarlyDischargeStatus   `protobuf:"varint,2,opt,name=status,proto3,enum=management_service.installment.v1.EarlyDischargeStatus" json:"status,omitempty"`
	Allowed       *bool                  `protobuf:"varint,3,opt,name=allowed,proto3,oneof" json:"allowed,omitempty"`
	InSession     *bool                  `protobuf:"varint,5,opt,name=in_session,proto3,oneof" json:"in_session,omitempty"`
	SessionInfo   *Session               `protobuf:"bytes,4,opt,name=session_info,proto3,oneof" json:"session_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EarlyDischargeBase) Reset() {
	*x = EarlyDischargeBase{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EarlyDischargeBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarlyDischargeBase) ProtoMessage() {}

func (x *EarlyDischargeBase) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarlyDischargeBase.ProtoReflect.Descriptor instead.
func (*EarlyDischargeBase) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{25}
}

func (x *EarlyDischargeBase) GetKind() EarlyDischargeKind {
	if x != nil {
		return x.Kind
	}
	return EarlyDischargeKind_EARLY_DISCHARGE_KIND_UNSPECIFIED
}

func (x *EarlyDischargeBase) GetStatus() EarlyDischargeStatus {
	if x != nil {
		return x.Status
	}
	return EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED
}

func (x *EarlyDischargeBase) GetAllowed() bool {
	if x != nil && x.Allowed != nil {
		return *x.Allowed
	}
	return false
}

func (x *EarlyDischargeBase) GetInSession() bool {
	if x != nil && x.InSession != nil {
		return *x.InSession
	}
	return false
}

func (x *EarlyDischargeBase) GetSessionInfo() *Session {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

type EarlyDischargeDetail struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	TotalDischargeAmount       int64                  `protobuf:"varint,1,opt,name=total_discharge_amount,proto3" json:"total_discharge_amount,omitempty"`
	EarlyDischargeAmount       int64                  `protobuf:"varint,2,opt,name=early_discharge_amount,proto3" json:"early_discharge_amount,omitempty"`
	EarlyDischargeFee          int64                  `protobuf:"varint,3,opt,name=early_discharge_fee,proto3" json:"early_discharge_fee,omitempty"`
	TotalOutstandingAmount     int64                  `protobuf:"varint,4,opt,name=total_outstanding_amount,proto3" json:"total_outstanding_amount,omitempty"`
	OutstandingPrincipalAmount *int64                 `protobuf:"varint,5,opt,name=outstanding_principal_amount,proto3,oneof" json:"outstanding_principal_amount,omitempty"`
	OutstandingInterestAmount  *int64                 `protobuf:"varint,6,opt,name=outstanding_interest_amount,proto3,oneof" json:"outstanding_interest_amount,omitempty"`
	OutstandingPenaltyAmount   *int64                 `protobuf:"varint,7,opt,name=outstanding_penalty_amount,proto3,oneof" json:"outstanding_penalty_amount,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *EarlyDischargeDetail) Reset() {
	*x = EarlyDischargeDetail{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EarlyDischargeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarlyDischargeDetail) ProtoMessage() {}

func (x *EarlyDischargeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarlyDischargeDetail.ProtoReflect.Descriptor instead.
func (*EarlyDischargeDetail) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{26}
}

func (x *EarlyDischargeDetail) GetTotalDischargeAmount() int64 {
	if x != nil {
		return x.TotalDischargeAmount
	}
	return 0
}

func (x *EarlyDischargeDetail) GetEarlyDischargeAmount() int64 {
	if x != nil {
		return x.EarlyDischargeAmount
	}
	return 0
}

func (x *EarlyDischargeDetail) GetEarlyDischargeFee() int64 {
	if x != nil {
		return x.EarlyDischargeFee
	}
	return 0
}

func (x *EarlyDischargeDetail) GetTotalOutstandingAmount() int64 {
	if x != nil {
		return x.TotalOutstandingAmount
	}
	return 0
}

func (x *EarlyDischargeDetail) GetOutstandingPrincipalAmount() int64 {
	if x != nil && x.OutstandingPrincipalAmount != nil {
		return *x.OutstandingPrincipalAmount
	}
	return 0
}

func (x *EarlyDischargeDetail) GetOutstandingInterestAmount() int64 {
	if x != nil && x.OutstandingInterestAmount != nil {
		return *x.OutstandingInterestAmount
	}
	return 0
}

func (x *EarlyDischargeDetail) GetOutstandingPenaltyAmount() int64 {
	if x != nil && x.OutstandingPenaltyAmount != nil {
		return *x.OutstandingPenaltyAmount
	}
	return 0
}

type EarlyDischarge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Info          *EarlyDischargeBase    `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Detail        *EarlyDischargeDetail  `protobuf:"bytes,2,opt,name=detail,proto3,oneof" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EarlyDischarge) Reset() {
	*x = EarlyDischarge{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EarlyDischarge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarlyDischarge) ProtoMessage() {}

func (x *EarlyDischarge) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarlyDischarge.ProtoReflect.Descriptor instead.
func (*EarlyDischarge) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{27}
}

func (x *EarlyDischarge) GetInfo() *EarlyDischargeBase {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *EarlyDischarge) GetDetail() *EarlyDischargeDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

// EarlyDischargeForRefund need to be use the `composition` approach when fields grow in the future, may be > 5 fields
type EarlyDischargeForRefund struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status EarlyDischargeStatus   `protobuf:"varint,1,opt,name=status,proto3,enum=management_service.installment.v1.EarlyDischargeStatus" json:"status,omitempty"`
	// discharge_amount is the raw total amount of the early discharge
	DischargeAmount int64 `protobuf:"varint,2,opt,name=discharge_amount,proto3" json:"discharge_amount,omitempty"`
	// session_available ref to in_session in EarlyDischargeBase
	SessionAvailable bool `protobuf:"varint,3,opt,name=session_available,proto3" json:"session_available,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EarlyDischargeForRefund) Reset() {
	*x = EarlyDischargeForRefund{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EarlyDischargeForRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EarlyDischargeForRefund) ProtoMessage() {}

func (x *EarlyDischargeForRefund) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EarlyDischargeForRefund.ProtoReflect.Descriptor instead.
func (*EarlyDischargeForRefund) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{28}
}

func (x *EarlyDischargeForRefund) GetStatus() EarlyDischargeStatus {
	if x != nil {
		return x.Status
	}
	return EarlyDischargeStatus_EARLY_DISCHARGE_STATUS_UNSPECIFIED
}

func (x *EarlyDischargeForRefund) GetDischargeAmount() int64 {
	if x != nil {
		return x.DischargeAmount
	}
	return 0
}

func (x *EarlyDischargeForRefund) GetSessionAvailable() bool {
	if x != nil {
		return x.SessionAvailable
	}
	return false
}

type InteractionGuide struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        *Action                `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Message       *Message               `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Notice        *Message               `protobuf:"bytes,3,opt,name=notice,proto3" json:"notice,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InteractionGuide) Reset() {
	*x = InteractionGuide{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InteractionGuide) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InteractionGuide) ProtoMessage() {}

func (x *InteractionGuide) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InteractionGuide.ProtoReflect.Descriptor instead.
func (*InteractionGuide) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{29}
}

func (x *InteractionGuide) GetAction() *Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *InteractionGuide) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *InteractionGuide) GetNotice() *Message {
	if x != nil {
		return x.Notice
	}
	return nil
}

type Action struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          ActionType             `protobuf:"varint,1,opt,name=type,proto3,enum=management_service.installment.v1.ActionType" json:"type,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	ZpiUrl        string                 `protobuf:"bytes,3,opt,name=zpi_url,json=zpiUrl,proto3" json:"zpi_url,omitempty"`
	ZpaUrl        string                 `protobuf:"bytes,4,opt,name=zpa_url,json=zpaUrl,proto3" json:"zpa_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Action) Reset() {
	*x = Action{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Action) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Action) ProtoMessage() {}

func (x *Action) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Action.ProtoReflect.Descriptor instead.
func (*Action) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{30}
}

func (x *Action) GetType() ActionType {
	if x != nil {
		return x.Type
	}
	return ActionType_ACTION_TYPE_UNSPECIFIED
}

func (x *Action) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Action) GetZpiUrl() string {
	if x != nil {
		return x.ZpiUrl
	}
	return ""
}

func (x *Action) GetZpaUrl() string {
	if x != nil {
		return x.ZpaUrl
	}
	return ""
}

type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Color         string                 `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{31}
}

func (x *Message) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Message) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type Fee struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FeeType       FeeType                `protobuf:"varint,1,opt,name=fee_type,json=feeType,proto3,enum=management_service.installment.v1.FeeType" json:"fee_type,omitempty"`
	FeeAmount     int64                  `protobuf:"varint,2,opt,name=fee_amount,json=feeAmount,proto3" json:"fee_amount,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	MessageEn     string                 `protobuf:"bytes,4,opt,name=message_en,json=messageEn,proto3" json:"message_en,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Fee) Reset() {
	*x = Fee{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Fee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fee) ProtoMessage() {}

func (x *Fee) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fee.ProtoReflect.Descriptor instead.
func (*Fee) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{32}
}

func (x *Fee) GetFeeType() FeeType {
	if x != nil {
		return x.FeeType
	}
	return FeeType_FEE_TYPE_INVALID
}

func (x *Fee) GetFeeAmount() int64 {
	if x != nil {
		return x.FeeAmount
	}
	return 0
}

func (x *Fee) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Fee) GetMessageEn() string {
	if x != nil {
		return x.MessageEn
	}
	return ""
}

// Session defines the operational time window when early discharge is available
type Session struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Start time in 24-hour format "HH:MM"
	StartTime string `protobuf:"bytes,1,opt,name=start_time,proto3" json:"start_time,omitempty"`
	// End time in 24-hour format "HH:MM"
	EndTime       string `protobuf:"bytes,2,opt,name=end_time,proto3" json:"end_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Session) Reset() {
	*x = Session{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{33}
}

func (x *Session) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *Session) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type ListClientEligiblePlansResponse_Plan struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Plan key is the unique key of the plan, it was generated encode/encrypt from the order information
	PlanKey string `protobuf:"bytes,1,opt,name=plan_key,proto3" json:"plan_key,omitempty"`
	// Plan info includes general information of the plan
	PlanInfo *BasePlan `protobuf:"bytes,2,opt,name=plan_info,proto3" json:"plan_info,omitempty"`
	// Cost info of the plan includes total of each fee amount and fee info url
	CostInfo *CostInfo `protobuf:"bytes,3,opt,name=cost_info,proto3" json:"cost_info,omitempty"`
	// Is highlight plan
	IsPopular bool `protobuf:"varint,4,opt,name=is_popular,proto3" json:"is_popular,omitempty"`
	// Plan detail url
	PlanDetailUrl string `protobuf:"bytes,5,opt,name=plan_detail_url,proto3" json:"plan_detail_url,omitempty"`
	// List of scheduled repayment
	ScheduledRepayments []*PlanRepaymentSchedule `protobuf:"bytes,6,rep,name=scheduled_repayments,proto3" json:"scheduled_repayments,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ListClientEligiblePlansResponse_Plan) Reset() {
	*x = ListClientEligiblePlansResponse_Plan{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientEligiblePlansResponse_Plan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientEligiblePlansResponse_Plan) ProtoMessage() {}

func (x *ListClientEligiblePlansResponse_Plan) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientEligiblePlansResponse_Plan.ProtoReflect.Descriptor instead.
func (*ListClientEligiblePlansResponse_Plan) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListClientEligiblePlansResponse_Plan) GetPlanKey() string {
	if x != nil {
		return x.PlanKey
	}
	return ""
}

func (x *ListClientEligiblePlansResponse_Plan) GetPlanInfo() *BasePlan {
	if x != nil {
		return x.PlanInfo
	}
	return nil
}

func (x *ListClientEligiblePlansResponse_Plan) GetCostInfo() *CostInfo {
	if x != nil {
		return x.CostInfo
	}
	return nil
}

func (x *ListClientEligiblePlansResponse_Plan) GetIsPopular() bool {
	if x != nil {
		return x.IsPopular
	}
	return false
}

func (x *ListClientEligiblePlansResponse_Plan) GetPlanDetailUrl() string {
	if x != nil {
		return x.PlanDetailUrl
	}
	return ""
}

func (x *ListClientEligiblePlansResponse_Plan) GetScheduledRepayments() []*PlanRepaymentSchedule {
	if x != nil {
		return x.ScheduledRepayments
	}
	return nil
}

type GetPaymentPlanDetailRequest_OrderInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Order app id
	AppId int32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// Order app transaction id
	AppTransId string `protobuf:"bytes,2,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"` // #Agreement_Payment
	// User charge amount
	ChargeAmount  int64 `protobuf:"varint,3,opt,name=charge_amount,json=chargeAmount,proto3" json:"charge_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentPlanDetailRequest_OrderInfo) Reset() {
	*x = GetPaymentPlanDetailRequest_OrderInfo{}
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentPlanDetailRequest_OrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentPlanDetailRequest_OrderInfo) ProtoMessage() {}

func (x *GetPaymentPlanDetailRequest_OrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_installment_v1_installment_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentPlanDetailRequest_OrderInfo.ProtoReflect.Descriptor instead.
func (*GetPaymentPlanDetailRequest_OrderInfo) Descriptor() ([]byte, []int) {
	return file_management_service_installment_v1_installment_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *GetPaymentPlanDetailRequest_OrderInfo) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetPaymentPlanDetailRequest_OrderInfo) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *GetPaymentPlanDetailRequest_OrderInfo) GetChargeAmount() int64 {
	if x != nil {
		return x.ChargeAmount
	}
	return 0
}

var File_management_service_installment_v1_installment_service_proto protoreflect.FileDescriptor

const file_management_service_installment_v1_installment_service_proto_rawDesc = "" +
	"\n" +
	";management_service/installment/v1/installment_service.proto\x12!management_service.installment.v1\x1a\x17validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xca\x01\n" +
	"\x1eListClientEligiblePlansRequest\x12+\n" +
	"\fpartner_code\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fpartner_code\x12\x1f\n" +
	"\x06app_id\x18\x02 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\x06app_id\x12+\n" +
	"\fapp_trans_id\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fapp_trans_id\x12-\n" +
	"\rcharge_amount\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\rcharge_amount\"\xa7\x04\n" +
	"\x1fListClientEligiblePlansResponse\x12k\n" +
	"\fplan_options\x18\x01 \x03(\v2G.management_service.installment.v1.ListClientEligiblePlansResponse.PlanR\fplan_options\x12$\n" +
	"\rplan_selected\x18\x02 \x01(\tR\rplan_selected\x1a\xf0\x02\n" +
	"\x04Plan\x12\x1a\n" +
	"\bplan_key\x18\x01 \x01(\tR\bplan_key\x12I\n" +
	"\tplan_info\x18\x02 \x01(\v2+.management_service.installment.v1.BasePlanR\tplan_info\x12I\n" +
	"\tcost_info\x18\x03 \x01(\v2+.management_service.installment.v1.CostInfoR\tcost_info\x12\x1e\n" +
	"\n" +
	"is_popular\x18\x04 \x01(\bR\n" +
	"is_popular\x12(\n" +
	"\x0fplan_detail_url\x18\x05 \x01(\tR\x0fplan_detail_url\x12l\n" +
	"\x14scheduled_repayments\x18\x06 \x03(\v28.management_service.installment.v1.PlanRepaymentScheduleR\x14scheduled_repayments\"A\n" +
	"\x1aGetClientPlanDetailRequest\x12#\n" +
	"\bplan_key\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\bplan_key\"\xdd\x01\n" +
	"\x1bGetClientPlanDetailResponse\x12I\n" +
	"\tplan_info\x18\x01 \x01(\v2+.management_service.installment.v1.BasePlanR\tplan_info\x12I\n" +
	"\tcost_info\x18\x02 \x01(\v2+.management_service.installment.v1.CostInfoR\tcost_info\x12(\n" +
	"\x0fplan_detail_url\x18\x03 \x01(\tR\x0fplan_detail_url\"\x8c\x01\n" +
	"\x17AcceptClientPlanRequest\x12#\n" +
	"\bplan_key\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\bplan_key\x12\x1f\n" +
	"\x06app_id\x18\x02 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\x06app_id\x12+\n" +
	"\fapp_trans_id\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\fapp_trans_id\"6\n" +
	"\x18AcceptClientPlanResponse\x12\x1a\n" +
	"\bplan_key\x18\x01 \x01(\tR\bplan_key\"\xe3\x02\n" +
	"\x1bGetPaymentPlanDetailRequest\x12&\n" +
	"\n" +
	"zalopay_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzalopayId\x12\"\n" +
	"\bplan_key\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x00R\aplanKey\x12q\n" +
	"\n" +
	"order_info\x18\x03 \x01(\v2H.management_service.installment.v1.GetPaymentPlanDetailRequest.OrderInfoB\b\xfaB\x05\x8a\x01\x02\x10\x01R\torderInfo\x1a\x84\x01\n" +
	"\tOrderInfo\x12\x1e\n" +
	"\x06app_id\x18\x01 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\x05appId\x12)\n" +
	"\fapp_trans_id\x18\x02 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"appTransId\x12,\n" +
	"\rcharge_amount\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fchargeAmount\"\xcd\x02\n" +
	"\x1cGetPaymentPlanDetailResponse\x12H\n" +
	"\tplan_info\x18\x01 \x01(\v2+.management_service.installment.v1.BasePlanR\bplanInfo\x12&\n" +
	"\x0fplan_detail_url\x18\x02 \x01(\tR\rplanDetailUrl\x12$\n" +
	"\x0efs_charge_info\x18\x03 \x01(\tR\ffsChargeInfo\x12`\n" +
	"\x11interaction_guide\x18\x04 \x01(\v23.management_service.installment.v1.InteractionGuideR\x10interactionGuide\x123\n" +
	"\bmetadata\x18\x05 \x01(\v2\x17.google.protobuf.StructR\bmetadata\"H\n" +
	"\x1bGetClientInstallmentRequest\x12)\n" +
	"\vzp_trans_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\vzp_trans_id\"\xd2\x02\n" +
	"\x1cGetClientInstallmentResponse\x12T\n" +
	"\vinstallment\x18\x01 \x01(\v22.management_service.installment.v1.InstallmentDataR\vinstallment\x12`\n" +
	"\x0fearly_discharge\x18\x02 \x01(\v21.management_service.installment.v1.EarlyDischargeH\x00R\x0fearly_discharge\x88\x01\x01\x12f\n" +
	"\x13repayment_schedules\x18\t \x03(\v24.management_service.installment.v1.RepaymentScheduleR\x13repayment_schedulesB\x12\n" +
	"\x10_early_discharge\"i\n" +
	"\x1bGetInstallmentStatusRequest\x12'\n" +
	"\vzp_trans_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzpTransId\x12!\n" +
	"\fforce_latest\x18\x02 \x01(\bR\vforceLatest\"\xc2\x01\n" +
	"\x1cGetInstallmentStatusResponse\x12L\n" +
	"\x06status\x18\x01 \x01(\x0e24.management_service.installment.v1.InstallmentStatusR\x06status\x12K\n" +
	"\x04info\x18\x02 \x01(\v22.management_service.installment.v1.InstallmentBaseH\x00R\x04info\x88\x01\x01B\a\n" +
	"\x05_info\"I\n" +
	"\x1eGetClientEarlyDischargeRequest\x12'\n" +
	"\vzp_trans_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\tzpTransId\"~\n" +
	"\x1fGetClientEarlyDischargeResponse\x12[\n" +
	"\x0fearly_discharge\x18\x01 \x01(\v21.management_service.installment.v1.EarlyDischargeR\x0fearly_discharge\"\x85\x01\n" +
	"\x1eGetEarlyDischargeRefundRequest\x12)\n" +
	"\vzp_trans_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\vzp_trans_id\x12'\n" +
	"\fforce_latest\x18\x02 \x01(\bH\x00R\fforce_latest\x88\x01\x01B\x0f\n" +
	"\r_force_latest\"\x85\x01\n" +
	"\x1fGetEarlyDischargeRefundResponse\x12b\n" +
	"\x0edischarge_info\x18\x01 \x01(\v2:.management_service.installment.v1.EarlyDischargeForRefundR\x0edischarge_info\"!\n" +
	"\x1fNotifyInstallmentRefundResponse\"\xf2\x01\n" +
	"\x0fInstallmentBase\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05tenor\x18\x02 \x01(\x05R\x05tenor\x12\x1e\n" +
	"\vzp_trans_id\x18\x03 \x01(\x03R\tzpTransId\x12!\n" +
	"\fpartner_code\x18\x04 \x01(\tR\vpartnerCode\x12&\n" +
	"\x0fpartner_inst_id\x18\x05 \x01(\tR\rpartnerInstId\x12#\n" +
	"\rinterest_rate\x18\x06 \x01(\x01R\finterestRate\x12)\n" +
	"\x10principal_amount\x18\a \x01(\x03R\x0fprincipalAmount\"\x97\x03\n" +
	"\x0fInstallmentData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06tenure\x18\x02 \x01(\x05R\x06tenure\x12L\n" +
	"\x06status\x18\x03 \x01(\x0e24.management_service.installment.v1.InstallmentStatusR\x06status\x12*\n" +
	"\x10principal_amount\x18\x04 \x01(\x03R\x10principal_amount\x12(\n" +
	"\x0finterest_amount\x18\x05 \x01(\x03R\x0finterest_amount\x12&\n" +
	"\x0epenalty_amount\x18\x06 \x01(\x03R\x0epenalty_amount\x12*\n" +
	"\x10total_amount_due\x18\a \x01(\x03R\x10total_amount_due\x12,\n" +
	"\x11total_paid_amount\x18\b \x01(\x03R\x11total_paid_amount\x126\n" +
	"\x16total_remaining_amount\x18\t \x01(\x03R\x16total_remaining_amount\"\x8f\x02\n" +
	"\x11InstallmentRefund\x12 \n" +
	"\vzp_trans_id\x18\x01 \x01(\x03R\vzp_trans_id\x12\x18\n" +
	"\aversion\x18\x02 \x01(\x05R\aversion\x12,\n" +
	"\x11net_refund_amount\x18\x03 \x01(\x03R\x11net_refund_amount\x120\n" +
	"\x13total_refund_amount\x18\x04 \x01(\x03R\x13total_refund_amount\x12,\n" +
	"\x11user_topup_amount\x18\x05 \x01(\x03R\x11user_topup_amount\x120\n" +
	"\x13user_topup_required\x18\x06 \x01(\bR\x13user_topup_required\"\x93\x02\n" +
	"\bBasePlan\x12\x1f\n" +
	"\bplan_key\x18\x01 \x01(\tH\x00R\bplan_key\x88\x01\x01\x12\"\n" +
	"\ftenor_number\x18\x02 \x01(\x03R\ftenor_number\x12\x1e\n" +
	"\n" +
	"emi_amount\x18\x03 \x01(\x03R\n" +
	"emi_amount\x12\"\n" +
	"\ftotal_amount\x18\x04 \x01(\x03R\ftotal_amount\x12*\n" +
	"\x10principal_amount\x18\x05 \x01(\x03R\x10principal_amount\x12E\n" +
	"\x06status\x18\x06 \x01(\x0e2-.management_service.installment.v1.PlanStatusR\x06statusB\v\n" +
	"\t_plan_key\"\xe6\x01\n" +
	"\bFullPlan\x12\x1a\n" +
	"\bplan_key\x18\x01 \x01(\tR\bplan_key\x12I\n" +
	"\tplan_info\x18\x02 \x01(\v2+.management_service.installment.v1.BasePlanR\tplan_info\x12I\n" +
	"\tcost_info\x18\x06 \x01(\v2+.management_service.installment.v1.CostInfoR\tcost_info\x12(\n" +
	"\x0fplan_detail_url\x18\a \x01(\tR\x0fplan_detail_url\"\xee\x02\n" +
	"\bCostInfo\x12,\n" +
	"\x11total_cost_amount\x18\x01 \x01(\x03R\x11total_cost_amount\x12*\n" +
	"\x10total_fee_amount\x18\x02 \x01(\x03R\x10total_fee_amount\x120\n" +
	"\x13platform_fee_amount\x18\x03 \x01(\x03R\x13platform_fee_amount\x124\n" +
	"\x15conversion_fee_amount\x18\x04 \x01(\x03R\x15conversion_fee_amount\x12(\n" +
	"\x0finterest_amount\x18\x05 \x01(\x03R\x0finterest_amount\x12L\n" +
	"\rlist_fee_info\x18\x06 \x03(\v2&.management_service.installment.v1.FeeR\rlist_fee_info\x12(\n" +
	"\x0ffee_explanation\x18\a \x01(\tR\x0ffee_explanation\"{\n" +
	"\x15PlanRepaymentSchedule\x12\x16\n" +
	"\x06amount\x18\x01 \x01(\x03R\x06amount\x12\x1a\n" +
	"\bdue_date\x18\x02 \x01(\tR\bdue_date\x12.\n" +
	"\x12installment_number\x18\x03 \x01(\x05R\x12installment_number\"\xe9\x02\n" +
	"\x11RepaymentSchedule\x12\x16\n" +
	"\x06seq_no\x18\x01 \x01(\x05R\x06seq_no\x12F\n" +
	"\x06status\x18\x02 \x01(\x0e2..management_service.installment.v1.RepayStatusR\x06status\x12\x1a\n" +
	"\bdue_date\x18\x03 \x01(\tR\bdue_date\x12\x1e\n" +
	"\n" +
	"due_amount\x18\x04 \x01(\x03R\n" +
	"due_amount\x12&\n" +
	"\x0epenalty_amount\x18\x05 \x01(\x03R\x0epenalty_amount\x12*\n" +
	"\x10total_due_amount\x18\x06 \x01(\x03R\x10total_due_amount\x12,\n" +
	"\x11total_paid_amount\x18\a \x01(\x03R\x11total_paid_amount\x126\n" +
	"\x16total_remaining_amount\x18\b \x01(\x03R\x16total_remaining_amount\"\xf5\x02\n" +
	"\x12EarlyDischargeBase\x12I\n" +
	"\x04kind\x18\x01 \x01(\x0e25.management_service.installment.v1.EarlyDischargeKindR\x04kind\x12O\n" +
	"\x06status\x18\x02 \x01(\x0e27.management_service.installment.v1.EarlyDischargeStatusR\x06status\x12\x1d\n" +
	"\aallowed\x18\x03 \x01(\bH\x00R\aallowed\x88\x01\x01\x12#\n" +
	"\n" +
	"in_session\x18\x05 \x01(\bH\x01R\n" +
	"in_session\x88\x01\x01\x12S\n" +
	"\fsession_info\x18\x04 \x01(\v2*.management_service.installment.v1.SessionH\x02R\fsession_info\x88\x01\x01B\n" +
	"\n" +
	"\b_allowedB\r\n" +
	"\v_in_sessionB\x0f\n" +
	"\r_session_info\"\xa9\x04\n" +
	"\x14EarlyDischargeDetail\x126\n" +
	"\x16total_discharge_amount\x18\x01 \x01(\x03R\x16total_discharge_amount\x126\n" +
	"\x16early_discharge_amount\x18\x02 \x01(\x03R\x16early_discharge_amount\x120\n" +
	"\x13early_discharge_fee\x18\x03 \x01(\x03R\x13early_discharge_fee\x12:\n" +
	"\x18total_outstanding_amount\x18\x04 \x01(\x03R\x18total_outstanding_amount\x12G\n" +
	"\x1coutstanding_principal_amount\x18\x05 \x01(\x03H\x00R\x1coutstanding_principal_amount\x88\x01\x01\x12E\n" +
	"\x1boutstanding_interest_amount\x18\x06 \x01(\x03H\x01R\x1boutstanding_interest_amount\x88\x01\x01\x12C\n" +
	"\x1aoutstanding_penalty_amount\x18\a \x01(\x03H\x02R\x1aoutstanding_penalty_amount\x88\x01\x01B\x1f\n" +
	"\x1d_outstanding_principal_amountB\x1e\n" +
	"\x1c_outstanding_interest_amountB\x1d\n" +
	"\x1b_outstanding_penalty_amount\"\xbc\x01\n" +
	"\x0eEarlyDischarge\x12I\n" +
	"\x04info\x18\x01 \x01(\v25.management_service.installment.v1.EarlyDischargeBaseR\x04info\x12T\n" +
	"\x06detail\x18\x02 \x01(\v27.management_service.installment.v1.EarlyDischargeDetailH\x00R\x06detail\x88\x01\x01B\t\n" +
	"\a_detail\"\xc4\x01\n" +
	"\x17EarlyDischargeForRefund\x12O\n" +
	"\x06status\x18\x01 \x01(\x0e27.management_service.installment.v1.EarlyDischargeStatusR\x06status\x12*\n" +
	"\x10discharge_amount\x18\x02 \x01(\x03R\x10discharge_amount\x12,\n" +
	"\x11session_available\x18\x03 \x01(\bR\x11session_available\"\xdf\x01\n" +
	"\x10InteractionGuide\x12A\n" +
	"\x06action\x18\x01 \x01(\v2).management_service.installment.v1.ActionR\x06action\x12D\n" +
	"\amessage\x18\x02 \x01(\v2*.management_service.installment.v1.MessageR\amessage\x12B\n" +
	"\x06notice\x18\x03 \x01(\v2*.management_service.installment.v1.MessageR\x06notice\"\x91\x01\n" +
	"\x06Action\x12A\n" +
	"\x04type\x18\x01 \x01(\x0e2-.management_service.installment.v1.ActionTypeR\x04type\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x17\n" +
	"\azpi_url\x18\x03 \x01(\tR\x06zpiUrl\x12\x17\n" +
	"\azpa_url\x18\x04 \x01(\tR\x06zpaUrl\"3\n" +
	"\aMessage\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x14\n" +
	"\x05color\x18\x02 \x01(\tR\x05color\"\xa4\x01\n" +
	"\x03Fee\x12E\n" +
	"\bfee_type\x18\x01 \x01(\x0e2*.management_service.installment.v1.FeeTypeR\afeeType\x12\x1d\n" +
	"\n" +
	"fee_amount\x18\x02 \x01(\x03R\tfeeAmount\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"message_en\x18\x04 \x01(\tR\tmessageEn\"E\n" +
	"\aSession\x12\x1e\n" +
	"\n" +
	"start_time\x18\x01 \x01(\tR\n" +
	"start_time\x12\x1a\n" +
	"\bend_time\x18\x02 \x01(\tR\bend_time*t\n" +
	"\n" +
	"PlanStatus\x12\x1b\n" +
	"\x17PLAN_STATUS_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12PLAN_STATUS_ACTIVE\x10\x01\x12\x18\n" +
	"\x14PLAN_STATUS_INACTIVE\x10\x02\x12\x17\n" +
	"\x13PLAN_STATUS_INVALID\x10\x03*\x8c\x01\n" +
	"\vRepayStatus\x12\x1c\n" +
	"\x18REPAY_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14REPAY_STATUS_PENDING\x10\x01\x12\x14\n" +
	"\x10REPAY_STATUS_DUE\x10\x02\x12\x15\n" +
	"\x11REPAY_STATUS_PAID\x10\x03\x12\x18\n" +
	"\x14REPAY_STATUS_OVERDUE\x10\x04*\x90\x01\n" +
	"\x11InstallmentStatus\x12\"\n" +
	"\x1eINSTALLMENT_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17INSTALLMENT_STATUS_INIT\x10\x01\x12\x1b\n" +
	"\x17INSTALLMENT_STATUS_OPEN\x10\x02\x12\x1d\n" +
	"\x19INSTALLMENT_STATUS_CLOSED\x10\x03*|\n" +
	"\x12EarlyDischargeKind\x12$\n" +
	" EARLY_DISCHARGE_KIND_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bEARLY_DISCHARGE_KIND_NORMAL\x10\x01\x12\x1f\n" +
	"\x1bEARLY_DISCHARGE_KIND_REFUND\x10\x02*\xd4\x01\n" +
	"\x14EarlyDischargeStatus\x12&\n" +
	"\"EARLY_DISCHARGE_STATUS_UNSPECIFIED\x10\x00\x12#\n" +
	"\x1fEARLY_DISCHARGE_STATUS_ELIGIBLE\x10\x01\x12%\n" +
	"!EARLY_DISCHARGE_STATUS_INELIGIBLE\x10\x02\x12%\n" +
	"!EARLY_DISCHARGE_STATUS_PROCESSING\x10\x03\x12!\n" +
	"\x1dEARLY_DISCHARGE_STATUS_CLOSED\x10\x04*I\n" +
	"\n" +
	"ActionType\x12\x1b\n" +
	"\x17ACTION_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aACTION_TYPE_PLAN_SELECTION\x10\x01*O\n" +
	"\aFeeType\x12\x14\n" +
	"\x10FEE_TYPE_INVALID\x10\x00\x12\x17\n" +
	"\x13FEE_TYPE_CONVERSION\x10\x01\x12\x15\n" +
	"\x11FEE_TYPE_PLATFORM\x10\x022\xc9\f\n" +
	"\vInstallment\x12\xc8\x01\n" +
	"\x17ListClientEligiblePlans\x12A.management_service.installment.v1.ListClientEligiblePlansRequest\x1aB.management_service.installment.v1.ListClientEligiblePlansResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/installment/v1/plans/eligible\x12\xba\x01\n" +
	"\x13GetClientPlanDetail\x12=.management_service.installment.v1.GetClientPlanDetailRequest\x1a>.management_service.installment.v1.GetClientPlanDetailResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/installment/v1/plans/detail\x12\xb8\x01\n" +
	"\x10AcceptClientPlan\x12:.management_service.installment.v1.AcceptClientPlanRequest\x1a;.management_service.installment.v1.AcceptClientPlanResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /installment/v1/plans/acceptance\x12\x99\x01\n" +
	"\x14GetPaymentPlanDetail\x12>.management_service.installment.v1.GetPaymentPlanDetailRequest\x1a?.management_service.installment.v1.GetPaymentPlanDetailResponse\"\x00\x12\xb5\x01\n" +
	"\x14GetClientInstallment\x12>.management_service.installment.v1.GetClientInstallmentRequest\x1a?.management_service.installment.v1.GetClientInstallmentResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/installment/v1/info\x12\xc9\x01\n" +
	"\x17GetClientEarlyDischarge\x12A.management_service.installment.v1.GetClientEarlyDischargeRequest\x1aB.management_service.installment.v1.GetClientEarlyDischargeResponse\"'\x82\xd3\xe4\x93\x02!\x12\x1f/installment/v1/early-discharge\x12\x99\x01\n" +
	"\x14GetInstallmentStatus\x12>.management_service.installment.v1.GetInstallmentStatusRequest\x1a?.management_service.installment.v1.GetInstallmentStatusResponse\"\x00\x12\xa2\x01\n" +
	"\x17GetEarlyDischargeRefund\x12A.management_service.installment.v1.GetEarlyDischargeRefundRequest\x1aB.management_service.installment.v1.GetEarlyDischargeRefundResponse\"\x00\x12\x95\x01\n" +
	"\x17NotifyInstallmentRefund\x124.management_service.installment.v1.InstallmentRefund\x1aB.management_service.installment.v1.NotifyInstallmentRefundResponse\"\x00B+Z)installment/api/installment-service/v1;v1b\x06proto3"

var (
	file_management_service_installment_v1_installment_service_proto_rawDescOnce sync.Once
	file_management_service_installment_v1_installment_service_proto_rawDescData []byte
)

func file_management_service_installment_v1_installment_service_proto_rawDescGZIP() []byte {
	file_management_service_installment_v1_installment_service_proto_rawDescOnce.Do(func() {
		file_management_service_installment_v1_installment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_management_service_installment_v1_installment_service_proto_rawDesc), len(file_management_service_installment_v1_installment_service_proto_rawDesc)))
	})
	return file_management_service_installment_v1_installment_service_proto_rawDescData
}

var file_management_service_installment_v1_installment_service_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_management_service_installment_v1_installment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_management_service_installment_v1_installment_service_proto_goTypes = []any{
	(PlanStatus)(0),                               // 0: management_service.installment.v1.PlanStatus
	(RepayStatus)(0),                              // 1: management_service.installment.v1.RepayStatus
	(InstallmentStatus)(0),                        // 2: management_service.installment.v1.InstallmentStatus
	(EarlyDischargeKind)(0),                       // 3: management_service.installment.v1.EarlyDischargeKind
	(EarlyDischargeStatus)(0),                     // 4: management_service.installment.v1.EarlyDischargeStatus
	(ActionType)(0),                               // 5: management_service.installment.v1.ActionType
	(FeeType)(0),                                  // 6: management_service.installment.v1.FeeType
	(*ListClientEligiblePlansRequest)(nil),        // 7: management_service.installment.v1.ListClientEligiblePlansRequest
	(*ListClientEligiblePlansResponse)(nil),       // 8: management_service.installment.v1.ListClientEligiblePlansResponse
	(*GetClientPlanDetailRequest)(nil),            // 9: management_service.installment.v1.GetClientPlanDetailRequest
	(*GetClientPlanDetailResponse)(nil),           // 10: management_service.installment.v1.GetClientPlanDetailResponse
	(*AcceptClientPlanRequest)(nil),               // 11: management_service.installment.v1.AcceptClientPlanRequest
	(*AcceptClientPlanResponse)(nil),              // 12: management_service.installment.v1.AcceptClientPlanResponse
	(*GetPaymentPlanDetailRequest)(nil),           // 13: management_service.installment.v1.GetPaymentPlanDetailRequest
	(*GetPaymentPlanDetailResponse)(nil),          // 14: management_service.installment.v1.GetPaymentPlanDetailResponse
	(*GetClientInstallmentRequest)(nil),           // 15: management_service.installment.v1.GetClientInstallmentRequest
	(*GetClientInstallmentResponse)(nil),          // 16: management_service.installment.v1.GetClientInstallmentResponse
	(*GetInstallmentStatusRequest)(nil),           // 17: management_service.installment.v1.GetInstallmentStatusRequest
	(*GetInstallmentStatusResponse)(nil),          // 18: management_service.installment.v1.GetInstallmentStatusResponse
	(*GetClientEarlyDischargeRequest)(nil),        // 19: management_service.installment.v1.GetClientEarlyDischargeRequest
	(*GetClientEarlyDischargeResponse)(nil),       // 20: management_service.installment.v1.GetClientEarlyDischargeResponse
	(*GetEarlyDischargeRefundRequest)(nil),        // 21: management_service.installment.v1.GetEarlyDischargeRefundRequest
	(*GetEarlyDischargeRefundResponse)(nil),       // 22: management_service.installment.v1.GetEarlyDischargeRefundResponse
	(*NotifyInstallmentRefundResponse)(nil),       // 23: management_service.installment.v1.NotifyInstallmentRefundResponse
	(*InstallmentBase)(nil),                       // 24: management_service.installment.v1.InstallmentBase
	(*InstallmentData)(nil),                       // 25: management_service.installment.v1.InstallmentData
	(*InstallmentRefund)(nil),                     // 26: management_service.installment.v1.InstallmentRefund
	(*BasePlan)(nil),                              // 27: management_service.installment.v1.BasePlan
	(*FullPlan)(nil),                              // 28: management_service.installment.v1.FullPlan
	(*CostInfo)(nil),                              // 29: management_service.installment.v1.CostInfo
	(*PlanRepaymentSchedule)(nil),                 // 30: management_service.installment.v1.PlanRepaymentSchedule
	(*RepaymentSchedule)(nil),                     // 31: management_service.installment.v1.RepaymentSchedule
	(*EarlyDischargeBase)(nil),                    // 32: management_service.installment.v1.EarlyDischargeBase
	(*EarlyDischargeDetail)(nil),                  // 33: management_service.installment.v1.EarlyDischargeDetail
	(*EarlyDischarge)(nil),                        // 34: management_service.installment.v1.EarlyDischarge
	(*EarlyDischargeForRefund)(nil),               // 35: management_service.installment.v1.EarlyDischargeForRefund
	(*InteractionGuide)(nil),                      // 36: management_service.installment.v1.InteractionGuide
	(*Action)(nil),                                // 37: management_service.installment.v1.Action
	(*Message)(nil),                               // 38: management_service.installment.v1.Message
	(*Fee)(nil),                                   // 39: management_service.installment.v1.Fee
	(*Session)(nil),                               // 40: management_service.installment.v1.Session
	(*ListClientEligiblePlansResponse_Plan)(nil),  // 41: management_service.installment.v1.ListClientEligiblePlansResponse.Plan
	(*GetPaymentPlanDetailRequest_OrderInfo)(nil), // 42: management_service.installment.v1.GetPaymentPlanDetailRequest.OrderInfo
	(*structpb.Struct)(nil),                       // 43: google.protobuf.Struct
}
var file_management_service_installment_v1_installment_service_proto_depIdxs = []int32{
	41, // 0: management_service.installment.v1.ListClientEligiblePlansResponse.plan_options:type_name -> management_service.installment.v1.ListClientEligiblePlansResponse.Plan
	27, // 1: management_service.installment.v1.GetClientPlanDetailResponse.plan_info:type_name -> management_service.installment.v1.BasePlan
	29, // 2: management_service.installment.v1.GetClientPlanDetailResponse.cost_info:type_name -> management_service.installment.v1.CostInfo
	42, // 3: management_service.installment.v1.GetPaymentPlanDetailRequest.order_info:type_name -> management_service.installment.v1.GetPaymentPlanDetailRequest.OrderInfo
	27, // 4: management_service.installment.v1.GetPaymentPlanDetailResponse.plan_info:type_name -> management_service.installment.v1.BasePlan
	36, // 5: management_service.installment.v1.GetPaymentPlanDetailResponse.interaction_guide:type_name -> management_service.installment.v1.InteractionGuide
	43, // 6: management_service.installment.v1.GetPaymentPlanDetailResponse.metadata:type_name -> google.protobuf.Struct
	25, // 7: management_service.installment.v1.GetClientInstallmentResponse.installment:type_name -> management_service.installment.v1.InstallmentData
	34, // 8: management_service.installment.v1.GetClientInstallmentResponse.early_discharge:type_name -> management_service.installment.v1.EarlyDischarge
	31, // 9: management_service.installment.v1.GetClientInstallmentResponse.repayment_schedules:type_name -> management_service.installment.v1.RepaymentSchedule
	2,  // 10: management_service.installment.v1.GetInstallmentStatusResponse.status:type_name -> management_service.installment.v1.InstallmentStatus
	24, // 11: management_service.installment.v1.GetInstallmentStatusResponse.info:type_name -> management_service.installment.v1.InstallmentBase
	34, // 12: management_service.installment.v1.GetClientEarlyDischargeResponse.early_discharge:type_name -> management_service.installment.v1.EarlyDischarge
	35, // 13: management_service.installment.v1.GetEarlyDischargeRefundResponse.discharge_info:type_name -> management_service.installment.v1.EarlyDischargeForRefund
	2,  // 14: management_service.installment.v1.InstallmentData.status:type_name -> management_service.installment.v1.InstallmentStatus
	0,  // 15: management_service.installment.v1.BasePlan.status:type_name -> management_service.installment.v1.PlanStatus
	27, // 16: management_service.installment.v1.FullPlan.plan_info:type_name -> management_service.installment.v1.BasePlan
	29, // 17: management_service.installment.v1.FullPlan.cost_info:type_name -> management_service.installment.v1.CostInfo
	39, // 18: management_service.installment.v1.CostInfo.list_fee_info:type_name -> management_service.installment.v1.Fee
	1,  // 19: management_service.installment.v1.RepaymentSchedule.status:type_name -> management_service.installment.v1.RepayStatus
	3,  // 20: management_service.installment.v1.EarlyDischargeBase.kind:type_name -> management_service.installment.v1.EarlyDischargeKind
	4,  // 21: management_service.installment.v1.EarlyDischargeBase.status:type_name -> management_service.installment.v1.EarlyDischargeStatus
	40, // 22: management_service.installment.v1.EarlyDischargeBase.session_info:type_name -> management_service.installment.v1.Session
	32, // 23: management_service.installment.v1.EarlyDischarge.info:type_name -> management_service.installment.v1.EarlyDischargeBase
	33, // 24: management_service.installment.v1.EarlyDischarge.detail:type_name -> management_service.installment.v1.EarlyDischargeDetail
	4,  // 25: management_service.installment.v1.EarlyDischargeForRefund.status:type_name -> management_service.installment.v1.EarlyDischargeStatus
	37, // 26: management_service.installment.v1.InteractionGuide.action:type_name -> management_service.installment.v1.Action
	38, // 27: management_service.installment.v1.InteractionGuide.message:type_name -> management_service.installment.v1.Message
	38, // 28: management_service.installment.v1.InteractionGuide.notice:type_name -> management_service.installment.v1.Message
	5,  // 29: management_service.installment.v1.Action.type:type_name -> management_service.installment.v1.ActionType
	6,  // 30: management_service.installment.v1.Fee.fee_type:type_name -> management_service.installment.v1.FeeType
	27, // 31: management_service.installment.v1.ListClientEligiblePlansResponse.Plan.plan_info:type_name -> management_service.installment.v1.BasePlan
	29, // 32: management_service.installment.v1.ListClientEligiblePlansResponse.Plan.cost_info:type_name -> management_service.installment.v1.CostInfo
	30, // 33: management_service.installment.v1.ListClientEligiblePlansResponse.Plan.scheduled_repayments:type_name -> management_service.installment.v1.PlanRepaymentSchedule
	7,  // 34: management_service.installment.v1.Installment.ListClientEligiblePlans:input_type -> management_service.installment.v1.ListClientEligiblePlansRequest
	9,  // 35: management_service.installment.v1.Installment.GetClientPlanDetail:input_type -> management_service.installment.v1.GetClientPlanDetailRequest
	11, // 36: management_service.installment.v1.Installment.AcceptClientPlan:input_type -> management_service.installment.v1.AcceptClientPlanRequest
	13, // 37: management_service.installment.v1.Installment.GetPaymentPlanDetail:input_type -> management_service.installment.v1.GetPaymentPlanDetailRequest
	15, // 38: management_service.installment.v1.Installment.GetClientInstallment:input_type -> management_service.installment.v1.GetClientInstallmentRequest
	19, // 39: management_service.installment.v1.Installment.GetClientEarlyDischarge:input_type -> management_service.installment.v1.GetClientEarlyDischargeRequest
	17, // 40: management_service.installment.v1.Installment.GetInstallmentStatus:input_type -> management_service.installment.v1.GetInstallmentStatusRequest
	21, // 41: management_service.installment.v1.Installment.GetEarlyDischargeRefund:input_type -> management_service.installment.v1.GetEarlyDischargeRefundRequest
	26, // 42: management_service.installment.v1.Installment.NotifyInstallmentRefund:input_type -> management_service.installment.v1.InstallmentRefund
	8,  // 43: management_service.installment.v1.Installment.ListClientEligiblePlans:output_type -> management_service.installment.v1.ListClientEligiblePlansResponse
	10, // 44: management_service.installment.v1.Installment.GetClientPlanDetail:output_type -> management_service.installment.v1.GetClientPlanDetailResponse
	12, // 45: management_service.installment.v1.Installment.AcceptClientPlan:output_type -> management_service.installment.v1.AcceptClientPlanResponse
	14, // 46: management_service.installment.v1.Installment.GetPaymentPlanDetail:output_type -> management_service.installment.v1.GetPaymentPlanDetailResponse
	16, // 47: management_service.installment.v1.Installment.GetClientInstallment:output_type -> management_service.installment.v1.GetClientInstallmentResponse
	20, // 48: management_service.installment.v1.Installment.GetClientEarlyDischarge:output_type -> management_service.installment.v1.GetClientEarlyDischargeResponse
	18, // 49: management_service.installment.v1.Installment.GetInstallmentStatus:output_type -> management_service.installment.v1.GetInstallmentStatusResponse
	22, // 50: management_service.installment.v1.Installment.GetEarlyDischargeRefund:output_type -> management_service.installment.v1.GetEarlyDischargeRefundResponse
	23, // 51: management_service.installment.v1.Installment.NotifyInstallmentRefund:output_type -> management_service.installment.v1.NotifyInstallmentRefundResponse
	43, // [43:52] is the sub-list for method output_type
	34, // [34:43] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_management_service_installment_v1_installment_service_proto_init() }
func file_management_service_installment_v1_installment_service_proto_init() {
	if File_management_service_installment_v1_installment_service_proto != nil {
		return
	}
	file_management_service_installment_v1_installment_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[20].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[25].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[26].OneofWrappers = []any{}
	file_management_service_installment_v1_installment_service_proto_msgTypes[27].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_management_service_installment_v1_installment_service_proto_rawDesc), len(file_management_service_installment_v1_installment_service_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_management_service_installment_v1_installment_service_proto_goTypes,
		DependencyIndexes: file_management_service_installment_v1_installment_service_proto_depIdxs,
		EnumInfos:         file_management_service_installment_v1_installment_service_proto_enumTypes,
		MessageInfos:      file_management_service_installment_v1_installment_service_proto_msgTypes,
	}.Build()
	File_management_service_installment_v1_installment_service_proto = out.File
	file_management_service_installment_v1_installment_service_proto_goTypes = nil
	file_management_service_installment_v1_installment_service_proto_depIdxs = nil
}
