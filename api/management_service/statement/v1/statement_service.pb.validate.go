// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: management_service/statement/v1/statement_service.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetClientStatementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientStatementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientStatementRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientStatementRequestMultiError, or nil if none found.
func (m *GetClientStatementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientStatementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAccountId() <= 0 {
		err := GetClientStatementRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	oneofQueryByPresent := false
	switch v := m.QueryBy.(type) {
	case *GetClientStatementRequest_StatementId:
		if v == nil {
			err := GetClientStatementRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofQueryByPresent = true

		if m.GetStatementId() <= 0 {
			err := GetClientStatementRequestValidationError{
				field:  "StatementId",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *GetClientStatementRequest_StatementDate:
		if v == nil {
			err := GetClientStatementRequestValidationError{
				field:  "QueryBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofQueryByPresent = true

		if utf8.RuneCountInString(m.GetStatementDate()) < 1 {
			err := GetClientStatementRequestValidationError{
				field:  "StatementDate",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}
	if !oneofQueryByPresent {
		err := GetClientStatementRequestValidationError{
			field:  "QueryBy",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientStatementRequestMultiError(errors)
	}

	return nil
}

// GetClientStatementRequestMultiError is an error wrapping multiple validation
// errors returned by GetClientStatementRequest.ValidateAll() if the
// designated constraints aren't met.
type GetClientStatementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientStatementRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientStatementRequestMultiError) AllErrors() []error { return m }

// GetClientStatementRequestValidationError is the validation error returned by
// GetClientStatementRequest.Validate if the designated constraints aren't met.
type GetClientStatementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientStatementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientStatementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientStatementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientStatementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientStatementRequestValidationError) ErrorName() string {
	return "GetClientStatementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientStatementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientStatementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientStatementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientStatementRequestValidationError{}

// Validate checks the field values on GetClientStatementResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientStatementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientStatementResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClientStatementResponseMultiError, or nil if none found.
func (m *GetClientStatementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientStatementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientStatementResponseValidationError{
					field:  "Statement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientStatementResponseValidationError{
					field:  "Statement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientStatementResponseValidationError{
				field:  "Statement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClientStatementResponseMultiError(errors)
	}

	return nil
}

// GetClientStatementResponseMultiError is an error wrapping multiple
// validation errors returned by GetClientStatementResponse.ValidateAll() if
// the designated constraints aren't met.
type GetClientStatementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientStatementResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientStatementResponseMultiError) AllErrors() []error { return m }

// GetClientStatementResponseValidationError is the validation error returned
// by GetClientStatementResponse.Validate if the designated constraints aren't met.
type GetClientStatementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientStatementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientStatementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientStatementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientStatementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientStatementResponseValidationError) ErrorName() string {
	return "GetClientStatementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientStatementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientStatementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientStatementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientStatementResponseValidationError{}

// Validate checks the field values on GetClientLatestStatementRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetClientLatestStatementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientLatestStatementRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetClientLatestStatementRequestMultiError, or nil if none found.
func (m *GetClientLatestStatementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientLatestStatementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAccountId() <= 0 {
		err := GetClientLatestStatementRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetClientLatestStatementRequestMultiError(errors)
	}

	return nil
}

// GetClientLatestStatementRequestMultiError is an error wrapping multiple
// validation errors returned by GetClientLatestStatementRequest.ValidateAll()
// if the designated constraints aren't met.
type GetClientLatestStatementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientLatestStatementRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientLatestStatementRequestMultiError) AllErrors() []error { return m }

// GetClientLatestStatementRequestValidationError is the validation error
// returned by GetClientLatestStatementRequest.Validate if the designated
// constraints aren't met.
type GetClientLatestStatementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientLatestStatementRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientLatestStatementRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientLatestStatementRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientLatestStatementRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientLatestStatementRequestValidationError) ErrorName() string {
	return "GetClientLatestStatementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientLatestStatementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientLatestStatementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientLatestStatementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientLatestStatementRequestValidationError{}

// Validate checks the field values on GetClientLatestStatementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetClientLatestStatementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClientLatestStatementResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetClientLatestStatementResponseMultiError, or nil if none found.
func (m *GetClientLatestStatementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClientLatestStatementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClientLatestStatementResponseValidationError{
					field:  "Statement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClientLatestStatementResponseValidationError{
					field:  "Statement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClientLatestStatementResponseValidationError{
				field:  "Statement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClientLatestStatementResponseMultiError(errors)
	}

	return nil
}

// GetClientLatestStatementResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetClientLatestStatementResponse.ValidateAll() if the designated
// constraints aren't met.
type GetClientLatestStatementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClientLatestStatementResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClientLatestStatementResponseMultiError) AllErrors() []error { return m }

// GetClientLatestStatementResponseValidationError is the validation error
// returned by GetClientLatestStatementResponse.Validate if the designated
// constraints aren't met.
type GetClientLatestStatementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClientLatestStatementResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClientLatestStatementResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClientLatestStatementResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClientLatestStatementResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClientLatestStatementResponseValidationError) ErrorName() string {
	return "GetClientLatestStatementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClientLatestStatementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClientLatestStatementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClientLatestStatementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClientLatestStatementResponseValidationError{}

// Validate checks the field values on ListClientStatementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientStatementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientStatementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClientStatementsRequestMultiError, or nil if none found.
func (m *ListClientStatementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientStatementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAccountId() <= 0 {
		err := ListClientStatementsRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFromTime() == nil {
		err := ListClientStatementsRequestValidationError{
			field:  "FromTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetToTime() == nil {
		err := ListClientStatementsRequestValidationError{
			field:  "ToTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListClientStatementsRequestMultiError(errors)
	}

	return nil
}

// ListClientStatementsRequestMultiError is an error wrapping multiple
// validation errors returned by ListClientStatementsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListClientStatementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientStatementsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientStatementsRequestMultiError) AllErrors() []error { return m }

// ListClientStatementsRequestValidationError is the validation error returned
// by ListClientStatementsRequest.Validate if the designated constraints
// aren't met.
type ListClientStatementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientStatementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientStatementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientStatementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientStatementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientStatementsRequestValidationError) ErrorName() string {
	return "ListClientStatementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientStatementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientStatementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientStatementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientStatementsRequestValidationError{}

// Validate checks the field values on ListClientStatementsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientStatementsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientStatementsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClientStatementsResponseMultiError, or nil if none found.
func (m *ListClientStatementsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientStatementsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStatements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientStatementsResponseValidationError{
						field:  fmt.Sprintf("Statements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientStatementsResponseValidationError{
						field:  fmt.Sprintf("Statements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientStatementsResponseValidationError{
					field:  fmt.Sprintf("Statements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClientStatementsResponseMultiError(errors)
	}

	return nil
}

// ListClientStatementsResponseMultiError is an error wrapping multiple
// validation errors returned by ListClientStatementsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListClientStatementsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientStatementsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientStatementsResponseMultiError) AllErrors() []error { return m }

// ListClientStatementsResponseValidationError is the validation error returned
// by ListClientStatementsResponse.Validate if the designated constraints
// aren't met.
type ListClientStatementsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientStatementsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientStatementsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientStatementsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientStatementsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientStatementsResponseValidationError) ErrorName() string {
	return "ListClientStatementsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientStatementsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientStatementsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientStatementsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientStatementsResponseValidationError{}

// Validate checks the field values on ListClientInstallmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientInstallmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientInstallmentsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListClientInstallmentsRequestMultiError, or nil if none found.
func (m *ListClientInstallmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientInstallmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAccountId() <= 0 {
		err := ListClientInstallmentsRequestValidationError{
			field:  "AccountId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStatementId() <= 0 {
		err := ListClientInstallmentsRequestValidationError{
			field:  "StatementId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListClientInstallmentsRequestMultiError(errors)
	}

	return nil
}

// ListClientInstallmentsRequestMultiError is an error wrapping multiple
// validation errors returned by ListClientInstallmentsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListClientInstallmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientInstallmentsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientInstallmentsRequestMultiError) AllErrors() []error { return m }

// ListClientInstallmentsRequestValidationError is the validation error
// returned by ListClientInstallmentsRequest.Validate if the designated
// constraints aren't met.
type ListClientInstallmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientInstallmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientInstallmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientInstallmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientInstallmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientInstallmentsRequestValidationError) ErrorName() string {
	return "ListClientInstallmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientInstallmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientInstallmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientInstallmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientInstallmentsRequestValidationError{}

// Validate checks the field values on ListClientInstallmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClientInstallmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClientInstallmentsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListClientInstallmentsResponseMultiError, or nil if none found.
func (m *ListClientInstallmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClientInstallmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetInstallments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClientInstallmentsResponseValidationError{
						field:  fmt.Sprintf("Installments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClientInstallmentsResponseValidationError{
						field:  fmt.Sprintf("Installments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClientInstallmentsResponseValidationError{
					field:  fmt.Sprintf("Installments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClientInstallmentsResponseMultiError(errors)
	}

	return nil
}

// ListClientInstallmentsResponseMultiError is an error wrapping multiple
// validation errors returned by ListClientInstallmentsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListClientInstallmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClientInstallmentsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClientInstallmentsResponseMultiError) AllErrors() []error { return m }

// ListClientInstallmentsResponseValidationError is the validation error
// returned by ListClientInstallmentsResponse.Validate if the designated
// constraints aren't met.
type ListClientInstallmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClientInstallmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClientInstallmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClientInstallmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClientInstallmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClientInstallmentsResponseValidationError) ErrorName() string {
	return "ListClientInstallmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClientInstallmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClientInstallmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClientInstallmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClientInstallmentsResponseValidationError{}

// Validate checks the field values on StatementData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatementData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatementDataMultiError, or
// nil if none found.
func (m *StatementData) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountId

	// no validation rules for DueDate

	// no validation rules for IncurredDate

	// no validation rules for PaidStatus

	// no validation rules for OutstandingAmount

	// no validation rules for OutstandingRepaid

	// no validation rules for InstallmentFeeAmount

	// no validation rules for InstallmentFeeRepaid

	// no validation rules for TotalDueAmount

	// no validation rules for TotalDueRepaid

	// no validation rules for TotalDueRemaining

	// no validation rules for PenaltyAmount

	if len(errors) > 0 {
		return StatementDataMultiError(errors)
	}

	return nil
}

// StatementDataMultiError is an error wrapping multiple validation errors
// returned by StatementData.ValidateAll() if the designated constraints
// aren't met.
type StatementDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementDataMultiError) AllErrors() []error { return m }

// StatementDataValidationError is the validation error returned by
// StatementData.Validate if the designated constraints aren't met.
type StatementDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementDataValidationError) ErrorName() string { return "StatementDataValidationError" }

// Error satisfies the builtin error interface
func (e StatementDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementDataValidationError{}

// Validate checks the field values on StatementItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatementItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatementItemMultiError, or
// nil if none found.
func (m *StatementItem) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountId

	// no validation rules for DueDate

	// no validation rules for IncurredDate

	// no validation rules for PaidStatus

	// no validation rules for TotalDueAmount

	// no validation rules for TotalDueRepaid

	// no validation rules for TotalDueRemaining

	if len(errors) > 0 {
		return StatementItemMultiError(errors)
	}

	return nil
}

// StatementItemMultiError is an error wrapping multiple validation errors
// returned by StatementItem.ValidateAll() if the designated constraints
// aren't met.
type StatementItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementItemMultiError) AllErrors() []error { return m }

// StatementItemValidationError is the validation error returned by
// StatementItem.Validate if the designated constraints aren't met.
type StatementItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementItemValidationError) ErrorName() string { return "StatementItemValidationError" }

// Error satisfies the builtin error interface
func (e StatementItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementItemValidationError{}

// Validate checks the field values on StatementInstallment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StatementInstallment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatementInstallment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StatementInstallmentMultiError, or nil if none found.
func (m *StatementInstallment) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementInstallment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StatementId

	// no validation rules for ZpTransId

	// no validation rules for InstallmentId

	// no validation rules for TransactionDesc

	// no validation rules for OutstandingAmount

	if all {
		switch v := interface{}(m.GetOutstandingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatementInstallmentValidationError{
					field:  "OutstandingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatementInstallmentValidationError{
					field:  "OutstandingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstandingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatementInstallmentValidationError{
				field:  "OutstandingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StatementInstallmentMultiError(errors)
	}

	return nil
}

// StatementInstallmentMultiError is an error wrapping multiple validation
// errors returned by StatementInstallment.ValidateAll() if the designated
// constraints aren't met.
type StatementInstallmentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementInstallmentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementInstallmentMultiError) AllErrors() []error { return m }

// StatementInstallmentValidationError is the validation error returned by
// StatementInstallment.Validate if the designated constraints aren't met.
type StatementInstallmentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementInstallmentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementInstallmentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementInstallmentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementInstallmentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementInstallmentValidationError) ErrorName() string {
	return "StatementInstallmentValidationError"
}

// Error satisfies the builtin error interface
func (e StatementInstallmentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementInstallment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementInstallmentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementInstallmentValidationError{}

// Validate checks the field values on StatementInstallment_OutstandingDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StatementInstallment_OutstandingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StatementInstallment_OutstandingDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// StatementInstallment_OutstandingDetailsMultiError, or nil if none found.
func (m *StatementInstallment_OutstandingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StatementInstallment_OutstandingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalDueAmount

	// no validation rules for TotalOverdueAmount

	// no validation rules for TotalPenaltyAmount

	if len(errors) > 0 {
		return StatementInstallment_OutstandingDetailsMultiError(errors)
	}

	return nil
}

// StatementInstallment_OutstandingDetailsMultiError is an error wrapping
// multiple validation errors returned by
// StatementInstallment_OutstandingDetails.ValidateAll() if the designated
// constraints aren't met.
type StatementInstallment_OutstandingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatementInstallment_OutstandingDetailsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatementInstallment_OutstandingDetailsMultiError) AllErrors() []error { return m }

// StatementInstallment_OutstandingDetailsValidationError is the validation
// error returned by StatementInstallment_OutstandingDetails.Validate if the
// designated constraints aren't met.
type StatementInstallment_OutstandingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatementInstallment_OutstandingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatementInstallment_OutstandingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatementInstallment_OutstandingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatementInstallment_OutstandingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatementInstallment_OutstandingDetailsValidationError) ErrorName() string {
	return "StatementInstallment_OutstandingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e StatementInstallment_OutstandingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatementInstallment_OutstandingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatementInstallment_OutstandingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatementInstallment_OutstandingDetailsValidationError{}
