syntax = "proto3";

package management_service.statement.v1;

option go_package = "installment/api/management-service/v1;v1";

import "validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service Statement {
  rpc GetClientStatement(GetClientStatementRequest) returns (GetClientStatementResponse) {
    option (google.api.http) = {
      get: "/statement/v1/info",
    };
  };
  rpc GetClientLatestStatement(GetClientLatestStatementRequest) returns (GetClientLatestStatementResponse) {
    option (google.api.http) = {
      get: "/statement/v1/latest"
    };
  };
  rpc ListClientStatements(ListClientStatementsRequest) returns (ListClientStatementsResponse) {
    option (google.api.http) = {
      get: "/statement/v1/list"
    };
  };
  // ListClientInstallments returns a list of installments for a given statement.
  rpc ListClientInstallments(ListClientInstallmentsRequest) returns (ListClientInstallmentsResponse) {
    option (google.api.http) = {
      get: "/statement/v1/installments"
    };
  };
}

message GetClientStatementRequest{
  int64 account_id = 1 [json_name="account_id", (validate.rules).int64.gt = 0];
  oneof query_by {
    option (validate.required) = true;
    int64 statement_id = 2 [json_name="statement_id", (validate.rules).int64.gt = 0];
    string statement_date = 3 [json_name="incurred_date", (validate.rules).string.min_len = 1];
  }
}

message GetClientStatementResponse{
  StatementData statement = 1;
}

message GetClientLatestStatementRequest{
  int64 account_id = 1 [json_name="account_id", (validate.rules).int64.gt = 0];
}

message GetClientLatestStatementResponse{
  StatementData statement = 1;
}

message ListClientStatementsRequest{
  int64 account_id = 1 [json_name="account_id", (validate.rules).int64.gt = 0];
  google.protobuf.Timestamp from_time = 2 [json_name="from_time", (validate.rules).timestamp.required = true];
  google.protobuf.Timestamp to_time = 3 [json_name="to_time", (validate.rules).timestamp.required = true];
}

message ListClientStatementsResponse{
  repeated StatementItem statements = 1;
}

message ListClientInstallmentsRequest{
  int64 account_id = 1 [json_name="account_id", (validate.rules).int64.gt = 0];
  int64 statement_id = 2 [json_name="statement_id", (validate.rules).int64.gt = 0];
}

message ListClientInstallmentsResponse{
  repeated StatementInstallment installments = 1;
}

// StatementData represents a statement detail for a given account.
message StatementData {
  // Unique identifier for the statement.
  int64 id = 1 [json_name="id"];

  // Identifier for the user account.
  int64 account_id = 2 [json_name="account_id"];

  // The repayment grace end date.
  string due_date = 3 [json_name="due_date"];

  // The date the statement was incurred.
  string incurred_date = 4 [json_name="incurred_date"];

  // The paid status of the statement.
  PaidStatus paid_status = 5 [json_name="paid_status"];

  // Snapshot of the outstanding amount at the statement date.
  int64 outstanding_amount = 6 [json_name="outstanding_amount"];

  // Amount that has been repaid towards the outstanding amount.
  int64 outstanding_repaid = 7 [json_name="outstanding_repaid"];

  // Snapshot of the total fees of the installment at the statement date.
  int64 installment_fee_amount = 8 [json_name="installment_fee_amount"];

  // Total fees repaid.
  int64 installment_fee_repaid = 9 [json_name="installment_fee_repaid"];

  // The total amount due, combining outstanding amount and installment fee amount.
  int64 total_due_amount = 10 [json_name="total_due_amount"];

  // The total amount repaid, combining outstanding repaid and installment fee repaid.
  int64 total_due_repaid = 11 [json_name="total_due_repaid"];

  // The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
  int64 total_due_remaining = 12 [json_name="total_due_remaining"];

  // The penalty amount when the statement is overdue.
  int64 penalty_amount = 13 [json_name="penalty_amount"];
}

// StatementItem represents item in statement list, it have less info than statement data.
message StatementItem {
  // Unique identifier for the statement.
  int64 id = 1 [json_name="id"];

  // Identifier for the user account.
  int64 account_id = 2 [json_name="account_id"];

  // The repayment grace end date.
  string due_date = 3 [json_name="due_date"];

  // The date the statement was incurred.
  string incurred_date = 4 [json_name="incurred_date"];

  // The paid status of the statement.
  PaidStatus paid_status = 5 [json_name="paid_status"];

  // The total amount due, combining outstanding amount and installment fee amount.
  int64 total_due_amount = 6 [json_name="total_due_amount"];

  // The total amount repaid, combining outstanding repaid and installment fee repaid.
  int64 total_due_repaid = 7 [json_name="total_due_repaid"];

  // The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
  int64 total_due_remaining = 8 [json_name="total_due_remaining"];
}

message StatementInstallment {
  message OutstandingDetails {
    int64 total_due_amount = 1 [json_name="total_due_amount"];
    int64 total_overdue_amount = 2 [json_name="total_overdue_amount"];
    int64 total_penalty_amount = 3 [json_name="total_penalty_amount"];
  }
  int64 id = 1 [json_name="id"];
  int64 statement_id = 2 [json_name="statement_id"];
  int64 zp_trans_id = 3 [json_name="zp_trans_id"];
  int64 installment_id = 4 [json_name="installment_id"];
  string transaction_desc = 5 [json_name="transaction_desc"];
  int64 outstanding_amount = 6 [json_name="outstanding_amount"];
  OutstandingDetails outstanding_details = 7 [json_name="outstanding_details"];
}

enum PaidStatus {
  PAID_STATUS_UNSPECIFIED = 0;
  PAID_STATUS_PAID = 1;
  PAID_STATUS_UNPAID = 2;
  PAID_STATUS_PARTIALLY_PAID = 3;
}
