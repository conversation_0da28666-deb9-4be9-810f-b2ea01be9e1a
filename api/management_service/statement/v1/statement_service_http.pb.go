// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.28.3
// source: management_service/statement/v1/statement_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationStatementGetClientLatestStatement = "/management_service.statement.v1.Statement/GetClientLatestStatement"
const OperationStatementGetClientStatement = "/management_service.statement.v1.Statement/GetClientStatement"
const OperationStatementListClientInstallments = "/management_service.statement.v1.Statement/ListClientInstallments"
const OperationStatementListClientStatements = "/management_service.statement.v1.Statement/ListClientStatements"

type StatementHTTPServer interface {
	GetClientLatestStatement(context.Context, *GetClientLatestStatementRequest) (*GetClientLatestStatementResponse, error)
	GetClientStatement(context.Context, *GetClientStatementRequest) (*GetClientStatementResponse, error)
	// ListClientInstallments ListClientInstallments returns a list of installments for a given statement.
	ListClientInstallments(context.Context, *ListClientInstallmentsRequest) (*ListClientInstallmentsResponse, error)
	ListClientStatements(context.Context, *ListClientStatementsRequest) (*ListClientStatementsResponse, error)
}

func RegisterStatementHTTPServer(s *http.Server, srv StatementHTTPServer) {
	r := s.Route("/")
	r.GET("/statement/v1/info", _Statement_GetClientStatement0_HTTP_Handler(srv))
	r.GET("/statement/v1/latest", _Statement_GetClientLatestStatement0_HTTP_Handler(srv))
	r.GET("/statement/v1/list", _Statement_ListClientStatements0_HTTP_Handler(srv))
	r.GET("/statement/v1/installments", _Statement_ListClientInstallments0_HTTP_Handler(srv))
}

func _Statement_GetClientStatement0_HTTP_Handler(srv StatementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientStatementRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatementGetClientStatement)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientStatement(ctx, req.(*GetClientStatementRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientStatementResponse)
		return ctx.Result(200, reply)
	}
}

func _Statement_GetClientLatestStatement0_HTTP_Handler(srv StatementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetClientLatestStatementRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatementGetClientLatestStatement)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetClientLatestStatement(ctx, req.(*GetClientLatestStatementRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetClientLatestStatementResponse)
		return ctx.Result(200, reply)
	}
}

func _Statement_ListClientStatements0_HTTP_Handler(srv StatementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClientStatementsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatementListClientStatements)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClientStatements(ctx, req.(*ListClientStatementsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClientStatementsResponse)
		return ctx.Result(200, reply)
	}
}

func _Statement_ListClientInstallments0_HTTP_Handler(srv StatementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListClientInstallmentsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatementListClientInstallments)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListClientInstallments(ctx, req.(*ListClientInstallmentsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListClientInstallmentsResponse)
		return ctx.Result(200, reply)
	}
}

type StatementHTTPClient interface {
	GetClientLatestStatement(ctx context.Context, req *GetClientLatestStatementRequest, opts ...http.CallOption) (rsp *GetClientLatestStatementResponse, err error)
	GetClientStatement(ctx context.Context, req *GetClientStatementRequest, opts ...http.CallOption) (rsp *GetClientStatementResponse, err error)
	ListClientInstallments(ctx context.Context, req *ListClientInstallmentsRequest, opts ...http.CallOption) (rsp *ListClientInstallmentsResponse, err error)
	ListClientStatements(ctx context.Context, req *ListClientStatementsRequest, opts ...http.CallOption) (rsp *ListClientStatementsResponse, err error)
}

type StatementHTTPClientImpl struct {
	cc *http.Client
}

func NewStatementHTTPClient(client *http.Client) StatementHTTPClient {
	return &StatementHTTPClientImpl{client}
}

func (c *StatementHTTPClientImpl) GetClientLatestStatement(ctx context.Context, in *GetClientLatestStatementRequest, opts ...http.CallOption) (*GetClientLatestStatementResponse, error) {
	var out GetClientLatestStatementResponse
	pattern := "/statement/v1/latest"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatementGetClientLatestStatement))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatementHTTPClientImpl) GetClientStatement(ctx context.Context, in *GetClientStatementRequest, opts ...http.CallOption) (*GetClientStatementResponse, error) {
	var out GetClientStatementResponse
	pattern := "/statement/v1/info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatementGetClientStatement))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatementHTTPClientImpl) ListClientInstallments(ctx context.Context, in *ListClientInstallmentsRequest, opts ...http.CallOption) (*ListClientInstallmentsResponse, error) {
	var out ListClientInstallmentsResponse
	pattern := "/statement/v1/installments"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatementListClientInstallments))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *StatementHTTPClientImpl) ListClientStatements(ctx context.Context, in *ListClientStatementsRequest, opts ...http.CallOption) (*ListClientStatementsResponse, error) {
	var out ListClientStatementsResponse
	pattern := "/statement/v1/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationStatementListClientStatements))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
