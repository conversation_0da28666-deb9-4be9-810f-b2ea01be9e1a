// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: management_service/statement/v1/statement_service.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaidStatus int32

const (
	PaidStatus_PAID_STATUS_UNSPECIFIED    PaidStatus = 0
	PaidStatus_PAID_STATUS_PAID           PaidStatus = 1
	PaidStatus_PAID_STATUS_UNPAID         PaidStatus = 2
	PaidStatus_PAID_STATUS_PARTIALLY_PAID PaidStatus = 3
)

// Enum value maps for PaidStatus.
var (
	PaidStatus_name = map[int32]string{
		0: "PAID_STATUS_UNSPECIFIED",
		1: "PAID_STATUS_PAID",
		2: "PAID_STATUS_UNPAID",
		3: "PAID_STATUS_PARTIALLY_PAID",
	}
	PaidStatus_value = map[string]int32{
		"PAID_STATUS_UNSPECIFIED":    0,
		"PAID_STATUS_PAID":           1,
		"PAID_STATUS_UNPAID":         2,
		"PAID_STATUS_PARTIALLY_PAID": 3,
	}
)

func (x PaidStatus) Enum() *PaidStatus {
	p := new(PaidStatus)
	*p = x
	return p
}

func (x PaidStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaidStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_management_service_statement_v1_statement_service_proto_enumTypes[0].Descriptor()
}

func (PaidStatus) Type() protoreflect.EnumType {
	return &file_management_service_statement_v1_statement_service_proto_enumTypes[0]
}

func (x PaidStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaidStatus.Descriptor instead.
func (PaidStatus) EnumDescriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{0}
}

type GetClientStatementRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	AccountId int64                  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	// Types that are valid to be assigned to QueryBy:
	//
	//	*GetClientStatementRequest_StatementId
	//	*GetClientStatementRequest_StatementDate
	QueryBy       isGetClientStatementRequest_QueryBy `protobuf_oneof:"query_by"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientStatementRequest) Reset() {
	*x = GetClientStatementRequest{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientStatementRequest) ProtoMessage() {}

func (x *GetClientStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientStatementRequest.ProtoReflect.Descriptor instead.
func (*GetClientStatementRequest) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetClientStatementRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *GetClientStatementRequest) GetQueryBy() isGetClientStatementRequest_QueryBy {
	if x != nil {
		return x.QueryBy
	}
	return nil
}

func (x *GetClientStatementRequest) GetStatementId() int64 {
	if x != nil {
		if x, ok := x.QueryBy.(*GetClientStatementRequest_StatementId); ok {
			return x.StatementId
		}
	}
	return 0
}

func (x *GetClientStatementRequest) GetStatementDate() string {
	if x != nil {
		if x, ok := x.QueryBy.(*GetClientStatementRequest_StatementDate); ok {
			return x.StatementDate
		}
	}
	return ""
}

type isGetClientStatementRequest_QueryBy interface {
	isGetClientStatementRequest_QueryBy()
}

type GetClientStatementRequest_StatementId struct {
	StatementId int64 `protobuf:"varint,2,opt,name=statement_id,proto3,oneof"`
}

type GetClientStatementRequest_StatementDate struct {
	StatementDate string `protobuf:"bytes,3,opt,name=statement_date,json=incurred_date,proto3,oneof"`
}

func (*GetClientStatementRequest_StatementId) isGetClientStatementRequest_QueryBy() {}

func (*GetClientStatementRequest_StatementDate) isGetClientStatementRequest_QueryBy() {}

type GetClientStatementResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statement     *StatementData         `protobuf:"bytes,1,opt,name=statement,proto3" json:"statement,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientStatementResponse) Reset() {
	*x = GetClientStatementResponse{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientStatementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientStatementResponse) ProtoMessage() {}

func (x *GetClientStatementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientStatementResponse.ProtoReflect.Descriptor instead.
func (*GetClientStatementResponse) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetClientStatementResponse) GetStatement() *StatementData {
	if x != nil {
		return x.Statement
	}
	return nil
}

type GetClientLatestStatementRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     int64                  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientLatestStatementRequest) Reset() {
	*x = GetClientLatestStatementRequest{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientLatestStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientLatestStatementRequest) ProtoMessage() {}

func (x *GetClientLatestStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientLatestStatementRequest.ProtoReflect.Descriptor instead.
func (*GetClientLatestStatementRequest) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetClientLatestStatementRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

type GetClientLatestStatementResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statement     *StatementData         `protobuf:"bytes,1,opt,name=statement,proto3" json:"statement,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClientLatestStatementResponse) Reset() {
	*x = GetClientLatestStatementResponse{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClientLatestStatementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClientLatestStatementResponse) ProtoMessage() {}

func (x *GetClientLatestStatementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClientLatestStatementResponse.ProtoReflect.Descriptor instead.
func (*GetClientLatestStatementResponse) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetClientLatestStatementResponse) GetStatement() *StatementData {
	if x != nil {
		return x.Statement
	}
	return nil
}

type ListClientStatementsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     int64                  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	FromTime      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_time,proto3" json:"from_time,omitempty"`
	ToTime        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to_time,proto3" json:"to_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientStatementsRequest) Reset() {
	*x = ListClientStatementsRequest{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientStatementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientStatementsRequest) ProtoMessage() {}

func (x *ListClientStatementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientStatementsRequest.ProtoReflect.Descriptor instead.
func (*ListClientStatementsRequest) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListClientStatementsRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *ListClientStatementsRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *ListClientStatementsRequest) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

type ListClientStatementsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statements    []*StatementItem       `protobuf:"bytes,1,rep,name=statements,proto3" json:"statements,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientStatementsResponse) Reset() {
	*x = ListClientStatementsResponse{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientStatementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientStatementsResponse) ProtoMessage() {}

func (x *ListClientStatementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientStatementsResponse.ProtoReflect.Descriptor instead.
func (*ListClientStatementsResponse) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListClientStatementsResponse) GetStatements() []*StatementItem {
	if x != nil {
		return x.Statements
	}
	return nil
}

type ListClientInstallmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     int64                  `protobuf:"varint,1,opt,name=account_id,proto3" json:"account_id,omitempty"`
	StatementId   int64                  `protobuf:"varint,2,opt,name=statement_id,proto3" json:"statement_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientInstallmentsRequest) Reset() {
	*x = ListClientInstallmentsRequest{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientInstallmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientInstallmentsRequest) ProtoMessage() {}

func (x *ListClientInstallmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientInstallmentsRequest.ProtoReflect.Descriptor instead.
func (*ListClientInstallmentsRequest) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListClientInstallmentsRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *ListClientInstallmentsRequest) GetStatementId() int64 {
	if x != nil {
		return x.StatementId
	}
	return 0
}

type ListClientInstallmentsResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Installments  []*StatementInstallment `protobuf:"bytes,1,rep,name=installments,proto3" json:"installments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListClientInstallmentsResponse) Reset() {
	*x = ListClientInstallmentsResponse{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClientInstallmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientInstallmentsResponse) ProtoMessage() {}

func (x *ListClientInstallmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientInstallmentsResponse.ProtoReflect.Descriptor instead.
func (*ListClientInstallmentsResponse) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListClientInstallmentsResponse) GetInstallments() []*StatementInstallment {
	if x != nil {
		return x.Installments
	}
	return nil
}

// StatementData represents a statement detail for a given account.
type StatementData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the statement.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Identifier for the user account.
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,proto3" json:"account_id,omitempty"`
	// The repayment grace end date.
	DueDate string `protobuf:"bytes,3,opt,name=due_date,proto3" json:"due_date,omitempty"`
	// The date the statement was incurred.
	IncurredDate string `protobuf:"bytes,4,opt,name=incurred_date,proto3" json:"incurred_date,omitempty"`
	// The paid status of the statement.
	PaidStatus PaidStatus `protobuf:"varint,5,opt,name=paid_status,proto3,enum=management_service.statement.v1.PaidStatus" json:"paid_status,omitempty"`
	// Snapshot of the outstanding amount at the statement date.
	OutstandingAmount int64 `protobuf:"varint,6,opt,name=outstanding_amount,proto3" json:"outstanding_amount,omitempty"`
	// Amount that has been repaid towards the outstanding amount.
	OutstandingRepaid int64 `protobuf:"varint,7,opt,name=outstanding_repaid,proto3" json:"outstanding_repaid,omitempty"`
	// Snapshot of the total fees of the installment at the statement date.
	InstallmentFeeAmount int64 `protobuf:"varint,8,opt,name=installment_fee_amount,proto3" json:"installment_fee_amount,omitempty"`
	// Total fees repaid.
	InstallmentFeeRepaid int64 `protobuf:"varint,9,opt,name=installment_fee_repaid,proto3" json:"installment_fee_repaid,omitempty"`
	// The total amount due, combining outstanding amount and installment fee amount.
	TotalDueAmount int64 `protobuf:"varint,10,opt,name=total_due_amount,proto3" json:"total_due_amount,omitempty"`
	// The total amount repaid, combining outstanding repaid and installment fee repaid.
	TotalDueRepaid int64 `protobuf:"varint,11,opt,name=total_due_repaid,proto3" json:"total_due_repaid,omitempty"`
	// The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
	TotalDueRemaining int64 `protobuf:"varint,12,opt,name=total_due_remaining,proto3" json:"total_due_remaining,omitempty"`
	// The penalty amount when the statement is overdue.
	PenaltyAmount int64 `protobuf:"varint,13,opt,name=penalty_amount,proto3" json:"penalty_amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatementData) Reset() {
	*x = StatementData{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatementData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementData) ProtoMessage() {}

func (x *StatementData) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementData.ProtoReflect.Descriptor instead.
func (*StatementData) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{8}
}

func (x *StatementData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StatementData) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StatementData) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *StatementData) GetIncurredDate() string {
	if x != nil {
		return x.IncurredDate
	}
	return ""
}

func (x *StatementData) GetPaidStatus() PaidStatus {
	if x != nil {
		return x.PaidStatus
	}
	return PaidStatus_PAID_STATUS_UNSPECIFIED
}

func (x *StatementData) GetOutstandingAmount() int64 {
	if x != nil {
		return x.OutstandingAmount
	}
	return 0
}

func (x *StatementData) GetOutstandingRepaid() int64 {
	if x != nil {
		return x.OutstandingRepaid
	}
	return 0
}

func (x *StatementData) GetInstallmentFeeAmount() int64 {
	if x != nil {
		return x.InstallmentFeeAmount
	}
	return 0
}

func (x *StatementData) GetInstallmentFeeRepaid() int64 {
	if x != nil {
		return x.InstallmentFeeRepaid
	}
	return 0
}

func (x *StatementData) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *StatementData) GetTotalDueRepaid() int64 {
	if x != nil {
		return x.TotalDueRepaid
	}
	return 0
}

func (x *StatementData) GetTotalDueRemaining() int64 {
	if x != nil {
		return x.TotalDueRemaining
	}
	return 0
}

func (x *StatementData) GetPenaltyAmount() int64 {
	if x != nil {
		return x.PenaltyAmount
	}
	return 0
}

// StatementItem represents item in statement list, it have less info than statement data.
type StatementItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the statement.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Identifier for the user account.
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,proto3" json:"account_id,omitempty"`
	// The repayment grace end date.
	DueDate string `protobuf:"bytes,3,opt,name=due_date,proto3" json:"due_date,omitempty"`
	// The date the statement was incurred.
	IncurredDate string `protobuf:"bytes,4,opt,name=incurred_date,proto3" json:"incurred_date,omitempty"`
	// The paid status of the statement.
	PaidStatus PaidStatus `protobuf:"varint,5,opt,name=paid_status,proto3,enum=management_service.statement.v1.PaidStatus" json:"paid_status,omitempty"`
	// The total amount due, combining outstanding amount and installment fee amount.
	TotalDueAmount int64 `protobuf:"varint,6,opt,name=total_due_amount,proto3" json:"total_due_amount,omitempty"`
	// The total amount repaid, combining outstanding repaid and installment fee repaid.
	TotalDueRepaid int64 `protobuf:"varint,7,opt,name=total_due_repaid,proto3" json:"total_due_repaid,omitempty"`
	// The total remaining amount due, combining the remaining outstanding balance and any remaining installment fees.
	TotalDueRemaining int64 `protobuf:"varint,8,opt,name=total_due_remaining,proto3" json:"total_due_remaining,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StatementItem) Reset() {
	*x = StatementItem{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatementItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementItem) ProtoMessage() {}

func (x *StatementItem) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementItem.ProtoReflect.Descriptor instead.
func (*StatementItem) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{9}
}

func (x *StatementItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StatementItem) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *StatementItem) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *StatementItem) GetIncurredDate() string {
	if x != nil {
		return x.IncurredDate
	}
	return ""
}

func (x *StatementItem) GetPaidStatus() PaidStatus {
	if x != nil {
		return x.PaidStatus
	}
	return PaidStatus_PAID_STATUS_UNSPECIFIED
}

func (x *StatementItem) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *StatementItem) GetTotalDueRepaid() int64 {
	if x != nil {
		return x.TotalDueRepaid
	}
	return 0
}

func (x *StatementItem) GetTotalDueRemaining() int64 {
	if x != nil {
		return x.TotalDueRemaining
	}
	return 0
}

type StatementInstallment struct {
	state              protoimpl.MessageState                   `protogen:"open.v1"`
	Id                 int64                                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StatementId        int64                                    `protobuf:"varint,2,opt,name=statement_id,proto3" json:"statement_id,omitempty"`
	ZpTransId          int64                                    `protobuf:"varint,3,opt,name=zp_trans_id,proto3" json:"zp_trans_id,omitempty"`
	InstallmentId      int64                                    `protobuf:"varint,4,opt,name=installment_id,proto3" json:"installment_id,omitempty"`
	TransactionDesc    string                                   `protobuf:"bytes,5,opt,name=transaction_desc,proto3" json:"transaction_desc,omitempty"`
	OutstandingAmount  int64                                    `protobuf:"varint,6,opt,name=outstanding_amount,proto3" json:"outstanding_amount,omitempty"`
	OutstandingDetails *StatementInstallment_OutstandingDetails `protobuf:"bytes,7,opt,name=outstanding_details,proto3" json:"outstanding_details,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StatementInstallment) Reset() {
	*x = StatementInstallment{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatementInstallment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementInstallment) ProtoMessage() {}

func (x *StatementInstallment) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementInstallment.ProtoReflect.Descriptor instead.
func (*StatementInstallment) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{10}
}

func (x *StatementInstallment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StatementInstallment) GetStatementId() int64 {
	if x != nil {
		return x.StatementId
	}
	return 0
}

func (x *StatementInstallment) GetZpTransId() int64 {
	if x != nil {
		return x.ZpTransId
	}
	return 0
}

func (x *StatementInstallment) GetInstallmentId() int64 {
	if x != nil {
		return x.InstallmentId
	}
	return 0
}

func (x *StatementInstallment) GetTransactionDesc() string {
	if x != nil {
		return x.TransactionDesc
	}
	return ""
}

func (x *StatementInstallment) GetOutstandingAmount() int64 {
	if x != nil {
		return x.OutstandingAmount
	}
	return 0
}

func (x *StatementInstallment) GetOutstandingDetails() *StatementInstallment_OutstandingDetails {
	if x != nil {
		return x.OutstandingDetails
	}
	return nil
}

type StatementInstallment_OutstandingDetails struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TotalDueAmount     int64                  `protobuf:"varint,1,opt,name=total_due_amount,proto3" json:"total_due_amount,omitempty"`
	TotalOverdueAmount int64                  `protobuf:"varint,2,opt,name=total_overdue_amount,proto3" json:"total_overdue_amount,omitempty"`
	TotalPenaltyAmount int64                  `protobuf:"varint,3,opt,name=total_penalty_amount,proto3" json:"total_penalty_amount,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StatementInstallment_OutstandingDetails) Reset() {
	*x = StatementInstallment_OutstandingDetails{}
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatementInstallment_OutstandingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatementInstallment_OutstandingDetails) ProtoMessage() {}

func (x *StatementInstallment_OutstandingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_management_service_statement_v1_statement_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatementInstallment_OutstandingDetails.ProtoReflect.Descriptor instead.
func (*StatementInstallment_OutstandingDetails) Descriptor() ([]byte, []int) {
	return file_management_service_statement_v1_statement_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *StatementInstallment_OutstandingDetails) GetTotalDueAmount() int64 {
	if x != nil {
		return x.TotalDueAmount
	}
	return 0
}

func (x *StatementInstallment_OutstandingDetails) GetTotalOverdueAmount() int64 {
	if x != nil {
		return x.TotalOverdueAmount
	}
	return 0
}

func (x *StatementInstallment_OutstandingDetails) GetTotalPenaltyAmount() int64 {
	if x != nil {
		return x.TotalPenaltyAmount
	}
	return 0
}

var File_management_service_statement_v1_statement_service_proto protoreflect.FileDescriptor

const file_management_service_statement_v1_statement_service_proto_rawDesc = "" +
	"\n" +
	"7management_service/statement/v1/statement_service.proto\x12\x1fmanagement_service.statement.v1\x1a\x17validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb6\x01\n" +
	"\x19GetClientStatementRequest\x12'\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"account_id\x12-\n" +
	"\fstatement_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00H\x00R\fstatement_id\x120\n" +
	"\x0estatement_date\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01H\x00R\rincurred_dateB\x0f\n" +
	"\bquery_by\x12\x03\xf8B\x01\"j\n" +
	"\x1aGetClientStatementResponse\x12L\n" +
	"\tstatement\x18\x01 \x01(\v2..management_service.statement.v1.StatementDataR\tstatement\"J\n" +
	"\x1fGetClientLatestStatementRequest\x12'\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"account_id\"p\n" +
	" GetClientLatestStatementResponse\x12L\n" +
	"\tstatement\x18\x01 \x01(\v2..management_service.statement.v1.StatementDataR\tstatement\"\xca\x01\n" +
	"\x1bListClientStatementsRequest\x12'\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"account_id\x12B\n" +
	"\tfrom_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampB\b\xfaB\x05\xb2\x01\x02\b\x01R\tfrom_time\x12>\n" +
	"\ato_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\b\xfaB\x05\xb2\x01\x02\b\x01R\ato_time\"n\n" +
	"\x1cListClientStatementsResponse\x12N\n" +
	"\n" +
	"statements\x18\x01 \x03(\v2..management_service.statement.v1.StatementItemR\n" +
	"statements\"u\n" +
	"\x1dListClientInstallmentsRequest\x12'\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"account_id\x12+\n" +
	"\fstatement_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\fstatement_id\"{\n" +
	"\x1eListClientInstallmentsResponse\x12Y\n" +
	"\finstallments\x18\x01 \x03(\v25.management_service.statement.v1.StatementInstallmentR\finstallments\"\xd2\x04\n" +
	"\rStatementData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\n" +
	"account_id\x12\x1a\n" +
	"\bdue_date\x18\x03 \x01(\tR\bdue_date\x12$\n" +
	"\rincurred_date\x18\x04 \x01(\tR\rincurred_date\x12M\n" +
	"\vpaid_status\x18\x05 \x01(\x0e2+.management_service.statement.v1.PaidStatusR\vpaid_status\x12.\n" +
	"\x12outstanding_amount\x18\x06 \x01(\x03R\x12outstanding_amount\x12.\n" +
	"\x12outstanding_repaid\x18\a \x01(\x03R\x12outstanding_repaid\x126\n" +
	"\x16installment_fee_amount\x18\b \x01(\x03R\x16installment_fee_amount\x126\n" +
	"\x16installment_fee_repaid\x18\t \x01(\x03R\x16installment_fee_repaid\x12*\n" +
	"\x10total_due_amount\x18\n" +
	" \x01(\x03R\x10total_due_amount\x12*\n" +
	"\x10total_due_repaid\x18\v \x01(\x03R\x10total_due_repaid\x120\n" +
	"\x13total_due_remaining\x18\f \x01(\x03R\x13total_due_remaining\x12&\n" +
	"\x0epenalty_amount\x18\r \x01(\x03R\x0epenalty_amount\"\xda\x02\n" +
	"\rStatementItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\n" +
	"account_id\x12\x1a\n" +
	"\bdue_date\x18\x03 \x01(\tR\bdue_date\x12$\n" +
	"\rincurred_date\x18\x04 \x01(\tR\rincurred_date\x12M\n" +
	"\vpaid_status\x18\x05 \x01(\x0e2+.management_service.statement.v1.PaidStatusR\vpaid_status\x12*\n" +
	"\x10total_due_amount\x18\x06 \x01(\x03R\x10total_due_amount\x12*\n" +
	"\x10total_due_repaid\x18\a \x01(\x03R\x10total_due_repaid\x120\n" +
	"\x13total_due_remaining\x18\b \x01(\x03R\x13total_due_remaining\"\x97\x04\n" +
	"\x14StatementInstallment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n" +
	"\fstatement_id\x18\x02 \x01(\x03R\fstatement_id\x12 \n" +
	"\vzp_trans_id\x18\x03 \x01(\x03R\vzp_trans_id\x12&\n" +
	"\x0einstallment_id\x18\x04 \x01(\x03R\x0einstallment_id\x12*\n" +
	"\x10transaction_desc\x18\x05 \x01(\tR\x10transaction_desc\x12.\n" +
	"\x12outstanding_amount\x18\x06 \x01(\x03R\x12outstanding_amount\x12z\n" +
	"\x13outstanding_details\x18\a \x01(\v2H.management_service.statement.v1.StatementInstallment.OutstandingDetailsR\x13outstanding_details\x1a\xa8\x01\n" +
	"\x12OutstandingDetails\x12*\n" +
	"\x10total_due_amount\x18\x01 \x01(\x03R\x10total_due_amount\x122\n" +
	"\x14total_overdue_amount\x18\x02 \x01(\x03R\x14total_overdue_amount\x122\n" +
	"\x14total_penalty_amount\x18\x03 \x01(\x03R\x14total_penalty_amount*w\n" +
	"\n" +
	"PaidStatus\x12\x1b\n" +
	"\x17PAID_STATUS_UNSPECIFIED\x10\x00\x12\x14\n" +
	"\x10PAID_STATUS_PAID\x10\x01\x12\x16\n" +
	"\x12PAID_STATUS_UNPAID\x10\x02\x12\x1e\n" +
	"\x1aPAID_STATUS_PARTIALLY_PAID\x10\x032\xe9\x05\n" +
	"\tStatement\x12\xa9\x01\n" +
	"\x12GetClientStatement\x12:.management_service.statement.v1.GetClientStatementRequest\x1a;.management_service.statement.v1.GetClientStatementResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/statement/v1/info\x12\xbd\x01\n" +
	"\x18GetClientLatestStatement\x12@.management_service.statement.v1.GetClientLatestStatementRequest\x1aA.management_service.statement.v1.GetClientLatestStatementResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/statement/v1/latest\x12\xaf\x01\n" +
	"\x14ListClientStatements\x12<.management_service.statement.v1.ListClientStatementsRequest\x1a=.management_service.statement.v1.ListClientStatementsResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/statement/v1/list\x12\xbd\x01\n" +
	"\x16ListClientInstallments\x12>.management_service.statement.v1.ListClientInstallmentsRequest\x1a?.management_service.statement.v1.ListClientInstallmentsResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/statement/v1/installmentsB*Z(installment/api/management-service/v1;v1b\x06proto3"

var (
	file_management_service_statement_v1_statement_service_proto_rawDescOnce sync.Once
	file_management_service_statement_v1_statement_service_proto_rawDescData []byte
)

func file_management_service_statement_v1_statement_service_proto_rawDescGZIP() []byte {
	file_management_service_statement_v1_statement_service_proto_rawDescOnce.Do(func() {
		file_management_service_statement_v1_statement_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_management_service_statement_v1_statement_service_proto_rawDesc), len(file_management_service_statement_v1_statement_service_proto_rawDesc)))
	})
	return file_management_service_statement_v1_statement_service_proto_rawDescData
}

var file_management_service_statement_v1_statement_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_management_service_statement_v1_statement_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_management_service_statement_v1_statement_service_proto_goTypes = []any{
	(PaidStatus)(0),                                 // 0: management_service.statement.v1.PaidStatus
	(*GetClientStatementRequest)(nil),               // 1: management_service.statement.v1.GetClientStatementRequest
	(*GetClientStatementResponse)(nil),              // 2: management_service.statement.v1.GetClientStatementResponse
	(*GetClientLatestStatementRequest)(nil),         // 3: management_service.statement.v1.GetClientLatestStatementRequest
	(*GetClientLatestStatementResponse)(nil),        // 4: management_service.statement.v1.GetClientLatestStatementResponse
	(*ListClientStatementsRequest)(nil),             // 5: management_service.statement.v1.ListClientStatementsRequest
	(*ListClientStatementsResponse)(nil),            // 6: management_service.statement.v1.ListClientStatementsResponse
	(*ListClientInstallmentsRequest)(nil),           // 7: management_service.statement.v1.ListClientInstallmentsRequest
	(*ListClientInstallmentsResponse)(nil),          // 8: management_service.statement.v1.ListClientInstallmentsResponse
	(*StatementData)(nil),                           // 9: management_service.statement.v1.StatementData
	(*StatementItem)(nil),                           // 10: management_service.statement.v1.StatementItem
	(*StatementInstallment)(nil),                    // 11: management_service.statement.v1.StatementInstallment
	(*StatementInstallment_OutstandingDetails)(nil), // 12: management_service.statement.v1.StatementInstallment.OutstandingDetails
	(*timestamppb.Timestamp)(nil),                   // 13: google.protobuf.Timestamp
}
var file_management_service_statement_v1_statement_service_proto_depIdxs = []int32{
	9,  // 0: management_service.statement.v1.GetClientStatementResponse.statement:type_name -> management_service.statement.v1.StatementData
	9,  // 1: management_service.statement.v1.GetClientLatestStatementResponse.statement:type_name -> management_service.statement.v1.StatementData
	13, // 2: management_service.statement.v1.ListClientStatementsRequest.from_time:type_name -> google.protobuf.Timestamp
	13, // 3: management_service.statement.v1.ListClientStatementsRequest.to_time:type_name -> google.protobuf.Timestamp
	10, // 4: management_service.statement.v1.ListClientStatementsResponse.statements:type_name -> management_service.statement.v1.StatementItem
	11, // 5: management_service.statement.v1.ListClientInstallmentsResponse.installments:type_name -> management_service.statement.v1.StatementInstallment
	0,  // 6: management_service.statement.v1.StatementData.paid_status:type_name -> management_service.statement.v1.PaidStatus
	0,  // 7: management_service.statement.v1.StatementItem.paid_status:type_name -> management_service.statement.v1.PaidStatus
	12, // 8: management_service.statement.v1.StatementInstallment.outstanding_details:type_name -> management_service.statement.v1.StatementInstallment.OutstandingDetails
	1,  // 9: management_service.statement.v1.Statement.GetClientStatement:input_type -> management_service.statement.v1.GetClientStatementRequest
	3,  // 10: management_service.statement.v1.Statement.GetClientLatestStatement:input_type -> management_service.statement.v1.GetClientLatestStatementRequest
	5,  // 11: management_service.statement.v1.Statement.ListClientStatements:input_type -> management_service.statement.v1.ListClientStatementsRequest
	7,  // 12: management_service.statement.v1.Statement.ListClientInstallments:input_type -> management_service.statement.v1.ListClientInstallmentsRequest
	2,  // 13: management_service.statement.v1.Statement.GetClientStatement:output_type -> management_service.statement.v1.GetClientStatementResponse
	4,  // 14: management_service.statement.v1.Statement.GetClientLatestStatement:output_type -> management_service.statement.v1.GetClientLatestStatementResponse
	6,  // 15: management_service.statement.v1.Statement.ListClientStatements:output_type -> management_service.statement.v1.ListClientStatementsResponse
	8,  // 16: management_service.statement.v1.Statement.ListClientInstallments:output_type -> management_service.statement.v1.ListClientInstallmentsResponse
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_management_service_statement_v1_statement_service_proto_init() }
func file_management_service_statement_v1_statement_service_proto_init() {
	if File_management_service_statement_v1_statement_service_proto != nil {
		return
	}
	file_management_service_statement_v1_statement_service_proto_msgTypes[0].OneofWrappers = []any{
		(*GetClientStatementRequest_StatementId)(nil),
		(*GetClientStatementRequest_StatementDate)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_management_service_statement_v1_statement_service_proto_rawDesc), len(file_management_service_statement_v1_statement_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_management_service_statement_v1_statement_service_proto_goTypes,
		DependencyIndexes: file_management_service_statement_v1_statement_service_proto_depIdxs,
		EnumInfos:         file_management_service_statement_v1_statement_service_proto_enumTypes,
		MessageInfos:      file_management_service_statement_v1_statement_service_proto_msgTypes,
	}.Build()
	File_management_service_statement_v1_statement_service_proto = out.File
	file_management_service_statement_v1_statement_service_proto_goTypes = nil
	file_management_service_statement_v1_statement_service_proto_depIdxs = nil
}
