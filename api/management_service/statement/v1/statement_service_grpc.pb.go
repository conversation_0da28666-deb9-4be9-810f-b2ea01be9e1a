// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: management_service/statement/v1/statement_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Statement_GetClientStatement_FullMethodName       = "/management_service.statement.v1.Statement/GetClientStatement"
	Statement_GetClientLatestStatement_FullMethodName = "/management_service.statement.v1.Statement/GetClientLatestStatement"
	Statement_ListClientStatements_FullMethodName     = "/management_service.statement.v1.Statement/ListClientStatements"
	Statement_ListClientInstallments_FullMethodName   = "/management_service.statement.v1.Statement/ListClientInstallments"
)

// StatementClient is the client API for Statement service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatementClient interface {
	GetClientStatement(ctx context.Context, in *GetClientStatementRequest, opts ...grpc.CallOption) (*GetClientStatementResponse, error)
	GetClientLatestStatement(ctx context.Context, in *GetClientLatestStatementRequest, opts ...grpc.CallOption) (*GetClientLatestStatementResponse, error)
	ListClientStatements(ctx context.Context, in *ListClientStatementsRequest, opts ...grpc.CallOption) (*ListClientStatementsResponse, error)
	// ListClientInstallments returns a list of installments for a given statement.
	ListClientInstallments(ctx context.Context, in *ListClientInstallmentsRequest, opts ...grpc.CallOption) (*ListClientInstallmentsResponse, error)
}

type statementClient struct {
	cc grpc.ClientConnInterface
}

func NewStatementClient(cc grpc.ClientConnInterface) StatementClient {
	return &statementClient{cc}
}

func (c *statementClient) GetClientStatement(ctx context.Context, in *GetClientStatementRequest, opts ...grpc.CallOption) (*GetClientStatementResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientStatementResponse)
	err := c.cc.Invoke(ctx, Statement_GetClientStatement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statementClient) GetClientLatestStatement(ctx context.Context, in *GetClientLatestStatementRequest, opts ...grpc.CallOption) (*GetClientLatestStatementResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClientLatestStatementResponse)
	err := c.cc.Invoke(ctx, Statement_GetClientLatestStatement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statementClient) ListClientStatements(ctx context.Context, in *ListClientStatementsRequest, opts ...grpc.CallOption) (*ListClientStatementsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClientStatementsResponse)
	err := c.cc.Invoke(ctx, Statement_ListClientStatements_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statementClient) ListClientInstallments(ctx context.Context, in *ListClientInstallmentsRequest, opts ...grpc.CallOption) (*ListClientInstallmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClientInstallmentsResponse)
	err := c.cc.Invoke(ctx, Statement_ListClientInstallments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatementServer is the server API for Statement service.
// All implementations must embed UnimplementedStatementServer
// for forward compatibility.
type StatementServer interface {
	GetClientStatement(context.Context, *GetClientStatementRequest) (*GetClientStatementResponse, error)
	GetClientLatestStatement(context.Context, *GetClientLatestStatementRequest) (*GetClientLatestStatementResponse, error)
	ListClientStatements(context.Context, *ListClientStatementsRequest) (*ListClientStatementsResponse, error)
	// ListClientInstallments returns a list of installments for a given statement.
	ListClientInstallments(context.Context, *ListClientInstallmentsRequest) (*ListClientInstallmentsResponse, error)
	mustEmbedUnimplementedStatementServer()
}

// UnimplementedStatementServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStatementServer struct{}

func (UnimplementedStatementServer) GetClientStatement(context.Context, *GetClientStatementRequest) (*GetClientStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientStatement not implemented")
}
func (UnimplementedStatementServer) GetClientLatestStatement(context.Context, *GetClientLatestStatementRequest) (*GetClientLatestStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientLatestStatement not implemented")
}
func (UnimplementedStatementServer) ListClientStatements(context.Context, *ListClientStatementsRequest) (*ListClientStatementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClientStatements not implemented")
}
func (UnimplementedStatementServer) ListClientInstallments(context.Context, *ListClientInstallmentsRequest) (*ListClientInstallmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClientInstallments not implemented")
}
func (UnimplementedStatementServer) mustEmbedUnimplementedStatementServer() {}
func (UnimplementedStatementServer) testEmbeddedByValue()                   {}

// UnsafeStatementServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatementServer will
// result in compilation errors.
type UnsafeStatementServer interface {
	mustEmbedUnimplementedStatementServer()
}

func RegisterStatementServer(s grpc.ServiceRegistrar, srv StatementServer) {
	// If the following call pancis, it indicates UnimplementedStatementServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Statement_ServiceDesc, srv)
}

func _Statement_GetClientStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatementServer).GetClientStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Statement_GetClientStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatementServer).GetClientStatement(ctx, req.(*GetClientStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Statement_GetClientLatestStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientLatestStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatementServer).GetClientLatestStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Statement_GetClientLatestStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatementServer).GetClientLatestStatement(ctx, req.(*GetClientLatestStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Statement_ListClientStatements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClientStatementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatementServer).ListClientStatements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Statement_ListClientStatements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatementServer).ListClientStatements(ctx, req.(*ListClientStatementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Statement_ListClientInstallments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClientInstallmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatementServer).ListClientInstallments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Statement_ListClientInstallments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatementServer).ListClientInstallments(ctx, req.(*ListClientInstallmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Statement_ServiceDesc is the grpc.ServiceDesc for Statement service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Statement_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "management_service.statement.v1.Statement",
	HandlerType: (*StatementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetClientStatement",
			Handler:    _Statement_GetClientStatement_Handler,
		},
		{
			MethodName: "GetClientLatestStatement",
			Handler:    _Statement_GetClientLatestStatement_Handler,
		},
		{
			MethodName: "ListClientStatements",
			Handler:    _Statement_ListClientStatements_Handler,
		},
		{
			MethodName: "ListClientInstallments",
			Handler:    _Statement_ListClientInstallments_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "management_service/statement/v1/statement_service.proto",
}
