// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: management_service/statement/v1/statement_service.proto

package v1

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockStatementClient is a mock of StatementClient interface.
type MockStatementClient struct {
	ctrl     *gomock.Controller
	recorder *MockStatementClientMockRecorder
}

// MockStatementClientMockRecorder is the mock recorder for MockStatementClient.
type MockStatementClientMockRecorder struct {
	mock *MockStatementClient
}

// NewMockStatementClient creates a new mock instance.
func NewMockStatementClient(ctrl *gomock.Controller) *MockStatementClient {
	mock := &MockStatementClient{ctrl: ctrl}
	mock.recorder = &MockStatementClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatementClient) EXPECT() *MockStatementClientMockRecorder {
	return m.recorder
}

// GetClientLatestStatement mocks base method.
func (m *MockStatementClient) GetClientLatestStatement(ctx context.Context, in *GetClientLatestStatementRequest, opts ...grpc.CallOption) (*GetClientLatestStatementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientLatestStatement", varargs...)
	ret0, _ := ret[0].(*GetClientLatestStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientLatestStatement indicates an expected call of GetClientLatestStatement.
func (mr *MockStatementClientMockRecorder) GetClientLatestStatement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientLatestStatement", reflect.TypeOf((*MockStatementClient)(nil).GetClientLatestStatement), varargs...)
}

// GetClientStatement mocks base method.
func (m *MockStatementClient) GetClientStatement(ctx context.Context, in *GetClientStatementRequest, opts ...grpc.CallOption) (*GetClientStatementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientStatement", varargs...)
	ret0, _ := ret[0].(*GetClientStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientStatement indicates an expected call of GetClientStatement.
func (mr *MockStatementClientMockRecorder) GetClientStatement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientStatement", reflect.TypeOf((*MockStatementClient)(nil).GetClientStatement), varargs...)
}

// ListClientInstallments mocks base method.
func (m *MockStatementClient) ListClientInstallments(ctx context.Context, in *ListClientInstallmentsRequest, opts ...grpc.CallOption) (*ListClientInstallmentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListClientInstallments", varargs...)
	ret0, _ := ret[0].(*ListClientInstallmentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientInstallments indicates an expected call of ListClientInstallments.
func (mr *MockStatementClientMockRecorder) ListClientInstallments(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientInstallments", reflect.TypeOf((*MockStatementClient)(nil).ListClientInstallments), varargs...)
}

// ListClientStatements mocks base method.
func (m *MockStatementClient) ListClientStatements(ctx context.Context, in *ListClientStatementsRequest, opts ...grpc.CallOption) (*ListClientStatementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListClientStatements", varargs...)
	ret0, _ := ret[0].(*ListClientStatementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientStatements indicates an expected call of ListClientStatements.
func (mr *MockStatementClientMockRecorder) ListClientStatements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientStatements", reflect.TypeOf((*MockStatementClient)(nil).ListClientStatements), varargs...)
}

// MockStatementServer is a mock of StatementServer interface.
type MockStatementServer struct {
	ctrl     *gomock.Controller
	recorder *MockStatementServerMockRecorder
}

// MockStatementServerMockRecorder is the mock recorder for MockStatementServer.
type MockStatementServerMockRecorder struct {
	mock *MockStatementServer
}

// NewMockStatementServer creates a new mock instance.
func NewMockStatementServer(ctrl *gomock.Controller) *MockStatementServer {
	mock := &MockStatementServer{ctrl: ctrl}
	mock.recorder = &MockStatementServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatementServer) EXPECT() *MockStatementServerMockRecorder {
	return m.recorder
}

// GetClientLatestStatement mocks base method.
func (m *MockStatementServer) GetClientLatestStatement(ctx context.Context, in *GetClientLatestStatementRequest) (*GetClientLatestStatementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientLatestStatement", ctx, in)
	ret0, _ := ret[0].(*GetClientLatestStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientLatestStatement indicates an expected call of GetClientLatestStatement.
func (mr *MockStatementServerMockRecorder) GetClientLatestStatement(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientLatestStatement", reflect.TypeOf((*MockStatementServer)(nil).GetClientLatestStatement), ctx, in)
}

// GetClientStatement mocks base method.
func (m *MockStatementServer) GetClientStatement(ctx context.Context, in *GetClientStatementRequest) (*GetClientStatementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientStatement", ctx, in)
	ret0, _ := ret[0].(*GetClientStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientStatement indicates an expected call of GetClientStatement.
func (mr *MockStatementServerMockRecorder) GetClientStatement(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientStatement", reflect.TypeOf((*MockStatementServer)(nil).GetClientStatement), ctx, in)
}

// ListClientInstallments mocks base method.
func (m *MockStatementServer) ListClientInstallments(ctx context.Context, in *ListClientInstallmentsRequest) (*ListClientInstallmentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClientInstallments", ctx, in)
	ret0, _ := ret[0].(*ListClientInstallmentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientInstallments indicates an expected call of ListClientInstallments.
func (mr *MockStatementServerMockRecorder) ListClientInstallments(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientInstallments", reflect.TypeOf((*MockStatementServer)(nil).ListClientInstallments), ctx, in)
}

// ListClientStatements mocks base method.
func (m *MockStatementServer) ListClientStatements(ctx context.Context, in *ListClientStatementsRequest) (*ListClientStatementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClientStatements", ctx, in)
	ret0, _ := ret[0].(*ListClientStatementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClientStatements indicates an expected call of ListClientStatements.
func (mr *MockStatementServerMockRecorder) ListClientStatements(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClientStatements", reflect.TypeOf((*MockStatementServer)(nil).ListClientStatements), ctx, in)
}
