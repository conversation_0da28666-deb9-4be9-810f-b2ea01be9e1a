// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/ab_platform/ab_platform.proto

package abplatform

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetExperimentRequest
type GetExperimentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExperimentId int64 `protobuf:"varint,1,opt,name=experiment_id,json=experimentId,proto3" json:"experiment_id,omitempty"`
}

func (x *GetExperimentRequest) Reset() {
	*x = GetExperimentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExperimentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExperimentRequest) ProtoMessage() {}

func (x *GetExperimentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExperimentRequest.ProtoReflect.Descriptor instead.
func (*GetExperimentRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{0}
}

func (x *GetExperimentRequest) GetExperimentId() int64 {
	if x != nil {
		return x.ExperimentId
	}
	return 0
}

// GetExperimentWithUserIDRequest
type GetExperimentWithUserIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExperimentId int64  `protobuf:"varint,1,opt,name=experiment_id,json=experimentId,proto3" json:"experiment_id,omitempty"`
	UserId       string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetExperimentWithUserIDRequest) Reset() {
	*x = GetExperimentWithUserIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExperimentWithUserIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExperimentWithUserIDRequest) ProtoMessage() {}

func (x *GetExperimentWithUserIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExperimentWithUserIDRequest.ProtoReflect.Descriptor instead.
func (*GetExperimentWithUserIDRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{1}
}

func (x *GetExperimentWithUserIDRequest) GetExperimentId() int64 {
	if x != nil {
		return x.ExperimentId
	}
	return 0
}

func (x *GetExperimentWithUserIDRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// GetExperimentRequest
type GetExperimentByKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExperimentKey string `protobuf:"bytes,1,opt,name=experiment_key,json=experimentKey,proto3" json:"experiment_key,omitempty"`
}

func (x *GetExperimentByKeyRequest) Reset() {
	*x = GetExperimentByKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExperimentByKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExperimentByKeyRequest) ProtoMessage() {}

func (x *GetExperimentByKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExperimentByKeyRequest.ProtoReflect.Descriptor instead.
func (*GetExperimentByKeyRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{2}
}

func (x *GetExperimentByKeyRequest) GetExperimentKey() string {
	if x != nil {
		return x.ExperimentKey
	}
	return ""
}

// GetExperimentResponse
type GetExperimentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group    string    `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	Config   string    `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	Metadata *Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *GetExperimentResponse) Reset() {
	*x = GetExperimentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExperimentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExperimentResponse) ProtoMessage() {}

func (x *GetExperimentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExperimentResponse.ProtoReflect.Descriptor instead.
func (*GetExperimentResponse) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{3}
}

func (x *GetExperimentResponse) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *GetExperimentResponse) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

func (x *GetExperimentResponse) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Metadata message
type Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AbInfo *ABInfo `protobuf:"bytes,1,opt,name=ab_info,json=abInfo,proto3" json:"ab_info,omitempty"`
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{4}
}

func (x *Metadata) GetAbInfo() *ABInfo {
	if x != nil {
		return x.AbInfo
	}
	return nil
}

// Behavior tracking message
type ABInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExperimentId  string `protobuf:"bytes,1,opt,name=experiment_id,json=experimentId,proto3" json:"experiment_id,omitempty"`
	GroupName     string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Access        string `protobuf:"bytes,3,opt,name=access,proto3" json:"access,omitempty"`
	ExperimentKey string `protobuf:"bytes,4,opt,name=experiment_key,json=experimentKey,proto3" json:"experiment_key,omitempty"`
}

func (x *ABInfo) Reset() {
	*x = ABInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ABInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABInfo) ProtoMessage() {}

func (x *ABInfo) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABInfo.ProtoReflect.Descriptor instead.
func (*ABInfo) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{5}
}

func (x *ABInfo) GetExperimentId() string {
	if x != nil {
		return x.ExperimentId
	}
	return ""
}

func (x *ABInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ABInfo) GetAccess() string {
	if x != nil {
		return x.Access
	}
	return ""
}

func (x *ABInfo) GetExperimentKey() string {
	if x != nil {
		return x.ExperimentKey
	}
	return ""
}

// StatusRequest
type GetFlagStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlagId int64 `protobuf:"varint,1,opt,name=flag_id,json=flagId,proto3" json:"flag_id,omitempty"`
}

func (x *GetFlagStatusRequest) Reset() {
	*x = GetFlagStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFlagStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFlagStatusRequest) ProtoMessage() {}

func (x *GetFlagStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFlagStatusRequest.ProtoReflect.Descriptor instead.
func (*GetFlagStatusRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{6}
}

func (x *GetFlagStatusRequest) GetFlagId() int64 {
	if x != nil {
		return x.FlagId
	}
	return 0
}

// GetFlagStatusResponse
type GetFlagStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetFlagStatusResponse) Reset() {
	*x = GetFlagStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFlagStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFlagStatusResponse) ProtoMessage() {}

func (x *GetFlagStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFlagStatusResponse.ProtoReflect.Descriptor instead.
func (*GetFlagStatusResponse) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{7}
}

func (x *GetFlagStatusResponse) GetStatus() *wrapperspb.BoolValue {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateWhiteListByExperimentKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExperimentKey  string   `protobuf:"bytes,1,opt,name=experiment_key,json=experimentKey,proto3" json:"experiment_key,omitempty"`
	ListZalopayIds []string `protobuf:"bytes,2,rep,name=list_zalopay_ids,json=listZalopayIds,proto3" json:"list_zalopay_ids,omitempty"`
}

func (x *UpdateWhiteListByExperimentKeyRequest) Reset() {
	*x = UpdateWhiteListByExperimentKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWhiteListByExperimentKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWhiteListByExperimentKeyRequest) ProtoMessage() {}

func (x *UpdateWhiteListByExperimentKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWhiteListByExperimentKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdateWhiteListByExperimentKeyRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateWhiteListByExperimentKeyRequest) GetExperimentKey() string {
	if x != nil {
		return x.ExperimentKey
	}
	return ""
}

func (x *UpdateWhiteListByExperimentKeyRequest) GetListZalopayIds() []string {
	if x != nil {
		return x.ListZalopayIds
	}
	return nil
}

type UpdateWhiteListByExperimentKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddedList []string `protobuf:"bytes,1,rep,name=added_list,json=addedList,proto3" json:"added_list,omitempty"`
}

func (x *UpdateWhiteListByExperimentKeyResponse) Reset() {
	*x = UpdateWhiteListByExperimentKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWhiteListByExperimentKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWhiteListByExperimentKeyResponse) ProtoMessage() {}

func (x *UpdateWhiteListByExperimentKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ab_platform_ab_platform_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWhiteListByExperimentKeyResponse.ProtoReflect.Descriptor instead.
func (*UpdateWhiteListByExperimentKeyResponse) Descriptor() ([]byte, []int) {
	return file_external_services_ab_platform_ab_platform_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateWhiteListByExperimentKeyResponse) GetAddedList() []string {
	if x != nil {
		return x.AddedList
	}
	return nil
}

var File_external_services_ab_platform_ab_platform_proto protoreflect.FileDescriptor

var file_external_services_ab_platform_ab_platform_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x61, 0x62, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x62, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x3b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x5e, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x42, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x4b, 0x65, 0x79, 0x22, 0x7c, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x35, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x3c, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a,
	0x07, 0x61, 0x62, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x2e, 0x41, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x61, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x8b, 0x01, 0x0a, 0x06, 0x41, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x22, 0x2f, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x6c, 0x61, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x6c, 0x61, 0x67, 0x49, 0x64, 0x22, 0x4b,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x78, 0x0a, 0x25, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x69, 0x73, 0x74, 0x5a, 0x61, 0x6c, 0x6f, 0x70,
	0x61, 0x79, 0x49, 0x64, 0x73, 0x22, 0x47, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x32, 0xcd,
	0x04, 0x0a, 0x0f, 0x41, 0x42, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x60, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x25, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x62, 0x74,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72,
	0x69, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x2a, 0x2e, 0x61, 0x62, 0x74,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x4b, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65,
	0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x60, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x25, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c,
	0x61, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x74, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d,
	0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x2f, 0x2e,
	0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x36, 0x2e, 0x61, 0x62,
	0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x15,
	0x5a, 0x13, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x62, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_ab_platform_ab_platform_proto_rawDescOnce sync.Once
	file_external_services_ab_platform_ab_platform_proto_rawDescData = file_external_services_ab_platform_ab_platform_proto_rawDesc
)

func file_external_services_ab_platform_ab_platform_proto_rawDescGZIP() []byte {
	file_external_services_ab_platform_ab_platform_proto_rawDescOnce.Do(func() {
		file_external_services_ab_platform_ab_platform_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_ab_platform_ab_platform_proto_rawDescData)
	})
	return file_external_services_ab_platform_ab_platform_proto_rawDescData
}

var file_external_services_ab_platform_ab_platform_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_external_services_ab_platform_ab_platform_proto_goTypes = []any{
	(*GetExperimentRequest)(nil),                   // 0: abtestingpublic.GetExperimentRequest
	(*GetExperimentWithUserIDRequest)(nil),         // 1: abtestingpublic.GetExperimentWithUserIDRequest
	(*GetExperimentByKeyRequest)(nil),              // 2: abtestingpublic.GetExperimentByKeyRequest
	(*GetExperimentResponse)(nil),                  // 3: abtestingpublic.GetExperimentResponse
	(*Metadata)(nil),                               // 4: abtestingpublic.Metadata
	(*ABInfo)(nil),                                 // 5: abtestingpublic.ABInfo
	(*GetFlagStatusRequest)(nil),                   // 6: abtestingpublic.GetFlagStatusRequest
	(*GetFlagStatusResponse)(nil),                  // 7: abtestingpublic.GetFlagStatusResponse
	(*UpdateWhiteListByExperimentKeyRequest)(nil),  // 8: abtestingpublic.UpdateWhiteListByExperimentKeyRequest
	(*UpdateWhiteListByExperimentKeyResponse)(nil), // 9: abtestingpublic.UpdateWhiteListByExperimentKeyResponse
	(*wrapperspb.BoolValue)(nil),                   // 10: google.protobuf.BoolValue
}
var file_external_services_ab_platform_ab_platform_proto_depIdxs = []int32{
	4,  // 0: abtestingpublic.GetExperimentResponse.metadata:type_name -> abtestingpublic.Metadata
	5,  // 1: abtestingpublic.Metadata.ab_info:type_name -> abtestingpublic.ABInfo
	10, // 2: abtestingpublic.GetFlagStatusResponse.status:type_name -> google.protobuf.BoolValue
	0,  // 3: abtestingpublic.ABPublicService.GetExperiment:input_type -> abtestingpublic.GetExperimentRequest
	2,  // 4: abtestingpublic.ABPublicService.GetExperimentByKey:input_type -> abtestingpublic.GetExperimentByKeyRequest
	6,  // 5: abtestingpublic.ABPublicService.GetFlagStatus:input_type -> abtestingpublic.GetFlagStatusRequest
	1,  // 6: abtestingpublic.ABPublicService.GetExperimentWithUserID:input_type -> abtestingpublic.GetExperimentWithUserIDRequest
	8,  // 7: abtestingpublic.ABPublicService.UpdateWhiteListByExperimentKey:input_type -> abtestingpublic.UpdateWhiteListByExperimentKeyRequest
	3,  // 8: abtestingpublic.ABPublicService.GetExperiment:output_type -> abtestingpublic.GetExperimentResponse
	3,  // 9: abtestingpublic.ABPublicService.GetExperimentByKey:output_type -> abtestingpublic.GetExperimentResponse
	7,  // 10: abtestingpublic.ABPublicService.GetFlagStatus:output_type -> abtestingpublic.GetFlagStatusResponse
	3,  // 11: abtestingpublic.ABPublicService.GetExperimentWithUserID:output_type -> abtestingpublic.GetExperimentResponse
	9,  // 12: abtestingpublic.ABPublicService.UpdateWhiteListByExperimentKey:output_type -> abtestingpublic.UpdateWhiteListByExperimentKeyResponse
	8,  // [8:13] is the sub-list for method output_type
	3,  // [3:8] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_external_services_ab_platform_ab_platform_proto_init() }
func file_external_services_ab_platform_ab_platform_proto_init() {
	if File_external_services_ab_platform_ab_platform_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_ab_platform_ab_platform_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetExperimentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetExperimentWithUserIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetExperimentByKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetExperimentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ABInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetFlagStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetFlagStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWhiteListByExperimentKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ab_platform_ab_platform_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateWhiteListByExperimentKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_ab_platform_ab_platform_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_ab_platform_ab_platform_proto_goTypes,
		DependencyIndexes: file_external_services_ab_platform_ab_platform_proto_depIdxs,
		MessageInfos:      file_external_services_ab_platform_ab_platform_proto_msgTypes,
	}.Build()
	File_external_services_ab_platform_ab_platform_proto = out.File
	file_external_services_ab_platform_ab_platform_proto_rawDesc = nil
	file_external_services_ab_platform_ab_platform_proto_goTypes = nil
	file_external_services_ab_platform_ab_platform_proto_depIdxs = nil
}
