// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/ab_platform/ab_platform.proto

package abplatform

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ABPublicService_GetExperiment_FullMethodName                  = "/abtestingpublic.ABPublicService/GetExperiment"
	ABPublicService_GetExperimentByKey_FullMethodName             = "/abtestingpublic.ABPublicService/GetExperimentByKey"
	ABPublicService_GetFlagStatus_FullMethodName                  = "/abtestingpublic.ABPublicService/GetFlagStatus"
	ABPublicService_GetExperimentWithUserID_FullMethodName        = "/abtestingpublic.ABPublicService/GetExperimentWithUserID"
	ABPublicService_UpdateWhiteListByExperimentKey_FullMethodName = "/abtestingpublic.ABPublicService/UpdateWhiteListByExperimentKey"
)

// ABPublicServiceClient is the client API for ABPublicService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The ABPublicService service definition
type ABPublicServiceClient interface {
	// Get Experiment By ExperimentID
	GetExperiment(ctx context.Context, in *GetExperimentRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error)
	// Get Experiment By ExperimentKey
	GetExperimentByKey(ctx context.Context, in *GetExperimentByKeyRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error)
	// Get flag status by id
	GetFlagStatus(ctx context.Context, in *GetFlagStatusRequest, opts ...grpc.CallOption) (*GetFlagStatusResponse, error)
	// Get Experiment With UserID
	GetExperimentWithUserID(ctx context.Context, in *GetExperimentWithUserIDRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error)
	// Updating the whitelist for an experiment running
	UpdateWhiteListByExperimentKey(ctx context.Context, in *UpdateWhiteListByExperimentKeyRequest, opts ...grpc.CallOption) (*UpdateWhiteListByExperimentKeyResponse, error)
}

type aBPublicServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewABPublicServiceClient(cc grpc.ClientConnInterface) ABPublicServiceClient {
	return &aBPublicServiceClient{cc}
}

func (c *aBPublicServiceClient) GetExperiment(ctx context.Context, in *GetExperimentRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExperimentResponse)
	err := c.cc.Invoke(ctx, ABPublicService_GetExperiment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aBPublicServiceClient) GetExperimentByKey(ctx context.Context, in *GetExperimentByKeyRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExperimentResponse)
	err := c.cc.Invoke(ctx, ABPublicService_GetExperimentByKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aBPublicServiceClient) GetFlagStatus(ctx context.Context, in *GetFlagStatusRequest, opts ...grpc.CallOption) (*GetFlagStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFlagStatusResponse)
	err := c.cc.Invoke(ctx, ABPublicService_GetFlagStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aBPublicServiceClient) GetExperimentWithUserID(ctx context.Context, in *GetExperimentWithUserIDRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExperimentResponse)
	err := c.cc.Invoke(ctx, ABPublicService_GetExperimentWithUserID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aBPublicServiceClient) UpdateWhiteListByExperimentKey(ctx context.Context, in *UpdateWhiteListByExperimentKeyRequest, opts ...grpc.CallOption) (*UpdateWhiteListByExperimentKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateWhiteListByExperimentKeyResponse)
	err := c.cc.Invoke(ctx, ABPublicService_UpdateWhiteListByExperimentKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ABPublicServiceServer is the server API for ABPublicService service.
// All implementations must embed UnimplementedABPublicServiceServer
// for forward compatibility.
//
// The ABPublicService service definition
type ABPublicServiceServer interface {
	// Get Experiment By ExperimentID
	GetExperiment(context.Context, *GetExperimentRequest) (*GetExperimentResponse, error)
	// Get Experiment By ExperimentKey
	GetExperimentByKey(context.Context, *GetExperimentByKeyRequest) (*GetExperimentResponse, error)
	// Get flag status by id
	GetFlagStatus(context.Context, *GetFlagStatusRequest) (*GetFlagStatusResponse, error)
	// Get Experiment With UserID
	GetExperimentWithUserID(context.Context, *GetExperimentWithUserIDRequest) (*GetExperimentResponse, error)
	// Updating the whitelist for an experiment running
	UpdateWhiteListByExperimentKey(context.Context, *UpdateWhiteListByExperimentKeyRequest) (*UpdateWhiteListByExperimentKeyResponse, error)
	mustEmbedUnimplementedABPublicServiceServer()
}

// UnimplementedABPublicServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedABPublicServiceServer struct{}

func (UnimplementedABPublicServiceServer) GetExperiment(context.Context, *GetExperimentRequest) (*GetExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExperiment not implemented")
}
func (UnimplementedABPublicServiceServer) GetExperimentByKey(context.Context, *GetExperimentByKeyRequest) (*GetExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExperimentByKey not implemented")
}
func (UnimplementedABPublicServiceServer) GetFlagStatus(context.Context, *GetFlagStatusRequest) (*GetFlagStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFlagStatus not implemented")
}
func (UnimplementedABPublicServiceServer) GetExperimentWithUserID(context.Context, *GetExperimentWithUserIDRequest) (*GetExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExperimentWithUserID not implemented")
}
func (UnimplementedABPublicServiceServer) UpdateWhiteListByExperimentKey(context.Context, *UpdateWhiteListByExperimentKeyRequest) (*UpdateWhiteListByExperimentKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWhiteListByExperimentKey not implemented")
}
func (UnimplementedABPublicServiceServer) mustEmbedUnimplementedABPublicServiceServer() {}
func (UnimplementedABPublicServiceServer) testEmbeddedByValue()                         {}

// UnsafeABPublicServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ABPublicServiceServer will
// result in compilation errors.
type UnsafeABPublicServiceServer interface {
	mustEmbedUnimplementedABPublicServiceServer()
}

func RegisterABPublicServiceServer(s grpc.ServiceRegistrar, srv ABPublicServiceServer) {
	// If the following call pancis, it indicates UnimplementedABPublicServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ABPublicService_ServiceDesc, srv)
}

func _ABPublicService_GetExperiment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExperimentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ABPublicServiceServer).GetExperiment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ABPublicService_GetExperiment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ABPublicServiceServer).GetExperiment(ctx, req.(*GetExperimentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ABPublicService_GetExperimentByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExperimentByKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ABPublicServiceServer).GetExperimentByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ABPublicService_GetExperimentByKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ABPublicServiceServer).GetExperimentByKey(ctx, req.(*GetExperimentByKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ABPublicService_GetFlagStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlagStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ABPublicServiceServer).GetFlagStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ABPublicService_GetFlagStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ABPublicServiceServer).GetFlagStatus(ctx, req.(*GetFlagStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ABPublicService_GetExperimentWithUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExperimentWithUserIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ABPublicServiceServer).GetExperimentWithUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ABPublicService_GetExperimentWithUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ABPublicServiceServer).GetExperimentWithUserID(ctx, req.(*GetExperimentWithUserIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ABPublicService_UpdateWhiteListByExperimentKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWhiteListByExperimentKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ABPublicServiceServer).UpdateWhiteListByExperimentKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ABPublicService_UpdateWhiteListByExperimentKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ABPublicServiceServer).UpdateWhiteListByExperimentKey(ctx, req.(*UpdateWhiteListByExperimentKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ABPublicService_ServiceDesc is the grpc.ServiceDesc for ABPublicService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ABPublicService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "abtestingpublic.ABPublicService",
	HandlerType: (*ABPublicServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetExperiment",
			Handler:    _ABPublicService_GetExperiment_Handler,
		},
		{
			MethodName: "GetExperimentByKey",
			Handler:    _ABPublicService_GetExperimentByKey_Handler,
		},
		{
			MethodName: "GetFlagStatus",
			Handler:    _ABPublicService_GetFlagStatus_Handler,
		},
		{
			MethodName: "GetExperimentWithUserID",
			Handler:    _ABPublicService_GetExperimentWithUserID_Handler,
		},
		{
			MethodName: "UpdateWhiteListByExperimentKey",
			Handler:    _ABPublicService_UpdateWhiteListByExperimentKey_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/ab_platform/ab_platform.proto",
}
