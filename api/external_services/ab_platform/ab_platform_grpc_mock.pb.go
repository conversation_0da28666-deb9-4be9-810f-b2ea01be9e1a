// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: external_services/ab_platform/ab_platform.proto

package abplatform

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockABPublicServiceClient is a mock of ABPublicServiceClient interface.
type MockABPublicServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockABPublicServiceClientMockRecorder
}

// MockABPublicServiceClientMockRecorder is the mock recorder for MockABPublicServiceClient.
type MockABPublicServiceClientMockRecorder struct {
	mock *MockABPublicServiceClient
}

// NewMockABPublicServiceClient creates a new mock instance.
func NewMockABPublicServiceClient(ctrl *gomock.Controller) *MockABPublicServiceClient {
	mock := &MockABPublicServiceClient{ctrl: ctrl}
	mock.recorder = &MockABPublicServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockABPublicServiceClient) EXPECT() *MockABPublicServiceClientMockRecorder {
	return m.recorder
}

// GetExperiment mocks base method.
func (m *MockABPublicServiceClient) GetExperiment(ctx context.Context, in *GetExperimentRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExperiment", varargs...)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperiment indicates an expected call of GetExperiment.
func (mr *MockABPublicServiceClientMockRecorder) GetExperiment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperiment", reflect.TypeOf((*MockABPublicServiceClient)(nil).GetExperiment), varargs...)
}

// GetExperimentByKey mocks base method.
func (m *MockABPublicServiceClient) GetExperimentByKey(ctx context.Context, in *GetExperimentByKeyRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExperimentByKey", varargs...)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperimentByKey indicates an expected call of GetExperimentByKey.
func (mr *MockABPublicServiceClientMockRecorder) GetExperimentByKey(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperimentByKey", reflect.TypeOf((*MockABPublicServiceClient)(nil).GetExperimentByKey), varargs...)
}

// GetExperimentWithUserID mocks base method.
func (m *MockABPublicServiceClient) GetExperimentWithUserID(ctx context.Context, in *GetExperimentWithUserIDRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExperimentWithUserID", varargs...)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperimentWithUserID indicates an expected call of GetExperimentWithUserID.
func (mr *MockABPublicServiceClientMockRecorder) GetExperimentWithUserID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperimentWithUserID", reflect.TypeOf((*MockABPublicServiceClient)(nil).GetExperimentWithUserID), varargs...)
}

// GetFlagStatus mocks base method.
func (m *MockABPublicServiceClient) GetFlagStatus(ctx context.Context, in *GetFlagStatusRequest, opts ...grpc.CallOption) (*GetFlagStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFlagStatus", varargs...)
	ret0, _ := ret[0].(*GetFlagStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlagStatus indicates an expected call of GetFlagStatus.
func (mr *MockABPublicServiceClientMockRecorder) GetFlagStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlagStatus", reflect.TypeOf((*MockABPublicServiceClient)(nil).GetFlagStatus), varargs...)
}

// UpdateWhiteListByExperimentKey mocks base method.
func (m *MockABPublicServiceClient) UpdateWhiteListByExperimentKey(ctx context.Context, in *UpdateWhiteListByExperimentKeyRequest, opts ...grpc.CallOption) (*UpdateWhiteListByExperimentKeyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateWhiteListByExperimentKey", varargs...)
	ret0, _ := ret[0].(*UpdateWhiteListByExperimentKeyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWhiteListByExperimentKey indicates an expected call of UpdateWhiteListByExperimentKey.
func (mr *MockABPublicServiceClientMockRecorder) UpdateWhiteListByExperimentKey(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWhiteListByExperimentKey", reflect.TypeOf((*MockABPublicServiceClient)(nil).UpdateWhiteListByExperimentKey), varargs...)
}

// MockABPublicServiceServer is a mock of ABPublicServiceServer interface.
type MockABPublicServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockABPublicServiceServerMockRecorder
}

// MockABPublicServiceServerMockRecorder is the mock recorder for MockABPublicServiceServer.
type MockABPublicServiceServerMockRecorder struct {
	mock *MockABPublicServiceServer
}

// NewMockABPublicServiceServer creates a new mock instance.
func NewMockABPublicServiceServer(ctrl *gomock.Controller) *MockABPublicServiceServer {
	mock := &MockABPublicServiceServer{ctrl: ctrl}
	mock.recorder = &MockABPublicServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockABPublicServiceServer) EXPECT() *MockABPublicServiceServerMockRecorder {
	return m.recorder
}

// GetExperiment mocks base method.
func (m *MockABPublicServiceServer) GetExperiment(ctx context.Context, in *GetExperimentRequest) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExperiment", ctx, in)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperiment indicates an expected call of GetExperiment.
func (mr *MockABPublicServiceServerMockRecorder) GetExperiment(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperiment", reflect.TypeOf((*MockABPublicServiceServer)(nil).GetExperiment), ctx, in)
}

// GetExperimentByKey mocks base method.
func (m *MockABPublicServiceServer) GetExperimentByKey(ctx context.Context, in *GetExperimentByKeyRequest) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExperimentByKey", ctx, in)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperimentByKey indicates an expected call of GetExperimentByKey.
func (mr *MockABPublicServiceServerMockRecorder) GetExperimentByKey(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperimentByKey", reflect.TypeOf((*MockABPublicServiceServer)(nil).GetExperimentByKey), ctx, in)
}

// GetExperimentWithUserID mocks base method.
func (m *MockABPublicServiceServer) GetExperimentWithUserID(ctx context.Context, in *GetExperimentWithUserIDRequest) (*GetExperimentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExperimentWithUserID", ctx, in)
	ret0, _ := ret[0].(*GetExperimentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExperimentWithUserID indicates an expected call of GetExperimentWithUserID.
func (mr *MockABPublicServiceServerMockRecorder) GetExperimentWithUserID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperimentWithUserID", reflect.TypeOf((*MockABPublicServiceServer)(nil).GetExperimentWithUserID), ctx, in)
}

// GetFlagStatus mocks base method.
func (m *MockABPublicServiceServer) GetFlagStatus(ctx context.Context, in *GetFlagStatusRequest) (*GetFlagStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlagStatus", ctx, in)
	ret0, _ := ret[0].(*GetFlagStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlagStatus indicates an expected call of GetFlagStatus.
func (mr *MockABPublicServiceServerMockRecorder) GetFlagStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlagStatus", reflect.TypeOf((*MockABPublicServiceServer)(nil).GetFlagStatus), ctx, in)
}

// UpdateWhiteListByExperimentKey mocks base method.
func (m *MockABPublicServiceServer) UpdateWhiteListByExperimentKey(ctx context.Context, in *UpdateWhiteListByExperimentKeyRequest) (*UpdateWhiteListByExperimentKeyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWhiteListByExperimentKey", ctx, in)
	ret0, _ := ret[0].(*UpdateWhiteListByExperimentKeyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateWhiteListByExperimentKey indicates an expected call of UpdateWhiteListByExperimentKey.
func (mr *MockABPublicServiceServerMockRecorder) UpdateWhiteListByExperimentKey(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWhiteListByExperimentKey", reflect.TypeOf((*MockABPublicServiceServer)(nil).UpdateWhiteListByExperimentKey), ctx, in)
}
