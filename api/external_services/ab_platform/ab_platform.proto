syntax = "proto3";

package abtestingpublic;
option go_package = "platform/abplatform";

import "google/protobuf/wrappers.proto";

// The ABPublicService service definition
service ABPublicService {
  // Get Experiment By ExperimentID
  rpc GetExperiment (GetExperimentRequest) returns (GetExperimentResponse) {}
  // Get Experiment By ExperimentKey
  rpc GetExperimentByKey (GetExperimentByKeyRequest) returns (GetExperimentResponse) {}
  // Get flag status by id
  rpc GetFlagStatus(GetFlagStatusRequest) returns (GetFlagStatusResponse) {}
  // Get Experiment With UserID
  rpc GetExperimentWithUserID (GetExperimentWithUserIDRequest) returns (GetExperimentResponse) {}
  // Updating the whitelist for an experiment running
  rpc UpdateWhiteListByExperimentKey(UpdateWhiteListByExperimentKeyRequest) returns (UpdateWhiteListByExperimentKeyResponse) {}
}

// GetExperimentRequest
message GetExperimentRequest {
  int64 experiment_id = 1;
}

// GetExperimentWithUserIDRequest
message GetExperimentWithUserIDRequest {
  int64 experiment_id = 1;
  string user_id = 2;
}

// GetExperimentRequest
message GetExperimentByKeyRequest {
  string experiment_key = 1;
}

// GetExperimentResponse
message GetExperimentResponse {
  string group = 1;
  string config = 2;
  Metadata metadata = 3;
}

// Metadata message
message Metadata {
  ABInfo ab_info = 1;
}

// Behavior tracking message
message ABInfo {
  string experiment_id = 1;
  string group_name = 2;
  string access = 3;
  string experiment_key = 4;
}

// StatusRequest
message GetFlagStatusRequest {
  int64 flag_id = 1;
}

// GetFlagStatusResponse
message GetFlagStatusResponse {
  google.protobuf.BoolValue status = 1;
}

message UpdateWhiteListByExperimentKeyRequest {
  string experiment_key = 1;
  repeated string list_zalopay_ids = 2;
}

message UpdateWhiteListByExperimentKeyResponse {
  repeated string added_list = 1;
}