// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/payment/v1/refund.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RefundSettleEventType int32

const (
	RefundSettleEventType_REFUND_SETTLE_EVENT_UNKNOWN  RefundSettleEventType = 0
	RefundSettleEventType_REFUND_SETTLE_EVENT_REQUEST  RefundSettleEventType = 1
	RefundSettleEventType_REFUND_SETTLE_EVENT_RESPONSE RefundSettleEventType = 2
)

// Enum value maps for RefundSettleEventType.
var (
	RefundSettleEventType_name = map[int32]string{
		0: "REFUND_SETTLE_EVENT_UNKNOWN",
		1: "REFUND_SETTLE_EVENT_REQUEST",
		2: "REFUND_SETTLE_EVENT_RESPONSE",
	}
	RefundSettleEventType_value = map[string]int32{
		"REFUND_SETTLE_EVENT_UNKNOWN":  0,
		"REFUND_SETTLE_EVENT_REQUEST":  1,
		"REFUND_SETTLE_EVENT_RESPONSE": 2,
	}
)

func (x RefundSettleEventType) Enum() *RefundSettleEventType {
	p := new(RefundSettleEventType)
	*p = x
	return p
}

func (x RefundSettleEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundSettleEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_refund_proto_enumTypes[0].Descriptor()
}

func (RefundSettleEventType) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_refund_proto_enumTypes[0]
}

func (x RefundSettleEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundSettleEventType.Descriptor instead.
func (RefundSettleEventType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{0}
}

type RefundType int32

const (
	RefundType_REFUND_TYPE_UNKNOWN RefundType = 0
	RefundType_REFUND_TYPE_AUTO    RefundType = 1
	RefundType_REFUND_TYPE_MANUAL  RefundType = 2
)

// Enum value maps for RefundType.
var (
	RefundType_name = map[int32]string{
		0: "REFUND_TYPE_UNKNOWN",
		1: "REFUND_TYPE_AUTO",
		2: "REFUND_TYPE_MANUAL",
	}
	RefundType_value = map[string]int32{
		"REFUND_TYPE_UNKNOWN": 0,
		"REFUND_TYPE_AUTO":    1,
		"REFUND_TYPE_MANUAL":  2,
	}
)

func (x RefundType) Enum() *RefundType {
	p := new(RefundType)
	*p = x
	return p
}

func (x RefundType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_refund_proto_enumTypes[1].Descriptor()
}

func (RefundType) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_refund_proto_enumTypes[1]
}

func (x RefundType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundType.Descriptor instead.
func (RefundType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{1}
}

type RefundSettleEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId      string                `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	EventType    RefundSettleEventType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=api.payment.v1.RefundSettleEventType" json:"event_type,omitempty"`
	SettleStatus SettleStatus          `protobuf:"varint,3,opt,name=settle_status,json=settleStatus,proto3,enum=api.payment.v1.SettleStatus" json:"settle_status,omitempty"`
	RefZpTransId int64                 `protobuf:"varint,4,opt,name=ref_zp_trans_id,json=refZpTransId,proto3" json:"ref_zp_trans_id,omitempty"`
	SettleAmount int64                 `protobuf:"varint,5,opt,name=settle_amount,json=settleAmount,proto3" json:"settle_amount,omitempty"`
	// The context of the settlement
	//
	// Types that are assignable to SettleContext:
	//
	//	*RefundSettleEvent_StandardSettle
	//	*RefundSettleEvent_ExpiredSettle
	SettleContext isRefundSettleEvent_SettleContext     `protobuf_oneof:"settle_context"`
	SettleResult  *RefundSettleEvent_RefundSettleResult `protobuf:"bytes,50,opt,name=settle_result,json=settleResult,proto3" json:"settle_result,omitempty"`
}

func (x *RefundSettleEvent) Reset() {
	*x = RefundSettleEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_v1_refund_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundSettleEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundSettleEvent) ProtoMessage() {}

func (x *RefundSettleEvent) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_v1_refund_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundSettleEvent.ProtoReflect.Descriptor instead.
func (*RefundSettleEvent) Descriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{0}
}

func (x *RefundSettleEvent) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *RefundSettleEvent) GetEventType() RefundSettleEventType {
	if x != nil {
		return x.EventType
	}
	return RefundSettleEventType_REFUND_SETTLE_EVENT_UNKNOWN
}

func (x *RefundSettleEvent) GetSettleStatus() SettleStatus {
	if x != nil {
		return x.SettleStatus
	}
	return SettleStatus_SETTLE_STATUS_UNSPECIFIED
}

func (x *RefundSettleEvent) GetRefZpTransId() int64 {
	if x != nil {
		return x.RefZpTransId
	}
	return 0
}

func (x *RefundSettleEvent) GetSettleAmount() int64 {
	if x != nil {
		return x.SettleAmount
	}
	return 0
}

func (m *RefundSettleEvent) GetSettleContext() isRefundSettleEvent_SettleContext {
	if m != nil {
		return m.SettleContext
	}
	return nil
}

func (x *RefundSettleEvent) GetStandardSettle() *RefundStandardSettle {
	if x, ok := x.GetSettleContext().(*RefundSettleEvent_StandardSettle); ok {
		return x.StandardSettle
	}
	return nil
}

func (x *RefundSettleEvent) GetExpiredSettle() *RefundExpiredSettle {
	if x, ok := x.GetSettleContext().(*RefundSettleEvent_ExpiredSettle); ok {
		return x.ExpiredSettle
	}
	return nil
}

func (x *RefundSettleEvent) GetSettleResult() *RefundSettleEvent_RefundSettleResult {
	if x != nil {
		return x.SettleResult
	}
	return nil
}

type isRefundSettleEvent_SettleContext interface {
	isRefundSettleEvent_SettleContext()
}

type RefundSettleEvent_StandardSettle struct {
	StandardSettle *RefundStandardSettle `protobuf:"bytes,6,opt,name=standard_settle,json=standardSettle,proto3,oneof"`
}

type RefundSettleEvent_ExpiredSettle struct {
	ExpiredSettle *RefundExpiredSettle `protobuf:"bytes,7,opt,name=expired_settle,json=expiredSettle,proto3,oneof"`
}

func (*RefundSettleEvent_StandardSettle) isRefundSettleEvent_SettleContext() {}

func (*RefundSettleEvent_ExpiredSettle) isRefundSettleEvent_SettleContext() {}

// For normal settlement processing
type RefundStandardSettle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettleId int64 `protobuf:"varint,1,opt,name=settle_id,json=settleId,proto3" json:"settle_id,omitempty"`
}

func (x *RefundStandardSettle) Reset() {
	*x = RefundStandardSettle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_v1_refund_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundStandardSettle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundStandardSettle) ProtoMessage() {}

func (x *RefundStandardSettle) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_v1_refund_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundStandardSettle.ProtoReflect.Descriptor instead.
func (*RefundStandardSettle) Descriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{1}
}

func (x *RefundStandardSettle) GetSettleId() int64 {
	if x != nil {
		return x.SettleId
	}
	return 0
}

// For expired refund log settlement
type RefundExpiredSettle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId int64 `protobuf:"varint,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"` // The specific log being processed
}

func (x *RefundExpiredSettle) Reset() {
	*x = RefundExpiredSettle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_v1_refund_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundExpiredSettle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundExpiredSettle) ProtoMessage() {}

func (x *RefundExpiredSettle) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_v1_refund_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundExpiredSettle.ProtoReflect.Descriptor instead.
func (*RefundExpiredSettle) Descriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{2}
}

func (x *RefundExpiredSettle) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

type RefundSettleEvent_RefundSettleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransId      int64       `protobuf:"varint,1,opt,name=trans_id,json=transId,proto3" json:"trans_id,omitempty"`
	OrderId      int64       `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AppId        int32       `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppTransId   string      `protobuf:"bytes,4,opt,name=app_trans_id,json=appTransId,proto3" json:"app_trans_id,omitempty"`
	TransStatus  TransStatus `protobuf:"varint,5,opt,name=trans_status,json=transStatus,proto3,enum=api.payment.v1.TransStatus" json:"trans_status,omitempty"`
	ErrorMessage string      `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *RefundSettleEvent_RefundSettleResult) Reset() {
	*x = RefundSettleEvent_RefundSettleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_v1_refund_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundSettleEvent_RefundSettleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundSettleEvent_RefundSettleResult) ProtoMessage() {}

func (x *RefundSettleEvent_RefundSettleResult) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_v1_refund_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundSettleEvent_RefundSettleResult.ProtoReflect.Descriptor instead.
func (*RefundSettleEvent_RefundSettleResult) Descriptor() ([]byte, []int) {
	return file_external_services_payment_v1_refund_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RefundSettleEvent_RefundSettleResult) GetTransId() int64 {
	if x != nil {
		return x.TransId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *RefundSettleEvent_RefundSettleResult) GetAppTransId() string {
	if x != nil {
		return x.AppTransId
	}
	return ""
}

func (x *RefundSettleEvent_RefundSettleResult) GetTransStatus() TransStatus {
	if x != nil {
		return x.TransStatus
	}
	return TransStatus_TRANS_STATUS_UNSPECIFIED
}

func (x *RefundSettleEvent_RefundSettleResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_external_services_payment_v1_refund_proto protoreflect.FileDescriptor

var file_external_services_payment_v1_refund_proto_rawDesc = []byte{
	0x0a, 0x29, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x29, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x05, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a,
	0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x5f, 0x7a, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x5a, 0x70,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0f,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x4c, 0x0a,
	0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x59, 0x0a, 0x0d, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0xe8, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x42, 0x10, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x22, 0x33, 0x0a, 0x14, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x72, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x2a, 0x7b, 0x0a, 0x15,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x02, 0x2a, 0x53, 0x0a, 0x0a, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x42, 0x45,
	0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79,
	0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_payment_v1_refund_proto_rawDescOnce sync.Once
	file_external_services_payment_v1_refund_proto_rawDescData = file_external_services_payment_v1_refund_proto_rawDesc
)

func file_external_services_payment_v1_refund_proto_rawDescGZIP() []byte {
	file_external_services_payment_v1_refund_proto_rawDescOnce.Do(func() {
		file_external_services_payment_v1_refund_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_payment_v1_refund_proto_rawDescData)
	})
	return file_external_services_payment_v1_refund_proto_rawDescData
}

var file_external_services_payment_v1_refund_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_external_services_payment_v1_refund_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_external_services_payment_v1_refund_proto_goTypes = []any{
	(RefundSettleEventType)(0),                   // 0: api.payment.v1.RefundSettleEventType
	(RefundType)(0),                              // 1: api.payment.v1.RefundType
	(*RefundSettleEvent)(nil),                    // 2: api.payment.v1.RefundSettleEvent
	(*RefundStandardSettle)(nil),                 // 3: api.payment.v1.RefundStandardSettle
	(*RefundExpiredSettle)(nil),                  // 4: api.payment.v1.RefundExpiredSettle
	(*RefundSettleEvent_RefundSettleResult)(nil), // 5: api.payment.v1.RefundSettleEvent.RefundSettleResult
	(SettleStatus)(0),                            // 6: api.payment.v1.SettleStatus
	(TransStatus)(0),                             // 7: api.payment.v1.TransStatus
}
var file_external_services_payment_v1_refund_proto_depIdxs = []int32{
	0, // 0: api.payment.v1.RefundSettleEvent.event_type:type_name -> api.payment.v1.RefundSettleEventType
	6, // 1: api.payment.v1.RefundSettleEvent.settle_status:type_name -> api.payment.v1.SettleStatus
	3, // 2: api.payment.v1.RefundSettleEvent.standard_settle:type_name -> api.payment.v1.RefundStandardSettle
	4, // 3: api.payment.v1.RefundSettleEvent.expired_settle:type_name -> api.payment.v1.RefundExpiredSettle
	5, // 4: api.payment.v1.RefundSettleEvent.settle_result:type_name -> api.payment.v1.RefundSettleEvent.RefundSettleResult
	7, // 5: api.payment.v1.RefundSettleEvent.RefundSettleResult.trans_status:type_name -> api.payment.v1.TransStatus
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_external_services_payment_v1_refund_proto_init() }
func file_external_services_payment_v1_refund_proto_init() {
	if File_external_services_payment_v1_refund_proto != nil {
		return
	}
	file_external_services_payment_v1_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_external_services_payment_v1_refund_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*RefundSettleEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_v1_refund_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*RefundStandardSettle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_v1_refund_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*RefundExpiredSettle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_payment_v1_refund_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*RefundSettleEvent_RefundSettleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_external_services_payment_v1_refund_proto_msgTypes[0].OneofWrappers = []any{
		(*RefundSettleEvent_StandardSettle)(nil),
		(*RefundSettleEvent_ExpiredSettle)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_payment_v1_refund_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_external_services_payment_v1_refund_proto_goTypes,
		DependencyIndexes: file_external_services_payment_v1_refund_proto_depIdxs,
		EnumInfos:         file_external_services_payment_v1_refund_proto_enumTypes,
		MessageInfos:      file_external_services_payment_v1_refund_proto_msgTypes,
	}.Build()
	File_external_services_payment_v1_refund_proto = out.File
	file_external_services_payment_v1_refund_proto_rawDesc = nil
	file_external_services_payment_v1_refund_proto_goTypes = nil
	file_external_services_payment_v1_refund_proto_depIdxs = nil
}
