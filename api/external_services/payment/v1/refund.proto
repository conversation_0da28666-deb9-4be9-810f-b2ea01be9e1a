syntax = "proto3";

package api.payment.v1;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

import "external_services/payment/v1/common.proto";

message RefundSettleEvent {
  message RefundSettleResult {
    int64 trans_id = 1;
    int64 order_id = 2;
    int32 app_id = 3;
    string app_trans_id = 4;
    TransStatus trans_status = 5;
    string error_message = 6;
  }

  string event_id = 1;
  RefundSettleEventType event_type = 2;
  SettleStatus settle_status = 3;
  int64 ref_zp_trans_id = 4;
  int64 settle_amount = 5;

  // The context of the settlement
  oneof settle_context {
    RefundStandardSettle standard_settle = 6;
    RefundExpiredSettle expired_settle = 7;
  }

  RefundSettleResult settle_result = 50; 
}

// For normal settlement processing
message RefundStandardSettle {
  int64 settle_id = 1;
}

// For expired refund log settlement
message RefundExpiredSettle {
  int64 refund_id = 1;  // The specific log being processed
}

enum RefundSettleEventType {
  REFUND_SETTLE_EVENT_UNKNOWN = 0;
  REFUND_SETTLE_EVENT_REQUEST = 1;
  REFUND_SETTLE_EVENT_RESPONSE = 2;
}

enum RefundType {
  REFUND_TYPE_UNKNOWN = 0;
  REFUND_TYPE_AUTO = 1;
  REFUND_TYPE_MANUAL = 2;
}
