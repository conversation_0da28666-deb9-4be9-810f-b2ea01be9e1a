// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/payment/v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransType int32

const (
	TransType_TRANS_TYPE_UNSPECIFIED TransType = 0
	TransType_TRANS_TYPE_PAYMENT     TransType = 200
	TransType_TRANS_TYPE_REPAYMENT   TransType = 201
)

// Enum value maps for TransType.
var (
	TransType_name = map[int32]string{
		0:   "TRANS_TYPE_UNSPECIFIED",
		200: "TRANS_TYPE_PAYMENT",
		201: "TRANS_TYPE_REPAYMENT",
	}
	TransType_value = map[string]int32{
		"TRANS_TYPE_UNSPECIFIED": 0,
		"TRANS_TYPE_PAYMENT":     200,
		"TRANS_TYPE_REPAYMENT":   201,
	}
)

func (x TransType) Enum() *TransType {
	p := new(TransType)
	*p = x
	return p
}

func (x TransType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransType) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_common_proto_enumTypes[0].Descriptor()
}

func (TransType) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_common_proto_enumTypes[0]
}

func (x TransType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransType.Descriptor instead.
func (TransType) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_common_proto_rawDescGZIP(), []int{0}
}

type TransStatus int32

const (
	TransStatus_TRANS_STATUS_UNSPECIFIED TransStatus = 0
	TransStatus_TRANS_STATUS_SUCCESS     TransStatus = 1
	TransStatus_TRANS_STATUS_FAILED      TransStatus = 2
	TransStatus_TRANS_STATUS_PENDING     TransStatus = 3
	TransStatus_TRANS_STATUS_PROCESSING  TransStatus = 4
)

// Enum value maps for TransStatus.
var (
	TransStatus_name = map[int32]string{
		0: "TRANS_STATUS_UNSPECIFIED",
		1: "TRANS_STATUS_SUCCESS",
		2: "TRANS_STATUS_FAILED",
		3: "TRANS_STATUS_PENDING",
		4: "TRANS_STATUS_PROCESSING",
	}
	TransStatus_value = map[string]int32{
		"TRANS_STATUS_UNSPECIFIED": 0,
		"TRANS_STATUS_SUCCESS":     1,
		"TRANS_STATUS_FAILED":      2,
		"TRANS_STATUS_PENDING":     3,
		"TRANS_STATUS_PROCESSING":  4,
	}
)

func (x TransStatus) Enum() *TransStatus {
	p := new(TransStatus)
	*p = x
	return p
}

func (x TransStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_common_proto_enumTypes[1].Descriptor()
}

func (TransStatus) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_common_proto_enumTypes[1]
}

func (x TransStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransStatus.Descriptor instead.
func (TransStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_common_proto_rawDescGZIP(), []int{1}
}

// SettleStatus is a settle status of loan transaction
// Currently SETTLE_STATUS_UNSETTLED is is includes some status like (PENDING, PROCESSING). Can be extracted to more detail status in the future
type SettleStatus int32

const (
	SettleStatus_SETTLE_STATUS_UNSPECIFIED SettleStatus = 0
	SettleStatus_SETTLE_STATUS_SETTLED     SettleStatus = 1
	SettleStatus_SETTLE_STATUS_UNSETTLED   SettleStatus = 2
	SettleStatus_SETTLE_STATUS_PROCESSING  SettleStatus = 3
)

// Enum value maps for SettleStatus.
var (
	SettleStatus_name = map[int32]string{
		0: "SETTLE_STATUS_UNSPECIFIED",
		1: "SETTLE_STATUS_SETTLED",
		2: "SETTLE_STATUS_UNSETTLED",
		3: "SETTLE_STATUS_PROCESSING",
	}
	SettleStatus_value = map[string]int32{
		"SETTLE_STATUS_UNSPECIFIED": 0,
		"SETTLE_STATUS_SETTLED":     1,
		"SETTLE_STATUS_UNSETTLED":   2,
		"SETTLE_STATUS_PROCESSING":  3,
	}
)

func (x SettleStatus) Enum() *SettleStatus {
	p := new(SettleStatus)
	*p = x
	return p
}

func (x SettleStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SettleStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_common_proto_enumTypes[2].Descriptor()
}

func (SettleStatus) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_common_proto_enumTypes[2]
}

func (x SettleStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SettleStatus.Descriptor instead.
func (SettleStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_common_proto_rawDescGZIP(), []int{2}
}

type SubCode int32

const (
	SubCode_UNKNOWN                         SubCode = 0
	SubCode_DATABASE_ERROR                  SubCode = 1
	SubCode_BAD_REQUEST                     SubCode = 2
	SubCode_CALL_CONNECTOR_FAILED           SubCode = 3
	SubCode_INTERNAL                        SubCode = 4
	SubCode_PUBLISH_KAFKA_FAILED            SubCode = 5
	SubCode_ZALOPAY_CORP_BALANCE_NOT_ENOUGH SubCode = 6
	SubCode_INVALID_PARTNER                 SubCode = 7
	// Purchase Code
	SubCode_DUPLICATED_TRANS_ID         SubCode = 200
	SubCode_ACCOUNT_NOT_EXIST           SubCode = 201
	SubCode_BALANCE_NOT_ENOUGH          SubCode = 202
	SubCode_ACCOUNT_LOG_NOT_EXIST       SubCode = 203
	SubCode_ACCOUNT_IS_INACTIVE         SubCode = 204
	SubCode_TRANSACTION_EXPIRED         SubCode = 205
	SubCode_OD_ACCOUNT_IS_BLOCKED       SubCode = 206
	SubCode_ACCOUNT_IS_BLOCKED          SubCode = 207
	SubCode_INVALID_BENEFICIARY         SubCode = 208
	SubCode_INVALID_PARTNER_BENEFICIARY SubCode = 209
	SubCode_TRANSACTION_NOT_FOUND       SubCode = 210
	SubCode_INVALID_ACCOUNT_INFORMATION SubCode = 211
	SubCode_TRANSACTION_UNAUTHENTICATED SubCode = 212
	// Repayment
	SubCode_INSUFFICIENT_DEBIT_BALANCE SubCode = 301
	SubCode_NOT_SUPPORT_QR             SubCode = 302
	SubCode_VIOLATE_PARTNER_POLICY SubCode = 401
)

// Enum value maps for SubCode.
var (
	SubCode_name = map[int32]string{
		0:   "UNKNOWN",
		1:   "DATABASE_ERROR",
		2:   "BAD_REQUEST",
		3:   "CALL_CONNECTOR_FAILED",
		4:   "INTERNAL",
		5:   "PUBLISH_KAFKA_FAILED",
		6:   "ZALOPAY_CORP_BALANCE_NOT_ENOUGH",
		7:   "INVALID_PARTNER",
		200: "DUPLICATED_TRANS_ID",
		201: "ACCOUNT_NOT_EXIST",
		202: "BALANCE_NOT_ENOUGH",
		203: "ACCOUNT_LOG_NOT_EXIST",
		204: "ACCOUNT_IS_INACTIVE",
		205: "TRANSACTION_EXPIRED",
		206: "OD_ACCOUNT_IS_BLOCKED",
		207: "ACCOUNT_IS_BLOCKED",
		208: "INVALID_BENEFICIARY",
		209: "INVALID_PARTNER_BENEFICIARY",
		210: "TRANSACTION_NOT_FOUND",
		211: "INVALID_ACCOUNT_INFORMATION",
		212: "TRANSACTION_UNAUTHENTICATED",
		301: "INSUFFICIENT_DEBIT_BALANCE",
		302: "NOT_SUPPORT_QR",
		401: "VIOLATE_PARTNER_POLICY",
	}
	SubCode_value = map[string]int32{
		"UNKNOWN":                         0,
		"DATABASE_ERROR":                  1,
		"BAD_REQUEST":                     2,
		"CALL_CONNECTOR_FAILED":           3,
		"INTERNAL":                        4,
		"PUBLISH_KAFKA_FAILED":            5,
		"ZALOPAY_CORP_BALANCE_NOT_ENOUGH": 6,
		"INVALID_PARTNER":                 7,
		"DUPLICATED_TRANS_ID":             200,
		"ACCOUNT_NOT_EXIST":               201,
		"BALANCE_NOT_ENOUGH":              202,
		"ACCOUNT_LOG_NOT_EXIST":           203,
		"ACCOUNT_IS_INACTIVE":             204,
		"TRANSACTION_EXPIRED":             205,
		"OD_ACCOUNT_IS_BLOCKED":           206,
		"ACCOUNT_IS_BLOCKED":              207,
		"INVALID_BENEFICIARY":             208,
		"INVALID_PARTNER_BENEFICIARY":     209,
		"TRANSACTION_NOT_FOUND":           210,
		"INVALID_ACCOUNT_INFORMATION":     211,
		"TRANSACTION_UNAUTHENTICATED":     212,
		"INSUFFICIENT_DEBIT_BALANCE":      301,
		"NOT_SUPPORT_QR":                  302,
		"VIOLATE_PARTNER_POLICY":          401,
	}
)

func (x SubCode) Enum() *SubCode {
	p := new(SubCode)
	*p = x
	return p
}

func (x SubCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubCode) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_payment_v1_common_proto_enumTypes[3].Descriptor()
}

func (SubCode) Type() protoreflect.EnumType {
	return &file_external_services_payment_v1_common_proto_enumTypes[3]
}

func (x SubCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubCode.Descriptor instead.
func (SubCode) EnumDescriptor() ([]byte, []int) {
	return file_external_services_payment_v1_common_proto_rawDescGZIP(), []int{3}
}

// ErrorDetail describe the detail of error when the trans_status is TRANS_STATUS_FAILED
type ErrorDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Code is the error code when the error occurs in the Installment system
	Code SubCode `protobuf:"varint,1,opt,name=code,proto3,enum=api.payment.v1.SubCode" json:"code,omitempty"`
	// BankReturnCode is the return code from bank when transaction failed
	BankReturnCode string `protobuf:"bytes,2,opt,name=bank_return_code,json=bankReturnCode,proto3" json:"bank_return_code,omitempty"`
	// Message is the error description
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ErrorDetail) Reset() {
	*x = ErrorDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_payment_v1_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDetail) ProtoMessage() {}

func (x *ErrorDetail) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_payment_v1_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDetail.ProtoReflect.Descriptor instead.
func (*ErrorDetail) Descriptor() ([]byte, []int) {
	return file_external_services_payment_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorDetail) GetCode() SubCode {
	if x != nil {
		return x.Code
	}
	return SubCode_UNKNOWN
}

func (x *ErrorDetail) GetBankReturnCode() string {
	if x != nil {
		return x.BankReturnCode
	}
	return ""
}

func (x *ErrorDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_external_services_payment_v1_common_proto protoreflect.FileDescriptor

var file_external_services_payment_v1_common_proto_rawDesc = []byte{
	0x0a, 0x29, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22, 0x7e, 0x0a, 0x0b, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x5b, 0x0a, 0x09, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x12, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xc8, 0x01, 0x12, 0x19, 0x0a,
	0x14, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xc9, 0x01, 0x2a, 0x95, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x04,
	0x2a, 0x83, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x53,
	0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x45, 0x54, 0x54, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x54, 0x54,
	0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x2a, 0xf7, 0x04, 0x0a, 0x07, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x44, 0x41, 0x54, 0x41, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e,
	0x4e, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x18, 0x0a,
	0x14, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x5f, 0x4b, 0x41, 0x46, 0x4b, 0x41, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x5a, 0x41, 0x4c, 0x4f, 0x50,
	0x41, 0x59, 0x5f, 0x43, 0x4f, 0x52, 0x50, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x10,
	0x07, 0x12, 0x18, 0x0a, 0x13, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x44, 0x10, 0xc8, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x10, 0xc9, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0xca, 0x01, 0x12, 0x1a, 0x0a, 0x15,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xcb, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0xcc, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0xcd, 0x01, 0x12, 0x1a, 0x0a, 0x15,
	0x4f, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0xce, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0xcf,
	0x01, 0x12, 0x18, 0x0a, 0x13, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x10, 0xd0, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x42,
	0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x10, 0xd1, 0x01, 0x12, 0x1a, 0x0a,
	0x15, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xd2, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd3, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x41, 0x55, 0x54,
	0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0xd4, 0x01, 0x12, 0x1f, 0x0a,
	0x1a, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x42, 0x49, 0x54, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0xad, 0x02, 0x12, 0x13,
	0x0a, 0x0e, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x51, 0x52,
	0x10, 0xae, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x56, 0x49, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x50,
	0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x91, 0x03,
	0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x7a, 0x61, 0x6c, 0x6f, 0x70,
	0x61, 0x79, 0x2e, 0x76, 0x6e, 0x2f, 0x66, 0x69, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_payment_v1_common_proto_rawDescOnce sync.Once
	file_external_services_payment_v1_common_proto_rawDescData = file_external_services_payment_v1_common_proto_rawDesc
)

func file_external_services_payment_v1_common_proto_rawDescGZIP() []byte {
	file_external_services_payment_v1_common_proto_rawDescOnce.Do(func() {
		file_external_services_payment_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_payment_v1_common_proto_rawDescData)
	})
	return file_external_services_payment_v1_common_proto_rawDescData
}

var file_external_services_payment_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_external_services_payment_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_external_services_payment_v1_common_proto_goTypes = []any{
	(TransType)(0),      // 0: api.payment.v1.TransType
	(TransStatus)(0),    // 1: api.payment.v1.TransStatus
	(SettleStatus)(0),   // 2: api.payment.v1.SettleStatus
	(SubCode)(0),        // 3: api.payment.v1.SubCode
	(*ErrorDetail)(nil), // 4: api.payment.v1.ErrorDetail
}
var file_external_services_payment_v1_common_proto_depIdxs = []int32{
	3, // 0: api.payment.v1.ErrorDetail.code:type_name -> api.payment.v1.SubCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_external_services_payment_v1_common_proto_init() }
func file_external_services_payment_v1_common_proto_init() {
	if File_external_services_payment_v1_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_payment_v1_common_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ErrorDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_payment_v1_common_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_external_services_payment_v1_common_proto_goTypes,
		DependencyIndexes: file_external_services_payment_v1_common_proto_depIdxs,
		EnumInfos:         file_external_services_payment_v1_common_proto_enumTypes,
		MessageInfos:      file_external_services_payment_v1_common_proto_msgTypes,
	}.Build()
	File_external_services_payment_v1_common_proto = out.File
	file_external_services_payment_v1_common_proto_rawDesc = nil
	file_external_services_payment_v1_common_proto_goTypes = nil
	file_external_services_payment_v1_common_proto_depIdxs = nil
}
