syntax = "proto3";

package ekyccenter.v2;

option go_package = "ekyc/ekyccenterv2";

service EKycCenterService {
  // Get user kyc status
  rpc GetUserEKycStatus(ekyccenter.v2.GetUserEKycStatusRequest) returns (ekyccenter.v2.GetUserEKycStatusResponse);
}

message GetUserEKycStatusRequest {
  string client_id = 1;
  string client_key = 2;
  string user_id = 3;
}

message GetUserEKycStatusResponse {
  string ekyc_id = 1;
  Status status = 2;
  enum Status {
    STATUS_INVALID = 0;
    PROCESSING = 1;
    APPROVE = 2;
    REJECT = 3;
  }
}
