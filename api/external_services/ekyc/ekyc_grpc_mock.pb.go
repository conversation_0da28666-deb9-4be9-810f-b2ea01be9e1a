// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: external_services/ekyc/ekyc.proto

package ekyccenterv2

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEKycCenterServiceClient is a mock of EKycCenterServiceClient interface.
type MockEKycCenterServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockEKycCenterServiceClientMockRecorder
}

// MockEKycCenterServiceClientMockRecorder is the mock recorder for MockEKycCenterServiceClient.
type MockEKycCenterServiceClientMockRecorder struct {
	mock *MockEKycCenterServiceClient
}

// NewMockEKycCenterServiceClient creates a new mock instance.
func NewMockEKycCenterServiceClient(ctrl *gomock.Controller) *MockEKycCenterServiceClient {
	mock := &MockEKycCenterServiceClient{ctrl: ctrl}
	mock.recorder = &MockEKycCenterServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEKycCenterServiceClient) EXPECT() *MockEKycCenterServiceClientMockRecorder {
	return m.recorder
}

// GetUserEKycStatus mocks base method.
func (m *MockEKycCenterServiceClient) GetUserEKycStatus(ctx context.Context, in *GetUserEKycStatusRequest, opts ...grpc.CallOption) (*GetUserEKycStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserEKycStatus", varargs...)
	ret0, _ := ret[0].(*GetUserEKycStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserEKycStatus indicates an expected call of GetUserEKycStatus.
func (mr *MockEKycCenterServiceClientMockRecorder) GetUserEKycStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEKycStatus", reflect.TypeOf((*MockEKycCenterServiceClient)(nil).GetUserEKycStatus), varargs...)
}

// MockEKycCenterServiceServer is a mock of EKycCenterServiceServer interface.
type MockEKycCenterServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockEKycCenterServiceServerMockRecorder
}

// MockEKycCenterServiceServerMockRecorder is the mock recorder for MockEKycCenterServiceServer.
type MockEKycCenterServiceServerMockRecorder struct {
	mock *MockEKycCenterServiceServer
}

// NewMockEKycCenterServiceServer creates a new mock instance.
func NewMockEKycCenterServiceServer(ctrl *gomock.Controller) *MockEKycCenterServiceServer {
	mock := &MockEKycCenterServiceServer{ctrl: ctrl}
	mock.recorder = &MockEKycCenterServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEKycCenterServiceServer) EXPECT() *MockEKycCenterServiceServerMockRecorder {
	return m.recorder
}

// GetUserEKycStatus mocks base method.
func (m *MockEKycCenterServiceServer) GetUserEKycStatus(ctx context.Context, in *GetUserEKycStatusRequest) (*GetUserEKycStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserEKycStatus", ctx, in)
	ret0, _ := ret[0].(*GetUserEKycStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserEKycStatus indicates an expected call of GetUserEKycStatus.
func (mr *MockEKycCenterServiceServerMockRecorder) GetUserEKycStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEKycStatus", reflect.TypeOf((*MockEKycCenterServiceServer)(nil).GetUserEKycStatus), ctx, in)
}
