// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/ekyc/ekyc.proto

package ekyccenterv2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EKycCenterService_GetUserEKycStatus_FullMethodName = "/ekyccenter.v2.EKycCenterService/GetUserEKycStatus"
)

// EKycCenterServiceClient is the client API for EKycCenterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EKycCenterServiceClient interface {
	// Get user kyc status
	GetUserEKycStatus(ctx context.Context, in *GetUserEKycStatusRequest, opts ...grpc.CallOption) (*GetUserEKycStatusResponse, error)
}

type eKycCenterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEKycCenterServiceClient(cc grpc.ClientConnInterface) EKycCenterServiceClient {
	return &eKycCenterServiceClient{cc}
}

func (c *eKycCenterServiceClient) GetUserEKycStatus(ctx context.Context, in *GetUserEKycStatusRequest, opts ...grpc.CallOption) (*GetUserEKycStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserEKycStatusResponse)
	err := c.cc.Invoke(ctx, EKycCenterService_GetUserEKycStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EKycCenterServiceServer is the server API for EKycCenterService service.
// All implementations must embed UnimplementedEKycCenterServiceServer
// for forward compatibility.
type EKycCenterServiceServer interface {
	// Get user kyc status
	GetUserEKycStatus(context.Context, *GetUserEKycStatusRequest) (*GetUserEKycStatusResponse, error)
	mustEmbedUnimplementedEKycCenterServiceServer()
}

// UnimplementedEKycCenterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEKycCenterServiceServer struct{}

func (UnimplementedEKycCenterServiceServer) GetUserEKycStatus(context.Context, *GetUserEKycStatusRequest) (*GetUserEKycStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserEKycStatus not implemented")
}
func (UnimplementedEKycCenterServiceServer) mustEmbedUnimplementedEKycCenterServiceServer() {}
func (UnimplementedEKycCenterServiceServer) testEmbeddedByValue()                           {}

// UnsafeEKycCenterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EKycCenterServiceServer will
// result in compilation errors.
type UnsafeEKycCenterServiceServer interface {
	mustEmbedUnimplementedEKycCenterServiceServer()
}

func RegisterEKycCenterServiceServer(s grpc.ServiceRegistrar, srv EKycCenterServiceServer) {
	// If the following call pancis, it indicates UnimplementedEKycCenterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EKycCenterService_ServiceDesc, srv)
}

func _EKycCenterService_GetUserEKycStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserEKycStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EKycCenterServiceServer).GetUserEKycStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EKycCenterService_GetUserEKycStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EKycCenterServiceServer).GetUserEKycStatus(ctx, req.(*GetUserEKycStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EKycCenterService_ServiceDesc is the grpc.ServiceDesc for EKycCenterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EKycCenterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ekyccenter.v2.EKycCenterService",
	HandlerType: (*EKycCenterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserEKycStatus",
			Handler:    _EKycCenterService_GetUserEKycStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/ekyc/ekyc.proto",
}
