// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/ekyc/ekyc.proto

package ekyccenterv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUserEKycStatusResponse_Status int32

const (
	GetUserEKycStatusResponse_STATUS_INVALID GetUserEKycStatusResponse_Status = 0
	GetUserEKycStatusResponse_PROCESSING     GetUserEKycStatusResponse_Status = 1
	GetUserEKycStatusResponse_APPROVE        GetUserEKycStatusResponse_Status = 2
	GetUserEKycStatusResponse_REJECT         GetUserEKycStatusResponse_Status = 3
)

// Enum value maps for GetUserEKycStatusResponse_Status.
var (
	GetUserEKycStatusResponse_Status_name = map[int32]string{
		0: "STATUS_INVALID",
		1: "PROCESSING",
		2: "APPROVE",
		3: "REJECT",
	}
	GetUserEKycStatusResponse_Status_value = map[string]int32{
		"STATUS_INVALID": 0,
		"PROCESSING":     1,
		"APPROVE":        2,
		"REJECT":         3,
	}
)

func (x GetUserEKycStatusResponse_Status) Enum() *GetUserEKycStatusResponse_Status {
	p := new(GetUserEKycStatusResponse_Status)
	*p = x
	return p
}

func (x GetUserEKycStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUserEKycStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_ekyc_ekyc_proto_enumTypes[0].Descriptor()
}

func (GetUserEKycStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_external_services_ekyc_ekyc_proto_enumTypes[0]
}

func (x GetUserEKycStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUserEKycStatusResponse_Status.Descriptor instead.
func (GetUserEKycStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_external_services_ekyc_ekyc_proto_rawDescGZIP(), []int{1, 0}
}

type GetUserEKycStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId  string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey string `protobuf:"bytes,2,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	UserId    string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetUserEKycStatusRequest) Reset() {
	*x = GetUserEKycStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ekyc_ekyc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserEKycStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserEKycStatusRequest) ProtoMessage() {}

func (x *GetUserEKycStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ekyc_ekyc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserEKycStatusRequest.ProtoReflect.Descriptor instead.
func (*GetUserEKycStatusRequest) Descriptor() ([]byte, []int) {
	return file_external_services_ekyc_ekyc_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserEKycStatusRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GetUserEKycStatusRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *GetUserEKycStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserEKycStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EkycId string                           `protobuf:"bytes,1,opt,name=ekyc_id,json=ekycId,proto3" json:"ekyc_id,omitempty"`
	Status GetUserEKycStatusResponse_Status `protobuf:"varint,2,opt,name=status,proto3,enum=ekyccenter.v2.GetUserEKycStatusResponse_Status" json:"status,omitempty"`
}

func (x *GetUserEKycStatusResponse) Reset() {
	*x = GetUserEKycStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_ekyc_ekyc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserEKycStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserEKycStatusResponse) ProtoMessage() {}

func (x *GetUserEKycStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_ekyc_ekyc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserEKycStatusResponse.ProtoReflect.Descriptor instead.
func (*GetUserEKycStatusResponse) Descriptor() ([]byte, []int) {
	return file_external_services_ekyc_ekyc_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserEKycStatusResponse) GetEkycId() string {
	if x != nil {
		return x.EkycId
	}
	return ""
}

func (x *GetUserEKycStatusResponse) GetStatus() GetUserEKycStatusResponse_Status {
	if x != nil {
		return x.Status
	}
	return GetUserEKycStatusResponse_STATUS_INVALID
}

var File_external_services_ekyc_ekyc_proto protoreflect.FileDescriptor

var file_external_services_ekyc_ekyc_proto_rawDesc = []byte{
	0x0a, 0x21, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x65, 0x6b, 0x79, 0x63, 0x2f, 0x65, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x65, 0x6b, 0x79, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x32, 0x22, 0x6f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x4b, 0x79,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45,
	0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x6b, 0x79, 0x63, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x65, 0x6b, 0x79,
	0x63, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x45, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x45, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0a,
	0x0a, 0x06, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x03, 0x32, 0x7b, 0x0a, 0x11, 0x45, 0x4b,
	0x79, 0x63, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x66, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x4b, 0x79, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x2e, 0x65, 0x6b, 0x79, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x4b, 0x79, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e,
	0x65, 0x6b, 0x79, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x13, 0x5a, 0x11, 0x65, 0x6b, 0x79, 0x63, 0x2f,
	0x65, 0x6b, 0x79, 0x63, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_ekyc_ekyc_proto_rawDescOnce sync.Once
	file_external_services_ekyc_ekyc_proto_rawDescData = file_external_services_ekyc_ekyc_proto_rawDesc
)

func file_external_services_ekyc_ekyc_proto_rawDescGZIP() []byte {
	file_external_services_ekyc_ekyc_proto_rawDescOnce.Do(func() {
		file_external_services_ekyc_ekyc_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_ekyc_ekyc_proto_rawDescData)
	})
	return file_external_services_ekyc_ekyc_proto_rawDescData
}

var file_external_services_ekyc_ekyc_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_external_services_ekyc_ekyc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_external_services_ekyc_ekyc_proto_goTypes = []any{
	(GetUserEKycStatusResponse_Status)(0), // 0: ekyccenter.v2.GetUserEKycStatusResponse.Status
	(*GetUserEKycStatusRequest)(nil),      // 1: ekyccenter.v2.GetUserEKycStatusRequest
	(*GetUserEKycStatusResponse)(nil),     // 2: ekyccenter.v2.GetUserEKycStatusResponse
}
var file_external_services_ekyc_ekyc_proto_depIdxs = []int32{
	0, // 0: ekyccenter.v2.GetUserEKycStatusResponse.status:type_name -> ekyccenter.v2.GetUserEKycStatusResponse.Status
	1, // 1: ekyccenter.v2.EKycCenterService.GetUserEKycStatus:input_type -> ekyccenter.v2.GetUserEKycStatusRequest
	2, // 2: ekyccenter.v2.EKycCenterService.GetUserEKycStatus:output_type -> ekyccenter.v2.GetUserEKycStatusResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_external_services_ekyc_ekyc_proto_init() }
func file_external_services_ekyc_ekyc_proto_init() {
	if File_external_services_ekyc_ekyc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_ekyc_ekyc_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserEKycStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_ekyc_ekyc_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserEKycStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_ekyc_ekyc_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_ekyc_ekyc_proto_goTypes,
		DependencyIndexes: file_external_services_ekyc_ekyc_proto_depIdxs,
		EnumInfos:         file_external_services_ekyc_ekyc_proto_enumTypes,
		MessageInfos:      file_external_services_ekyc_ekyc_proto_msgTypes,
	}.Build()
	File_external_services_ekyc_ekyc_proto = out.File
	file_external_services_ekyc_ekyc_proto_rawDesc = nil
	file_external_services_ekyc_ekyc_proto_goTypes = nil
	file_external_services_ekyc_ekyc_proto_depIdxs = nil
}
