// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/zp_base/zp_base.proto

package zalopay_grpc_client_protobuf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GrpcBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MethodName string            `protobuf:"bytes,1,opt,name=methodName,proto3" json:"methodName,omitempty"`
	Params     map[string]string `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GrpcBaseRequest) Reset() {
	*x = GrpcBaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_zp_base_zp_base_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrpcBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrpcBaseRequest) ProtoMessage() {}

func (x *GrpcBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_zp_base_zp_base_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrpcBaseRequest.ProtoReflect.Descriptor instead.
func (*GrpcBaseRequest) Descriptor() ([]byte, []int) {
	return file_external_services_zp_base_zp_base_proto_rawDescGZIP(), []int{0}
}

func (x *GrpcBaseRequest) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *GrpcBaseRequest) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

type GrpcBaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response string `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *GrpcBaseResponse) Reset() {
	*x = GrpcBaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_zp_base_zp_base_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrpcBaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrpcBaseResponse) ProtoMessage() {}

func (x *GrpcBaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_zp_base_zp_base_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrpcBaseResponse.ProtoReflect.Descriptor instead.
func (*GrpcBaseResponse) Descriptor() ([]byte, []int) {
	return file_external_services_zp_base_zp_base_proto_rawDescGZIP(), []int{1}
}

func (x *GrpcBaseResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

type NameValuePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NameValuePair) Reset() {
	*x = NameValuePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_zp_base_zp_base_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameValuePair) ProtoMessage() {}

func (x *NameValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_zp_base_zp_base_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameValuePair.ProtoReflect.Descriptor instead.
func (*NameValuePair) Descriptor() ([]byte, []int) {
	return file_external_services_zp_base_zp_base_proto_rawDescGZIP(), []int{2}
}

func (x *NameValuePair) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NameValuePair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GrpcBaseRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MethodName string           `protobuf:"bytes,1,opt,name=methodName,proto3" json:"methodName,omitempty"`
	Params     []*NameValuePair `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"`
}

func (x *GrpcBaseRequestV2) Reset() {
	*x = GrpcBaseRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_zp_base_zp_base_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrpcBaseRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrpcBaseRequestV2) ProtoMessage() {}

func (x *GrpcBaseRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_zp_base_zp_base_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrpcBaseRequestV2.ProtoReflect.Descriptor instead.
func (*GrpcBaseRequestV2) Descriptor() ([]byte, []int) {
	return file_external_services_zp_base_zp_base_proto_rawDescGZIP(), []int{3}
}

func (x *GrpcBaseRequestV2) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *GrpcBaseRequestV2) GetParams() []*NameValuePair {
	if x != nil {
		return x.Params
	}
	return nil
}

var File_external_services_zp_base_zp_base_proto protoreflect.FileDescriptor

var file_external_services_zp_base_zp_base_proto_rawDesc = []byte{
	0x0a, 0x27, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x7a, 0x70, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x7a, 0x70, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x22, 0xab, 0x01, 0x0a, 0x0f, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x2e, 0x0a, 0x10, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x39, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61,
	0x69, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x64, 0x0a, 0x11,
	0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x32, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x32, 0x9c, 0x01, 0x0a, 0x06, 0x5a, 0x50, 0x42, 0x61, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0b, 0x73, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x32, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x47,
	0x72, 0x70, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x42, 0x1e, 0x5a, 0x1c, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_zp_base_zp_base_proto_rawDescOnce sync.Once
	file_external_services_zp_base_zp_base_proto_rawDescData = file_external_services_zp_base_zp_base_proto_rawDesc
)

func file_external_services_zp_base_zp_base_proto_rawDescGZIP() []byte {
	file_external_services_zp_base_zp_base_proto_rawDescOnce.Do(func() {
		file_external_services_zp_base_zp_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_zp_base_zp_base_proto_rawDescData)
	})
	return file_external_services_zp_base_zp_base_proto_rawDescData
}

var file_external_services_zp_base_zp_base_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_external_services_zp_base_zp_base_proto_goTypes = []any{
	(*GrpcBaseRequest)(nil),   // 0: protobuf.GrpcBaseRequest
	(*GrpcBaseResponse)(nil),  // 1: protobuf.GrpcBaseResponse
	(*NameValuePair)(nil),     // 2: protobuf.NameValuePair
	(*GrpcBaseRequestV2)(nil), // 3: protobuf.GrpcBaseRequestV2
	nil,                       // 4: protobuf.GrpcBaseRequest.ParamsEntry
}
var file_external_services_zp_base_zp_base_proto_depIdxs = []int32{
	4, // 0: protobuf.GrpcBaseRequest.params:type_name -> protobuf.GrpcBaseRequest.ParamsEntry
	2, // 1: protobuf.GrpcBaseRequestV2.params:type_name -> protobuf.NameValuePair
	0, // 2: protobuf.ZPBase.sendRequest:input_type -> protobuf.GrpcBaseRequest
	3, // 3: protobuf.ZPBase.sendRequestV2:input_type -> protobuf.GrpcBaseRequestV2
	1, // 4: protobuf.ZPBase.sendRequest:output_type -> protobuf.GrpcBaseResponse
	1, // 5: protobuf.ZPBase.sendRequestV2:output_type -> protobuf.GrpcBaseResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_external_services_zp_base_zp_base_proto_init() }
func file_external_services_zp_base_zp_base_proto_init() {
	if File_external_services_zp_base_zp_base_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_zp_base_zp_base_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GrpcBaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_zp_base_zp_base_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GrpcBaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_zp_base_zp_base_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*NameValuePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_zp_base_zp_base_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GrpcBaseRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_zp_base_zp_base_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_zp_base_zp_base_proto_goTypes,
		DependencyIndexes: file_external_services_zp_base_zp_base_proto_depIdxs,
		MessageInfos:      file_external_services_zp_base_zp_base_proto_msgTypes,
	}.Build()
	File_external_services_zp_base_zp_base_proto = out.File
	file_external_services_zp_base_zp_base_proto_rawDesc = nil
	file_external_services_zp_base_zp_base_proto_goTypes = nil
	file_external_services_zp_base_zp_base_proto_depIdxs = nil
}
