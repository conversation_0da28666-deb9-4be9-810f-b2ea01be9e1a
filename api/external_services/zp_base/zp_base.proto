syntax = "proto3";

option go_package = "zalopay.grpc_client.protobuf";

package protobuf;

service ZPBase {
  rpc sendRequest (GrpcBaseRequest) returns (GrpcBaseResponse) {}
  rpc sendRequestV2 (GrpcBaseRequestV2) returns (GrpcBaseResponse) {}
}

message GrpcBaseRequest {
  string methodName = 1;
  map<string, string> params = 2;
}

message GrpcBaseResponse {
  string response = 1;
}

message NameValuePair {
  string name = 1;
  string value = 2;
}

message GrpcBaseRequestV2 {
  string methodName = 1;
  repeated NameValuePair params = 2;
}