// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: external_services/zp_base/zp_base.proto

package zalopay_grpc_client_protobuf

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockZPBaseClient is a mock of ZPBaseClient interface.
type MockZPBaseClient struct {
	ctrl     *gomock.Controller
	recorder *MockZPBaseClientMockRecorder
}

// MockZPBaseClientMockRecorder is the mock recorder for MockZPBaseClient.
type MockZPBaseClientMockRecorder struct {
	mock *MockZPBaseClient
}

// NewMockZPBaseClient creates a new mock instance.
func NewMockZPBaseClient(ctrl *gomock.Controller) *MockZPBaseClient {
	mock := &MockZPBaseClient{ctrl: ctrl}
	mock.recorder = &MockZPBaseClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZPBaseClient) EXPECT() *MockZPBaseClientMockRecorder {
	return m.recorder
}

// SendRequest mocks base method.
func (m *MockZPBaseClient) SendRequest(ctx context.Context, in *GrpcBaseRequest, opts ...grpc.CallOption) (*GrpcBaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendRequest", varargs...)
	ret0, _ := ret[0].(*GrpcBaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRequest indicates an expected call of SendRequest.
func (mr *MockZPBaseClientMockRecorder) SendRequest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRequest", reflect.TypeOf((*MockZPBaseClient)(nil).SendRequest), varargs...)
}

// SendRequestV2 mocks base method.
func (m *MockZPBaseClient) SendRequestV2(ctx context.Context, in *GrpcBaseRequestV2, opts ...grpc.CallOption) (*GrpcBaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendRequestV2", varargs...)
	ret0, _ := ret[0].(*GrpcBaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRequestV2 indicates an expected call of SendRequestV2.
func (mr *MockZPBaseClientMockRecorder) SendRequestV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRequestV2", reflect.TypeOf((*MockZPBaseClient)(nil).SendRequestV2), varargs...)
}

// MockZPBaseServer is a mock of ZPBaseServer interface.
type MockZPBaseServer struct {
	ctrl     *gomock.Controller
	recorder *MockZPBaseServerMockRecorder
}

// MockZPBaseServerMockRecorder is the mock recorder for MockZPBaseServer.
type MockZPBaseServerMockRecorder struct {
	mock *MockZPBaseServer
}

// NewMockZPBaseServer creates a new mock instance.
func NewMockZPBaseServer(ctrl *gomock.Controller) *MockZPBaseServer {
	mock := &MockZPBaseServer{ctrl: ctrl}
	mock.recorder = &MockZPBaseServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZPBaseServer) EXPECT() *MockZPBaseServerMockRecorder {
	return m.recorder
}

// SendRequest mocks base method.
func (m *MockZPBaseServer) SendRequest(ctx context.Context, in *GrpcBaseRequest) (*GrpcBaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRequest", ctx, in)
	ret0, _ := ret[0].(*GrpcBaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRequest indicates an expected call of SendRequest.
func (mr *MockZPBaseServerMockRecorder) SendRequest(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRequest", reflect.TypeOf((*MockZPBaseServer)(nil).SendRequest), ctx, in)
}

// SendRequestV2 mocks base method.
func (m *MockZPBaseServer) SendRequestV2(ctx context.Context, in *GrpcBaseRequestV2) (*GrpcBaseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRequestV2", ctx, in)
	ret0, _ := ret[0].(*GrpcBaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRequestV2 indicates an expected call of SendRequestV2.
func (mr *MockZPBaseServerMockRecorder) SendRequestV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRequestV2", reflect.TypeOf((*MockZPBaseServer)(nil).SendRequestV2), ctx, in)
}
