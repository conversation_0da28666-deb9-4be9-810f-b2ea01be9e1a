// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/zp_base/zp_base.proto

package zalopay_grpc_client_protobuf

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ZPBase_SendRequest_FullMethodName   = "/protobuf.ZPBase/sendRequest"
	ZPBase_SendRequestV2_FullMethodName = "/protobuf.ZPBase/sendRequestV2"
)

// ZPBaseClient is the client API for ZPBase service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ZPBaseClient interface {
	SendRequest(ctx context.Context, in *GrpcBaseRequest, opts ...grpc.CallOption) (*GrpcBaseResponse, error)
	SendRequestV2(ctx context.Context, in *GrpcBaseRequestV2, opts ...grpc.CallOption) (*GrpcBaseResponse, error)
}

type zPBaseClient struct {
	cc grpc.ClientConnInterface
}

func NewZPBaseClient(cc grpc.ClientConnInterface) ZPBaseClient {
	return &zPBaseClient{cc}
}

func (c *zPBaseClient) SendRequest(ctx context.Context, in *GrpcBaseRequest, opts ...grpc.CallOption) (*GrpcBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GrpcBaseResponse)
	err := c.cc.Invoke(ctx, ZPBase_SendRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zPBaseClient) SendRequestV2(ctx context.Context, in *GrpcBaseRequestV2, opts ...grpc.CallOption) (*GrpcBaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GrpcBaseResponse)
	err := c.cc.Invoke(ctx, ZPBase_SendRequestV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ZPBaseServer is the server API for ZPBase service.
// All implementations must embed UnimplementedZPBaseServer
// for forward compatibility.
type ZPBaseServer interface {
	SendRequest(context.Context, *GrpcBaseRequest) (*GrpcBaseResponse, error)
	SendRequestV2(context.Context, *GrpcBaseRequestV2) (*GrpcBaseResponse, error)
	mustEmbedUnimplementedZPBaseServer()
}

// UnimplementedZPBaseServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedZPBaseServer struct{}

func (UnimplementedZPBaseServer) SendRequest(context.Context, *GrpcBaseRequest) (*GrpcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRequest not implemented")
}
func (UnimplementedZPBaseServer) SendRequestV2(context.Context, *GrpcBaseRequestV2) (*GrpcBaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRequestV2 not implemented")
}
func (UnimplementedZPBaseServer) mustEmbedUnimplementedZPBaseServer() {}
func (UnimplementedZPBaseServer) testEmbeddedByValue()                {}

// UnsafeZPBaseServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ZPBaseServer will
// result in compilation errors.
type UnsafeZPBaseServer interface {
	mustEmbedUnimplementedZPBaseServer()
}

func RegisterZPBaseServer(s grpc.ServiceRegistrar, srv ZPBaseServer) {
	// If the following call pancis, it indicates UnimplementedZPBaseServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ZPBase_ServiceDesc, srv)
}

func _ZPBase_SendRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrpcBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZPBaseServer).SendRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ZPBase_SendRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZPBaseServer).SendRequest(ctx, req.(*GrpcBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ZPBase_SendRequestV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrpcBaseRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZPBaseServer).SendRequestV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ZPBase_SendRequestV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZPBaseServer).SendRequestV2(ctx, req.(*GrpcBaseRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

// ZPBase_ServiceDesc is the grpc.ServiceDesc for ZPBase service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ZPBase_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "protobuf.ZPBase",
	HandlerType: (*ZPBaseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "sendRequest",
			Handler:    _ZPBase_SendRequest_Handler,
		},
		{
			MethodName: "sendRequestV2",
			Handler:    _ZPBase_SendRequestV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/zp_base/zp_base.proto",
}
