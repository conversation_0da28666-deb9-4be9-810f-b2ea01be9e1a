// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: external_services/user_profile/user_profile.proto

package user_profile_grpc_client

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUserProfileClient is a mock of UserProfileClient interface.
type MockUserProfileClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserProfileClientMockRecorder
}

// MockUserProfileClientMockRecorder is the mock recorder for MockUserProfileClient.
type MockUserProfileClientMockRecorder struct {
	mock *MockUserProfileClient
}

// NewMockUserProfileClient creates a new mock instance.
func NewMockUserProfileClient(ctrl *gomock.Controller) *MockUserProfileClient {
	mock := &MockUserProfileClient{ctrl: ctrl}
	mock.recorder = &MockUserProfileClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserProfileClient) EXPECT() *MockUserProfileClientMockRecorder {
	return m.recorder
}

// QueryByPhone mocks base method.
func (m *MockUserProfileClient) QueryByPhone(ctx context.Context, in *QueryByPhoneRequest, opts ...grpc.CallOption) (*Profile, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByPhone", varargs...)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByPhone indicates an expected call of QueryByPhone.
func (mr *MockUserProfileClientMockRecorder) QueryByPhone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByPhone", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByPhone), varargs...)
}

// QueryByUserId mocks base method.
func (m *MockUserProfileClient) QueryByUserId(ctx context.Context, in *QueryByUserIdRequest, opts ...grpc.CallOption) (*Profile, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByUserId", varargs...)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByUserId indicates an expected call of QueryByUserId.
func (mr *MockUserProfileClientMockRecorder) QueryByUserId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByUserId", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByUserId), varargs...)
}

// QueryByZaloId mocks base method.
func (m *MockUserProfileClient) QueryByZaloId(ctx context.Context, in *QueryByZaloIdRequest, opts ...grpc.CallOption) (*Profile, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryByZaloId", varargs...)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByZaloId indicates an expected call of QueryByZaloId.
func (mr *MockUserProfileClientMockRecorder) QueryByZaloId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByZaloId", reflect.TypeOf((*MockUserProfileClient)(nil).QueryByZaloId), varargs...)
}

// VerifyPin mocks base method.
func (m *MockUserProfileClient) VerifyPin(ctx context.Context, in *VerifyPinRequest, opts ...grpc.CallOption) (*VerifyPinResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyPin", varargs...)
	ret0, _ := ret[0].(*VerifyPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPin indicates an expected call of VerifyPin.
func (mr *MockUserProfileClientMockRecorder) VerifyPin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPin", reflect.TypeOf((*MockUserProfileClient)(nil).VerifyPin), varargs...)
}

// MockUserProfileServer is a mock of UserProfileServer interface.
type MockUserProfileServer struct {
	ctrl     *gomock.Controller
	recorder *MockUserProfileServerMockRecorder
}

// MockUserProfileServerMockRecorder is the mock recorder for MockUserProfileServer.
type MockUserProfileServerMockRecorder struct {
	mock *MockUserProfileServer
}

// NewMockUserProfileServer creates a new mock instance.
func NewMockUserProfileServer(ctrl *gomock.Controller) *MockUserProfileServer {
	mock := &MockUserProfileServer{ctrl: ctrl}
	mock.recorder = &MockUserProfileServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserProfileServer) EXPECT() *MockUserProfileServerMockRecorder {
	return m.recorder
}

// QueryByPhone mocks base method.
func (m *MockUserProfileServer) QueryByPhone(ctx context.Context, in *QueryByPhoneRequest) (*Profile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByPhone", ctx, in)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByPhone indicates an expected call of QueryByPhone.
func (mr *MockUserProfileServerMockRecorder) QueryByPhone(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByPhone", reflect.TypeOf((*MockUserProfileServer)(nil).QueryByPhone), ctx, in)
}

// QueryByUserId mocks base method.
func (m *MockUserProfileServer) QueryByUserId(ctx context.Context, in *QueryByUserIdRequest) (*Profile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByUserId", ctx, in)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByUserId indicates an expected call of QueryByUserId.
func (mr *MockUserProfileServerMockRecorder) QueryByUserId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByUserId", reflect.TypeOf((*MockUserProfileServer)(nil).QueryByUserId), ctx, in)
}

// QueryByZaloId mocks base method.
func (m *MockUserProfileServer) QueryByZaloId(ctx context.Context, in *QueryByZaloIdRequest) (*Profile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryByZaloId", ctx, in)
	ret0, _ := ret[0].(*Profile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryByZaloId indicates an expected call of QueryByZaloId.
func (mr *MockUserProfileServerMockRecorder) QueryByZaloId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryByZaloId", reflect.TypeOf((*MockUserProfileServer)(nil).QueryByZaloId), ctx, in)
}

// VerifyPin mocks base method.
func (m *MockUserProfileServer) VerifyPin(ctx context.Context, in *VerifyPinRequest) (*VerifyPinResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyPin", ctx, in)
	ret0, _ := ret[0].(*VerifyPinResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPin indicates an expected call of VerifyPin.
func (mr *MockUserProfileServerMockRecorder) VerifyPin(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPin", reflect.TypeOf((*MockUserProfileServer)(nil).VerifyPin), ctx, in)
}
