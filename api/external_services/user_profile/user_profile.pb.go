// https://confluence.zalopay.vn/x/TSSuBg

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.3
// source: external_services/user_profile/user_profile.proto

package user_profile_grpc_client

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IdentityProfile_NfcStatus int32

const (
	IdentityProfile_NFC_STATUS_UNSPECIFIED IdentityProfile_NfcStatus = 0
	IdentityProfile_NFC_STATUS_DETECTED    IdentityProfile_NfcStatus = 1
	IdentityProfile_NFC_STATUS_VERIFIED    IdentityProfile_NfcStatus = 2
)

// Enum value maps for IdentityProfile_NfcStatus.
var (
	IdentityProfile_NfcStatus_name = map[int32]string{
		0: "NFC_STATUS_UNSPECIFIED",
		1: "NFC_STATUS_DETECTED",
		2: "NFC_STATUS_VERIFIED",
	}
	IdentityProfile_NfcStatus_value = map[string]int32{
		"NFC_STATUS_UNSPECIFIED": 0,
		"NFC_STATUS_DETECTED":    1,
		"NFC_STATUS_VERIFIED":    2,
	}
)

func (x IdentityProfile_NfcStatus) Enum() *IdentityProfile_NfcStatus {
	p := new(IdentityProfile_NfcStatus)
	*p = x
	return p
}

func (x IdentityProfile_NfcStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityProfile_NfcStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_external_services_user_profile_user_profile_proto_enumTypes[0].Descriptor()
}

func (IdentityProfile_NfcStatus) Type() protoreflect.EnumType {
	return &file_external_services_user_profile_user_profile_proto_enumTypes[0]
}

func (x IdentityProfile_NfcStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityProfile_NfcStatus.Descriptor instead.
func (IdentityProfile_NfcStatus) EnumDescriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{5, 0}
}

// Chỉ bật cờ ứng với data cần query để tránh ảnh hưởng performance
type QueryByPhoneRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneNumber     int64 `protobuf:"varint,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	BasicProfile    bool  `protobuf:"varint,2,opt,name=basic_profile,json=basicProfile,proto3" json:"basic_profile,omitempty"`
	IdentityProfile bool  `protobuf:"varint,3,opt,name=identity_profile,json=identityProfile,proto3" json:"identity_profile,omitempty"`
	LockStatus      bool  `protobuf:"varint,4,opt,name=lock_status,json=lockStatus,proto3" json:"lock_status,omitempty"`
	PinStatus       bool  `protobuf:"varint,5,opt,name=pin_status,json=pinStatus,proto3" json:"pin_status,omitempty"`
	KycProfile      bool  `protobuf:"varint,6,opt,name=kyc_profile,json=kycProfile,proto3" json:"kyc_profile,omitempty"`
	RefProfile      bool  `protobuf:"varint,7,opt,name=ref_profile,json=refProfile,proto3" json:"ref_profile,omitempty"`
}

func (x *QueryByPhoneRequest) Reset() {
	*x = QueryByPhoneRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryByPhoneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryByPhoneRequest) ProtoMessage() {}

func (x *QueryByPhoneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryByPhoneRequest.ProtoReflect.Descriptor instead.
func (*QueryByPhoneRequest) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{0}
}

func (x *QueryByPhoneRequest) GetPhoneNumber() int64 {
	if x != nil {
		return x.PhoneNumber
	}
	return 0
}

func (x *QueryByPhoneRequest) GetBasicProfile() bool {
	if x != nil {
		return x.BasicProfile
	}
	return false
}

func (x *QueryByPhoneRequest) GetIdentityProfile() bool {
	if x != nil {
		return x.IdentityProfile
	}
	return false
}

func (x *QueryByPhoneRequest) GetLockStatus() bool {
	if x != nil {
		return x.LockStatus
	}
	return false
}

func (x *QueryByPhoneRequest) GetPinStatus() bool {
	if x != nil {
		return x.PinStatus
	}
	return false
}

func (x *QueryByPhoneRequest) GetKycProfile() bool {
	if x != nil {
		return x.KycProfile
	}
	return false
}

func (x *QueryByPhoneRequest) GetRefProfile() bool {
	if x != nil {
		return x.RefProfile
	}
	return false
}

type QueryByUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BasicProfile    bool   `protobuf:"varint,2,opt,name=basic_profile,json=basicProfile,proto3" json:"basic_profile,omitempty"`
	IdentityProfile bool   `protobuf:"varint,3,opt,name=identity_profile,json=identityProfile,proto3" json:"identity_profile,omitempty"`
	LockStatus      bool   `protobuf:"varint,4,opt,name=lock_status,json=lockStatus,proto3" json:"lock_status,omitempty"`
	PinStatus       bool   `protobuf:"varint,5,opt,name=pin_status,json=pinStatus,proto3" json:"pin_status,omitempty"`
	KycProfile      bool   `protobuf:"varint,6,opt,name=kyc_profile,json=kycProfile,proto3" json:"kyc_profile,omitempty"`
	RefProfile      bool   `protobuf:"varint,7,opt,name=ref_profile,json=refProfile,proto3" json:"ref_profile,omitempty"`
	FdProfile       bool   `protobuf:"varint,8,opt,name=fd_profile,json=fdProfile,proto3" json:"fd_profile,omitempty"`
	MerchantProfile bool   `protobuf:"varint,9,opt,name=merchant_profile,json=merchantProfile,proto3" json:"merchant_profile,omitempty"` // Bật flag này thì phải check null basic_profile/merchant_profile để đảm bảo tồn tại user/merchant hay không
}

func (x *QueryByUserIdRequest) Reset() {
	*x = QueryByUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryByUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryByUserIdRequest) ProtoMessage() {}

func (x *QueryByUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryByUserIdRequest.ProtoReflect.Descriptor instead.
func (*QueryByUserIdRequest) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{1}
}

func (x *QueryByUserIdRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *QueryByUserIdRequest) GetBasicProfile() bool {
	if x != nil {
		return x.BasicProfile
	}
	return false
}

func (x *QueryByUserIdRequest) GetIdentityProfile() bool {
	if x != nil {
		return x.IdentityProfile
	}
	return false
}

func (x *QueryByUserIdRequest) GetLockStatus() bool {
	if x != nil {
		return x.LockStatus
	}
	return false
}

func (x *QueryByUserIdRequest) GetPinStatus() bool {
	if x != nil {
		return x.PinStatus
	}
	return false
}

func (x *QueryByUserIdRequest) GetKycProfile() bool {
	if x != nil {
		return x.KycProfile
	}
	return false
}

func (x *QueryByUserIdRequest) GetRefProfile() bool {
	if x != nil {
		return x.RefProfile
	}
	return false
}

func (x *QueryByUserIdRequest) GetFdProfile() bool {
	if x != nil {
		return x.FdProfile
	}
	return false
}

func (x *QueryByUserIdRequest) GetMerchantProfile() bool {
	if x != nil {
		return x.MerchantProfile
	}
	return false
}

type QueryByZaloIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZaloId          string `protobuf:"bytes,1,opt,name=zalo_id,json=zaloId,proto3" json:"zalo_id,omitempty"`
	BasicProfile    bool   `protobuf:"varint,2,opt,name=basic_profile,json=basicProfile,proto3" json:"basic_profile,omitempty"`
	IdentityProfile bool   `protobuf:"varint,3,opt,name=identity_profile,json=identityProfile,proto3" json:"identity_profile,omitempty"`
	LockStatus      bool   `protobuf:"varint,4,opt,name=lock_status,json=lockStatus,proto3" json:"lock_status,omitempty"`
	PinStatus       bool   `protobuf:"varint,5,opt,name=pin_status,json=pinStatus,proto3" json:"pin_status,omitempty"`
	KycProfile      bool   `protobuf:"varint,6,opt,name=kyc_profile,json=kycProfile,proto3" json:"kyc_profile,omitempty"`
	RefProfile      bool   `protobuf:"varint,7,opt,name=ref_profile,json=refProfile,proto3" json:"ref_profile,omitempty"`
}

func (x *QueryByZaloIdRequest) Reset() {
	*x = QueryByZaloIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryByZaloIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryByZaloIdRequest) ProtoMessage() {}

func (x *QueryByZaloIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryByZaloIdRequest.ProtoReflect.Descriptor instead.
func (*QueryByZaloIdRequest) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{2}
}

func (x *QueryByZaloIdRequest) GetZaloId() string {
	if x != nil {
		return x.ZaloId
	}
	return ""
}

func (x *QueryByZaloIdRequest) GetBasicProfile() bool {
	if x != nil {
		return x.BasicProfile
	}
	return false
}

func (x *QueryByZaloIdRequest) GetIdentityProfile() bool {
	if x != nil {
		return x.IdentityProfile
	}
	return false
}

func (x *QueryByZaloIdRequest) GetLockStatus() bool {
	if x != nil {
		return x.LockStatus
	}
	return false
}

func (x *QueryByZaloIdRequest) GetPinStatus() bool {
	if x != nil {
		return x.PinStatus
	}
	return false
}

func (x *QueryByZaloIdRequest) GetKycProfile() bool {
	if x != nil {
		return x.KycProfile
	}
	return false
}

func (x *QueryByZaloIdRequest) GetRefProfile() bool {
	if x != nil {
		return x.RefProfile
	}
	return false
}

type Profile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicProfile    *BasicProfile    `protobuf:"bytes,1,opt,name=basic_profile,json=basicProfile,proto3" json:"basic_profile,omitempty"`
	IdentityProfile *IdentityProfile `protobuf:"bytes,2,opt,name=identity_profile,json=identityProfile,proto3" json:"identity_profile,omitempty"`
	LockStatus      *LockStatus      `protobuf:"bytes,3,opt,name=lock_status,json=lockStatus,proto3" json:"lock_status,omitempty"`
	PinStatus       *PinStatus       `protobuf:"bytes,4,opt,name=pin_status,json=pinStatus,proto3" json:"pin_status,omitempty"`
	KycProfile      *KycProfile      `protobuf:"bytes,5,opt,name=kyc_profile,json=kycProfile,proto3" json:"kyc_profile,omitempty"`
	RefProfile      *RefProfile      `protobuf:"bytes,6,opt,name=ref_profile,json=refProfile,proto3" json:"ref_profile,omitempty"`
	FdProfile       *FdProfile       `protobuf:"bytes,7,opt,name=fd_profile,json=fdProfile,proto3" json:"fd_profile,omitempty"`
	MerchantProfile *MerchantProfile `protobuf:"bytes,8,opt,name=merchant_profile,json=merchantProfile,proto3" json:"merchant_profile,omitempty"`
}

func (x *Profile) Reset() {
	*x = Profile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{3}
}

func (x *Profile) GetBasicProfile() *BasicProfile {
	if x != nil {
		return x.BasicProfile
	}
	return nil
}

func (x *Profile) GetIdentityProfile() *IdentityProfile {
	if x != nil {
		return x.IdentityProfile
	}
	return nil
}

func (x *Profile) GetLockStatus() *LockStatus {
	if x != nil {
		return x.LockStatus
	}
	return nil
}

func (x *Profile) GetPinStatus() *PinStatus {
	if x != nil {
		return x.PinStatus
	}
	return nil
}

func (x *Profile) GetKycProfile() *KycProfile {
	if x != nil {
		return x.KycProfile
	}
	return nil
}

func (x *Profile) GetRefProfile() *RefProfile {
	if x != nil {
		return x.RefProfile
	}
	return nil
}

func (x *Profile) GetFdProfile() *FdProfile {
	if x != nil {
		return x.FdProfile
	}
	return nil
}

func (x *Profile) GetMerchantProfile() *MerchantProfile {
	if x != nil {
		return x.MerchantProfile
	}
	return nil
}

type BasicProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId            string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ZaloId            string `protobuf:"bytes,2,opt,name=zalo_id,json=zaloId,proto3" json:"zalo_id,omitempty"`
	PhoneNumber       int64  `protobuf:"varint,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email             string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Avatar            string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	DisplayName       string `protobuf:"bytes,6,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	ProfileFlag       int32  `protobuf:"varint,7,opt,name=profile_flag,json=profileFlag,proto3" json:"profile_flag,omitempty"`
	CurrentAddress    string `protobuf:"bytes,8,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	Occupation        string `protobuf:"bytes,9,opt,name=occupation,proto3" json:"occupation,omitempty"`
	Position          string `protobuf:"bytes,10,opt,name=position,proto3" json:"position,omitempty"`
	ZalopayAvatar     string `protobuf:"bytes,11,opt,name=zalopay_avatar,json=zalopayAvatar,proto3" json:"zalopay_avatar,omitempty"`
	ZalopayName       string `protobuf:"bytes,12,opt,name=zalopay_name,json=zalopayName,proto3" json:"zalopay_name,omitempty"`
	WrapZalopayAvatar string `protobuf:"bytes,13,opt,name=wrap_zalopay_avatar,json=wrapZalopayAvatar,proto3" json:"wrap_zalopay_avatar,omitempty"`
	WrapZalopayName   string `protobuf:"bytes,14,opt,name=wrap_zalopay_name,json=wrapZalopayName,proto3" json:"wrap_zalopay_name,omitempty"`
	CurrentCity       string `protobuf:"bytes,15,opt,name=current_city,json=currentCity,proto3" json:"current_city,omitempty"`
	CurrentDistrict   string `protobuf:"bytes,16,opt,name=current_district,json=currentDistrict,proto3" json:"current_district,omitempty"`
	CurrentWard       string `protobuf:"bytes,17,opt,name=current_ward,json=currentWard,proto3" json:"current_ward,omitempty"`
	Sme               bool   `protobuf:"varint,18,opt,name=sme,proto3" json:"sme,omitempty"`
}

func (x *BasicProfile) Reset() {
	*x = BasicProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicProfile) ProtoMessage() {}

func (x *BasicProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicProfile.ProtoReflect.Descriptor instead.
func (*BasicProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{4}
}

func (x *BasicProfile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BasicProfile) GetZaloId() string {
	if x != nil {
		return x.ZaloId
	}
	return ""
}

func (x *BasicProfile) GetPhoneNumber() int64 {
	if x != nil {
		return x.PhoneNumber
	}
	return 0
}

func (x *BasicProfile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BasicProfile) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *BasicProfile) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *BasicProfile) GetProfileFlag() int32 {
	if x != nil {
		return x.ProfileFlag
	}
	return 0
}

func (x *BasicProfile) GetCurrentAddress() string {
	if x != nil {
		return x.CurrentAddress
	}
	return ""
}

func (x *BasicProfile) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *BasicProfile) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *BasicProfile) GetZalopayAvatar() string {
	if x != nil {
		return x.ZalopayAvatar
	}
	return ""
}

func (x *BasicProfile) GetZalopayName() string {
	if x != nil {
		return x.ZalopayName
	}
	return ""
}

func (x *BasicProfile) GetWrapZalopayAvatar() string {
	if x != nil {
		return x.WrapZalopayAvatar
	}
	return ""
}

func (x *BasicProfile) GetWrapZalopayName() string {
	if x != nil {
		return x.WrapZalopayName
	}
	return ""
}

func (x *BasicProfile) GetCurrentCity() string {
	if x != nil {
		return x.CurrentCity
	}
	return ""
}

func (x *BasicProfile) GetCurrentDistrict() string {
	if x != nil {
		return x.CurrentDistrict
	}
	return ""
}

func (x *BasicProfile) GetCurrentWard() string {
	if x != nil {
		return x.CurrentWard
	}
	return ""
}

func (x *BasicProfile) GetSme() bool {
	if x != nil {
		return x.Sme
	}
	return false
}

type IdentityProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Approved         bool                      `protobuf:"varint,1,opt,name=approved,proto3" json:"approved,omitempty"` // Cờ này = true thì user đã định danh (kyc-ed), và ngược lại
	FullName         string                    `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Birthday         int64                     `protobuf:"varint,3,opt,name=birthday,proto3" json:"birthday,omitempty"`
	Gender           int32                     `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	IdNumber         string                    `protobuf:"bytes,5,opt,name=id_number,json=idNumber,proto3" json:"id_number,omitempty"`
	IdType           int32                     `protobuf:"varint,6,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"` // 1: CMND, 2: passport, 3: CCCD, 4: CMSQ, 5: CCCD gắn chip
	CountryCode      string                    `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Source           int32                     `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	AvatarImageUri   string                    `protobuf:"bytes,9,opt,name=avatar_image_uri,json=avatarImageUri,proto3" json:"avatar_image_uri,omitempty"`
	FrontImageUri    string                    `protobuf:"bytes,10,opt,name=front_image_uri,json=frontImageUri,proto3" json:"front_image_uri,omitempty"`
	BackImageUri     string                    `protobuf:"bytes,11,opt,name=back_image_uri,json=backImageUri,proto3" json:"back_image_uri,omitempty"`
	IssueDate        int64                     `protobuf:"varint,12,opt,name=issue_date,json=issueDate,proto3" json:"issue_date,omitempty"`
	IssuePlace       string                    `protobuf:"bytes,13,opt,name=issue_place,json=issuePlace,proto3" json:"issue_place,omitempty"`
	IssuePlaceText   string                    `protobuf:"bytes,14,opt,name=issue_place_text,json=issuePlaceText,proto3" json:"issue_place_text,omitempty"`
	NativePlace      string                    `protobuf:"bytes,15,opt,name=native_place,json=nativePlace,proto3" json:"native_place,omitempty"`
	ExpirationDate   int64                     `protobuf:"varint,16,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	Birthplace       string                    `protobuf:"bytes,17,opt,name=birthplace,proto3" json:"birthplace,omitempty"`
	PermanentAddress string                    `protobuf:"bytes,18,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	KycUpdateDate    int64                     `protobuf:"varint,19,opt,name=kyc_update_date,json=kycUpdateDate,proto3" json:"kyc_update_date,omitempty"`
	BirthdayYmd      *Ymd                      `protobuf:"bytes,20,opt,name=birthday_ymd,json=birthdayYmd,proto3" json:"birthday_ymd,omitempty"`
	IssueYmd         *Ymd                      `protobuf:"bytes,21,opt,name=issue_ymd,json=issueYmd,proto3" json:"issue_ymd,omitempty"`
	ExpirationYmd    *Ymd                      `protobuf:"bytes,22,opt,name=expiration_ymd,json=expirationYmd,proto3" json:"expiration_ymd,omitempty"`
	NfcStatus        IdentityProfile_NfcStatus `protobuf:"varint,23,opt,name=nfc_status,json=nfcStatus,proto3,enum=user_profile.v1.IdentityProfile_NfcStatus" json:"nfc_status,omitempty"`
}

func (x *IdentityProfile) Reset() {
	*x = IdentityProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityProfile) ProtoMessage() {}

func (x *IdentityProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityProfile.ProtoReflect.Descriptor instead.
func (*IdentityProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{5}
}

func (x *IdentityProfile) GetApproved() bool {
	if x != nil {
		return x.Approved
	}
	return false
}

func (x *IdentityProfile) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *IdentityProfile) GetBirthday() int64 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *IdentityProfile) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *IdentityProfile) GetIdNumber() string {
	if x != nil {
		return x.IdNumber
	}
	return ""
}

func (x *IdentityProfile) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

func (x *IdentityProfile) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *IdentityProfile) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *IdentityProfile) GetAvatarImageUri() string {
	if x != nil {
		return x.AvatarImageUri
	}
	return ""
}

func (x *IdentityProfile) GetFrontImageUri() string {
	if x != nil {
		return x.FrontImageUri
	}
	return ""
}

func (x *IdentityProfile) GetBackImageUri() string {
	if x != nil {
		return x.BackImageUri
	}
	return ""
}

func (x *IdentityProfile) GetIssueDate() int64 {
	if x != nil {
		return x.IssueDate
	}
	return 0
}

func (x *IdentityProfile) GetIssuePlace() string {
	if x != nil {
		return x.IssuePlace
	}
	return ""
}

func (x *IdentityProfile) GetIssuePlaceText() string {
	if x != nil {
		return x.IssuePlaceText
	}
	return ""
}

func (x *IdentityProfile) GetNativePlace() string {
	if x != nil {
		return x.NativePlace
	}
	return ""
}

func (x *IdentityProfile) GetExpirationDate() int64 {
	if x != nil {
		return x.ExpirationDate
	}
	return 0
}

func (x *IdentityProfile) GetBirthplace() string {
	if x != nil {
		return x.Birthplace
	}
	return ""
}

func (x *IdentityProfile) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

func (x *IdentityProfile) GetKycUpdateDate() int64 {
	if x != nil {
		return x.KycUpdateDate
	}
	return 0
}

func (x *IdentityProfile) GetBirthdayYmd() *Ymd {
	if x != nil {
		return x.BirthdayYmd
	}
	return nil
}

func (x *IdentityProfile) GetIssueYmd() *Ymd {
	if x != nil {
		return x.IssueYmd
	}
	return nil
}

func (x *IdentityProfile) GetExpirationYmd() *Ymd {
	if x != nil {
		return x.ExpirationYmd
	}
	return nil
}

func (x *IdentityProfile) GetNfcStatus() IdentityProfile_NfcStatus {
	if x != nil {
		return x.NfcStatus
	}
	return IdentityProfile_NFC_STATUS_UNSPECIFIED
}

type RefProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email             string             `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	NativePlace       string             `protobuf:"bytes,3,opt,name=native_place,json=nativePlace,proto3" json:"native_place,omitempty"`
	CurrentAddress    string             `protobuf:"bytes,4,opt,name=current_address,json=currentAddress,proto3" json:"current_address,omitempty"`
	CurrentCity       string             `protobuf:"bytes,5,opt,name=current_city,json=currentCity,proto3" json:"current_city,omitempty"`
	CurrentDistrict   string             `protobuf:"bytes,6,opt,name=current_district,json=currentDistrict,proto3" json:"current_district,omitempty"`
	CurrentWard       string             `protobuf:"bytes,7,opt,name=current_ward,json=currentWard,proto3" json:"current_ward,omitempty"`
	PermanentAddress  string             `protobuf:"bytes,8,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	PermanentCity     string             `protobuf:"bytes,9,opt,name=permanent_city,json=permanentCity,proto3" json:"permanent_city,omitempty"`
	PermanentDistrict string             `protobuf:"bytes,10,opt,name=permanent_district,json=permanentDistrict,proto3" json:"permanent_district,omitempty"`
	PermanentWard     string             `protobuf:"bytes,11,opt,name=permanent_ward,json=permanentWard,proto3" json:"permanent_ward,omitempty"`
	EmploymentStatus  string             `protobuf:"bytes,12,opt,name=employment_status,json=employmentStatus,proto3" json:"employment_status,omitempty"`
	Occupation        string             `protobuf:"bytes,13,opt,name=occupation,proto3" json:"occupation,omitempty"`
	Position          string             `protobuf:"bytes,14,opt,name=position,proto3" json:"position,omitempty"`
	Income            int64              `protobuf:"varint,15,opt,name=income,proto3" json:"income,omitempty"`
	Company           string             `protobuf:"bytes,16,opt,name=company,proto3" json:"company,omitempty"`
	PaymentMethod     string             `protobuf:"bytes,17,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	Fatca             bool               `protobuf:"varint,18,opt,name=fatca,proto3" json:"fatca,omitempty"`
	EducationStatus   string             `protobuf:"bytes,19,opt,name=education_status,json=educationStatus,proto3" json:"education_status,omitempty"`
	MarriedStatus     string             `protobuf:"bytes,20,opt,name=married_status,json=marriedStatus,proto3" json:"married_status,omitempty"`
	Name              string             `protobuf:"bytes,21,opt,name=name,proto3" json:"name,omitempty"`
	Dob               int64              `protobuf:"varint,22,opt,name=dob,proto3" json:"dob,omitempty"`
	Gender            int32              `protobuf:"varint,23,opt,name=gender,proto3" json:"gender,omitempty"`
	Source            int32              `protobuf:"varint,24,opt,name=source,proto3" json:"source,omitempty"`
	Relations         []*RelationProfile `protobuf:"bytes,25,rep,name=relations,proto3" json:"relations,omitempty"`
}

func (x *RefProfile) Reset() {
	*x = RefProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefProfile) ProtoMessage() {}

func (x *RefProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefProfile.ProtoReflect.Descriptor instead.
func (*RefProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{6}
}

func (x *RefProfile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RefProfile) GetNativePlace() string {
	if x != nil {
		return x.NativePlace
	}
	return ""
}

func (x *RefProfile) GetCurrentAddress() string {
	if x != nil {
		return x.CurrentAddress
	}
	return ""
}

func (x *RefProfile) GetCurrentCity() string {
	if x != nil {
		return x.CurrentCity
	}
	return ""
}

func (x *RefProfile) GetCurrentDistrict() string {
	if x != nil {
		return x.CurrentDistrict
	}
	return ""
}

func (x *RefProfile) GetCurrentWard() string {
	if x != nil {
		return x.CurrentWard
	}
	return ""
}

func (x *RefProfile) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

func (x *RefProfile) GetPermanentCity() string {
	if x != nil {
		return x.PermanentCity
	}
	return ""
}

func (x *RefProfile) GetPermanentDistrict() string {
	if x != nil {
		return x.PermanentDistrict
	}
	return ""
}

func (x *RefProfile) GetPermanentWard() string {
	if x != nil {
		return x.PermanentWard
	}
	return ""
}

func (x *RefProfile) GetEmploymentStatus() string {
	if x != nil {
		return x.EmploymentStatus
	}
	return ""
}

func (x *RefProfile) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *RefProfile) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *RefProfile) GetIncome() int64 {
	if x != nil {
		return x.Income
	}
	return 0
}

func (x *RefProfile) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *RefProfile) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *RefProfile) GetFatca() bool {
	if x != nil {
		return x.Fatca
	}
	return false
}

func (x *RefProfile) GetEducationStatus() string {
	if x != nil {
		return x.EducationStatus
	}
	return ""
}

func (x *RefProfile) GetMarriedStatus() string {
	if x != nil {
		return x.MarriedStatus
	}
	return ""
}

func (x *RefProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RefProfile) GetDob() int64 {
	if x != nil {
		return x.Dob
	}
	return 0
}

func (x *RefProfile) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *RefProfile) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *RefProfile) GetRelations() []*RelationProfile {
	if x != nil {
		return x.Relations
	}
	return nil
}

type RelationProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Phone       int64  `protobuf:"varint,3,opt,name=phone,proto3" json:"phone,omitempty"`
}

func (x *RelationProfile) Reset() {
	*x = RelationProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationProfile) ProtoMessage() {}

func (x *RelationProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationProfile.ProtoReflect.Descriptor instead.
func (*RelationProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{7}
}

func (x *RelationProfile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RelationProfile) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RelationProfile) GetPhone() int64 {
	if x != nil {
		return x.Phone
	}
	return 0
}

type Ymd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   int32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *Ymd) Reset() {
	*x = Ymd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ymd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ymd) ProtoMessage() {}

func (x *Ymd) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ymd.ProtoReflect.Descriptor instead.
func (*Ymd) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{8}
}

func (x *Ymd) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *Ymd) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *Ymd) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type LockStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Locked      bool   `protobuf:"varint,1,opt,name=locked,proto3" json:"locked,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Time        int64  `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	ClientId    string `protobuf:"bytes,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *LockStatus) Reset() {
	*x = LockStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LockStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockStatus) ProtoMessage() {}

func (x *LockStatus) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockStatus.ProtoReflect.Descriptor instead.
func (*LockStatus) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{9}
}

func (x *LockStatus) GetLocked() bool {
	if x != nil {
		return x.Locked
	}
	return false
}

func (x *LockStatus) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LockStatus) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *LockStatus) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

type PinStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasPin       bool  `protobuf:"varint,1,opt,name=has_pin,json=hasPin,proto3" json:"has_pin,omitempty"`
	WrongTimes   int32 `protobuf:"varint,2,opt,name=wrong_times,json=wrongTimes,proto3" json:"wrong_times,omitempty"`
	AttemptsLeft int32 `protobuf:"varint,3,opt,name=attempts_left,json=attemptsLeft,proto3" json:"attempts_left,omitempty"`
}

func (x *PinStatus) Reset() {
	*x = PinStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinStatus) ProtoMessage() {}

func (x *PinStatus) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinStatus.ProtoReflect.Descriptor instead.
func (*PinStatus) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{10}
}

func (x *PinStatus) GetHasPin() bool {
	if x != nil {
		return x.HasPin
	}
	return false
}

func (x *PinStatus) GetWrongTimes() int32 {
	if x != nil {
		return x.WrongTimes
	}
	return 0
}

func (x *PinStatus) GetAttemptsLeft() int32 {
	if x != nil {
		return x.AttemptsLeft
	}
	return 0
}

type KycProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level       int32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	BindingBank bool  `protobuf:"varint,2,opt,name=binding_bank,json=bindingBank,proto3" json:"binding_bank,omitempty"`
}

func (x *KycProfile) Reset() {
	*x = KycProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycProfile) ProtoMessage() {}

func (x *KycProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycProfile.ProtoReflect.Descriptor instead.
func (*KycProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{11}
}

func (x *KycProfile) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *KycProfile) GetBindingBank() bool {
	if x != nil {
		return x.BindingBank
	}
	return false
}

type VerifyPinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pin    string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
}

func (x *VerifyPinRequest) Reset() {
	*x = VerifyPinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPinRequest) ProtoMessage() {}

func (x *VerifyPinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPinRequest.ProtoReflect.Descriptor instead.
func (*VerifyPinRequest) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{12}
}

func (x *VerifyPinRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *VerifyPinRequest) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

type VerifyPinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReturnCode    int32  `protobuf:"varint,1,opt,name=return_code,json=returnCode,proto3" json:"return_code,omitempty"`
	ReturnMessage string `protobuf:"bytes,2,opt,name=return_message,json=returnMessage,proto3" json:"return_message,omitempty"`
}

func (x *VerifyPinResponse) Reset() {
	*x = VerifyPinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPinResponse) ProtoMessage() {}

func (x *VerifyPinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPinResponse.ProtoReflect.Descriptor instead.
func (*VerifyPinResponse) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{13}
}

func (x *VerifyPinResponse) GetReturnCode() int32 {
	if x != nil {
		return x.ReturnCode
	}
	return 0
}

func (x *VerifyPinResponse) GetReturnMessage() string {
	if x != nil {
		return x.ReturnMessage
	}
	return ""
}

type FdProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindingFd bool `protobuf:"varint,1,opt,name=binding_fd,json=bindingFd,proto3" json:"binding_fd,omitempty"`
}

func (x *FdProfile) Reset() {
	*x = FdProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FdProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FdProfile) ProtoMessage() {}

func (x *FdProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FdProfile.ProtoReflect.Descriptor instead.
func (*FdProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{14}
}

func (x *FdProfile) GetBindingFd() bool {
	if x != nil {
		return x.BindingFd
	}
	return false
}

type MerchantProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Avatar       string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	DisplayName  string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	MerchantId   int32  `protobuf:"varint,4,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	MerchantName string `protobuf:"bytes,5,opt,name=merchantName,proto3" json:"merchantName,omitempty"`
}

func (x *MerchantProfile) Reset() {
	*x = MerchantProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_external_services_user_profile_user_profile_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProfile) ProtoMessage() {}

func (x *MerchantProfile) ProtoReflect() protoreflect.Message {
	mi := &file_external_services_user_profile_user_profile_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProfile.ProtoReflect.Descriptor instead.
func (*MerchantProfile) Descriptor() ([]byte, []int) {
	return file_external_services_user_profile_user_profile_proto_rawDescGZIP(), []int{15}
}

func (x *MerchantProfile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MerchantProfile) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *MerchantProfile) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *MerchantProfile) GetMerchantId() int32 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MerchantProfile) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

var File_external_services_user_profile_user_profile_proto protoreflect.FileDescriptor

var file_external_services_user_profile_user_profile_proto_rawDesc = []byte{
	0x0a, 0x31, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x22, 0x8a, 0x02, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x22, 0xcb, 0x02, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x66, 0x64, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22,
	0x81, 0x02, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x5a, 0x61, 0x6c, 0x6f, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x7a, 0x61, 0x6c, 0x6f,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x7a, 0x61, 0x6c, 0x6f, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x22, 0x97, 0x04, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x42, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x3c, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09,
	0x70, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6b, 0x79, 0x63,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x0a, 0x6b, 0x79, 0x63,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x66, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x64, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x09, 0x66, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x4b, 0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x0f, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0xe5, 0x04,
	0x0a, 0x0c, 0x42, 0x61, 0x73, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x7a, 0x61, 0x6c, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x7a, 0x61, 0x6c, 0x6f, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e,
	0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x7a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x7a, 0x61, 0x6c, 0x6f, 0x70,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x72, 0x61, 0x70, 0x5f, 0x7a,
	0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x72, 0x61, 0x70, 0x5a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79,
	0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x77, 0x72, 0x61, 0x70, 0x5f, 0x7a,
	0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x77, 0x72, 0x61, 0x70, 0x5a, 0x61, 0x6c, 0x6f, 0x70, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x61, 0x72, 0x64,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x57,
	0x61, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x03, 0x73, 0x6d, 0x65, 0x22, 0xe1, 0x07, 0x0a, 0x0f, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x64, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x69, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x69, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x69, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x69, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x79, 0x63, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6b, 0x79, 0x63,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0c, 0x62, 0x69,
	0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x5f, 0x79, 0x6d, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x59, 0x6d, 0x64, 0x52, 0x0b, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x59, 0x6d, 0x64, 0x12, 0x31, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x79, 0x6d, 0x64,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x6d, 0x64, 0x52, 0x08, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x59, 0x6d, 0x64, 0x12, 0x3b, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x6d, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x59, 0x6d, 0x64, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x59, 0x6d, 0x64, 0x12, 0x49, 0x0a, 0x0a, 0x6e, 0x66, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4e, 0x66, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x6e, 0x66, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x59,
	0x0a, 0x09, 0x4e, 0x66, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x4e,
	0x46, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x46, 0x43, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x4e, 0x46, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x02, 0x22, 0xc9, 0x06, 0x0a, 0x0a, 0x52, 0x65,
	0x66, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a,
	0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x77, 0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x57, 0x61, 0x72, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x70,
	0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d,
	0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x69, 0x74, 0x79, 0x12,
	0x2d, 0x0a, 0x12, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x61, 0x72, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x57, 0x61, 0x72, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x61, 0x74, 0x63, 0x61,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x61, 0x74, 0x63, 0x61, 0x12, 0x29, 0x0a,
	0x10, 0x65, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x61, 0x72, 0x72, 0x69, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x5d, 0x0a, 0x0f, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x22, 0x41, 0x0a, 0x03, 0x59, 0x6d, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0x77, 0x0a, 0x0a, 0x4c, 0x6f, 0x63, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x6a, 0x0a, 0x09, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x68, 0x61, 0x73, 0x50, 0x69, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x72, 0x6f, 0x6e, 0x67, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x77, 0x72, 0x6f,
	0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x4c, 0x65, 0x66, 0x74, 0x22, 0x45, 0x0a, 0x0a,
	0x4b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x21, 0x0a, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6e, 0x6b, 0x22, 0x3d, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x69, 0x6e, 0x22, 0x5b, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x2a, 0x0a, 0x09, 0x46, 0x64, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x64, 0x22, 0xaa, 0x01, 0x0a, 0x0f,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0xdd, 0x02, 0x0a, 0x0b, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x00, 0x12, 0x52,
	0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x5a, 0x61, 0x6c, 0x6f, 0x49, 0x64, 0x12,
	0x25, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x5a, 0x61, 0x6c, 0x6f, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x22, 0x00, 0x12, 0x54, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x69, 0x6e, 0x12,
	0x21, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x1a, 0x5a, 0x18, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_external_services_user_profile_user_profile_proto_rawDescOnce sync.Once
	file_external_services_user_profile_user_profile_proto_rawDescData = file_external_services_user_profile_user_profile_proto_rawDesc
)

func file_external_services_user_profile_user_profile_proto_rawDescGZIP() []byte {
	file_external_services_user_profile_user_profile_proto_rawDescOnce.Do(func() {
		file_external_services_user_profile_user_profile_proto_rawDescData = protoimpl.X.CompressGZIP(file_external_services_user_profile_user_profile_proto_rawDescData)
	})
	return file_external_services_user_profile_user_profile_proto_rawDescData
}

var file_external_services_user_profile_user_profile_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_external_services_user_profile_user_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_external_services_user_profile_user_profile_proto_goTypes = []any{
	(IdentityProfile_NfcStatus)(0), // 0: user_profile.v1.IdentityProfile.NfcStatus
	(*QueryByPhoneRequest)(nil),    // 1: user_profile.v1.QueryByPhoneRequest
	(*QueryByUserIdRequest)(nil),   // 2: user_profile.v1.QueryByUserIdRequest
	(*QueryByZaloIdRequest)(nil),   // 3: user_profile.v1.QueryByZaloIdRequest
	(*Profile)(nil),                // 4: user_profile.v1.Profile
	(*BasicProfile)(nil),           // 5: user_profile.v1.BasicProfile
	(*IdentityProfile)(nil),        // 6: user_profile.v1.IdentityProfile
	(*RefProfile)(nil),             // 7: user_profile.v1.RefProfile
	(*RelationProfile)(nil),        // 8: user_profile.v1.RelationProfile
	(*Ymd)(nil),                    // 9: user_profile.v1.Ymd
	(*LockStatus)(nil),             // 10: user_profile.v1.LockStatus
	(*PinStatus)(nil),              // 11: user_profile.v1.PinStatus
	(*KycProfile)(nil),             // 12: user_profile.v1.KycProfile
	(*VerifyPinRequest)(nil),       // 13: user_profile.v1.VerifyPinRequest
	(*VerifyPinResponse)(nil),      // 14: user_profile.v1.VerifyPinResponse
	(*FdProfile)(nil),              // 15: user_profile.v1.FdProfile
	(*MerchantProfile)(nil),        // 16: user_profile.v1.MerchantProfile
}
var file_external_services_user_profile_user_profile_proto_depIdxs = []int32{
	5,  // 0: user_profile.v1.Profile.basic_profile:type_name -> user_profile.v1.BasicProfile
	6,  // 1: user_profile.v1.Profile.identity_profile:type_name -> user_profile.v1.IdentityProfile
	10, // 2: user_profile.v1.Profile.lock_status:type_name -> user_profile.v1.LockStatus
	11, // 3: user_profile.v1.Profile.pin_status:type_name -> user_profile.v1.PinStatus
	12, // 4: user_profile.v1.Profile.kyc_profile:type_name -> user_profile.v1.KycProfile
	7,  // 5: user_profile.v1.Profile.ref_profile:type_name -> user_profile.v1.RefProfile
	15, // 6: user_profile.v1.Profile.fd_profile:type_name -> user_profile.v1.FdProfile
	16, // 7: user_profile.v1.Profile.merchant_profile:type_name -> user_profile.v1.MerchantProfile
	9,  // 8: user_profile.v1.IdentityProfile.birthday_ymd:type_name -> user_profile.v1.Ymd
	9,  // 9: user_profile.v1.IdentityProfile.issue_ymd:type_name -> user_profile.v1.Ymd
	9,  // 10: user_profile.v1.IdentityProfile.expiration_ymd:type_name -> user_profile.v1.Ymd
	0,  // 11: user_profile.v1.IdentityProfile.nfc_status:type_name -> user_profile.v1.IdentityProfile.NfcStatus
	8,  // 12: user_profile.v1.RefProfile.relations:type_name -> user_profile.v1.RelationProfile
	1,  // 13: user_profile.v1.UserProfile.QueryByPhone:input_type -> user_profile.v1.QueryByPhoneRequest
	2,  // 14: user_profile.v1.UserProfile.QueryByUserId:input_type -> user_profile.v1.QueryByUserIdRequest
	3,  // 15: user_profile.v1.UserProfile.QueryByZaloId:input_type -> user_profile.v1.QueryByZaloIdRequest
	13, // 16: user_profile.v1.UserProfile.VerifyPin:input_type -> user_profile.v1.VerifyPinRequest
	4,  // 17: user_profile.v1.UserProfile.QueryByPhone:output_type -> user_profile.v1.Profile
	4,  // 18: user_profile.v1.UserProfile.QueryByUserId:output_type -> user_profile.v1.Profile
	4,  // 19: user_profile.v1.UserProfile.QueryByZaloId:output_type -> user_profile.v1.Profile
	14, // 20: user_profile.v1.UserProfile.VerifyPin:output_type -> user_profile.v1.VerifyPinResponse
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_external_services_user_profile_user_profile_proto_init() }
func file_external_services_user_profile_user_profile_proto_init() {
	if File_external_services_user_profile_user_profile_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_external_services_user_profile_user_profile_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*QueryByPhoneRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*QueryByUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*QueryByZaloIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Profile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*BasicProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*IdentityProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*RefProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*RelationProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Ymd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*LockStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*PinStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*KycProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*VerifyPinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*VerifyPinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*FdProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_external_services_user_profile_user_profile_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*MerchantProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_external_services_user_profile_user_profile_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_external_services_user_profile_user_profile_proto_goTypes,
		DependencyIndexes: file_external_services_user_profile_user_profile_proto_depIdxs,
		EnumInfos:         file_external_services_user_profile_user_profile_proto_enumTypes,
		MessageInfos:      file_external_services_user_profile_user_profile_proto_msgTypes,
	}.Build()
	File_external_services_user_profile_user_profile_proto = out.File
	file_external_services_user_profile_user_profile_proto_rawDesc = nil
	file_external_services_user_profile_user_profile_proto_goTypes = nil
	file_external_services_user_profile_user_profile_proto_depIdxs = nil
}
