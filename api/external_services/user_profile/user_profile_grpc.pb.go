// https://confluence.zalopay.vn/x/TSSuBg

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: external_services/user_profile/user_profile.proto

package user_profile_grpc_client

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserProfile_QueryByPhone_FullMethodName  = "/user_profile.v1.UserProfile/QueryByPhone"
	UserProfile_QueryByUserId_FullMethodName = "/user_profile.v1.UserProfile/QueryByUserId"
	UserProfile_QueryByZaloId_FullMethodName = "/user_profile.v1.UserProfile/QueryByZaloId"
	UserProfile_VerifyPin_FullMethodName     = "/user_profile.v1.UserProfile/VerifyPin"
)

// UserProfileClient is the client API for UserProfile service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserProfileClient interface {
	QueryByPhone(ctx context.Context, in *QueryByPhoneRequest, opts ...grpc.CallOption) (*Profile, error)
	QueryByUserId(ctx context.Context, in *QueryByUserIdRequest, opts ...grpc.CallOption) (*Profile, error)
	QueryByZaloId(ctx context.Context, in *QueryByZaloIdRequest, opts ...grpc.CallOption) (*Profile, error)
	VerifyPin(ctx context.Context, in *VerifyPinRequest, opts ...grpc.CallOption) (*VerifyPinResponse, error)
}

type userProfileClient struct {
	cc grpc.ClientConnInterface
}

func NewUserProfileClient(cc grpc.ClientConnInterface) UserProfileClient {
	return &userProfileClient{cc}
}

func (c *userProfileClient) QueryByPhone(ctx context.Context, in *QueryByPhoneRequest, opts ...grpc.CallOption) (*Profile, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Profile)
	err := c.cc.Invoke(ctx, UserProfile_QueryByPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userProfileClient) QueryByUserId(ctx context.Context, in *QueryByUserIdRequest, opts ...grpc.CallOption) (*Profile, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Profile)
	err := c.cc.Invoke(ctx, UserProfile_QueryByUserId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userProfileClient) QueryByZaloId(ctx context.Context, in *QueryByZaloIdRequest, opts ...grpc.CallOption) (*Profile, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Profile)
	err := c.cc.Invoke(ctx, UserProfile_QueryByZaloId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userProfileClient) VerifyPin(ctx context.Context, in *VerifyPinRequest, opts ...grpc.CallOption) (*VerifyPinResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyPinResponse)
	err := c.cc.Invoke(ctx, UserProfile_VerifyPin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserProfileServer is the server API for UserProfile service.
// All implementations must embed UnimplementedUserProfileServer
// for forward compatibility.
type UserProfileServer interface {
	QueryByPhone(context.Context, *QueryByPhoneRequest) (*Profile, error)
	QueryByUserId(context.Context, *QueryByUserIdRequest) (*Profile, error)
	QueryByZaloId(context.Context, *QueryByZaloIdRequest) (*Profile, error)
	VerifyPin(context.Context, *VerifyPinRequest) (*VerifyPinResponse, error)
	mustEmbedUnimplementedUserProfileServer()
}

// UnimplementedUserProfileServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserProfileServer struct{}

func (UnimplementedUserProfileServer) QueryByPhone(context.Context, *QueryByPhoneRequest) (*Profile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByPhone not implemented")
}
func (UnimplementedUserProfileServer) QueryByUserId(context.Context, *QueryByUserIdRequest) (*Profile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByUserId not implemented")
}
func (UnimplementedUserProfileServer) QueryByZaloId(context.Context, *QueryByZaloIdRequest) (*Profile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryByZaloId not implemented")
}
func (UnimplementedUserProfileServer) VerifyPin(context.Context, *VerifyPinRequest) (*VerifyPinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyPin not implemented")
}
func (UnimplementedUserProfileServer) mustEmbedUnimplementedUserProfileServer() {}
func (UnimplementedUserProfileServer) testEmbeddedByValue()                     {}

// UnsafeUserProfileServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserProfileServer will
// result in compilation errors.
type UnsafeUserProfileServer interface {
	mustEmbedUnimplementedUserProfileServer()
}

func RegisterUserProfileServer(s grpc.ServiceRegistrar, srv UserProfileServer) {
	// If the following call pancis, it indicates UnimplementedUserProfileServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserProfile_ServiceDesc, srv)
}

func _UserProfile_QueryByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByPhoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserProfileServer).QueryByPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserProfile_QueryByPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserProfileServer).QueryByPhone(ctx, req.(*QueryByPhoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserProfile_QueryByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserProfileServer).QueryByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserProfile_QueryByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserProfileServer).QueryByUserId(ctx, req.(*QueryByUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserProfile_QueryByZaloId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryByZaloIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserProfileServer).QueryByZaloId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserProfile_QueryByZaloId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserProfileServer).QueryByZaloId(ctx, req.(*QueryByZaloIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserProfile_VerifyPin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyPinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserProfileServer).VerifyPin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserProfile_VerifyPin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserProfileServer).VerifyPin(ctx, req.(*VerifyPinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserProfile_ServiceDesc is the grpc.ServiceDesc for UserProfile service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserProfile_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user_profile.v1.UserProfile",
	HandlerType: (*UserProfileServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryByPhone",
			Handler:    _UserProfile_QueryByPhone_Handler,
		},
		{
			MethodName: "QueryByUserId",
			Handler:    _UserProfile_QueryByUserId_Handler,
		},
		{
			MethodName: "QueryByZaloId",
			Handler:    _UserProfile_QueryByZaloId_Handler,
		},
		{
			MethodName: "VerifyPin",
			Handler:    _UserProfile_VerifyPin_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "external_services/user_profile/user_profile.proto",
}
