account:
  app:
    env: "dev"
    name: "account"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.account.database.driver }}
      source: {{ fin.installment.account.database.source }}
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: {{ fin.installment.redis.address }}
      username: {{ fin.installment.redis.username }}
      password: {{ fin.installment.redis.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    onboarding_service:
      address: {{ fin.installment.adapters.onboarding_service.address }}
      secured: {{ fin.installment.adapters.onboarding_service.secured }}
      timeout: 30s
    management_service:
      address: {{ fin.installment.adapters.management_service.address }}
      secured: {{ fin.installment.adapters.management_service.secured }}
      timeout: 30s
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
    risk_system:
      address: {{ fin.installment.adapters.risk_system.address }}
      secured: {{ fin.installment.adapters.risk_system.secured }}
    crm_event_pub:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "qc.fin.installment.crm.general"
  schedulers:
    sync_account_balance:
      queue_name: "account.syncing.common"
      workflow_type: "SyncAccountBalanceWorkflow"
    polling_account_balance:
      queue_name: "account.syncing.common"
      workflow_type: "PollingAccountBalanceWorkflow"
    sync_cimb_debt_accounts_balance:
      queue_name: "account.syncing.common"
      workflow_type: "SyncCIMBDebtAccountsBalanceWorkflow"
  resources:
    service_file_path: '/apps/resources/services/services.json'
    deeplinks:
      default:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialdev.zalopay.vn/spa/v2/installment"
      application:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialdev.zalopay.vn/spa/v2/installment"
  tracing:
    enabled: true
    service_name: fin.installment.account
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: debug
    encoding: json
    stack_trace: true

onboarding:
  app:
    env: "dev"
    name: "onboarding"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.onboarding.database.driver }}
      source: {{ fin.installment.onboarding.database.source }}
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: {{ fin.installment.redis.address }}
      username: {{ fin.installment.redis.username }}
      password: {{ fin.installment.redis.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
    s3_storage:
      host: {{ fin.installment.s3.host }}
      region: {{ fin.installment.s3.region }}
      bucket_name: {{ fin.installment.s3.bucket_name }}
      access_key: {{ fin.installment.s3.access_key }}
      secret_key: {{ fin.installment.s3.secret_key }}
      use_proxy: {{ fin.installment.s3.use_proxy }}
      proxy_url: {{ fin.installment.s3.proxy_url }}
      enable_ssl: true
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    user_profile:
      address: {{ fin.installment.adapters.user_profile.address }}
      secured: {{ fin.installment.adapters.user_profile.secured }}
      client_id: {{ fin.installment.adapters.user_profile.client_id }}
      client_key: {{ fin.installment.adapters.user_profile.client_key }}
      timeout: 30s
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    ekyc_center:
      address: {{ fin.installment.adapters.ekyc_center.address }}
      secured: {{ fin.installment.adapters.ekyc_center.secured }}
      client_id: {{ fin.installment.adapters.ekyc_center.client_id }}
      client_key: {{ fin.installment.adapters.ekyc_center.client_key }}
    risk_system:
      address: {{ fin.installment.adapters.risk_system.address }}
      secured: {{ fin.installment.adapters.risk_system.secured }}
    credit_score:
      address: {{ fin.installment.adapters.credit_score.address }}
      secured: {{ fin.installment.adapters.credit_score.secured }}
    kyc_image:
      base_url: {{ fin.installment.adapters.ekyc_image.base_url }}
      timeout: 10s
      retries: 3
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
    face_auth:
      address: {{ fin.installment.adapters.face_auth.address }}
      secured: {{ fin.installment.adapters.face_auth.secured }}
      client_id: {{ fin.installment.adapters.face_auth.client_id }}
      client_key: {{ fin.installment.adapters.face_auth.client_key }}
    ekyc_nfc:
      address: {{ fin.installment.adapters.ekyc_nfc.address }}
      secured: {{ fin.installment.adapters.ekyc_nfc.secured }}
      client_id: {{ fin.installment.adapters.ekyc_nfc.client_id }}
      client_key: {{ fin.installment.adapters.ekyc_nfc.client_key }}
    http_common:
      base_url: ""
      proxy_url: "http://**********:8088"
      timeout: 10s
      retries: 3
    ab_platform:
      address: {{ fin.installment.adapters.ab_platform.address }}
      secured: {{ fin.installment.adapters.ab_platform.secured }}
  schedulers:
    onboarding_status:
      queue_name: "onboarding.common"
      workflow_type: "PollingOnboardingStatusWorkflow"
      max_time_retry: 2592000s # 30 days
    submit_face_image:
      queue_name: "onboarding.contract"
      workflow_type: "UploadFaceImageWorkflow"
      time_interval: 60s
      max_time_retry: 86400s
      max_time_interval: 300s
  tracing:
    enabled: true
    service_name: fin.installment.onboarding
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: debug
    encoding: json
    stack_trace: true
  fraud_risk:
    hash_key: {{ fin.installment.fraud_risk.hash_key }}
  credit_score:
    hash_key: {{ fin.installment.credit_score.hash_key }}
  whitelists:
    onboarding:
      enabled: false
      experiment_key: "UG7_1111"
      whitelist_group: "Variation 1"
  user_challenge:
    face_source_id: {{ fin.installment.user_challenge.face_source_id }}
    face_expired_in: {{ fin.installment.user_challenge.face_expired_in }}

management:
  app:
    env: "dev"
    name: "management"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.management.database.driver }}
      source: {{ fin.installment.management.database.source }}
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: {{ fin.installment.redis.address }}
      username: {{ fin.installment.redis.username }}
      password: {{ fin.installment.redis.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
  schedulers:
    sync_stmts_periodic:
      queue_name: "statement.syncing.common"
      workflow_type: "SyncStatementsPeriodicWorkflow"
    sync_stmts_batch_proc:
      queue_name: "statement.syncing.process"
      workflow_type: "StatementSyncBatchProcessingWorkflow"
    create_installment_loan:
      queue_name: "installment.common"
      workflow_type: "CreateInstallmentLoanWorkflow"
  resources:
    fee_file_path: '/apps/resources/fee/fee.json'
    deeplinks:
      default:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialdev.zalopay.vn/spa/v2/installment"
      plan_detail:
        zpa_url: "https://zlpqc-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
        zpi_url: "https://zlpqc-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
        common_url: "https://zlpqc-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
      plan_detail_mf:
        common_url: https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/947ecaf8-eb9c-4d38-b64b-10db0cf6f4a3?mf_scope=installment_external_app&mf_module=./InstallmentPlanDetail
  crypto_sets:
    plan_encryption:
      key: {{ fin.installment.crypto.plan_encryption.key }}
      iv: {{ fin.installment.crypto.plan_encryption.iv }}
  tracing:
    enabled: true
    service_name: fin.installment.management
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: debug
