zombie:
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
      
account:
  app:
    env: "stg"
    name: "account"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.account.database.driver }}
      source: >
        {{ private-cicd.zalopay.mysql.148.user.1039.username }}:{{ private-cicd.zalopay.mysql.148.user.1039.password }}
        @tcp({{ private-cicd.zalopay.mysql.148.host }}:{{ private-cicd.zalopay.mysql.148.port }})
        /fin_installment_account_stg?parseTime=true&loc=Local
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: "{{ private-cicd.zalopay.redis.host }}:{{ private-cicd.zalopay.redis.port }}"
      username:
      password: {{ private-cicd.zalopay.redis.1000107.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    onboarding_service:
      address: {{ fin.installment.adapters.onboarding_service.address }}
      secured: {{ fin.installment.adapters.onboarding_service.secured }}
      timeout: 30s
    management_service:
      address: {{ fin.installment.adapters.management_service.address }}
      secured: {{ fin.installment.adapters.management_service.secured }}
      timeout: 30s
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
    risk_system:
      address: {{ fin.installment.adapters.risk_system.address }}
      secured: {{ fin.installment.adapters.risk_system.secured }}
    crm_event_pub:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
      topic: "stg.fin.installment.crm.general"
  schedulers:
    sync_account_balance:
      queue_name: "account.syncing.common"
      workflow_type: "SyncAccountBalanceWorkflow"
    polling_account_balance:
      queue_name: "account.syncing.common"
      workflow_type: "PollingAccountBalanceWorkflow"
    sync_cimb_account_balances:
      queue_name: "account.syncing.common"
      workflow_type: "SyncCIMBAccountBalancesWorkflow"
    sync_cimb_debt_account_balances:
      queue_name: "account.syncing.common"
      workflow_type: "SyncCIMBDebtAccountBalancesWorkflow"
  resources:
    service_file_path: '/apps/resources/services/services.json'
    deeplinks:
      default:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialstg.zalopay.vn/spa/v2/installment"
      application:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialstg.zalopay.vn/spa/v2/installment"
  fraud_risk:
    hash_key: {{ fin.installment.fraud_risk.hash_key }}
  tracing:
    enabled: true
    service_name: fin.installment.account
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: info
    encoding: json
    stack_trace: true

onboarding:
  app:
    env: "stg"
    name: "onboarding"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.onboarding.database.driver }}
      source: >
        {{ private-cicd.zalopay.mysql.148.user.1039.username }}:{{ private-cicd.zalopay.mysql.148.user.1039.password }}
        @tcp({{ private-cicd.zalopay.mysql.148.host }}:{{ private-cicd.zalopay.mysql.148.port }})
        /fin_installment_onboarding_stg?parseTime=true&loc=Local
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: "{{ private-cicd.zalopay.redis.host }}:{{ private-cicd.zalopay.redis.port }}"
      username:
      password: {{ private-cicd.zalopay.redis.1000107.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
    s3_storage:
      host: {{ fin.installment.s3.host }}
      region: {{ fin.installment.s3.region }}
      bucket_name: {{ fin.installment.s3.bucket_name }}
      access_key: {{ private-cicd.fin.installment.s3.access_key }}
      secret_key: {{ private-cicd.fin.installment.s3.secret_key }}
      use_proxy: {{ fin.installment.s3.use_proxy }}
      proxy_url: {{ fin.installment.s3.proxy_url }}
      enable_ssl: true
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    user_profile:
      address: {{ fin.installment.adapters.user_profile.address }}
      secured: {{ fin.installment.adapters.user_profile.secured }}
      client_id: {{ fin.installment.adapters.user_profile.client_id }}
      client_key: {{ fin.installment.adapters.user_profile.client_key }}
      timeout: 30s
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    ekyc_center:
      address: {{ fin.installment.adapters.ekyc_center.address }}
      secured: {{ fin.installment.adapters.ekyc_center.secured }}
      client_id: {{ fin.installment.adapters.ekyc_center.client_id }}
      client_key: {{ fin.installment.adapters.ekyc_center.client_key }}
    risk_system:
      address: {{ fin.installment.adapters.risk_system.address }}
      secured: {{ fin.installment.adapters.risk_system.secured }}
    credit_score:
      address: {{ fin.installment.adapters.credit_score.address }}
      secured: {{ fin.installment.adapters.credit_score.secured }}
    kyc_image:
      base_url: {{ fin.installment.adapters.ekyc_image.base_url }}
      timeout: 10s
      retries: 3
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
    face_auth:
      address: {{ fin.installment.adapters.face_auth.address }}
      secured: {{ fin.installment.adapters.face_auth.secured }}
      client_id: {{ fin.installment.adapters.face_auth.client_id }}
      client_key: {{ fin.installment.adapters.face_auth.client_key }}
    ekyc_nfc:
      address: {{ fin.installment.adapters.ekyc_nfc.address }}
      secured: {{ fin.installment.adapters.ekyc_nfc.secured }}
      client_id: {{ fin.installment.adapters.ekyc_nfc.client_id }}
      client_key: {{ fin.installment.adapters.ekyc_nfc.client_key }}
    http_common:
      base_url: ""
      proxy_url: "http://**********:8088"
      timeout: 10s
      retries: 3
    ab_platform:
      address: {{ fin.installment.adapters.ab_platform.address }}
      secured: {{ fin.installment.adapters.ab_platform.secured }}
  schedulers:
    onboarding_status:
      queue_name: "onboarding.common"
      workflow_type: "PollingOnboardingStatusWorkflow"
      max_time_retry: 2592000s # 30 days
    submit_face_image:
      queue_name: "onboarding.contract"
      workflow_type: "UploadFaceImageWorkflow"
      time_interval: 60s
      max_time_retry: 86400s
      max_time_interval: 300s
    contract_signing:
      queue_name: "onboarding.contract"
      workflow_type: "ContractSigningWorkflow"
      time_interval: 30s
      max_time_retry: 86400s
      max_time_interval: 150s
  tracing:
    enabled: true
    service_name: fin.installment.onboarding
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: info
    encoding: json
    stack_trace: true
  fraud_risk:
    hash_key: {{ fin.installment.fraud_risk.hash_key }}
  credit_score:
    hash_key: {{ fin.installment.credit_score.hash_key }}
  whitelists:
    onboarding:
      enabled: true
      experiment_key: "UG9_262"
      whitelist_group: "Variation 1"
  user_challenge:
    face_source_id: {{ fin.installment.user_challenge.face_source_id }}
    face_expired_in: {{ fin.installment.user_challenge.face_expired_in }}

management:
  app:
    env: "stg"
    name: "management"
  server:
    http:
      addr: 0.0.0.0:8080
      timeout: 30s
    grpc:
      addr: 0.0.0.0:9090
      timeout: 30s
  data:
    database:
      driver: {{ fin.installment.management.database.driver }}
      source: >
        {{ private-cicd.zalopay.mysql.148.user.1039.username }}:{{ private-cicd.zalopay.mysql.148.user.1039.password }}
        @tcp({{ private-cicd.zalopay.mysql.148.host }}:{{ private-cicd.zalopay.mysql.148.port }})
        /fin_installment_management_stg?parseTime=true&loc=Local
      max_open_conn: 128
      max_idle_conn: 64
      conn_max_lifetime: 600s
    redis_cache:
      address: "{{ private-cicd.zalopay.redis.host }}:{{ private-cicd.zalopay.redis.port }}"
      username:
      password: {{ private-cicd.zalopay.redis.1000107.password }}
      master_name: "mymaster"
      pool_size: 10
      min_idle_conn: 10
      max_conn_age: 300s
  adapters:
    session:
      address: {{ fin.installment.adapters.session.address }}
      secured: {{ fin.installment.adapters.session.secured }}
    cimb_connector:
      address: {{ fin.installment.adapters.cimb_connector.address }}
      secured: {{ fin.installment.adapters.cimb_connector.secured }}
      client_id: {{ fin.installment.adapters.cimb_connector.client_id }}
      client_key: {{ fin.installment.adapters.cimb_connector.client_key }}
      timeout: 30s
    account_service:
      address: {{ fin.installment.adapters.account_service.address }}
      secured: {{ fin.installment.adapters.account_service.secured }}
      timeout: 30s
    temporal:
      address: {{ fin.installment.adapters.temporal.address }}
      namespace: {{ fin.installment.adapters.temporal.namespace }}
      enable_ssl: true
    payment_comm_pub:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-7.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-7.port }}"
      topic: "stg.fin.installment.payment.update_payment"
    refund_settle_sub:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "stg.fin.installment.refund.settle"
      group_id: "fin.installment.management.refund.settle_process.group"
      num_workers: 2
  schedulers:
    sync_statements_periodic:
      queue_name: "statement.syncing.common"
      workflow_type: "SyncStatementsPeriodicWorkflow"
    sync_statements_penalty:
      queue_name: "statement.syncing.common"
      workflow_type: "SyncStatementsPenaltyWorkflow"
    sync_statements_batch_proc:
      queue_name: "statement.syncing.process"
      workflow_type: "StatementSyncBatchProcessingWorkflow"
    create_installment_loan:
      queue_name: "installment.common"
      workflow_type: "CreateInstallmentLoanWorkflow"
    sync_installments_daily:
      queue_name: "installment.common"
      workflow_type: "SyncInstallmentsDailyWorkflow"
    sync_loan_repayment:
      queue_name: "management.common"
      workflow_type: "SyncLoanRepaymentWorkflow"
    sync_account_balance:
      queue_name: "account.syncing.common"
      workflow_type: "SyncAccountBalanceWorkflow"
    sync_installment_info:
      queue_name: "installment.common"
      workflow_type: "SyncInstallmentInfoWorkflow"
    sync_latest_statement:
      queue_name: "statement.syncing.common"
      workflow_type: "SyncLatestStatementWorkflow"
  resources:
    fee_file_path: '/apps/resources/fee/fee.json'
    deeplinks:
      default:
        zpa_url: "zalopay://launch/app/2263"
        zpi_url: "https://socialstg.zalopay.vn/spa/v2/installment"
      plan_detail:
        zpa_url: "https://zlpstg-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
        zpi_url: "https://zlpstg-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
        common_url: "https://zlpstg-mnap-director.zalopay.vn/redir/2262?redirect_to=InstallmentPlanDetailScreen"
      plan_detail_mf:
        common_url: "https://socialstg.zalopay.vn/mfpublic/share-module/micro-app/f61dd861-fead-4af5-a232-7c51ec2ec231?mf_scope=installment_external_app&mf_module=./InstallmentPlanDetail"
  crypto_sets:
    plan_encryption:
      key: {{ fin.installment.crypto.plan_encryption.key }}
      iv: {{ fin.installment.crypto.plan_encryption.iv }}
  statement_sync:
    penalty_all_buffer_days: 3
  early_discharge:
    refund:
      enabled: true
      time_window:
        start_time: 
          hours: 8
          minutes: 0
        end_time: 
          hours: 22
          minutes: 0
  tracing:
    enabled: true
    service_name: fin.installment.management
    agent_host: {{ fin.installment.tracing.agent_host }}
    agent_port: {{ fin.installment.tracing.agent_port }}
  logger:
    level: info
    encoding: json
    stack_trace: true
