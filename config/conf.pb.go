// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.3
// source: conf.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *Account               `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Onboarding    *Onboarding            `protobuf:"bytes,2,opt,name=onboarding,proto3" json:"onboarding,omitempty"`
	Management    *Management            `protobuf:"bytes,3,opt,name=management,proto3" json:"management,omitempty"`
	Zombie        *Zombie                `protobuf:"bytes,4,opt,name=zombie,proto3" json:"zombie,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *Bootstrap) GetOnboarding() *Onboarding {
	if x != nil {
		return x.Onboarding
	}
	return nil
}

func (x *Bootstrap) GetManagement() *Management {
	if x != nil {
		return x.Management
	}
	return nil
}

func (x *Bootstrap) GetZombie() *Zombie {
	if x != nil {
		return x.Zombie
	}
	return nil
}

type Zombie struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Zombie) Reset() {
	*x = Zombie{}
	mi := &file_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Zombie) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Zombie) ProtoMessage() {}

func (x *Zombie) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Zombie.ProtoReflect.Descriptor instead.
func (*Zombie) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Zombie) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

type Account struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	App           *Application           `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Server        *Server                `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                  `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Adapters      *Account_Adapters      `protobuf:"bytes,4,opt,name=adapters,proto3" json:"adapters,omitempty"`
	Resources     *Account_Resources     `protobuf:"bytes,5,opt,name=resources,proto3" json:"resources,omitempty"`
	Schedulers    *Account_Schedulers    `protobuf:"bytes,6,opt,name=schedulers,proto3" json:"schedulers,omitempty"`
	Tracing       *Tracing               `protobuf:"bytes,7,opt,name=tracing,proto3" json:"tracing,omitempty"`
	Logger        *Logger                `protobuf:"bytes,8,opt,name=logger,proto3" json:"logger,omitempty"`
	FraudRisk     *Account_FraudRisk     `protobuf:"bytes,9,opt,name=fraud_risk,json=fraudRisk,proto3" json:"fraud_risk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Account) GetApp() *Application {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Account) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Account) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Account) GetAdapters() *Account_Adapters {
	if x != nil {
		return x.Adapters
	}
	return nil
}

func (x *Account) GetResources() *Account_Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Account) GetSchedulers() *Account_Schedulers {
	if x != nil {
		return x.Schedulers
	}
	return nil
}

func (x *Account) GetTracing() *Tracing {
	if x != nil {
		return x.Tracing
	}
	return nil
}

func (x *Account) GetLogger() *Logger {
	if x != nil {
		return x.Logger
	}
	return nil
}

func (x *Account) GetFraudRisk() *Account_FraudRisk {
	if x != nil {
		return x.FraudRisk
	}
	return nil
}

type Onboarding struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	App           *Application              `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Server        *Server                   `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Adapters      *Onboarding_Adapters      `protobuf:"bytes,4,opt,name=adapters,proto3" json:"adapters,omitempty"`
	Schedulers    *Onboarding_Schedulers    `protobuf:"bytes,5,opt,name=schedulers,proto3" json:"schedulers,omitempty"`
	Logger        *Logger                   `protobuf:"bytes,6,opt,name=logger,proto3" json:"logger,omitempty"`
	Tracing       *Tracing                  `protobuf:"bytes,7,opt,name=tracing,proto3" json:"tracing,omitempty"`
	Resources     *Onboarding_Resources     `protobuf:"bytes,8,opt,name=resources,proto3" json:"resources,omitempty"`
	FraudRisk     *Onboarding_FraudRisk     `protobuf:"bytes,9,opt,name=fraud_risk,json=fraudRisk,proto3" json:"fraud_risk,omitempty"`
	UserChallenge *Onboarding_UserChallenge `protobuf:"bytes,10,opt,name=user_challenge,json=userChallenge,proto3" json:"user_challenge,omitempty"`
	Whitelists    *Onboarding_Whitelists    `protobuf:"bytes,11,opt,name=whitelists,proto3" json:"whitelists,omitempty"`
	CreditScore   *Onboarding_CreditScore   `protobuf:"bytes,12,opt,name=credit_score,json=creditScore,proto3" json:"credit_score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding) Reset() {
	*x = Onboarding{}
	mi := &file_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding) ProtoMessage() {}

func (x *Onboarding) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding.ProtoReflect.Descriptor instead.
func (*Onboarding) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Onboarding) GetApp() *Application {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Onboarding) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Onboarding) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Onboarding) GetAdapters() *Onboarding_Adapters {
	if x != nil {
		return x.Adapters
	}
	return nil
}

func (x *Onboarding) GetSchedulers() *Onboarding_Schedulers {
	if x != nil {
		return x.Schedulers
	}
	return nil
}

func (x *Onboarding) GetLogger() *Logger {
	if x != nil {
		return x.Logger
	}
	return nil
}

func (x *Onboarding) GetTracing() *Tracing {
	if x != nil {
		return x.Tracing
	}
	return nil
}

func (x *Onboarding) GetResources() *Onboarding_Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Onboarding) GetFraudRisk() *Onboarding_FraudRisk {
	if x != nil {
		return x.FraudRisk
	}
	return nil
}

func (x *Onboarding) GetUserChallenge() *Onboarding_UserChallenge {
	if x != nil {
		return x.UserChallenge
	}
	return nil
}

func (x *Onboarding) GetWhitelists() *Onboarding_Whitelists {
	if x != nil {
		return x.Whitelists
	}
	return nil
}

func (x *Onboarding) GetCreditScore() *Onboarding_CreditScore {
	if x != nil {
		return x.CreditScore
	}
	return nil
}

type Management struct {
	state          protoimpl.MessageState     `protogen:"open.v1"`
	App            *Application               `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Server         *Server                    `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
	Data           *Data                      `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Adapters       *Management_Adapters       `protobuf:"bytes,4,opt,name=adapters,proto3" json:"adapters,omitempty"`
	Tracing        *Tracing                   `protobuf:"bytes,5,opt,name=tracing,proto3" json:"tracing,omitempty"`
	Logger         *Logger                    `protobuf:"bytes,6,opt,name=logger,proto3" json:"logger,omitempty"`
	Schedulers     *Management_Schedulers     `protobuf:"bytes,7,opt,name=schedulers,proto3" json:"schedulers,omitempty"`
	Resources      *Management_Resources      `protobuf:"bytes,8,opt,name=resources,proto3" json:"resources,omitempty"`
	CryptoSets     *Management_CryptoSets     `protobuf:"bytes,9,opt,name=crypto_sets,json=cryptoSets,proto3" json:"crypto_sets,omitempty"`
	StatementSync  *Management_StatementSync  `protobuf:"bytes,10,opt,name=statement_sync,json=statementSync,proto3" json:"statement_sync,omitempty"`
	EarlyDischarge *Management_EarlyDischarge `protobuf:"bytes,11,opt,name=early_discharge,json=earlyDischarge,proto3" json:"early_discharge,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Management) Reset() {
	*x = Management{}
	mi := &file_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management) ProtoMessage() {}

func (x *Management) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management.ProtoReflect.Descriptor instead.
func (*Management) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Management) GetApp() *Application {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Management) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Management) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Management) GetAdapters() *Management_Adapters {
	if x != nil {
		return x.Adapters
	}
	return nil
}

func (x *Management) GetTracing() *Tracing {
	if x != nil {
		return x.Tracing
	}
	return nil
}

func (x *Management) GetLogger() *Logger {
	if x != nil {
		return x.Logger
	}
	return nil
}

func (x *Management) GetSchedulers() *Management_Schedulers {
	if x != nil {
		return x.Schedulers
	}
	return nil
}

func (x *Management) GetResources() *Management_Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Management) GetCryptoSets() *Management_CryptoSets {
	if x != nil {
		return x.CryptoSets
	}
	return nil
}

func (x *Management) GetStatementSync() *Management_StatementSync {
	if x != nil {
		return x.StatementSync
	}
	return nil
}

func (x *Management) GetEarlyDischarge() *Management_EarlyDischarge {
	if x != nil {
		return x.EarlyDischarge
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Http          *Server_HTTP           `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc          *Server_GRPC           `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Database      *Database              `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	S3Storage     *S3Storage             `protobuf:"bytes,2,opt,name=s3_storage,json=s3Storage,proto3" json:"s3_storage,omitempty"`
	RedisCache    *RedisCache            `protobuf:"bytes,3,opt,name=redis_cache,json=redisCache,proto3" json:"redis_cache,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Data) GetDatabase() *Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetS3Storage() *S3Storage {
	if x != nil {
		return x.S3Storage
	}
	return nil
}

func (x *Data) GetRedisCache() *RedisCache {
	if x != nil {
		return x.RedisCache
	}
	return nil
}

type GrpcService struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Secured       bool                   `protobuf:"varint,3,opt,name=secured,proto3" json:"secured,omitempty"`
	ClientId      string                 `protobuf:"bytes,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,5,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GrpcService) Reset() {
	*x = GrpcService{}
	mi := &file_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GrpcService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrpcService) ProtoMessage() {}

func (x *GrpcService) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrpcService.ProtoReflect.Descriptor instead.
func (*GrpcService) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{7}
}

func (x *GrpcService) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GrpcService) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *GrpcService) GetSecured() bool {
	if x != nil {
		return x.Secured
	}
	return false
}

func (x *GrpcService) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GrpcService) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

type HttpService struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BaseUrl       string                 `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	ProxyUrl      string                 `protobuf:"bytes,2,opt,name=proxy_url,json=proxyUrl,proto3" json:"proxy_url,omitempty"`
	Retries       int32                  `protobuf:"varint,3,opt,name=retries,proto3" json:"retries,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,4,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpService) Reset() {
	*x = HttpService{}
	mi := &file_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpService) ProtoMessage() {}

func (x *HttpService) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpService.ProtoReflect.Descriptor instead.
func (*HttpService) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{8}
}

func (x *HttpService) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *HttpService) GetProxyUrl() string {
	if x != nil {
		return x.ProxyUrl
	}
	return ""
}

func (x *HttpService) GetRetries() int32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *HttpService) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Database struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Driver          string                 `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source          string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	MaxIdleConn     int32                  `protobuf:"varint,3,opt,name=max_idle_conn,json=maxIdleConn,proto3" json:"max_idle_conn,omitempty"`
	MaxOpenConn     int32                  `protobuf:"varint,4,opt,name=max_open_conn,json=maxOpenConn,proto3" json:"max_open_conn,omitempty"`
	ConnMaxLifetime *durationpb.Duration   `protobuf:"bytes,5,opt,name=conn_max_lifetime,json=connMaxLifetime,proto3" json:"conn_max_lifetime,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Database) Reset() {
	*x = Database{}
	mi := &file_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Database) ProtoMessage() {}

func (x *Database) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Database.ProtoReflect.Descriptor instead.
func (*Database) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{9}
}

func (x *Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Database) GetMaxIdleConn() int32 {
	if x != nil {
		return x.MaxIdleConn
	}
	return 0
}

func (x *Database) GetMaxOpenConn() int32 {
	if x != nil {
		return x.MaxOpenConn
	}
	return 0
}

func (x *Database) GetConnMaxLifetime() *durationpb.Duration {
	if x != nil {
		return x.ConnMaxLifetime
	}
	return nil
}

type S3Storage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Region        string                 `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	BucketName    string                 `protobuf:"bytes,3,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	AccessKey     string                 `protobuf:"bytes,4,opt,name=access_key,json=accessKey,proto3" json:"access_key,omitempty"`
	SecretKey     string                 `protobuf:"bytes,5,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	UseProxy      bool                   `protobuf:"varint,6,opt,name=use_proxy,json=useProxy,proto3" json:"use_proxy,omitempty"`
	ProxyUrl      string                 `protobuf:"bytes,7,opt,name=proxy_url,json=proxyUrl,proto3" json:"proxy_url,omitempty"`
	EnableSsl     bool                   `protobuf:"varint,8,opt,name=enable_ssl,json=enableSsl,proto3" json:"enable_ssl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S3Storage) Reset() {
	*x = S3Storage{}
	mi := &file_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S3Storage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S3Storage) ProtoMessage() {}

func (x *S3Storage) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S3Storage.ProtoReflect.Descriptor instead.
func (*S3Storage) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{10}
}

func (x *S3Storage) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *S3Storage) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *S3Storage) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *S3Storage) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *S3Storage) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *S3Storage) GetUseProxy() bool {
	if x != nil {
		return x.UseProxy
	}
	return false
}

func (x *S3Storage) GetProxyUrl() string {
	if x != nil {
		return x.ProxyUrl
	}
	return ""
}

func (x *S3Storage) GetEnableSsl() bool {
	if x != nil {
		return x.EnableSsl
	}
	return false
}

type Temporal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Namespace     string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	EnableTls     string                 `protobuf:"bytes,3,opt,name=enable_tls,json=enableTls,proto3" json:"enable_tls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Temporal) Reset() {
	*x = Temporal{}
	mi := &file_conf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Temporal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Temporal) ProtoMessage() {}

func (x *Temporal) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Temporal.ProtoReflect.Descriptor instead.
func (*Temporal) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{11}
}

func (x *Temporal) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Temporal) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Temporal) GetEnableTls() string {
	if x != nil {
		return x.EnableTls
	}
	return ""
}

type TemporalTask struct {
	state           protoimpl.MessageState       `protogen:"open.v1"`
	QueueName       string                       `protobuf:"bytes,1,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	WorkflowType    string                       `protobuf:"bytes,2,opt,name=workflow_type,json=workflowType,proto3" json:"workflow_type,omitempty"`
	Timeout         *durationpb.Duration         `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	TimeInterval    *durationpb.Duration         `protobuf:"bytes,4,opt,name=time_interval,json=timeInterval,proto3" json:"time_interval,omitempty"`
	MaxTimeRetry    *durationpb.Duration         `protobuf:"bytes,5,opt,name=max_time_retry,json=maxTimeRetry,proto3" json:"max_time_retry,omitempty"`
	MaxTimeInterval *durationpb.Duration         `protobuf:"bytes,6,opt,name=max_time_interval,json=maxTimeInterval,proto3" json:"max_time_interval,omitempty"`
	Activities      map[string]*TemporalActivity `protobuf:"bytes,7,rep,name=activities,proto3" json:"activities,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TemporalTask) Reset() {
	*x = TemporalTask{}
	mi := &file_conf_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemporalTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporalTask) ProtoMessage() {}

func (x *TemporalTask) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporalTask.ProtoReflect.Descriptor instead.
func (*TemporalTask) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{12}
}

func (x *TemporalTask) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *TemporalTask) GetWorkflowType() string {
	if x != nil {
		return x.WorkflowType
	}
	return ""
}

func (x *TemporalTask) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TemporalTask) GetTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.TimeInterval
	}
	return nil
}

func (x *TemporalTask) GetMaxTimeRetry() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeRetry
	}
	return nil
}

func (x *TemporalTask) GetMaxTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeInterval
	}
	return nil
}

func (x *TemporalTask) GetActivities() map[string]*TemporalActivity {
	if x != nil {
		return x.Activities
	}
	return nil
}

type TemporalActivity struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	QueueName       string                 `protobuf:"bytes,1,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	ActivityType    string                 `protobuf:"bytes,2,opt,name=activity_type,json=activityType,proto3" json:"activity_type,omitempty"`
	Timeout         *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	TimeInterval    *durationpb.Duration   `protobuf:"bytes,4,opt,name=time_interval,json=timeInterval,proto3" json:"time_interval,omitempty"`
	MaxTimeRetry    *durationpb.Duration   `protobuf:"bytes,5,opt,name=max_time_retry,json=maxTimeRetry,proto3" json:"max_time_retry,omitempty"`
	MaxTimeInterval *durationpb.Duration   `protobuf:"bytes,6,opt,name=max_time_interval,json=maxTimeInterval,proto3" json:"max_time_interval,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TemporalActivity) Reset() {
	*x = TemporalActivity{}
	mi := &file_conf_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemporalActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemporalActivity) ProtoMessage() {}

func (x *TemporalActivity) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemporalActivity.ProtoReflect.Descriptor instead.
func (*TemporalActivity) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{13}
}

func (x *TemporalActivity) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *TemporalActivity) GetActivityType() string {
	if x != nil {
		return x.ActivityType
	}
	return ""
}

func (x *TemporalActivity) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *TemporalActivity) GetTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.TimeInterval
	}
	return nil
}

func (x *TemporalActivity) GetMaxTimeRetry() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeRetry
	}
	return nil
}

func (x *TemporalActivity) GetMaxTimeInterval() *durationpb.Duration {
	if x != nil {
		return x.MaxTimeInterval
	}
	return nil
}

type RedisCache struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	MasterName    string                 `protobuf:"bytes,4,opt,name=master_name,json=masterName,proto3" json:"master_name,omitempty"`
	PoolSize      int32                  `protobuf:"varint,5,opt,name=pool_size,json=poolSize,proto3" json:"pool_size,omitempty"`
	MaxRetries    int32                  `protobuf:"varint,6,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	MinIdleConn   int32                  `protobuf:"varint,7,opt,name=min_idle_conn,json=minIdleConn,proto3" json:"min_idle_conn,omitempty"`
	PoolTimeout   *durationpb.Duration   `protobuf:"bytes,8,opt,name=pool_timeout,json=poolTimeout,proto3" json:"pool_timeout,omitempty"`
	IdleTimeout   *durationpb.Duration   `protobuf:"bytes,9,opt,name=idle_timeout,json=idleTimeout,proto3" json:"idle_timeout,omitempty"`
	MaxConnAge    *durationpb.Duration   `protobuf:"bytes,10,opt,name=max_conn_age,json=maxConnAge,proto3" json:"max_conn_age,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedisCache) Reset() {
	*x = RedisCache{}
	mi := &file_conf_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedisCache) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedisCache) ProtoMessage() {}

func (x *RedisCache) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedisCache.ProtoReflect.Descriptor instead.
func (*RedisCache) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{14}
}

func (x *RedisCache) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *RedisCache) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RedisCache) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RedisCache) GetMasterName() string {
	if x != nil {
		return x.MasterName
	}
	return ""
}

func (x *RedisCache) GetPoolSize() int32 {
	if x != nil {
		return x.PoolSize
	}
	return 0
}

func (x *RedisCache) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *RedisCache) GetMinIdleConn() int32 {
	if x != nil {
		return x.MinIdleConn
	}
	return 0
}

func (x *RedisCache) GetPoolTimeout() *durationpb.Duration {
	if x != nil {
		return x.PoolTimeout
	}
	return nil
}

func (x *RedisCache) GetIdleTimeout() *durationpb.Duration {
	if x != nil {
		return x.IdleTimeout
	}
	return nil
}

func (x *RedisCache) GetMaxConnAge() *durationpb.Duration {
	if x != nil {
		return x.MaxConnAge
	}
	return nil
}

type Encryption struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Iv            string                 `protobuf:"bytes,2,opt,name=iv,proto3" json:"iv,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Encryption) Reset() {
	*x = Encryption{}
	mi := &file_conf_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Encryption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Encryption) ProtoMessage() {}

func (x *Encryption) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Encryption.ProtoReflect.Descriptor instead.
func (*Encryption) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{15}
}

func (x *Encryption) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Encryption) GetIv() string {
	if x != nil {
		return x.Iv
	}
	return ""
}

type DeepLink struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ZpaUrl        string                 `protobuf:"bytes,1,opt,name=zpa_url,json=zpaUrl,proto3" json:"zpa_url,omitempty"`
	ZpiUrl        string                 `protobuf:"bytes,2,opt,name=zpi_url,json=zpiUrl,proto3" json:"zpi_url,omitempty"`
	CommonUrl     string                 `protobuf:"bytes,3,opt,name=common_url,json=commonUrl,proto3" json:"common_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeepLink) Reset() {
	*x = DeepLink{}
	mi := &file_conf_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeepLink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeepLink) ProtoMessage() {}

func (x *DeepLink) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeepLink.ProtoReflect.Descriptor instead.
func (*DeepLink) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{16}
}

func (x *DeepLink) GetZpaUrl() string {
	if x != nil {
		return x.ZpaUrl
	}
	return ""
}

func (x *DeepLink) GetZpiUrl() string {
	if x != nil {
		return x.ZpiUrl
	}
	return ""
}

func (x *DeepLink) GetCommonUrl() string {
	if x != nil {
		return x.CommonUrl
	}
	return ""
}

type Logger struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         string                 `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	Encoding      string                 `protobuf:"bytes,2,opt,name=encoding,proto3" json:"encoding,omitempty"`
	StackTrace    bool                   `protobuf:"varint,3,opt,name=stack_trace,json=stackTrace,proto3" json:"stack_trace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Logger) Reset() {
	*x = Logger{}
	mi := &file_conf_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Logger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Logger) ProtoMessage() {}

func (x *Logger) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Logger.ProtoReflect.Descriptor instead.
func (*Logger) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{17}
}

func (x *Logger) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Logger) GetEncoding() string {
	if x != nil {
		return x.Encoding
	}
	return ""
}

func (x *Logger) GetStackTrace() bool {
	if x != nil {
		return x.StackTrace
	}
	return false
}

type Tracing struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enabled       bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	ServiceName   string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	AgentHost     string                 `protobuf:"bytes,3,opt,name=agent_host,json=agentHost,proto3" json:"agent_host,omitempty"`
	AgentPort     int32                  `protobuf:"varint,4,opt,name=agent_port,json=agentPort,proto3" json:"agent_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Tracing) Reset() {
	*x = Tracing{}
	mi := &file_conf_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tracing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tracing) ProtoMessage() {}

func (x *Tracing) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tracing.ProtoReflect.Descriptor instead.
func (*Tracing) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{18}
}

func (x *Tracing) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Tracing) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *Tracing) GetAgentHost() string {
	if x != nil {
		return x.AgentHost
	}
	return ""
}

func (x *Tracing) GetAgentPort() int32 {
	if x != nil {
		return x.AgentPort
	}
	return 0
}

type Application struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Env           string                 `protobuf:"bytes,1,opt,name=env,proto3" json:"env,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Application) Reset() {
	*x = Application{}
	mi := &file_conf_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Application) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application) ProtoMessage() {}

func (x *Application) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application.ProtoReflect.Descriptor instead.
func (*Application) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{19}
}

func (x *Application) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *Application) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Kafka struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Brokers       string                 `protobuf:"bytes,1,opt,name=brokers,proto3" json:"brokers,omitempty"`
	Topic         string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	GroupId       *string                `protobuf:"bytes,3,opt,name=group_id,json=groupId,proto3,oneof" json:"group_id,omitempty"`
	NumWorkers    *int32                 `protobuf:"varint,4,opt,name=num_workers,json=numWorkers,proto3,oneof" json:"num_workers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Kafka) Reset() {
	*x = Kafka{}
	mi := &file_conf_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Kafka) ProtoMessage() {}

func (x *Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Kafka.ProtoReflect.Descriptor instead.
func (*Kafka) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{20}
}

func (x *Kafka) GetBrokers() string {
	if x != nil {
		return x.Brokers
	}
	return ""
}

func (x *Kafka) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *Kafka) GetGroupId() string {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return ""
}

func (x *Kafka) GetNumWorkers() int32 {
	if x != nil && x.NumWorkers != nil {
		return *x.NumWorkers
	}
	return 0
}

type Whitelist struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Enabled        bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	ExperimentKey  string                 `protobuf:"bytes,2,opt,name=experiment_key,json=experimentKey,proto3" json:"experiment_key,omitempty"`
	WhitelistGroup string                 `protobuf:"bytes,3,opt,name=whitelist_group,json=whitelistGroup,proto3" json:"whitelist_group,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Whitelist) Reset() {
	*x = Whitelist{}
	mi := &file_conf_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Whitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Whitelist) ProtoMessage() {}

func (x *Whitelist) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Whitelist.ProtoReflect.Descriptor instead.
func (*Whitelist) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{21}
}

func (x *Whitelist) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Whitelist) GetExperimentKey() string {
	if x != nil {
		return x.ExperimentKey
	}
	return ""
}

func (x *Whitelist) GetWhitelistGroup() string {
	if x != nil {
		return x.WhitelistGroup
	}
	return ""
}

type TimeOfDay struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Hours of day in 24 hour format. Should be from 0 to 23. An API may choose
	// to allow the value "24:00:00" for scenarios like business closing time.
	Hours int32 `protobuf:"varint,1,opt,name=hours,proto3" json:"hours,omitempty"`
	// Minutes of hour of day. Must be from 0 to 59.
	Minutes int32 `protobuf:"varint,2,opt,name=minutes,proto3" json:"minutes,omitempty"`
	// Seconds of minute of the time. Must normally be from 0 to 59. An API may
	// allow the value 60 if it allows leap-seconds.
	Seconds int32 `protobuf:"varint,3,opt,name=seconds,proto3" json:"seconds,omitempty"`
	// Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
	Nanos         int32 `protobuf:"varint,4,opt,name=nanos,proto3" json:"nanos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeOfDay) Reset() {
	*x = TimeOfDay{}
	mi := &file_conf_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeOfDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeOfDay) ProtoMessage() {}

func (x *TimeOfDay) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeOfDay.ProtoReflect.Descriptor instead.
func (*TimeOfDay) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{22}
}

func (x *TimeOfDay) GetHours() int32 {
	if x != nil {
		return x.Hours
	}
	return 0
}

func (x *TimeOfDay) GetMinutes() int32 {
	if x != nil {
		return x.Minutes
	}
	return 0
}

func (x *TimeOfDay) GetSeconds() int32 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *TimeOfDay) GetNanos() int32 {
	if x != nil {
		return x.Nanos
	}
	return 0
}

type Account_Adapters struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Session           *GrpcService           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	CimbConnector     *GrpcService           `protobuf:"bytes,2,opt,name=cimb_connector,json=cimbConnector,proto3" json:"cimb_connector,omitempty"`
	OnboardingService *GrpcService           `protobuf:"bytes,3,opt,name=onboarding_service,json=onboardingService,proto3" json:"onboarding_service,omitempty"`
	Temporal          *Temporal              `protobuf:"bytes,4,opt,name=temporal,proto3" json:"temporal,omitempty"`
	RiskSystem        *GrpcService           `protobuf:"bytes,5,opt,name=risk_system,json=riskSystem,proto3" json:"risk_system,omitempty"`
	CrmEventPub       *Kafka                 `protobuf:"bytes,6,opt,name=crm_event_pub,json=crmEventPub,proto3" json:"crm_event_pub,omitempty"`
	ManagementService *GrpcService           `protobuf:"bytes,7,opt,name=management_service,json=managementService,proto3" json:"management_service,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Account_Adapters) Reset() {
	*x = Account_Adapters{}
	mi := &file_conf_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account_Adapters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account_Adapters) ProtoMessage() {}

func (x *Account_Adapters) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account_Adapters.ProtoReflect.Descriptor instead.
func (*Account_Adapters) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Account_Adapters) GetSession() *GrpcService {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *Account_Adapters) GetCimbConnector() *GrpcService {
	if x != nil {
		return x.CimbConnector
	}
	return nil
}

func (x *Account_Adapters) GetOnboardingService() *GrpcService {
	if x != nil {
		return x.OnboardingService
	}
	return nil
}

func (x *Account_Adapters) GetTemporal() *Temporal {
	if x != nil {
		return x.Temporal
	}
	return nil
}

func (x *Account_Adapters) GetRiskSystem() *GrpcService {
	if x != nil {
		return x.RiskSystem
	}
	return nil
}

func (x *Account_Adapters) GetCrmEventPub() *Kafka {
	if x != nil {
		return x.CrmEventPub
	}
	return nil
}

func (x *Account_Adapters) GetManagementService() *GrpcService {
	if x != nil {
		return x.ManagementService
	}
	return nil
}

type Account_Resources struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	FeeFilePath     string                 `protobuf:"bytes,1,opt,name=fee_file_path,json=feeFilePath,proto3" json:"fee_file_path,omitempty"`
	ServiceFilePath string                 `protobuf:"bytes,2,opt,name=service_file_path,json=serviceFilePath,proto3" json:"service_file_path,omitempty"`
	Deeplinks       map[string]*DeepLink   `protobuf:"bytes,3,rep,name=deeplinks,proto3" json:"deeplinks,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Account_Resources) Reset() {
	*x = Account_Resources{}
	mi := &file_conf_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account_Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account_Resources) ProtoMessage() {}

func (x *Account_Resources) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account_Resources.ProtoReflect.Descriptor instead.
func (*Account_Resources) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Account_Resources) GetFeeFilePath() string {
	if x != nil {
		return x.FeeFilePath
	}
	return ""
}

func (x *Account_Resources) GetServiceFilePath() string {
	if x != nil {
		return x.ServiceFilePath
	}
	return ""
}

func (x *Account_Resources) GetDeeplinks() map[string]*DeepLink {
	if x != nil {
		return x.Deeplinks
	}
	return nil
}

type Account_Schedulers struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	SyncAccountBalance          *TemporalTask          `protobuf:"bytes,1,opt,name=sync_account_balance,json=syncAccountBalance,proto3" json:"sync_account_balance,omitempty"`
	PollingAccountBalance       *TemporalTask          `protobuf:"bytes,2,opt,name=polling_account_balance,json=pollingAccountBalance,proto3" json:"polling_account_balance,omitempty"`
	SyncCimbAccountBalances     *TemporalTask          `protobuf:"bytes,3,opt,name=sync_cimb_account_balances,json=syncCimbAccountBalances,proto3" json:"sync_cimb_account_balances,omitempty"`
	SyncCimbDebtAccountBalances *TemporalTask          `protobuf:"bytes,4,opt,name=sync_cimb_debt_account_balances,json=syncCimbDebtAccountBalances,proto3" json:"sync_cimb_debt_account_balances,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *Account_Schedulers) Reset() {
	*x = Account_Schedulers{}
	mi := &file_conf_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account_Schedulers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account_Schedulers) ProtoMessage() {}

func (x *Account_Schedulers) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account_Schedulers.ProtoReflect.Descriptor instead.
func (*Account_Schedulers) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Account_Schedulers) GetSyncAccountBalance() *TemporalTask {
	if x != nil {
		return x.SyncAccountBalance
	}
	return nil
}

func (x *Account_Schedulers) GetPollingAccountBalance() *TemporalTask {
	if x != nil {
		return x.PollingAccountBalance
	}
	return nil
}

func (x *Account_Schedulers) GetSyncCimbAccountBalances() *TemporalTask {
	if x != nil {
		return x.SyncCimbAccountBalances
	}
	return nil
}

func (x *Account_Schedulers) GetSyncCimbDebtAccountBalances() *TemporalTask {
	if x != nil {
		return x.SyncCimbDebtAccountBalances
	}
	return nil
}

type Account_FraudRisk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HashKey       string                 `protobuf:"bytes,1,opt,name=hash_key,json=hashKey,proto3" json:"hash_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account_FraudRisk) Reset() {
	*x = Account_FraudRisk{}
	mi := &file_conf_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account_FraudRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account_FraudRisk) ProtoMessage() {}

func (x *Account_FraudRisk) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account_FraudRisk.ProtoReflect.Descriptor instead.
func (*Account_FraudRisk) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Account_FraudRisk) GetHashKey() string {
	if x != nil {
		return x.HashKey
	}
	return ""
}

type Onboarding_Adapters struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Session        *GrpcService           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	UserProfile    *GrpcService           `protobuf:"bytes,2,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	EkycCenter     *GrpcService           `protobuf:"bytes,3,opt,name=ekyc_center,json=ekycCenter,proto3" json:"ekyc_center,omitempty"`
	CimbConnector  *GrpcService           `protobuf:"bytes,4,opt,name=cimb_connector,json=cimbConnector,proto3" json:"cimb_connector,omitempty"`
	AccountService *GrpcService           `protobuf:"bytes,5,opt,name=account_service,json=accountService,proto3" json:"account_service,omitempty"`
	RiskSystem     *GrpcService           `protobuf:"bytes,6,opt,name=risk_system,json=riskSystem,proto3" json:"risk_system,omitempty"`
	KycImage       *HttpService           `protobuf:"bytes,7,opt,name=kyc_image,json=kycImage,proto3" json:"kyc_image,omitempty"`
	Temporal       *Temporal              `protobuf:"bytes,8,opt,name=temporal,proto3" json:"temporal,omitempty"`
	FaceAuth       *GrpcService           `protobuf:"bytes,9,opt,name=face_auth,json=faceAuth,proto3" json:"face_auth,omitempty"`
	EkycNfc        *GrpcService           `protobuf:"bytes,10,opt,name=ekyc_nfc,json=ekycNfc,proto3" json:"ekyc_nfc,omitempty"`
	HttpCommon     *HttpService           `protobuf:"bytes,11,opt,name=http_common,json=httpCommon,proto3" json:"http_common,omitempty"`
	AbPlatform     *GrpcService           `protobuf:"bytes,12,opt,name=ab_platform,json=abPlatform,proto3" json:"ab_platform,omitempty"`
	CreditScore    *GrpcService           `protobuf:"bytes,13,opt,name=credit_score,json=creditScore,proto3" json:"credit_score,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Onboarding_Adapters) Reset() {
	*x = Onboarding_Adapters{}
	mi := &file_conf_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_Adapters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_Adapters) ProtoMessage() {}

func (x *Onboarding_Adapters) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_Adapters.ProtoReflect.Descriptor instead.
func (*Onboarding_Adapters) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Onboarding_Adapters) GetSession() *GrpcService {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *Onboarding_Adapters) GetUserProfile() *GrpcService {
	if x != nil {
		return x.UserProfile
	}
	return nil
}

func (x *Onboarding_Adapters) GetEkycCenter() *GrpcService {
	if x != nil {
		return x.EkycCenter
	}
	return nil
}

func (x *Onboarding_Adapters) GetCimbConnector() *GrpcService {
	if x != nil {
		return x.CimbConnector
	}
	return nil
}

func (x *Onboarding_Adapters) GetAccountService() *GrpcService {
	if x != nil {
		return x.AccountService
	}
	return nil
}

func (x *Onboarding_Adapters) GetRiskSystem() *GrpcService {
	if x != nil {
		return x.RiskSystem
	}
	return nil
}

func (x *Onboarding_Adapters) GetKycImage() *HttpService {
	if x != nil {
		return x.KycImage
	}
	return nil
}

func (x *Onboarding_Adapters) GetTemporal() *Temporal {
	if x != nil {
		return x.Temporal
	}
	return nil
}

func (x *Onboarding_Adapters) GetFaceAuth() *GrpcService {
	if x != nil {
		return x.FaceAuth
	}
	return nil
}

func (x *Onboarding_Adapters) GetEkycNfc() *GrpcService {
	if x != nil {
		return x.EkycNfc
	}
	return nil
}

func (x *Onboarding_Adapters) GetHttpCommon() *HttpService {
	if x != nil {
		return x.HttpCommon
	}
	return nil
}

func (x *Onboarding_Adapters) GetAbPlatform() *GrpcService {
	if x != nil {
		return x.AbPlatform
	}
	return nil
}

func (x *Onboarding_Adapters) GetCreditScore() *GrpcService {
	if x != nil {
		return x.CreditScore
	}
	return nil
}

type Onboarding_Schedulers struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OnboardingStatus *TemporalTask          `protobuf:"bytes,1,opt,name=onboarding_status,json=onboardingStatus,proto3" json:"onboarding_status,omitempty"`
	SubmitFaceImage  *TemporalTask          `protobuf:"bytes,2,opt,name=submit_face_image,json=submitFaceImage,proto3" json:"submit_face_image,omitempty"`
	ContractSigning  *TemporalTask          `protobuf:"bytes,3,opt,name=contract_signing,json=contractSigning,proto3" json:"contract_signing,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Onboarding_Schedulers) Reset() {
	*x = Onboarding_Schedulers{}
	mi := &file_conf_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_Schedulers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_Schedulers) ProtoMessage() {}

func (x *Onboarding_Schedulers) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_Schedulers.ProtoReflect.Descriptor instead.
func (*Onboarding_Schedulers) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Onboarding_Schedulers) GetOnboardingStatus() *TemporalTask {
	if x != nil {
		return x.OnboardingStatus
	}
	return nil
}

func (x *Onboarding_Schedulers) GetSubmitFaceImage() *TemporalTask {
	if x != nil {
		return x.SubmitFaceImage
	}
	return nil
}

func (x *Onboarding_Schedulers) GetContractSigning() *TemporalTask {
	if x != nil {
		return x.ContractSigning
	}
	return nil
}

type Onboarding_UserChallenge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FaceSourceId  int32                  `protobuf:"varint,1,opt,name=face_source_id,json=faceSourceId,proto3" json:"face_source_id,omitempty"`
	FaceExpiredIn *durationpb.Duration   `protobuf:"bytes,2,opt,name=face_expired_in,json=faceExpiredIn,proto3" json:"face_expired_in,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_UserChallenge) Reset() {
	*x = Onboarding_UserChallenge{}
	mi := &file_conf_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_UserChallenge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_UserChallenge) ProtoMessage() {}

func (x *Onboarding_UserChallenge) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_UserChallenge.ProtoReflect.Descriptor instead.
func (*Onboarding_UserChallenge) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 2}
}

func (x *Onboarding_UserChallenge) GetFaceSourceId() int32 {
	if x != nil {
		return x.FaceSourceId
	}
	return 0
}

func (x *Onboarding_UserChallenge) GetFaceExpiredIn() *durationpb.Duration {
	if x != nil {
		return x.FaceExpiredIn
	}
	return nil
}

type Onboarding_Deeplinks struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KycUpdate     *DeepLink              `protobuf:"bytes,1,opt,name=kyc_update,json=kycUpdate,proto3" json:"kyc_update,omitempty"`
	NfcUpdate     *DeepLink              `protobuf:"bytes,2,opt,name=nfc_update,json=nfcUpdate,proto3" json:"nfc_update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_Deeplinks) Reset() {
	*x = Onboarding_Deeplinks{}
	mi := &file_conf_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_Deeplinks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_Deeplinks) ProtoMessage() {}

func (x *Onboarding_Deeplinks) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_Deeplinks.ProtoReflect.Descriptor instead.
func (*Onboarding_Deeplinks) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 3}
}

func (x *Onboarding_Deeplinks) GetKycUpdate() *DeepLink {
	if x != nil {
		return x.KycUpdate
	}
	return nil
}

func (x *Onboarding_Deeplinks) GetNfcUpdate() *DeepLink {
	if x != nil {
		return x.NfcUpdate
	}
	return nil
}

type Onboarding_Resources struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Deeplinks     *Onboarding_Deeplinks  `protobuf:"bytes,1,opt,name=deeplinks,proto3" json:"deeplinks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_Resources) Reset() {
	*x = Onboarding_Resources{}
	mi := &file_conf_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_Resources) ProtoMessage() {}

func (x *Onboarding_Resources) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_Resources.ProtoReflect.Descriptor instead.
func (*Onboarding_Resources) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 4}
}

func (x *Onboarding_Resources) GetDeeplinks() *Onboarding_Deeplinks {
	if x != nil {
		return x.Deeplinks
	}
	return nil
}

type Onboarding_FraudRisk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HashKey       string                 `protobuf:"bytes,1,opt,name=hash_key,json=hashKey,proto3" json:"hash_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_FraudRisk) Reset() {
	*x = Onboarding_FraudRisk{}
	mi := &file_conf_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_FraudRisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_FraudRisk) ProtoMessage() {}

func (x *Onboarding_FraudRisk) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_FraudRisk.ProtoReflect.Descriptor instead.
func (*Onboarding_FraudRisk) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 5}
}

func (x *Onboarding_FraudRisk) GetHashKey() string {
	if x != nil {
		return x.HashKey
	}
	return ""
}

type Onboarding_CreditScore struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HashKey       string                 `protobuf:"bytes,1,opt,name=hash_key,json=hashKey,proto3" json:"hash_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_CreditScore) Reset() {
	*x = Onboarding_CreditScore{}
	mi := &file_conf_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_CreditScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_CreditScore) ProtoMessage() {}

func (x *Onboarding_CreditScore) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_CreditScore.ProtoReflect.Descriptor instead.
func (*Onboarding_CreditScore) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 6}
}

func (x *Onboarding_CreditScore) GetHashKey() string {
	if x != nil {
		return x.HashKey
	}
	return ""
}

type Onboarding_Whitelists struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Onboarding    *Whitelist             `protobuf:"bytes,1,opt,name=onboarding,proto3" json:"onboarding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Onboarding_Whitelists) Reset() {
	*x = Onboarding_Whitelists{}
	mi := &file_conf_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Onboarding_Whitelists) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Onboarding_Whitelists) ProtoMessage() {}

func (x *Onboarding_Whitelists) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Onboarding_Whitelists.ProtoReflect.Descriptor instead.
func (*Onboarding_Whitelists) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{3, 7}
}

func (x *Onboarding_Whitelists) GetOnboarding() *Whitelist {
	if x != nil {
		return x.Onboarding
	}
	return nil
}

type Management_Adapters struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Session         *GrpcService           `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	CimbConnector   *GrpcService           `protobuf:"bytes,2,opt,name=cimb_connector,json=cimbConnector,proto3" json:"cimb_connector,omitempty"`
	AccountService  *GrpcService           `protobuf:"bytes,3,opt,name=account_service,json=accountService,proto3" json:"account_service,omitempty"`
	Temporal        *Temporal              `protobuf:"bytes,4,opt,name=temporal,proto3" json:"temporal,omitempty"`
	PaymentCommPub  *Kafka                 `protobuf:"bytes,5,opt,name=payment_comm_pub,json=paymentCommPub,proto3" json:"payment_comm_pub,omitempty"`
	RefundSettleSub *Kafka                 `protobuf:"bytes,6,opt,name=refund_settle_sub,json=refundSettleSub,proto3" json:"refund_settle_sub,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Management_Adapters) Reset() {
	*x = Management_Adapters{}
	mi := &file_conf_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_Adapters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_Adapters) ProtoMessage() {}

func (x *Management_Adapters) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_Adapters.ProtoReflect.Descriptor instead.
func (*Management_Adapters) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Management_Adapters) GetSession() *GrpcService {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *Management_Adapters) GetCimbConnector() *GrpcService {
	if x != nil {
		return x.CimbConnector
	}
	return nil
}

func (x *Management_Adapters) GetAccountService() *GrpcService {
	if x != nil {
		return x.AccountService
	}
	return nil
}

func (x *Management_Adapters) GetTemporal() *Temporal {
	if x != nil {
		return x.Temporal
	}
	return nil
}

func (x *Management_Adapters) GetPaymentCommPub() *Kafka {
	if x != nil {
		return x.PaymentCommPub
	}
	return nil
}

func (x *Management_Adapters) GetRefundSettleSub() *Kafka {
	if x != nil {
		return x.RefundSettleSub
	}
	return nil
}

type Management_Schedulers struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	SyncStatementsPeriodic  *TemporalTask          `protobuf:"bytes,1,opt,name=sync_statements_periodic,json=syncStatementsPeriodic,proto3" json:"sync_statements_periodic,omitempty"`
	SyncStatementsPenalty   *TemporalTask          `protobuf:"bytes,11,opt,name=sync_statements_penalty,json=syncStatementsPenalty,proto3" json:"sync_statements_penalty,omitempty"`
	SyncStatementsBatchProc *TemporalTask          `protobuf:"bytes,2,opt,name=sync_statements_batch_proc,json=syncStatementsBatchProc,proto3" json:"sync_statements_batch_proc,omitempty"`
	CreateInstallmentLoan   *TemporalTask          `protobuf:"bytes,3,opt,name=create_installment_loan,json=createInstallmentLoan,proto3" json:"create_installment_loan,omitempty"`
	SyncInstallmentsDaily   *TemporalTask          `protobuf:"bytes,4,opt,name=sync_installments_daily,json=syncInstallmentsDaily,proto3" json:"sync_installments_daily,omitempty"`
	SyncLoanRepayment       *TemporalTask          `protobuf:"bytes,5,opt,name=sync_loan_repayment,json=syncLoanRepayment,proto3" json:"sync_loan_repayment,omitempty"`
	SyncAccountBalance      *TemporalTask          `protobuf:"bytes,6,opt,name=sync_account_balance,json=syncAccountBalance,proto3" json:"sync_account_balance,omitempty"`
	SyncInstallmentInfo     *TemporalTask          `protobuf:"bytes,7,opt,name=sync_installment_info,json=syncInstallmentInfo,proto3" json:"sync_installment_info,omitempty"`
	SyncLatestStatement     *TemporalTask          `protobuf:"bytes,8,opt,name=sync_latest_statement,json=syncLatestStatement,proto3" json:"sync_latest_statement,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *Management_Schedulers) Reset() {
	*x = Management_Schedulers{}
	mi := &file_conf_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_Schedulers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_Schedulers) ProtoMessage() {}

func (x *Management_Schedulers) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_Schedulers.ProtoReflect.Descriptor instead.
func (*Management_Schedulers) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Management_Schedulers) GetSyncStatementsPeriodic() *TemporalTask {
	if x != nil {
		return x.SyncStatementsPeriodic
	}
	return nil
}

func (x *Management_Schedulers) GetSyncStatementsPenalty() *TemporalTask {
	if x != nil {
		return x.SyncStatementsPenalty
	}
	return nil
}

func (x *Management_Schedulers) GetSyncStatementsBatchProc() *TemporalTask {
	if x != nil {
		return x.SyncStatementsBatchProc
	}
	return nil
}

func (x *Management_Schedulers) GetCreateInstallmentLoan() *TemporalTask {
	if x != nil {
		return x.CreateInstallmentLoan
	}
	return nil
}

func (x *Management_Schedulers) GetSyncInstallmentsDaily() *TemporalTask {
	if x != nil {
		return x.SyncInstallmentsDaily
	}
	return nil
}

func (x *Management_Schedulers) GetSyncLoanRepayment() *TemporalTask {
	if x != nil {
		return x.SyncLoanRepayment
	}
	return nil
}

func (x *Management_Schedulers) GetSyncAccountBalance() *TemporalTask {
	if x != nil {
		return x.SyncAccountBalance
	}
	return nil
}

func (x *Management_Schedulers) GetSyncInstallmentInfo() *TemporalTask {
	if x != nil {
		return x.SyncInstallmentInfo
	}
	return nil
}

func (x *Management_Schedulers) GetSyncLatestStatement() *TemporalTask {
	if x != nil {
		return x.SyncLatestStatement
	}
	return nil
}

type Management_Resources struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FeeFilePath   string                 `protobuf:"bytes,1,opt,name=fee_file_path,json=feeFilePath,proto3" json:"fee_file_path,omitempty"`
	Deeplinks     map[string]*DeepLink   `protobuf:"bytes,3,rep,name=deeplinks,proto3" json:"deeplinks,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Management_Resources) Reset() {
	*x = Management_Resources{}
	mi := &file_conf_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_Resources) ProtoMessage() {}

func (x *Management_Resources) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_Resources.ProtoReflect.Descriptor instead.
func (*Management_Resources) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 2}
}

func (x *Management_Resources) GetFeeFilePath() string {
	if x != nil {
		return x.FeeFilePath
	}
	return ""
}

func (x *Management_Resources) GetDeeplinks() map[string]*DeepLink {
	if x != nil {
		return x.Deeplinks
	}
	return nil
}

type Management_CryptoSets struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PlanEncryption *Encryption            `protobuf:"bytes,1,opt,name=plan_encryption,json=planEncryption,proto3" json:"plan_encryption,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Management_CryptoSets) Reset() {
	*x = Management_CryptoSets{}
	mi := &file_conf_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_CryptoSets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_CryptoSets) ProtoMessage() {}

func (x *Management_CryptoSets) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_CryptoSets.ProtoReflect.Descriptor instead.
func (*Management_CryptoSets) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 3}
}

func (x *Management_CryptoSets) GetPlanEncryption() *Encryption {
	if x != nil {
		return x.PlanEncryption
	}
	return nil
}

type Management_StatementSync struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	PenaltyAllBufferDays int32                  `protobuf:"varint,1,opt,name=penalty_all_buffer_days,json=penaltyAllBufferDays,proto3" json:"penalty_all_buffer_days,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Management_StatementSync) Reset() {
	*x = Management_StatementSync{}
	mi := &file_conf_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_StatementSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_StatementSync) ProtoMessage() {}

func (x *Management_StatementSync) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_StatementSync.ProtoReflect.Descriptor instead.
func (*Management_StatementSync) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 4}
}

func (x *Management_StatementSync) GetPenaltyAllBufferDays() int32 {
	if x != nil {
		return x.PenaltyAllBufferDays
	}
	return 0
}

type Management_EarlyDischarge struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Scenario-specific configurations
	Refund        *Management_EarlyDischarge_BaseScenario `protobuf:"bytes,1,opt,name=refund,proto3" json:"refund,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Management_EarlyDischarge) Reset() {
	*x = Management_EarlyDischarge{}
	mi := &file_conf_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_EarlyDischarge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_EarlyDischarge) ProtoMessage() {}

func (x *Management_EarlyDischarge) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_EarlyDischarge.ProtoReflect.Descriptor instead.
func (*Management_EarlyDischarge) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 5}
}

func (x *Management_EarlyDischarge) GetRefund() *Management_EarlyDischarge_BaseScenario {
	if x != nil {
		return x.Refund
	}
	return nil
}

// Time window settings for early discharge
type Management_EarlyDischarge_TimeWindow struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Time of day when early discharge becomes available
	StartTime *TimeOfDay `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// Time of day when early discharge becomes unavailable
	EndTime       *TimeOfDay `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Management_EarlyDischarge_TimeWindow) Reset() {
	*x = Management_EarlyDischarge_TimeWindow{}
	mi := &file_conf_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_EarlyDischarge_TimeWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_EarlyDischarge_TimeWindow) ProtoMessage() {}

func (x *Management_EarlyDischarge_TimeWindow) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_EarlyDischarge_TimeWindow.ProtoReflect.Descriptor instead.
func (*Management_EarlyDischarge_TimeWindow) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 5, 0}
}

func (x *Management_EarlyDischarge_TimeWindow) GetStartTime() *TimeOfDay {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Management_EarlyDischarge_TimeWindow) GetEndTime() *TimeOfDay {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// Base scenario configuration for early discharge
type Management_EarlyDischarge_BaseScenario struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the scenario is enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Time window when early discharge is permitted
	TimeWindow    *Management_EarlyDischarge_TimeWindow `protobuf:"bytes,2,opt,name=time_window,json=timeWindow,proto3,oneof" json:"time_window,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Management_EarlyDischarge_BaseScenario) Reset() {
	*x = Management_EarlyDischarge_BaseScenario{}
	mi := &file_conf_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Management_EarlyDischarge_BaseScenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Management_EarlyDischarge_BaseScenario) ProtoMessage() {}

func (x *Management_EarlyDischarge_BaseScenario) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Management_EarlyDischarge_BaseScenario.ProtoReflect.Descriptor instead.
func (*Management_EarlyDischarge_BaseScenario) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{4, 5, 1}
}

func (x *Management_EarlyDischarge_BaseScenario) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Management_EarlyDischarge_BaseScenario) GetTimeWindow() *Management_EarlyDischarge_TimeWindow {
	if x != nil {
		return x.TimeWindow
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_conf_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_conf_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_conf_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_conf_proto_rawDescGZIP(), []int{5, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

var File_conf_proto protoreflect.FileDescriptor

const file_conf_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"conf.proto\x12\n" +
	"kratos.api\x1a\x1egoogle/protobuf/duration.proto\"\xd6\x01\n" +
	"\tBootstrap\x12-\n" +
	"\aaccount\x18\x01 \x01(\v2\x13.kratos.api.AccountR\aaccount\x126\n" +
	"\n" +
	"onboarding\x18\x02 \x01(\v2\x16.kratos.api.OnboardingR\n" +
	"onboarding\x126\n" +
	"\n" +
	"management\x18\x03 \x01(\v2\x16.kratos.api.ManagementR\n" +
	"management\x12*\n" +
	"\x06zombie\x18\x04 \x01(\v2\x12.kratos.api.ZombieR\x06zombie\"4\n" +
	"\x06Zombie\x12*\n" +
	"\x06server\x18\x01 \x01(\v2\x12.kratos.api.ServerR\x06server\"\x93\f\n" +
	"\aAccount\x12)\n" +
	"\x03app\x18\x01 \x01(\v2\x17.kratos.api.ApplicationR\x03app\x12*\n" +
	"\x06server\x18\x02 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x03 \x01(\v2\x10.kratos.api.DataR\x04data\x128\n" +
	"\badapters\x18\x04 \x01(\v2\x1c.kratos.api.Account.AdaptersR\badapters\x12;\n" +
	"\tresources\x18\x05 \x01(\v2\x1d.kratos.api.Account.ResourcesR\tresources\x12>\n" +
	"\n" +
	"schedulers\x18\x06 \x01(\v2\x1e.kratos.api.Account.SchedulersR\n" +
	"schedulers\x12-\n" +
	"\atracing\x18\a \x01(\v2\x13.kratos.api.TracingR\atracing\x12*\n" +
	"\x06logger\x18\b \x01(\v2\x12.kratos.api.LoggerR\x06logger\x12<\n" +
	"\n" +
	"fraud_risk\x18\t \x01(\v2\x1d.kratos.api.Account.FraudRiskR\tfraudRisk\x1a\xb0\x03\n" +
	"\bAdapters\x121\n" +
	"\asession\x18\x01 \x01(\v2\x17.kratos.api.GrpcServiceR\asession\x12>\n" +
	"\x0ecimb_connector\x18\x02 \x01(\v2\x17.kratos.api.GrpcServiceR\rcimbConnector\x12F\n" +
	"\x12onboarding_service\x18\x03 \x01(\v2\x17.kratos.api.GrpcServiceR\x11onboardingService\x120\n" +
	"\btemporal\x18\x04 \x01(\v2\x14.kratos.api.TemporalR\btemporal\x128\n" +
	"\vrisk_system\x18\x05 \x01(\v2\x17.kratos.api.GrpcServiceR\n" +
	"riskSystem\x125\n" +
	"\rcrm_event_pub\x18\x06 \x01(\v2\x11.kratos.api.KafkaR\vcrmEventPub\x12F\n" +
	"\x12management_service\x18\a \x01(\v2\x17.kratos.api.GrpcServiceR\x11managementService\x1a\xfb\x01\n" +
	"\tResources\x12\"\n" +
	"\rfee_file_path\x18\x01 \x01(\tR\vfeeFilePath\x12*\n" +
	"\x11service_file_path\x18\x02 \x01(\tR\x0fserviceFilePath\x12J\n" +
	"\tdeeplinks\x18\x03 \x03(\v2,.kratos.api.Account.Resources.DeeplinksEntryR\tdeeplinks\x1aR\n" +
	"\x0eDeeplinksEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.kratos.api.DeepLinkR\x05value:\x028\x01\x1a\xe1\x02\n" +
	"\n" +
	"Schedulers\x12J\n" +
	"\x14sync_account_balance\x18\x01 \x01(\v2\x18.kratos.api.TemporalTaskR\x12syncAccountBalance\x12P\n" +
	"\x17polling_account_balance\x18\x02 \x01(\v2\x18.kratos.api.TemporalTaskR\x15pollingAccountBalance\x12U\n" +
	"\x1async_cimb_account_balances\x18\x03 \x01(\v2\x18.kratos.api.TemporalTaskR\x17syncCimbAccountBalances\x12^\n" +
	"\x1fsync_cimb_debt_account_balances\x18\x04 \x01(\v2\x18.kratos.api.TemporalTaskR\x1bsyncCimbDebtAccountBalances\x1a&\n" +
	"\tFraudRisk\x12\x19\n" +
	"\bhash_key\x18\x01 \x01(\tR\ahashKey\"\xe6\x10\n" +
	"\n" +
	"Onboarding\x12)\n" +
	"\x03app\x18\x01 \x01(\v2\x17.kratos.api.ApplicationR\x03app\x12*\n" +
	"\x06server\x18\x02 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x03 \x01(\v2\x10.kratos.api.DataR\x04data\x12;\n" +
	"\badapters\x18\x04 \x01(\v2\x1f.kratos.api.Onboarding.AdaptersR\badapters\x12A\n" +
	"\n" +
	"schedulers\x18\x05 \x01(\v2!.kratos.api.Onboarding.SchedulersR\n" +
	"schedulers\x12*\n" +
	"\x06logger\x18\x06 \x01(\v2\x12.kratos.api.LoggerR\x06logger\x12-\n" +
	"\atracing\x18\a \x01(\v2\x13.kratos.api.TracingR\atracing\x12>\n" +
	"\tresources\x18\b \x01(\v2 .kratos.api.Onboarding.ResourcesR\tresources\x12?\n" +
	"\n" +
	"fraud_risk\x18\t \x01(\v2 .kratos.api.Onboarding.FraudRiskR\tfraudRisk\x12K\n" +
	"\x0euser_challenge\x18\n" +
	" \x01(\v2$.kratos.api.Onboarding.UserChallengeR\ruserChallenge\x12A\n" +
	"\n" +
	"whitelists\x18\v \x01(\v2!.kratos.api.Onboarding.WhitelistsR\n" +
	"whitelists\x12E\n" +
	"\fcredit_score\x18\f \x01(\v2\".kratos.api.Onboarding.CreditScoreR\vcreditScore\x1a\xf1\x05\n" +
	"\bAdapters\x121\n" +
	"\asession\x18\x01 \x01(\v2\x17.kratos.api.GrpcServiceR\asession\x12:\n" +
	"\fuser_profile\x18\x02 \x01(\v2\x17.kratos.api.GrpcServiceR\vuserProfile\x128\n" +
	"\vekyc_center\x18\x03 \x01(\v2\x17.kratos.api.GrpcServiceR\n" +
	"ekycCenter\x12>\n" +
	"\x0ecimb_connector\x18\x04 \x01(\v2\x17.kratos.api.GrpcServiceR\rcimbConnector\x12@\n" +
	"\x0faccount_service\x18\x05 \x01(\v2\x17.kratos.api.GrpcServiceR\x0eaccountService\x128\n" +
	"\vrisk_system\x18\x06 \x01(\v2\x17.kratos.api.GrpcServiceR\n" +
	"riskSystem\x124\n" +
	"\tkyc_image\x18\a \x01(\v2\x17.kratos.api.HttpServiceR\bkycImage\x120\n" +
	"\btemporal\x18\b \x01(\v2\x14.kratos.api.TemporalR\btemporal\x124\n" +
	"\tface_auth\x18\t \x01(\v2\x17.kratos.api.GrpcServiceR\bfaceAuth\x122\n" +
	"\bekyc_nfc\x18\n" +
	" \x01(\v2\x17.kratos.api.GrpcServiceR\aekycNfc\x128\n" +
	"\vhttp_common\x18\v \x01(\v2\x17.kratos.api.HttpServiceR\n" +
	"httpCommon\x128\n" +
	"\vab_platform\x18\f \x01(\v2\x17.kratos.api.GrpcServiceR\n" +
	"abPlatform\x12:\n" +
	"\fcredit_score\x18\r \x01(\v2\x17.kratos.api.GrpcServiceR\vcreditScore\x1a\xde\x01\n" +
	"\n" +
	"Schedulers\x12E\n" +
	"\x11onboarding_status\x18\x01 \x01(\v2\x18.kratos.api.TemporalTaskR\x10onboardingStatus\x12D\n" +
	"\x11submit_face_image\x18\x02 \x01(\v2\x18.kratos.api.TemporalTaskR\x0fsubmitFaceImage\x12C\n" +
	"\x10contract_signing\x18\x03 \x01(\v2\x18.kratos.api.TemporalTaskR\x0fcontractSigning\x1ax\n" +
	"\rUserChallenge\x12$\n" +
	"\x0eface_source_id\x18\x01 \x01(\x05R\ffaceSourceId\x12A\n" +
	"\x0fface_expired_in\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\rfaceExpiredIn\x1au\n" +
	"\tDeeplinks\x123\n" +
	"\n" +
	"kyc_update\x18\x01 \x01(\v2\x14.kratos.api.DeepLinkR\tkycUpdate\x123\n" +
	"\n" +
	"nfc_update\x18\x02 \x01(\v2\x14.kratos.api.DeepLinkR\tnfcUpdate\x1aK\n" +
	"\tResources\x12>\n" +
	"\tdeeplinks\x18\x01 \x01(\v2 .kratos.api.Onboarding.DeeplinksR\tdeeplinks\x1a&\n" +
	"\tFraudRisk\x12\x19\n" +
	"\bhash_key\x18\x01 \x01(\tR\ahashKey\x1a(\n" +
	"\vCreditScore\x12\x19\n" +
	"\bhash_key\x18\x01 \x01(\tR\ahashKey\x1aC\n" +
	"\n" +
	"Whitelists\x125\n" +
	"\n" +
	"onboarding\x18\x01 \x01(\v2\x15.kratos.api.WhitelistR\n" +
	"onboarding\"\xab\x13\n" +
	"\n" +
	"Management\x12)\n" +
	"\x03app\x18\x01 \x01(\v2\x17.kratos.api.ApplicationR\x03app\x12*\n" +
	"\x06server\x18\x02 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x03 \x01(\v2\x10.kratos.api.DataR\x04data\x12;\n" +
	"\badapters\x18\x04 \x01(\v2\x1f.kratos.api.Management.AdaptersR\badapters\x12-\n" +
	"\atracing\x18\x05 \x01(\v2\x13.kratos.api.TracingR\atracing\x12*\n" +
	"\x06logger\x18\x06 \x01(\v2\x12.kratos.api.LoggerR\x06logger\x12A\n" +
	"\n" +
	"schedulers\x18\a \x01(\v2!.kratos.api.Management.SchedulersR\n" +
	"schedulers\x12>\n" +
	"\tresources\x18\b \x01(\v2 .kratos.api.Management.ResourcesR\tresources\x12B\n" +
	"\vcrypto_sets\x18\t \x01(\v2!.kratos.api.Management.CryptoSetsR\n" +
	"cryptoSets\x12K\n" +
	"\x0estatement_sync\x18\n" +
	" \x01(\v2$.kratos.api.Management.StatementSyncR\rstatementSync\x12N\n" +
	"\x0fearly_discharge\x18\v \x01(\v2%.kratos.api.Management.EarlyDischargeR\x0eearlyDischarge\x1a\xed\x02\n" +
	"\bAdapters\x121\n" +
	"\asession\x18\x01 \x01(\v2\x17.kratos.api.GrpcServiceR\asession\x12>\n" +
	"\x0ecimb_connector\x18\x02 \x01(\v2\x17.kratos.api.GrpcServiceR\rcimbConnector\x12@\n" +
	"\x0faccount_service\x18\x03 \x01(\v2\x17.kratos.api.GrpcServiceR\x0eaccountService\x120\n" +
	"\btemporal\x18\x04 \x01(\v2\x14.kratos.api.TemporalR\btemporal\x12;\n" +
	"\x10payment_comm_pub\x18\x05 \x01(\v2\x11.kratos.api.KafkaR\x0epaymentCommPub\x12=\n" +
	"\x11refund_settle_sub\x18\x06 \x01(\v2\x11.kratos.api.KafkaR\x0frefundSettleSub\x1a\xdf\x05\n" +
	"\n" +
	"Schedulers\x12R\n" +
	"\x18sync_statements_periodic\x18\x01 \x01(\v2\x18.kratos.api.TemporalTaskR\x16syncStatementsPeriodic\x12P\n" +
	"\x17sync_statements_penalty\x18\v \x01(\v2\x18.kratos.api.TemporalTaskR\x15syncStatementsPenalty\x12U\n" +
	"\x1async_statements_batch_proc\x18\x02 \x01(\v2\x18.kratos.api.TemporalTaskR\x17syncStatementsBatchProc\x12P\n" +
	"\x17create_installment_loan\x18\x03 \x01(\v2\x18.kratos.api.TemporalTaskR\x15createInstallmentLoan\x12P\n" +
	"\x17sync_installments_daily\x18\x04 \x01(\v2\x18.kratos.api.TemporalTaskR\x15syncInstallmentsDaily\x12H\n" +
	"\x13sync_loan_repayment\x18\x05 \x01(\v2\x18.kratos.api.TemporalTaskR\x11syncLoanRepayment\x12J\n" +
	"\x14sync_account_balance\x18\x06 \x01(\v2\x18.kratos.api.TemporalTaskR\x12syncAccountBalance\x12L\n" +
	"\x15sync_installment_info\x18\a \x01(\v2\x18.kratos.api.TemporalTaskR\x13syncInstallmentInfo\x12L\n" +
	"\x15sync_latest_statement\x18\b \x01(\v2\x18.kratos.api.TemporalTaskR\x13syncLatestStatement\x1a\xd2\x01\n" +
	"\tResources\x12\"\n" +
	"\rfee_file_path\x18\x01 \x01(\tR\vfeeFilePath\x12M\n" +
	"\tdeeplinks\x18\x03 \x03(\v2/.kratos.api.Management.Resources.DeeplinksEntryR\tdeeplinks\x1aR\n" +
	"\x0eDeeplinksEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.kratos.api.DeepLinkR\x05value:\x028\x01\x1aM\n" +
	"\n" +
	"CryptoSets\x12?\n" +
	"\x0fplan_encryption\x18\x01 \x01(\v2\x16.kratos.api.EncryptionR\x0eplanEncryption\x1aF\n" +
	"\rStatementSync\x125\n" +
	"\x17penalty_all_buffer_days\x18\x01 \x01(\x05R\x14penaltyAllBufferDays\x1a\xe5\x02\n" +
	"\x0eEarlyDischarge\x12J\n" +
	"\x06refund\x18\x01 \x01(\v22.kratos.api.Management.EarlyDischarge.BaseScenarioR\x06refund\x1at\n" +
	"\n" +
	"TimeWindow\x124\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x15.kratos.api.TimeOfDayR\tstartTime\x120\n" +
	"\bend_time\x18\x02 \x01(\v2\x15.kratos.api.TimeOfDayR\aendTime\x1a\x90\x01\n" +
	"\fBaseScenario\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12V\n" +
	"\vtime_window\x18\x02 \x01(\v20.kratos.api.Management.EarlyDischarge.TimeWindowH\x00R\n" +
	"timeWindow\x88\x01\x01B\x0e\n" +
	"\f_time_window\"\xb8\x02\n" +
	"\x06Server\x12+\n" +
	"\x04http\x18\x01 \x01(\v2\x17.kratos.api.Server.HTTPR\x04http\x12+\n" +
	"\x04grpc\x18\x02 \x01(\v2\x17.kratos.api.Server.GRPCR\x04grpc\x1ai\n" +
	"\x04HTTP\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1ai\n" +
	"\x04GRPC\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\"\xa7\x01\n" +
	"\x04Data\x120\n" +
	"\bdatabase\x18\x01 \x01(\v2\x14.kratos.api.DatabaseR\bdatabase\x124\n" +
	"\n" +
	"s3_storage\x18\x02 \x01(\v2\x15.kratos.api.S3StorageR\ts3Storage\x127\n" +
	"\vredis_cache\x18\x03 \x01(\v2\x16.kratos.api.RedisCacheR\n" +
	"redisCache\"\xb2\x01\n" +
	"\vGrpcService\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x123\n" +
	"\atimeout\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12\x18\n" +
	"\asecured\x18\x03 \x01(\bR\asecured\x12\x1b\n" +
	"\tclient_id\x18\x04 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x05 \x01(\tR\tclientKey\"\x94\x01\n" +
	"\vHttpService\x12\x19\n" +
	"\bbase_url\x18\x01 \x01(\tR\abaseUrl\x12\x1b\n" +
	"\tproxy_url\x18\x02 \x01(\tR\bproxyUrl\x12\x18\n" +
	"\aretries\x18\x03 \x01(\x05R\aretries\x123\n" +
	"\atimeout\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\atimeout\"\xc9\x01\n" +
	"\bDatabase\x12\x16\n" +
	"\x06driver\x18\x01 \x01(\tR\x06driver\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\"\n" +
	"\rmax_idle_conn\x18\x03 \x01(\x05R\vmaxIdleConn\x12\"\n" +
	"\rmax_open_conn\x18\x04 \x01(\x05R\vmaxOpenConn\x12E\n" +
	"\x11conn_max_lifetime\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\x0fconnMaxLifetime\"\xef\x01\n" +
	"\tS3Storage\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12\x16\n" +
	"\x06region\x18\x02 \x01(\tR\x06region\x12\x1f\n" +
	"\vbucket_name\x18\x03 \x01(\tR\n" +
	"bucketName\x12\x1d\n" +
	"\n" +
	"access_key\x18\x04 \x01(\tR\taccessKey\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x05 \x01(\tR\tsecretKey\x12\x1b\n" +
	"\tuse_proxy\x18\x06 \x01(\bR\buseProxy\x12\x1b\n" +
	"\tproxy_url\x18\a \x01(\tR\bproxyUrl\x12\x1d\n" +
	"\n" +
	"enable_ssl\x18\b \x01(\bR\tenableSsl\"a\n" +
	"\bTemporal\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12\x1c\n" +
	"\tnamespace\x18\x02 \x01(\tR\tnamespace\x12\x1d\n" +
	"\n" +
	"enable_tls\x18\x03 \x01(\tR\tenableTls\"\xf6\x03\n" +
	"\fTemporalTask\x12\x1d\n" +
	"\n" +
	"queue_name\x18\x01 \x01(\tR\tqueueName\x12#\n" +
	"\rworkflow_type\x18\x02 \x01(\tR\fworkflowType\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12>\n" +
	"\rtime_interval\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\ftimeInterval\x12?\n" +
	"\x0emax_time_retry\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\fmaxTimeRetry\x12E\n" +
	"\x11max_time_interval\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\x0fmaxTimeInterval\x12H\n" +
	"\n" +
	"activities\x18\a \x03(\v2(.kratos.api.TemporalTask.ActivitiesEntryR\n" +
	"activities\x1a[\n" +
	"\x0fActivitiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x122\n" +
	"\x05value\x18\x02 \x01(\v2\x1c.kratos.api.TemporalActivityR\x05value:\x028\x01\"\xd3\x02\n" +
	"\x10TemporalActivity\x12\x1d\n" +
	"\n" +
	"queue_name\x18\x01 \x01(\tR\tqueueName\x12#\n" +
	"\ractivity_type\x18\x02 \x01(\tR\factivityType\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x12>\n" +
	"\rtime_interval\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\ftimeInterval\x12?\n" +
	"\x0emax_time_retry\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\fmaxTimeRetry\x12E\n" +
	"\x11max_time_interval\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\x0fmaxTimeInterval\"\x9a\x03\n" +
	"\n" +
	"RedisCache\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1f\n" +
	"\vmaster_name\x18\x04 \x01(\tR\n" +
	"masterName\x12\x1b\n" +
	"\tpool_size\x18\x05 \x01(\x05R\bpoolSize\x12\x1f\n" +
	"\vmax_retries\x18\x06 \x01(\x05R\n" +
	"maxRetries\x12\"\n" +
	"\rmin_idle_conn\x18\a \x01(\x05R\vminIdleConn\x12<\n" +
	"\fpool_timeout\x18\b \x01(\v2\x19.google.protobuf.DurationR\vpoolTimeout\x12<\n" +
	"\fidle_timeout\x18\t \x01(\v2\x19.google.protobuf.DurationR\vidleTimeout\x12;\n" +
	"\fmax_conn_age\x18\n" +
	" \x01(\v2\x19.google.protobuf.DurationR\n" +
	"maxConnAge\".\n" +
	"\n" +
	"Encryption\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x0e\n" +
	"\x02iv\x18\x02 \x01(\tR\x02iv\"[\n" +
	"\bDeepLink\x12\x17\n" +
	"\azpa_url\x18\x01 \x01(\tR\x06zpaUrl\x12\x17\n" +
	"\azpi_url\x18\x02 \x01(\tR\x06zpiUrl\x12\x1d\n" +
	"\n" +
	"common_url\x18\x03 \x01(\tR\tcommonUrl\"[\n" +
	"\x06Logger\x12\x14\n" +
	"\x05level\x18\x01 \x01(\tR\x05level\x12\x1a\n" +
	"\bencoding\x18\x02 \x01(\tR\bencoding\x12\x1f\n" +
	"\vstack_trace\x18\x03 \x01(\bR\n" +
	"stackTrace\"\x84\x01\n" +
	"\aTracing\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12!\n" +
	"\fservice_name\x18\x02 \x01(\tR\vserviceName\x12\x1d\n" +
	"\n" +
	"agent_host\x18\x03 \x01(\tR\tagentHost\x12\x1d\n" +
	"\n" +
	"agent_port\x18\x04 \x01(\x05R\tagentPort\"3\n" +
	"\vApplication\x12\x10\n" +
	"\x03env\x18\x01 \x01(\tR\x03env\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x9a\x01\n" +
	"\x05Kafka\x12\x18\n" +
	"\abrokers\x18\x01 \x01(\tR\abrokers\x12\x14\n" +
	"\x05topic\x18\x02 \x01(\tR\x05topic\x12\x1e\n" +
	"\bgroup_id\x18\x03 \x01(\tH\x00R\agroupId\x88\x01\x01\x12$\n" +
	"\vnum_workers\x18\x04 \x01(\x05H\x01R\n" +
	"numWorkers\x88\x01\x01B\v\n" +
	"\t_group_idB\x0e\n" +
	"\f_num_workers\"u\n" +
	"\tWhitelist\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12%\n" +
	"\x0eexperiment_key\x18\x02 \x01(\tR\rexperimentKey\x12'\n" +
	"\x0fwhitelist_group\x18\x03 \x01(\tR\x0ewhitelistGroup\"k\n" +
	"\tTimeOfDay\x12\x14\n" +
	"\x05hours\x18\x01 \x01(\x05R\x05hours\x12\x18\n" +
	"\aminutes\x18\x02 \x01(\x05R\aminutes\x12\x18\n" +
	"\aseconds\x18\x03 \x01(\x05R\aseconds\x12\x14\n" +
	"\x05nanos\x18\x04 \x01(\x05R\x05nanosB_Z]gitlab.zalopay.vn/fin/installment/installment-service/internal/account/internal/config;configb\x06proto3"

var (
	file_conf_proto_rawDescOnce sync.Once
	file_conf_proto_rawDescData []byte
)

func file_conf_proto_rawDescGZIP() []byte {
	file_conf_proto_rawDescOnce.Do(func() {
		file_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)))
	})
	return file_conf_proto_rawDescData
}

var file_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 48)
var file_conf_proto_goTypes = []any{
	(*Bootstrap)(nil),                              // 0: kratos.api.Bootstrap
	(*Zombie)(nil),                                 // 1: kratos.api.Zombie
	(*Account)(nil),                                // 2: kratos.api.Account
	(*Onboarding)(nil),                             // 3: kratos.api.Onboarding
	(*Management)(nil),                             // 4: kratos.api.Management
	(*Server)(nil),                                 // 5: kratos.api.Server
	(*Data)(nil),                                   // 6: kratos.api.Data
	(*GrpcService)(nil),                            // 7: kratos.api.GrpcService
	(*HttpService)(nil),                            // 8: kratos.api.HttpService
	(*Database)(nil),                               // 9: kratos.api.Database
	(*S3Storage)(nil),                              // 10: kratos.api.S3Storage
	(*Temporal)(nil),                               // 11: kratos.api.Temporal
	(*TemporalTask)(nil),                           // 12: kratos.api.TemporalTask
	(*TemporalActivity)(nil),                       // 13: kratos.api.TemporalActivity
	(*RedisCache)(nil),                             // 14: kratos.api.RedisCache
	(*Encryption)(nil),                             // 15: kratos.api.Encryption
	(*DeepLink)(nil),                               // 16: kratos.api.DeepLink
	(*Logger)(nil),                                 // 17: kratos.api.Logger
	(*Tracing)(nil),                                // 18: kratos.api.Tracing
	(*Application)(nil),                            // 19: kratos.api.Application
	(*Kafka)(nil),                                  // 20: kratos.api.Kafka
	(*Whitelist)(nil),                              // 21: kratos.api.Whitelist
	(*TimeOfDay)(nil),                              // 22: kratos.api.TimeOfDay
	(*Account_Adapters)(nil),                       // 23: kratos.api.Account.Adapters
	(*Account_Resources)(nil),                      // 24: kratos.api.Account.Resources
	(*Account_Schedulers)(nil),                     // 25: kratos.api.Account.Schedulers
	(*Account_FraudRisk)(nil),                      // 26: kratos.api.Account.FraudRisk
	nil,                                            // 27: kratos.api.Account.Resources.DeeplinksEntry
	(*Onboarding_Adapters)(nil),                    // 28: kratos.api.Onboarding.Adapters
	(*Onboarding_Schedulers)(nil),                  // 29: kratos.api.Onboarding.Schedulers
	(*Onboarding_UserChallenge)(nil),               // 30: kratos.api.Onboarding.UserChallenge
	(*Onboarding_Deeplinks)(nil),                   // 31: kratos.api.Onboarding.Deeplinks
	(*Onboarding_Resources)(nil),                   // 32: kratos.api.Onboarding.Resources
	(*Onboarding_FraudRisk)(nil),                   // 33: kratos.api.Onboarding.FraudRisk
	(*Onboarding_CreditScore)(nil),                 // 34: kratos.api.Onboarding.CreditScore
	(*Onboarding_Whitelists)(nil),                  // 35: kratos.api.Onboarding.Whitelists
	(*Management_Adapters)(nil),                    // 36: kratos.api.Management.Adapters
	(*Management_Schedulers)(nil),                  // 37: kratos.api.Management.Schedulers
	(*Management_Resources)(nil),                   // 38: kratos.api.Management.Resources
	(*Management_CryptoSets)(nil),                  // 39: kratos.api.Management.CryptoSets
	(*Management_StatementSync)(nil),               // 40: kratos.api.Management.StatementSync
	(*Management_EarlyDischarge)(nil),              // 41: kratos.api.Management.EarlyDischarge
	nil,                                            // 42: kratos.api.Management.Resources.DeeplinksEntry
	(*Management_EarlyDischarge_TimeWindow)(nil),   // 43: kratos.api.Management.EarlyDischarge.TimeWindow
	(*Management_EarlyDischarge_BaseScenario)(nil), // 44: kratos.api.Management.EarlyDischarge.BaseScenario
	(*Server_HTTP)(nil),                            // 45: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),                            // 46: kratos.api.Server.GRPC
	nil,                                            // 47: kratos.api.TemporalTask.ActivitiesEntry
	(*durationpb.Duration)(nil),                    // 48: google.protobuf.Duration
}
var file_conf_proto_depIdxs = []int32{
	2,   // 0: kratos.api.Bootstrap.account:type_name -> kratos.api.Account
	3,   // 1: kratos.api.Bootstrap.onboarding:type_name -> kratos.api.Onboarding
	4,   // 2: kratos.api.Bootstrap.management:type_name -> kratos.api.Management
	1,   // 3: kratos.api.Bootstrap.zombie:type_name -> kratos.api.Zombie
	5,   // 4: kratos.api.Zombie.server:type_name -> kratos.api.Server
	19,  // 5: kratos.api.Account.app:type_name -> kratos.api.Application
	5,   // 6: kratos.api.Account.server:type_name -> kratos.api.Server
	6,   // 7: kratos.api.Account.data:type_name -> kratos.api.Data
	23,  // 8: kratos.api.Account.adapters:type_name -> kratos.api.Account.Adapters
	24,  // 9: kratos.api.Account.resources:type_name -> kratos.api.Account.Resources
	25,  // 10: kratos.api.Account.schedulers:type_name -> kratos.api.Account.Schedulers
	18,  // 11: kratos.api.Account.tracing:type_name -> kratos.api.Tracing
	17,  // 12: kratos.api.Account.logger:type_name -> kratos.api.Logger
	26,  // 13: kratos.api.Account.fraud_risk:type_name -> kratos.api.Account.FraudRisk
	19,  // 14: kratos.api.Onboarding.app:type_name -> kratos.api.Application
	5,   // 15: kratos.api.Onboarding.server:type_name -> kratos.api.Server
	6,   // 16: kratos.api.Onboarding.data:type_name -> kratos.api.Data
	28,  // 17: kratos.api.Onboarding.adapters:type_name -> kratos.api.Onboarding.Adapters
	29,  // 18: kratos.api.Onboarding.schedulers:type_name -> kratos.api.Onboarding.Schedulers
	17,  // 19: kratos.api.Onboarding.logger:type_name -> kratos.api.Logger
	18,  // 20: kratos.api.Onboarding.tracing:type_name -> kratos.api.Tracing
	32,  // 21: kratos.api.Onboarding.resources:type_name -> kratos.api.Onboarding.Resources
	33,  // 22: kratos.api.Onboarding.fraud_risk:type_name -> kratos.api.Onboarding.FraudRisk
	30,  // 23: kratos.api.Onboarding.user_challenge:type_name -> kratos.api.Onboarding.UserChallenge
	35,  // 24: kratos.api.Onboarding.whitelists:type_name -> kratos.api.Onboarding.Whitelists
	34,  // 25: kratos.api.Onboarding.credit_score:type_name -> kratos.api.Onboarding.CreditScore
	19,  // 26: kratos.api.Management.app:type_name -> kratos.api.Application
	5,   // 27: kratos.api.Management.server:type_name -> kratos.api.Server
	6,   // 28: kratos.api.Management.data:type_name -> kratos.api.Data
	36,  // 29: kratos.api.Management.adapters:type_name -> kratos.api.Management.Adapters
	18,  // 30: kratos.api.Management.tracing:type_name -> kratos.api.Tracing
	17,  // 31: kratos.api.Management.logger:type_name -> kratos.api.Logger
	37,  // 32: kratos.api.Management.schedulers:type_name -> kratos.api.Management.Schedulers
	38,  // 33: kratos.api.Management.resources:type_name -> kratos.api.Management.Resources
	39,  // 34: kratos.api.Management.crypto_sets:type_name -> kratos.api.Management.CryptoSets
	40,  // 35: kratos.api.Management.statement_sync:type_name -> kratos.api.Management.StatementSync
	41,  // 36: kratos.api.Management.early_discharge:type_name -> kratos.api.Management.EarlyDischarge
	45,  // 37: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	46,  // 38: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	9,   // 39: kratos.api.Data.database:type_name -> kratos.api.Database
	10,  // 40: kratos.api.Data.s3_storage:type_name -> kratos.api.S3Storage
	14,  // 41: kratos.api.Data.redis_cache:type_name -> kratos.api.RedisCache
	48,  // 42: kratos.api.GrpcService.timeout:type_name -> google.protobuf.Duration
	48,  // 43: kratos.api.HttpService.timeout:type_name -> google.protobuf.Duration
	48,  // 44: kratos.api.Database.conn_max_lifetime:type_name -> google.protobuf.Duration
	48,  // 45: kratos.api.TemporalTask.timeout:type_name -> google.protobuf.Duration
	48,  // 46: kratos.api.TemporalTask.time_interval:type_name -> google.protobuf.Duration
	48,  // 47: kratos.api.TemporalTask.max_time_retry:type_name -> google.protobuf.Duration
	48,  // 48: kratos.api.TemporalTask.max_time_interval:type_name -> google.protobuf.Duration
	47,  // 49: kratos.api.TemporalTask.activities:type_name -> kratos.api.TemporalTask.ActivitiesEntry
	48,  // 50: kratos.api.TemporalActivity.timeout:type_name -> google.protobuf.Duration
	48,  // 51: kratos.api.TemporalActivity.time_interval:type_name -> google.protobuf.Duration
	48,  // 52: kratos.api.TemporalActivity.max_time_retry:type_name -> google.protobuf.Duration
	48,  // 53: kratos.api.TemporalActivity.max_time_interval:type_name -> google.protobuf.Duration
	48,  // 54: kratos.api.RedisCache.pool_timeout:type_name -> google.protobuf.Duration
	48,  // 55: kratos.api.RedisCache.idle_timeout:type_name -> google.protobuf.Duration
	48,  // 56: kratos.api.RedisCache.max_conn_age:type_name -> google.protobuf.Duration
	7,   // 57: kratos.api.Account.Adapters.session:type_name -> kratos.api.GrpcService
	7,   // 58: kratos.api.Account.Adapters.cimb_connector:type_name -> kratos.api.GrpcService
	7,   // 59: kratos.api.Account.Adapters.onboarding_service:type_name -> kratos.api.GrpcService
	11,  // 60: kratos.api.Account.Adapters.temporal:type_name -> kratos.api.Temporal
	7,   // 61: kratos.api.Account.Adapters.risk_system:type_name -> kratos.api.GrpcService
	20,  // 62: kratos.api.Account.Adapters.crm_event_pub:type_name -> kratos.api.Kafka
	7,   // 63: kratos.api.Account.Adapters.management_service:type_name -> kratos.api.GrpcService
	27,  // 64: kratos.api.Account.Resources.deeplinks:type_name -> kratos.api.Account.Resources.DeeplinksEntry
	12,  // 65: kratos.api.Account.Schedulers.sync_account_balance:type_name -> kratos.api.TemporalTask
	12,  // 66: kratos.api.Account.Schedulers.polling_account_balance:type_name -> kratos.api.TemporalTask
	12,  // 67: kratos.api.Account.Schedulers.sync_cimb_account_balances:type_name -> kratos.api.TemporalTask
	12,  // 68: kratos.api.Account.Schedulers.sync_cimb_debt_account_balances:type_name -> kratos.api.TemporalTask
	16,  // 69: kratos.api.Account.Resources.DeeplinksEntry.value:type_name -> kratos.api.DeepLink
	7,   // 70: kratos.api.Onboarding.Adapters.session:type_name -> kratos.api.GrpcService
	7,   // 71: kratos.api.Onboarding.Adapters.user_profile:type_name -> kratos.api.GrpcService
	7,   // 72: kratos.api.Onboarding.Adapters.ekyc_center:type_name -> kratos.api.GrpcService
	7,   // 73: kratos.api.Onboarding.Adapters.cimb_connector:type_name -> kratos.api.GrpcService
	7,   // 74: kratos.api.Onboarding.Adapters.account_service:type_name -> kratos.api.GrpcService
	7,   // 75: kratos.api.Onboarding.Adapters.risk_system:type_name -> kratos.api.GrpcService
	8,   // 76: kratos.api.Onboarding.Adapters.kyc_image:type_name -> kratos.api.HttpService
	11,  // 77: kratos.api.Onboarding.Adapters.temporal:type_name -> kratos.api.Temporal
	7,   // 78: kratos.api.Onboarding.Adapters.face_auth:type_name -> kratos.api.GrpcService
	7,   // 79: kratos.api.Onboarding.Adapters.ekyc_nfc:type_name -> kratos.api.GrpcService
	8,   // 80: kratos.api.Onboarding.Adapters.http_common:type_name -> kratos.api.HttpService
	7,   // 81: kratos.api.Onboarding.Adapters.ab_platform:type_name -> kratos.api.GrpcService
	7,   // 82: kratos.api.Onboarding.Adapters.credit_score:type_name -> kratos.api.GrpcService
	12,  // 83: kratos.api.Onboarding.Schedulers.onboarding_status:type_name -> kratos.api.TemporalTask
	12,  // 84: kratos.api.Onboarding.Schedulers.submit_face_image:type_name -> kratos.api.TemporalTask
	12,  // 85: kratos.api.Onboarding.Schedulers.contract_signing:type_name -> kratos.api.TemporalTask
	48,  // 86: kratos.api.Onboarding.UserChallenge.face_expired_in:type_name -> google.protobuf.Duration
	16,  // 87: kratos.api.Onboarding.Deeplinks.kyc_update:type_name -> kratos.api.DeepLink
	16,  // 88: kratos.api.Onboarding.Deeplinks.nfc_update:type_name -> kratos.api.DeepLink
	31,  // 89: kratos.api.Onboarding.Resources.deeplinks:type_name -> kratos.api.Onboarding.Deeplinks
	21,  // 90: kratos.api.Onboarding.Whitelists.onboarding:type_name -> kratos.api.Whitelist
	7,   // 91: kratos.api.Management.Adapters.session:type_name -> kratos.api.GrpcService
	7,   // 92: kratos.api.Management.Adapters.cimb_connector:type_name -> kratos.api.GrpcService
	7,   // 93: kratos.api.Management.Adapters.account_service:type_name -> kratos.api.GrpcService
	11,  // 94: kratos.api.Management.Adapters.temporal:type_name -> kratos.api.Temporal
	20,  // 95: kratos.api.Management.Adapters.payment_comm_pub:type_name -> kratos.api.Kafka
	20,  // 96: kratos.api.Management.Adapters.refund_settle_sub:type_name -> kratos.api.Kafka
	12,  // 97: kratos.api.Management.Schedulers.sync_statements_periodic:type_name -> kratos.api.TemporalTask
	12,  // 98: kratos.api.Management.Schedulers.sync_statements_penalty:type_name -> kratos.api.TemporalTask
	12,  // 99: kratos.api.Management.Schedulers.sync_statements_batch_proc:type_name -> kratos.api.TemporalTask
	12,  // 100: kratos.api.Management.Schedulers.create_installment_loan:type_name -> kratos.api.TemporalTask
	12,  // 101: kratos.api.Management.Schedulers.sync_installments_daily:type_name -> kratos.api.TemporalTask
	12,  // 102: kratos.api.Management.Schedulers.sync_loan_repayment:type_name -> kratos.api.TemporalTask
	12,  // 103: kratos.api.Management.Schedulers.sync_account_balance:type_name -> kratos.api.TemporalTask
	12,  // 104: kratos.api.Management.Schedulers.sync_installment_info:type_name -> kratos.api.TemporalTask
	12,  // 105: kratos.api.Management.Schedulers.sync_latest_statement:type_name -> kratos.api.TemporalTask
	42,  // 106: kratos.api.Management.Resources.deeplinks:type_name -> kratos.api.Management.Resources.DeeplinksEntry
	15,  // 107: kratos.api.Management.CryptoSets.plan_encryption:type_name -> kratos.api.Encryption
	44,  // 108: kratos.api.Management.EarlyDischarge.refund:type_name -> kratos.api.Management.EarlyDischarge.BaseScenario
	16,  // 109: kratos.api.Management.Resources.DeeplinksEntry.value:type_name -> kratos.api.DeepLink
	22,  // 110: kratos.api.Management.EarlyDischarge.TimeWindow.start_time:type_name -> kratos.api.TimeOfDay
	22,  // 111: kratos.api.Management.EarlyDischarge.TimeWindow.end_time:type_name -> kratos.api.TimeOfDay
	43,  // 112: kratos.api.Management.EarlyDischarge.BaseScenario.time_window:type_name -> kratos.api.Management.EarlyDischarge.TimeWindow
	48,  // 113: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	48,  // 114: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	13,  // 115: kratos.api.TemporalTask.ActivitiesEntry.value:type_name -> kratos.api.TemporalActivity
	116, // [116:116] is the sub-list for method output_type
	116, // [116:116] is the sub-list for method input_type
	116, // [116:116] is the sub-list for extension type_name
	116, // [116:116] is the sub-list for extension extendee
	0,   // [0:116] is the sub-list for field type_name
}

func init() { file_conf_proto_init() }
func file_conf_proto_init() {
	if File_conf_proto != nil {
		return
	}
	file_conf_proto_msgTypes[20].OneofWrappers = []any{}
	file_conf_proto_msgTypes[44].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_conf_proto_rawDesc), len(file_conf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   48,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_proto_goTypes,
		DependencyIndexes: file_conf_proto_depIdxs,
		MessageInfos:      file_conf_proto_msgTypes,
	}.Build()
	File_conf_proto = out.File
	file_conf_proto_goTypes = nil
	file_conf_proto_depIdxs = nil
}
