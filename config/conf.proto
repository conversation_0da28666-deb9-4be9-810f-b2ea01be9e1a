syntax = "proto3";
package kratos.api;

option go_package = "gitlab.zalopay.vn/fin/installment/installment-service/internal/account/internal/config;config";

import "google/protobuf/duration.proto";

message Bootstrap {
  Account account = 1;
  Onboarding onboarding = 2;
  Management management = 3;
  Zombie zombie = 4;
}

message Zombie {
  Server server = 1;
}

message Account {
  message Adapters {
    GrpcService session = 1;
    GrpcService cimb_connector = 2;
    GrpcService onboarding_service = 3;
    Temporal temporal = 4;
    GrpcService risk_system = 5;
    Kafka crm_event_pub = 6;
    GrpcService management_service = 7;
  }
  message Resources {
    string fee_file_path = 1;
    string service_file_path = 2;
    map<string, DeepLink> deeplinks = 3;
  }
  message Schedulers {
    TemporalTask sync_account_balance = 1;
    TemporalTask polling_account_balance = 2;
    TemporalTask sync_cimb_account_balances = 3;
    TemporalTask sync_cimb_debt_account_balances = 4;
  }
  message FraudRisk {
    string hash_key = 1;
  }
  
  Application app = 1;
  Server server = 2;
  Data data = 3;
  Adapters adapters = 4;
  Resources resources = 5;
  Schedulers schedulers = 6;
  Tracing tracing = 7;
  Logger logger = 8;
  FraudRisk fraud_risk = 9;
}

message Onboarding {
  message Adapters {
    GrpcService session = 1;
    GrpcService user_profile = 2;
    GrpcService ekyc_center = 3;
    GrpcService cimb_connector = 4;
    GrpcService account_service = 5;
    GrpcService risk_system = 6;
    HttpService kyc_image = 7;
    Temporal temporal = 8;
    GrpcService face_auth = 9;
    GrpcService ekyc_nfc = 10;
    HttpService http_common = 11;
    GrpcService ab_platform = 12;
    GrpcService credit_score = 13;
  }
  message Schedulers {
    TemporalTask onboarding_status = 1;
    TemporalTask submit_face_image = 2;
    TemporalTask contract_signing = 3;
  }
  message UserChallenge {
    int32 face_source_id = 1;
    google.protobuf.Duration face_expired_in = 2;
  }
  message Deeplinks {
    DeepLink kyc_update = 1;
    DeepLink nfc_update = 2;
  }
  message Resources {
    Deeplinks deeplinks = 1;
  }
  message FraudRisk {
    string hash_key = 1;
  }
  message CreditScore {
    string hash_key = 1;
  }
  message Whitelists {
    Whitelist onboarding = 1;
  }

  Application app = 1;
  Server server = 2;
  Data data = 3;
  Adapters adapters = 4;
  Schedulers schedulers = 5;
  Logger logger = 6;
  Tracing tracing = 7;
  Resources resources = 8;
  FraudRisk fraud_risk = 9;
  UserChallenge user_challenge = 10;
  Whitelists whitelists = 11;
  CreditScore credit_score = 12;
}

message Management {
  message Adapters {
    GrpcService session = 1;
    GrpcService cimb_connector = 2;
    GrpcService account_service = 3;
    Temporal temporal = 4;
    Kafka payment_comm_pub = 5;
    Kafka refund_settle_sub = 6;
  }
  message Schedulers {
    TemporalTask sync_statements_periodic = 1;
    TemporalTask sync_statements_penalty = 11;
    TemporalTask sync_statements_batch_proc = 2;
    TemporalTask create_installment_loan = 3;
    TemporalTask sync_installments_daily = 4;
    TemporalTask sync_loan_repayment = 5;
    TemporalTask sync_account_balance = 6;
    TemporalTask sync_installment_info = 7;
    TemporalTask sync_latest_statement = 8;
  }
  message Resources {
    string fee_file_path = 1;
    map<string, DeepLink> deeplinks = 3;
  }
  message CryptoSets {
    Encryption plan_encryption = 1;
  }
  message StatementSync {
    int32 penalty_all_buffer_days = 1;
  }
  message EarlyDischarge {
    // Time window settings for early discharge 
    message TimeWindow {
      // Time of day when early discharge becomes available
      TimeOfDay start_time = 1;
      // Time of day when early discharge becomes unavailable
      TimeOfDay end_time = 2;
    }
    
    // Base scenario configuration for early discharge
    message BaseScenario {
      // Whether the scenario is enabled
      bool enabled = 1;
      // Time window when early discharge is permitted
      optional TimeWindow time_window = 2;
    }

    // Scenario-specific configurations
    BaseScenario refund = 1;
  }

  Application app = 1;
  Server server = 2;
  Data data = 3;
  Adapters adapters = 4;
  Tracing tracing = 5;
  Logger logger = 6;
  Schedulers schedulers = 7;
  Resources resources = 8;
  CryptoSets crypto_sets = 9;
  StatementSync statement_sync = 10;
  EarlyDischarge early_discharge = 11;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  Database database = 1;
  S3Storage s3_storage = 2;
  RedisCache redis_cache = 3;
}

message GrpcService {
  string address = 1;
  google.protobuf.Duration timeout = 2;
  bool secured = 3;
  string client_id = 4;
  string client_key = 5;
}

message HttpService {
  string base_url = 1;
  string proxy_url = 2;
  int32 retries = 3;
  google.protobuf.Duration timeout = 4;
}

message Database {
  string driver = 1;
  string source = 2;
  int32 max_idle_conn = 3;
  int32 max_open_conn = 4;
  google.protobuf.Duration conn_max_lifetime = 5;
}

message S3Storage {
  string host = 1;
  string region = 2;
  string bucket_name = 3;
  string access_key = 4;
  string secret_key = 5;
  bool use_proxy = 6;
  string proxy_url = 7;
  bool enable_ssl = 8;
}

message Temporal {
  string address = 1;
  string namespace = 2;
  string enable_tls = 3;
}

message TemporalTask {
  string queue_name = 1;
  string workflow_type = 2;
  google.protobuf.Duration timeout = 3;
  google.protobuf.Duration time_interval = 4;
  google.protobuf.Duration max_time_retry = 5;
  google.protobuf.Duration max_time_interval = 6;
  map<string, TemporalActivity> activities = 7;
}

message TemporalActivity {
  string queue_name = 1;
  string activity_type = 2;
  google.protobuf.Duration timeout = 3;
  google.protobuf.Duration time_interval = 4;
  google.protobuf.Duration max_time_retry = 5;
  google.protobuf.Duration max_time_interval = 6;
}

message RedisCache {
  string address = 1;
  string username = 2;
  string password = 3;
  string master_name = 4;
  int32 pool_size = 5;
  int32 max_retries = 6;
  int32 min_idle_conn = 7;
  google.protobuf.Duration pool_timeout = 8;
  google.protobuf.Duration idle_timeout = 9;
  google.protobuf.Duration max_conn_age = 10;
}

message Encryption {
  string key = 1;
  string iv = 2;
}

message DeepLink {
  string zpa_url = 1;
  string zpi_url = 2;
  string common_url = 3;
}

message Logger {
  string level = 1;
  string encoding = 2;
  bool stack_trace = 3;
}

message Tracing {
  bool enabled = 1;
  string service_name = 2;
  string agent_host = 3;
  int32 agent_port = 4;
}

message Application {
  string env = 1;
  string name = 2;
}

message Kafka {
  string brokers = 1;
  string topic = 2;
  optional string group_id = 3;
  optional int32 num_workers = 4;
}

message Whitelist {
  bool enabled = 1;
  string experiment_key = 2;
  string whitelist_group = 3;
}

message TimeOfDay {
  // Hours of day in 24 hour format. Should be from 0 to 23. An API may choose
  // to allow the value "24:00:00" for scenarios like business closing time.
  int32 hours = 1;

  // Minutes of hour of day. Must be from 0 to 59.
  int32 minutes = 2;

  // Seconds of minute of the time. Must normally be from 0 to 59. An API may
  // allow the value 60 if it allows leap-seconds.
  int32 seconds = 3;

  // Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
  int32 nanos = 4;
}